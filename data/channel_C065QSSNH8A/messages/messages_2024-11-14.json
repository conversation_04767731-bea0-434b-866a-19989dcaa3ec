{"date": "2024-11-14", "channel_id": "C065QSSNH8A", "message_count": 39, "messages": [{"ts": "1731600284.199119", "text": "<@U04DKEFP1K8> :point_up:", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "AxORD", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "point_up", "unicode": "261d-fe0f"}]}]}]}, {"ts": "1731600269.863049", "text": "<https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1731600269.863049", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "2HwXU", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1731600260.172569", "text": "<@U0690EB5JE5>, looks like am<PERSON> is on a call with DK lets use this zoom", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PjCy+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": ", looks like am<PERSON> is on a call with DK lets use this zoom"}]}]}]}, {"ts": "1731598771.582759", "text": "<@U0690EB5JE5> Figma file for the UX designs for Merit Planner FYA\n<https://www.figma.com/file/WOJVdr3bbnBloZT9z6pY9K?node-id=7477%3A91270&type=design&fuid=1170850046975729676#1001974578>", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1731598777.000000"}, "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "992IY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Figma file for the UX designs for Merit Planner FYA\n"}, {"type": "link", "url": "https://www.figma.com/file/WOJVdr3bbnBloZT9z6pY9K?node-id=7477%3A91270&type=design&fuid=1170850046975729676#1001974578"}]}]}]}, {"ts": "1731581531.743419", "text": "We can find product usage metrics here. These were setup by <PERSON> and were not working due to configuration issue. The issue is fixed now and We can see metrics from today.\n<https://clarity.microsoft.com/projects/view/kcl6cu536t/dashboard?date=Last%203%20days>\n<https://heapanalytics.com/app/env/2999280564/dashboards>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1731581531.743419", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "1731582440.000000"}, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "T7KbD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can find product usage metrics here. These were setup by <PERSON> and were not working due to configuration issue. The issue is fixed now and We can see metrics from today.\n"}, {"type": "link", "url": "https://clarity.microsoft.com/projects/view/kcl6cu536t/dashboard?date=Last%203%20days"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://heapanalytics.com/app/env/2999280564/dashboards"}]}]}]}, {"ts": "1731578699.734949", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> Whats up with rightway? We have an active prod instance running for this customer.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1731578699.734949", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "pbPzN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Whats up with rightway? We have an active prod instance running for this customer."}]}]}]}, {"ts": "1731578652.003249", "text": "*Agenda for today:*\n• Alaycare slowness\n• DegenKolb readiness\n• equity and cycle builder", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "o8iSL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Alaycare slowness"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "DegenKolb readiness"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "equity and cycle builder"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1731549814.392439", "text": "Yes this could be because of number of users logged in at once.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1731525232.121819", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "TFdoM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes this could be because of number of users logged in at once."}]}]}]}, {"ts": "1731548627.827399", "text": "Yes <@U07M6QKHUC9> I would request not to commit on anything major/minor new changes to upcoming customers, unless its really works for all customers and its a blocker. Please avoid custom requirements especially.\n\nWith large customers in pipeline, We also need to focus on architecture side of things and is a major task. Lets have some buffer time for fixing architecture and performance tune the product and load test", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1731523417.496349", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1731548700.000000"}, "blocks": [{"type": "rich_text", "block_id": "7HBVE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I would request not to commit on anything major/minor new changes to upcoming customers, unless its really works for all customers and its a blocker. Please avoid custom requirements especially.\n\nWith large customers in pipeline, We also need to focus on architecture side of things and is a major task. Lets have some buffer time for fixing architecture and performance tune the product and load test"}]}]}]}, {"ts": "1731548109.768819", "text": "Looks like system is unable to handle multiple users. <@U07M6QKHUC9> Its high time we prioritize the multi-tenancy thing and optimize once for all. I will start on this next week.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1731527087.956519", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "Ljo2e", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Looks like system is unable to handle multiple users. "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Its high time we prioritize the multi-tenancy thing and optimize once for all. I will start on this next week."}]}]}]}, {"ts": "1731548014.967729", "text": "<@U04DKEFP1K8> <@U07M6QKHUC9> Do we know if this helped? I will look into this anyway today and see if there anything further can be done to optimize the infra.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1731527087.956519", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "efG9Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Do we know if this helped? I will look into this anyway today and see if there anything further can be done to optimize the infra."}]}]}]}, {"ts": "1731543509.622939", "text": "<@U0690EB5JE5> I haven’t put in a Jeera ticket because I’m just on my phone now, but is there anyway you can look into Degenkolb merit view and see why I cannot filter by manager? I think it just says null for a whole list.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731543509.622939", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "5RKZO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I haven’t put in a <PERSON>era ticket because I’m just on my phone now, but is there anyway you can look into Degen<PERSON><PERSON>b merit view and see why I cannot filter by manager? I think it just says null for a whole list."}]}]}]}, {"ts": "1731531429.322849", "text": "New requirement they just shared with us today: there is an additional modifier on the overall budget unlocked. If the company achieves x% of their EBITDA goal, then the employees are eligible for x% of their total bonus (these aren't linear, the eligibility criteria is complicated, we will have the total modifier % as our input). For example:\n\nEmployee Total Possible bonus: 20% - 10,000\nEBIDTA Goal achieved 2024: 95%\nBonus Modifier: 98%\nEmployee New Bonus Target: 98% of 20%. $9800\nCompany Target 40%: $3920\nIndividual Target 60%: $5880\n\nThe overall bonus modifier has reduced their 2024 target by 200.\n\nAt that point, we would apply the 95% EBITDA performance modifier to the Company target 40%/$3920. The individual modifier would be against $5880.\n\nI understand this changes a lot; <@U0690EB5JE5> can you estimate how long this additional effort will require?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731531429.322849", "reply_count": 3, "files": [{"id": "F080S3M0FA6", "created": 1731531372, "timestamp": 1731531372, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 59903, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F080S3M0FA6/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F080S3M0FA6/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F080S3M0FA6-aec2bdc4e7/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F080S3M0FA6-aec2bdc4e7/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F080S3M0FA6-aec2bdc4e7/image_360.png", "thumb_360_w": 360, "thumb_360_h": 37, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F080S3M0FA6-aec2bdc4e7/image_480.png", "thumb_480_w": 480, "thumb_480_h": 49, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F080S3M0FA6-aec2bdc4e7/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F080S3M0FA6-aec2bdc4e7/image_720.png", "thumb_720_w": 720, "thumb_720_h": 73, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F080S3M0FA6-aec2bdc4e7/image_800.png", "thumb_800_w": 800, "thumb_800_h": 82, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F080S3M0FA6-aec2bdc4e7/image_960.png", "thumb_960_w": 960, "thumb_960_h": 98, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F080S3M0FA6-aec2bdc4e7/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 104, "original_w": 3198, "original_h": 326, "thumb_tiny": "AwAEADDRo7daKKAG5PrTvxplPFA2Jz6mlGc9aSlFAj//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F080S3M0FA6/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F080S3M0FA6-4a8bda83ae", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F080UJMFWRJ", "created": 1731531377, "timestamp": 1731531377, "name": "Diversified Revised Bonus Requirement.xlsx", "title": "Diversified Revised Bonus Requirement.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 286478, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F080UJMFWRJ/diversified_revised_bonus_requirement.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F080UJMFWRJ/download/diversified_revised_bonus_requirement.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F080UJMFWRJ-1bb7c1d617/diversified_revised_bonus_requirement_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F080UJMFWRJ-1bb7c1d617/diversified_revised_bonus_requirement_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F080UJMFWRJ/diversified_revised_bonus_requirement.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F080UJMFWRJ-eb8c26caa4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "2RVOy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "New requirement they just shared with us today: there is an additional modifier on the overall budget unlocked. If the company achieves x% of their EBITDA goal, then the employees are eligible for x% of their total bonus (these aren't linear, the eligibility criteria is complicated, we will have the total modifier % as our input). For example:\n\nEmployee Total Possible bonus: 20% - 10,000\nEBIDTA Goal achieved 2024: 95%\nBonus Modifier: 98%\nEmployee New Bonus Target: 98% of 20%. $9800\nCompany Target 40%: $3920\nIndividual Target 60%: $5880\n\nThe overall bonus modifier has reduced their 2024 target by 200.\n\nAt that point, we would apply the 95% EBITDA performance modifier to the Company target 40%/$3920. The individual modifier would be against $5880.\n\nI understand this changes a lot; "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you estimate how long this additional effort will require?"}]}]}]}, {"ts": "1731530105.861089", "text": "<@U0690EB5JE5> <@U07MH77PUBV> there is one more change coming in the bonus worlflow for DE. <@U07EJ2LP44S> will update the spreadsheet that shows the columns needed and send it over. We will also discuss it briefly during the standup tomorrow.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3rUYB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " there is one more change coming in the bonus worlflow for DE. "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " will update the spreadsheet that shows the columns needed and send it over. We will also discuss it briefly during the standup tomorrow."}]}]}]}, {"ts": "1731527145.343719", "text": "saurab<PERSON>.jain has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R080NSR3A05", "block_id": "9jtwl", "api_decoration_available": false, "call": {"v1": {"id": "R080NSR3A05", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1731527145, "active_participants": [], "all_participants": [{"slack_id": "U04DKEFP1K8"}, {"slack_id": "U04DS2MBWP4"}], "display_id": "824-9855-4756", "join_url": "https://us06web.zoom.us/j/82498554756?pwd=IHFQbsu2sB7XgOJbVUEa0nQv0nyNIA.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEXzc3ODk3NjRhNjE2OTRiZDM5ZDAyNTMxODgxNGI2NDRhJnVzcz10NHhEX25mTGtpQnlMOVNCZWNxZUh4eEgycTJQX1dlcXBYNGlwTnNWcy1QOTZDSFdlT3Nib1ZYNTNIYmoxV1dJb1ZXNUdRdTMwNndIRlZsazMzMFp3WFVwX25kUkpQX3VldFh2Z3d6dzNkOXNRXzh1VHA4NTBTR0hjNUo4S1diR0F4OGc5OHF6RUp6czNYYlJVdG5SODlYUTV2LVpPQXpOSEdSZjdHYy5tZ0w2Yk1lU1E4ODg0UTds&action=join&confno=82498554756&pwd=IHFQbsu2sB7XgOJbVUEa0nQv0nyNIA.1", "name": "Zoom meeting started by <PERSON><PERSON><PERSON><PERSON>", "created_by": "U05185RFCNT", "date_end": 1731527805, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "dJFII", "text": {"type": "mrkdwn", "text": "Meeting passcode: IHFQbsu2sB7XgOJbVUEa0nQv0nyNIA.1", "verbatim": false}}]}, {"ts": "1731527142.826909", "text": "join here", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0m/SO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "join here"}]}]}]}, {"ts": "1731527087.956519", "text": "<@U04DKEFP1K8> cherl reported another slowness", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1731527087.956519", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "Zsvdf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " <PERSON>er<PERSON> reported another slowness"}]}]}]}, {"ts": "1731527087.296329", "text": "and he hasn't joined in months", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731527087.296329", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "I7MNI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "and he hasn't joined in months"}]}]}]}, {"ts": "1731527082.411879", "text": "Mark is coming", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "s5P7W", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Mark is coming"}]}]}]}, {"ts": "1731527075.202409", "text": "I think you should join", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FIyt5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think you should join"}]}]}]}, {"ts": "1731527072.780959", "text": "<PERSON>ery<PERSON> reported another slowness", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "KAfOT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>ery<PERSON> reported another slowness"}]}]}]}, {"ts": "1731526939.983279", "text": "I saw myself as optional so will skip it unless you absolutely need me", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wPWqa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I saw myself as optional so will skip it unless you absolutely need me"}]}]}]}, {"ts": "1731526881.138299", "text": "<@U07M6QKHUC9> you are coming to the diversified call in 20, yes?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "l9nFu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " you are coming to the diversified call in 20, yes?"}]}]}]}, {"ts": "1731525610.105779", "text": "<@U0690EB5JE5> here is the ticket with logs so you can debug why AlayaCare lost access to their environemnt\n<https://compiify.atlassian.net/browse/COM-3982>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14065::73447b933c7a40f3be48c782bade97e7", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3982?atlOrigin=eyJpIjoiNjQ4ZDkyNWQ2OGUxNDRhMDhiOGNhY2I0NmI0NWUwZWUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3982 Investigate and Resolve Recent Environment Issue>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14065::01fe9ba6c26e44778d42f06e37743dc3", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14065\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3982\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3982", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "bLeWG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " here is the ticket with logs so you can debug why AlayaCare lost access to their environemnt\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3982"}]}]}]}, {"ts": "1731525287.735279", "text": "yep its working for me", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DEvI0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yep its working for me"}]}]}]}, {"ts": "1731525232.121819", "text": "weird", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731525232.121819", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "F2dNN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "weird"}]}]}]}, {"ts": "1731525230.494309", "text": "Its working again now?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GOwYV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Its working again now?"}]}]}]}, {"ts": "1731525003.867029", "text": "saurab<PERSON>.jain has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R080RGNS0FL", "block_id": "N+ZF0", "api_decoration_available": false, "call": {"v1": {"id": "R080RGNS0FL", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1731525003, "active_participants": [], "all_participants": [{"slack_id": "U04DKEFP1K8"}, {"slack_id": "U04DS2MBWP4"}], "display_id": "875-0996-7405", "join_url": "https://us06web.zoom.us/j/87509967405?pwd=wGyw3wnNmxKmvMdH0be4PhYuSj9SWT.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEXzZkYjQ0ZjU4MzQzMjRjODdhYmVhZjdlYTliOWZhZTk3JnVzcz0tQ2FrOURuM0dLWWNWSjdqMUZSd0M2ZjE0c1pTbnp4WWxXelVHbHdQUnRIWEZpMDNxMVFfY3ZhaTlEMzZydHpGWG1nTkYyLWVSZTQ5d0ZqYnJ2eXNFX05hQzlzclZWRS1lbV9PekdQclNCbGpzNDVudjlSVGFlUUZrOTV3YjhtNVRsT2p3WkJoUExHUlVtNVBOc3hlRFhobHlrakltWmRRRWxzek9DNC4tWE5MOXR5cVlwMHlMRFFn&action=join&confno=87509967405&pwd=wGyw3wnNmxKmvMdH0be4PhYuSj9SWT.1", "name": "Zoom meeting started by <PERSON><PERSON><PERSON><PERSON>", "created_by": "U05185RFCNT", "date_end": 1731525603, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "F/dYR", "text": {"type": "mrkdwn", "text": "Meeting passcode: wGyw3wnNmxKmvMdH0be4PhYuSj9SWT.1", "verbatim": false}}]}, {"ts": "1731524999.008119", "text": "can you join a call", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "R97zZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "can you join a call"}]}]}]}, {"ts": "1731524993.118199", "text": "<@U07M6QKHUC9> we will need to production environment why alayacare has lost access to the environment", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4F4pl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " we will need to production environment why alayacare has lost access to the environment"}]}]}]}, {"ts": "1731524587.941899", "text": "<PERSON>aya<PERSON> had someone submit who didn't mean to", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XEKeV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>aya<PERSON> had someone submit who didn't mean to"}]}]}]}, {"ts": "1731524577.366659", "text": "<@U04DKEFP1K8> can we 'undo' a submit, maybe on the back end?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731524577.366659", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "hbuXG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can we 'undo' a submit, maybe on the back end?"}]}]}]}, {"ts": "1731523767.448479", "text": "<@U07EJ2LP44S> Great job on framing the Nauto's feedback to focus on the impact on their HR team- which was more relevant for PlayQ.  Nobody understands customers the way you do :fire::tada:", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1731523767.448479", "reply_count": 1, "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}, {"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "GF8VC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Great job on framing the Nauto's feedback to focus on the impact on their HR team- which was more relevant for PlayQ.  Nobody understands customers the way you do "}, {"type": "emoji", "name": "fire", "unicode": "1f525"}, {"type": "emoji", "name": "tada", "unicode": "1f389"}]}]}]}, {"ts": "1731523525.417649", "text": "<@U0690EB5JE5> Degenkolb Ratings - the second tab is the version that has the EEID and the comment to upload", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731523525.417649", "reply_count": 1, "files": [{"id": "F080TTG2JAG", "created": 1731523516, "timestamp": 1731523516, "name": "DGK Ratings Sheet.xlsx", "title": "DGK Ratings Sheet.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 83702, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F080TTG2JAG/dgk_ratings_sheet.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F080TTG2JAG/download/dgk_ratings_sheet.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F080TTG2JAG-701dbc715b/dgk_ratings_sheet_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F080TTG2JAG-701dbc715b/dgk_ratings_sheet_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F080TTG2JAG/dgk_ratings_sheet.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F080TTG2JAG-b9b598589d", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "/6B/c", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Degenkolb Ratings - the second tab is the version that has the EEID and the comment to upload"}]}]}]}, {"ts": "1731523417.496349", "text": "<@U0690EB5JE5> It's common for startups to fall into the trap of building customer's every requirement. Thank you for your strategic thinking in preventing us from falling into this trap, and help us stay focused. :fire:", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1731523417.496349", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "c4rfG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " It's common for startups to fall into the trap of building customer's every requirement. Thank you for your strategic thinking in preventing us from falling into this trap, and help us stay focused. "}, {"type": "emoji", "name": "fire", "unicode": "1f525"}]}]}]}, {"ts": "1731608289.710159", "text": "<@U0690EB5JE5> ticket for a bug in pay bands\n<https://compiify.atlassian.net/browse/COM-3985>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1731608289.710159", "reply_count": 3, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14068::d589f2f72fac49f1a79aa858fa2fe12f", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3985?atlOrigin=eyJpIjoiYWU0MDlhYjk3ZmM2NGFlZjhjNjgwNzFjMDMyYWM4MjIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3985 Data Analysis of Department Pay Ranges :moneybag:>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14068::b6bafe821ca64655827a37494575609d", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14068\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3985\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3985", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "koshR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " ticket for a bug in pay bands\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3985"}]}]}]}, {"ts": "1731607580.244499", "text": "<@U0690EB5JE5> need to remove the hierarchical job category(Job type +job levels) under the employee name since Tithely does not have functional job levels\n<https://compiify.atlassian.net/browse/COM-3984>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1731607580.244499", "reply_count": 2, "edited": {"user": "U07M6QKHUC9", "ts": "1731607625.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14067::1dd51d869bd047f6a2f2303df0a97b94", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3984?atlOrigin=eyJpIjoiNzgzNjBmODA2OGM1NGJhZjg0ODcwODZiNWQ1MDk5OTMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3984 Hide Employee Job Category from Employee Name Columns>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14067::cd5084fb79c34d9da8337e5fa376357d", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14067\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3984\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3984", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "WvMff", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " need to remove the hierarchical job category(Job type +job levels) under the employee name since <PERSON><PERSON><PERSON> does not have functional job levels\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3984"}]}]}]}, {"ts": "1731606048.907269", "text": "<@U07EJ2LP44S> have upgraded dgoc ENV infra. ", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nwyJ+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " have upgraded dgoc ENV infra. "}]}]}]}, {"ts": "1731605448.273339", "text": "Need performance ratings for Valgenesis set up in back end, preferably by weekend. <https://compiify.atlassian.net/browse/COM-3983>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731605448.273339", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14066::b60bad1d781e463e860e91e5e3e47184", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3983?atlOrigin=eyJpIjoiYmZlMWY2MWU0ZjMxNDVmZjhhZmI3ZDlkNTgyZmQxYTciLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3983 Peformance ratings setup>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14066::a1447ad98ac342868b0c0db6bf08b6d1", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14066\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3983\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3983", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "uOTMX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Need performance ratings for Valgenesis set up in back end, preferably by weekend. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3983"}]}]}]}], "created_at": "2025-05-22T21:35:34.666887"}