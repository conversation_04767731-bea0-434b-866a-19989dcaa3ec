#!/usr/bin/env python
"""
Search API Performance Testing Script

This script analyzes performance bottlenecks in the Django Search API:
1. Tests search API endpoints directly
2. Measures detailed timing for each component
3. Identifies bottlenecks in the search pipeline
4. Reports performance metrics and recommendations

Author: AI Assistant
Date: 2025-01-30
"""

import os
import sys
import django
import logging
import time
import json
import requests
from datetime import datetime
from typing import Dict, List, Any, Tuple
from pathlib import Path

# Set up Django
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.search.services.rag_service import RAGService
from apps.search.services.unified_rag_service import UnifiedRAGService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SearchPerformanceTester:
    """
    Performance tester for the Search API with detailed bottleneck analysis.
    """

    def __init__(self):
        self.tenant = None
        self.user = None
        self.performance_data = {
            'test_start_time': None,
            'test_end_time': None,
            'queries_tested': 0,
            'total_time': 0.0,
            'component_timings': {
                'initialization': [],
                'vector_search': [],
                'llm_generation': [],
                'citation_creation': [],
                'database_operations': []
            },
            'bottlenecks': [],
            'recommendations': []
        }

    def setup_environment(self) -> Tuple[Tenant, User]:
        """Set up test environment."""
        logger.info("🔧 Setting up performance test environment...")

        # Get test tenant
        try:
            tenant = Tenant.objects.get(slug="test-tenant")
            logger.info(f"✅ Using existing tenant: {tenant.name}")
        except Tenant.DoesNotExist:
            logger.error("❌ Test tenant not found. Please run ingestion test first.")
            raise

        # Get test user
        try:
            user = User.objects.get(username="test_user")
            logger.info(f"✅ Using existing user: {user.username}")
        except User.DoesNotExist:
            logger.error("❌ Test user not found. Please run ingestion test first.")
            raise

        self.tenant = tenant
        self.user = user
        return tenant, user

    def test_component_initialization(self) -> Dict[str, float]:
        """Test the time taken to initialize RAG components."""
        logger.info("⏱️ Testing component initialization performance...")
        
        timings = {}
        
        # Test RAGService initialization
        start_time = time.time()
        rag_service = RAGService(user=self.user, tenant_slug=self.tenant.slug)
        rag_init_time = time.time() - start_time
        timings['rag_service_init'] = rag_init_time
        
        # Test UnifiedRAGService initialization
        start_time = time.time()
        unified_service = UnifiedRAGService(tenant=self.tenant.slug, user=self.user)
        unified_init_time = time.time() - start_time
        timings['unified_service_init'] = unified_init_time
        
        logger.info(f"   RAGService init: {rag_init_time:.3f}s")
        logger.info(f"   UnifiedRAGService init: {unified_init_time:.3f}s")
        
        self.performance_data['component_timings']['initialization'].append(timings)
        
        return timings

    def test_search_performance(self, queries: List[str]) -> Dict[str, Any]:
        """Test search performance with detailed component timing."""
        logger.info("🔍 Testing search performance with component breakdown...")
        
        results = {
            'queries': [],
            'total_time': 0.0,
            'average_time': 0.0,
            'component_breakdown': {
                'initialization': 0.0,
                'search_execution': 0.0,
                'result_processing': 0.0
            }
        }
        
        for i, query in enumerate(queries):
            logger.info(f"   Testing query {i+1}/{len(queries)}: '{query}'")
            
            query_result = self._test_single_query(query)
            results['queries'].append(query_result)
            results['total_time'] += query_result['total_time']
            
            # Aggregate component timings
            for component, timing in query_result['component_timings'].items():
                if component in results['component_breakdown']:
                    results['component_breakdown'][component] += timing
        
        # Calculate averages
        if len(queries) > 0:
            results['average_time'] = results['total_time'] / len(queries)
            for component in results['component_breakdown']:
                results['component_breakdown'][component] /= len(queries)
        
        self.performance_data['queries_tested'] = len(queries)
        self.performance_data['total_time'] = results['total_time']
        
        return results

    def _test_single_query(self, query: str) -> Dict[str, Any]:
        """Test a single query with detailed timing breakdown."""
        query_start = time.time()
        
        # Initialize service
        init_start = time.time()
        rag_service = RAGService(user=self.user, tenant_slug=self.tenant.slug)
        init_time = time.time() - init_start
        
        # Execute search
        search_start = time.time()
        try:
            result, docs = rag_service.search(
                query_text=query,
                top_k=5
            )
            search_time = time.time() - search_start
            success = True
            error = None
        except Exception as e:
            search_time = time.time() - search_start
            success = False
            error = str(e)
            result = None
            docs = []
        
        # Process results
        process_start = time.time()
        answer_length = len(str(result)) if result else 0
        doc_count = len(docs) if docs else 0
        process_time = time.time() - process_start
        
        total_time = time.time() - query_start
        
        return {
            'query': query,
            'success': success,
            'error': error,
            'total_time': total_time,
            'answer_length': answer_length,
            'document_count': doc_count,
            'component_timings': {
                'initialization': init_time,
                'search_execution': search_time,
                'result_processing': process_time
            }
        }

    def analyze_bottlenecks(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Analyze performance results to identify bottlenecks."""
        logger.info("🔍 Analyzing performance bottlenecks...")
        
        bottlenecks = []
        
        # Check average query time
        avg_time = results['average_time']
        if avg_time > 30.0:  # More than 30 seconds is concerning
            bottlenecks.append({
                'type': 'CRITICAL',
                'component': 'Overall Search',
                'issue': f'Average search time is {avg_time:.2f}s (>30s threshold)',
                'impact': 'High - Users will experience significant delays',
                'recommendation': 'Investigate LLM response time and vector search optimization'
            })
        elif avg_time > 10.0:  # More than 10 seconds is slow
            bottlenecks.append({
                'type': 'WARNING',
                'component': 'Overall Search',
                'issue': f'Average search time is {avg_time:.2f}s (>10s threshold)',
                'impact': 'Medium - Users may experience noticeable delays',
                'recommendation': 'Consider optimizing LLM calls and vector search'
            })
        
        # Check component breakdown
        breakdown = results['component_breakdown']
        
        # Check initialization time
        if breakdown['initialization'] > 5.0:
            bottlenecks.append({
                'type': 'WARNING',
                'component': 'Service Initialization',
                'issue': f'Service initialization takes {breakdown["initialization"]:.2f}s',
                'impact': 'Medium - Slow first-time response',
                'recommendation': 'Implement service caching or connection pooling'
            })
        
        # Check search execution time
        if breakdown['search_execution'] > 25.0:
            bottlenecks.append({
                'type': 'CRITICAL',
                'component': 'Search Execution',
                'issue': f'Search execution takes {breakdown["search_execution"]:.2f}s',
                'impact': 'High - Core search functionality is slow',
                'recommendation': 'Optimize LLM calls, vector search, and database queries'
            })
        elif breakdown['search_execution'] > 8.0:
            bottlenecks.append({
                'type': 'WARNING',
                'component': 'Search Execution',
                'issue': f'Search execution takes {breakdown["search_execution"]:.2f}s',
                'impact': 'Medium - Search could be faster',
                'recommendation': 'Review LLM model choice and vector search parameters'
            })
        
        self.performance_data['bottlenecks'] = bottlenecks
        
        return bottlenecks

    def generate_recommendations(self, bottlenecks: List[Dict[str, Any]]) -> List[str]:
        """Generate performance improvement recommendations."""
        logger.info("💡 Generating performance recommendations...")
        
        recommendations = []
        
        # General recommendations based on analysis
        recommendations.extend([
            "1. LLM Optimization:",
            "   - Consider using a faster LLM model (e.g., smaller Gemini model)",
            "   - Implement LLM response caching for common queries",
            "   - Use streaming responses for better perceived performance",
            "",
            "2. Vector Search Optimization:",
            "   - Reduce similarity_top_k parameter to retrieve fewer documents",
            "   - Implement vector search result caching",
            "   - Consider using approximate search algorithms",
            "",
            "3. Database Optimization:",
            "   - Add database indexes for frequently queried fields",
            "   - Optimize citation creation queries with bulk operations",
            "   - Use select_related/prefetch_related for related objects",
            "",
            "4. Service Architecture:",
            "   - Implement connection pooling for external services",
            "   - Cache initialized services between requests",
            "   - Consider async processing for non-critical operations"
        ])
        
        # Specific recommendations based on bottlenecks
        critical_bottlenecks = [b for b in bottlenecks if b['type'] == 'CRITICAL']
        if critical_bottlenecks:
            recommendations.extend([
                "",
                "5. Critical Issues to Address:",
            ])
            for bottleneck in critical_bottlenecks:
                recommendations.append(f"   - {bottleneck['component']}: {bottleneck['recommendation']}")
        
        self.performance_data['recommendations'] = recommendations
        
        return recommendations

    def save_performance_report(self, results: Dict[str, Any], bottlenecks: List[Dict[str, Any]], recommendations: List[str]):
        """Save detailed performance report to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"../docs/search_performance_report_{timestamp}.json"
        
        report = {
            'test_metadata': {
                'timestamp': timestamp,
                'test_start_time': self.performance_data['test_start_time'],
                'test_end_time': self.performance_data['test_end_time'],
                'queries_tested': self.performance_data['queries_tested']
            },
            'performance_results': results,
            'bottlenecks': bottlenecks,
            'recommendations': recommendations,
            'raw_performance_data': self.performance_data
        }
        
        try:
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            logger.info(f"📄 Performance report saved to: {filename}")
        except Exception as e:
            logger.error(f"❌ Failed to save performance report: {e}")

    def run_performance_test(self) -> Dict[str, Any]:
        """Run the complete performance test suite."""
        logger.info("🚀 Starting Search API Performance Test...")
        logger.info("=" * 80)
        
        self.performance_data['test_start_time'] = datetime.now()
        
        try:
            # Phase 1: Setup
            self.setup_environment()
            
            # Phase 2: Test component initialization
            init_timings = self.test_component_initialization()
            
            # Phase 3: Test search performance
            test_queries = [
                "budget adherence testing",
                "bug reports and issues",
                "manager recommendations",
                "testing updates",
                "showstopper bugs"
            ]
            
            search_results = self.test_search_performance(test_queries)
            
            # Phase 4: Analyze bottlenecks
            bottlenecks = self.analyze_bottlenecks(search_results)
            
            # Phase 5: Generate recommendations
            recommendations = self.generate_recommendations(bottlenecks)
            
            self.performance_data['test_end_time'] = datetime.now()
            
            # Phase 6: Save report
            self.save_performance_report(search_results, bottlenecks, recommendations)
            
            # Phase 7: Print summary
            self._print_performance_summary(search_results, bottlenecks, recommendations)
            
            logger.info("=" * 80)
            logger.info("🎉 Performance test completed!")
            
            return {
                'results': search_results,
                'bottlenecks': bottlenecks,
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"❌ Performance test failed: {e}")
            raise

    def _print_performance_summary(self, results: Dict[str, Any], bottlenecks: List[Dict[str, Any]], recommendations: List[str]):
        """Print performance test summary."""
        print("\n" + "=" * 80)
        print("📊 SEARCH API PERFORMANCE ANALYSIS SUMMARY")
        print("=" * 80)
        
        print(f"\n🔍 Test Results:")
        print(f"   - Queries tested: {len(results['queries'])}")
        print(f"   - Total time: {results['total_time']:.2f}s")
        print(f"   - Average time per query: {results['average_time']:.2f}s")
        
        print(f"\n⏱️ Component Breakdown (Average):")
        for component, time_val in results['component_breakdown'].items():
            print(f"   - {component.replace('_', ' ').title()}: {time_val:.2f}s")
        
        print(f"\n🚨 Bottlenecks Found: {len(bottlenecks)}")
        critical_count = len([b for b in bottlenecks if b['type'] == 'CRITICAL'])
        warning_count = len([b for b in bottlenecks if b['type'] == 'WARNING'])
        print(f"   - Critical: {critical_count}")
        print(f"   - Warnings: {warning_count}")
        
        if bottlenecks:
            print(f"\n🔍 Top Issues:")
            for i, bottleneck in enumerate(bottlenecks[:3], 1):
                print(f"   {i}. [{bottleneck['type']}] {bottleneck['component']}")
                print(f"      Issue: {bottleneck['issue']}")
                print(f"      Recommendation: {bottleneck['recommendation']}")
        
        print(f"\n💡 Key Recommendations:")
        for rec in recommendations[:5]:
            if rec.strip():
                print(f"   {rec}")


def main():
    """Main function to run the performance test."""
    print("⚡ Search API Performance Testing")
    print("=" * 50)
    
    tester = SearchPerformanceTester()
    
    try:
        report = tester.run_performance_test()
        return 0
        
    except Exception as e:
        print(f"\n❌ Performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
