#!/usr/bin/env python
"""
Script to ingest Slack data with the improved time-based chunking strategy.
"""

import os
import django
import json
import logging
from datetime import datetime

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk, EmbeddingMetadata
from apps.documents.interfaces.local_slack import LocalSlackSourceInterface
from apps.documents.services.local_ingestion_service import LocalIngestionService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_document_source(tenant, name="Local Slack Data", source_type="local_slack"):
    """Create a document source for the LocalSlackSourceInterface with two-year time period."""
    try:
        # Define the configuration for the LocalSlackSourceInterface with two-year time period
        config = {
            "data_dir": "data/slack/",  # Path to directory containing Slack data files
            "time_period": "custom",    # Use custom time period
            "custom_days": 730,         # Two years (365 * 2)
            "enable_semantic_cross_refs": True,  # Enable semantic cross-references
            "quality_threshold": 0.3,   # Only include documents with quality score >= 0.3
            "max_documents_per_channel": 1000,  # Limit documents per channel
        }

        # Create the document source
        source = DocumentSource.objects.create(
            tenant=tenant,
            name=name,
            source_type=source_type,
            config=config,
            is_active=True
        )

        logger.info(f"Created document source: {source.name} ({source.source_type})")
        return source

    except Exception as e:
        logger.error(f"Error creating document source: {str(e)}")
        return None

def ingest_slack_data(tenant, source):
    """Ingest Slack data using the LocalIngestionService."""
    try:
        # Create ingestion service
        ingestion_service = LocalIngestionService(tenant=tenant)

        # Get source interface
        source_interface = ingestion_service._get_source_interface(source)

        # Fetch documents
        documents = source_interface.fetch_documents()
        logger.info(f"Fetched {len(documents)} documents from {source.name}")

        # Debug document structure
        if documents:
            first_doc = documents[0]
            logger.info(f"First document structure: {type(first_doc)}")
            logger.info(f"First document keys: {first_doc.keys() if isinstance(first_doc, dict) else 'Not a dict'}")

            # Print first few keys and values
            if isinstance(first_doc, dict):
                for key, value in list(first_doc.items())[:5]:
                    logger.info(f"Key: {key}, Value type: {type(value)}")

                # Check metadata specifically
                if 'metadata' in first_doc:
                    metadata = first_doc['metadata']
                    logger.info(f"Metadata type: {type(metadata)}")
                    if isinstance(metadata, str):
                        try:
                            metadata_dict = json.loads(metadata)
                            logger.info(f"Parsed metadata keys: {metadata_dict.keys()}")
                            # Update the document with parsed metadata
                            first_doc['metadata'] = metadata_dict
                        except json.JSONDecodeError:
                            logger.error("Failed to parse metadata as JSON")

        # Process each document
        processed = 0
        failed = 0

        for document in documents:
            try:
                # Ensure document is a dictionary
                if not isinstance(document, dict):
                    logger.error(f"Document is not a dictionary: {type(document)}")
                    failed += 1
                    continue

                # Ensure metadata is a dictionary
                if 'metadata' in document and isinstance(document['metadata'], str):
                    try:
                        document['metadata'] = json.loads(document['metadata'])
                    except json.JSONDecodeError:
                        logger.error("Failed to parse metadata as JSON")
                        document['metadata'] = {}

                # Create raw document manually
                try:
                    # Extract document fields
                    doc_id = document.get('id', '')
                    title = document.get('title', '')
                    content = document.get('content', '')
                    content_type = document.get('content_type', 'text/plain')
                    metadata = document.get('metadata', {})

                    # Create raw document
                    doc = RawDocument.objects.create(
                        source=source,
                        external_id=doc_id,
                        title=title,
                        content=content,
                        content_type=content_type,
                        metadata=metadata if isinstance(metadata, str) else json.dumps(metadata)
                    )

                    # Process document with chunker
                    chunker = ingestion_service._get_chunker()
                    chunks = chunker.chunk_document(
                        content,
                        content_type=content_type,
                        metadata={
                            "document_id": doc.id,
                            **metadata
                        }
                    )

                    # Create document chunks
                    db_chunks = []
                    for i, chunk in enumerate(chunks):
                        db_chunk = DocumentChunk.objects.create(
                            document=doc,
                            chunk_id=f"{doc.id}-{i}",
                            content=chunk.page_content,
                            chunk_type=content_type,
                            metadata=json.dumps(chunk.metadata)
                        )
                        db_chunks.append(db_chunk)

                    processed += 1
                    logger.info(f"Processed document: {doc.title} - Created {len(db_chunks)} chunks")
                except Exception as e:
                    logger.error(f"Error creating document: {str(e)}")
                    failed += 1
            except Exception as e:
                logger.error(f"Error processing document: {str(e)}")
                failed += 1

        logger.info(f"Ingestion completed: {processed} documents processed, {failed} documents failed")
        return processed, failed

    except Exception as e:
        logger.error(f"Error ingesting data: {str(e)}")
        return 0, 0

def print_database_stats():
    """Print database statistics."""
    logger.info("Database Statistics:")
    logger.info(f"Raw Documents: {RawDocument.objects.count()}")
    logger.info(f"Document Chunks: {DocumentChunk.objects.count()}")
    logger.info(f"Document Sources: {DocumentSource.objects.count()}")
    logger.info(f"Embedding Metadata: {EmbeddingMetadata.objects.count()}")

    # Get Slack sources
    slack_sources = DocumentSource.objects.filter(source_type__in=["slack", "local_slack"])
    logger.info(f"Slack Sources: {slack_sources.count()}")

    for source in slack_sources:
        docs = RawDocument.objects.filter(source=source)
        chunks = DocumentChunk.objects.filter(document__source=source)
        logger.info(f"  - {source.name} ({source.source_type}): {docs.count()} documents, {chunks.count()} chunks")

        # Get document counts by content type
        content_types = docs.values_list('content_type', flat=True).distinct()
        for content_type in content_types:
            doc_count = docs.filter(content_type=content_type).count()
            chunk_count = chunks.filter(chunk_type=content_type).count()
            logger.info(f"    - {content_type}: {doc_count} documents, {chunk_count} chunks")

    # Get chunking strategies for Slack documents
    if slack_sources.exists():
        logger.info("Chunking Strategies:")
        for source in slack_sources:
            docs = RawDocument.objects.filter(source=source)
            for doc in docs[:5]:  # Limit to 5 documents to avoid too much output
                try:
                    metadata = doc.metadata
                    if isinstance(metadata, str):
                        metadata = json.loads(metadata)

                    chunking_strategy = metadata.get('chunking_strategy', 'unknown')
                    period_type = metadata.get('period_type', 'unknown')
                    logger.info(f"  - {doc.title}: {chunking_strategy} ({period_type})")
                except Exception as e:
                    logger.info(f"  - {doc.title}: Error parsing metadata - {str(e)}")

def main():
    """Main function to ingest Slack data with the improved time-based chunking strategy."""
    logger.info("Starting Slack data ingestion with time-based chunking strategy...")

    # Get or create tenant
    tenant, created = Tenant.objects.get_or_create(
        slug="default",
        defaults={"name": "Default Tenant"}
    )

    # Print initial database stats
    logger.info("Initial database stats:")
    print_database_stats()

    # Create document source
    source = create_document_source(tenant)
    if not source:
        logger.error("Failed to create document source. Exiting.")
        return

    # Ingest Slack data
    logger.info("Ingesting Slack data with two-year time period...")
    processed, failed = ingest_slack_data(tenant, source)

    # Print final database stats
    logger.info("Final database stats after ingestion:")
    print_database_stats()

    logger.info("Slack data ingestion completed.")

if __name__ == "__main__":
    main()
