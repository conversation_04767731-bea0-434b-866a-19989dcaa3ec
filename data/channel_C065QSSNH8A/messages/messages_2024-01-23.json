{"date": "2024-01-23", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1705978431.487359", "text": "<@U04DKEFP1K8> does aws offer a service to purchase a domain if the existing owner is willing to sell it?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1705978431.487359", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "lfob5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " does aws offer a service to purchase a domain if the existing owner is willing to sell it?"}]}]}]}, {"ts": "1705972013.557359", "text": "Priorities for Eng for the next day:\n• DA: Production instance won't load a new cycle (<https://compiify.atlassian.net/browse/COM-2208|COM-2208>)\n• SDF: Allow blank prefill while still giving recommendations (<https://compiify.atlassian.net/browse/COM-2200|COM-2200> and <https://compiify.atlassian.net/browse/COM-2206|COM-2206>)\n• SDF: Correct the Monthly XLM values in the tooltip (<https://compiify.atlassian.net/browse/COM-2209|COM-2209>)\n• SDF: Load Stellar's custom performance rating scale (<https://compiify.atlassian.net/browse/COM-2020|COM-2020>)\n• Export / download support (<https://compiify.atlassian.net/browse/COM-2060|COM-2060>)\n• Various fixes for filters (<https://compiify.atlassian.net/browse/COM-2204|COM-2204>, <https://compiify.atlassian.net/browse/COM-2139|COM-2139>)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705972013.557359", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "aRSfE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA: Production instance won't load a new cycle ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2208", "text": "COM-2208"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF: Allow blank prefill while still giving recommendations ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2200", "text": "COM-2200"}, {"type": "text", "text": " and "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2206", "text": "COM-2206"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF: Correct the Monthly XLM values in the tooltip ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2209", "text": "COM-2209"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF: Load Stellar's custom performance rating scale ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2020", "text": "COM-2020"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Export / download support ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2060", "text": "COM-2060"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Various fixes for filters ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2204", "text": "COM-2204"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2139", "text": "COM-2139"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1705966153.255219", "text": "<!here> i have meeting from 8pm - 1130pm today ( will be logging off around 445pm)", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "SQxve", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " i have meeting from 8pm - 1130pm today ( will be logging off around 445pm)"}]}]}]}, {"ts": "1705952109.690369", "text": "<@U0658EW4B8D> For your testing this week:\n• Please use <https://qa.compiify.com> with \"<PERSON>\" login from <https://docs.google.com/document/d/1it4-9IEwBbS0izBRJKJp-h13bHAt_QJP2E7vUqeRAP4/edit|this doc>\n• Be sure to test a combo of merit, market adjustment, and bonus changes\n• Log any bugs you find in JIRA, as subtasks of the <https://compiify.atlassian.net/browse/COM-2145|Neuroflow UAT >ticket\n• When you've completed making adjustments, ping me and <@U04DKEFP1K8> for help generating adjustment letters for testing.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705952109.690369", "reply_count": 19, "edited": {"user": "U065H3M6WJV", "ts": "1705952112.000000"}, "reactions": [{"name": "+1", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "MLb+H", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " For your testing this week:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please use "}, {"type": "link", "url": "https://qa.compiify.com"}, {"type": "text", "text": " with \"<PERSON>\" login from "}, {"type": "link", "url": "https://docs.google.com/document/d/1it4-9IEwBbS0izBRJKJp-h13bHAt_QJP2E7vUqeRAP4/edit", "text": "this doc"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Be sure to test a combo of merit, market adjustment, and bonus changes"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Log any bugs you find in JIRA, as subtasks of the "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2145", "text": "Neuroflow UAT "}, {"type": "text", "text": "ticket"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "When you've completed making adjustments, ping me and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " for help generating adjustment letters for testing."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1705951668.905349", "text": "Upcoming environment changes, in order of priority & sequence:\n• :white_check_mark: *SDF production* instance - will be shared with <PERSON> and <PERSON><PERSON> *today* (Monday) - _Done!_\n• :white_check_mark: *DA production* instance - data will be loaded *today* to share with <PERSON><PERSON><PERSON> and <PERSON> *tomorrow* (Tuesday) - _Done!_\n• :white_check_mark: *Staging* - will be updated later *today* (Monday, after 2pm) to get the latest features including table filters & various fixes - Done!\nThings we're *not* planning:\n• No urgent fixes identified for Neuroflow, so we'll keep that stable\n• No new data provided from Convene, Cipher, Cypher, or MX\n\n<@U04DS2MBWP4> Any others in the pipeline that we need to be planning as new beta environments?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705951668.905349", "reply_count": 6, "edited": {"user": "U065H3M6WJV", "ts": "1706036420.000000"}, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "v2PU+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Upcoming environment changes, in order of priority & sequence:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "white_check_mark", "unicode": "2705", "style": {"bold": true}}, {"type": "text", "text": " SDF production", "style": {"bold": true}}, {"type": "text", "text": " instance - will be shared with <PERSON> and <PERSON><PERSON> "}, {"type": "text", "text": "today", "style": {"bold": true}}, {"type": "text", "text": " (Monday) - "}, {"type": "text", "text": "Done!", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "white_check_mark", "unicode": "2705", "style": {"bold": true}}, {"type": "text", "text": " DA production ", "style": {"bold": true}}, {"type": "text", "text": "instance - data will be loaded"}, {"type": "text", "text": " today ", "style": {"bold": true}}, {"type": "text", "text": "to share with <PERSON><PERSON><PERSON> and <PERSON> "}, {"type": "text", "text": "tomorrow", "style": {"bold": true}}, {"type": "text", "text": " (Tuesday) - "}, {"type": "text", "text": "Done!", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "white_check_mark", "unicode": "2705", "style": {"bold": true}}, {"type": "text", "text": " Staging ", "style": {"bold": true}}, {"type": "text", "text": "- will be updated later "}, {"type": "text", "text": "today", "style": {"bold": true}}, {"type": "text", "text": " (Monday, after 2pm) to get the latest features including table filters & various fixes - Done!"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nThings we're "}, {"type": "text", "text": "not", "style": {"bold": true}}, {"type": "text", "text": " planning:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No urgent fixes identified for <PERSON><PERSON><PERSON><PERSON>, so we'll keep that stable"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "No new data provided from Convene, Cipher, Cypher, or MX"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n\n"}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Any others in the pipeline that we need to be planning as new beta environments?"}]}]}]}], "created_at": "2025-05-22T21:35:34.589882"}