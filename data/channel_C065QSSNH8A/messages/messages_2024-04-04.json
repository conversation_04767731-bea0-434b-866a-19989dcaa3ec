{"date": "2024-04-04", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1712253036.887029", "text": "<@U065H3M6WJV> I think we can remove entirely. Also, We can setup the board to have only tickets closed in last week.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1712253036.887029", "reply_count": 3, "edited": {"user": "U0690EB5JE5", "ts": "1712253048.000000"}, "reactions": [{"name": "bulb", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "wpypR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I think we can remove entirely. Also, We can setup the board to have only tickets closed in last week."}]}]}]}, {"ts": "1712252969.543469", "text": "<@U0690EB5JE5> What process do you want to use for removing tickets from the Eng Priorities board once they're done? (I've been moving the status to 'Done' once I can verify them in a test env, do you want to remove them entirely from the board at that point, or wait til they've been pushed to a production env?)", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "madWv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " What process do you want to use for removing tickets from the Eng Priorities board once they're done? (I've been moving the status to 'Done' once I can verify them in a test env, do you want to remove them entirely from the board at that point, or wait til they've been pushed to a production env?)"}]}]}]}], "created_at": "2025-05-22T21:35:34.606233"}