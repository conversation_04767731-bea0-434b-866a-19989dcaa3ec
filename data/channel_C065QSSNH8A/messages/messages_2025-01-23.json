{"date": "2025-01-23", "channel_id": "C065QSSNH8A", "message_count": 12, "messages": [{"ts": "1737656468.812519", "text": "<@U0690EB5JE5> Div Deletes. I'm probably doing something wrong again but won't upload. Only includes update type of D and EEID", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737656468.812519", "reply_count": 3, "files": [{"id": "F089XSTUNN9", "created": 1737656451, "timestamp": 1737656451, "name": "DivDeletes.csv", "title": "DivDeletes.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 113, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089XSTUNN9/divdeletes.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089XSTUNN9/download/divdeletes.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089XSTUNN9/divdeletes.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089XSTUNN9-8ac55cce63", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F089XSTUNN9/divdeletes.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *\r\nD,30440\r\nD,17217\r\nD,17348\r\nD,19586\r\nD,11602\r\nD,19585\r\nD,24924\r\nD,17247", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">30440</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">17217</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">17348</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">19586</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">11602</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">19585</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">24924</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">17247</div></div></div>\n</div>\n", "lines": 9, "lines_more": 0, "preview_is_truncated": false, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "/RrA6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Div Deletes. I'm probably doing something wrong again but won't upload. Only includes update type of D and EEID"}]}]}]}, {"ts": "1737655321.657089", "text": "FYI on Diversified Letters - <PERSON> is advising <PERSON> against it, she just doesn't want to deal with it, so most likely we won't end up doing their letters. But she's waiting to hear back from him", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}, {"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "XJOUR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "FYI on Diversified Letters - <PERSON> is advising <PERSON> against it, she just doesn't want to deal with it, so most likely we won't end up doing their letters. But she's waiting to hear back from him"}]}]}]}, {"ts": "1737654543.437469", "text": "<@U07EJ2LP44S> can it wait until tomorrow?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.009819", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "LBhKI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can it wait until tomorrow?"}]}]}]}, {"ts": "**********.546999", "text": "<@U0690EB5JE5> For Valgenesis SSO - the list of recommenders should be finalized in the account. These people need to be added to the SSO on our end, correct?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.546999", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "gUl1S", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " For Valgenesis SSO - the list of recommenders should be finalized in the account. These people need to be added to the SSO on our end, correct?"}]}]}]}, {"ts": "**********.009819", "text": "<@U0690EB5JE5> here is the Curana's data file from their HRIS", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.009819", "reply_count": 13, "files": [{"id": "F089XER2G21", "created": **********, "timestamp": **********, "name": "Curana-Stride 01.23.xlsx", "title": "Curana-Stride 01.23.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 312950, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089XER2G21/curana-stride_01.23.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089XER2G21/download/curana-stride_01.23.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F089XER2G21-4bbd2e4025/curana-stride_01.23_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F089XER2G21-4bbd2e4025/curana-stride_01.23_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089XER2G21/curana-stride_01.23.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089XER2G21-6469c6334c", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "mlQBK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " here is the Curana's data file from their HRIS"}]}]}]}, {"ts": "1737651836.826439", "text": "<@U0690EB5JE5> confirming that Tithely does not have equity", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "c8iVj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " confirming that Tithely does not have equity"}]}]}]}, {"ts": "1737651686.831419", "text": "<@U07EJ2LP44S>", "user": "U07M6QKHUC9", "type": "message", "files": [{"id": "F08A071HB9Q", "created": 1737651681, "timestamp": 1737651681, "name": "Sample Adjustment Letter.pdf", "title": "Sample Adjustment Letter.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 23684, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08A071HB9Q/sample_adjustment_letter.pdf", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08A071HB9Q/download/sample_adjustment_letter.pdf", "media_display_type": "unknown", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A071HB9Q-8b51c8d7c1/sample_adjustment_letter_thumb_pdf.png", "thumb_pdf_w": 911, "thumb_pdf_h": 1288, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08A071HB9Q/sample_adjustment_letter.pdf", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08A071HB9Q-ab0583817a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "oI5mB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1737651371.524569", "text": "<@U07EJ2LP44S>", "user": "U0690EB5JE5", "type": "message", "files": [{"id": "F08AP0K10U8", "created": 1737651367, "timestamp": 1737651367, "name": "Tith<PERSON> - Tithley_sync.csv", "title": "Tith<PERSON> - Tithley_sync.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": true, "size": 6700, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08AP0K10U8/tithley_-_tithley_sync.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08AP0K10U8/download/tithley_-_tithley_sync.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AP0K10U8/tithley_-_tithley_sync.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08AP0K10U8-f4632cc9cf", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AP0K10U8/tithley_-_tithley_sync.csv/edit", "preview": ",\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">359</div><div class=\"cm-col\">Nicholas</div><div class=\"cm-col\">Wojtanowicz</div><div class=\"cm-col\">Nicholas Wojtanowicz</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">U</div><div class=\"cm-col\">01/13/25</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">156</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">01/13/25</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IL</div><div class=\"cm-col\">Customer Support Advocate</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">47840</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">360</div><div class=\"cm-col\">Daniel</div><div class=\"cm-col\">Tak</div><div class=\"cm-col\">Daniel Tak</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">U</div><div class=\"cm-col\">01/13/25</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">156</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">01/13/25</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">CA</div><div class=\"cm-col\">Customer Support Advocate</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">49920</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">358</div><div class=\"cm-col\">Maame</div><div class=\"cm-col\">Richardson</div><div class=\"cm-col\">Sika Richardson</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">U</div><div class=\"cm-col\">01/13/25</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">200</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">01/13/25</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">CA</div><div class=\"cm-col\">Operations Specialist</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\">No</div><div class=\"cm-col\"></div><div class=\"cm-col\">PART_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">56160</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">363</div><div class=\"cm-col\">Derek</div><div class=\"cm-col\">House</div><div class=\"cm-col\">Derek House</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">U</div><div class=\"cm-col\">01/21/25</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">357</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">01/21/25</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">MI</div><div class=\"cm-col\">Principal Data Scientist</div><div class=\"cm-col\">Corporate Finance Accounting and Compliance</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">200000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">357</div><div class=\"cm-col\">Chris</div><div class=\"cm-col\">Phillips</div><div class=\"cm-col\">Chris Phillips</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">U</div><div class=\"cm-col\">01/01/25</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">31</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">01/01/25</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">CO</div><div class=\"cm-col\">CFO</div><div class=\"cm-col\">Executive</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">325000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">361</div><div class=\"cm-col\">Timothy</div><div class=\"cm-col\">Armbruster</div><div class=\"cm-col\">Tim Armbruster</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">U</div><div class=\"cm-col\">01/21/25</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">341</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">01/21/25</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">NC</div><div class=\"cm-col\">Sr. Manager of Revenue Operations</div><div class=\"cm-col\">Sales</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">135000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 36, "lines_more": 35, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "oI5mB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1737651345.782139", "text": "<@U0690EB5JE5> here is the employee history report for Tithely. Let us know if you have any questions on it.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737651345.782139", "reply_count": 1, "files": [{"id": "F089KKHFAGP", "created": 1737651286, "timestamp": 1737651286, "name": "Justworks - Tithe.ly Work History Report 01_23_2025 (Stride).xlsx", "title": "Justworks - Tithe.ly Work History Report 01_23_2025 (Stride).xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 111747, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089KKHFAGP/justworks_-_tithe.ly_work_history_report_01_23_2025__stride_.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089KKHFAGP/download/justworks_-_tithe.ly_work_history_report_01_23_2025__stride_.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F089KKHFAGP-1c67e38d4f/justworks_-_tithe.ly_work_history_report_01_23_2025__stride__converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F089KKHFAGP-1c67e38d4f/justworks_-_tithe.ly_work_history_report_01_23_2025__stride__thumb_pdf.png", "thumb_pdf_w": 935, "thumb_pdf_h": 1210, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089KKHFAGP/justworks_-_tithe.ly_work_history_report_01_23_2025__stride_.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089KKHFAGP-b42688e90a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "qrnti", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " here is the employee history report for <PERSON><PERSON><PERSON>. Let us know if you have any questions on it."}]}]}]}, {"ts": "1737648944.894719", "text": "I am attempting to input the parameters for <PERSON><PERSON><PERSON>'s performance/compa ratio matrix, but their parameters don't match what's in the system. We use:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737648944.894719", "reply_count": 7, "files": [{"id": "F089ZQRST99", "created": 1737648929, "timestamp": 1737648929, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 96946, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089ZQRST99/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089ZQRST99/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F089ZQRST99-80106a5956/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F089ZQRST99-80106a5956/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F089ZQRST99-80106a5956/image_360.png", "thumb_360_w": 360, "thumb_360_h": 169, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F089ZQRST99-80106a5956/image_480.png", "thumb_480_w": 480, "thumb_480_h": 226, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F089ZQRST99-80106a5956/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F089ZQRST99-80106a5956/image_720.png", "thumb_720_w": 720, "thumb_720_h": 338, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F089ZQRST99-80106a5956/image_800.png", "thumb_800_w": 800, "thumb_800_h": 376, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F089ZQRST99-80106a5956/image_960.png", "thumb_960_w": 960, "thumb_960_h": 451, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F089ZQRST99-80106a5956/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 481, "original_w": 1758, "original_h": 826, "thumb_tiny": "AwAWADDRxzS4FFFABxRwKKKAFzRmkooAO9FFFABRigUtABikxS0UAf/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089ZQRST99/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089ZQRST99-fc2d800488", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "L2q5M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am attempting to input the parameters for <PERSON><PERSON><PERSON>'s performance/compa ratio matrix, but their parameters don't match what's in the system. We use:"}]}]}]}, {"ts": "1737570907.813449", "text": "<@U0690EB5JE5> The benefits data and the letter template are both in the Tithely channel. I have not had time to extract them into tickets.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737570907.813449", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "g/eyB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " The benefits data and the letter template are both in the Tithely channel. I have not had time to extract them into tickets."}]}]}]}, {"ts": "1737570821.186969", "text": "<@U07M6QKHUC9> I'm not able to do the work that needs to be done in 20 hours a week. I have worked a minimum of 6 hours a day just to stay on top of all these issues and go back and forth with customers. I'm going to need more support or more hours, I cannot do all of this in PT hours.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737570821.186969", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "Qy1l/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I'm not able to do the work that needs to be done in 20 hours a week. I have worked a minimum of 6 hours a day just to stay on top of all these issues and go back and forth with customers. I'm going to need more support or more hours, I cannot do all of this in PT hours."}]}]}]}], "created_at": "2025-05-22T21:35:34.705370"}