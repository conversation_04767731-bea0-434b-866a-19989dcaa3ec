{"thread_ts": "**********.428329", "channel_id": "C065QSSNH8A", "reply_count": 40, "replies": [{"ts": "**********.503379", "text": "Which ENV is this?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "recPm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Which ENV is this?"}]}]}]}, {"ts": "**********.635279", "text": "<PERSON> went into the cycle to add someone, but he never hit publish. We had the setting on in the account to default everyone to 100%", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "4RI6Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> went into the cycle to add someone, but he never hit publish. We had the setting on in the account to default everyone to 100%"}]}]}]}, {"ts": "**********.387179", "text": "curana mip", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "ZIEYe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "curana mip"}]}]}]}, {"ts": "**********.973599", "text": "", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "files": [{"id": "F08GS9LF151", "created": **********, "timestamp": **********, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 111901, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GS9LF151/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GS9LF151/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GS9LF151-265a0509f3/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GS9LF151-265a0509f3/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GS9LF151-265a0509f3/image_360.png", "thumb_360_w": 360, "thumb_360_h": 75, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GS9LF151-265a0509f3/image_480.png", "thumb_480_w": 480, "thumb_480_h": 100, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GS9LF151-265a0509f3/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GS9LF151-265a0509f3/image_720.png", "thumb_720_w": 720, "thumb_720_h": 149, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GS9LF151-265a0509f3/image_800.png", "thumb_800_w": 800, "thumb_800_h": 166, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GS9LF151-265a0509f3/image_960.png", "thumb_960_w": 960, "thumb_960_h": 199, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GS9LF151-265a0509f3/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 212, "original_w": 2324, "original_h": 482, "thumb_tiny": "AwAJADDSJNBJpD1o7CgAxz1P50Y5xz+dKeopO9AC8+1AJpaaKAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GS9LF151/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GS9LF151-60b8119fb9", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}]}, {"ts": "**********.658799", "text": "You can see in the change history there's a timestamp for <PERSON>. He swears he did NOT hit publish b/c he was afraid it would change things (and I believe him, he tends to not want to touch anything in the account at all).", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "f2Jhw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "You can see in the change history there's a timestamp for <PERSON>. He swears he did NOT hit publish b/c he was afraid it would change things (and I believe him, he tends to not want to touch anything in the account at all)."}]}]}]}, {"ts": "**********.711319", "text": "So the real issue is this is supposed to be finalized TODAY", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "nzhG5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So the real issue is this is supposed to be finalized TODAY"}]}]}]}, {"ts": "**********.320339", "text": "to meet payroll. So if there is any way for us to revert back the people that were changed by this action. Or, if we can get a report of them somehow from the backend, we can manually go back in to update them to what they were using the change history", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "V/8uO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "to meet payroll. So if there is any way for us to revert back the people that were changed by this action. Or, if we can get a report of them somehow from the backend, we can manually go back in to update them to what they were using the change history"}]}]}]}, {"ts": "1741373009.241889", "text": "<@U0690EB5JE5> how do we address this?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "PjMig", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " how do we address this?"}]}]}]}, {"ts": "1741373058.107769", "text": "<@U07EJ2LP44S> when you say he went to add? he made changes to eligibility rules?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "tp5J1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " when you say he went to add? he made changes to eligibility rules?"}]}]}]}, {"ts": "1741373117.049319", "text": "He was adding someone as a recommender", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "7QMNa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "He was adding someone as a recommender"}]}]}]}, {"ts": "1741373125.778729", "text": "If I understand correctly. We need to rollback the amounts before submission. correct?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "Wuwz8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If I understand correctly. We need to rollback the amounts before submission. correct?"}]}]}]}, {"ts": "**********.111359", "text": "Yes correct", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "3H+xJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes correct"}]}]}]}, {"ts": "**********.783389", "text": "But not for the whole account b/c people have made updates since then", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "yPFLL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "But not for the whole account b/c people have made updates since then"}]}]}]}, {"ts": "**********.861789", "text": "Can this wait till Monday? We can but I need to make sure I am not messing up whole account.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "Wn+ME", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can this wait till Monday? We can but I need to make sure I am not messing up whole account."}]}]}]}, {"ts": "**********.870449", "text": "No it cannot.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "/i9M0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No it cannot."}]}]}]}, {"ts": "**********.054589", "text": "Unfortunately they are supposed to pay it on monday so it still has to get approved and go to payroll before monday", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "8ORyg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Unfortunately they are supposed to pay it on monday so it still has to get approved and go to payroll before monday"}]}]}]}, {"ts": "**********.798139", "text": "this is super time sensitive <@U0690EB5JE5>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "QnTEr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "this is super time sensitive "}, {"type": "user", "user_id": "U0690EB5JE5"}]}]}]}, {"ts": "**********.915629", "text": "We can export whats in the account now for safekeeping", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "G75VZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can export whats in the account now for safekeeping"}]}]}]}, {"ts": "**********.330049", "text": "That way if someone DOES get messed up we have the work saved", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "Cu+za", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That way if someone DOES get messed up we have the work saved"}]}]}]}, {"ts": "**********.143229", "text": "I am super exhausted right now :disappointed: . Let me give a try. I will probably not update the system. Will see If I can pull a report quickly from audit log which I don't think is also simple. But let me give a try.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "6q8+n", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am super exhausted right now "}, {"type": "emoji", "name": "disappointed", "unicode": "1f61e"}, {"type": "text", "text": " . Let me give a try. I will probably not update the system. Will see If I can pull a report quickly from audit log which I don't think is also simple. But let me give a try."}]}]}]}, {"ts": "1741373741.029899", "text": "<@U07EJ2LP44S> <PERSON> sent an urgent meeting request for 10:30 but I didn't see as I was on a different call.\nDo we need to meet with him now?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "U+37D", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " <PERSON> sent an urgent meeting request for 10:30 but I didn't see as I was on a different call.\nDo we need to meet with him now?"}]}]}]}, {"ts": "1741373825.297739", "text": "I already did", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nu9ue", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I already did"}]}]}]}, {"ts": "1741373847.772819", "text": "I told him I'd get back to him with a plan", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "SBF6p", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I told him I'd get back to him with a plan"}]}]}]}, {"ts": "1741375146.288729", "text": "<@U0690EB5JE5> <PERSON><PERSON><PERSON> is asking for the ETA?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "wwTvg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> is asking for the ETA?"}]}]}]}, {"ts": "1741375185.791399", "text": "<@U07EJ2LP44S> Finally I am able to get the list of employees impacted around 127", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.428329", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "files": [{"id": "F08GNTRQF6Z", "created": 1741375180, "timestamp": 1741375180, "name": "Bonuses_Updated_to_100_percent.xlsx", "title": "Bonuses_Updated_to_100_percent.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 34073, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GNTRQF6Z/bonuses_updated_to_100_percent.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GNTRQF6Z/download/bonuses_updated_to_100_percent.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GNTRQF6Z-3f100986ea/bonuses_updated_to_100_percent_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GNTRQF6Z-3f100986ea/bonuses_updated_to_100_percent_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GNTRQF6Z/bonuses_updated_to_100_percent.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GNTRQF6Z-68f429849b", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "5Sh5m", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Finally I am able to get the list of employees impacted around 127"}]}]}]}, {"ts": "1741375225.578129", "text": "<PERSON> must have clicked publish, there is no other way, may be he didn't realize.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.428329", "edited": {"user": "U0690EB5JE5", "ts": "1741375243.000000"}, "blocks": [{"type": "rich_text", "block_id": "MY9Df", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> must have clicked publish, there is no other way, may be he didn't realize."}]}]}]}, {"ts": "1741375258.240569", "text": "Is there any way to get an employee ID with this report? I can vlookup if not", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "da9OK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is there any way to get an employee ID with this report? I can vlookup if not"}]}]}]}, {"ts": "1741375291.278749", "text": "Actually never mind", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "4vwwv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Actually never mind"}]}]}]}, {"ts": "1741375294.586759", "text": "There name in the report. Do you still need employeeId?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "uFHr5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There name in the report. Do you still need employeeId?"}]}]}]}, {"ts": "1741375309.165429", "text": "No I can vlookup on the name, it looks like the same format", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "NLsKO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No I can vlookup on the name, it looks like the same format"}]}]}]}, {"ts": "1741375481.081209", "text": "I will be available for 30mnts more just in case. Let me anything required.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.428329", "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Qc/5U", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will be available for 30mnts more just in case. Let me anything required."}]}]}]}, {"ts": "**********.424729", "text": "Last thing is just a quesiton - can we upload these changes back into the account?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "kaIJZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Last thing is just a quesiton - can we upload these changes back into the account?"}]}]}]}, {"ts": "**********.770109", "text": "Or do we have to work offline", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "4Rrma", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Or do we have to work offline"}]}]}]}, {"ts": "**********.638399", "text": "Uploading is tricky, If its time sensitive. Offline is the safest way. If need my help. I can take care of it on Monday as we need to take care of other calculations as well.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "Hj7YJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Uploading is tricky, If its time sensitive. Offline is the safest way. If need my help. I can take care of it on Monday as we need to take care of other calculations as well."}]}]}]}, {"ts": "**********.252039", "text": "Ok. we'll just download another sheet when it's complete, and update the file I created with the correct data. (I'm attaching it just in case)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "files": [{"id": "F08GP4H3QSZ", "created": 1741377507, "timestamp": 1741377507, "name": "CuranaMIP.xlsx", "title": "CuranaMIP.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 27148, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GP4H3QSZ/curanamip.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GP4H3QSZ/download/curanamip.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GP4H3QSZ-8e0209f9e4/curanamip_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GP4H3QSZ-8e0209f9e4/curanamip_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GP4H3QSZ/curanamip.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GP4H3QSZ-be7ba454ae", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "5qpUr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. we'll just download another sheet when it's complete, and update the file I created with the correct data. (I'm attaching it just in case)"}]}]}]}, {"ts": "1741377535.991649", "text": "In that sheet I updated the ones that had been changed to the 'previous' amount and percentage", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "U5dZ2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In that sheet I updated the ones that had been changed to the 'previous' amount and percentage"}]}]}]}, {"ts": "1741377643.694269", "text": "<@U07EJ2LP44S> the above sheet is just copy of merit table correct? i.e. current state.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "RX3QO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " the above sheet is just copy of merit table correct? i.e. current state."}]}]}]}, {"ts": "1741379265.704029", "text": "The above sheet is the merit table that I have updated with the correct numbers. I downloaded the sheet as of 2 o’clock my time, and then updated to the previous amount for those that changed.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2+UBL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The above sheet is the merit table that I have updated with the correct numbers. I downloaded the sheet as of 2 o’clock my time, and then updated to the previous amount for those that changed."}]}]}]}, {"ts": "1741382831.266499", "text": "That sheet was actually wrong, so don't bother even looking at it", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "blocks": [{"type": "rich_text", "block_id": "1iagW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That sheet was actually wrong, so don't bother even looking at it"}]}]}]}, {"ts": "1741382842.055269", "text": "They manually fixed everything using the raw data file you provided", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.428329", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Mz/rY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They manually fixed everything using the raw data file you provided"}]}]}]}], "created_at": "2025-05-22T22:00:49.365686"}