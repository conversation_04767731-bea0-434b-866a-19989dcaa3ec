{"date": "2024-07-03", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1720024257.969799", "text": "Great! Thank you so much for the update", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ljy4K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Great! Thank you so much for the update"}]}]}]}, {"ts": "1720024228.077039", "text": "<@U04DS2MBWP4> status on perf rating changes and bug fixes reported by <@U04DKEFP1K8> for cain watters. Issues are fixed and <PERSON><PERSON><PERSON><PERSON> will be dry running during his day. Changes/fixes will be pushed to customer ENVs tomorrow.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "slEQ7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " status on perf rating changes and bug fixes reported by "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " for cain watters. Issues are fixed and <PERSON><PERSON><PERSON><PERSON> will be dry running during his day. Changes/fixes will be pushed to customer ENVs tomorrow."}]}]}]}, {"ts": "1720024008.016749", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> what is the status of testing equity for Nauto?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1720024008.016749", "reply_count": 17, "blocks": [{"type": "rich_text", "block_id": "zHnZm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " what is the status of testing equity for Nauto?"}]}]}]}], "created_at": "2025-05-22T21:35:34.622441"}