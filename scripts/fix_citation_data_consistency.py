#!/usr/bin/env python
"""
Fix citation data consistency by ensuring all vector IDs in Qdrant have corresponding EmbeddingMetadata records.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from apps.documents.models import EmbeddingMetadata, DocumentChunk, RawDocument
from apps.accounts.models import Tenant
from qdrant_client import QdrantClient
from django.conf import settings
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_citation_data_consistency():
    """Fix data consistency between Qdrant and EmbeddingMetadata."""
    print("🔧 Fixing Citation Data Consistency")
    print("=" * 60)

    # Get stride tenant
    tenant = Tenant.objects.get(slug="stride")

    # Initialize Qdrant client
    client = QdrantClient(host=settings.QDRANT_HOST, port=settings.QDRANT_PORT)
    collection_name = f"tenant_{tenant.slug}_default"

    # Get all vector IDs from Qdrant
    print("📊 Getting vector IDs from Qdrant...")
    try:
        # Scroll through all points to get their IDs
        qdrant_vector_ids = set()
        offset = None
        batch_size = 100

        while True:
            result = client.scroll(
                collection_name=collection_name,
                limit=batch_size,
                offset=offset,
                with_payload=False,
                with_vectors=False
            )

            if not result[0]:  # No more points
                break

            for point in result[0]:
                qdrant_vector_ids.add(str(point.id))

            offset = result[1]  # Next offset
            if offset is None:
                break

        print(f"✅ Found {len(qdrant_vector_ids)} vector IDs in Qdrant")

    except Exception as e:
        print(f"❌ Error getting vector IDs from Qdrant: {e}")
        return False

    # Get all vector IDs from EmbeddingMetadata
    print("📊 Getting vector IDs from EmbeddingMetadata...")
    embedding_vector_ids = set(
        EmbeddingMetadata.objects.values_list('vector_id', flat=True)
    )
    print(f"✅ Found {len(embedding_vector_ids)} vector IDs in EmbeddingMetadata")

    # Find missing vector IDs
    missing_in_metadata = qdrant_vector_ids - embedding_vector_ids
    missing_in_qdrant = embedding_vector_ids - qdrant_vector_ids

    print(f"\n📊 Data Consistency Analysis:")
    print(f"   Vector IDs in Qdrant: {len(qdrant_vector_ids)}")
    print(f"   Vector IDs in EmbeddingMetadata: {len(embedding_vector_ids)}")
    print(f"   Missing in EmbeddingMetadata: {len(missing_in_metadata)}")
    print(f"   Missing in Qdrant: {len(missing_in_qdrant)}")

    if missing_in_metadata:
        print(f"\n⚠️  Found {len(missing_in_metadata)} vector IDs in Qdrant without EmbeddingMetadata")
        print("   Sample missing IDs:")
        for i, vector_id in enumerate(list(missing_in_metadata)[:5]):
            print(f"     {i+1}. {vector_id}")

        # These are orphaned vectors in Qdrant - we should remove them
        print(f"\n🧹 Removing {len(missing_in_metadata)} orphaned vectors from Qdrant...")
        try:
            # Remove orphaned vectors in batches
            batch_size = 100
            missing_list = list(missing_in_metadata)

            for i in range(0, len(missing_list), batch_size):
                batch = missing_list[i:i + batch_size]
                client.delete(
                    collection_name=collection_name,
                    points_selector=batch
                )
                print(f"   Removed batch {i//batch_size + 1}/{(len(missing_list) + batch_size - 1)//batch_size}")

            print(f"✅ Successfully removed {len(missing_in_metadata)} orphaned vectors")

        except Exception as e:
            print(f"❌ Error removing orphaned vectors: {e}")
            return False

    if missing_in_qdrant:
        print(f"\n⚠️  Found {len(missing_in_qdrant)} EmbeddingMetadata records without vectors in Qdrant")
        print("   Sample missing vector IDs:")
        for i, vector_id in enumerate(list(missing_in_qdrant)[:5]):
            print(f"     {i+1}. {vector_id}")

        # These are metadata records without vectors - mark them as not synced
        print(f"\n🔧 Marking {len(missing_in_qdrant)} metadata records as not synced...")
        try:
            EmbeddingMetadata.objects.filter(
                vector_id__in=missing_in_qdrant
            ).update(is_synced=False)
            print(f"✅ Successfully marked {len(missing_in_qdrant)} records as not synced")

        except Exception as e:
            print(f"❌ Error updating metadata records: {e}")
            return False

    # Verify the fix
    print(f"\n🔍 Verifying the fix...")

    # Test a few problematic vector IDs from earlier
    test_vector_ids = [
        "834fa0ab-b849-4a09-9f15-52c017ea1597",
        "71fb5251-1858-433e-8b12-5a8d92514761",
        "b135bf21-553e-4e4b-a338-916baf394b4b"
    ]

    for vector_id in test_vector_ids:
        chunk = EmbeddingMetadata.get_chunk_by_vector_id(vector_id)
        if chunk:
            print(f"   ✅ {vector_id} -> Chunk {chunk.id}")
        else:
            print(f"   ❌ {vector_id} -> Not found")

    print(f"\n✅ Citation data consistency fix completed!")
    return True

if __name__ == "__main__":
    success = fix_citation_data_consistency()
    if success:
        print("\n🎉 Data consistency has been restored!")
        print("   Citations should now work correctly in search results.")
    else:
        print("\n❌ Failed to fix data consistency.")
        print("   Please check the logs for more details.")
