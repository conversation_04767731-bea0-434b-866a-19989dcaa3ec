{"date": "2025-02-11", "channel_id": "C065QSSNH8A", "message_count": 14, "messages": [{"ts": "1739298383.273299", "text": "I am avaiable", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/dwfp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am avaiable"}]}]}]}, {"ts": "1739298364.740229", "text": "I had a call with curana and they have a request I want to run by you (and <PERSON><PERSON><PERSON>)", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "p3+by", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I had a call with curana and they have a request I want to run by you (and <PERSON><PERSON><PERSON>)"}]}]}]}, {"ts": "1739298353.263239", "text": "<@U0690EB5JE5> are you still around?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "deHa/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " are you still around?"}]}]}]}, {"ts": "1739297152.462759", "text": "<@U07EJ2LP44S> I cannot reproduce this issue locally. Please ask user to delete cookies and try again.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.438369", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "AizKw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I cannot reproduce this issue locally. Please ask user to delete cookies and try again."}]}]}]}, {"ts": "**********.798149", "text": "This is in my local with <PERSON>'s login when switched to her Employee Account view. And regarding click issue let me check tomorrow and get back.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "d/Klw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is in my local with <PERSON>'s login when switched to her Employee Account view. And regarding click issue let me check tomorrow and get back."}]}]}]}, {"ts": "**********.126179", "text": "<@U07EJ2LP44S> Super admin can switch between personal view and admin view.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "files": [{"id": "F08CQV1TCSZ", "created": **********, "timestamp": **********, "name": "Screenshot 2025-02-11 at 11.31.51 PM.png", "title": "Screenshot 2025-02-11 at 11.31.51 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 168508, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08CQV1TCSZ/screenshot_2025-02-11_at_11.31.51___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08CQV1TCSZ/download/screenshot_2025-02-11_at_11.31.51___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 180, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 239, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 359, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 399, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 479, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 511, "original_w": 1917, "original_h": 956, "thumb_tiny": "AwAXADDRIyP/AK1DEgDFKBgcUhoAXP1pRzSdKWgApuOnFOpKACg470UjUAO60UDoKKACiiigD//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08CQV1TCSZ/screenshot_2025-02-11_at_11.31.51___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08CQV1TCSZ-9c601617ab", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "kRTeT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Super admin can switch between personal view and admin view."}]}]}]}, {"ts": "1739296602.076429", "text": "<@U0690EB5JE5> also check the comment re: total rewards in the tithely channel", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739296602.076429", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "ZKYSo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " also check the comment re: total rewards in the tithely channel"}]}]}]}, {"ts": "1739296532.147379", "text": "<@U0690EB5JE5> From Tithely, I don't know the answer to this:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739296532.147379", "reply_count": 12, "files": [{"id": "F08CM410DML", "created": 1739296530, "timestamp": 1739296530, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 58381, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08CM410DML/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08CM410DML/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_360.png", "thumb_360_w": 360, "thumb_360_h": 35, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_480.png", "thumb_480_w": 480, "thumb_480_h": 47, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_720.png", "thumb_720_w": 720, "thumb_720_h": 70, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_800.png", "thumb_800_w": 800, "thumb_800_h": 78, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_960.png", "thumb_960_w": 960, "thumb_960_h": 93, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 99, "original_w": 1608, "original_h": 156, "thumb_tiny": "AwAEADDQ2AHjvzR+Jpx60lABz6mlA46mkpw6UAGPrRj60UUAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08CM410DML/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08CM410DML-f4429a63e7", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ygcxk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " From Tithely, I don't know the answer to this:"}]}]}]}, {"ts": "1739295222.824489", "text": "<@U07EJ2LP44S> <https://stridedemo.stridehr.io/> is back online", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ds1B4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://stridedemo.stridehr.io/"}, {"type": "text", "text": " is back online"}]}]}]}, {"ts": "1739280732.846839", "text": "Ok I will renable demo in couple of hours ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QgejN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok I will renable demo in couple of hours "}]}]}]}, {"ts": "1739280633.857759", "text": "I will be creating the training environments for both <PERSON> and <PERSON><PERSON> simultaneously", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739280633.857759", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "bGe8W", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will be creating the training environments for both <PERSON> and <PERSON><PERSON> simultaneously"}]}]}]}, {"ts": "1739280601.304249", "text": "I still need the customer demo one please", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "h25Q+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I still need the customer demo one please"}]}]}]}, {"ts": "1739266875.844119", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> I have disabled all ENVs except these.\n• <http://test.stridehr.io|test.stridehr.io>\n• <http://curana.stridhr.io|curana.stridhr.io>\n• <http://div-energy.stridehr.io|div-energy.stridehr.io>\n• <http://tithely.stridehr.io|tithely.stridehr.io>\n• <http://valgenesis.stridehr.io|valgenesis.stridehr.io>\nPlease let me know if any inactive customers need to login (DGOC/Alayacare). Also please let me know if I can downgrade <http://test.stridehr.io|test.stridehr.io>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1739268649.000000"}, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8tabf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I have disabled all ENVs except these.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://curana.stridhr.io", "text": "curana.stridhr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://div-energy.stridehr.io", "text": "div-energy.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://tithely.stridehr.io", "text": "tithely.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://valgenesis.stridehr.io", "text": "valgenesis.stridehr.io"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nPlease let me know if any inactive customers need to login (DGOC/Alayacare). Also please let me know if I can downgrade "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}]}]}]}, {"ts": "**********.438369", "text": "From tithely, have not tried to reproduce yet. Cannot move past this section in comp builder.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.438369", "reply_count": 5, "files": [{"id": "F08CN1J4L2E", "file_access": "access_denied", "created": 0, "timestamp": 0, "user": "U08QENR4TNY", "filetype": "webm"}], "blocks": [{"type": "rich_text", "block_id": "B1q13", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "From tithely, have not tried to reproduce yet. Cannot move past this section in comp builder."}]}]}]}], "created_at": "2025-05-22T21:35:34.700411"}