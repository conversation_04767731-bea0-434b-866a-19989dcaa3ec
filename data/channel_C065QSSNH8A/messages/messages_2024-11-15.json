{"date": "2024-11-15", "channel_id": "C065QSSNH8A", "message_count": 11, "messages": [{"ts": "**********.659639", "text": "<@U04DKEFP1K8> How do we add a user to SDF test account? Is that something you can show <PERSON> and <PERSON>?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.659639", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "e2gHI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " How do we add a user to SDF test account? Is that something you can show <PERSON> and <PERSON>?"}]}]}]}, {"ts": "**********.573579", "text": "<@U0690EB5JE5> pay bands missing for international employees and other data issues in paybands for Tithely\n<https://compiify.atlassian.net/browse/COM-3990>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.573579", "reply_count": 6, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14073::6700efa641d34695a2ab831fc8dbc29b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3990?atlOrigin=eyJpIjoiODdlNTQyNGY2NDY5NDkzNTk0OWNiYmM0MjM4OWNlMmUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3990 Missing Pay Bands for International Employees>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14073::0111737e5af1435bbeb2293da294cc51", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14073\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3990\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3990", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "9MeMj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " pay bands missing for international employees and other data issues in paybands for Tithely\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3990"}]}]}]}, {"ts": "1731691675.710189", "text": "<@U0690EB5JE5> <@U07EJ2LP44S> do we automatically calculate the hourly rate for hourly employees in the org view? Or it has to be an upload?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1731691675.710189", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "vHXcF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " do we automatically calculate the hourly rate for hourly employees in the org view? Or it has to be an upload?"}]}]}]}, {"ts": "1731690088.634169", "text": "Promotion/Payband Bug for DGK: <https://compiify.atlassian.net/browse/COM-3989>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14072::a4e630e5427e4b4b999a2d6d8782eeea", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3989?atlOrigin=eyJpIjoiZWYyYmMwYTI2N2E4NGQ0NWFhZGRhZGM2ZTQxZTkyNmYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3989 Issue: Incorrect Job Title Displayed During Promotion Process>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14072::abecfba172884dd2b9e0cb270c777418", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14072\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3989\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3989", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "7YVmk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Promotion/Payband Bug for DGK: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3989"}]}]}]}, {"ts": "1731683881.926759", "text": "This is an enhancement request, but I've had multiple customers mention it. <https://compiify.atlassian.net/browse/COM-3987>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14070::8ad8869f2c9941bbb58bf8584bde33aa", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3987?atlOrigin=eyJpIjoiNTI2Y2VmODk1NzJhNDg0YzgzNWZjMWQ2MTg4YjA2NjUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3987 Retain Sorting and Filtering After Data Changes in Org View>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14070::2abf22cdea654dec9976ec4b2b425ea1", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14070\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3987\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3987", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "mn44Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is an enhancement request, but I've had multiple customers mention it. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3987"}]}]}]}, {"ts": "1731637052.256819", "text": "clarity is amazing, it tracks every user activity.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1731581531.743419", "subtype": "thread_broadcast", "reactions": [{"name": "ok_hand", "users": ["U04DKEFP1K8"], "count": 1}, {"name": "tada", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "IMYsP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "clarity is amazing, it tracks every user activity."}]}]}]}, {"ts": "1731633089.869149", "text": ":tada:Just awesome stuff by <@U07EJ2LP44S> today to complete 2 hour training for degenkolb's 20 managers, probably the biggest and the longest session by a stride team member for any customer!:tada:", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1731633089.869149", "reply_count": 3, "reactions": [{"name": "tada", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}, {"name": "partying_face", "users": ["U07M6QKHUC9"], "count": 1}, {"name": "fire", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Q+XcI", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "tada", "unicode": "1f389"}, {"type": "text", "text": "Just awesome stuff by "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " today to complete 2 hour training for degenkolb's 20 managers, probably the biggest and the longest session by a stride team member for any customer!"}, {"type": "emoji", "name": "tada", "unicode": "1f389"}]}]}]}, {"ts": "1731626070.154859", "text": "<@U0690EB5JE5> Degenkolb has decided they do want to use a budget for their cycle, and set it at 6%. However now I can't override because I did edit/save in the allocations page to try and get the budget to stick (which didn't work btw). Can you somehow reset it so we can put in 6% as the budget for their cycle (both merit and promo together)?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731626070.154859", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "gX36i", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON><PERSON><PERSON> has decided they do want to use a budget for their cycle, and set it at 6%. However now I can't override because I did edit/save in the allocations page to try and get the budget to stick (which didn't work btw). Can you somehow reset it so we can put in 6% as the budget for their cycle (both merit and promo together)?"}]}]}]}, {"ts": "1731625990.680709", "text": "<@U0690EB5JE5> <PERSON><PERSON><PERSON><PERSON><PERSON>'s team is very tied to the full payband amount displaying in the merit view. EG they would not want to see 96k and 150k displayed as the low and high points, if the actual payband is 96400 to 150900. Is there any way to show the full payband (to the decimal point) instead of the rounded amount?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731625990.680709", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "KzMpb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON><PERSON><PERSON>'s team is very tied to the full payband amount displaying in the merit view. EG they would not want to see 96k and 150k displayed as the low and high points, if the actual payband is 96400 to 150900. Is there any way to show the full payband (to the decimal point) instead of the rounded amount?"}]}]}]}, {"ts": "1731613327.591449", "text": "<@U07M6QKHUC9> Super Admin roles were not assigned to <PERSON> or <PERSON><PERSON> so they wouldn't have been able to see anything. I added them, and I had already done that for <PERSON> so their people team should be good.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731613327.591449", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "j1N1t", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Super Admin roles were not assigned to <PERSON> or <PERSON><PERSON> so they wouldn't have been able to see anything. I added them, and I had already done that for <PERSON> so their people team should be good."}]}]}]}, {"ts": "1731612694.032449", "text": "<@U07M6QKHUC9> We also need to update Tithelys payband mapping to include location, I believe <@U0690EB5JE5> has it just mapping on title right now.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731612694.032449", "reply_count": 2, "edited": {"user": "U07EJ2LP44S", "ts": "1731612704.000000"}, "blocks": [{"type": "rich_text", "block_id": "QF4S7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " We also need to update Tithelys payband mapping to include location, I believe "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " has it just mapping on title right now."}]}]}]}], "created_at": "2025-05-22T21:35:34.689314"}