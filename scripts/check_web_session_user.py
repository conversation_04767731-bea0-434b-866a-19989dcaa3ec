#!/usr/bin/env python3
"""
Check what user the web session is using.
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.accounts.models import User, Tenant

def check_all_users():
    """Check all users and their tenants."""
    print("👥 Checking All Users and Their Tenants")
    print("=" * 60)
    
    users = User.objects.all()
    print(f"📊 Total users: {users.count()}")
    
    for user in users:
        print(f"\n👤 User: {user.username} ({user.email})")
        print(f"   ID: {user.id}")
        print(f"   Active: {user.is_active}")
        print(f"   Staff: {user.is_staff}")
        print(f"   Superuser: {user.is_superuser}")
        
        if hasattr(user, 'profile') and user.profile:
            profile = user.profile
            print(f"   Profile tenant: {profile.tenant.slug}")
            print(f"   Profile tenant ID: {profile.tenant.id}")
        else:
            print("   ❌ No profile or profile has no tenant")
    
    # Check which user might be used for 'testuser' login
    print(f"\n🔍 Checking for 'testuser' username...")
    testuser = User.objects.filter(username='testuser').first()
    if testuser:
        print(f"✅ Found testuser: {testuser.email}")
        if hasattr(testuser, 'profile') and testuser.profile:
            print(f"   Testuser tenant: {testuser.profile.tenant.slug}")
        else:
            print("   ❌ Testuser has no profile")
    else:
        print("❌ No testuser found")
    
    # Check for any user with 'default' tenant
    print(f"\n🔍 Checking users with 'default' tenant...")
    default_tenant = Tenant.objects.filter(slug='default').first()
    if default_tenant:
        default_users = User.objects.filter(profile__tenant=default_tenant)
        print(f"📊 Users with 'default' tenant: {default_users.count()}")
        for user in default_users:
            print(f"   - {user.username} ({user.email})")
    else:
        print("❌ No 'default' tenant found")

def fix_testuser_tenant():
    """Fix testuser tenant if it exists."""
    print(f"\n🔧 Fixing testuser tenant...")
    
    testuser = User.objects.filter(username='testuser').first()
    stride_tenant = Tenant.objects.filter(slug='stride').first()
    
    if not testuser:
        print("❌ No testuser found")
        return
    
    if not stride_tenant:
        print("❌ No stride tenant found")
        return
    
    if hasattr(testuser, 'profile') and testuser.profile:
        profile = testuser.profile
        if profile.tenant.slug == 'stride':
            print("✅ Testuser already has stride tenant")
        else:
            print(f"🔧 Changing testuser tenant from {profile.tenant.slug} to stride")
            profile.tenant = stride_tenant
            profile.save()
            print("✅ Testuser tenant updated to stride")
    else:
        print("🔧 Creating profile for testuser with stride tenant")
        from apps.accounts.models import UserProfile
        UserProfile.objects.create(user=testuser, tenant=stride_tenant)
        print("✅ Testuser profile created with stride tenant")

if __name__ == "__main__":
    check_all_users()
    fix_testuser_tenant()
