{"date": "2025-02-28", "channel_id": "C065QSSNH8A", "message_count": 10, "messages": [{"ts": "1740763822.801109", "text": "<@U0690EB5JE5> Can we create a custom report for Diversified energy based on the attached? We would need one row for <PERSON><PERSON>'s direct reports, and one row for each M8. Calculations are in the sheet.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "reply_count": 39, "files": [{"id": "F08G8G816BA", "created": 1740763821, "timestamp": 1740763821, "name": "Summary Report Request .csv", "title": "Summary Report Request .csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1632, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08G8G816BA/summary_report_request_.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08G8G816BA/download/summary_report_request_.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08G8G816BA/summary_report_request_.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08G8G816BA-e7cb8bb982", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08G8G816BA/summary_report_request_.csv/edit", "preview": ",,,,,,,,,,,,\r\n,No. EE,Salaries $,Bonus Eligibility $,Bonus El % of $,Bonus Paid,Bonus/EE,% Eligibility,,,,,\r\n<PERSON><PERSON>ons Directs,4,\" $2,024,999.86 \",\" $3,143,749.77 \",155.2%,\" $2,573,725.62 \",\" $643,431.40 \",81.9%,,,,,\r\nRigg,3,\" $625,977.30 \",\" $160,613.47 \",25.7%,\" $160,613.47 \",\" $53,537.82 \",100.0%,,,,,\r\nRidgeway,22,\" $3,317,626.00 \",\" $819,644.83 \",24.7%,\" $819,644.83 \",\" $37,256.58 \",100.0%,,,,,\r\nNLE,8,\" $1,215,461.10 \",\" $176,051.37 \",14.5%,\" $176,051.37 \",\" $22,006.42 \",100.0%,,,,,\r\n<PERSON>,58,\" $6,516,384.68 \",\" $1,181,382.23 \",18.1%,\" $1,195,085.95 \",\" $20,604.93 \",101.2%,,<PERSON><PERSON><PERSON>s ,,,6\r\n<PERSON>,84,\" $10,426,770.90 \",\" $1,713,905.83 \",16.4%,\" $1,688,247.65 \",\" $20,098.19 \",98.5%,,M8 Teams,,,\r\nGray,88,\" $8,749,046.54 \",\" $1,674,646.02 \",19.1%,\" $1,631,478.72 \",\" $18,539.53 \",97.4%,,,,,\r\nBentley,200,\" $21,820,980.74 \",\" $3,616,337.30 \",16.6%,\" $3,581,678.25 \",\" $17,908.39 \",99.0%,,,,,\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\">No. EE</div><div class=\"cm-col\">Salaries $</div><div class=\"cm-col\">Bonus Eligibility $</div><div class=\"cm-col\">Bonus El % of $</div><div class=\"cm-col\">Bonus Paid</div><div class=\"cm-col\">Bonus/EE</div><div class=\"cm-col\">% Eligibility</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Hutsons Directs</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\"> $2,024,999.86 </div><div class=\"cm-col\"> $3,143,749.77 </div><div class=\"cm-col\">155.2%</div><div class=\"cm-col\"> $2,573,725.62 </div><div class=\"cm-col\"> $643,431.40 </div><div class=\"cm-col\">81.9%</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Rigg</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\"> $625,977.30 </div><div class=\"cm-col\"> $160,613.47 </div><div class=\"cm-col\">25.7%</div><div class=\"cm-col\"> $160,613.47 </div><div class=\"cm-col\"> $53,537.82 </div><div class=\"cm-col\">100.0%</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Ridgeway</div><div class=\"cm-col cm-num\">22</div><div class=\"cm-col\"> $3,317,626.00 </div><div class=\"cm-col\"> $819,644.83 </div><div class=\"cm-col\">24.7%</div><div class=\"cm-col\"> $819,644.83 </div><div class=\"cm-col\"> $37,256.58 </div><div class=\"cm-col\">100.0%</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">NLE</div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\"> $1,215,461.10 </div><div class=\"cm-col\"> $176,051.37 </div><div class=\"cm-col\">14.5%</div><div class=\"cm-col\"> $176,051.37 </div><div class=\"cm-col\"> $22,006.42 </div><div class=\"cm-col\">100.0%</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Sullivan</div><div class=\"cm-col cm-num\">58</div><div class=\"cm-col\"> $6,516,384.68 </div><div class=\"cm-col\"> $1,181,382.23 </div><div class=\"cm-col\">18.1%</div><div class=\"cm-col\"> $1,195,085.95 </div><div class=\"cm-col\"> $20,604.93 </div><div class=\"cm-col\">101.2%</div><div class=\"cm-col\"></div><div class=\"cm-col\">Hutsons Directs </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">6</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Myers</div><div class=\"cm-col cm-num\">84</div><div class=\"cm-col\"> $10,426,770.90 </div><div class=\"cm-col\"> $1,713,905.83 </div><div class=\"cm-col\">16.4%</div><div class=\"cm-col\"> $1,688,247.65 </div><div class=\"cm-col\"> $20,098.19 </div><div class=\"cm-col\">98.5%</div><div class=\"cm-col\"></div><div class=\"cm-col\">M8 Teams</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Gray</div><div class=\"cm-col cm-num\">88</div><div class=\"cm-col\"> $8,749,046.54 </div><div class=\"cm-col\"> $1,674,646.02 </div><div class=\"cm-col\">19.1%</div><div class=\"cm-col\"> $1,631,478.72 </div><div class=\"cm-col\"> $18,539.53 </div><div class=\"cm-col\">97.4%</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Bentley</div><div class=\"cm-col cm-num\">200</div><div class=\"cm-col\"> $21,820,980.74 </div><div class=\"cm-col\"> $3,616,337.30 </div><div class=\"cm-col\">16.6%</div><div class=\"cm-col\"> $3,581,678.25 </div><div class=\"cm-col\"> $17,908.39 </div><div class=\"cm-col\">99.0%</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 28, "lines_more": 18, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "GVbTP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can we create a custom report for Diversified energy based on the attached? We would need one row for <PERSON><PERSON>'s direct reports, and one row for each M8. Calculations are in the sheet."}]}]}]}, {"ts": "1740761151.670959", "text": "<@U07EJ2LP44S> Done.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.210139", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "Ts22O", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Done."}]}]}]}, {"ts": "1740756047.044429", "text": "Ok will add them in an hour away right now.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "05FLQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok will add them in an hour away right"}, {"type": "text", "text": " "}, {"type": "text", "text": "now."}]}]}]}, {"ts": "**********.210139", "text": "<@U0690EB5JE5> One more update for Curana (well at least for now). Employee additions for the MIP account.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.210139", "reply_count": 2, "files": [{"id": "F08G7LBRZKJ", "created": **********, "timestamp": **********, "name": "Curana Health_EmployeeDataTemplate_MIP additions.xlsx", "title": "Curana Health_EmployeeDataTemplate_MIP additions.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 24995, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08G7LBRZKJ/curana_health_employeedatatemplate_mip_additions.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08G7LBRZKJ/download/curana_health_employeedatatemplate_mip_additions.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G7LBRZKJ-6562a6b853/curana_health_employeedatatemplate_mip_additions_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G7LBRZKJ-6562a6b853/curana_health_employeedatatemplate_mip_additions_thumb_pdf.png", "thumb_pdf_w": 935, "thumb_pdf_h": 1210, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08G7LBRZKJ/curana_health_employeedatatemplate_mip_additions.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08G7LBRZKJ-c1d8539c6d", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "WkUM/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " One more update for Curana (well at least for now). Employee additions for the MIP account."}]}]}]}, {"ts": "**********.675179", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> Can I stop all the non-prod ENVs? I assume training is done (may be Tithely would still not done?)", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.675179", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "4eHOU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Can I stop all the non-prod ENVs? I assume training is done (may be Tith<PERSON> would still not done?)"}]}]}]}, {"ts": "**********.628499", "text": "Will take a look into all the reported stuff above.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jfFN6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take a look into all the reported stuff above."}]}]}]}, {"ts": "1740683606.282089", "text": "<@U0690EB5JE5> We need to pivot for the matrix performance/compa ratio in Tithely. B/c they are looking at it from range penetration and we from compa ratio the numbers are wrong.\n\nCan we change the ranges in the matrix to\n\n<.80\n.80-1\n1.1-1.20\ngreater than 1.2\n\nThey would like to keep the same ratings (which were set to US but still need to be applied to all the international matrixes - just the same numbers). We just need to change the compa ratio categories", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740683606.282089", "reply_count": 2, "edited": {"user": "U07EJ2LP44S", "ts": "1740684348.000000"}, "blocks": [{"type": "rich_text", "block_id": "bUnZ0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We need to pivot for the matrix performance/compa ratio in Tithely. B/c they are looking at it from range penetration and we from compa ratio the numbers are wrong.\n\nCan we change the ranges in the matrix to\n\n<.80\n.80-1\n1.1-1.20\ngreater than 1.2\n\nThey would like to keep the same ratings (which were set to US but still need to be applied to all the international matrixes - just the same numbers). We just need to change the compa ratio categories"}]}]}]}, {"ts": "1740682896.228889", "text": "<@U0690EB5JE5> for Tithely, it looks like anyone who is hourly has no option for a merit increase. These people should behave the same as the salary people. But for hourly (mostly in Customer Success) they merit box is grayed out.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740682896.228889", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "ozxVg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " for Tithely, it looks like anyone who is hourly has no option for a merit increase. These people should behave the same as the salary people. But for hourly (mostly in Customer Success) they merit box is grayed out."}]}]}]}, {"ts": "1740681677.031909", "text": "<@U0690EB5JE5> Again for total rewards, can we change the 401k language to 'can we change the “retirement 401k” line item to “Retirement Match”. The international folks don't have a 401k", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740681677.031909", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "6d3kS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Again for total rewards, can we change the 401k language to 'can we change the “retirement 401k” line item to “Retirement Match”. The international folks don't have a 401k"}]}]}]}, {"ts": "1740681542.134039", "text": "<@U0690EB5JE5> For the total rewards, the download is not showing the entire salary history. It's only showing what's in the main box before scrolling. This needs to be fixed so it shows the entire salary history, including the data that only shows after the scroll.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740681542.134039", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "OFuIE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " For the total rewards, the download is not showing the entire salary history. It's only showing what's in the main box before scrolling. This needs to be fixed so it shows the entire salary history, including the data that only shows after the scroll."}]}]}]}], "created_at": "2025-05-22T21:35:34.708639"}