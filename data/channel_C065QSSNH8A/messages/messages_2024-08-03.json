{"date": "2024-08-03", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1722638955.314539", "text": "I hope it’s for a fun reason! ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722638955.314539", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "ikxnz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I hope it’s for a fun reason! "}]}]}]}, {"ts": "1722634829.913499", "text": "<!here> FYI, I will OOO on Monday", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8", "U0690EB5JE5"], "count": 2}, {"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "QYuJ3", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " FYI, I will OOO on Monday"}]}]}]}, {"ts": "1722625811.060929", "text": "<@U04DS2MBWP4> <@U07EJ2LP44S> Following updates can be sent to Vercara, their environment is now upgraded with latest changes\n\n        1.\tAdministrators can now change an employee’s compensation type to OTE directly through the UI. Just select the employee name, click Edit, and change the Compensation Type to OTE.\n\t2.\tFor OTE employees, you can now adjust both Base Pay and Variable Pay via the UI. Additionally, you can modify the Pay mix percentages directly from the UI.\n\t3.\tYou can now input the Target Bonus % through the UI as well.\n\t4.\tThe CSV templates now include an employee name column, which is read-only.\n\t5.\tColumns in the Compensation Data template are now optional. You don’t need to fill in columns with dummy data if you don’t have any input for them.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722625811.060929", "reply_count": 3, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "wOQP8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Following updates can be sent to Vercara, their environment is now upgraded with latest changes\n\n        1.\tAdministrators can now change an employee’s compensation type to OTE directly through the UI. Just select the employee name, click Edit, and change the Compensation Type to OTE.\n\t2.\tFor OTE employees, you can now adjust both Base Pay and Variable Pay via the UI. Additionally, you can modify the Pay mix percentages directly from the UI.\n\t3.\tYou can now input the Target Bonus % through the UI as well.\n\t4.\tThe CSV templates now include an employee name column, which is read-only.\n\t5.\tColumns in the Compensation Data template are now optional. You don’t need to fill in columns with dummy data if you don’t have any input for them."}]}]}]}, {"ts": "1722625200.407449", "text": "<!here> here is the feedback that <PERSON> (VP of operations at <http://Tithly.io|Tithly.io>) gave during the demo today\n<https://app.fireflies.ai/soundbites/5N28VaD0c5C>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722625200.407449", "reply_count": 13, "attachments": [{"from_url": "https://app.fireflies.ai/soundbites/5N28VaD0c5C", "image_url": "https://files.fireflies.ai/app/img/preview.png", "image_width": 1280, "image_height": 720, "image_bytes": 188965, "service_icon": "https://app.fireflies.ai/favicon.ico", "id": 1, "original_url": "https://app.fireflies.ai/soundbites/5N28VaD0c5C", "fallback": "Fireflies.ai - Free Meeting Recorder", "text": "Record, transcribe, search and collaborate across your meetings. Fireflies takes notes for your meetings and turn words into actions.", "title": "Fireflies.ai - Free Meeting Recorder", "title_link": "https://app.fireflies.ai/soundbites/5N28VaD0c5C", "service_name": "app.fireflies.ai"}], "blocks": [{"type": "rich_text", "block_id": "LUp0r", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " here is the feedback that <PERSON> (VP of operations at "}, {"type": "link", "url": "http://Tithly.io", "text": "Tithly.io"}, {"type": "text", "text": ") gave during the demo today\n"}, {"type": "link", "url": "https://app.fireflies.ai/soundbites/5N28VaD0c5C"}]}]}]}], "created_at": "2025-05-22T21:35:34.633325"}