{"date": "2024-03-20", "channel_id": "C065QSSNH8A", "message_count": 10, "messages": [{"ts": "1710955606.493039", "text": "<@U065H3M6WJV> here is an early preview of cookiebot implementation <https://3ae4-176-106-202-180.ngrok-free.app/>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710955606.493039", "reply_count": 3, "reactions": [{"name": "muscle", "users": ["U04DS2MBWP4"], "count": 1}], "attachments": [{"from_url": "https://3ae4-176-106-202-180.ngrok-free.app/", "id": 1, "original_url": "https://3ae4-176-106-202-180.ngrok-free.app/", "fallback": "Compiify", "text": "Compiify software", "title": "Compiify", "title_link": "https://3ae4-176-106-202-180.ngrok-free.app/", "service_name": "3ae4-176-106-202-180.ngrok-free.app"}], "blocks": [{"type": "rich_text", "block_id": "4s+J1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " here is an early preview of cookiebot implementation "}, {"type": "link", "url": "https://3ae4-176-106-202-180.ngrok-free.app/"}]}]}]}, {"ts": "1710952977.030339", "text": "<@U068MM2H2QG> I will send an update when the environment is active again.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710952977.030339", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "HMyKe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U068MM2H2QG"}, {"type": "text", "text": " I will send an update when the environment is active again."}]}]}]}, {"ts": "1710952931.485959", "text": "<@U04DS2MBWP4> For your next call with <PERSON>, in addition to getting feedback - if they are planning to continue with another cycle / Total Rewards / etc. can we ask for their Zenefits API key as well, so <@U0690EB5JE5> can test that integration?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "guOAX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " For your next call with <PERSON>, in addition to getting feedback - if they are planning to continue with another cycle / Total Rewards / etc. can we ask for their Zenefits API key as well, so "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can test that integration?"}]}]}]}, {"ts": "1710952695.838869", "text": "<@U04DKEFP1K8> I think <PERSON>’s next demo is at noon our time", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZTORR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I think "}, {"type": "text", "text": "Wesley’s"}, {"type": "text", "text": " next demo is at noon our time"}]}]}]}, {"ts": "1710952632.212919", "text": "<@U04DS2MBWP4> <@U065H3M6WJV> when is the next demo planned", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "M+GQN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " when is the next demo planned"}]}]}]}, {"ts": "1710952615.449469", "text": "<!here> staging environment is undergoing a db update. Do we have any demo in next few mins. env should be back online  soon", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710952615.449469", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "cEXyT", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " staging environment is undergoing a db update. Do we have any demo in next few mins. env should be back online  soon"}]}]}]}, {"ts": "1710891745.141419", "text": "from what I see it should work", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1710869506.597329", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "g/jla", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "from what I see it should work"}]}]}]}, {"ts": "1710881263.924019", "text": "I stepped outside to pick up kids , let me revert back on both queries once i am back", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710881263.924019", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TiW0D", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I stepped outside to pick up kids , let me revert back on both queries once i am back"}]}]}]}, {"ts": "1710881156.293419", "text": "Also, <@U04DKEFP1K8> Any idea why DA's \"Post Merit Employee Roster\" report has fewer employees listed than their Merit view Export file for the full org?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710881156.293419", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "egc2U", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also, "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Any idea why DA's \"Post Merit Employee Roster\" report has fewer employees listed than their Merit view Export file for the full org?"}]}]}]}, {"ts": "1710879621.065839", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> Yesterday we discussed how SDF's customizations for XLM equity are behind a feature flag, but I'm seeing the XLM column names in the export from DA's merit cycle (example <https://drive.google.com/file/d/1YZSWGO1q6fUNySeoGKPOENIaEHsIIb-F/view?usp=drive_link|here>).\n\nWas the column type (dropdown vs numeric field) the only thing currently behind a flag? Is more work needed to wrap the rest of these \"XLM\" label changes behind the same flag?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710879621.065839", "reply_count": 1, "edited": {"user": "U065H3M6WJV", "ts": "1710879625.000000"}, "blocks": [{"type": "rich_text", "block_id": "pYRc7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Yesterday we discussed how SDF's customizations for XLM equity are behind a feature flag, but I'm seeing the XLM column names in the export from DA's merit cycle (example "}, {"type": "link", "url": "https://drive.google.com/file/d/1YZSWGO1q6fUNySeoGKPOENIaEHsIIb-F/view?usp=drive_link", "text": "here"}, {"type": "text", "text": ").\n\nWas the column type (dropdown vs numeric field) the only thing currently behind a flag? Is more work needed to wrap the rest of these \"XLM\" label changes behind the same flag?"}]}]}]}], "created_at": "2025-05-22T21:35:34.608078"}