{"thread_ts": "1738271308.186779", "channel_id": "C065QSSNH8A", "reply_count": 26, "replies": [{"ts": "1738300588.923349", "text": "This is expected behavior.\n\nThe behavior:\n• The value input on the Bonus Planning step in cycle is not functional and does not affect any value/calculations\n• The allocate page values match with those of the merit view. These values are the sum of all the target bonuses after applying modifier.", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1738271308.186779", "files": [{"id": "F08AY1AD75L", "created": 1738300236, "timestamp": 1738300236, "name": "Screenshot 2025-01-31 at 10.39.29 AM.png", "title": "Screenshot 2025-01-31 at 10.39.29 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07MH77PUBV", "user_team": "T04DM97F1UM", "editable": false, "size": 116557, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08AY1AD75L/screenshot_2025-01-31_at_10.39.29___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08AY1AD75L/download/screenshot_2025-01-31_at_10.39.29___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AY1AD75L-2179399474/screenshot_2025-01-31_at_10.39.29___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AY1AD75L-2179399474/screenshot_2025-01-31_at_10.39.29___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AY1AD75L-2179399474/screenshot_2025-01-31_at_10.39.29___am_360.png", "thumb_360_w": 360, "thumb_360_h": 305, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AY1AD75L-2179399474/screenshot_2025-01-31_at_10.39.29___am_480.png", "thumb_480_w": 480, "thumb_480_h": 407, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AY1AD75L-2179399474/screenshot_2025-01-31_at_10.39.29___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AY1AD75L-2179399474/screenshot_2025-01-31_at_10.39.29___am_720.png", "thumb_720_w": 720, "thumb_720_h": 611, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AY1AD75L-2179399474/screenshot_2025-01-31_at_10.39.29___am_800.png", "thumb_800_w": 800, "thumb_800_h": 679, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AY1AD75L-2179399474/screenshot_2025-01-31_at_10.39.29___am_960.png", "thumb_960_w": 960, "thumb_960_h": 814, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AY1AD75L-2179399474/screenshot_2025-01-31_at_10.39.29___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 869, "original_w": 1094, "original_h": 928, "thumb_tiny": "AwAoADDRxuHzDHNAVfSgZ2nJzzSg0AA2rx0pc0h69TSZFADqKQD3zRj3oAB0paQdKUnHYmgBDQM0de350c+woAWik/KloABRQKKAI5WZcbUZ/p2pEkZmAMTKPUkVLRQAmPc0tFFAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AY1AD75L/screenshot_2025-01-31_at_10.39.29___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08AY1AD75L-0a9d902e7f", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F08AQ42R031", "created": 1738300403, "timestamp": 1738300403, "name": "Screenshot 2025-01-31 at 10.42.07 AM.png", "title": "Screenshot 2025-01-31 at 10.42.07 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07MH77PUBV", "user_team": "T04DM97F1UM", "editable": false, "size": 77539, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08AQ42R031/screenshot_2025-01-31_at_10.42.07___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08AQ42R031/download/screenshot_2025-01-31_at_10.42.07___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQ42R031-3fe7cb41be/screenshot_2025-01-31_at_10.42.07___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQ42R031-3fe7cb41be/screenshot_2025-01-31_at_10.42.07___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQ42R031-3fe7cb41be/screenshot_2025-01-31_at_10.42.07___am_360.png", "thumb_360_w": 360, "thumb_360_h": 90, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQ42R031-3fe7cb41be/screenshot_2025-01-31_at_10.42.07___am_480.png", "thumb_480_w": 480, "thumb_480_h": 120, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQ42R031-3fe7cb41be/screenshot_2025-01-31_at_10.42.07___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQ42R031-3fe7cb41be/screenshot_2025-01-31_at_10.42.07___am_720.png", "thumb_720_w": 720, "thumb_720_h": 180, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQ42R031-3fe7cb41be/screenshot_2025-01-31_at_10.42.07___am_800.png", "thumb_800_w": 800, "thumb_800_h": 200, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQ42R031-3fe7cb41be/screenshot_2025-01-31_at_10.42.07___am_960.png", "thumb_960_w": 960, "thumb_960_h": 240, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQ42R031-3fe7cb41be/screenshot_2025-01-31_at_10.42.07___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 256, "original_w": 1800, "original_h": 450, "thumb_tiny": "AwAMADDRwPb8qdRiigAooooAQk9qPqKXFGKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AQ42R031/screenshot_2025-01-31_at_10.42.07___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08AQ42R031-1a0a74093a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F08BTEJM656", "created": 1738300571, "timestamp": 1738300571, "name": "Screenshot 2025-01-31 at 10.44.53 AM.png", "title": "Screenshot 2025-01-31 at 10.44.53 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07MH77PUBV", "user_team": "T04DM97F1UM", "editable": false, "size": 186111, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08BTEJM656/screenshot_2025-01-31_at_10.44.53___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08BTEJM656/download/screenshot_2025-01-31_at_10.44.53___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BTEJM656-34177413aa/screenshot_2025-01-31_at_10.44.53___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BTEJM656-34177413aa/screenshot_2025-01-31_at_10.44.53___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BTEJM656-34177413aa/screenshot_2025-01-31_at_10.44.53___am_360.png", "thumb_360_w": 360, "thumb_360_h": 141, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BTEJM656-34177413aa/screenshot_2025-01-31_at_10.44.53___am_480.png", "thumb_480_w": 480, "thumb_480_h": 188, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BTEJM656-34177413aa/screenshot_2025-01-31_at_10.44.53___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BTEJM656-34177413aa/screenshot_2025-01-31_at_10.44.53___am_720.png", "thumb_720_w": 720, "thumb_720_h": 282, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BTEJM656-34177413aa/screenshot_2025-01-31_at_10.44.53___am_800.png", "thumb_800_w": 800, "thumb_800_h": 313, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BTEJM656-34177413aa/screenshot_2025-01-31_at_10.44.53___am_960.png", "thumb_960_w": 960, "thumb_960_h": 376, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BTEJM656-34177413aa/screenshot_2025-01-31_at_10.44.53___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 401, "original_w": 1896, "original_h": 742, "thumb_tiny": "AwASADDR9Mn9KPz/ACo9KXFABn6/lRn6/lRijFACAk0nzbugxS4pcc0AHpS96T0pe9AAaSlNJQAUvekpaAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08BTEJM656/screenshot_2025-01-31_at_10.44.53___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08BTEJM656-58b315ce7c", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "aiS8U", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is expected behavior.\n\nThe behavior:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The value input on the Bonus Planning step in cycle is not functional and does not affect any value/calculations"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The allocate page values match with those of the merit view. These values are the sum of all the target bonuses after applying modifier."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1738300765.176909", "text": "The context:\nFour of us had a meeting where this was brought up and one of the proposals was to hide this or to make use of it somehow.\nThen we decided to keep it as it is (for some reason maybe:slightly_smiling_face:)", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "m2zi/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The context:\nFour of us had a meeting where this was brought up and one of the proposals was to hide this or to make use of it somehow.\nThen we decided to keep it as it is (for some reason maybe"}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": ")"}]}]}]}, {"ts": "1738300826.422279", "text": "Thank You <@U07MH77PUBV>. I remember that now. The numbers input in the budget section has no influence on the allocation page.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "edited": {"user": "U0690EB5JE5", "ts": "1738300904.000000"}, "blocks": [{"type": "rich_text", "block_id": "k09Wd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank You "}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": ". I remember that now. The numbers input in the budget section has no influence on the allocation page."}]}]}]}, {"ts": "1738331584.883149", "text": "We need to override this somehow then. We can’t put a number that’s not their actual budget into the cycle. Have them manage to it. Can we just put the right amount at the top of the allocation page and it will work?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "naQb/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We need to override this somehow then. We can’t put a number that’s not their actual budget into the cycle. Have them manage to it. Can we just put the right amount at the top of the allocation page and it will work?"}]}]}]}, {"ts": "1738331607.446749", "text": "Can y’all please try and get this fixed, I’ve already maxed out my time working this week", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "RUFi4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can y’all please try and get this fixed, I’ve already maxed out my time working this week"}]}]}]}, {"ts": "1738331642.576129", "text": "One thing we can do is hide it and manage allocations from allocate budget", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "dmHLq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "One thing we can do is hide it and manage allocations from allocate budget"}]}]}]}, {"ts": "1738331657.396309", "text": "Does that work?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "SgJmF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Does that work?"}]}]}]}, {"ts": "1738331689.940729", "text": "Allocate page allows you to edit budgets if you remember top down", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "IBlxU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Allocate page allows you to edit budgets if you remember top down"}]}]}]}, {"ts": "1738332171.089039", "text": "<@U07EJ2LP44S> we can adjust the top but it has to be distributed down. If customer can share the distribution top down, I will take care.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "W8R54", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " we can adjust the top but it has to be distributed down. If customer can share the distribution top down, I will take care."}]}]}]}, {"ts": "1738335407.577069", "text": "It should be based on the salaries. I can’t ask her to go back for this. Our system should be able to compute it. I think basically we just need to take a percent difference between what it’s assigning and what we have and carry that all the way down", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "AWOW2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It should be based on the salaries. I can’t ask her to go back for this. Our system should be able to compute it. I think basically we just need to take a percent difference between what it’s assigning and what we have and carry that all the way down"}]}]}]}, {"ts": "1738335742.756989", "text": "Ok. Will take care of this by Monday.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "gu9Pg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. Will take care of this by Monday."}]}]}]}, {"ts": "1738335911.442459", "text": "Thank you", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738271308.186779", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "VoJ9i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you"}]}]}]}, {"ts": "1738349964.826349", "text": "<@U07EJ2LP44S> Do we have the final numbers? should I just take whatever is there in cycle now?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "eh0f8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Do we have the final numbers? should I just take whatever is there in cycle now?"}]}]}]}, {"ts": "1738350167.654749", "text": "The number in there for bonus right now is the exact dollar and cents that she told me about a week ago and it’s not supposed to have changed. I did ask her an email to confirm, but I think it’s the same.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738271308.186779", "edited": {"user": "U07EJ2LP44S", "ts": "1738350220.000000"}, "blocks": [{"type": "rich_text", "block_id": "KfAms", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The number in there for bonus right now is the exact dollar "}, {"type": "text", "text": "and cents"}, {"type": "text", "text": " that she told me about a week ago and it’s not supposed to have changed. I did ask her an email to confirm, but I think it’s the same."}]}]}]}, {"ts": "1738587857.326209", "text": "<@U07EJ2LP44S> I have an <https://github.com/Compiify/Yellowstone/pull/2086|API> ready to distribute the delta based on the current distribution. I am just waiting for <PERSON>'s confirmation and validate with you.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1738587981.000000"}, "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}, {"name": "tada", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "0l5Fe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have an "}, {"type": "link", "url": "https://github.com/Compiify/Yellowstone/pull/2086", "text": "API"}, {"type": "text", "text": " ready to distribute the delta based on the current distribution. I am just waiting for <PERSON>'s confirmation and validate with you."}]}]}]}, {"ts": "1738587914.536789", "text": "", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "files": [{"id": "F08BDS44UPP", "created": 1738587905, "timestamp": 1738587905, "name": "Screenshot 2025-02-03 at 6.34.52 PM.png", "title": "Screenshot 2025-02-03 at 6.34.52 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 149409, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08BDS44UPP/screenshot_2025-02-03_at_6.34.52___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08BDS44UPP/download/screenshot_2025-02-03_at_6.34.52___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BDS44UPP-78480402fe/screenshot_2025-02-03_at_6.34.52___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BDS44UPP-78480402fe/screenshot_2025-02-03_at_6.34.52___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BDS44UPP-78480402fe/screenshot_2025-02-03_at_6.34.52___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 117, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BDS44UPP-78480402fe/screenshot_2025-02-03_at_6.34.52___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 156, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BDS44UPP-78480402fe/screenshot_2025-02-03_at_6.34.52___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BDS44UPP-78480402fe/screenshot_2025-02-03_at_6.34.52___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 234, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BDS44UPP-78480402fe/screenshot_2025-02-03_at_6.34.52___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 260, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BDS44UPP-78480402fe/screenshot_2025-02-03_at_6.34.52___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 312, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08BDS44UPP-78480402fe/screenshot_2025-02-03_at_6.34.52___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 333, "original_w": 2976, "original_h": 968, "thumb_tiny": "AwAPADDQGOeBS4HoKaWwTwaPMH9007CH0UzzP9k07NIBaO9JmigD/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08BDS44UPP/screenshot_2025-02-03_at_6.34.52___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08BDS44UPP-759d6a66d2", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}]}, {"ts": "1738587954.317449", "text": "*numbers in screenshot is for illustration.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "MxdUe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "*numbers in screenshot is for illustration."}]}]}]}, {"ts": "1738674560.377119", "text": "Can you go ahead and run the API based on the numbers in there now? I’m assuming we can do it again if they change last minute?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "i66fe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can you go ahead and run the API based on the numbers in there now? I’m assuming we can do it again if they change last minute?"}]}]}]}, {"ts": "1738674816.793879", "text": "Ok", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "PfDzz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok"}]}]}]}, {"ts": "1738674851.485889", "text": "Need to deploy the API. Will confirm once the API applied", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "OegHz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Need to deploy the API. Will confirm once the API applied"}]}]}]}, {"ts": "1738683656.973689", "text": "Is this done yet? Would like to update <PERSON>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "wN+zM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is this done yet? Would like to update <PERSON>"}]}]}]}, {"ts": "1738683698.121839", "text": "not yet will update here", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "mMM0P", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "not yet will update here"}]}]}]}, {"ts": "1738683753.556229", "text": "need couple of hours. I just came back home", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "I4BPR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "need couple of hours. I just came back home"}]}]}]}, {"ts": "1738736483.459779", "text": "<@U07EJ2LP44S> This is done. Please check and let me know", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "m1YME", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is done. Please check and let me know"}]}]}]}, {"ts": "1738775570.593279", "text": "Looks fine from my end, but just so I understand how this is originally done. It was taking the total budget amounts based on percent of salary correct? So essentially it was determining the budget originally based on the total eligible amount for all the employees in the cycle. And then from there, we just re-distributed with the same ratio, but the new number.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738271308.186779", "blocks": [{"type": "rich_text", "block_id": "oNIzV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Looks fine from my end, but just so I understand how this is originally done. It was taking the total budget amounts based on percent of salary correct? So essentially it was determining the budget originally based on the total eligible amount for all the employees in the cycle. And then from there, we just re-distributed with the same ratio, but the new number."}]}]}]}, {"ts": "1738775839.128019", "text": "Yes it’s total of target bonus amounts bottom up and new amount is distributed in the proportion or ratio top down.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "r<PERSON><PERSON>s", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes "}, {"type": "text", "text": "it’s"}, {"type": "text", "text": " total of target bonus amounts bottom up and new amount is distributed in the proportion or ratio top down."}]}]}]}], "created_at": "2025-05-22T22:00:49.345136"}