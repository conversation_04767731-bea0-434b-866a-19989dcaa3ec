{"date": "2024-01-09", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1704782917.738409", "text": "<@U065H3M6WJV> nice work on putting together the documentation for Google Login Setup", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "meow_thx", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "zRlwY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " nice work on putting together the documentation for Google Login Setup"}]}]}]}, {"ts": "1704760532.809609", "text": "Yes will set one up. ", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1704760532.809609", "reply_count": 1, "edited": {"user": "U04DS2MBWP4", "ts": "1704760571.000000"}, "blocks": [{"type": "rich_text", "block_id": "itFQG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes will set one up. "}]}]}]}, {"ts": "1704760486.881769", "text": "<@U04DS2MBWP4> Can we get a <mailto:<EMAIL>|<EMAIL>> alias set up? Something that distributes to me, <PERSON><PERSON><PERSON><PERSON>, you...?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "l7mZI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Can we get a "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " alias set up? Something that distributes to me, <PERSON><PERSON><PERSON><PERSON>, you...?"}]}]}]}, {"ts": "**********.510609", "text": "Yeah, I was going to ask if I can get access to <http://cfy.app|cfy.app> to do my own walkthrough for screenshots :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.510609", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "xAq8t", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yeah, I was going to ask if I can get access to cfy.app to do my own walkthrough for screenshots "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "**********.208859", "text": "Just otter. But I can make you the account admin for our second domain if you want to capture the screenshots", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "2mVOZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just otter. But I can make you the account admin for our second domain if you want to capture the screenshots"}]}]}]}, {"ts": "**********.549389", "text": "<@U04DS2MBWP4> From our call last week for Google SSO setup, did you have a full Zoom recording or just the Otter transcript? (there are some screens that weren't captured in the Otter version)", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Fslnl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " From our call last week for Google SSO setup, did you have a full Zoom recording or just the Otter transcript? (there are some screens that weren't captured in the Otter version)"}]}]}]}], "created_at": "2025-05-22T21:35:34.593651"}