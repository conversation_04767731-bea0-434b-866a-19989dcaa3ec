{"date": "2024-10-23", "channel_id": "C065QSSNH8A", "message_count": 11, "messages": [{"ts": "1729697065.887729", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> Please review HRBP changes as per Alayacare's requirements\n<https://www.loom.com/share/be3c7fb7d0404df09da54a637cc8c8f2?sid=be545980-0e14-4db2-96e4-6c0b8fd091d2>\n\nThank You <@U071FN2589Y> for the Loom", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729697065.887729", "reply_count": 5, "edited": {"user": "U0690EB5JE5", "ts": "1729697090.000000"}, "blocks": [{"type": "rich_text", "block_id": "U5XHD", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please review HRBP changes as per Alayacare's requirements\n"}, {"type": "link", "url": "https://www.loom.com/share/be3c7fb7d0404df09da54a637cc8c8f2?sid=be545980-0e14-4db2-96e4-6c0b8fd091d2"}, {"type": "text", "text": "\n\nThank You "}, {"type": "user", "user_id": "U071FN2589Y"}, {"type": "text", "text": " for the Loom"}]}]}]}, {"ts": "1729695285.146359", "text": "I see one issue in SDF-test which is due to missing employee equity data. <@U04DKEFP1K8> was this intended ? how is it expected to work if no data available but equity component is chosen.?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729695285.146359", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1729696561.000000"}, "files": [{"id": "F07TV3Z25LG", "created": 1729695239, "timestamp": 1729695239, "name": "Screenshot 2024-10-23 at 8.23.29 PM.png", "title": "Screenshot 2024-10-23 at 8.23.29 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 77732, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07TV3Z25LG/screenshot_2024-10-23_at_8.23.29___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07TV3Z25LG/download/screenshot_2024-10-23_at_8.23.29___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TV3Z25LG-a1f60de4e0/screenshot_2024-10-23_at_8.23.29___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TV3Z25LG-a1f60de4e0/screenshot_2024-10-23_at_8.23.29___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TV3Z25LG-a1f60de4e0/screenshot_2024-10-23_at_8.23.29___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 170, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TV3Z25LG-a1f60de4e0/screenshot_2024-10-23_at_8.23.29___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 227, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TV3Z25LG-a1f60de4e0/screenshot_2024-10-23_at_8.23.29___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TV3Z25LG-a1f60de4e0/screenshot_2024-10-23_at_8.23.29___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 340, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TV3Z25LG-a1f60de4e0/screenshot_2024-10-23_at_8.23.29___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 378, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TV3Z25LG-a1f60de4e0/screenshot_2024-10-23_at_8.23.29___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 454, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TV3Z25LG-a1f60de4e0/screenshot_2024-10-23_at_8.23.29___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 484, "original_w": 1750, "original_h": 827, "thumb_tiny": "AwAWADDQJAXcxFOHTilHSigAooooAKQiloPSgAooooAKKKKACg9KKKAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07TV3Z25LG/screenshot_2024-10-23_at_8.23.29___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07TV3Z25LG-a1dee182b6", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "hAnvN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I see one issue in SDF-test which is due to missing employee equity data. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " was this intended ? how is it expected to work if no data available but equity component is chosen.?"}]}]}]}, {"ts": "1729655610.005699", "text": "<@U06HN8XDC5A> <@U071FN2589Y> <@U07MH77PUBV>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sBMTt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U071FN2589Y"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07MH77PUBV"}]}]}]}, {"ts": "1729654241.512959", "text": "has renamed the channel from \"1-productleadership\" to \"1-productengineering\"", "user": "U07M6QKHUC9", "type": "message", "subtype": "channel_name"}, {"ts": "1729653986.830359", "text": "<@U07M6QKHUC9> I will be adding engineers to this channel. Do you want to rename it before that?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729653986.830359", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "LwstP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I will be adding engineers to this channel. Do you want to rename it before that?"}]}]}]}, {"ts": "1729638452.122409", "text": "<@U0690EB5JE5> Please let's do what we all have agreed on i.e. engineering to do testing or we will continue to get back in circles.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729611330.718579", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "/zce3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Please let's do what we all have agreed on i.e. engineering to do testing or we will continue to get back in circles."}]}]}]}, {"ts": "1729634823.749089", "text": "<@U0690EB5JE5> Cainwatters team has reported <https://compiify.atlassian.net/browse/COM-3890>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13951::0a74b7022bbb42d2b471cbe3829f14b6", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3890?atlOrigin=eyJpIjoiNWMwMGE0NmM5NmM2NDA4OTk1NGZlZjMyM2MwYzBlNTQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3890 Issue: Proposed Salary Increase Not Acknowledged for Employee Linle…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13951::7f5b94a4f667405eb211b26894545d06", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13951\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3890\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3890", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "O+pnz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Cainwatters team has reported "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3890"}]}]}]}, {"ts": "1729626601.361579", "text": "<@U0690EB5JE5> Looks like after yesterday's production upgrade Pay Bands are completely inaccessible on cainwatters and alayacare environment with authorization error ( jira here <https://compiify.atlassian.net/browse/COM-3889>)\n<@U07EJ2LP44S> did you performed any salary bands updates in these environments since yesterday ?\n\n<@U07EJ2LP44S> Do we need to inform <PERSON><PERSON> of this outage?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1729626601.361579", "reply_count": 25, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13950::ecd9115d53ac4791ac2f58641b8e5f98", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3889?atlOrigin=eyJpIjoiZGY2ODdmNGQ0NzU1NDI1ZmE2NzdhOTE1MGE4MjIwNjQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3889 Pay bands are inaccessible on all production environments>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13950::9caa570cdbe7443897f42bc9453d64ab", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13950\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3889\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3889", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "CrBVl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Looks like after yesterday's production upgrade Pay Bands are completely inaccessible on cainwatters and alayacare environment with authorization error ( jira here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3889"}, {"type": "text", "text": ")\n"}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " did you performed any salary bands updates in these environments since yesterday ?\n\n"}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Do we need to inform <PERSON><PERSON> of this outage?"}]}]}]}, {"ts": "1729624852.713119", "text": "<@U0690EB5JE5> comp cycle report generation in alayacare production environment is broken , raised a highest pri jira <https://compiify.atlassian.net/browse/COM-3888>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1729624852.713119", "reply_count": 3, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13949::73cbe3206c4e4d10bcb91bd7017ef0e0", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3888?atlOrigin=eyJpIjoiNGJhN2YyYjA3YzFkNGYzY2IzMmZmMzE4YWNmYWQ3YWIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3888 GC triggered in alayacare production environment on fetching budget…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13949::b33e43ba702f4102a102b948d2282042", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13949\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3888\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3888", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "U7jHb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " comp cycle report generation in alayacare production environment is broken , raised a highest pri jira "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3888"}]}]}]}, {"ts": "1729622887.729249", "text": "just a heads up was busy in morning with amanda on following issue\n1. curana perf rating config setup + perf rating data upload which failed\n2. degenkolb pay bands upload issue, now resolved.\n3. alayacare hourly employee dat aupdate ", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PWX7i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "just a heads up was busy in morning with amanda on following issue\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "curana perf rating config setup + perf rating data upload which failed"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "degenkolb pay bands upload issue, now resolved."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "alayacare hourly employee dat aupdate "}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1729622738.214289", "text": "<@U07EJ2LP44S> what's the status of tool tips?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729622738.214289", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "r5esf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " what's the status of tool tips?"}]}]}]}], "created_at": "2025-05-22T21:35:34.663258"}