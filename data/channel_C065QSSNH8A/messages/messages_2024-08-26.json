{"date": "2024-08-26", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1724693727.008229", "text": "*Manager Enablement Question*\nDo you have any other documents, notes or internal meeting recordings/transcriptions about Stride's ideas related to the concept of manager enablement? (aside from the <https://docs.google.com/document/d/13oJ8EhRUkr_wtHnCXRIjVxoDoq5Jpu9_Fg5NKPln148/edit#heading=h.e045j8nr4rcy|manager comms for adjustment letters concept>, which I already have)", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724693727.008229", "reply_count": 1, "edited": {"user": "U07HCJ07H7G", "ts": "1724693775.000000"}, "blocks": [{"type": "rich_text", "block_id": "pXCGy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Manager Enablement Question", "style": {"bold": true}}, {"type": "text", "text": "\nDo you have any other documents, notes or internal meeting recordings/transcriptions about Stride's ideas related to the concept of manager enablement? (aside from the "}, {"type": "link", "url": "https://docs.google.com/document/d/13oJ8EhRUkr_wtHnCXRIjVxoDoq5Jpu9_Fg5NKPln148/edit#heading=h.e045j8nr4rcy", "text": "manager comms for adjustment letters concept"}, {"type": "text", "text": ", which I already have)"}]}]}]}, {"ts": "1724693562.530679", "text": "<!here> Vercara got acquired last week. I don't know what their acquiring company uses for compensation but it's likely that they already have a system in place and the new company might not use us. I am still trying to get more info. Let's not do anymore work on Vercara until we have clarity from them.", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1724693583.000000"}, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "le50q", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Vercara got acquired last week. I don't know what their acquiring company uses for compensation but it's likely that they already have a system in place and the new company might not use us. I am still trying to get more info. Let's not do anymore work on Vercara until we have clarity from them."}]}]}]}, {"ts": "1724693031.251609", "text": "I shared a copy of the prioritization spreadsheet with you all - please let me know if you have any questions! <@U07EJ2LP44S> and <@U04DKEFP1K8>, feel free to go ahead and rank all the columns in Tabs 1 and 2. Tab 2 has additional prioritization criteria related to sales. It's totally ok to take wild guesses on the sales impact - it's still a useful exercise as you start to think about prioritizing beyond these first customers, even if you don't have this info right now since there is no sales person.\n\nLooking forward to discussing the results tomorrow.\n\n<https://docs.google.com/spreadsheets/d/1du9AeHuDyWtaTiwr4Cvb5f2j-FUYk1CbBMBmbag591c/edit?gid=1207079711#gid=1207079711>", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724693031.251609", "reply_count": 1, "files": [{"id": "F07JMM6V5PW", "created": 1724693034, "timestamp": 1724693034, "name": "COPY: Stride Prioritization Proof of Concept (WIP)", "title": "COPY: Stride Prioritization Proof of Concept (WIP)", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U07HCJ07H7G", "user_team": "T04DM97F1UM", "editable": false, "size": 39644, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1du9AeHuDyWtaTiwr4Cvb5f2j-FUYk1CbBMBmbag591c", "external_url": "https://docs.google.com/spreadsheets/d/1du9AeHuDyWtaTiwr4Cvb5f2j-FUYk1CbBMBmbag591c/edit?gid=1207079711#gid=1207079711", "url_private": "https://docs.google.com/spreadsheets/d/1du9AeHuDyWtaTiwr4Cvb5f2j-FUYk1CbBMBmbag591c/edit?gid=1207079711#gid=1207079711", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACG+5fdhWUfVc/1pP3399P8Avk/41IRznFGKQEY80j76f98f/Xpy+ZuG51I9lx/WlAI7d6UDmmAtFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07JMM6V5PW/copy__stride_prioritization_proof_of_concept__wip_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "t2KDB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I shared a copy of the prioritization spreadsheet with you all - please let me know if you have any questions! "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": ", feel free to go ahead and rank all the columns in Tabs 1 and 2. Tab 2 has additional prioritization criteria related to sales. It's totally ok to take wild guesses on the sales impact - it's still a useful exercise as you start to think about prioritizing beyond these first customers, even if you don't have this info right now since there is no sales person.\n\nLooking forward to discussing the results tomorrow.\n\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1du9AeHuDyWtaTiwr4Cvb5f2j-FUYk1CbBMBmbag591c/edit?gid=1207079711#gid=1207079711"}]}]}]}, {"ts": "1724665990.335549", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> Very sorry I accidentally clicked disconnect while I was working on a fix for Valgensis data and the connection is invalidated. We need to request customer to connect again to sync data if required. The button won't prompt unfortunately in our integrations page UI, Will add a prompt.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "QT32s", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Very sorry I accidentally clicked disconnect while I was working on a fix for Valgensis data and the connection is invalidated. We need to request customer to connect again to sync data if required. The button won't prompt unfortunately in our integrations page UI, Will add a prompt."}]}]}]}, {"ts": "1724652320.215309", "text": "<@U04DS2MBWP4> Would be great if you could add your inputs to this ticket\n<https://compiify.atlassian.net/browse/COM-3532>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724652320.215309", "reply_count": 1, "reactions": [{"name": "done", "users": ["U04DS2MBWP4"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13593::2d0e9780637111efb7238d34919ef61b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3532?atlOrigin=eyJpIjoiYjRkZjYxODkwOGNlNDk4Mzk3ZTllMDRhYzc0ZTI0ZGIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3532 Cycle Builder Usability enhancements>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13593::2d0e9782637111efb7238d34919ef61b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/082e10750208b91e38660f50da682c58?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13593::2d0e9781637111efb7238d34919ef61b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13593\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13593\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3532", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "cpEgt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Would be great if you could add your inputs to this ticket\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3532"}]}]}]}, {"ts": "1724651869.819389", "text": "Agenda for today:\n• Adjustment letters\n• Prioritization process by Vicky", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "z3esb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Adjustment letters"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Prioritization process by <PERSON>"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724651360.977579", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> Based on our experience with Adjustment letter work we did for Nauto, This is very time consuming and can have lot of scenarios based on each customer. Would be great if you could let us know if there are any customer in this year planning to use adjustment letters. Also, we should try to understand how each customer generate adjustment letters today which will help us be ready for future.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724651452.000000"}, "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "+rlom", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Based on our experience with Adjustment letter work we did for Nauto, This is very time consuming and can have lot of scenarios based on each customer. Would be great if you could let us know if there are any customer in this year planning to use adjustment letters. Also, we should try to understand how each customer generate adjustment letters today which will help us be ready for future."}]}]}]}], "created_at": "2025-05-22T21:35:34.625492"}