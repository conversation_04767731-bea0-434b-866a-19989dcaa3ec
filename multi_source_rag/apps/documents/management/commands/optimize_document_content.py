"""
Management command to optimize document content for performance.
"""

import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from django.db import models
from apps.documents.models import DocumentContent

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Optimize document content for performance by updating size and summary fields'

    def add_arguments(self, parser):
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Number of documents to process in each batch (default: 100)'
        )
        parser.add_argument(
            '--force-update',
            action='store_true',
            help='Force update even if fields already have values'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes'
        )

    def handle(self, *args, **options):
        batch_size = options['batch_size']
        force_update = options['force_update']
        dry_run = options['dry_run']

        self.stdout.write(
            self.style.SUCCESS(f'Starting document content optimization...')
        )

        # Get documents that need optimization
        if force_update:
            queryset = DocumentContent.objects.all()
            self.stdout.write(f'Processing all {queryset.count()} documents (force update)')
        else:
            queryset = DocumentContent.objects.filter(
                content_size=0
            )
            self.stdout.write(f'Processing {queryset.count()} documents with missing size/summary')

        if queryset.count() == 0:
            self.stdout.write(
                self.style.WARNING('No documents need optimization.')
            )
            return

        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'DRY RUN: Would process {queryset.count()} documents')
            )
            return

        # Process in batches
        updated_count = 0
        error_count = 0

        for i in range(0, queryset.count(), batch_size):
            batch = queryset[i:i + batch_size]

            with transaction.atomic():
                for doc_content in batch:
                    try:
                        needs_update = False

                        # Update content size if needed
                        if force_update or doc_content.content_size == 0:
                            if doc_content.content:
                                doc_content.content_size = len(doc_content.content)
                                needs_update = True

                        # Update summary if needed
                        if force_update or not doc_content.content_summary:
                            if doc_content.content and doc_content.content_size > 1000:
                                doc_content.content_summary = doc_content._generate_summary()
                                needs_update = True

                        if needs_update:
                            doc_content.save(update_fields=['content_size', 'content_summary'])
                            updated_count += 1

                            if updated_count % 50 == 0:
                                self.stdout.write(f'Processed {updated_count} documents...')

                    except Exception as e:
                        error_count += 1
                        logger.error(f'Error processing document {doc_content.id}: {str(e)}')
                        self.stdout.write(
                            self.style.ERROR(f'Error processing document {doc_content.id}: {str(e)}')
                        )

        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'Optimization complete! Updated {updated_count} documents, {error_count} errors.'
            )
        )

        # Show statistics
        self._show_statistics()

    def _show_statistics(self):
        """Show content size statistics."""
        self.stdout.write('\n' + self.style.SUCCESS('Content Statistics:'))

        total_docs = DocumentContent.objects.count()
        docs_with_summary = DocumentContent.objects.filter(
            content_summary__isnull=False
        ).exclude(content_summary='').count()

        large_docs = DocumentContent.objects.filter(content_size__gt=10000).count()
        medium_docs = DocumentContent.objects.filter(
            content_size__gt=1000,
            content_size__lte=10000
        ).count()
        small_docs = DocumentContent.objects.filter(content_size__lte=1000).count()

        self.stdout.write(f'  Total documents: {total_docs}')
        self.stdout.write(f'  Documents with summaries: {docs_with_summary}')
        self.stdout.write(f'  Large documents (>10KB): {large_docs}')
        self.stdout.write(f'  Medium documents (1-10KB): {medium_docs}')
        self.stdout.write(f'  Small documents (≤1KB): {small_docs}')

        if total_docs > 0:
            avg_size = DocumentContent.objects.aggregate(
                avg_size=models.Avg('content_size')
            )['avg_size'] or 0
            self.stdout.write(f'  Average content size: {avg_size:.0f} characters')
