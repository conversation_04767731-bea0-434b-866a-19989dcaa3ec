{"thread_ts": "1737037184.481569", "channel_id": "C065QSSNH8A", "reply_count": 2, "replies": [{"ts": "1737038842.473219", "text": "This was the same issue so disregard.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737037184.481569", "blocks": [{"type": "rich_text", "block_id": "3X25r", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This was the same issue so disregard."}]}]}]}, {"ts": "1737038946.953439", "text": "Sure. I think I know the issue. Integration sync does not handle updating actively cycles properly like file upload does. Will take care of this going forward.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737037184.481569", "edited": {"user": "U0690EB5JE5", "ts": "1737038999.000000"}, "blocks": [{"type": "rich_text", "block_id": "8OcHY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure. I think I know the issue. Integration sync does"}, {"type": "text", "text": " not"}, {"type": "text", "text": " handle updating actively cycles properly like file upload does. Will take care of this going forward."}]}]}]}], "created_at": "2025-05-22T22:00:49.332020"}