#!/usr/bin/env python
"""
Debug the tenant being used in the search service during citation creation.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from apps.search.services.rag_service import RAGService
from apps.accounts.models import Tenant
from django.contrib.auth.models import User

def debug_search_tenant():
    """Debug the tenant being used in search service."""
    print("🔍 Debugging Search Service Tenant")
    print("=" * 50)
    
    # Get the user
    user = User.objects.get(email="<EMAIL>")
    print(f"Using user: {user.email}")
    
    # Get all tenants
    tenants = Tenant.objects.all()
    print(f"\n📊 Available tenants:")
    for tenant in tenants:
        print(f"   - {tenant.slug}: {tenant.name}")
    
    # Initialize the search service
    print(f"\n🔧 Initializing UnifiedRAGService with tenant 'stride'...")
    try:
        service = RAGService(user=user, tenant_slug=tenant="stride", user=user)
        print(f"✅ Service initialized successfully")
        print(f"   Service tenant slug: {service.tenant_slug}")
        print(f"   Service tenant object: {service.tenant.slug} ({service.tenant.name})")
        print(f"   Service tenant ID: {service.tenant.id}")
        
        # Check if this matches the stride tenant
        stride_tenant = Tenant.objects.get(slug="stride")
        print(f"\n📊 Tenant comparison:")
        print(f"   Stride tenant ID: {stride_tenant.id}")
        print(f"   Service tenant ID: {service.tenant.id}")
        print(f"   Match: {stride_tenant.id == service.tenant.id}")
        
        # Test a simple search to see what happens in citation creation
        print(f"\n🔍 Testing citation creation with a simple search...")
        
        # Monkey patch the citation creation to add debug info
        original_create_citations = service._create_citations
        
        def debug_create_citations(search_result, source_nodes):
            print(f"\n🔧 Citation creation debug:")
            print(f"   Search result tenant: {search_result.search_query.tenant.slug}")
            print(f"   Service tenant: {service.tenant.slug}")
            print(f"   Number of source nodes: {len(source_nodes)}")
            
            if source_nodes:
                # Test the first few nodes
                from apps.documents.models import EmbeddingMetadata
                for i, node in enumerate(source_nodes[:3]):
                    node_id = node.node_id
                    print(f"\n   Node {i+1}: {node_id}")
                    
                    # Test the lookup
                    chunk = EmbeddingMetadata.get_chunk_by_vector_id(node_id)
                    if chunk:
                        print(f"      ✅ Chunk found: {chunk.id}")
                        print(f"      Chunk tenant: {chunk.tenant.slug}")
                        print(f"      Tenant match: {chunk.tenant == service.tenant}")
                        
                        # This is the exact check from the service
                        if chunk and chunk.tenant != service.tenant:
                            print(f"      ❌ TENANT MISMATCH! Chunk will be set to None")
                        else:
                            print(f"      ✅ Tenant check passes")
                    else:
                        print(f"      ❌ No chunk found")
            
            # Call the original method
            return original_create_citations(search_result, source_nodes)
        
        # Replace the method
        service._create_citations = debug_create_citations
        
        # Perform a simple search
        result, docs = service.search("List issues reported by Amanda", top_k=5)
        
        print(f"\n📊 Search completed:")
        print(f"   Result ID: {result.id}")
        print(f"   Citations created: {result.citations.count()}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_search_tenant()
