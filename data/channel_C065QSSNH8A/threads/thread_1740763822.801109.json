{"thread_ts": "1740763822.801109", "channel_id": "C065QSSNH8A", "reply_count": 39, "replies": [{"ts": "1740764054.372459", "text": "<@U07EJ2LP44S> How soon this is required?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "ks2XS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " How soon this is required?"}]}]}]}, {"ts": "1740764079.461539", "text": "Also is it really must needed report? these need effort.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "o96bG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also is it really must needed report? these need effort."}]}]}]}, {"ts": "1740764168.717559", "text": "can we push back.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "F4AIa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "can we push back."}]}]}]}, {"ts": "1740764285.605999", "text": "This particular report is required. <PERSON> can spend a day building it herself in excel if she has to, but if we can build the report through a sql query in a shorter period, I would like to offer that to her.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "zk3UG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This particular report is required. <PERSON> can spend a day building it herself in excel if she has to, but if we can build the report through a sql query in a shorter period, I would like to offer that to her."}]}]}]}, {"ts": "1740764300.785229", "text": "Ideally Monday or Tuesday", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "Awr<PERSON>j", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ideally Monday or Tuesday"}]}]}]}, {"ts": "1740764490.071419", "text": "Let get back on this Monday. This will take us half a day and also back end forth.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "edited": {"user": "U0690EB5JE5", "ts": "1740764532.000000"}, "blocks": [{"type": "rich_text", "block_id": "BG0aZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let get back on this Monday. This will take us half a day and also back end forth."}]}]}]}, {"ts": "1740764545.980189", "text": "May be not much ROI.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "EcRnU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "May be not much ROI."}]}]}]}, {"ts": "1740764778.439959", "text": "And I am not sure if we should put such effort with situation we are in. I can work on it on Monday if you think we need to.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "zoZs0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And I am not sure if we should put such effort with situation we are in. I can work on it on Monday if you think we need to."}]}]}]}, {"ts": "1740767956.179219", "text": "I know we're in a strange situation, but they are still active customers. I believe we would have accommodated this a month ago, so I think we should do so now.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "HwaZL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I know we're in a strange situation, but they are still active customers. I believe we would have accommodated this a month ago, so I think we should do so now."}]}]}]}, {"ts": "1740768136.037719", "text": "Go it. I would have still questioned this request even if we were in a good situation, which customers can handle themselves using excel  based on reports we support :slightly_smiling_face: . Anyways I will work on it on Monday. Please do push back such requests going forward.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "0RSuY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Go it. I would have still questioned this request even if we were in a good situation, which customers can handle themselves using excel  based on reports we support "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " . Anyways I will work on it on Monday. Please do push back such requests going forward."}]}]}]}, {"ts": "1740768318.494859", "text": "I understand. This particular one would be very difficult to do in excel because you can't just roll up hierarchy for 500 people in an excel formula.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "P1Qzq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I understand. This particular one would be very difficult to do in excel because you can't just roll up hierarchy for 500 people in an excel formula."}]}]}]}, {"ts": "1741004525.922969", "text": "<@U07EJ2LP44S> couple of questions:\n• So we need to generate report with sum of all salaries bottom up and then bonus paid. Could you please help me with mapping to merit table columns to the report above. \n• Need little more clarity on the second table", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1741004713.000000"}, "blocks": [{"type": "rich_text", "block_id": "PxCie", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " couple of questions:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So we need to generate report with sum of all salaries bottom up and then bonus paid. Could you please help me with mapping to merit table columns to the report above. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Need little more clarity on the second table"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1741008032.299729", "text": "Only the summary table is required. Just the M8 level employees rolled up..", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "edited": {"user": "U07EJ2LP44S", "ts": "1741015846.000000"}, "blocks": [{"type": "rich_text", "block_id": "tUw6g", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Only the summary table is required. Just the M8 level employees rolled up.."}]}]}]}, {"ts": "1741008222.088869", "text": "I have a class this morning that I’m running out to, but from memory, there was a salaries, which is just the total salary column, there was a total bonus eligibility (which is just target bonus amount), I believe, a total bonus awarded column, and a number of employees for each of those levels.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "KJwZ5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have a class this morning that I’m running out to, but from memory, there was a salaries, which is just the total salary column, there was a total bonus eligibility"}, {"type": "text", "text": " (which is just target bonus amount)"}, {"type": "text", "text": ", I believe, a total bonus awarded column, and a number of employees for each of those levels."}]}]}]}, {"ts": "1741015016.037319", "text": "<@U07EJ2LP44S> Please take your time and share details. I will take this up tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "7PQoD", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please take your time and share details. I will take this up tomorrow."}]}]}]}, {"ts": "1741015188.065549", "text": "an example would be very helpful", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "WH+RD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "an example would be very helpful"}]}]}]}, {"ts": "1741015255.008129", "text": "I need to better understand what you’re asking for. All of the formulas are in the provided file and all the fields are very straightforward. What is it that you are needing?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "dugUT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I need to better understand what you’re asking for. All of the formulas are in the provided file and all the fields are very straightforward. What is it that you are needing?"}]}]}]}, {"ts": "1741015590.568419", "text": "The example is the summary table in the file.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "5St11", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The example is the summary table in the file."}]}]}]}, {"ts": "1741015927.704729", "text": "• Salaries seems to sum of Target Bonus am I correct?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "HgThC", "elements": [{"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Salaries seems to sum of Target Bonus am I correct?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1741015980.291569", "text": "• is Bonus Eligibility Earned Individual or Awarded?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "sj/Ui", "elements": [{"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "is Bonus Eligibility Earned Individual or Awarded?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1741016017.636029", "text": "what is Bonus EE?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "9UpDA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "what is Bonus EE?"}]}]}]}, {"ts": "1741016067.041209", "text": "Salaries = sum of salaries.\nBonus Eligibility = Total Target Bonus", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "rmjwf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Salaries = sum of salaries.\nBonus Eligibility = Total Target Bonus"}]}]}]}, {"ts": "1741016074.195979", "text": "I see the file I sent stripped the formulas", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "kgz3Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I see the file I sent stripped the formulas"}]}]}]}, {"ts": "1741016079.920089", "text": "Let me attach the xls it has all that", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "wS8EY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me attach the xls it has all that"}]}]}]}, {"ts": "1741016163.027139", "text": "in the sheet above `Bonus Eligibility $`/ `Salaries $`  is giving me 155% which is not making sense to me.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "6USfT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "in the sheet above "}, {"type": "text", "text": "Bonus Eligibility $", "style": {"code": true}}, {"type": "text", "text": "/ "}, {"type": "text", "text": "Salaries $", "style": {"code": true}}, {"type": "text", "text": "  is giving me 155% which is not making sense to me."}]}]}]}, {"ts": "1741016167.914959", "text": "All formulas are here", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "files": [{"id": "F08FTLGPR37", "created": 1741016163, "timestamp": 1741016163, "name": "2023 Annual Bonus Worksheet.xlsx", "title": "2023 Annual Bonus Worksheet.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 285379, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FTLGPR37/2023_annual_bonus_worksheet.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FTLGPR37/download/2023_annual_bonus_worksheet.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FTLGPR37-e0a03e14e1/2023_annual_bonus_worksheet_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FTLGPR37-e0a03e14e1/2023_annual_bonus_worksheet_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FTLGPR37/2023_annual_bonus_worksheet.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FTLGPR37-e02b6d11c6", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "cP3BX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All formulas are here"}]}]}]}, {"ts": "1741016203.230659", "text": "That number is just dumb b/c the executives bonuses are higher than their salaries", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "4tSrd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That number is just dumb b/c the executives bonuses are higher than their salaries"}]}]}]}, {"ts": "1741016209.491429", "text": "all the other numbers are lower", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "F/kgx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "all the other numbers are lower"}]}]}]}, {"ts": "1741016260.057179", "text": "ah okay. Will work on this tomorrow. Thank You.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "L5fEp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ah okay. Will work on this tomorrow. Thank You."}]}]}]}, {"ts": "1741016337.434259", "text": "it should be very straightforward, it's just summary fields for salary, bonus, and bonus awarded, with those various things divided into each other, or into the total # employees in each group.\n\nThey are asking for the summary table on the top of the first sheet, that's all. They would like all M8 levels, then the exec group (which is <PERSON><PERSON>on's Direct Reports)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "Nn++a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "it should be very straightforward, it's just summary fields for salary, bonus, and bonus awarded, with those various things divided into each other, or into the total # employees in each group.\n\nThey are asking for the summary table on the top of the first sheet, that's all. They would like all M8 levels, then the exec group (which is <PERSON><PERSON>on's Direct Reports)"}]}]}]}, {"ts": "1741016534.657619", "text": "I am away right now. Will try to work on it once I am back. I am not committing as I am not sure how long it takes for me to return home.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "edited": {"user": "U0690EB5JE5", "ts": "1741016569.000000"}, "blocks": [{"type": "rich_text", "block_id": "iqClT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am away right now. Will try to work on it once I am back. I am not committing as I am not sure how long it takes for me to return home."}]}]}]}, {"ts": "1741022252.786379", "text": "<@U07EJ2LP44S> xls was helpful. One last question. This should contain only eligible employee count? Basically the report will reflect data of <PERSON><PERSON>'s view?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "files": [{"id": "F08FNTH7NB0", "created": 1741022243, "timestamp": 1741022243, "name": "Screenshot 2025-03-03 at 10.47.12 PM.png", "title": "Screenshot 2025-03-03 at 10.47.12 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 428124, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FNTH7NB0/screenshot_2025-03-03_at_10.47.12___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FNTH7NB0/download/screenshot_2025-03-03_at_10.47.12___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FNTH7NB0-b1ce6e0bd9/screenshot_2025-03-03_at_10.47.12___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FNTH7NB0-b1ce6e0bd9/screenshot_2025-03-03_at_10.47.12___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FNTH7NB0-b1ce6e0bd9/screenshot_2025-03-03_at_10.47.12___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 168, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FNTH7NB0-b1ce6e0bd9/screenshot_2025-03-03_at_10.47.12___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 224, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FNTH7NB0-b1ce6e0bd9/screenshot_2025-03-03_at_10.47.12___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FNTH7NB0-b1ce6e0bd9/screenshot_2025-03-03_at_10.47.12___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 335, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FNTH7NB0-b1ce6e0bd9/screenshot_2025-03-03_at_10.47.12___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 373, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FNTH7NB0-b1ce6e0bd9/screenshot_2025-03-03_at_10.47.12___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 447, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FNTH7NB0-b1ce6e0bd9/screenshot_2025-03-03_at_10.47.12___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 477, "original_w": 3314, "original_h": 1544, "thumb_tiny": "AwAWADDR5I6ClA9aTGBQaAFwKMCgHnFLQAYFJilooAQ9KQjilooAO9LRRQAUUUUAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FNTH7NB0/screenshot_2025-03-03_at_10.47.12___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FNTH7NB0-d596d5cf69", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "pDTbN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " xls was helpful. One last question. This should contain only eligible employee count? Basically the report will reflect data of <PERSON><PERSON>'s view?"}]}]}]}, {"ts": "1741022436.287689", "text": "Yes exactly", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "vCjKm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes exactly"}]}]}]}, {"ts": "1741022597.833399", "text": "This was simple. I think the bonus/EE confused me. It is bit tricky to write the SQL query due the way the data is stored though it looks simple from the view. Worst case I will have to write some code which will take a little more time.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "+qDK+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This was simple. I think the bonus/EE confused me. It is bit tricky to write the SQL query due the way the data is stored though it looks simple from the view. Worst case I will have to write some code which will take a little more time."}]}]}]}, {"ts": "1741022660.545959", "text": "Yes the data itself is very simple. I don't assume getting to it is 'easy' though!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes the data itself is very simple. I don't assume getting to it is 'easy' though!"}]}]}]}, {"ts": "1741025508.012369", "text": "<@U07EJ2LP44S> I was able to generate one. Thanks to chat<PERSON>T for helping in writing that complex SQL query quickly", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "files": [{"id": "F08FZTY1PK6", "created": 1741025476, "timestamp": 1741025476, "name": "Diven_Summary.xlsx", "title": "Diven_Summary.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 5586, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FZTY1PK6/diven_summary.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FZTY1PK6/download/diven_summary.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FZTY1PK6-48af8a6c03/diven_summary_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FZTY1PK6-48af8a6c03/diven_summary_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FZTY1PK6/diven_summary.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FZTY1PK6-69b9a3d5d1", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "qt1aN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I was able to generate one. Thanks to chatGPT for helping in writing that complex SQL query quickly"}]}]}]}, {"ts": "1741026860.677319", "text": "Nice! AI for the win.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "ndJA6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Nice! AI for the win."}]}]}]}, {"ts": "1741028671.776749", "text": "Unfortunately this looks pretty off. For example, for Ridgeway he has only two directs and the salary is 416k. Did you include the manager too? This should be just the downline, the same way their budget is just for their downline. I'm thinking you added the exec, so for Ridgeway I think your calc has him AND his 2 reports. It should only be his reports", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740763822.801109", "blocks": [{"type": "rich_text", "block_id": "iOdR1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Unfortunately this looks pretty off. For example, for Ridgeway he has only two directs and the salary is 416k. Did you include the manager too? This should be just the downline, the same way their budget is just for their downline. I'm thinking you added the exec, so for Ridgeway I think your calc has him AND his 2 reports. It should only be his reports"}]}]}]}, {"ts": "1741082009.883089", "text": "Sorry my bad, was pretty sleepy :slightly_smiling_face: . Here is the corrected one.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "files": [{"id": "F08G23THJKC", "created": 1741082004, "timestamp": 1741082004, "name": "Diven_Summary.xlsx", "title": "Diven_Summary.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 5606, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08G23THJKC/diven_summary.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08G23THJKC/download/diven_summary.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G23THJKC-9fcfade98e/diven_summary_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G23THJKC-9fcfade98e/diven_summary_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08G23THJKC/diven_summary.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08G23THJKC-c27be53f07", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "pjv/U", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sorry my bad, was pretty sleepy "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " . Here is the corrected one."}]}]}]}], "created_at": "2025-05-22T22:00:49.354837"}