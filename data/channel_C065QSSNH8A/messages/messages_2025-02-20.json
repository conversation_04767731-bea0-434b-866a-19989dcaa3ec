{"date": "2025-02-20", "channel_id": "C065QSSNH8A", "message_count": 35, "messages": [{"ts": "1740071115.841569", "text": "yes sure, Will first thing my morning.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "A+19M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes sure, Will first thing my morning."}]}]}]}, {"ts": "1740071114.144469", "text": "We need time to adjust the recommendations/budget info", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jUM+Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We need time to adjust the recommendations/budget info"}]}]}]}, {"ts": "1740071089.605649", "text": "Yes, can it be done by the time I get in?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wSBxO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, can it be done by the time I get in?"}]}]}]}, {"ts": "1740071077.289779", "text": "I can take care of that. Can it wait till tomorrow?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gliFJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can take care of that. Can it wait till tomorrow?"}]}]}]}, {"ts": "1740070976.256489", "text": "Do you want me to prep the document to only include people with a bonus to upload, or can you do that as you upload the doc?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "tgp+P", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you want me to prep the document to only include people with a bonus to upload, or can you do that as you upload the doc?"}]}]}]}, {"ts": "**********.514879", "text": "<@U0690EB5JE5> We need to upload the Curana performance ratings into the MIP account as well.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.514879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "xLTRA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We need to upload the Curana performance ratings into the MIP account as well."}]}]}]}, {"ts": "**********.973219", "text": "We are hosted on  Amazon AWS cloud. I don't have much insights into that.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ktL8k", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We are hosted on  Amazon AWS cloud. I don't have much insights into that."}]}]}]}, {"ts": "**********.389279", "text": "Thats depends on how AWS network works.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yu/Mm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thats depends on how AWS network works."}]}]}]}, {"ts": "**********.847859", "text": "Why everything but our Stride environments loaded is a mystery to me though", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ckiut", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Why everything but our Stride environments loaded is a mystery to me though"}]}]}]}, {"ts": "1740068271.323419", "text": "He reset the routers and it messed up all the DNS stuff", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5s2SE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "He reset the routers and it messed up all the DNS stuff"}]}]}]}, {"ts": "1740068240.920249", "text": "He doesn't usually work from here and he started futzing with things and didn't bother to say so", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VaBJh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "He doesn't usually work from here and he started futzing with things and didn't bother to say so"}]}]}]}, {"ts": "1740068217.369589", "text": "Yes it would have been <PERSON><PERSON><PERSON> if he would have told me :face_with_rolling_eyes:", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "CM24Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes it would have been GRE<PERSON> if he would have told me "}, {"type": "emoji", "name": "face_with_rolling_eyes", "unicode": "1f644"}]}]}]}, {"ts": "1740067904.655569", "text": "yes I thought so.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sVC4N", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes I thought so."}]}]}]}, {"ts": "1740067806.998869", "text": "It stopped working again but my husband just told me he's been having network problems so clearly its us. :confused:", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IjwyG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It stopped working again but my husband just told me he's been having network problems so clearly its us. "}, {"type": "emoji", "name": "confused", "unicode": "1f615"}]}]}]}, {"ts": "1740067458.209509", "text": "Ok cool.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "w+VhM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok cool."}]}]}]}, {"ts": "1740067320.976799", "text": "But I DID get stridedemo on chrome so I will use that for the training today", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MncbQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "But I DID get stridedemo on chrome so I will use that for the training today"}]}]}]}, {"ts": "1740067308.539259", "text": "I cannot get <PERSON><PERSON><PERSON> to load on <PERSON><PERSON> (it just takes me back to the login screen over and over again) but I can get every other environment", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "a1B6r", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I cannot get <PERSON><PERSON><PERSON> to load on <PERSON><PERSON> (it just takes me back to the login screen over and over again) but I can get every other environment"}]}]}]}, {"ts": "1740067251.515329", "text": "Ok I'm back on, and I've been able to get most things to load", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "54wH5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok I'm back on, and I've been able to get most things to load"}]}]}]}, {"ts": "1740066492.437069", "text": "I will restart, gimme 5.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/K/cs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will restart, gimme 5."}]}]}]}, {"ts": "1740066479.824519", "text": "i switched wifi, nothing. i am able to stream video and open literally any page but ours", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qgjdR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i switched wifi, nothing. i am able to stream video and open literally any page but ours"}]}]}]}, {"ts": "1740066424.571259", "text": "restart laptop once", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xpUuC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "restart laptop once"}]}]}]}, {"ts": "1740066396.609459", "text": "interesting. Try switching wifi if you have more than one at home.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Hz7qG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "interesting. Try switching wifi if you have more than one at home."}]}]}]}, {"ts": "1740066243.962259", "text": "It doesn't sit and churn or anything it just goes straight to a server not found", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fey4F", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It doesn't sit and churn or anything it just goes straight to a server not found"}]}]}]}, {"ts": "1740066222.436579", "text": "Nothing. I even tried incognito", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "swdzW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Nothing. I even tried incognito"}]}]}]}, {"ts": "1740066168.539789", "text": "i'll clear cookies", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "z+XTd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i'll clear cookies"}]}]}]}, {"ts": "1740066158.233659", "text": "everythign else is working", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "USPcF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "everythign else is working"}]}]}]}, {"ts": "1740066084.748199", "text": "I can login. Can you check your internet once?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WcQFY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can login. Can you check your internet once?"}]}]}]}, {"ts": "1740066022.470419", "text": "This is the chrome error", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F08E1TZ652S", "created": 1740066019, "timestamp": 1740066019, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 39897, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08E1TZ652S/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08E1TZ652S/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_360.png", "thumb_360_w": 360, "thumb_360_h": 236, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_480.png", "thumb_480_w": 480, "thumb_480_h": 315, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_720.png", "thumb_720_w": 720, "thumb_720_h": 473, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_800.png", "thumb_800_w": 800, "thumb_800_h": 525, "original_w": 810, "original_h": 532, "thumb_tiny": "AwAfADDTPAzTVbd2INOpFUL0oAWiiigAooooAKj2Jz8rVJSY9zQAzYnHytx9aNif3W5+tPx7mjHuaAI/LT+635mpaKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08E1TZ652S/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08E1TZ652S-d1e4ee93d5", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "1orET", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is the chrome error"}]}]}]}, {"ts": "1740066014.520519", "text": "Let me check ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FMP3J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me check "}]}]}]}, {"ts": "1740065996.885049", "text": "I have tried test, curana, tithely, valgenesis. No reports fro customers yet but I cannot get in in Safari or Chrome", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3THVL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have tried test, curana, tithely, valgenesis. No reports fro customers yet but I cannot get in in Safari or Chrome"}]}]}]}, {"ts": "1740065981.581669", "text": "All of them", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BRPCd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All of them"}]}]}]}, {"ts": "1740065959.235469", "text": "Which ENV is this?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DErKs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Which ENV is this?"}]}]}]}, {"ts": "1740065908.454379", "text": "<@U0690EB5JE5> Everything appears to be down right now:", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F08E8DKDD0B", "created": 1740065904, "timestamp": 1740065904, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 155235, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08E8DKDD0B/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08E8DKDD0B/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_360.png", "thumb_360_w": 360, "thumb_360_h": 193, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_480.png", "thumb_480_w": 480, "thumb_480_h": 257, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_720.png", "thumb_720_w": 720, "thumb_720_h": 386, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_800.png", "thumb_800_w": 800, "thumb_800_h": 429, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_960.png", "thumb_960_w": 960, "thumb_960_h": 514, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 549, "original_w": 1202, "original_h": 644, "thumb_tiny": "AwAZADDSppJzwRj6UN16UnHpQAuT6j8qMn1H5UlFACgnPJH5U6mcelKvXpQApGaTbTqKAG7TRtNOooAbtpQMUtFAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08E8DKDD0B/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08E8DKDD0B-65e91195ba", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Grm+I", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Everything appears to be down right now:"}]}]}]}, {"ts": "**********.230549", "text": "I think SSO should work for all recommenders", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GJOGc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think SSO should work for all recommenders"}]}]}]}, {"ts": "**********.382919", "text": "<@U0690EB5JE5> the other thing we need to ensure is that SSO will work for both accounts for Kia. They have a different set of recommenders. But I believe all the recommenders in the MIP account are also recommenders in the main account. It’s just more limited. Also, since they opened up the main cycle to all managers, do we need to re-assess the list of SSO folks in the primary account?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eEdKy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " the other thing we need to ensure is that SSO will work for both accounts for Kia. They have a different set of recommenders. But I believe all the recommenders in the MIP account are also recommenders in the main account. It’s just more limited. Also, since they opened up the main cycle to all managers, do we need to re-assess the list of SSO folks in the primary account?"}]}]}]}], "created_at": "2025-05-22T21:35:34.710967"}