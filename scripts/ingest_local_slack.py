#!/usr/bin/env python
"""
Re-ingest Slack data with upgraded 768d embeddings (BAAI/bge-base-en-v1.5)
"""
import os, sys, django

# Set the new embedding model before Django setup
os.environ["EMBEDDING_MODEL_NAME"] = "BAAI/bge-base-en-v1.5"

sys.path.append('.'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()
from apps.documents.services.ingestion_service import IngestionService
from apps.accounts.models import Tenant

print("🚀 Re-ingesting Slack data with 768d embeddings (BAAI/bge-base-en-v1.5)")
print("=" * 70)

tenant, _ = Tenant.objects.get_or_create(slug="stride", defaults={"name": "Stride Technologies"})
service = IngestionService(tenant=tenant)
source = service.create_source("Local Slack", "local_slack", {"data_dir": "../data/", "channel": "C065QSSNH8A", "custom_days": 730})

print(f"📊 Starting ingestion...")
processed, failed = service.process_source(source, batch_size=200, days_back=730)
print(f"✅ Processed: {processed}, Failed: {failed}")

if processed > 0:
    print(f"🎉 Successfully re-ingested {processed} documents with 768d embeddings!")
    print("💡 The search system now uses higher-quality vector representations")
else:
    print("⚠️  No documents were processed - check the data directory")
