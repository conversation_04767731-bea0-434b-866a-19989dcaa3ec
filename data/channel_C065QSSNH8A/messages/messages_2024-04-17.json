{"date": "2024-04-17", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1713351457.340209", "text": "<@U065H3M6WJV> nauto-test ENV is ready for integration", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1712872862.169409", "subtype": "thread_broadcast", "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}, {"name": "birthday_party_parrot", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F06VDCY53NU", "created": 1713351452, "timestamp": 1713351452, "name": "Screenshot 2024-04-17 at 4.26.19 PM.png", "title": "Screenshot 2024-04-17 at 4.26.19 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 186663, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06VDCY53NU/screenshot_2024-04-17_at_4.26.19___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06VDCY53NU/download/screenshot_2024-04-17_at_4.26.19___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 194, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 259, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 388, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 432, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 518, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 552, "original_w": 1917, "original_h": 1034, "thumb_tiny": "AwAZADBn2qf/AJ6H8hR9qn/vn9KhpaALcLXEylhMFAOOaJmuIVDGYMCccVJp/Mbf739KNR4iT/e/pQBV+1T/AN8/pR9qn/vn8hUVFACUtFFAFm2uRAhG0HJz1xSXNz56AbQuD65qvRQAUUUUAf/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06VDCY53NU/screenshot_2024-04-17_at_4.26.19___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06VDCY53NU-698eb6401b", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "F+3/I", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " nauto-test ENV is ready for integration"}]}]}]}, {"ts": "1713309339.970349", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I finally had some time to step through the calculation formulas <PERSON> documented in COM-2524. I have a bunch of questions, maybe we can start with async review and cover it live if needed. My questions are here in the bold &amp; green text: <https://docs.google.com/document/d/1k84xWPsTYasi0njEgAlEdyVaVgmNuQRJF5Kq0Cihynw/edit|Compiify formulas>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713309339.970349", "reply_count": 2, "files": [{"id": "F06UFAGF93Q", "created": 1713309341, "timestamp": 1713309341, "name": "Compiify formulas [COM-2524]", "title": "Compiify formulas [COM-2524]", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 148367, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1k84xWPsTYasi0njEgAlEdyVaVgmNuQRJF5Kq0Cihynw", "external_url": "https://docs.google.com/document/d/1k84xWPsTYasi0njEgAlEdyVaVgmNuQRJF5Kq0Cihynw/edit", "url_private": "https://docs.google.com/document/d/1k84xWPsTYasi0njEgAlEdyVaVgmNuQRJF5Kq0Cihynw/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTSOfek59TSt05pvy+9ADhx3paYCvvT6ACiiigBD0pvH92nHOOKTDetABx6UuPYUc+1HPtQAoooooAQ9OmaTH+z+tONJk0AJj/Z/WnUmTQM0ALRRRQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06UFAGF93Q/compiify_formulas__com-2524_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "QoH7f", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I finally had some time to step through the calculation formulas <PERSON> documented in COM-2524. I have a bunch of questions, maybe we can start with async review and cover it live if needed. My questions are here in the bold & green text: "}, {"type": "link", "url": "https://docs.google.com/document/d/1k84xWPsTYasi0njEgAlEdyVaVgmNuQRJF5Kq0Cihynw/edit", "text": "Compiify formulas"}]}]}]}], "created_at": "2025-05-22T21:35:34.604348"}