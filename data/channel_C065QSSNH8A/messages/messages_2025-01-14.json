{"date": "2025-01-14", "channel_id": "C065QSSNH8A", "message_count": 24, "messages": [{"ts": "1736875163.362239", "text": "<https://compiify.atlassian.net/browse/COM-4062> Bonus bugs", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14145::af3d2cf9159c48f8bfc8e84a00fb71a4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4062?atlOrigin=eyJpIjoiYTMwYzc0ZWRhODkzNDExODg1Mzg2MDM1ZjUxZmYzNzkiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4062 Bonus Feature Bugs>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14145::2a0c56fcc528439b87f5eb76a9f0e7fa", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14145\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4062\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4062", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "XlSUx", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4062"}, {"type": "text", "text": " Bonus bugs"}]}]}]}, {"ts": "1736872329.425949", "text": "Yes, that's what they said, their India office is out", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Re+7a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, that's what they said, their India office is out"}]}]}]}, {"ts": "1736872124.232649", "text": "Valgenesis being India Heavy, they would have declared Holiday.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1736872169.000000"}, "blocks": [{"type": "rich_text", "block_id": "5tQYZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis being India Heavy, they would have declared Holiday."}]}]}]}, {"ts": "1736872086.445009", "text": "Its Holiday in India.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lOXT6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Its Holiday in India."}]}]}]}, {"ts": "1736872049.489219", "text": "oh yes , <PERSON><PERSON><PERSON> not VG", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mZD7C", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "oh yes , <PERSON><PERSON><PERSON> not VG"}]}]}]}, {"ts": "1736872027.275349", "text": "Is it a holiday? Val Genesis mentioned their team is off today ", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vukmg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is it a holiday? Val Genesis mentioned their team is off today "}]}]}]}, {"ts": "1736871973.631159", "text": "I can join the call in five 5mnts. I returned sooner that I thought.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "JhKVc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can join the call in five 5mnts. I returned sooner that I thought."}]}]}]}, {"ts": "1736871784.382629", "text": "Call is with Curana today, not VG", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3B+ou", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Call is with Curana today, not VG"}]}]}]}, {"ts": "1736871427.456859", "text": "Please set expectation that we are still testing fixing some issues with column configurator.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "yYSmH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please set expectation that we are still testing fixing some issues with column configurator."}]}]}]}, {"ts": "1736871385.603849", "text": "Valgenesis ENV is updated with latest build and should be good.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "O/Fsx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis ENV is updated with latest build and should be good."}]}]}]}, {"ts": "1736871260.806059", "text": "We have a call with Val Genesis today to walk them through the product settings", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xDhI9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We have a call with Val Genesis today to walk them through the product settings"}]}]}]}, {"ts": "1736871205.109249", "text": "<@U07M6QKHUC9> I might miss the meeting due to festival at home", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3/4cP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I might miss the meeting due to festival at home"}]}]}]}, {"ts": "1736865261.401019", "text": "I will verify the ENVs once more tomorrow. Please do sanity check and let me know if you see any issues.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0h64M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will verify the ENVs once more tomorrow. Please do sanity check and let me know if you see any issues."}]}]}]}, {"ts": "1736865226.439009", "text": "<@U07EJ2LP44S> *UPDATE*:\n• I have updated all the ENVs with latest build\n• I am facing issue with data sync and could only sync data for div-energy. Please note for Diven, the old data was test data and have removed and replaced with latest synced data, also created a test cycle. Please do take a look. Any assistance needed in updating recommenders list please do let me know. And for Curana and Valgenesis, I will need another day.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1736865681.000000"}, "blocks": [{"type": "rich_text", "block_id": "Zg50R", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "text", "text": "UPDATE", "style": {"bold": true}}, {"type": "text", "text": ":\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have updated all the ENVs with latest build"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "I am facing issue with data sync and could only sync data for div-energy. Please note for <PERSON><PERSON>, the old data was test data and have removed and replaced with latest synced data, also created a test cycle. Please do take a look. Any assistance needed in updating recommenders list please do let me know. And for Curana and Valgenesis, I will need another day."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1736862030.899129", "text": "I am working on syncing data and updating all ENVs with latest build. Will update here once done.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736774149.596269", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "KH0yH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am working on syncing data and updating all ENVs with latest build. Will update here once done."}]}]}]}, {"ts": "1736808227.295919", "text": "<@U0690EB5JE5> feel free to bump up the aws resources for Curana if needed.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736808227.295919", "reply_count": 1, "edited": {"user": "U07M6QKHUC9", "ts": "1736808235.000000"}, "blocks": [{"type": "rich_text", "block_id": "OdaDy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " feel free to bump up the aws resources for Curana if needed."}]}]}]}, {"ts": "1736807506.892759", "text": "<@U0690EB5JE5> After you have fixed the filters in the org view, can we push column configurator to all active customer enviorments (valgenesis, curana, DE, tithely).\n<@U07EJ2LP44S> are you okay with that?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736807506.892759", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "3iHJ8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " After you have fixed the filters in the org view, can we push column configurator to all active customer enviorments (valgenesis, curana, DE, tithely).\n"}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " are you okay with that?"}]}]}]}, {"ts": "1736807433.278159", "text": "<https://compiify.atlassian.net/browse/COM-4061>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736807433.278159", "reply_count": 8, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14144::e1f01623dc6f42feaf62208a520c5ecf", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4061?atlOrigin=eyJpIjoiOWQ3ODE5ZjZlNjIxNGJlMWI5NGUxOGVlOWQyMTE4N2IiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4061 Issue: Improve scrolling behavior when switching between tabs>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14144::0a37558d61734ded8c71231c257eda2e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14144\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4061\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4061", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "FK7Ff", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4061"}]}]}]}, {"ts": "1736807090.885729", "text": "<@U0690EB5JE5> <https://compiify.atlassian.net/browse/COM-4060>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14143::64028d6385cf4a50b6a118316f96ba90", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4060?atlOrigin=eyJpIjoiNWFkY2EwMjIzOTY0NGYwODkxY2IzZGU1ZWZhZWJkM2EiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4060 Issue: Update Salary Tab Behavior in Merit Planning View>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14143::675358fe3f5044e2a29d88f1430b04c7", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14143\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4060\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4060", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "vYRN/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4060"}]}]}]}, {"ts": "1736806957.520559", "text": "<@U0690EB5JE5> It looks like  eng should do more basic QA testing as I am seeing basic bugs which should have been caught by eng itself. I am gonna hold off on testing this until  eng has done QA testing.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ipA5f", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " It looks like  eng should do more basic QA testing as I am seeing basic bugs which should have been caught by eng itself. I am gonna hold off on testing this until  eng has done QA testing."}]}]}]}, {"ts": "1736806848.969409", "text": "<@U0690EB5JE5> bug in column configurator\n<https://compiify.atlassian.net/browse/COM-4059>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14142::67c576c199464f75a80feaddf93f3f3a", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4059?atlOrigin=eyJpIjoiNDdlZTA1Yjk1ZTY3NGZjZjg3MWY3M2Y0MzA0Njk4ZjQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4059 Filters not working in org view>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14142::2f8fb6f6a2744b46af37e40a2cb135ac", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14142\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4059\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4059", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " bug in column configurator\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4059"}]}]}]}, {"ts": "1736806578.472289", "text": "<@U0690EB5JE5> bug in column configurator\n<https://compiify.atlassian.net/browse/COM-4058>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14141::189046e717544bbd8de00566a02a1c0e", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4058?atlOrigin=eyJpIjoiMTg5MjJjY2UxMWI0NDBkMDkyYjhmNGExZjM1YTliOTgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4058 Missing Fields for Variable Pay in Column Configurator>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14141::2553eefda7c44a90a5987ec6e8683cac", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14141\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4058\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4058", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "/OnKN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " bug in column configurator\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4058"}]}]}]}, {"ts": "1736806273.433139", "text": "<@U0690EB5JE5> bug in column configurator\n<https://compiify.atlassian.net/browse/COM-4056>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14139::45ea5a07a4974bf1a3f9f506e192dbf9", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4056?atlOrigin=eyJpIjoiYjUyNTQ1MGU3OGIxNDAzMjgzNDczMzhiNzk0NWE0ODEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4056 Frozen Columns Setting Not Persisting>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14139::38445ebc792d483cb17206c62ec5f40a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14139\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4056\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4056", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "3gdng", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " bug in column configurator\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4056"}]}]}]}, {"ts": "1736795356.625279", "text": "<@U0690EB5JE5> Potential Curana Bug\n<https://compiify.atlassian.net/browse/COM-4055>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736795356.625279", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14138::d73cf3d97a094540b997f73ce01338b8", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4055?atlOrigin=eyJpIjoiMmYwYjEzZmQwYzU4NDczNmE5OTRmMjYwNDg0NjA3N2MiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4055 Incorrect Display of Direct Reports in Mark Prizes Task List>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14138::c1696e195bcf460c8a916e1f01032ed1", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14138\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4055\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4055", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "/Kfof", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Potential Curana Bug\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4055"}]}]}]}], "created_at": "2025-05-22T21:35:34.693326"}