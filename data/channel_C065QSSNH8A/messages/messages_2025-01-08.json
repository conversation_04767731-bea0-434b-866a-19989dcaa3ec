{"date": "2025-01-08", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1736348563.895059", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9>\n<https://qa.stridehr.io/> is ready with latest changes and nauto data. We still testing and improving. Please take a look and let us know if any feedback. <@U06HN8XDC5A> and team has done a good job.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1736348847.000000"}, "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "attachments": [{"from_url": "https://qa.stridehr.io/", "service_icon": "https://qa.stridehr.io/apple-touch-icon.png", "id": 1, "original_url": "https://qa.stridehr.io/", "fallback": "Stride", "text": "Web site created using create-react-app", "title": "Stride", "title_link": "https://qa.stridehr.io/", "service_name": "qa.stridehr.io"}], "blocks": [{"type": "rich_text", "block_id": "8oViz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://qa.stridehr.io/"}, {"type": "text", "text": " is ready with latest changes and nauto data. We still testing and improving. Please take a look and let us know if any feedback. "}, {"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " and team has done a good job."}]}]}]}, {"ts": "1736346703.761659", "text": "on it.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "R74w+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "on it."}]}]}]}, {"ts": "1736345927.801669", "text": "Can we please turn back on Degenkolb; they are trying to access it.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736345927.801669", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "1kL/M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can we please turn back on Degenkolb; they are trying to access it."}]}]}]}, {"ts": "1736275614.380889", "text": "<@U07M6QKHUC9> can you try uploading this into diversified - employee template?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736275614.380889", "reply_count": 17, "files": [{"id": "F0880SQUV4Z", "created": 1736275612, "timestamp": 1736275612, "name": "DGOCRootUser.csv", "title": "DGOCRootUser.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1775, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F0880SQUV4Z/dgocrootuser.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F0880SQUV4Z/download/dgocrootuser.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0880SQUV4Z/dgocrootuser.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F0880SQUV4Z-f34c3dd2f6", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F0880SQUV4Z/dgocrootuser.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">Root</div><div class=\"cm-col\">User</div><div class=\"cm-col\"></div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">1/1/01</div><div class=\"cm-col\">US</div><div class=\"cm-col\">C</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/1/01</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">001- ALABAMA BHAM</div><div class=\"cm-col\">Root User</div><div class=\"cm-col\">Executive:Bham</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">9</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Full Time</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16491</div><div class=\"cm-col\">Robert</div><div class=\"cm-col\">Hutson</div><div class=\"cm-col\">Robert Hutson</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">1/1/01</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/1/01</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">001- ALABAMA BHAM</div><div class=\"cm-col\">CEO</div><div class=\"cm-col\">Executive:Bham</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">9</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Full Time</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">780000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">175</div><div class=\"cm-col cm-num\">1365000</div><div class=\"cm-col cm-num\">546000</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">819000</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">546000</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">819000</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">375</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 3, "lines_more": 2, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "QgW6U", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " can you try uploading this into diversified - employee template?"}]}]}]}], "created_at": "2025-05-22T21:35:34.695717"}