{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2024-09", "message_count": 203, "messages": [{"ts": "1725377373.374439", "text": "<!here> SDF test  ENV with their prod data is ready except for equity data where I need <@U04DKEFP1K8>’s help.\n<https://sdf-test.stridehr.io/>\n<@U07EJ2LP44S> You may need  <PERSON><PERSON><PERSON><PERSON>’s help to know the cycle configuration. I will share the the details with <PERSON><PERSON><PERSON><PERSON>.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1725377601.000000"}, "reactions": [{"name": "thankyouty", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Xfa//", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " SDF test  ENV with their prod data is ready except for equity data where I need "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "’s help.\n"}, {"type": "link", "url": "https://sdf-test.stridehr.io/"}, {"type": "text", "text": "\n"}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " You may need  <PERSON><PERSON><PERSON><PERSON><PERSON>s help to know the cycle configuration. I will share the the details with <PERSON><PERSON><PERSON><PERSON>."}]}]}]}, {"ts": "1725377556.379249", "text": "<@U04DKEFP1K8> <@U04DS2MBWP4> Enabled SSO for you both as well.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "MfJVQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Enabled SSO for you both as well."}]}]}]}, {"ts": "1725400099.691879", "text": "<!here> I'd like to go ahead and get the initial Manager Enablement Solution Proposal (early PRD) review and feedback session scheduled for next Monday, 9/9. Would you like to do it during your regular Leadership Standup? We'll need at least the full hour. Let me know if you'd like me to send a new invite with agenda, or if you just want to change the agenda of the existing meeting.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1725400099.691879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "jrf56", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I'd like to go ahead and get the initial Manager Enablement Solution Proposal (early PRD) review and feedback session scheduled for next Monday, 9/9. Would you like to do it during your regular Leadership Standup? We'll need at least the full hour. Let me know if you'd like me to send a new invite with agenda, or if you just want to change the agenda of the existing meeting."}]}]}]}, {"ts": "1725445709.582779", "text": "<!here> *Eng* *Updates*:\nFollowing changes are deployed until Test ENVs today\n• Ability to exclude employees by job title, compensation type\n• You can now run sync directly from integrations page (Screenshot below)\n> Clicking on *Sync* - will sync changes since Last Successful sync date\n> Clicking on *Full Sync* - will sync whole Org\n> Clicking on *Remove -* will delete the integration. \n> Please note its in MVP stage and UI needs more enhancements like combining both sync buttons and have date input to sync from any give date etc.\n> *What happens when you run sync:*\n>     ◦ It pulls all the data and updates/creates employees. *Caveat*:  There might some scenarios we may not supposed to overwrite and this will be handled in next iteration as part of delta sync work\n>     ◦ *Sets root employee as super admin* which is required for Org View to load.\n>     ◦ *Calculates Job Level* based on Reporting structure - highest number being the root employee\n>     ◦ *Sets Job Category* as `M` is employee has at least one employee reporting and `IC` if none reporting\n>     ◦ Tenure was not being updated and is being done now.\n>     ◦ *For Valgenesis:* Apart from pulling target bonus and division\n>         ▪︎ Since country is missing the country is extracted from currency and region\n>         ▪︎ Last raise date is being pulled from remote data\n> Basically from now on We can bring up Org View just clicking this button if we have integration. I have test this for Valgenesis. I am testing this for Div energy where I am seeing some issues which i am guessing data specific.\n> \n> *Upcoming work on integrations:*\n> • Add more controls to integrations page to give inputs like sync date, full sync etc...\n> • Delta sync. Ability to review, edit and approve on going changes like New Hire, Termination and  updates to existing employees\n> • Advanced feature - Ability to map remote data, at least internally. This is there already in merge page. But will have to brainstorm more. But this will be post November\n*Other changes pushed from the priority list until yesterday:*\n• COLA Component\n• View and edit perf rating in Org View\n• Region column in Org View", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1725445709.582779", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1725450246.000000"}, "reactions": [{"name": "rocket", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F07L80F355F", "created": 1725444937, "timestamp": 1725444937, "name": "Screenshot 2024-09-04 at 3.43.38 PM.png", "title": "Screenshot 2024-09-04 at 3.43.38 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 171443, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07L80F355F/screenshot_2024-09-04_at_3.43.38___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07L80F355F/download/screenshot_2024-09-04_at_3.43.38___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 199, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 266, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 399, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 443, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 532, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 567, "original_w": 1907, "original_h": 1056, "thumb_tiny": "AwAaADDQ4zzTuKbjIzx+NKFB7CgBeKOKTaM9vyoKj2oAXikOKXaPQUmAemKAF6D0pR0oFFAB3oNFFABSClooA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07L80F355F/screenshot_2024-09-04_at_3.43.38___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07L80F355F-6b99cd28f0", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "RHiAI", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}, {"type": "text", "text": "Eng", "style": {"bold": true}}, {"type": "text", "text": " "}, {"type": "text", "text": "Updates", "style": {"bold": true}}, {"type": "text", "text": ":\nFollowing changes are deployed until Test ENVs today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ability to exclude employees by job title, compensation type"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "You can now run sync directly from integrations page (Screenshot below)"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_quote", "elements": [{"type": "text", "text": "Clicking on"}, {"type": "text", "text": " Sync", "style": {"bold": true}}, {"type": "text", "text": " - will sync changes since Last Successful sync date\nClicking on"}, {"type": "text", "text": " Full Sync", "style": {"bold": true}}, {"type": "text", "text": " - will sync whole Org\nClicking on "}, {"type": "text", "text": "Remove - ", "style": {"bold": true}}, {"type": "text", "text": "will delete the integration. \nPlease note its in MVP stage and UI needs more enhancements like combining both sync buttons and have date input to sync from any give date etc.\n"}, {"type": "text", "text": "What happens when you run sync:", "style": {"bold": true}}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It pulls all the data and updates/creates employees. "}, {"type": "text", "text": "Caveat", "style": {"bold": true}}, {"type": "text", "text": ":  There might some scenarios we may not supposed to overwrite and this will be handled in next iteration as part of delta sync work"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Sets root employee as super admin", "style": {"bold": true}}, {"type": "text", "text": " which is required for Org View to load."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Calculates Job Level", "style": {"bold": true}}, {"type": "text", "text": " based on Reporting structure - highest number being the root employee"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Sets Job Category", "style": {"bold": true}}, {"type": "text", "text": " as "}, {"type": "text", "text": "M", "style": {"code": true}}, {"type": "text", "text": " is employee has at least one employee reporting and "}, {"type": "text", "text": "IC", "style": {"code": true}}, {"type": "text", "text": " if none reporting"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Tenure was not being updated and is being done now."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "For Valgenesis: ", "style": {"bold": true}}, {"type": "text", "text": "Apart from pulling target bonus and division"}]}], "style": "bullet", "indent": 1, "border": 1}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Since country is missing the country is extracted from currency and region"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Last raise date is being pulled from remote data"}]}], "style": "bullet", "indent": 2, "border": 1}, {"type": "rich_text_quote", "elements": [{"type": "text", "text": "Basically from now on We can bring up Org View just clicking this button if we have integration. I have test this for Valgenesis. I am testing this for Div energy where I am seeing some issues which i am guessing data specific.\n\n"}, {"type": "text", "text": "Upcoming work on integrations:", "style": {"bold": true}}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Add more controls to integrations page to give inputs like sync date, full sync etc..."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Delta sync. Ability to review, edit and approve on going changes like New Hire, Termination and  updates to existing employees"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Advanced feature - Ability to map remote data, at least internally. This is there already in merge page. But will have to brainstorm more. But this will be post November"}]}], "style": "bullet", "indent": 0, "border": 1}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Other changes pushed from the priority list until yesterday:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "COLA Component"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "View and edit perf rating in Org View"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Region column in Org View"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1725465292.804739", "text": "Nice. Did you mean they are deployed to just the Test environment and not the demo environment yet? <@U0690EB5JE5> ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "dxmrP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Nice. Did you mean they are deployed to just the Test environment and not the demo environment yet? "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1725465422.236779", "text": "<@U04DS2MBWP4> I am holding deployment to demo ENV as it will disable people insights alerts. I have been thinking to fix and merge but unable to get to that issue. ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "W5FdS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I am holding deployment to demo ENV as it will disable people insights alerts. I have been thinking to fix and merge but unable to get to that issue. "}]}]}]}, {"ts": "1725465451.190229", "text": "Ok", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PfDzz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok"}]}]}]}, {"ts": "1725466851.695219", "text": "Is there a way for me to get access to <http://Fireflies.ai|Fireflies.ai> for note taking during customer interviews? At this point I only have two scheduled, both later today, so I just need access for today. (One with our HR advisor <PERSON>, and one with a  personal contact who is a Sr. Director of a 900 person company for a manager perspective)\n\nI will record the interviews too, but having the automatic notes would save a bit of time not having to re-listen.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1725466851.695219", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "dwPoi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is there a way for me to get access to "}, {"type": "link", "url": "http://Fireflies.ai", "text": "Fireflies.ai"}, {"type": "text", "text": " for note taking during customer interviews? At this point I only have two scheduled, both later today, so I just need access for today. (One with our HR advisor <PERSON>, and one with a  personal contact who is a Sr. Director of a 900 person company for a manager perspective)\n\nI will record the interviews too, but having the automatic notes would save a bit of time not having to re-listen."}]}]}]}, {"ts": "1725487648.450519", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> Exponent got back about the SFTP and would like to schedule a call. Let me know if you want to join, <@U0690EB5JE5> - we could do 8am PST Friday", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1725487648.450519", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "ZuocT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Exponent got back about the SFTP and would like to schedule a call. Let me know if you want to join, "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " - we could do 8am PST Friday"}]}]}]}, {"ts": "1725510643.477429", "text": "<@U07EJ2LP44S> <@U0690EB5JE5> First draft of HRBP role is in jira <https://compiify.atlassian.net/browse/COM-3506>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1725510643.477429", "reply_count": 5, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13567::9e8284006b3f11efb536b9cee8dab712", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3506?atlOrigin=eyJpIjoiYzUzMTkzMTE0YzU5NDNkNDhiNDJkZjYwNTUxZTVmMDMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3506 Enable role HRBP>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13567::9e8284026b3f11efb536b9cee8dab712", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13567::9e8284016b3f11efb536b9cee8dab712", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13567\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13567\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3506", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "zqVCp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " First draft of HRBP role is in jira "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3506"}]}]}]}, {"ts": "1725546469.625849", "text": "Good news! <https://www.linkedin.com/in/michael-a-b0378a1a3/|<PERSON>> has signed the offer letter. His start date is Sept 17th.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "party_blob", "users": ["U07EJ2LP44S", "U0690EB5JE5"], "count": 2}, {"name": "raised_hands", "users": ["U0690EB5JE5", "U07HCJ07H7G", "U04DKEFP1K8"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "s5qCy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Good news! "}, {"type": "link", "url": "https://www.linkedin.com/in/michael-a-b0378a1a3/", "text": "<PERSON>"}, {"type": "text", "text": " has signed the offer letter. His start date is Sept 17th."}]}]}]}, {"ts": "1725551270.539649", "text": "will be about 5 min late to the leadership call", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1725551283.000000"}, "blocks": [{"type": "rich_text", "block_id": "ETF+O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will be about 5 min late to the leadership call"}]}]}]}, {"ts": "1725559572.579019", "text": "<@U04DKEFP1K8> we are terminating our contract with marketing but before we do that we need to publish the ROI calculator. I know you said last week the intern in on it. can we complete this task by Friday?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1725559572.579019", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "4L/fO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " we are terminating our contract with marketing but before we do that we need to publish the ROI calculator. I know you said last week the intern in on it. can we complete this task by Friday?"}]}]}]}, {"ts": "1725564339.335129", "text": "<@U04DS2MBWP4> what is latest on vercara?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1725564339.335129", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "ltMTm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " what is latest on vercara?"}]}]}]}, {"ts": "1725572628.395089", "text": "<@U07EJ2LP44S> I just sent the implementation kick off call invite with <PERSON><PERSON><PERSON> for next Tue 9:30. They have 1800 emp. out of which about 650 are eligible for merit. We can have 30 min leadership call on <PERSON><PERSON>.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1725572628.395089", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "zULXC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I just sent the implementation kick off call invite with <PERSON><PERSON><PERSON> for next <PERSON><PERSON> 9:30. They have 1800 emp. out of which about 650 are eligible for merit. We can have 30 min leadership call on <PERSON><PERSON>."}]}]}]}, {"ts": "1725572806.918679", "text": "<@U04DS2MBWP4> <@U0690EB5JE5> I have shared docs with <@U07EJ2LP44S> to get feedback from <PERSON> on items discussed in the standup earlier\n<https://docs.google.com/document/d/1tgEcItnJyst2Q7UYot_1xbuUp8SzvNxvaKG4PORKzRM/edit>\n<https://docs.google.com/document/d/185YfCyXN9uBEipdt0T1M88BjU7TelI8MsOBUjQCmt1I/edit>", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "zkCD8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I have shared docs with "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " to get feedback from <PERSON> on items discussed in the standup earlier\n"}, {"type": "link", "url": "https://docs.google.com/document/d/1tgEcItnJyst2Q7UYot_1xbuUp8SzvNxvaKG4PORKzRM/edit"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://docs.google.com/document/d/185YfCyXN9uBEipdt0T1M88BjU7TelI8MsOBUjQCmt1I/edit"}]}]}]}, {"ts": "1725637746.393159", "text": "Running few minutes late to the call at 9", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "I7hzA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Running few minutes late to the call at 9"}]}]}]}, {"ts": "1725643129.983059", "text": "Do we already have these?\n\nSubscriber may terminate this Agreement immediately if Compiifi does not obtain a SOC 2 Type II\ncertification by September 30, 2024 and continue in full force and effect for the duration of the Term a valid and\nactive industry recognized security standard and methodology certification (e.g. ISO, HITRUST, SOC 2 Type\nII) that meets or exceeds all HIPAA and HITECH requirements and other federal and state legal and regulatory\ndata security requirements that apply to Compiifi at any time during the Term.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1725643129.983059", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "k1Wvo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we already have these?\n\nSubscriber may terminate this Agreement immediately if Compiifi does not obtain a SOC 2 Type II\ncertification by September 30, 2024 and continue in full force and effect for the duration of the Term a valid and\nactive industry recognized security standard and methodology certification (e.g. ISO, HITRUST, SOC 2 Type\nII) that meets or exceeds all HIPAA and HITECH requirements and other federal and state legal and regulatory\ndata security requirements that apply to Compiifi at any time during the Term."}]}]}]}, {"ts": "1725651920.552689", "text": "<@U07EJ2LP44S> here are some ofthe testimonial questions:\n\nWhat challenges were you facing with compensation management before implementing Stride?\n\nHow has Stride helped you streamline your compensation processes and save time for your HR team?\n\nCan you share an example of how Stride's data-driven insights have helped you make more informed decisions about employee pay?\n\nHow has Stride's platform helped you ensure pay equity across your organization, especially as you've grown?\n\nIn what ways has Stride improved communication about compensation between management and employees?\n\nAs a startup, how important was Stride's ability to integrate with your existing HR systems, and how smooth was the implementation process?\n\nFor other startups considering Stride, what would you say has been your ROI with Stride (hint: getting rid of manual spreadsheets to achieve operational efficiency, better pay decisions driving higher retention and higher engagement)\n\nMany startups struggle with creating a fair and transparent promotion and raise process. How has Stride supported your company in this area?", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "VEvNL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " here are some ofthe testimonial questions:\n\nWhat challenges were you facing with compensation management before implementing Stride?\n\nHow has Stride helped you streamline your compensation processes and save time for your HR team?\n\nCan you share an example of how Stride's data-driven insights have helped you make more informed decisions about employee pay?\n\nHow has Stride's platform helped you ensure pay equity across your organization, especially as you've grown?\n\nIn what ways has Stride improved communication about compensation between management and employees?\n\nAs a startup, how important was Stride's ability to integrate with your existing HR systems, and how smooth was the implementation process?\n\nFor other startups considering Stride, what would you say has been your ROI with Stride (hint: getting rid of manual spreadsheets to achieve operational efficiency, better pay decisions driving higher retention and higher engagement)\n\nMany startups struggle with creating a fair and transparent promotion and raise process. How has Stride supported your company in this area?"}]}]}]}, {"ts": "1725652540.808029", "text": "I think we can focus the <PERSON>uto testimonials on their experience in working with us, the white glove service we provided them, and the improvements they saw over manual processes. If they can quantify the ROI in terms of efficiency gained and impact of better pay decisions, that will be great. I guess that's what other prospects would want to hear.", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1725652556.000000"}, "blocks": [{"type": "rich_text", "block_id": "YMZl7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think we can focus the <PERSON>uto testimonials on their experience in working with us, the white glove service we provided them, and the improvements they saw over manual processes. If they can quantify the ROI in terms of efficiency gained and impact of better pay decisions, that will be great. I guess that's what other prospects would want to hear."}]}]}]}, {"ts": "1725893311.991049", "text": "First draft of the implementation deck - feedback welcome <https://docs.google.com/presentation/d/15Hh_CdS8jbrRcPc-7R0ov3unsTP_z_Qm/edit?usp=sharing&amp;ouid=115163084630192241671&amp;rtpof=true&amp;sd=true>", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07LYD032QH", "created": 1725893316, "timestamp": 1725893316, "name": "Implementation Master Deck.pptx", "title": "Implementation Master Deck.pptx", "mimetype": "application/vnd.openxmlformats-officedocument.presentationml.presentation", "filetype": "pptx", "pretty_type": "PowerPoint Presentation", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 27129, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "15Hh_CdS8jbrRcPc-7R0ov3unsTP_z_Qm", "external_url": "https://docs.google.com/presentation/d/15Hh_CdS8jbrRcPc-7R0ov3unsTP_z_Qm/edit?usp=sharing&ouid=115163084630192241671&rtpof=true&sd=true", "url_private": "https://docs.google.com/presentation/d/15Hh_CdS8jbrRcPc-7R0ov3unsTP_z_Qm/edit?usp=sharing&ouid=115163084630192241671&rtpof=true&sd=true", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYD032QH-3a872bbc48/implementation_master_deck_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYD032QH-3a872bbc48/implementation_master_deck_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYD032QH-3a872bbc48/implementation_master_deck_360.png", "thumb_360_w": 360, "thumb_360_h": 203, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYD032QH-3a872bbc48/implementation_master_deck_480.png", "thumb_480_w": 480, "thumb_480_h": 270, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYD032QH-3a872bbc48/implementation_master_deck_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYD032QH-3a872bbc48/implementation_master_deck_720.png", "thumb_720_w": 720, "thumb_720_h": 405, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYD032QH-3a872bbc48/implementation_master_deck_800.png", "thumb_800_w": 800, "thumb_800_h": 450, "original_w": 800, "original_h": 450, "thumb_tiny": "AwAbADCnRSqAfvHH4Zp21Ozn/vmussF2Y+Ytn0FD7ONhP407yh6t/wB8Gjyh/eb/AL4NK4EVFP2p3c/9801gB0OfwxTASlGe1JRQA75vej5vem0UAKfekoooA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07LYD032QH/implementation_master_deck.pptx", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "6aeOR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "First draft of the implementation deck - feedback welcome "}, {"type": "link", "url": "https://docs.google.com/presentation/d/15Hh_CdS8jbrRcPc-7R0ov3unsTP_z_Qm/edit?usp=sharing&ouid=115163084630192241671&rtpof=true&sd=true"}]}]}]}, {"ts": "1725958501.695519", "text": "Agenda for today:\n• Review open questions on customer requirements\n• Cycle Builder Usability enhancements", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1725965049.000000"}, "blocks": [{"type": "rich_text", "block_id": "8p49s", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Review open questions on customer requirements"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle Builder Usability enhancements"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1725965748.059959", "text": "<@U04DKEFP1K8> Following changes are deployed to <http://test.stridehr.io|test.stridehr.io>\n• Issue while creating compensation cycle - <https://compiify.atlassian.net/browse/COM-3541>\n• Unable to control viewing recommendation for individual merit component - <https://compiify.atlassian.net/browse/COM-3541>\n• `Sync` buttons on integration page are available only for Stride admins\n• diversified energy data sync via integration tested and fixed some bugs and optimized the slowness in sync with large dataset\n• Audit Log feature is now available only in <http://qa.stridehr.io|qa.stridehr.io>. We will stress test the feature in this ENV and deploy to regular ENVs", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1725966939.000000"}, "blocks": [{"type": "rich_text", "block_id": "BBoJE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Following changes are deployed to "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Issue while creating compensation cycle - "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3541"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Unable to control viewing recommendation for individual merit component - "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3541"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Sync", "style": {"code": true}}, {"type": "text", "text": " buttons on integration page are available only for Stride admins"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "diversified energy data sync via integration tested and fixed some bugs and optimized the slowness in sync with large dataset"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Audit Log feature is now available only in "}, {"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}, {"type": "text", "text": ". We will stress test the feature in this ENV and deploy to regular ENVs"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1725979907.992259", "text": "we have an imp demo next week. can we push audit log feature to demo env? <@U0690EB5JE5> <@U04DKEFP1K8>", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1ogwA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we have an imp demo next week. can we push audit log feature to demo env? "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1725980180.466539", "text": "Sure <@U04DS2MBWP4> end of this week worst case.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "prTq1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " end of this week worst case."}]}]}]}, {"ts": "1725982763.600529", "text": "<!here> i have a last minute errand came up at home, will be joining curana call at 930am", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "AuPTF", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " i have a last minute errand came up at home, will be joining curana call at 930am"}]}]}]}, {"ts": "1725982816.468099", "text": "<@U0690EB5JE5> since we only have 30 minutes today, do you have any customer questions for me? All I have for you right now is an update on Diversified's paycor data", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QAE9w", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " since we only have 30 minutes today, do you have any customer questions for me? All I have for you right now is an update on Diversified's paycor data"}]}]}]}, {"ts": "1725982887.258209", "text": "<@U07EJ2LP44S> I am waiting on clarifications/confirmation on recent new customer requirements. Needed an update. Other stuff can wait till tomorrow.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oI4aH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am waiting on clarifications/confirmation on recent new customer requirements. Needed an update. Other stuff can wait till tomorrow."}]}]}]}, {"ts": "1725982933.232139", "text": "• Enable three performance rating columns - DegenKolb\n• Justification requirements for promotions and pay increases - DegenKolb\n• Certain pay bands have an extended maximum value, with an additional amount available beyond the standard maximum - DegenKolb\n• Managing visibility of pay ranges available to reviewers - DegenKolb", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1725982933.232139", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "PrGCG", "elements": [{"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Enable three performance rating columns - DegenKolb"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Justification requirements for promotions and pay increases - DegenKolb"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Certain pay bands have an extended maximum value, with an additional amount available beyond the standard maximum - DegenKolb"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Managing visibility of pay ranges available to reviewers - DegenKolb"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1725982948.076399", "text": "We can do share updates offline also.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yl1OQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can do share updates offline also."}]}]}]}, {"ts": "1725982964.218159", "text": "I am fine to skip meeting if needed.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5SG2y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am fine to skip meeting if needed."}]}]}]}, {"ts": "1725983014.817369", "text": "The agenda for today’s meeting is to go over the OKRs", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mhoVL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The agenda for today’s meeting is to go over the OKRs"}]}]}]}, {"ts": "1725983018.011719", "text": "Also I am not aware if there are any new requirements to prioritize. Its been a while we discussed it feels like.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "iZhD3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also I am not aware if there are any new requirements to prioritize. Its been a while we discussed it feels like."}]}]}]}, {"ts": "1725983057.509019", "text": "<@U04DS2MBWP4> Sorry, I was deep into integrations work and haven't got a chance to look into OKR stuff.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yv7jf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Sorry, I was deep into integrations work and haven't got a chance to look into OKR stuff."}]}]}]}, {"ts": "1725983104.815369", "text": "will work with <@U04DKEFP1K8> on this my morning.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "po4T7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will work with "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " on this my morning."}]}]}]}, {"ts": "1725983170.393499", "text": "OK, we can discuss the companywide and customer success care okrs today. We will move engineering  okrs to tomorrow", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1725983170.393499", "reply_count": 1, "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5", "U07EJ2LP44S"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "jbkZd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OK, we can discuss the companywide and customer success care okrs today. We will move engineering  okrs to tomorrow"}]}]}]}, {"ts": "1725989645.483239", "text": "Good call with <PERSON><PERSON><PERSON>. Doesn't look any thing complex with them so far", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "AmVbA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Good call with <PERSON><PERSON><PERSON>. Doesn't look any thing complex with them so far"}]}]}]}, {"ts": "1725989667.113099", "text": "Do you have the emails for the other two guys, ka<PERSON><PERSON>? I only have brians'", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1725989667.113099", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "P4b3h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you have the emails for the other two guys, ka<PERSON><PERSON>? I only have brians'"}]}]}]}, {"ts": "1725990403.546429", "text": "<@U04DKEFP1K8> Is this correct for instructions? I know we need to change the url <https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1725990403.546429", "reply_count": 6, "files": [{"id": "F07M69YTJ3T", "created": 1725990406, "timestamp": 1725990406, "name": "Setting up Azure AD as SAML enterprise connection", "title": "Setting up Azure AD as SAML enterprise connection", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 124649, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50", "external_url": "https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit", "url_private": "https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1035, "thumb_tiny": "AwAwACXTJxSZ9v1ob6ZpAD7UALk+lLSc+1LzQAUUUUAI30puDTmpvHqaAF+alyfSjj1pfxoAKKKKAEP1pu0HvTyM03YKADb70uBSbBSgAdKAFooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07M69YTJ3T/setting_up_azure_ad_as_saml_enterprise_connection", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "wDUhM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Is this correct for instructions? I know we need to change the url "}, {"type": "link", "url": "https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit"}]}]}]}, {"ts": "1725991931.966479", "text": "<@U0690EB5JE5> Do you have an update on Diversified's Paycor data?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1725991931.966479", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "RDEFr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Do you have an update on Diversified's Paycor data?"}]}]}]}, {"ts": "1725999425.113189", "text": "<@U04DKEFP1K8> Can we push ROI calculator today or tomorrow if today is not possible?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1725999425.113189", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "fiafm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can we push ROI calculator today or tomorrow if today is not possible?"}]}]}]}, {"ts": "1726003831.876109", "text": "<@U0690EB5JE5> Draft for supporting base currency is here <https://docs.google.com/document/d/15rIlYquKvlR7HypDu8UsFd9wXnxdtVwHzKHnE3DqMdk/edit>. I have shared it with <PERSON> as well\ncc: <@U07EJ2LP44S> <@U07M6QKHUC9>", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "CV/ZC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Draft for supporting base currency is here "}, {"type": "link", "url": "https://docs.google.com/document/d/15rIlYquKvlR7HypDu8UsFd9wXnxdtVwHzKHnE3DqMdk/edit"}, {"type": "text", "text": ". I have shared it with <PERSON> as well\ncc: "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}]}]}]}, {"ts": "1726008852.794379", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> My email for SSO is changed from kapil.gupta to <mailto:<EMAIL>|<EMAIL>>.\n\nI am unable to login into any of the test, demo or production environments anymore. Can we please change my login email to <mailto:<EMAIL>|<EMAIL>> for all environements?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726008852.794379", "reply_count": 3, "edited": {"user": "U07M6QKHUC9", "ts": "1726008877.000000"}, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "RaY1R", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " My email for SSO is changed from kapil.gupta to "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ".\n\nI am unable to login into any of the test, demo or production environments anymore. Can we please change my login email to "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " for all environements?"}]}]}]}, {"ts": "**********.425459", "text": "<@U07EJ2LP44S> I was testing `Alayacare <> BambooHR` sync. We are not receiving email and comp data from their HRIS system. Could you please check with customer if the account they have authenticated with has required permissions for us to pull the email? The data pulled has 656 employees. Also please confirm if this count is correct.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.425459", "reply_count": 23, "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "TwEi/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I was testing "}, {"type": "text", "text": "Alayacare <> BambooHR", "style": {"code": true}}, {"type": "text", "text": " sync. We are not receiving email and comp data from their HRIS system. Could you please check with customer if the account they have authenticated with has required permissions for us to pull the email? The data pulled has 656 employees. Also please confirm if this count is correct."}]}]}]}, {"ts": "**********.424419", "text": "<!here> I really need one discussion on below two requirements. Probably today\n• Cycle closure. There are two tickets with details, I am still bit confused confused with terminologies. Need help in getting more clarity.\n• Cycle builder enhacements. <@U04DS2MBWP4> Need to review designs as team and prioritize the scope.\n• Customer requirements open questions\n<@U04DS2MBWP4> Can we move OKR discussion to Friday.\nAlso We haven't discussed on customer requirements after 9/5 and Not sure if anything new coming up and there are still open questions. FYI... I am not available for Thursday meeting.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.424419", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "mINGx", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I really need one discussion on below two requirements. Probably today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle closure. There are two tickets with details, I am still bit confused confused with terminologies. Need help in getting more clarity."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle builder enhacements. "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Need to review designs as team and prioritize the scope."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Customer requirements open questions"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Can we move OKR discussion to Friday.\nAlso We haven't discussed on customer requirements after 9/5 and Not sure if anything new coming up and there are still open questions. FYI... I am not available for Thursday meeting."}]}]}]}, {"ts": "1726074576.084289", "text": "<@U07EJ2LP44S> If <PERSON> agrees to provide feedback on manager enablement during the call today, we should have <PERSON> be on the call and present her preliminary mock ups. Make sense?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MEc2W", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " If <PERSON> agrees to provide feedback on manager enablement during the call today, we should have <PERSON> be on the call and present her preliminary mock ups. Make sense?"}]}]}]}, {"ts": "1726074596.756109", "text": "unless we have a specific agenda items for a the call today", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "opZOz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "unless we have a specific agenda items for a the call today"}]}]}]}, {"ts": "1726074618.765249", "text": "we need to ask about the merit view for bands, but other than that i think the ball is back in our court.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Mpvop", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we need to ask about the merit view for bands, but other than that i think the ball is back in our court."}]}]}]}, {"ts": "1726074634.157249", "text": "so we can likely use the time for that.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZbJX9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "so we can likely use the time for that."}]}]}]}, {"ts": "1726074674.063329", "text": "ok then let's have <PERSON> be ready to join the call if needed", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fta3q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok then let's have <PERSON> be ready to join the call if needed"}]}]}]}, {"ts": "1726074700.928059", "text": "I can send <PERSON> slack message if you need me to", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1726074715.000000"}, "blocks": [{"type": "rich_text", "block_id": "H3n5R", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can send <PERSON> slack message if you need me to"}]}]}]}, {"ts": "1726074764.328009", "text": "<PERSON> is already in this channel :slightly_smiling_face: Cc <@U07HCJ07H7G>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nkwCf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> is already in this channel "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " Cc "}, {"type": "user", "user_id": "U07HCJ07H7G"}]}]}]}, {"ts": "1726074833.089709", "text": "They did have one more topic - an equity request for this cycle that we need to discuss internally", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YgDet", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They did have one more topic - an equity request for this cycle that we need to discuss internally"}]}]}]}, {"ts": "1726074882.688949", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1726075204.393959", "text": "<@U0690EB5JE5> so far Demo, test and nauto loging is working for me. Rest including valgenesis, div energy, are not working", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726075204.393959", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "gxyKp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " so far Demo, test and nauto loging is working for me. Rest including valgenesis, div energy, are not working"}]}]}]}, {"ts": "1726076780.892599", "text": "Happy to join the call and get some feedback! I have my availability updated on my calendar, but I do have an appointment today 11:45 - 1pm PST during which I'll be offline", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1726076780.892599", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "v<PERSON>y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Happy to join the call and get some feedback! I have my availability updated on my calendar, but I do have an appointment today 11:45 - 1pm PST during which I'll be offline"}]}]}]}, {"ts": "1726090795.343059", "text": "<!here> With cainwatters providing us with a a 2nd version of their salary bands earlier in the day, we will need versioning support for Salary Bands. In the production environment we cannot directly update existing bands as it can affect calculations for previous cycle. Will discuss with <@U0690EB5JE5> scope for this change.\ncc: <@U07EJ2LP44S>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1726090795.343059", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "cRTWg", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " With cainwatters providing us with a a 2nd version of their salary bands earlier in the day, we will need versioning support for Salary Bands. In the production environment we cannot directly update existing bands as it can affect calculations for previous cycle. Will discuss with "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " scope for this change.\ncc: "}, {"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1726092204.273189", "text": "<@U07EJ2LP44S> is it possible to go through the SDF issues and ensure they are resolved before showing the demo to <PERSON> and her CTO?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "58j1z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " is it possible to go through the SDF issues and ensure they are resolved before showing the demo to <PERSON> and her CTO?"}]}]}]}, {"ts": "1726095531.373489", "text": "Yes, we just need to schedule a call out a little bit so I have time", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "3pMhA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, we just need to schedule a call out a little bit so I have time"}]}]}]}, {"ts": "1726157098.437619", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> just met with <PERSON><PERSON>. He's a GO from me", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "KSS7b", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " just met with <PERSON><PERSON>. He's a GO from me"}]}]}]}, {"ts": "1726169274.058029", "text": "<@U07HCJ07H7G> here are some product images for <PERSON><PERSON><PERSON>'s comp tool. it has some neat ways to show data to managers for decision making\n<https://www.visier.com/products/smart-compensation/?showForm=0>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726169274.058029", "reply_count": 1, "attachments": [{"from_url": "https://www.visier.com/products/smart-compensation/?showForm=0", "thumb_url": "https://www.visier.com/static/visier-og-image-289b36a6392a307b7342ffcf69bdee4c.jpg", "thumb_width": 1200, "thumb_height": 630, "service_icon": "https://www.visier.com/icons/icon-48x48.png?v=e8ec1f4912f47be2b7d645b2d5d5b0d6", "id": 1, "original_url": "https://www.visier.com/products/smart-compensation/?showForm=0", "fallback": "Visier Smart Compensation - Compensation Planning Solution | Visier", "text": "Make fair and smarter compensation decisions with Visier Smart Compensation. The compensation planning solution helps your organization maximize retention impact from merit increase budget and ensure equitable compensation decisions with people analytics.", "title": "Visier Smart Compensation - Compensation Planning Solution | Visier", "title_link": "https://www.visier.com/products/smart-compensation/?showForm=0", "service_name": "visier.com"}], "blocks": [{"type": "rich_text", "block_id": "rilcw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07HCJ07H7G"}, {"type": "text", "text": " here are some product images for <PERSON><PERSON><PERSON>'s comp tool. it has some neat ways to show data to managers for decision making\n"}, {"type": "link", "url": "https://www.visier.com/products/smart-compensation/?showForm=0"}]}]}]}, {"ts": "1726169305.633149", "text": "<@U07EJ2LP44S> gentle reminder on adding me to manager enablement channel :slightly_smiling_face:", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "g6wHz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " gentle reminder on adding me to manager enablement channel "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1726169333.182239", "text": "I definitely did! Maybe i readded the wrong one", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1Fsh7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I definitely did! Maybe i readded the wrong one"}]}]}]}, {"ts": "1726169336.863089", "text": "let me go look", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MUd9M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "let me go look"}]}]}]}, {"ts": "1726169428.918699", "text": "Ok you're in there. I know what I did. I clicked on you but didn't click the tiny little 'add' link so it didn't actually do anything. Done now!", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0dLWM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok you're in there. I know what I did. I clicked on you but didn't click the tiny little 'add' link so it didn't actually do anything. Done now!"}]}]}]}, {"ts": "1726170519.955039", "text": "<PERSON><PERSON><PERSON> I thought you were in there already! I see two <PERSON><PERSON><PERSON> on the list?? (do you have an evil twin I didn't know about)", "user": "U07HCJ07H7G", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6E91n", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> I thought you were in there already! I see two <PERSON><PERSON><PERSON> on the list?? (do you have an evil twin I didn't know about)"}]}]}]}, {"ts": "1726171010.702109", "text": "The Visier link reminded me... do you all have an internal list of all competitors, or any sort of competitive analysis <@U07M6QKHUC9>? I know of a few comp products, but I have a feeling there are many more out there that I am not aware of.\n\nNormally, I would have done a competitive analysis for a project like manager enablement, as it can be really critical for differentiation (and for understanding what the table stakes features are that all/most competitors have). I didn't go down that path yet because my time is so limited and I wanted to at least get you something to start with ASAP, but for the longer-term vision of Manager Enablement, this will be an important exercise.", "user": "U07HCJ07H7G", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GFJMi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The Visier link reminded me... do you all have an internal list of all competitors, or any sort of competitive analysis "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": "? I know of a few comp products, but I have a feeling there are many more out there that I am not aware of.\n\nNormally, I would have done a competitive analysis for a project like manager enablement, as it can be really critical for differentiation (and for understanding what the table stakes features are that all/most competitors have). I didn't go down that path yet because my time is so limited and I wanted to at least get you something to start with ASAP, but for the longer-term vision of Manager Enablement, this will be an important exercise."}]}]}]}, {"ts": "1726171410.311659", "text": "I don;t think other tools have this manager enablement piece yet. so not sure there is much we can find", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726171410.311659", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Z/rxV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I don;t think other tools have this manager enablement piece yet. so not sure there is much we can find"}]}]}]}, {"ts": "1726171425.742349", "text": "I think visier is probably one exception", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/z61t", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think visier is probably one exception"}]}]}]}, {"ts": "1726171453.623839", "text": "<@U07EJ2LP44S> we need a good response for <PERSON><PERSON> question:)", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726171453.623839", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "zSx88", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " we need a good response for <PERSON><PERSON> question:)"}]}]}]}, {"ts": "1726208923.721379", "text": "<!here> FYI... India Holiday calendar. We have a long weekend in October i.e. 30th Oct, 1st Nov. Its festival season in India.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "files": [{"id": "F06M66CEHK4", "created": 1709170503, "timestamp": 1709170503, "name": "Screenshot 2024-02-29 at 7.04.54 AM.png", "title": "Screenshot 2024-02-29 at 7.04.54 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 89730, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06M66CEHK4/screenshot_2024-02-29_at_7.04.54___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06M66CEHK4/download/screenshot_2024-02-29_at_7.04.54___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_360.png", "thumb_360_w": 360, "thumb_360_h": 297, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_480.png", "thumb_480_w": 480, "thumb_480_h": 396, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_720.png", "thumb_720_w": 720, "thumb_720_h": 594, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_800.png", "thumb_800_w": 800, "thumb_800_h": 660, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_960.png", "thumb_960_w": 960, "thumb_960_h": 792, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 845, "original_w": 1088, "original_h": 898, "thumb_tiny": "AwAnADDS5JPP6Uo6c03qerUuP9o0ALRSYP8AeNLQAUxTk/ez/wABp9N/i/i/pQAEc9D+dKBz0P4mkI5PA/OgKO4H50AOooooAD04GaaoOejfiacelMUYPQD8aAFI56L+VKBjsPwpaKACiiigBDyOMfjSKpB6L+Ap1FAH/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06M66CEHK4/screenshot_2024-02-29_at_7.04.54___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06M66CEHK4-8fb373344a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "m/z4z", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " FYI... India Holiday calendar. We have a long weekend in October i.e. 30th Oct, 1st Nov. Its festival season in India."}]}]}]}, {"ts": "1726243109.013599", "text": "<PERSON><PERSON><PERSON><PERSON> and I are going to run over, we are on with <PERSON><PERSON>", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "XwQYv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON> and I are going to run over, we are on with <PERSON><PERSON>"}]}]}]}, {"ts": "1726243425.371669", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1726243433.160619", "text": "Me too running 5 mnts late.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pbY85", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Me too running 5 mnts late."}]}]}]}, {"ts": "1726243472.896729", "text": "Let meet at 9:15 so you both get a little break", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+WQKz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let meet at 9:15 so you both get a little break"}]}]}]}, {"ts": "1726244242.642829", "text": "<@U0690EB5JE5> lets meet here <https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9gC7i", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " lets meet here "}, {"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1726244259.416369", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> you can join my zoom above", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "M5mCQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " you can join my zoom above"}]}]}]}, {"ts": "1726244361.346369", "text": "we are still on wtih v", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5/GVn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we are still on wtih v"}]}]}]}, {"ts": "1726244404.761649", "text": "<@U0690EB5JE5> Nauto feedback <https://app.fireflies.ai/view/Nauto-Feedback-Stride::T9mshlBSYB4VZWHi>", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"from_url": "https://app.fireflies.ai/view/Nauto-Feedback-Stride::T9mshlBSYB4VZWHi", "thumb_url": "https://files.fireflies.ai/app/img/preview.png", "thumb_width": 1280, "thumb_height": 720, "image_url": "https://files.fireflies.ai/app/img/preview.png", "image_width": 1280, "image_height": 720, "image_bytes": 188965, "service_icon": "https://app.fireflies.ai/favicon.ico", "id": 1, "original_url": "https://app.fireflies.ai/view/Nauto-Feedback-Stride::T9mshlBSYB4VZWHi", "fallback": "<PERSON><PERSON> / Stride - Meeting recording by Fireflies.ai", "text": "The Nauto Feedback/Stride meeting focused on gathering insights for product improvement and addressing user experiences during the merit cycle. Key discussions highl...", "title": "<PERSON><PERSON> / Stride - Meeting recording by Fireflies.ai", "title_link": "https://app.fireflies.ai/view/Nauto-Feedback-Stride::T9mshlBSYB4VZWHi", "service_name": "app.fireflies.ai"}], "blocks": [{"type": "rich_text", "block_id": "0f+HI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Nauto feedback "}, {"type": "link", "url": "https://app.fireflies.ai/view/Nauto-Feedback-Stride::T9mshlBSYB4VZWHi"}]}]}]}, {"ts": "1726248531.449859", "text": "<@U04DKEFP1K8> Following changes have been deployed from priority list and recent tickets\n1. Additional Planning Levels\n2. Breakdown budget usage when budget is combined\n3. COLA Component \n4. Exclude by job title/comp type\n5. Add a new column to show region\n6. Merit Planning audit log - only on <http://qa.stridehr.io|qa.stridehr.io> will merge it to remaining ENVs on Monday\n7. Hourly Employee support - merged today to <http://test.stridehr.io|test.stridehr.io>\n8. Edit/view Performance rating from org view\n9. Control Recommendations by sub components\n10. Fix budget alerts in Cycle insights\n11. Other bugs fixed\n    a. <https://compiify.atlassian.net/browse/COM-3588>\n    b. div energy cycle creation issues and minor enhancements i the same ticket\n12. OTE was also deployed long back but needs to be tested. Please ignore if done already", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1726248531.449859", "reply_count": 3, "edited": {"user": "U0690EB5JE5", "ts": "1726491918.000000"}, "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}, {"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13649::a55d833071f511efa99641c6f85bda48", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3588?atlOrigin=eyJpIjoiYTAzM2M1M2RlMmRlNDZjY2IzZDU5YTFmMDJlNTZkNzUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3588 Inconsistent Total Employee Count on eligibility rule step>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13649::a55d833271f511efa99641c6f85bda48", "elements": [{"type": "mrkdwn", "text": "Status: *In QA*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/712020:7d833b3a-e9d6-423f-93b8-d525378819e9/af9dff77-7442-4e64-9882-eb63aaa8f5a1/48?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13649::a55d833171f511efa99641c6f85bda48", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13649\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13649\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3588", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "px6n5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Following changes have been deployed from priority list and recent tickets\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Additional Planning Levels"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Breakdown budget usage when budget is combined"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "COLA Component "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Exclude by job title/comp type"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Add a new column to show region"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Merit Planning audit log - only on "}, {"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}, {"type": "text", "text": " will merge it to remaining ENVs on Monday"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Hourly Employee support - merged today to "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Edit/view Performance rating from org view"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Control Recommendations by sub components"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Fix budget alerts in Cycle insights"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Other bugs fixed"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3588"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "div energy cycle creation issues and minor enhancements i the same ticket"}]}], "style": "ordered", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OTE was also deployed long back but needs to be tested. Please ignore if done already"}]}], "style": "ordered", "indent": 0, "offset": 11, "border": 0}]}]}, {"ts": "1726250266.752679", "text": "I am thinking combining the case study and testimonial together might be best (for <PERSON> at least) So one call, recorded, going through case study questions. That will likely result in the material needed for the video testimonial. <https://docs.google.com/document/d/1h_1yUkG-H1zp03BH6p4k16yegGmAAbGp9SbpPznagCs/edit?usp=sharing>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726250266.752679", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "files": [{"id": "F07LYKDAAR5", "created": 1726250268, "timestamp": 1726250268, "name": "Case Study Instructions", "title": "Case Study Instructions", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 177885, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1h_1yUkG-H1zp03BH6p4k16yegGmAAbGp9SbpPznagCs", "external_url": "https://docs.google.com/document/d/1h_1yUkG-H1zp03BH6p4k16yegGmAAbGp9SbpPznagCs/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1h_1yUkG-H1zp03BH6p4k16yegGmAAbGp9SbpPznagCs/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTSP1xRuHrSN1FJ+VADtw9aMj1puR6Cl3Z9PzoAdRRRQAjcc03cPU0r/dpo+maAHBx70oOexpmP9k0oAz0NAD6KKKAGv92mY/zmnv8Adpn4ZoAMe4/OnJx9frSd+gpyjuMUAOooooA//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07LYKDAAR5/case_study_instructions", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "cV9Ea", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am thinking combining the case study and testimonial together might be best (for <PERSON> at least) So one call, recorded, going through case study questions. That will likely result in the material needed for the video testimonial. "}, {"type": "link", "url": "https://docs.google.com/document/d/1h_1yUkG-H1zp03BH6p4k16yegGmAAbGp9SbpPznagCs/edit?usp=sharing"}]}]}]}, {"ts": "1726497419.386839", "text": "<!here> I have a family emergency and will try to join the leadership call over the phone if I’m able to. ", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726497419.386839", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "jTTv4", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I have a family emergency and will try to join the leadership call over the phone if I’m able to. "}]}]}]}, {"ts": "1726510771.324969", "text": "Not sure if this goes here on in the engineering issues channel, but I can't load integrations on Curana (like has happened before)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726510771.324969", "reply_count": 3, "files": [{"id": "F07NAR7SEFJ", "created": 1726510767, "timestamp": 1726510767, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 44301, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NAR7SEFJ/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NAR7SEFJ/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NAR7SEFJ-d506a6c84b/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NAR7SEFJ-d506a6c84b/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NAR7SEFJ-d506a6c84b/image_360.png", "thumb_360_w": 360, "thumb_360_h": 116, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NAR7SEFJ-d506a6c84b/image_480.png", "thumb_480_w": 480, "thumb_480_h": 155, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NAR7SEFJ-d506a6c84b/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NAR7SEFJ-d506a6c84b/image_720.png", "thumb_720_w": 720, "thumb_720_h": 233, "original_w": 786, "original_h": 254, "thumb_tiny": "AwAPADDSZtoJwTjsKQk+/wCVOpNoznFADcn/ACtKCe/8qNo9P1o2j0oAUHPY/lSMOD16UtB+6aAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NAR7SEFJ/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NAR7SEFJ-98f00e5f0f", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "GyerX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Not sure if this goes here on in the engineering issues channel, but I can't load integrations on Curana (like has happened before)"}]}]}]}, {"ts": "1726520584.421339", "text": "i just scheduled sdf for the 26th over our daily standup. let me know if you want to push the standup an hour earlier (8am ET) or an hour back.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726520584.421339", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "IRvGZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i just scheduled sdf for the 26th over our daily standup. let me know if you want to push the standup an hour earlier (8am ET) or an hour back."}]}]}]}, {"ts": "1726582719.118999", "text": "<@U0690EB5JE5> Can you add <PERSON> to the demo and test environments?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ku8eH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you add <PERSON> to the demo and test environments?"}]}]}]}, {"ts": "1726584243.857379", "text": "<@U0690EB5JE5> and <@U04DKEFP1K8> i cannot get into the demo environment, can you check ?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726584243.857379", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "0YClF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " i cannot get into the demo environment, can you check ?"}]}]}]}, {"ts": "1726584581.276599", "text": "I have a customer demo today:)", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726584581.276599", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "YJ7z7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have a customer demo today:)"}]}]}]}, {"ts": "1726587314.951029", "text": "Product feedback from Monday board (I'm having trouble getting it transferred to a google doc so here's a pdf in the meantime)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726587314.951029", "reply_count": 1, "files": [{"id": "F07N5TEHHLZ", "created": 1726587310, "timestamp": 1726587310, "name": "Product Recommendations.pdf", "title": "Product Recommendations.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 8096656, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07N5TEHHLZ/product_recommendations.pdf", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07N5TEHHLZ/download/product_recommendations.pdf", "media_display_type": "unknown", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N5TEHHLZ-e62b0d55f3/product_recommendations_thumb_pdf.png", "thumb_pdf_w": 1833, "thumb_pdf_h": 2567, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07N5TEHHLZ/product_recommendations.pdf", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07N5TEHHLZ-cfd561e696", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "qXyQ8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Product feedback from Monday board (I'm having trouble getting it transferred to a google doc so here's a pdf in the meantime)"}]}]}]}, {"ts": "1726587746.462279", "text": "<!here> running 15 min late to standup ", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5/1o6", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " running 15 min late to standup "}]}]}]}, {"ts": "1726588583.189649", "text": "<@U0690EB5JE5> when does the data in the audit log start showing? is it only after it is submitted, reviewed and approved?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726588583.189649", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "wveGb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " when does the data in the audit log start showing? is it only after it is submitted, reviewed and approved?"}]}]}]}, {"ts": "**********.131859", "text": "Can we prioritize the bug that is preventing password reset from working? <@U0690EB5JE5> <@U04DKEFP1K8>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.131859", "reply_count": 13, "blocks": [{"type": "rich_text", "block_id": "fDz0d", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can we prioritize the bug that is preventing password reset from working? "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "**********.646679", "text": "<@U07EJ2LP44S> Started curana health UAT here <https://compiify.atlassian.net/browse/COM-3613> and have added the issue with data sync , will discuss it later tonight with <@U0690EB5JE5>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13674::aa0d2650751b11efaa1cef8da9b56fe2", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3613?atlOrigin=eyJpIjoiNDFmMzgxMGU4NDVmNGEwNWIwMTMyNjc3MzdiZTIzMTIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3613 Curana Health UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13674::aa0d2652751b11efaa1cef8da9b56fe2", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13674::aa0d2651751b11efaa1cef8da9b56fe2", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13674\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13674\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3613", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "z2dBT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Started curana health UAT here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3613"}, {"type": "text", "text": " and have added the issue with data sync , will discuss it later tonight with "}, {"type": "user", "user_id": "U0690EB5JE5"}]}]}]}, {"ts": "**********.416699", "text": "SaaS startup for dmarc, dkim stuff <https://easydmarc.com/> :slightly_smiling_face: Raised $20m, cmon :slightly_smiling_face:", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.416699", "reply_count": 1, "attachments": [{"from_url": "https://easydmarc.com/", "service_icon": "https://easydmarc.com/img/favicon/apple-touch-icon.png", "thumb_url": "https://easydmarc.com/img/easydmarc-og.jpg", "thumb_width": 1200, "thumb_height": 627, "id": 1, "original_url": "https://easydmarc.com/", "fallback": "EasyDMARC: EasyDMARC | DMARC Journey Made Simple", "text": "Your smart DMARC reporting and monitoring platform. Ensure domain-level security and email deliverability with EasyDMARC’s DMARC, SPF, DKIM, and BIMI services.", "title": "EasyDMARC | DMARC Journey Made Simple", "title_link": "https://easydmarc.com/", "service_name": "EasyDMARC"}], "blocks": [{"type": "rich_text", "block_id": "pLGK/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SaaS startup for dmarc, dkim stuff "}, {"type": "link", "url": "https://easydmarc.com/"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " Raised $20m, cmon "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1726610441.348659", "text": "I gave a demo to a customer today. They are deciding between <http://salary.com|salary.com> and us. It will be a good test to how do we fair against <http://salary.com|salary.com>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7DzKV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I gave a demo to a customer today. They are deciding between "}, {"type": "link", "url": "http://salary.com", "text": "salary.com"}, {"type": "text", "text": " and us. It will be a good test to how do we fair against "}, {"type": "link", "url": "http://salary.com", "text": "salary.com"}]}]}]}, {"ts": "1726617093.966589", "text": "<!here> For the leadership standup, let’s make sure we clearly distinguish between the November and January implementations. It’s highly critical that we consistently prioritize the November tasks over those scheduled for January.", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "100", "users": ["U07M6QKHUC9", "U07EJ2LP44S"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "Z+I9u", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " For the leadership standup, let’s make sure we clearly distinguish between the November and January implementations. It’s highly critical that we consistently prioritize the November tasks over those scheduled for January."}]}]}]}, {"ts": "1726636871.399349", "text": "<@U07M6QKHUC9> Finally renamed \"Compiify Admin\" to \"Stride Admin\" :slightly_smiling_face:", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "partyparrot", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Rd2o8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Finally renamed \"Compiify Admin\" to \"Stride Admin\" "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1726646853.846219", "text": "<@U07EJ2LP44S> sdf-test ENV is updated with their cycle data now it is copy of their current production state. <@U04DKEFP1K8> Please verify and let me know if anything else needs to be taken care w.r.t cycle per new merit flow.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ckgC7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " sdf-test ENV is updated with their cycle data now it is copy of their current production state. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Please verify and let me know if anything else needs to be taken care w.r.t cycle per new merit flow."}]}]}]}, {"ts": "1726672841.156789", "text": "<@U0690EB5JE5> can you add <@U07NBMXTL1E> to demo and test environments? With SSO?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726672841.156789", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "KaS+X", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you add "}, {"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " to demo and test environments? With SSO?"}]}]}]}, {"ts": "1726676848.775439", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> `<https://alayacare-test.stridehr.io/>` is ready. This has the exact copy of data from <http://demo.stridehr.io|demo.stridehr.io>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1726676848.775439", "reply_count": 4, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "67RtJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://alayacare-test.stridehr.io/", "style": {"code": true}}, {"type": "text", "text": " is ready. This has the exact copy of data from "}, {"type": "link", "url": "http://demo.stridehr.io", "text": "demo.stridehr.io"}]}]}]}, {"ts": "1726679624.823969", "text": "<@U0690EB5JE5> i am seeing this window pop up , fyi", "user": "U04DKEFP1K8", "type": "message", "files": [{"id": "F07N03F7RB5", "created": 1726679619, "timestamp": 1726679619, "name": "Screenshot 2024-09-18 at 10.13.10 AM.png", "title": "Screenshot 2024-09-18 at 10.13.10 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 181085, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07N03F7RB5/screenshot_2024-09-18_at_10.13.10___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07N03F7RB5/download/screenshot_2024-09-18_at_10.13.10___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_360.png", "thumb_360_w": 273, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_480.png", "thumb_480_w": 364, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_720.png", "thumb_720_w": 546, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_800.png", "thumb_800_w": 800, "thumb_800_h": 1055, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_960.png", "thumb_960_w": 728, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_1024.png", "thumb_1024_w": 776, "thumb_1024_h": 1024, "original_w": 978, "original_h": 1290, "thumb_tiny": "AwAwACTTooFRyRqxyQx+lAD9wPcUtQ+Um7O1uv4VNQAUUDpRQAVV8qYXe7rHnPWrPY4ppDH0oAcQT0OPwpR05pgDA9qcM96AFHSigdKKACmgL/d/SnUnNAFWe7WKUx+XnHfNOtroTSFAm3Az1qxj2FH4CqurbAKOlFFFSB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07N03F7RB5/screenshot_2024-09-18_at_10.13.10___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07N03F7RB5-51933f6087", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "n0y+H", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " i am seeing this window pop up , fyi"}]}]}]}, {"ts": "1726679754.516489", "text": "you can accept it. I will check why it shows tomorrow", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1726679754.516489", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Soyqt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "you can accept it. I will check why it shows tomorrow"}]}]}]}, {"ts": "**********.885749", "text": "Mayank will be demonstrating HRBP changes today’s call", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "doZrV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Mayank will be demonstrating HRBP changes today’s call"}]}]}]}, {"ts": "**********.502009", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> Still having trouble with the demo account. Right now it's hanging up on trying to download a report. Also a 'something went wrong' error on Org view. It eventually loaded.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.502009", "reply_count": 6, "edited": {"user": "U07EJ2LP44S", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "T8kf/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Still having trouble with the demo account. Right now it's hanging up on trying to download a report. Also a 'something went wrong' error on Org view. It eventually loaded."}]}]}]}, {"ts": "**********.171119", "text": "<https://docs.google.com/spreadsheets/d/1LB8R2sohO0uddIo664l2rbY9Wrx4mv-nLnp6bxocJ24/edit?usp=sharing>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.171119", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "9p24Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/spreadsheets/d/1LB8R2sohO0uddIo664l2rbY9Wrx4mv-nLnp6bxocJ24/edit?usp=sharing"}]}]}]}, {"ts": "**********.607629", "text": "<@U07EJ2LP44S> can you pls send me two good looking two adjustments letters created from Stride? Need to send this to a prospect", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.607629", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "8h7WZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can you pls send me two good looking two adjustments letters created from Stride? Need to send this to a prospect"}]}]}]}, {"ts": "1726761305.360049", "text": "<@U0690EB5JE5> demo env is not loading for me", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RczdC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " demo env is not loading for me"}]}]}]}, {"ts": "1726763752.630749", "text": "<PERSON><PERSON><PERSON> still hanging here:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726763752.630749", "reply_count": 4, "files": [{"id": "F07NV8ALAL8", "created": 1726763747, "timestamp": 1726763747, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 335103, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NV8ALAL8/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NV8ALAL8/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_360.png", "thumb_360_w": 360, "thumb_360_h": 220, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_480.png", "thumb_480_w": 480, "thumb_480_h": 293, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_720.png", "thumb_720_w": 720, "thumb_720_h": 439, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_800.png", "thumb_800_w": 800, "thumb_800_h": 488, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_960.png", "thumb_960_w": 960, "thumb_960_h": 586, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 625, "original_w": 3038, "original_h": 1854, "thumb_tiny": "AwAdADCa4ZlcAEjjoDUfmOP42/On3I/eD6VFQAvmN/eP50eY398/nTaKAH+Y/wDfb86TzH/vN+dJRQBLc/6wfSoqluDmQfQVEeRQAlFABHU5paAEpaKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NV8ALAL8/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NV8ALAL8-62055d10a4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "rdtBt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> still hanging here:"}]}]}]}, {"ts": "1726768530.004359", "text": "<@U07EJ2LP44S> *UPDATE on the issues:*\nThere is some database query breaking the server when you click on compensation cycle reports. Please do not click on reports until we fix the issue.\n*For cycle edit:* We have disabled audit trail for now and this solved the issue and I was able to complete the edit cycle flow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1726768530.004359", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "qffHR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "text", "text": "UPDATE on the issues:", "style": {"bold": true}}, {"type": "text", "text": "\nThere is some database query breaking the server when you click on compensation cycle reports. Please do not click on reports until we fix the issue.\n"}, {"type": "text", "text": "For cycle edit:", "style": {"bold": true}}, {"type": "text", "text": " We have disabled audit trail for now and this solved the issue and I was able to complete the edit cycle flow."}]}]}]}, {"ts": "1726769942.130109", "text": "Report issue is deeper than I thought and will take time. Will have closure on this tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1726768530.004359", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "WsQJ3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Report issue is deeper than I thought and will take time. Will have closure on this tomorrow."}]}]}]}, {"ts": "1726776597.736099", "text": "We did it and beat <http://Salary.com|Salary.com> :partying_face::moneybag:", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726776597.736099", "reply_count": 4, "files": [{"id": "F07NWE95W9E", "created": 1726776591, "timestamp": 1726776591, "name": "Screenshot 2024-09-19 at 1.08.44 PM.png", "title": "Screenshot 2024-09-19 at 1.08.44 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 198371, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NWE95W9E/screenshot_2024-09-19_at_1.08.44___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NWE95W9E/download/screenshot_2024-09-19_at_1.08.44___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NWE95W9E-8cd3cce57e/screenshot_2024-09-19_at_1.08.44___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NWE95W9E-8cd3cce57e/screenshot_2024-09-19_at_1.08.44___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NWE95W9E-8cd3cce57e/screenshot_2024-09-19_at_1.08.44___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 163, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NWE95W9E-8cd3cce57e/screenshot_2024-09-19_at_1.08.44___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 218, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NWE95W9E-8cd3cce57e/screenshot_2024-09-19_at_1.08.44___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NWE95W9E-8cd3cce57e/screenshot_2024-09-19_at_1.08.44___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 327, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NWE95W9E-8cd3cce57e/screenshot_2024-09-19_at_1.08.44___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 363, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NWE95W9E-8cd3cce57e/screenshot_2024-09-19_at_1.08.44___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 436, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NWE95W9E-8cd3cce57e/screenshot_2024-09-19_at_1.08.44___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 465, "original_w": 2374, "original_h": 1078, "thumb_tiny": "AwAVADDRHHalyfSjJ9qXNACc+lLn2oyPWjIoAM+1FFFADRyM07ApE+7S0AGBRgUUUAGKKKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NWE95W9E/screenshot_2024-09-19_at_1.08.44___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NWE95W9E-4b0ac7357c", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "yz7RN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We did it and beat "}, {"type": "link", "url": "http://Salary.com", "text": "Salary.com"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "partying_face", "unicode": "1f973"}, {"type": "emoji", "name": "moneybag", "unicode": "1f4b0"}]}]}]}, {"ts": "1726785562.005349", "text": "<!here> On friday(7/20) I will be away after 3pm PST", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "U+eZC", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " On friday(7/20) I will be away after 3pm PST"}]}]}]}, {"ts": "1726835438.265419", "text": "<@U07EJ2LP44S> Alayacare test ENV had some data issue and cycle config had gone into inconsistent state. I have reset the data to fix the issues with cycle and insights. Hope this is fine.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NVZG1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Alayacare test ENV had some data issue and cycle config had gone into inconsistent state. I have reset the data to fix the issues with cycle and insights. Hope this is fine."}]}]}]}, {"ts": "1726835494.157219", "text": "Please login and let me know if anything else required for Alayacare test. Please do adjustments as required and submissions as well", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1726835494.157219", "reply_count": 19, "blocks": [{"type": "rich_text", "block_id": "Ee9y2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please login and let me know if anything else required for Alayacare test. Please do adjustments as required and submissions as well"}]}]}]}, {"ts": "1726846488.933769", "text": "For the proration functionality, do the budgets themselves get prorated, or just the employees salary", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726846488.933769", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "IyN4i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For the proration functionality, do the budgets themselves get prorated, or just the employees salary"}]}]}]}, {"ts": "1726852236.867899", "text": "<@U07M6QKHUC9> FYI... We have disabled <PERSON><PERSON> trail in All ENVs for time being. We will fix the slownees its causing by end of next week hopefully.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VWlqi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " FYI... We have disabled <PERSON><PERSON> trail in All ENVs for time being. We will fix the slownees its causing by end of next week hopefully."}]}]}]}, {"ts": "1726852257.300569", "text": "Ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PfDzz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok"}]}]}]}, {"ts": "1726856950.812139", "text": "<https://us06web.zoom.us/rec/share/QqjgHjyY8JkiY4WEdaTPX9dJrANkc1l6EiMvX4gdL0tlQGhkEWPH4bWPWdgp4Tki.Fi4FQeJIAPaTL_DZ?startTime=1726852162000>\nPasscode: GVf0+f6a My call with <PERSON>.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "tada", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "9xj7p", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://us06web.zoom.us/rec/share/QqjgHjyY8JkiY4WEdaTPX9dJrANkc1l6EiMvX4gdL0tlQGhkEWPH4bWPWdgp4Tki.Fi4FQeJIAPaTL_DZ?startTime=1726852162000"}, {"type": "text", "text": "\nPasscode: GVf0+f6a My call with <PERSON>."}]}]}]}, {"ts": "1726856968.396759", "text": "It was very informal but her talking sections have a lot of good stuff", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3zNkQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It was very informal but her talking sections have a lot of good stuff"}]}]}]}, {"ts": "1726860847.288249", "text": "<@U0690EB5JE5> most of the alerts are gone from the demo env.\nAlso merit view is showing flags for out of range for bonus but there is no bonus configured in the comp cycle", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726860847.288249", "reply_count": 4, "files": [{"id": "F07NS8MHATT", "created": 1726860839, "timestamp": 1726860839, "name": "Screenshot 2024-09-20 at 12.31.28 PM.png", "title": "Screenshot 2024-09-20 at 12.31.28 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 146287, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NS8MHATT/screenshot_2024-09-20_at_12.31.28___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NS8MHATT/download/screenshot_2024-09-20_at_12.31.28___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NS8MHATT-7a1ba3846a/screenshot_2024-09-20_at_12.31.28___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NS8MHATT-7a1ba3846a/screenshot_2024-09-20_at_12.31.28___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NS8MHATT-7a1ba3846a/screenshot_2024-09-20_at_12.31.28___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 150, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NS8MHATT-7a1ba3846a/screenshot_2024-09-20_at_12.31.28___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 200, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NS8MHATT-7a1ba3846a/screenshot_2024-09-20_at_12.31.28___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NS8MHATT-7a1ba3846a/screenshot_2024-09-20_at_12.31.28___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 300, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NS8MHATT-7a1ba3846a/screenshot_2024-09-20_at_12.31.28___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 333, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NS8MHATT-7a1ba3846a/screenshot_2024-09-20_at_12.31.28___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 400, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NS8MHATT-7a1ba3846a/screenshot_2024-09-20_at_12.31.28___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 426, "original_w": 1892, "original_h": 788, "thumb_tiny": "AwATADDSP+eKB/nig9KAD/k0ALUZ69P/AB2pKZtPp+tNAOXoP8KU9KRRgD/GlpAFFFFABRRRQAU3JpxptAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NS8MHATT/screenshot_2024-09-20_at_12.31.28___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NS8MHATT-b13ab67ce9", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "asBuU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " most of the alerts are gone from the demo env.\nAlso merit view is showing flags for out of range for bonus but there is no bonus configured in the comp cycle"}]}]}]}, {"ts": "1727070502.869149", "text": "<!here> Today would like review all customer requirements and their priorities. In the stand up.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1727070513.000000"}, "blocks": [{"type": "rich_text", "block_id": "rW+vX", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Today would like review all customer requirements and their priorities. In the stand up."}]}]}]}, {"ts": "1727084945.366729", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> Regarding CWA cycle creation issue. There are missing data\n• Job Category is missing for a lot employees\n• CEO is not super admin\nPlease fix these data issues you should be good to create cycle.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727084945.366729", "reply_count": 3, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Vr0gd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Regarding CWA cycle creation issue. There are missing data\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Category is missing for a lot employees"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "CEO is not super admin"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Please fix these data issues you should be good to create cycle."}]}]}]}, {"ts": "1727084976.424709", "text": "Also the CEO was already super admin. Did you use reset mode by any chance to upload data?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727084976.424709", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "nwaS7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also the CEO was already super admin. Did you use reset mode by any chance to upload data?"}]}]}]}, {"ts": "1727086160.858729", "text": "<!here> I saw this note from <PERSON> in her transition doc. Should we remove budget from the view itself? We have pushed recently a change to show breakdown as well :slightly_smiling_face:", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727086160.858729", "reply_count": 2, "edited": {"user": "U0690EB5JE5", "ts": "1727087725.000000"}, "files": [{"id": "F07P8E1GL00", "created": 1727086156, "timestamp": 1727086156, "name": "Screenshot 2024-09-23 at 3.38.19 PM.png", "title": "Screenshot 2024-09-23 at 3.38.19 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 267630, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07P8E1GL00/screenshot_2024-09-23_at_3.38.19___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07P8E1GL00/download/screenshot_2024-09-23_at_3.38.19___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 201, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 268, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 401, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 446, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 535, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 571, "original_w": 1604, "original_h": 894, "thumb_tiny": "AwAaADDSf7pqLBPY1NScZoAj2H0o2N6VJ3xRQBHtb0p6Agc9adRQAU3vTqKAE/ipaKKACiiigD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07P8E1GL00/screenshot_2024-09-23_at_3.38.19___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07P8E1GL00-adadbbf41e", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "jXhqU", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I saw this note from <PERSON> in her transition doc. Should we remove budget from the view itself? We have pushed recently a change to show breakdown as well "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "**********.927899", "text": "<@U07EJ2LP44S> Curana Health Data sync update:\n• We are not receiving the comp info from sync \n• There are  total 2155 employees from sync \n• There are around 269 employees who do not have manager\n• There are 127 employees missing status \"Active\" or \"Inactive\" \nCould you please check with customer whats the criteria to pull data. Also request them to allow access to comp info.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.927899", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "bX1gt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Curana Health Data sync update:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We are not receiving the comp info from sync "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "There are  total 2155 employees from sync "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "There are around 269 employees who do not have manager"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "There are 127 employees missing status \"Active\" or \"Inactive\" "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Could you please check with customer whats the criteria to pull data. Also request them to allow access to comp info."}]}]}]}, {"ts": "**********.585039", "text": "Tithly signed.:partying_face::moneybag: <@U07EJ2LP44S> just introduced you to them to kick off the implementation process.", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "/Zw1g", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> signed."}, {"type": "emoji", "name": "partying_face", "unicode": "1f973"}, {"type": "emoji", "name": "moneybag", "unicode": "1f4b0"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " just introduced you to them to kick off the implementation process."}]}]}]}, {"ts": "**********.335259", "text": "<@U0690EB5JE5> Demo account issues are recorded here <https://compiify.atlassian.net/browse/COM-3626>", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13687::db2ede9d2fc946c8934ccc88944c3bc2", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3626?atlOrigin=eyJpIjoiY2QwOTNjMjEyNTcyNGFjNjkwNTNlZTkzODNhOTY1MzMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3626 Demo account issues [Sep 2024]>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13687::8f50684ab098441486996468c3919d68", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13687\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3626\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3626", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "vYqjR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Demo account issues are recorded here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3626"}]}]}]}, {"ts": "**********.994479", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I have users set up for Sonendo but the integrations page isn't loading. This happens on pretty much every new environment. Should be enabling something else when we create the internal users?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.994479", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "c5+yK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I have users set up for Sonendo but the integrations page isn't loading. This happens on pretty much every new environment. Should be enabling something else when we create the internal users?"}]}]}]}, {"ts": "**********.307489", "text": "<@U07EJ2LP44S> I hope <PERSON> was able to successfully demo to managers with no major issues. I guess no news is good news:slightly_smiling_face:", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.307489", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "/+07V", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I hope <PERSON> was able to successfully demo to managers with no major issues. I guess no news is good news"}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1727200233.227179", "text": "<!here> Competitor's demo if anyone is interested\n<https://drive.google.com/file/d/1cUqnc13an-rKQptELecGqT8ZDTpZe9SN/view?usp=sharing|Simply Merit Demo>\n<https://drive.google.com/file/d/1Vv7rumS3oUqPBfFs_9ANgWqbk9ARNQba/view?usp=sharing|Assemble Demo>\n<https://drive.google.com/file/d/1l85o1oOj5TJd7WTledJWRn40Q829d_dR/view?usp=sharing|Comprehensive Demo>\n<https://drive.google.com/file/d/1DlFF3ZUuicTRbPoT-NdhKbpty2mE1b_0/view?usp=sharing|Kamsa Demo>\n<https://drive.google.com/file/d/1482slTDH1EScVH5OHbY7SpygG2TuuO5R/view?usp=sharing|Pequity Demo>", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1727200293.000000"}, "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "files": [{"id": "F07PHM4VDCG", "created": 1727200250, "timestamp": 1727200250, "name": "Demo-Kamsa.MOV", "title": "Demo-Kamsa.MOV", "mimetype": "video/quicktime", "filetype": "mov", "pretty_type": "QuickTime Movie", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1DlFF3ZUuicTRbPoT-NdhKbpty2mE1b_0", "external_url": "https://drive.google.com/file/d/1DlFF3ZUuicTRbPoT-NdhKbpty2mE1b_0/view?usp=sharing", "url_private": "https://drive.google.com/file/d/1DlFF3ZUuicTRbPoT-NdhKbpty2mE1b_0/view?usp=sharing", "media_display_type": "video", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07PHM4VDCG/demo-kamsa.mov", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F07NXCE16EQ", "created": 1727200250, "timestamp": 1727200250, "name": "Assemble_Demo.MOV", "title": "Assemble_Demo.MOV", "mimetype": "video/quicktime", "filetype": "mov", "pretty_type": "QuickTime Movie", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1Vv7rumS3oUqPBfFs_9ANgWqbk9ARNQba", "external_url": "https://drive.google.com/file/d/1Vv7rumS3oUqPBfFs_9ANgWqbk9ARNQba/view?usp=sharing", "url_private": "https://drive.google.com/file/d/1Vv7rumS3oUqPBfFs_9ANgWqbk9ARNQba/view?usp=sharing", "media_display_type": "video", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NXCE16EQ/assemble_demo.mov", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F07NEAZK5HD", "created": 1727200256, "timestamp": 1727200256, "name": "SimplyMerit Demo1.mp4", "title": "SimplyMerit Demo1.mp4", "mimetype": "video/mp4", "filetype": "mp4", "pretty_type": "MPEG 4 Video", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 134115745, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1cUqnc13an-rKQptELecGqT8ZDTpZe9SN", "external_url": "https://drive.google.com/file/d/1cUqnc13an-rKQptELecGqT8ZDTpZe9SN/view?usp=sharing", "url_private": "https://drive.google.com/file/d/1cUqnc13an-rKQptELecGqT8ZDTpZe9SN/view?usp=sharing", "media_display_type": "video", "thumb_video": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NEAZK5HD-05ec70bd67/simplymerit_demo1_thumb_video.jpeg", "thumb_video_w": 1504, "thumb_video_h": 856, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NEAZK5HD/simplymerit_demo1.mp4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F07NS1T0XEZ", "created": 1727200281, "timestamp": 1727200281, "name": "Comprehensive Demo.mp4", "title": "Comprehensive Demo.mp4", "mimetype": "video/mp4", "filetype": "mp4", "pretty_type": "MPEG 4 Video", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 1032061817, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1l85o1oOj5TJd7WTledJWRn40Q829d_dR", "external_url": "https://drive.google.com/file/d/1l85o1oOj5TJd7WTledJWRn40Q829d_dR/view?usp=sharing", "url_private": "https://drive.google.com/file/d/1l85o1oOj5TJd7WTledJWRn40Q829d_dR/view?usp=sharing", "media_display_type": "video", "thumb_video": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NS1T0XEZ-f8fefa0752/comprehensive_demo_thumb_video.jpeg", "thumb_video_w": 1280, "thumb_video_h": 720, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NS1T0XEZ/comprehensive_demo.mp4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "rdi1X", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Competitor's demo if anyone is interested\n"}, {"type": "link", "url": "https://drive.google.com/file/d/1cUqnc13an-rKQptELecGqT8ZDTpZe9SN/view?usp=sharing", "text": "Simply Merit Demo"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://drive.google.com/file/d/1Vv7rumS3oUqPBfFs_9ANgWqbk9ARNQba/view?usp=sharing", "text": "Assemble Demo"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://drive.google.com/file/d/1l85o1oOj5TJd7WTledJWRn40Q829d_dR/view?usp=sharing", "text": "Comprehensive Demo"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://drive.google.com/file/d/1DlFF3ZUuicTRbPoT-NdhKbpty2mE1b_0/view?usp=sharing", "text": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://drive.google.com/file/d/1482slTDH1EScVH5OHbY7SpygG2TuuO5R/view?usp=sharing", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}]}]}, {"ts": "1727212344.870069", "text": "I am trying to create a new test cycle in SDF test, and it will not allow it.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727212344.870069", "reply_count": 4, "files": [{"id": "F07P8LP40SD", "created": 1727212343, "timestamp": 1727212343, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 95252, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07P8LP40SD/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07P8LP40SD/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_360.png", "thumb_360_w": 360, "thumb_360_h": 168, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_480.png", "thumb_480_w": 480, "thumb_480_h": 224, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_720.png", "thumb_720_w": 720, "thumb_720_h": 335, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_800.png", "thumb_800_w": 800, "thumb_800_h": 373, "original_w": 906, "original_h": 422, "thumb_tiny": "AwAWADDSJPGBnnn2pMn/ACKUkdzUE8LyPlJdoxjGTQgZOMn0/Kjn2qp9mm/57fqf8aPs03/Pb9T/AI07IV2W8c9aaqrzwKighkjk3PJuGMYyamQDrjmkxoUgHGaAoHSl7iigBKO9FHcUAHGaRKXvSJQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07P8LP40SD/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07P8LP40SD-3ef91f244b", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "KcM7x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am trying to create a new test cycle in SDF test, and it will not allow it."}]}]}]}, {"ts": "1727212351.675919", "text": "For purposes of demo", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ddHFm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For purposes of demo"}]}]}]}, {"ts": "1727212371.953819", "text": "Do I need to use the old one?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727212371.953819", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "N6JQB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do I need to use the old one?"}]}]}]}, {"ts": "1727212960.308939", "text": "<@U04DKEFP1K8> Eligibility Rule page", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727212960.308939", "reply_count": 2, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F07NFF8RFJB", "created": 1727212957, "timestamp": 1727212957, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 37426, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NFF8RFJB/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NFF8RFJB/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NFF8RFJB-d6db756e4d/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NFF8RFJB-d6db756e4d/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NFF8RFJB-d6db756e4d/image_360.png", "thumb_360_w": 360, "thumb_360_h": 138, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NFF8RFJB-d6db756e4d/image_480.png", "thumb_480_w": 480, "thumb_480_h": 184, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NFF8RFJB-d6db756e4d/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NFF8RFJB-d6db756e4d/image_720.png", "thumb_720_w": 720, "thumb_720_h": 276, "original_w": 798, "original_h": 306, "thumb_tiny": "AwASADDSbdg7QCfekJOe/wCVOooAaN3TJ/Klwf736UtFAB2prfdNOprnCE4oAdRRRQAUUUUAFI33TS0jfdNAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NFF8RFJB/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NFF8RFJB-4904e006a6", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Hjs2F", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eligibility Rule page"}]}]}]}, {"ts": "1727212965.916419", "text": "Looks ok but flashed up an error", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qfx2X", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Looks ok but flashed up an error"}]}]}]}, {"ts": "1727213190.201969", "text": "After selecting 'no' for combined budgets", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07NYH54THS", "created": 1727213182, "timestamp": 1727213182, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 52700, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NYH54THS/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NYH54THS/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_360.png", "thumb_360_w": 360, "thumb_360_h": 229, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_480.png", "thumb_480_w": 480, "thumb_480_h": 306, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_720.png", "thumb_720_w": 720, "thumb_720_h": 458, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_800.png", "thumb_800_w": 800, "thumb_800_h": 509, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_960.png", "thumb_960_w": 960, "thumb_960_h": 611, "original_w": 996, "original_h": 634, "thumb_tiny": "AwAeADDS59f0pCcDO7j6UvNHPegBOT/F+lLg+v6UUE4FAAAc9f0paZuPr+lPoAaT74pR9c1XmuvKkCbM8DnOKYt9k48vsT96nZiui5SVTF98wHl9f9qrtDVgTuJS0UUhn//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NYH54THS/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NYH54THS-e178646658", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Is3Yn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "After selecting 'no' for combined budgets"}]}]}]}, {"ts": "1727213275.604379", "text": "I went back to beginnning and ran through again and it looks normal", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F07PJS27AF2", "created": 1727213273, "timestamp": 1727213273, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 117992, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07PJS27AF2/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07PJS27AF2/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_360.png", "thumb_360_w": 360, "thumb_360_h": 140, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_480.png", "thumb_480_w": 480, "thumb_480_h": 187, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_720.png", "thumb_720_w": 720, "thumb_720_h": 280, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_800.png", "thumb_800_w": 800, "thumb_800_h": 311, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_960.png", "thumb_960_w": 960, "thumb_960_h": 373, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 398, "original_w": 2006, "original_h": 780, "thumb_tiny": "AwASADDSxRgelRfaIQSN4zT0dZFyhBHSnYLjunajNGKAuKQBk+lLSYoAxQAYHoKXGOlFFABRRRQAUUUUAf/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07PJS27AF2/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07PJS27AF2-aa21147faf", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Lssl8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I went back to beginnning and ran through again and it looks normal"}]}]}]}, {"ts": "1727213497.790369", "text": "Equity not allowing changes to the recommended % (defaults back to %0 if changed)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727213497.790369", "reply_count": 3, "files": [{"id": "F07NYHVCM0C", "created": 1727213494, "timestamp": 1727213494, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 151976, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NYHVCM0C/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NYHVCM0C/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_360.png", "thumb_360_w": 304, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_480.png", "thumb_480_w": 406, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_720.png", "thumb_720_w": 609, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_800.png", "thumb_800_w": 800, "thumb_800_h": 946, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_960.png", "thumb_960_w": 811, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_1024.png", "thumb_1024_w": 866, "thumb_1024_h": 1024, "original_w": 1114, "original_h": 1318, "thumb_tiny": "AwAwACi/K5QAhXbn+GmxyM7YKSLxnJqUj2zSY/2T+dMQc/7VKBjuT9aNo9KTkHgUhjqKBRQAhpAPX+dKcd6AR2NACAeufzpcD3/Og0mB/k0AKAB6/nS0gpaAENLSGigAooooAUUUCigD/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NYHVCM0C/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NYHVCM0C-6d9f54b779", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "0ULDd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Equity not allowing changes to the recommended % (defaults back to %0 if changed)"}]}]}]}, {"ts": "1727213570.127989", "text": "Also we have two equity pieces, and they behave slightly differently from each other. The second category showed no rating matrix until I chose which factor. The first one did not, it defaulted to showing the matrix.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "b/yvZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also we have two equity pieces, and they behave slightly differently from each other. The second category showed no rating matrix until I chose which factor. The first one did not, it defaulted to showing the matrix."}]}]}]}, {"ts": "1727213722.167339", "text": "Allocated budget page not loading:", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07NW3SV43C", "created": 1727213717, "timestamp": 1727213717, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 101420, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NW3SV43C/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NW3SV43C/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_360.png", "thumb_360_w": 360, "thumb_360_h": 153, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_480.png", "thumb_480_w": 480, "thumb_480_h": 204, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_720.png", "thumb_720_w": 720, "thumb_720_h": 306, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_800.png", "thumb_800_w": 800, "thumb_800_h": 340, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_960.png", "thumb_960_w": 960, "thumb_960_h": 408, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 435, "original_w": 2770, "original_h": 1178, "thumb_tiny": "AwAUADDS5paKKADFFFFABRRRQAUUUUAFFFFABRRRQB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NW3SV43C/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NW3SV43C-02bf009d25", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "KKrtA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Allocated budget page not loading:"}]}]}]}, {"ts": "1727219983.783259", "text": "<!here> Just FYI, we have to delete emails of <PERSON>, <PERSON><PERSON> and <PERSON> for SOC2 compliance purposes as they are external to the company. We will create external slack channels with them using their business/personal  emails for any communication/collaboration going forward.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "i97tm", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Just FYI, we have to delete emails of <PERSON>, <PERSON><PERSON> and <PERSON> for SOC2 compliance purposes as they are external to the company. We will create external slack channels with them using their business/personal  emails for any communication/collaboration going forward."}]}]}]}, {"ts": "**********.504509", "text": "<@U04DKEFP1K8> Can we pls replace <PERSON><PERSON>'s email (zoyaat stridehr) for test account with <mailto:zoya<PERSON><EMAIL>|<EMAIL>>, and <PERSON>'s email (vicky@stridehr) with <mailto:<EMAIL>|<EMAIL>>?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.504509", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "9hsos", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can we pls replace <PERSON><PERSON>'s email (zoyaat stridehr) for test account with "}, {"type": "link", "url": "mailto:z<PERSON><PERSON><PERSON>@gmail.com", "text": "<EMAIL>"}, {"type": "text", "text": ", and <PERSON>'s email (vicky@stridehr) with "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "**********.723239", "text": "<@U07EJ2LP44S> I fixed the cycle create issue on SDF-test but there is weird infra issue which I am looking into as of now.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "74hio", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I fixed the cycle create issue on SDF-test but there is weird infra issue which I am looking into as of now."}]}]}]}, {"ts": "**********.617619", "text": "I am trying few things ENV will be intermittently down for two hours.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.617619", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "8HCjs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am trying few things ENV will be intermittently down for two hours."}]}]}]}, {"ts": "1727279429.617149", "text": "Running 15 min late to the standup", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0qOG5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Running 15 min late to the standup"}]}]}]}, {"ts": "1727284648.198849", "text": "<@U04DKEFP1K8> FYI... band assignment might be not done I think as the payzone identifiers were missing", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1E2v5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " FYI... band assignment might be not done I think as the payzone identifiers were missing"}]}]}]}, {"ts": "1727284811.404999", "text": "Okay i ll check", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "E3MGE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Okay i ll check"}]}]}]}, {"ts": "1727284926.245469", "text": "The one data thing (I think it's a data thing) we should fix in test &amp; demo is the country, AR still showing everything as 0 because no employees there, I think?", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "EwMsd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The one data thing (I think it's a data thing) we should fix in test & demo is the country, AR still showing everything as 0 because no employees there, I think?"}]}]}]}, {"ts": "1727285032.167949", "text": "Does this also need to be hidden?", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07NUS9V45U", "created": 1727285025, "timestamp": 1727285025, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 101412, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NUS9V45U/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NUS9V45U/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_360.png", "thumb_360_w": 360, "thumb_360_h": 290, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_480.png", "thumb_480_w": 480, "thumb_480_h": 386, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_720.png", "thumb_720_w": 720, "thumb_720_h": 579, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_800.png", "thumb_800_w": 800, "thumb_800_h": 643, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_960.png", "thumb_960_w": 960, "thumb_960_h": 772, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 824, "original_w": 1124, "original_h": 904, "thumb_tiny": "AwAmADDSOccH9M0mT6/+O0ppOf7woATJ9T/3zRk+v/jtOz7/AK0fjQADpS03/gQp2R60AIaTHsPypaWgBCPQD8qMD0FLTdv+c0ALgegpcD0pAMGloAjmi82PbuxzmoBYgY/eH8qt0U7sVkVXsg7s3mEZJPSrKjaoHoMUtFFwsFFFFIZ//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NUS9V45U/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NUS9V45U-b86296dcab", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "m5ob3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Does this also need to be hidden?"}]}]}]}, {"ts": "1727285140.152719", "text": "These do not persist upon editing (should i put this feedback elsewhere?)", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07P40Q7L3E", "created": 1727285129, "timestamp": 1727285129, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 37677, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07P40Q7L3E/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07P40Q7L3E/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_360.png", "thumb_360_w": 360, "thumb_360_h": 101, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_480.png", "thumb_480_w": 480, "thumb_480_h": 134, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_720.png", "thumb_720_w": 720, "thumb_720_h": 202, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_800.png", "thumb_800_w": 800, "thumb_800_h": 224, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_960.png", "thumb_960_w": 960, "thumb_960_h": 269, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 287, "original_w": 1186, "original_h": 332, "thumb_tiny": "AwANADDSOc//AFqTn1/SlPWjmgAH1oz9aKMUAGfrRS0lAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07P40Q7L3E/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07P40Q7L3E-6993ddfb68", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "7ZEXI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "These do not persist upon editing (should i put this feedback elsewhere?)"}]}]}]}, {"ts": "1727285213.901829", "text": "<@U07EJ2LP44S> Please do  keep adding  ticket  under same epic.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727285213.901829", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "2GbFe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please do  keep adding  ticket  under same epic."}]}]}]}, {"ts": "1727285218.275969", "text": "This is what we were seeing earlier, it is still broken (cannot proceed through to next step on proration page)", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07NLUYFR8F", "created": 1727285194, "timestamp": 1727285194, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 215928, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NLUYFR8F/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NLUYFR8F/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_360.png", "thumb_360_w": 360, "thumb_360_h": 263, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_480.png", "thumb_480_w": 480, "thumb_480_h": 351, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_720.png", "thumb_720_w": 720, "thumb_720_h": 526, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_800.png", "thumb_800_w": 800, "thumb_800_h": 584, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_960.png", "thumb_960_w": 960, "thumb_960_h": 701, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 748, "original_w": 2056, "original_h": 1502, "thumb_tiny": "AwAjADDSOc96AOetHeloAKKTHNLQAUUmKWgBKMCmsyg4LKD7mnUAHTtRmjFIAc9KAFzS5pMc0gGKAEaKNzlkUn1Ip+AOlFFABRRRQAUUUUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NLUYFR8F/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NLUYFR8F-e8b33e9e38", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "aGEPV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is what we were seeing earlier, it is still broken (cannot proceed through to next step on proration page)"}]}]}]}, {"ts": "1727285254.254679", "text": "<@U07EJ2LP44S> test for scenario  NO.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727285254.254679", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "x4ocS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " test for scenario  NO."}]}]}]}, {"ts": "1727285553.840359", "text": "<!here> <PERSON> reponded and wants to meet tomorrow", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "I8Gfi", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " <PERSON> reponded and wants to meet tomorrow"}]}]}]}, {"ts": "1727285594.502109", "text": "<@U07EJ2LP44S> can we add their HR director Sara <mailto:<EMAIL>|<EMAIL>> to the call as well", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7kVk+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can we add their HR director Sara "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " to the call as well"}]}]}]}, {"ts": "1727285599.335099", "text": "already done", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Z559a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "already done"}]}]}]}, {"ts": "1727289079.728989", "text": "<@U07EJ2LP44S> For tomorrow's sdf call here are critical talking point around which you can prep the demo\n1. SSO support ( SAML with google) - ( Numerous reset password last time)\n2. Using the bulk edit feature + employee edit feature to allow sdf admins to update any data items without Stride team's support ( Numerous data update issue was requested last time)\n3. Data consistency in app + any downloadable reports\n4. Updated merit workflow ( they needed Managers to take greater responsibility in the process, new updates allows that)\n    a. Updated promotion workflow with improved custom job title support\n    b. Improved flags with addition of manual flags\n5. Org and People Insights ( <PERSON> had to prep hand made charts last time around)\n6. Improved pay positioning ( optional to bring it up)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1727289079.728989", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "8QOx0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " For tomorrow's sdf call here are critical talking point around which you can prep the demo\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SSO support ( SAML with google) - ( Numerous reset password last time)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Using the bulk edit feature + employee edit feature to allow sdf admins to update any data items without Stride team's support ( Numerous data update issue was requested last time)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Data consistency in app + any downloadable reports"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Updated merit workflow ( they needed Managers to take greater responsibility in the process, new updates allows that)"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Updated promotion workflow with improved custom job title support"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Improved flags with addition of manual flags"}]}], "style": "ordered", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>g and People Insights ( <PERSON> had to prep hand made charts last time around)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Improved pay positioning ( optional to bring it up)"}]}], "style": "ordered", "indent": 0, "offset": 4, "border": 0}]}]}, {"ts": "1727291414.736329", "text": "<@U04DKEFP1K8> as an admin, how do I made edits after I have reviewed and accepted a manager's recommendation? I am unable to do it in the demo env", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727291414.736329", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "OYZpl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " as an admin, how do I made edits after I have reviewed and accepted a manager's recommendation? I am unable to do it in the demo env"}]}]}]}, {"ts": "1727298055.895689", "text": "<@U07EJ2LP44S> I’ve completed a sanity check for the sdf-test, and the environment looks good to proceed with tomorrow’s demo. However, there are a few issues with the cycle builder that will need to be addressed (we should avoid showcasing these during the demo).\n1. additional planning level <https://compiify.atlassian.net/browse/COM-3642>\n2. proration <https://compiify.atlassian.net/browse/COM-3645>\n3. dates for cycle <https://compiify.atlassian.net/browse/COM-3647>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13703::aa6f4271af9f41d59c6fddd9a650acaf", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3642?atlOrigin=eyJpIjoiZDNhZmQ2Yjg1MTI4NGRjZDljMmQ5ZjQ5MGQyZTY4ZWIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3642 Cycle edit failure on sdf-test [9/25 PT testing] on including addit…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13703::a46afa83dd784e77b4c2620ee2ab60a7", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13703\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3642\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3642", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:13706::c5ce9be1d17745dc9871b7c89d3a0b1c", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3645?atlOrigin=eyJpIjoiNWI5MzE1MmYzZTFjNGQ2YzgzNGIwZWViOTI0ZjUwZmQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3645 When editing a cycle using proration, cannot move on from proration…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13706::b0d8660e51ff48ffbacb12e4fc99981e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13706\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3645\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3645", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 3, "blocks": [{"type": "section", "block_id": "uf:ih:13708::f37c8e7c38ca41c3a5c86a20e7d25e4d", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3647?atlOrigin=eyJpIjoiNDdhMmU4NzFlN2MyNDZkZGE1OWFkNzgxNWU3ODMzNmQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3647 Should allow a past date when setting up a cycle>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13708::1abe5cd9ded149b282c4ab9df101f16e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13708\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3647\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3647", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "omvCJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I’ve completed a sanity check for the sdf-test, and the environment looks good to proceed with tomorrow’s demo. However, there are a few issues with the cycle builder that will need to be addressed (we should avoid showcasing these during the demo).\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "additional planning level "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3642"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "proration "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3645"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "dates for cycle "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3647"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1727302285.147919", "text": "<@U07EJ2LP44S> are you working on cleaning up the doc \"Compiify Improvements for SDF\" for tomorrow's call or is it <@U04DKEFP1K8>?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "aA3K5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " are you working on cleaning up the doc \"Compiify Improvements for SDF\" for tomorrow's call or is it "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1727302543.579549", "text": "I have not, but I can work on it in the morning if need be. Basically updating the right column?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0obFD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have not, but I can work on it in the morning if need be. Basically updating the right column?"}]}]}]}, {"ts": "1727303346.716499", "text": "yes, thanks", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xoxCv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes, thanks"}]}]}]}, {"ts": "1727303433.925219", "text": "<@U07EJ2LP44S> you are driving the sdf demo tomorrow?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1727303433.925219", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "dHXU/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " you are driving the sdf demo tomorrow?"}]}]}]}, {"ts": "1727360658.244279", "text": "I have impersonated <PERSON> in SDF Test. She is a manager and not a cycle admin. Shouldn't comp builder be hidden?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727360658.244279", "reply_count": 5, "files": [{"id": "F07PKMASZMF", "created": 1727360635, "timestamp": 1727360635, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 142179, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07PKMASZMF/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07PKMASZMF/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_360.png", "thumb_360_w": 360, "thumb_360_h": 306, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_480.png", "thumb_480_w": 480, "thumb_480_h": 408, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_720.png", "thumb_720_w": 720, "thumb_720_h": 611, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_800.png", "thumb_800_w": 800, "thumb_800_h": 679, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_960.png", "thumb_960_w": 960, "thumb_960_h": 815, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 870, "original_w": 1420, "original_h": 1206, "thumb_tiny": "AwAoADDQKsTw5H4UoBC4JyfWmhlZmAxkHninjPt+FMBAn+0350qjAxkn60mfY0Z570gHUUmaWgBvOT16+tLRgelFAACfSgE+n60cjtQfpQAUtIKWgBKKD0NB6GgBQPSkwKUdBRQAYooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07PKMASZMF/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07PKMASZMF-945966ce03", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "87+ob", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have impersonated <PERSON> in SDF Test. She is a manager and not a cycle admin. Shouldn't comp builder be hidden?"}]}]}]}, {"ts": "1727362915.039209", "text": "<@U04DKEFP1K8> Issues fixed today\n• <https://compiify.atlassian.net/browse/COM-3642>\n• <https://compiify.atlassian.net/browse/COM-3628>\n• <https://compiify.atlassian.net/browse/COM-3629>\n• <https://compiify.atlassian.net/browse/COM-3643>\n• <https://compiify.atlassian.net/browse/COM-3644>\n• <https://compiify.atlassian.net/browse/COM-3645>\n• <https://compiify.atlassian.net/browse/COM-3647>\n", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727362915.039209", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "AoREl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Issues fixed today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3642"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3628"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3629"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3643"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3644"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3645"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3647"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1727363005.781369", "text": "Couple more couldn't reproduce or not issues\n• <https://compiify.atlassian.net/browse/COM-3632>\n• <https://compiify.atlassian.net/browse/COM-3646>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VRACw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Couple more couldn't reproduce or not issues\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3632"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3646"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1727363196.811739", "text": "There two new features released recently\n• Hide self in merit planning and reports for super admins\n• HRBP role", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1727363208.000000"}, "blocks": [{"type": "rich_text", "block_id": "fgm7W", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There two new features released recently\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hide self in merit planning and reports for super admins"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "HRBP role"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1727363238.594889", "text": "Base currency is ready for merging. Will do tomorrow as it would impact SDF meeting.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "raDyE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Base currency is ready for merging. Will do tomorrow as it would impact SDF meeting."}]}]}]}, {"ts": "1727365331.482219", "text": "<PERSON> is pushing LOLOL", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "E9PVn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> is pushing LOLOL"}]}]}]}, {"ts": "1727365351.369639", "text": "She said 915 on monday", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ooK6a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She said 915 on monday"}]}]}]}, {"ts": "1727365368.827979", "text": "^^ <@U07M6QKHUC9> <@U04DKEFP1K8>", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oBZUF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "^^ "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1727366176.489339", "text": "<!channel> ^", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ew51r", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "channel"}, {"type": "text", "text": " ^"}]}]}]}, {"ts": "1727366316.251759", "text": "Lol. Will give us some more time to push the last minute changes we discussed in the call.\n<@U0690EB5JE5> <@U04DKEFP1K8> FYA", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ze2yE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON>. Will give us some more time to push the last minute changes we discussed in the call.\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " FYA"}]}]}]}, {"ts": "1727366401.614109", "text": "<!here> Do we want to do the demo data stories now?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BtgkA", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Do we want to do the demo data stories now?"}]}]}]}, {"ts": "1727366555.068269", "text": "I’d like to grab some food but then yes", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NxQEj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I’d like to grab some food but then yes"}]}]}]}, {"ts": "1727366608.208529", "text": "okay", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "cixRH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "okay"}]}]}]}, {"ts": "1727366633.510289", "text": "Let's meet at 9:30 <@U07EJ2LP44S> <@U07NBMXTL1E> <@U04DKEFP1K8>", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "L9gNI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let's meet at 9:30 "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1727383120.772719", "text": "<@U07EJ2LP44S> It looks like <PERSON>gen<PERSON><PERSON><PERSON> has been waiting for a reply from you.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727383120.772719", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "RJYYU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " It looks like <PERSON><PERSON><PERSON><PERSON><PERSON> has been waiting for a reply from you."}]}]}]}, {"ts": "1727386788.693989", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> For some reasons, 3 of the charts are not showing up in demo env. Is it because of the data upload issues?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727386788.693989", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "sP+7q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " For some reasons, 3 of the charts are not showing up in demo env. Is it because of the data upload issues?"}]}]}]}, {"ts": "1727404512.622239", "text": "Just a heads up to everyone, this hurricane is looking pretty bad and our area is expecting some pretty strong winds, tornadoes, and a lot of rain overnight and tomorrow morning. We will be fine, but we may lose power and I may have issues getting online tomorrow. It could be fine, but just a heads up. Importantly, though, we are fully prepared to brew coffee with no power. :rolling_on_the_floor_laughing: ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727404512.622239", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "LddVr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just a heads up to everyone, this hurricane is looking pretty bad and our area is expecting some pretty strong winds, tornadoes, and a lot of rain overnight and tomorrow morning. We will be fine, but we may lose power and I may have issues getting online tomorrow. It could be fine, but just a heads up. Importantly, though, we are fully prepared to brew coffee with no power. "}, {"type": "emoji", "name": "rolling_on_the_floor_laughing", "unicode": "1f923"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1727407377.430219", "text": "<@U04DKEFP1K8> not sure if you were in the demo data meeting today when we we identified the issues around filters and shifting of columns, not persisting after refreshing the screen. Are you able to explain that to <PERSON><PERSON><PERSON> during your call tonight or let me know I can join the call for five minutes to explain those issues", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727407377.430219", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "ITzxR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " not sure if you were in the demo data meeting today when we we identified the issues around filters and shifting of columns, not persisting after refreshing the screen. Are you able to explain that to <PERSON><PERSON><PERSON> during your call tonight or let me know I can join the call for five minutes to explain those issues"}]}]}]}, {"ts": "1727450876.106889", "text": "You may want to use a different link for the call in 30. I don’t think I’m going to be available yet.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727450876.106889", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "TGkTh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "You may want to use a different link for the call in 30. I don’t think I’m going to be available yet."}]}]}]}, {"ts": "1727452344.736629", "text": "<@U04DKEFP1K8> org view is still not showing charts. Do you know if <PERSON> was able to make the data changes (locations, name change for marketing) for the demo data?", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1727452432.000000"}, "blocks": [{"type": "rich_text", "block_id": "fvOPG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " org view is still not showing charts. Do you know if <PERSON> was able to make the data changes (locations, name change for marketing) for the demo data?"}]}]}]}, {"ts": "1727452546.941169", "text": "<!here> Let's use this zoom link for the leadership meeting\n<https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZfBlD", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Let's use this zoom link for the leadership meeting\n"}, {"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1727452619.360889", "text": "stay safe and take care <@U07EJ2LP44S>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727404512.622239", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "3/O+y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "stay safe and take care "}, {"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1727452856.321269", "text": "<@U04DKEFP1K8> Issues fixed today. all of them are issues raised by <PERSON>\n<https://compiify.atlassian.net/browse/COM-3649>,\n<https://compiify.atlassian.net/browse/COM-3654>,\n<https://compiify.atlassian.net/browse/COM-3653>,\n<https://compiify.atlassian.net/browse/COM-3651>\nAlso there was a fix pushed for Bands issue\nIts both data and code bugs. Please follow the instructions in <https://github.com/Compiify/Yellowstone/pull/1586|PR> to fix the issue", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727452856.321269", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "1727456782.000000"}, "blocks": [{"type": "rich_text", "block_id": "8lULl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Issues fixed today. all of them are issues raised by <PERSON>\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3649"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3654"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3653"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3651"}, {"type": "text", "text": "\nAlso there was a fix pushed for Bands issue\nIts both data and code bugs. Please follow the instructions in "}, {"type": "link", "url": "https://github.com/Compiify/Yellowstone/pull/1586", "text": "PR"}, {"type": "text", "text": " to fix the issue"}]}]}]}, {"ts": "1727453639.934719", "text": "Responsive\t599 (LI)\tCall #1 Scheduled\tw/ <PERSON><PERSON>\t                                        Call #1 - <PERSON><PERSON> 10/1 @ 2p\nAlfaTech\t        370 (LI)\tCall #1 Scheduled\tw/ <PERSON>\t                                Call #1 - <PERSON><PERSON><PERSON> 10/10 @ 3p\nFord Direct\t402 (LI)\tCall #1 Scheduled\tw/ <PERSON><PERSON><PERSON>, <PERSON><PERSON>\t        Call #1 - Mon 9/30 @ 11a\nFinanceIt\t318 (LI)\tCall #1 Scheduled\tw/ <PERSON><PERSON><PERSON>, <PERSON>\t        Call #1 - Fri 10/18 @ 2p", "user": "U07NBMXTL1E", "type": "message", "edited": {"user": "U07NBMXTL1E", "ts": "1727454525.000000"}, "blocks": [{"type": "rich_text", "block_id": "Vx+78", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Responsive\t599 (LI)\tCall #1 Scheduled\tw/ <PERSON><PERSON>\t                                        Call #1 - <PERSON><PERSON> 10/1 @ 2p\nAlfaTech\t        370 (LI)\tCall #1 Scheduled\tw/ <PERSON>\t                                Call #1 - <PERSON><PERSON><PERSON> 10/10 @ 3p\nFord Direct\t402 (LI)\tCall #1 Scheduled\tw/ <PERSON><PERSON><PERSON>, <PERSON><PERSON>\t        Call #1 - Mon 9/30 @ 11a\nFinanceIt\t318 (LI)\tCall #1 Scheduled\tw/ <PERSON><PERSON><PERSON>, <PERSON>\t        Call #1 - Fri 10/18 @ 2p"}]}]}]}, {"ts": "1727456732.095529", "text": "Just a quick update on me, I think the worst has passed, but we’re still under tornado watch and we have some severe storms running through. I don’t think I’m gonna be able to get anything done today with all the drama with the weather.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "SxFzJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just a quick update on me, I think the worst has passed, but we’re still under tornado watch and we have some severe storms running through. I don’t think I’m gonna be able to get anything done today with all the drama with the weather."}]}]}]}, {"ts": "1727461693.126129", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> can we pls respond to Valgenesis by EOD today? Our goal is to respond to customers within 4 hours but not later than 24 hours especially when we are claiming white glove support to our early customers.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727461693.126129", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "/4gPq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can we pls respond to Valgenesis by EOD today? Our goal is to respond to customers within 4 hours but not later than 24 hours especially when we are claiming white glove support to our early customers."}]}]}]}, {"ts": "1727704780.224439", "text": "I have an event to attend. I might join half an hour late or miss the meeting ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727704780.224439", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "phqcf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have an event to attend"}, {"type": "text", "text": "."}, {"type": "text", "text": " I might join half an hour late or miss the meeting "}]}]}]}, {"ts": "1727705616.166099", "text": "<@U04DKEFP1K8> will sync up with you on the issues once I am back in case I miss meeting. ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7tUtR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " will sync up with you on the issues once I am back in case I miss meeting. "}]}]}]}, {"ts": "**********.103799", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> it looks like demo account is still not fixed. we have a demo in 45 mins", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NExDE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " it looks like demo account is still not fixed. we have a demo in 45 mins"}]}]}]}, {"ts": "**********.641289", "text": "can you pls add <PERSON> to <http://test.stridehr.io|test.stridehr.io>. account", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gTsuI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "can you pls add <PERSON> to "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": ". account"}]}]}]}, {"ts": "**********.654939", "text": "<@U07M6QKHUC9> I am outside ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "J/m73", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I am outside "}]}]}]}, {"ts": "**********.292709", "text": "I will see what I can do", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "l0N2S", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will see what I can do"}]}]}]}, {"ts": "**********.158669", "text": "I have access to the test account for now, so should be good for the demo in 40 minutes", "user": "U07NBMXTL1E", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OGwrf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have access to the test account for now, so should be good for the demo in 40 minutes"}]}]}]}, {"ts": "**********.654799", "text": "<PERSON> helped me set it up weeks ago", "user": "U07NBMXTL1E", "type": "message", "blocks": [{"type": "rich_text", "block_id": "UJcHQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> helped me set it up weeks ago"}]}]}]}, {"ts": "**********.241129", "text": "Ok cool", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZAFgG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok cool"}]}]}]}, {"ts": "**********.228039", "text": "<@U0690EB5JE5> one of our prospects is using Success Factors. Do you anticipate any issues in pulling the data from them? I think Merge does support SuccessFactors", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.228039", "reply_count": 5, "edited": {"user": "U07M6QKHUC9", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "W0bOu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " one of our prospects is using Success Factors. Do you anticipate any issues in pulling the data from them? I think <PERSON><PERSON> does support SuccessFactors"}]}]}]}, {"ts": "1727716269.595459", "text": "<@U04DKEFP1K8> in the SDF test env, not sure why the flag was showing for promotions use case when there was no promotion recommendations. Is that a bug? If so, we should fix it before handing over the test env to them?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727716269.595459", "reply_count": 1, "edited": {"user": "U07M6QKHUC9", "ts": "1727716287.000000"}, "blocks": [{"type": "rich_text", "block_id": "KzHpG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " in the SDF test env, not sure why the flag was showing for promotions use case when there was no promotion recommendations. Is that a bug? If so, we should fix it before handing over the test env to them?"}]}]}]}, {"ts": "1727716585.102029", "text": "<!here> these are the latest integration supported by merge <https://docs.merge.dev/integrations/hris/overview/> and during any of our demo calls, we should confidently confirm our support for these integrations.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1727716585.102029", "reply_count": 2, "attachments": [{"from_url": "https://docs.merge.dev/integrations/hris/overview/", "service_icon": "https://docs.merge.dev/icons/icon-48x48.png?v=01743f138ae8249f0cb7bed86fae3c90", "thumb_url": "https://docs.merge.dev/img/open-graph/merge-docs.jpg", "thumb_width": 2400, "thumb_height": 1256, "id": 1, "original_url": "https://docs.merge.dev/integrations/hris/overview/", "fallback": "<PERSON><PERSON>", "text": "Learn how to add Merge to your product.", "title": "<PERSON><PERSON>", "title_link": "https://docs.merge.dev/integrations/hris/overview/", "service_name": "docs.merge.dev"}], "blocks": [{"type": "rich_text", "block_id": "FNxgX", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " these are the latest integration supported by merge "}, {"type": "link", "url": "https://docs.merge.dev/integrations/hris/overview/"}, {"type": "text", "text": " and during any of our demo calls, we should confidently confirm our support for these integrations."}]}]}]}, {"ts": "1727717230.491289", "text": "<@U07NBMXTL1E> Huge kudos to you for doing an impressive demo for FordDirect. Your attention to details and the overall talk track is really good. Great job ramping up so quickly and becoming self sufficient with the demo.\n<@U07EJ2LP44S> Great work in helping <PERSON> with getting up to speed on the demo.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727717230.491289", "reply_count": 1, "reactions": [{"name": "tada", "users": ["U04DKEFP1K8", "U07EJ2LP44S", "U0690EB5JE5"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "+9XR2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " Huge kudos to you for doing an impressive demo for FordDirect. Your attention to details and the overall talk track is really good. Great job ramping up so quickly and becoming self sufficient with the demo.\n"}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Great work in helping <PERSON> with getting up to speed on the demo."}]}]}]}, {"ts": "1727719089.536929", "text": "<@U07NBMXTL1E> are you done with demo environment for the day?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1727719089.536929", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "OauHr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " are you done with demo environment for the day?"}]}]}]}, {"ts": "1727719810.731689", "text": "I think we also need to give ourselves a hand for the SDF call; I thought it went pretty darn well! Especially if she wants to invite others into the environment to see it. <@U07M6QKHUC9> <@U04DKEFP1K8>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727719810.731689", "reply_count": 1, "reactions": [{"name": "grinning", "users": ["U04DKEFP1K8"], "count": 1}, {"name": "100", "users": ["U07M6QKHUC9"], "count": 1}, {"name": "moneybag", "users": ["U07M6QKHUC9"], "count": 1}, {"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "GKLsM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think we also need to give ourselves a hand for the SDF call; I thought it went pretty darn well! Especially if she wants to invite others into the environment to see it. "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}]}