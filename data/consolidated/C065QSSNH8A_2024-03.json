{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2024-03", "message_count": 92, "messages": [{"ts": "1709237074.259649", "text": "Quick update on DA: We're going to reset their environment and have <PERSON> start with a fresh cycle.\n\nSome of the issues he encountered so far:\n• Proration didn't match what they'd set up in HiBob - most likely, they miscommunicated their expectation because they aren't using proration at all. \n• The hierarchy made merit planning harder than it needed to be - we're going to roll up teams under leaders, more similar to SDF\n• Currency conversions were not completely matching between Compiify and HiBob -- we can solve this with a higher precision conversion rate\n• <PERSON> hit a persistent bug where employees could not be \"Reviewed\" or \"Approved\", either in impersonation or in Super Admin mode, and we couldn't resolve this without a code push\nWe're aiming to have this reset for him by tomorrow morning. :cat-roomba:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709237074.259649", "reply_count": 8, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "DCeVf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Quick update on DA: We're going to reset their environment and have <PERSON> start with a fresh cycle.\n\nSome of the issues he encountered so far:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Proration didn't match what they'd set up in HiBob - most likely, they miscommunicated their expectation because they aren't using proration at all. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The hierarchy made merit planning harder than it needed to be - we're going to roll up teams under leaders, more similar to SDF"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Currency conversions were not completely matching between Compiify and HiBob -- we can solve this with a higher precision conversion rate"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> hit a persistent bug where employees could not be \"Reviewed\" or \"Approved\", either in impersonation or in Super Admin mode, and we couldn't resolve this without a code push"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nWe're aiming to have this reset for him by tomorrow morning. "}, {"type": "emoji", "name": "cat-roomba"}]}]}]}, {"ts": "1709240451.521939", "text": "<@U04DKEFP1K8> For <PERSON>'s issues today - do we have an internal \"audit trail\" view (or DB entries) that we can access to figure out what happened with <PERSON>?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709240451.521939", "reply_count": 41, "blocks": [{"type": "rich_text", "block_id": "bVWX2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For <PERSON>'s issues today - do we have an internal \"audit trail\" view (or DB entries) that we can access to figure out what happened with <PERSON>?"}]}]}]}, {"ts": "1709250260.053769", "text": "<@U04DKEFP1K8> can you send over the two stride domains we purchased through aws?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1709250260.053769", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "Mj7w6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you send over the two stride domains we purchased through aws?"}]}]}]}, {"ts": "1709250512.773069", "text": "<!here> <http://stridenow.ai|stridenow.ai>.       <http://stridenow.io|stridenow.io>.      <http://stridehere.com|stridehere.com>\n\nwhich one do we want to pick? do you have any strong preference or strong NO GO? If we are confident about using ai then we should do <http://stridenow.ai|stridenow.ai>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "thinking_face", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "+zMIY", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://stridenow.ai", "text": "stridenow.ai"}, {"type": "text", "text": ".       "}, {"type": "link", "url": "http://stridenow.io", "text": "stridenow.io"}, {"type": "text", "text": ".      "}, {"type": "link", "url": "http://stridehere.com", "text": "stridehere.com"}, {"type": "text", "text": "\n\nwhich one do we want to pick? do you have any strong preference or strong NO GO? If we are confident about using ai then we should do "}, {"type": "link", "url": "http://stridenow.ai", "text": "stridenow.ai"}]}]}]}, {"ts": "1709271898.533129", "text": "<@U04DKEFP1K8> Updated the <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities> list, with the DA blockers as the highest priority.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709271898.533129", "reply_count": 2, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "+pNck", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Updated the "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities"}, {"type": "text", "text": " list, with the DA blockers as the highest priority."}]}]}]}, {"ts": "1709320015.731009", "text": "<@U065H3M6WJV> i am putting fixes to unblock DA now on qa and sdf-test now. Let's sync up when they are deployed", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "45B3Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " i am putting fixes to unblock DA now on qa and sdf-test now. Let's sync up when they are deployed"}]}]}]}, {"ts": "1709320127.703369", "text": "Ok. I have to leave around 11:30 but lmk if there’s anything to review before then", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "uujBH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. I have to leave around 11:30 but lmk if "}, {"type": "text", "text": "there’s"}, {"type": "text", "text": " anything to review before then"}]}]}]}, {"ts": "1709320173.650479", "text": "lets connect now in that case", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VRMae", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "lets connect now in that case"}]}]}]}, {"ts": "1709320182.013719", "text": "saurab<PERSON>.jain has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R06MLTKQRE0", "block_id": "OUXI1", "api_decoration_available": false, "call": {"v1": {"id": "R06MLTKQRE0", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1709320181, "active_participants": [], "all_participants": [{"slack_id": "U04DKEFP1K8"}, {"slack_id": "U065H3M6WJV"}], "display_id": "892-8873-6620", "join_url": "https://us06web.zoom.us/j/89288736620?pwd=ULCnoTQmfaqyemP1voq9Za3XfkQHRm.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEX2YyNTA0OTVmMmM4YjRlNjJiYzViZWU1MWNiMjdjMTcwJnVzcz1QdmxsdzVHcW0taEpya2tvLUstcFh2Z3FtaGpnRzFCSm1CM3ZNMHN6MExQLXlXSjI2Ukg1N1pIejFHWVMzcGZfQTJFbGxNSVlOTDJwUmxCM2p2Z3FvbEJzMm5EOWo1aGxkV0sxeFN3Llp5ZzB2ZWNKVk5pb1ZHNm8%3D&action=join&confno=89288736620&pwd=ULCnoTQmfaqyemP1voq9Za3XfkQHRm.1", "name": "Zoom meeting started by <PERSON><PERSON><PERSON><PERSON>", "created_by": "U05185RFCNT", "date_end": 1709321201, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "+lpGX", "text": {"type": "mrkdwn", "text": "Meeting passcode: ULCnoTQmfaqyemP1voq9Za3XfkQHRm.1", "verbatim": false}}]}, {"ts": "1709320430.161879", "text": "Sorry just saw, be right there", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "V+Fzw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sorry just saw, be right there"}]}]}]}, {"ts": "1709341219.947659", "text": "After <PERSON> asked some questions about the charts on the Organization tab, I decided to do a deeper dive to figure out which charts seem to be using the live data and which ones don't match. In the Reports section, there's some obvious discrepancies in averages depending on the chart, so something doesn't fully align there.\n\nStarting to capture the details in <https://docs.google.com/spreadsheets/d/1XVqH5s7IeAhpUf7Nn9gwLIkLgFox4VgJRZeg0l7PVH8/edit#gid=1319426088|this spreadsheet>. (This should be less of an issue when we're ready with newer insights, but I think it'll help me understand which data sources seem to be working correctly vs. not.)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709341219.947659", "reply_count": 1, "reactions": [{"name": "rocket", "users": ["U04DS2MBWP4", "U0690EB5JE5"], "count": 2}], "files": [{"id": "F06MKS3LEQ2", "created": 1709341208, "timestamp": 1709341208, "name": "Screenshot 2024-03-01 at 4.56.22 PM.png", "title": "Screenshot 2024-03-01 at 4.56.22 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 453564, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06MKS3LEQ2/screenshot_2024-03-01_at_4.56.22___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06MKS3LEQ2/download/screenshot_2024-03-01_at_4.56.22___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 194, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 259, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 388, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 432, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 518, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 552, "original_w": 3700, "original_h": 1996, "thumb_tiny": "AwAZADDTpglUsRg4BxnHGadTfLXdu984zxmgB2KPypabtU9u+fxoAdmk70gRR0HfNKAB0oAKaC/OVX2wafRQA0b/ADDnG3HFBLDOADzTqKAG5b+739e1KCeMjH40UtAH/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06MKS3LEQ2/screenshot_2024-03-01_at_4.56.22___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06MKS3LEQ2-fea068c6d0", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06MGUE8QUD", "created": 1709341211, "timestamp": 1709341211, "name": "Screenshot 2024-03-01 at 4.56.35 PM.png", "title": "Screenshot 2024-03-01 at 4.56.35 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 390846, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06MGUE8QUD/screenshot_2024-03-01_at_4.56.35___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06MGUE8QUD/download/screenshot_2024-03-01_at_4.56.35___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 213, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 285, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 427, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 474, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 569, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 607, "original_w": 3674, "original_h": 2178, "thumb_tiny": "AwAcADDTqMSqWK4OAcZxxmn03yl3bueucZ4z60AOxRS00qDnI60AOopu0HPvS7R6d80AISFznPrQXAz149qWloAaGBOOeuKdRRQAUUUUAf/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06MGUE8QUD/screenshot_2024-03-01_at_4.56.35___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06MGUE8QUD-a3596144d5", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06MKNYASLT", "mode": "tombstone"}], "blocks": [{"type": "rich_text", "block_id": "znYOW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "After <PERSON> asked some questions about the charts on the Organization tab, I decided to do a deeper dive to figure out which charts seem to be using the live data and which ones don't match. In the Reports section, there's some obvious discrepancies in averages depending on the chart, so something doesn't fully align there.\n\nStarting to capture the details in "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1XVqH5s7IeAhpUf7Nn9gwLIkLgFox4VgJRZeg0l7PVH8/edit#gid=1319426088", "text": "this spreadsheet"}, {"type": "text", "text": ". (This should be less of an issue when we're ready with newer insights, but I think it'll help me understand which data sources seem to be working correctly vs. not.)"}]}]}]}, {"ts": "1709398105.735089", "text": "<@U065H3M6WJV> when do you plan to be ready for regrouping to discuss the \"What\" of next 2 months?\n<@U04DKEFP1K8> <@U0690EB5JE5> Looking forward to hearing your thoughts on the action plan/next steps as discussed in \"tech regroup\" meeting last week. Are we still on for <PERSON><PERSON> for that?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1709398105.735089", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Cx3Ms", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " when do you plan to be ready for regrouping to discuss the \"What\" of next 2 months?\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Looking forward to hearing your thoughts on the action plan/next steps as discussed in \"tech regroup\" meeting last week. Are we still on for <PERSON><PERSON> for that?"}]}]}]}, {"ts": "1709407468.036369", "text": "In addition to the bigger tech questions, we still have some live customers we need to stay \"in stride\" with. <@U04DKEFP1K8> who is on point for each of these?\n• *SDF* - Adjustment letters \n    ◦ Ensure the logo is correctly displayed / not a broken link when generating\n    ◦ Identify plan for incorporating Lumen grant amounts if SDF provides these (should be ~13 employees total with a new grant)\n• *DA* - Adjustment letters\n    ◦ Ensure the format &amp; logo match what they requested\n• *Nauto* - Google social login\n    ◦ Need to test this before releasing to customers\n    ◦ Should allow admins like Delaney &amp; Renato to enable other employees without our intervention", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709407468.036369", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "RBL12", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In addition to the bigger tech questions, we still have some live customers we need to stay \"in stride\" with. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " who is on point for each of these?\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF", "style": {"bold": true}}, {"type": "text", "text": " - Adjustment letters "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ensure the logo is correctly displayed / not a broken link when generating"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Identify plan for incorporating Lumen grant amounts if SDF provides these (should be ~13 employees total with a new grant)"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA ", "style": {"bold": true}}, {"type": "text", "text": "- Adjustment letters"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ensure the format & logo match what they requested"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON> ", "style": {"bold": true}}, {"type": "text", "text": "- Google social login"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Need to test this before releasing to customers"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should allow admins like Delaney & Renato to enable other employees without our intervention"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}, {"ts": "1709424222.753689", "text": "<@U065H3M6WJV> <PERSON><PERSON>'s data update request is complete", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "gratitude-thank-you", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "NpFAI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Katy<PERSON>'s data update request is complete"}]}]}]}, {"ts": "1709426186.441359", "text": "<@U065H3M6WJV> Nauto's equity data is uploaded in test environment. We will need to add enhancement to allow manual upload of equity vesting data for an employee ( currently we use automated calculation for the same)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1709426186.441359", "reply_count": 1, "edited": {"user": "U04DKEFP1K8", "ts": "1709429439.000000"}, "blocks": [{"type": "rich_text", "block_id": "SLkuY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Nauto's equity data is uploaded in test environment. We will need to add enhancement to allow manual upload of equity vesting data for an employee ( currently we use automated calculation for the same)"}]}]}]}, {"ts": "1709431346.908089", "text": "<@U065H3M6WJV> social login are enabled on <http://nauto-test.compiify.com|nauto-test.compiify.com>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1709431346.908089", "reply_count": 15, "blocks": [{"type": "rich_text", "block_id": "J55ja", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " social login are enabled on "}, {"type": "link", "url": "http://nauto-test.compiify.com", "text": "nauto-test.compiify.com"}]}]}]}, {"ts": "1709586014.774699", "text": "<@U065H3M6WJV> here is analysis of why <PERSON><PERSON> is unable to view correct band for <PERSON><PERSON>\nThis is the current and next band details available for <PERSON><PERSON>\nInstead of selecting the next band which is available for <PERSON><PERSON> , she is adding a custom job title for <PERSON><PERSON>. When we add a custom job title we do not know wheat is going to be new range for it so we default to existing range itself.\nIf she selects the L4 level she has provided in her salary bands she will she the right range", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1709586014.774699", "reply_count": 1, "files": [{"id": "F06N7T6RDR7", "created": 1709585882, "timestamp": 1709585882, "name": "Screenshot 2024-03-04 at 12.55.43 PM.png", "title": "Screenshot 2024-03-04 at 12.55.43 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 61346, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06N7T6RDR7/screenshot_2024-03-04_at_12.55.43___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06N7T6RDR7/download/screenshot_2024-03-04_at_12.55.43___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 20, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 26, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 39, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 44, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 52, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 56, "original_w": 2900, "original_h": 158, "thumb_tiny": "AwACADDSP9aQdaU/1pB1NAA3am55pzdqZ3oAe3SkP3FpW6Uh+4tZz/QZ/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06N7T6RDR7/screenshot_2024-03-04_at_12.55.43___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06N7T6RDR7-71f3763cb4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ACnfg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " here is analysis of why <PERSON><PERSON> is unable to view correct band for <PERSON><PERSON>\nThis is the current and next band details available for <PERSON><PERSON>\nInstead of selecting the next band which is available for <PERSON><PERSON> , she is adding a custom job title for <PERSON><PERSON>. When we add a custom job title we do not know wheat is going to be new range for it so we default to existing range itself.\nIf she selects the L4 level she has provided in her salary bands she will she the right range"}]}]}]}, {"ts": "1709587935.415969", "text": "Hey <@U04DKEFP1K8> I'm trying to figure out why the \"new\" (after cycle) salary band &amp; compa ratio for \"Blair Zhu\" seems to be wrong in the downloadable \"Post Merit Roster\" report, because I think I reported something incorrectly to <PERSON> as a result.\n\nAre promotions not necessarily reflected in this report before things are finalized? Or any other reason why the \"New_Salary_Max\" is lower than \"Old_Salary_Max\", and the New_Compensation_Ratio is ~1.36 when it should be 1.05?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1ysCw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I'm trying to figure out why the \"new\" (after cycle) salary band & compa ratio for \"Blair Zhu\" seems to be wrong in the downloadable \"Post Merit Roster\" report, because I think I reported something incorrectly to <PERSON> as a result.\n\nAre promotions not necessarily reflected in this report before things are finalized? Or any other reason why the \"New_Salary_Max\" is lower than \"Old_Salary_Max\", and the New_Compensation_Ratio is ~1.36 when it should be 1.05?"}]}]}]}, {"ts": "1709588011.507719", "text": "Let me get back on this at 2 pm( wrapping up lunch)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Vx+NH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me get back on this at 2 pm( wrapping up lunch)"}]}]}]}, {"ts": "1709590464.384139", "text": "The issue with <PERSON> zhu is the same as one with <PERSON><PERSON> where <PERSON><PERSON> has not selected the next band provided per salary but created a new job title\n\nIf she would have selected pre-existing next level band per the salary range , system would have calculated <PERSON>'s new comp ratio correctly\n\n$154980 / $147500 ( 1.05)\n\nCurrently she is using a promoted job title \"Sr Growth Marketing Manager\" with a Salary Target of $113400 CAD  and hence system is reporting $154980 / $113400 = 1.3666", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8x4qR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The issue with <PERSON> zhu is the same as one with <PERSON><PERSON> where <PERSON><PERSON> has not selected the next band provided per salary but created a new job title\n\nIf she would have selected pre-existing next level band per the salary range , system would have calculated <PERSON>'s new comp ratio correctly\n\n$154980 / $147500 ( 1.05)\n\nCurrently she is using a promoted job title \"Sr Growth Marketing Manager\" with a Salary Target of $113400 CAD  and hence system is reporting $154980 / $113400 = 1.3666"}]}]}]}, {"ts": "1709590552.979249", "text": "for the promoted employees <PERSON><PERSON> has created a custom job title whenever she did not found a title which she wants to assisgn and that is creating the issues", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oIN13", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "for the promoted employees <PERSON><PERSON> has created a custom job title whenever she did not found a title which she wants to assisgn and that is creating the issues"}]}]}]}, {"ts": "1709590586.840839", "text": "Ahhhh sorry, I missed that one too. Thank you for explaining <@U04DKEFP1K8>.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Oy71+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ahhhh sorry, I missed that one too. Thank you for explaining "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1709590624.240619", "text": "I have been looking mostly at the CSV output so sometimes forget to look at how it shows in the actual Merit view / how to figure out it's a nonstandard title :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vXZt3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have been looking mostly at the CSV output so sometimes forget to look at how it shows in the actual Merit view / how to figure out it's a nonstandard title "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1709590799.173499", "text": "Yes it is easier to sort it out in database table, also she should really use the custom job title if she is sure she has not provided us the band she wats to use", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8g8RO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes it is easier to sort it out in database table, also she should really use the custom job title if she is sure she has not provided us the band she wats to use"}]}]}]}, {"ts": "1709590860.801069", "text": "Here is the document which explain how to connect to production db <https://docs.google.com/document/d/1TPOQshkYQ6spka0RwC4j1B_-3u7RAxuwjhsnpVGb9MI/edit>", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "VwnmO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the document which explain how to connect to production db "}, {"type": "link", "url": "https://docs.google.com/document/d/1TPOQshkYQ6spka0RwC4j1B_-3u7RAxuwjhsnpVGb9MI/edit"}]}]}]}, {"ts": "1709670316.581549", "text": "Is anything underway that would affect Staging environment? I'm suddenly having issues with login.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709670316.581549", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "sMLAl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is anything underway that would affect Staging environment? I'm suddenly having issues with login."}]}]}]}, {"ts": "1709677878.622529", "text": "<@U04DKEFP1K8> when you get a moment to hop on zoom, lets apply for AWS credits. I have the code", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1XQ80", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " when you get a moment to hop on zoom, lets apply for AWS credits. I have the code"}]}]}]}, {"ts": "**********.453889", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I am stuck on this page. Its not accepting my IAM user or Root user account", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F06N37CRUCA", "created": **********, "timestamp": **********, "name": "Screenshot 2024-03-05 at 3.25.34 PM.png", "title": "Screenshot 2024-03-05 at 3.25.34 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 658991, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06N37CRUCA/screenshot_2024-03-05_at_3.25.34___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06N37CRUCA/download/screenshot_2024-03-05_at_3.25.34___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N37CRUCA-4cde597b84/screenshot_2024-03-05_at_3.25.34___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N37CRUCA-4cde597b84/screenshot_2024-03-05_at_3.25.34___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N37CRUCA-4cde597b84/screenshot_2024-03-05_at_3.25.34___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 318, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N37CRUCA-4cde597b84/screenshot_2024-03-05_at_3.25.34___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 424, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N37CRUCA-4cde597b84/screenshot_2024-03-05_at_3.25.34___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N37CRUCA-4cde597b84/screenshot_2024-03-05_at_3.25.34___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 635, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N37CRUCA-4cde597b84/screenshot_2024-03-05_at_3.25.34___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 706, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N37CRUCA-4cde597b84/screenshot_2024-03-05_at_3.25.34___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 847, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N37CRUCA-4cde597b84/screenshot_2024-03-05_at_3.25.34___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 904, "original_w": 2414, "original_h": 2130, "thumb_tiny": "AwAqADDTpmQpOBT6byCf8aAAthSfQVSF7MVBCJz9auP/AKtvoawwelAF9r6Reqxj8TS29+8s6oUUA981ROPWpbMf6XGc9/6UwNioy/P3X/75qSmke360gG7t4KhWGR3FZ406Yd0/OtMD2xS0AZv2CX1X86fb2Usc6OSuB1wav0UAFGKKKADAooooAKMDOaKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06N37CRUCA/screenshot_2024-03-05_at_3.25.34___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06N37CRUCA-413709d1a6", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "5iJLH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am stuck on this page. Its not accepting my IAM user or Root user account"}]}]}]}, {"ts": "**********.326099", "text": "I will ask vadym to enable sso for you then you can use google sso", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9+W3k", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will ask vadym to enable sso for you then you can use google sso"}]}]}]}, {"ts": "**********.547449", "text": "Look like I still do not have access to many of the feautures", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "co0I3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Look like I still do not have access to many of the feautures"}]}]}]}, {"ts": "**********.139239", "text": "can we apply using your account?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "En6Kh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "can we apply using your account?"}]}]}]}, {"ts": "**********.088369", "text": "or better to wait until I have access", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "bOtmm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "or better to wait until I have access"}]}]}]}, {"ts": "**********.046709", "text": "Yeah", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Gx3tD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yeah"}]}]}]}, {"ts": "**********.300329", "text": "All the comms will come to my email address", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4Skl4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All the comms will come to my email address"}]}]}]}, {"ts": "**********.989499", "text": "can you do zoom now?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.989499", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "qtxHu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "can you do zoom now?"}]}]}]}, {"ts": "1709751455.969609", "text": "<@U065H3M6WJV> Following issue is in review and pending deployment <https://compiify.atlassian.net/browse/COM-2456> Just wanted to make sure you and <PERSON><PERSON><PERSON> have a plan for the same", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1709751455.969609", "reply_count": 5, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12517::6627b5a0dbeb11eeb1cd7dfb83c6047f", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2456?atlOrigin=eyJpIjoiMjI4OGI3YzUwZThkNDQyYzk1ZmU0MzJjM2E4YTExZDQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2456 Cannot approve or overwrite merit recommendations as a Super Admin>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12517::6627b5a2dbeb11eeb1cd7dfb83c6047f", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/79ad6fec1eb23766c2c2c46cb17078ab?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12517::6627b5a1dbeb11eeb1cd7dfb83c6047f", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12517\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12517\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2456", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "mXkDs", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Following issue is in review and pending deployment "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2456"}, {"type": "text", "text": " Just wanted to make sure you and <PERSON><PERSON><PERSON> have a plan for the same"}]}]}]}, {"ts": "**********.151409", "text": "<@U04DS2MBWP4> Who will take the ownership of AWS Parent account, please let me know", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "V6T7v", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Who will take the ownership of AWS Parent account, please let me know"}]}]}]}, {"ts": "**********.421369", "text": "Lets make me for now", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "XYEvT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Lets make me for now"}]}]}]}, {"ts": "**********.927119", "text": "<@U065H3M6WJV> <PERSON><PERSON><PERSON> had reported that letter generation on latest build is failing. I have verified that latest merge did broke the build and generation still works fine on previous build. Opened <https://compiify.atlassian.net/browse/COM-2467> and have captured stack trace for the failure.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.927119", "reply_count": 4, "reactions": [{"name": "thankyouty", "users": ["U065H3M6WJV", "U0690EB5JE5"], "count": 2}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12528::ec1a75f0dbfd11eebae1817f9de92744", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2467?atlOrigin=eyJpIjoiNGVmZDVjN2UwYmZiNGUyZmJkYWY0NDg4NTYxZDVjNmMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2467 Letter generation is broken on latest build with commit id (49fe03d…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12528::ec1a75f2dbfd11eebae1817f9de92744", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12528::ec1a75f1dbfd11eebae1817f9de92744", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12528\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12528\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2467", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "4WBa6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> had reported that letter generation on latest build is failing. I have verified that latest merge did broke the build and generation still works fine on previous build. Opened "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2467"}, {"type": "text", "text": " and have captured stack trace for the failure."}]}]}]}, {"ts": "1709873102.591849", "text": "In addition to the new People Insights with compelling charts &amp; graphics, I'm proposing a <https://docs.google.com/document/d/1tEsOQuOXvL5y2CzgJDXz5kG3cI_jfWGwP6jsuBow4hA/edit|refresh of our downloadable reports> based on the types of requests we got from beta customers so far.\n\n<@U04DKEFP1K8> If you have some time to chime in on this, my main questions for you are\n• How do the current available roles relate to access to reports - should we make any assumptions about restricting this to Admins only?\n• Are there any fields in the current upload templates that we should consider removing? For example, if no customer is uploading \"cost center\" or \"job family group\", how important is it to include these in standard downloads?\n<@U0690EB5JE5> This is the PRD I mentioned that could correspond to our data consistency exercise.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709873102.591849", "reply_count": 9, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "files": [{"id": "F06NJHPGE4T", "created": 1709873105, "timestamp": 1709873105, "name": "Reports Refresh PRD", "title": "Reports Refresh PRD", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1tEsOQuOXvL5y2CzgJDXz5kG3cI_jfWGwP6jsuBow4hA", "external_url": "https://docs.google.com/document/d/1tEsOQuOXvL5y2CzgJDXz5kG3cI_jfWGwP6jsuBow4hA/edit", "url_private": "https://docs.google.com/document/d/1tEsOQuOXvL5y2CzgJDXz5kG3cI_jfWGwP6jsuBow4hA/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSOcjDAfUUmG/vD8qVhn60mG9aAHUU3B9aXBoAWiiigBr4yM4pFKjuKc2RSA8dKAF3D1oyKM+1GfagBRzRRRQAjU38vypxyOlAJ9RQAnP+RR/npTqM0AA6UUUUAf/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06NJHPGE4T/reports_refresh_prd", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "pwooU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In addition to the new People Insights with compelling charts & graphics, I'm proposing a "}, {"type": "link", "url": "https://docs.google.com/document/d/1tEsOQuOXvL5y2CzgJDXz5kG3cI_jfWGwP6jsuBow4hA/edit", "text": "refresh of our downloadable reports"}, {"type": "text", "text": " based on the types of requests we got from beta customers so far.\n\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " If you have some time to chime in on this, my main questions for you are\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "How do the current available roles relate to access to reports - should we make any assumptions about restricting this to Admins only?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Are there any fields in the current upload templates that we should consider removing? For example, if no customer is uploading \"cost center\" or \"job family group\", how important is it to include these in standard downloads?"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " This is the PRD I mentioned that could correspond to our data consistency exercise."}]}]}]}, {"ts": "1709917540.724929", "text": "<@U0690EB5JE5> For building the new Stride website, how important would it be for it to be built with next.js specifically, vs. just react.js ?\n\nDo you see any concerns about our frontend engineers' ability to maintain it long term if it's not specifically built with next.js?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709917540.724929", "reply_count": 18, "blocks": [{"type": "rich_text", "block_id": "Djb1N", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " For building the new Stride website, how important would it be for it to be built with next.js specifically, vs. just react.js ?\n\nDo you see any concerns about our frontend engineers' ability to maintain it long term if it's not specifically built with next.js?"}]}]}]}, {"ts": "1709935815.071769", "text": "I noticed that SDF has started \"locking in\" some final values, but they seem to be going team-by-team. So far <PERSON> has only approved/overwritten the values for the Legal and Vibrant teams...", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709935815.071769", "reply_count": 25, "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06P2HGT3JM", "created": 1709935809, "timestamp": 1709935809, "name": "Screenshot 2024-03-08 at 2.02.10 PM.png", "title": "Screenshot 2024-03-08 at 2.02.10 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 173208, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06P2HGT3JM/screenshot_2024-03-08_at_2.02.10___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06P2HGT3JM/download/screenshot_2024-03-08_at_2.02.10___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 253, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 337, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 505, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 562, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 674, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 719, "original_w": 1658, "original_h": 1164, "thumb_tiny": "AwAhADDSZQRzn86ricJlQp4PrVg9Kov99vrQBdjfegbGKCOe/wCBplv/AKkU8j2/WgBRx2NLTcdOP1p3egBD0qi332+tXj0qkytuPynr6UAWbf8A1Ip5x/s/jTYAREMjFOJ9x+VAB6fdpe/akz05H5U7vQAHpQaD0oNABRRRQAUUUUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06P2HGT3JM/screenshot_2024-03-08_at_2.02.10___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06P2HGT3JM-4750b1f846", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06NM0KGGBX", "created": 1709935812, "timestamp": 1709935812, "name": "Screenshot 2024-03-08 at 2.02.24 PM.png", "title": "Screenshot 2024-03-08 at 2.02.24 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 225888, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06NM0KGGBX/screenshot_2024-03-08_at_2.02.24___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06NM0KGGBX/download/screenshot_2024-03-08_at_2.02.24___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 310, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 414, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 621, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 690, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 828, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 883, "original_w": 1656, "original_h": 1428, "thumb_tiny": "AwApADDSYAjmq6yqHACEHOM5qwelUl/1o/3qAL1NI56H8DThTSPb9aAFHHY/nS03HTj9aXvQAHp2qkv+tH+9V09O1Ul/1o/3qAL2KaR7D8adzTT9V/GgAwOOBS45pPT7tO79qAEPSqaq3mA7T19Ku0UAAppPv+lOooAbnpz+lO70UUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06NM0KGGBX/screenshot_2024-03-08_at_2.02.24___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06NM0KGGBX-ccafc37b9a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "vk4we", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I noticed that SDF has started \"locking in\" some final values, but they seem to be going team-by-team. So far <PERSON> has only approved/overwritten the values for the Legal and Vibrant teams..."}]}]}]}, {"ts": "1709940017.215719", "text": "<PERSON> caught another issue with the downloadable files, ugh. :confused:\n<https://compiify.atlassian.net/browse/COM-2482>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709940017.215719", "reply_count": 3, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12543::6d3319d0dda211eea5c779ddd910214c", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2482?atlOrigin=eyJpIjoiZGRkN2FhMGY2ZWM5NGJjYzkwMDcxOTYwY2VhZTE1NTciLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2482 Increase % value in Export file doesn't match Merit view>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12543::6d3340e0dda211eea5c779ddd910214c", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12543::6d3319d1dda211eea5c779ddd910214c", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12543\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12543\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2482", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "tSmQg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> caught another issue with the downloadable files, ugh. "}, {"type": "emoji", "name": "confused", "unicode": "1f615"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2482"}]}]}]}, {"ts": "1709959076.202339", "text": "And, a customer need for an audit log (either customer-facing or at least available to us for supporting them) -- <PERSON> just asked what a manager had input before she edited it this week :zany_face:\n\n(Fortunately I had an earlier snapshot and was able to answer :wink: )", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709959076.202339", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "PS+27", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And, a customer need for an audit log (either customer-facing or at least available to us for supporting them) -- <PERSON> just asked what a manager had input before she edited it this week "}, {"type": "emoji", "name": "zany_face", "unicode": "1f92a"}, {"type": "text", "text": "\n\n(Fortunately I had an earlier snapshot and was able to answer "}, {"type": "emoji", "name": "wink", "unicode": "1f609"}, {"type": "text", "text": " )"}]}]}]}, {"ts": "1709961484.513449", "text": "We might also have to think about including higher precision for %s in our downloadable reports... :thinking_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709961484.513449", "reply_count": 3, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ZVjna", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We might also have to think about including higher precision for %s in our downloadable reports... "}, {"type": "emoji", "name": "thinking_face", "unicode": "1f914"}]}]}]}, {"ts": "1710110901.701429", "text": "<@U065H3M6WJV> Here is a list of tasks that require to be automated for customer implementation\n<https://compiify.atlassian.net/browse/COM-2484> ( Note: This is a WIP as i continue to populate each of the jira's)", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}, {"name": "thankyouty", "users": ["U065H3M6WJV"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12545::4c6f2240df3011eebd20710c412f167b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2484?atlOrigin=eyJpIjoiMjQ3NjQ3MGU2NmZmNDhhYTg4YjYxNjk1N2M3MmU3M2QiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2484 End to End Customer Implementation Automation>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12545::4c6f2242df3011eebd20710c412f167b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12545::4c6f2241df3011eebd20710c412f167b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12545\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12545\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2484", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "q7Jf+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Here is a list of tasks that require to be automated for customer implementation\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2484"}, {"type": "text", "text": " ( Note: This is a WIP as i continue to populate each of the jira's)"}]}]}]}, {"ts": "1710183244.757159", "text": "SDF is not currently planning to use the adjustment letters for this cycle (<@U0690EB5JE5> <@U04DKEFP1K8> fyi)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710183244.757159", "reply_count": 8, "files": [{"id": "F06NSH6Q4HL", "created": 1710183218, "timestamp": 1710183218, "name": "Screenshot 2024-03-11 at 11.53.23 AM.png", "title": "Screenshot 2024-03-11 at 11.53.23 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 77667, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06NSH6Q4HL/screenshot_2024-03-11_at_11.53.23___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06NSH6Q4HL/download/screenshot_2024-03-11_at_11.53.23___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NSH6Q4HL-2e40554620/screenshot_2024-03-11_at_11.53.23___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NSH6Q4HL-2e40554620/screenshot_2024-03-11_at_11.53.23___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NSH6Q4HL-2e40554620/screenshot_2024-03-11_at_11.53.23___am_360.png", "thumb_360_w": 360, "thumb_360_h": 79, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NSH6Q4HL-2e40554620/screenshot_2024-03-11_at_11.53.23___am_480.png", "thumb_480_w": 480, "thumb_480_h": 105, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NSH6Q4HL-2e40554620/screenshot_2024-03-11_at_11.53.23___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NSH6Q4HL-2e40554620/screenshot_2024-03-11_at_11.53.23___am_720.png", "thumb_720_w": 720, "thumb_720_h": 157, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NSH6Q4HL-2e40554620/screenshot_2024-03-11_at_11.53.23___am_800.png", "thumb_800_w": 800, "thumb_800_h": 175, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NSH6Q4HL-2e40554620/screenshot_2024-03-11_at_11.53.23___am_960.png", "thumb_960_w": 960, "thumb_960_h": 210, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NSH6Q4HL-2e40554620/screenshot_2024-03-11_at_11.53.23___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 224, "original_w": 1244, "original_h": 272, "thumb_tiny": "AwAKADDQ2lSTuPJzyelGT6inGkwPSgABPcijP+1+lGB6UYHpQAZ/2v0pQeev6UAc0tAH/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06NSH6Q4HL/screenshot_2024-03-11_at_11.53.23___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06NSH6Q4HL-9fffc2821f", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "yfjjD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF is not currently planning to use the adjustment letters for this cycle ("}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " fyi)"}]}]}]}, {"ts": "1710183823.751509", "text": "DA seems to be going better, comparatively.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710183823.751509", "reply_count": 8, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06PN2AMQ0Y", "created": 1710183821, "timestamp": 1710183821, "name": "Screenshot 2024-03-11 at 12.03.19 PM.png", "title": "Screenshot 2024-03-11 at 12.03.19 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 68601, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06PN2AMQ0Y/screenshot_2024-03-11_at_12.03.19___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06PN2AMQ0Y/download/screenshot_2024-03-11_at_12.03.19___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06PN2AMQ0Y-f0e5fba88e/screenshot_2024-03-11_at_12.03.19___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06PN2AMQ0Y-f0e5fba88e/screenshot_2024-03-11_at_12.03.19___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06PN2AMQ0Y-f0e5fba88e/screenshot_2024-03-11_at_12.03.19___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 58, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06PN2AMQ0Y-f0e5fba88e/screenshot_2024-03-11_at_12.03.19___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 77, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06PN2AMQ0Y-f0e5fba88e/screenshot_2024-03-11_at_12.03.19___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06PN2AMQ0Y-f0e5fba88e/screenshot_2024-03-11_at_12.03.19___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 115, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06PN2AMQ0Y-f0e5fba88e/screenshot_2024-03-11_at_12.03.19___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 128, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06PN2AMQ0Y-f0e5fba88e/screenshot_2024-03-11_at_12.03.19___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 153, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06PN2AMQ0Y-f0e5fba88e/screenshot_2024-03-11_at_12.03.19___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 164, "original_w": 1214, "original_h": 194, "thumb_tiny": "AwAHADC9s2dWJyc80u4+tK/am9/woAduPrRuPrTf8aKAH/N6ilG7uaWigD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06PN2AMQ0Y/screenshot_2024-03-11_at_12.03.19___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06PN2AMQ0Y-8eb325dbe4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "VrqCu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA seems to be going better, comparatively."}]}]}]}, {"ts": "1710267338.897769", "text": "<@U04DKEFP1K8> For <PERSON><PERSON>, did you set up their first cycle, or is that something <PERSON> might have done?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710267338.897769", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "qIay1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For <PERSON><PERSON>, did you set up their first cycle, or is that something <PERSON> might have done?"}]}]}]}, {"ts": "1710268843.321769", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> Starting a doc for the <https://docs.google.com/document/d/1Mk7miV5SiW6S3IQ5nAM188oT5oqjW9JYcV1klrjVEi4/edit?usp=sharing|Social login requirements here>.\n\n<PERSON><PERSON><PERSON><PERSON> - I will need some help from you understanding how we currently set up the initial Compiify admin and/or customer admin access, and how that might need to change (or not).", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710268843.321769", "reply_count": 4, "files": [{"id": "F06P6829GMQ", "created": 1710268845, "timestamp": 1710268845, "name": "Product Requirements for \"Social\" login", "title": "Product Requirements for \"Social\" login", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1Mk7miV5SiW6S3IQ5nAM188oT5oqjW9JYcV1klrjVEi4", "external_url": "https://docs.google.com/document/d/1Mk7miV5SiW6S3IQ5nAM188oT5oqjW9JYcV1klrjVEi4/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1Mk7miV5SiW6S3IQ5nAM188oT5oqjW9JYcV1klrjVEi4/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSYkdAPzpA3rSsMim4YUAO3CjcKTa3rRtPr+tADqKB0ooARvqR9KQEDuT+FKwzSBfWgB1FJtFG0elAC0UUUANYcjkUmPcU5vofwpAM+ooAAp9aXB9f0owKMCgBaKKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06P6829GMQ/product_requirements_for__social__login", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Tkrjp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Starting a doc for the "}, {"type": "link", "url": "https://docs.google.com/document/d/1Mk7miV5SiW6S3IQ5nAM188oT5oqjW9JYcV1klrjVEi4/edit?usp=sharing", "text": "Social login requirements here"}, {"type": "text", "text": ".\n\n<PERSON><PERSON><PERSON><PERSON> - I will need some help from you understanding how we currently set up the initial Compiify admin and/or customer admin access, and how that might need to change (or not)."}]}]}]}, {"ts": "1710283066.854589", "text": "<@U04DS2MBWP4> Instead of manually provided link to soc2 document, <PERSON><PERSON> provides a trust center, link to it can be provided on compiify's website (needs configuration to make it publicly accessible) <https://app.vanta.com/trust-center/view> ( you can preview it here with your google login currently ). I will bring it up in next meeting with Agency and provide remaining details.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710283066.854589", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "/ZC6+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Instead of manually provided link to soc2 document, <PERSON><PERSON> provides a trust center, link to it can be provided on compiify's website (needs configuration to make it publicly accessible) "}, {"type": "link", "url": "https://app.vanta.com/trust-center/view"}, {"type": "text", "text": " ( you can preview it here with your google login currently ). I will bring it up in next meeting with Agency and provide remaining details."}]}]}]}, {"ts": "1710285886.251609", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> Will the <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities board> get updated after the PR queue is completed / merged? I'm having a hard time keeping track of what's done vs pending using the current view.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710285886.251609", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "ECgYS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Will the "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities board"}, {"type": "text", "text": " get updated after the PR queue is completed / merged? I'm having a hard time keeping track of what's done vs pending using the current view."}]}]}]}, {"ts": "1710287300.356659", "text": "<@U065H3M6WJV>\n1. Fixes for <https://compiify.atlassian.net/browse/COM-2509> is deployed on sdf-test and are ready for deployment on sdf prod. Okay to deploy fixes later tonight on production?\n2. Started documenting managing precision digits for usd conversion and storing decimal digits here <https://compiify.atlassian.net/browse/COM-2520>\n3. No updates today on setting up DA's templates on <http://qa.compiify.com|qa.compiify.com>, will target this activity for tomorrow", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710287300.356659", "reply_count": 4, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12570::020d19e0e0cb11eeba9c5bc0aa480443", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2509?atlOrigin=eyJpIjoiNWRhZGFmZDMwOWMyNDQwYzg1ZDEzNDUzYWYyYzkwNWQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2509 Approving a leader hierarchy is not allowed if an ineligible employ…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12570::020d19e4e0cb11eeba9c5bc0aa480443", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/79ad6fec1eb23766c2c2c46cb17078ab?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12570::020d19e1e0cb11eeba9c5bc0aa480443", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12570\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12570\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2509", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:12581::020d19e2e0cb11eeba9c5bc0aa480443", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2520?atlOrigin=eyJpIjoiMTNjYmE3M2Q2YzdjNDU4N2E0NzYwNTVmN2YzOWIxNDMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2520 Managing currency conversion and decimal digits in the product>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12581::020d19e5e0cb11eeba9c5bc0aa480443", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12581::020d19e3e0cb11eeba9c5bc0aa480443", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12581\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12581\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2520", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "i15P9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Fixes for "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2509"}, {"type": "text", "text": " is deployed on sdf-test and are ready for deployment on sdf prod. Okay to deploy fixes later tonight on production?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Started documenting managing precision digits for usd conversion and storing decimal digits here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2520"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "No updates today on setting up DA's templates on "}, {"type": "link", "url": "http://qa.compiify.com", "text": "qa.compiify.com"}, {"type": "text", "text": ", will target this activity for tomorrow"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1710347224.867799", "text": "<@U065H3M6WJV> what's the expected cycle closing date for SDF?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "72hhd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " what's the expected cycle closing date for SDF?"}]}]}]}, {"ts": "1710347234.037989", "text": "and for DA", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9nUMj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "and for DA"}]}]}]}, {"ts": "1710347819.462509", "text": "<@U04DS2MBWP4> *SDF* was originally supposed to close their cycle 2/27, they officially delayed it to 3/1, but unofficially were still making changes in the last week. <PERSON> indicated that managers were sharing raises with employees this week (probably because pay effective date was still 3/1 and they wanted to inform before payroll shows up). They opted not to use our adjustment letters, and I'm waiting to see what else <PERSON> will need to close everything out.\n\n*DA* may still be waiting on their comp committee, they originally had a deadline of 3/15 for that and their pay effective date is 4/1.", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TnLSZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "text", "text": "SDF ", "style": {"bold": true}}, {"type": "text", "text": "was originally supposed to close their cycle 2/27, they officially delayed it to 3/1, but unofficially were still making changes in the last week. <PERSON> indicated that managers were sharing raises with employees this week (probably because pay effective date was still 3/1 and they wanted to inform before payroll shows up). They opted not to use our adjustment letters, and I'm waiting to see what else <PERSON> will need to close everything out.\n\n"}, {"type": "text", "text": "DA ", "style": {"bold": true}}, {"type": "text", "text": "may still be waiting on their comp committee, they originally had a deadline of 3/15 for that and their pay effective date is 4/1."}]}]}]}, {"ts": "1710362404.100539", "text": "<@U065H3M6WJV> We need to add a cookie consent manager to our live website <http://compiify.com|compiify.com> I have put together a jira with initial functional requirements. Please review and provide your feedback. <@U04DS2MBWP4> i am assuming new website already has been designed to include this if not please include , this is a must have.\n\n<https://compiify.atlassian.net/browse/COM-2532>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710362404.100539", "reply_count": 12, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12593::df8323b0e17911ee970425a73efabb2c", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2532?atlOrigin=eyJpIjoiY2E1Y2EwYTAyMWMxNDFhZWFkZjg2NWVjMWFlNDAwYWMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2532 Cookie consent manager>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12593::df8323b2e17911ee970425a73efabb2c", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12593::df8323b1e17911ee970425a73efabb2c", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12593\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12593\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2532", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "/e7EG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " We need to add a cookie consent manager to our live website "}, {"type": "link", "url": "http://compiify.com", "text": "compiify.com"}, {"type": "text", "text": " I have put together a jira with initial functional requirements. Please review and provide your feedback. "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " i am assuming new website already has been designed to include this if not please include , this is a must have.\n\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2532"}]}]}]}, {"ts": "1710366792.217609", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I've added a <https://docs.google.com/document/d/1sRICN3PCrHRh4Wgk2O1Qw5CF8tCYgBnwe5S_WBBgNQI/edit|suggested agenda> for our next eng discussion:\n• Updates on this week’s deliverables:\n    ◦ PR queue deployment\n    ◦ Project plan, including estimates for size of effort for top priorities\n• Discuss possible approach to balance different types of investment:\n    ◦ Engineering quality &amp; infra _[Ex: test framework; multi-tenancy]_\n    ◦ Core comp cycle work for beta customers _[Ex: Reports refresh, Cycle config, Merit view v2, People Insights]_\n    ◦ New features _[Ex: Salary Bands completion, Total Rewards]_\n• Customer enablement - current progress, demo?\nLet me know if there's anything else in addition or more urgent that we need to discuss.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710366792.217609", "reply_count": 7, "files": [{"id": "F06NT7KNA0N", "created": 1710190164, "timestamp": 1710190164, "name": "Engineering discussions", "title": "Engineering discussions", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1sRICN3PCrHRh4Wgk2O1Qw5CF8tCYgBnwe5S_WBBgNQI", "external_url": "https://docs.google.com/document/d/1sRICN3PCrHRh4Wgk2O1Qw5CF8tCYgBnwe5S_WBBgNQI/edit", "url_private": "https://docs.google.com/document/d/1sRICN3PCrHRh4Wgk2O1Qw5CF8tCYgBnwe5S_WBBgNQI/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACXSOe1AJ9DSkZpnQ45/OgB+fajPtSAA9z+dLigAooooAM00jnNOPSo/woAkFFNGPSnZoAKKKKAEaj/PWloxQAmfp+dLzRiigAooooA//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06NT7KNA0N/engineering_discussions", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "gGrZ8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I've added a "}, {"type": "link", "url": "https://docs.google.com/document/d/1sRICN3PCrHRh4Wgk2O1Qw5CF8tCYgBnwe5S_WBBgNQI/edit", "text": "suggested agenda"}, {"type": "text", "text": " for our next eng discussion:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Updates on this week’s deliverables:"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "PR queue deployment"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Project plan, including estimates for size of effort for top priorities"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Discuss possible approach to balance different types of investment:"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Engineering quality & infra"}, {"type": "text", "text": " [Ex: test framework; multi-tenancy]", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Core comp cycle work for beta customers "}, {"type": "text", "text": "[Ex: Reports refresh, Cycle config, Merit view v2, People Insights]", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "New features "}, {"type": "text", "text": "[Ex: Salary Bands completion, Total Rewards]", "style": {"italic": true}}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Customer enablement - current progress, demo?"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nLet me know if there's anything else in addition or more urgent that we need to discuss."}]}]}]}, {"ts": "1710371618.231909", "text": "I'm trying to test some things on sdf-test and ran into a bug where real-time budget updates are no longer working. <@U04DKEFP1K8> <@U0690EB5JE5> could this be related to any recent push to prod? <https://www.loom.com/share/fdcafcaa231846f2b37d97b12417a850?sid=0b9f81d0-8587-4928-942e-79cb6490888d>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710371618.231909", "reply_count": 10, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "D046G", "video_url": "https://www.loom.com/embed/fdcafcaa231846f2b37d97b12417a850?sid=0b9f81d0-8587-4928-942e-79cb6490888d&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/fdcafcaa231846f2b37d97b12417a850-4x3.jpg", "alt_text": "Bug - Budget error / real-time updates", "title": {"type": "plain_text", "text": "Bug - Budget error / real-time updates", "emoji": true}, "title_url": "https://www.loom.com/share/fdcafcaa231846f2b37d97b12417a850", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 49 sec  ", "emoji": true}}, {"type": "actions", "block_id": "OZLb5", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/fdcafcaa231846f2b37d97b12417a850?sid=0b9f81d0-8587-4928-942e-79cb6490888d"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"fdcafcaa231846f2b37d97b12417a850\",\"videoName\":\"Bug - Budget error / real-time updates\",\"sendWatchLaterReminderWeekdaysOnly\":true,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/fdcafcaa231846f2b37d97b12417a850?sid=0b9f81d0-8587-4928-942e-79cb6490888d", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "Pv05X", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'm trying to test some things on sdf-test and ran into a bug where real-time budget updates are no longer working. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " could this be related to any recent push to prod? "}, {"type": "link", "url": "https://www.loom.com/share/fdcafcaa231846f2b37d97b12417a850?sid=0b9f81d0-8587-4928-942e-79cb6490888d"}]}]}]}, {"ts": "1710431447.090069", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> need a couple min break before we start the next call", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1Y+k0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " need a couple min break before we start the next call"}]}]}]}, {"ts": "1710523965.329419", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> What's the environment we will use to test the PRs that have been merged? dev-test ?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710523965.329419", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "UPyur", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " What's the environment we will use to test the PRs that have been merged? dev-test ?"}]}]}]}, {"ts": "1710540010.084879", "text": "<@U04DKEFP1K8> Since I have a call with <PERSON> on Monday about adjustment letters:\n• Will we be able to set up DA env with their templates before then?\n• Will we be able to correct the logo size &amp; include the sample signature as well?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710540010.084879", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "zow33", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Since I have a call with <PERSON> on Monday about adjustment letters:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will we be able to set up DA env with their templates before then?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Will we be able to correct the logo size & include the sample signature as well?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1710783063.029459", "text": "<@U04DKEFP1K8> I walked through adjustment letters workflow with <PERSON> this morning, and have some updates:\n• They want to use <PERSON><PERSON><PERSON>'s name and signature instead of Emnet's. <PERSON> has sent me a .png for <PERSON><PERSON><PERSON>'s signature, and we'll need to update the template text as well. \n• Their pay effective date is April 1, not March 15, so I believe we need to change their current cycle settings for that. \n• They will not be giving any one-time bonuses so we only need to have 1 template, for pay increases.\n• <PERSON> will be generating &amp; downloading the adjustment letters -- let's make sure that the process for doing this in bulk works as expected, and correctly assigns a specific employee name in each file name.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710783063.029459", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "bLs9a", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I walked through adjustment letters workflow with <PERSON> this morning, and have some updates:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They want to use <PERSON><PERSON><PERSON>'s name and signature instead of Emnet's. <PERSON> has sent me a .png for <PERSON><PERSON><PERSON>'s signature, and we'll need to update the template text as well. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Their pay effective date is April 1, not March 15, so I believe we need to change their current cycle settings for that. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "They will not be giving any one-time bonuses so we only need to have 1 template, for pay increases."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> will be generating & downloading the adjustment letters -- let's make sure that the process for doing this in bulk works as expected, and correctly assigns a specific employee name in each file name."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1710869506.597329", "text": "<@U065H3M6WJV> resuming the thread again on cookie consent manager, <PERSON><PERSON> consent maanger will be needed for application website only\n<https://compiify.atlassian.net/browse/COM-2532>. I will need your support to be finalize design , requirement for it", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710869506.597329", "reply_count": 36, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12593::90353860e61611ee818ce5792cd1a8e6", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2532?atlOrigin=eyJpIjoiN2M5YjUyNzVkZjliNDAyZWI5NWMwYzgxNzk0NjQyN2IiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2532 Cookie consent manager>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12593::90353862e61611ee818ce5792cd1a8e6", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/712020:cfb94840-2a5e-4f44-9a54-640524aeaa45/9bf71d6c-2eb3-4583-9fc6-290983a77c34/48?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12593::90353861e61611ee818ce5792cd1a8e6", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12593\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12593\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2532", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "8KEi9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " resuming the thread again on cookie consent manager, <PERSON><PERSON> consent maanger will be needed for application website only\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2532"}, {"type": "text", "text": ". I will need your support to be finalize design , requirement for it"}]}]}]}, {"ts": "1710879621.065839", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> Yesterday we discussed how SDF's customizations for XLM equity are behind a feature flag, but I'm seeing the XLM column names in the export from DA's merit cycle (example <https://drive.google.com/file/d/1YZSWGO1q6fUNySeoGKPOENIaEHsIIb-F/view?usp=drive_link|here>).\n\nWas the column type (dropdown vs numeric field) the only thing currently behind a flag? Is more work needed to wrap the rest of these \"XLM\" label changes behind the same flag?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710879621.065839", "reply_count": 1, "edited": {"user": "U065H3M6WJV", "ts": "1710879625.000000"}, "blocks": [{"type": "rich_text", "block_id": "pYRc7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Yesterday we discussed how SDF's customizations for XLM equity are behind a feature flag, but I'm seeing the XLM column names in the export from DA's merit cycle (example "}, {"type": "link", "url": "https://drive.google.com/file/d/1YZSWGO1q6fUNySeoGKPOENIaEHsIIb-F/view?usp=drive_link", "text": "here"}, {"type": "text", "text": ").\n\nWas the column type (dropdown vs numeric field) the only thing currently behind a flag? Is more work needed to wrap the rest of these \"XLM\" label changes behind the same flag?"}]}]}]}, {"ts": "1710881156.293419", "text": "Also, <@U04DKEFP1K8> Any idea why DA's \"Post Merit Employee Roster\" report has fewer employees listed than their Merit view Export file for the full org?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710881156.293419", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "egc2U", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also, "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Any idea why DA's \"Post Merit Employee Roster\" report has fewer employees listed than their Merit view Export file for the full org?"}]}]}]}, {"ts": "1710881263.924019", "text": "I stepped outside to pick up kids , let me revert back on both queries once i am back", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710881263.924019", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TiW0D", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I stepped outside to pick up kids , let me revert back on both queries once i am back"}]}]}]}, {"ts": "1710891745.141419", "text": "from what I see it should work", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1710869506.597329", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "g/jla", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "from what I see it should work"}]}]}]}, {"ts": "1710952615.449469", "text": "<!here> staging environment is undergoing a db update. Do we have any demo in next few mins. env should be back online  soon", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710952615.449469", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "cEXyT", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " staging environment is undergoing a db update. Do we have any demo in next few mins. env should be back online  soon"}]}]}]}, {"ts": "1710952632.212919", "text": "<@U04DS2MBWP4> <@U065H3M6WJV> when is the next demo planned", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "M+GQN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " when is the next demo planned"}]}]}]}, {"ts": "1710952695.838869", "text": "<@U04DKEFP1K8> I think <PERSON>’s next demo is at noon our time", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZTORR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I think "}, {"type": "text", "text": "Wesley’s"}, {"type": "text", "text": " next demo is at noon our time"}]}]}]}, {"ts": "1710952931.485959", "text": "<@U04DS2MBWP4> For your next call with <PERSON>, in addition to getting feedback - if they are planning to continue with another cycle / Total Rewards / etc. can we ask for their Zenefits API key as well, so <@U0690EB5JE5> can test that integration?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "guOAX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " For your next call with <PERSON>, in addition to getting feedback - if they are planning to continue with another cycle / Total Rewards / etc. can we ask for their Zenefits API key as well, so "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can test that integration?"}]}]}]}, {"ts": "1710952977.030339", "text": "<@U068MM2H2QG> I will send an update when the environment is active again.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710952977.030339", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "HMyKe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U068MM2H2QG"}, {"type": "text", "text": " I will send an update when the environment is active again."}]}]}]}, {"ts": "1710955606.493039", "text": "<@U065H3M6WJV> here is an early preview of cookiebot implementation <https://3ae4-176-106-202-180.ngrok-free.app/>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710955606.493039", "reply_count": 3, "reactions": [{"name": "muscle", "users": ["U04DS2MBWP4"], "count": 1}], "attachments": [{"from_url": "https://3ae4-176-106-202-180.ngrok-free.app/", "id": 1, "original_url": "https://3ae4-176-106-202-180.ngrok-free.app/", "fallback": "Compiify", "text": "Compiify software", "title": "Compiify", "title_link": "https://3ae4-176-106-202-180.ngrok-free.app/", "service_name": "3ae4-176-106-202-180.ngrok-free.app"}], "blocks": [{"type": "rich_text", "block_id": "4s+J1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " here is an early preview of cookiebot implementation "}, {"type": "link", "url": "https://3ae4-176-106-202-180.ngrok-free.app/"}]}]}]}, {"ts": "1710972361.059869", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I met with <PERSON> from Nauto today, to walk through the cycle builder and capture more of their requirements &amp; feedback. Short version of <https://docs.google.com/document/d/1LvHRsNc_8LUKHm3MM2QdK3nbXw8VVicGQmHelxLfnX4/edit|notes here>, some of these are existing JIRAs but I'll need to create some new ones as well.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710972361.059869", "reply_count": 4, "edited": {"user": "U065H3M6WJV", "ts": "1710972366.000000"}, "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "hEo9Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I met with <PERSON> from Nauto today, to walk through the cycle builder and capture more of their requirements & feedback. Short version of "}, {"type": "link", "url": "https://docs.google.com/document/d/1LvHRsNc_8LUKHm3MM2QdK3nbXw8VVicGQmHelxLfnX4/edit", "text": "notes here"}, {"type": "text", "text": ", some of these are existing JIRAs but I'll need to create some new ones as well."}]}]}]}, {"ts": "1710976375.075729", "text": "<@U065H3M6WJV> what's our current strategy/stop gap for customers who want to have custom fields or formula for say salary, bonus and equity calculations for merit planning?\n\nJust do it in the backend for now?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1710976375.075729", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "SdvlY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " what's our current strategy/stop gap for customers who want to have custom fields or formula for say salary, bonus and equity calculations for merit planning?\n\nJust do it in the backend for now?"}]}]}]}, {"ts": "1710992267.675949", "text": "<@U065H3M6WJV> Engineering is planning to update db configuration for compliance requirement tomorrow morning. There will be a downtime of 1.5 hours for upgrade to complete for DA and SDF between. Target is to complete everything before 10am. Let me know if the plan is okay and eng can proceed?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710992267.675949", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "6e7NX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Engineering is planning to update db configuration for compliance requirement tomorrow morning. There will be a downtime of 1.5 hours for upgrade to complete for DA and SDF between. Target is to complete everything before 10am. Let me know if the plan is okay and eng can proceed?"}]}]}]}, {"ts": "1711036219.405269", "text": "<!here> Planned update is in progress currently ETA to bring services online 10am PST", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1711036219.405269", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Ud/GD", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Planned update is in progress currently ETA to bring services online 10am PST"}]}]}]}, {"ts": "1711045775.281219", "text": "pequity just launched their benchmarking data. It's AI powered\n<https://pequity.com/solutions/data>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1711045775.281219", "reply_count": 5, "edited": {"user": "U04DS2MBWP4", "ts": "1711045790.000000"}, "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "attachments": [{"image_url": "https://uploads-ssl.webflow.com/65d3793cb48b155bbd2ad553/65f8abbb3b7621b6719421ef_pequity_data_opengraph.png", "image_width": 1200, "image_height": 630, "image_bytes": 385073, "from_url": "https://pequity.com/solutions/data", "id": 1, "original_url": "https://pequity.com/solutions/data", "fallback": "Pequity Solutions | Compensation Data", "text": "Compensation data you don't have to share your employee census data to access.", "title": "Pequity Solutions | Compensation Data", "title_link": "https://pequity.com/solutions/data", "service_name": "pequity.com"}], "blocks": [{"type": "rich_text", "block_id": "uJham", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "pequity just launched their benchmarking data. It's AI powered\n"}, {"type": "link", "url": "https://pequity.com/solutions/data"}]}]}]}, {"ts": "1711051631.419079", "text": "<@U04DKEFP1K8> Following up from this morning's discussion, I've now updated the <https://docs.google.com/spreadsheets/d/1c61ad1qR6dAqNXDmID5YWr41OTBXurMW/edit?usp=drive_link&amp;ouid=107994932584597228039&amp;rtpof=true&amp;sd=true|main equity template> to include the new columns.\n• Can you review and let me know if the column names &amp; descriptions look correct?\n• Should we also add a column to capture any 'cliff' timing?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1711051631.419079", "reply_count": 12, "files": [{"id": "F06QBF5U031", "created": 1711051633, "timestamp": 1711051633, "name": "Equity Template.xlsx", "title": "Equity Template.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 21675, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1c61ad1qR6dAqNXDmID5YWr41OTBXurMW", "external_url": "https://docs.google.com/spreadsheets/d/1c61ad1qR6dAqNXDmID5YWr41OTBXurMW/edit?usp=drive_link&ouid=107994932584597228039&rtpof=true&sd=true", "url_private": "https://docs.google.com/spreadsheets/d/1c61ad1qR6dAqNXDmID5YWr41OTBXurMW/edit?usp=drive_link&ouid=107994932584597228039&rtpof=true&sd=true", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06QBF5U031-fe84b4394c/equity_template_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06QBF5U031-fe84b4394c/equity_template_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06QBF5U031-fe84b4394c/equity_template_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06QBF5U031-fe84b4394c/equity_template_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06QBF5U031-fe84b4394c/equity_template_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06QBF5U031-fe84b4394c/equity_template_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06QBF5U031-fe84b4394c/equity_template_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06QBF5U031-fe84b4394c/equity_template_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06QBF5U031-fe84b4394c/equity_template_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHSOe1J83tTqKAE59KWiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06QBF5U031/equity_template.xlsx", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "4llB+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Following up from this morning's discussion, I've now updated the "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1c61ad1qR6dAqNXDmID5YWr41OTBXurMW/edit?usp=drive_link&ouid=107994932584597228039&rtpof=true&sd=true", "text": "main equity template"}, {"type": "text", "text": " to include the new columns.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can you review and let me know if the column names & descriptions look correct?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we also add a column to capture any 'cliff' timing?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1711064454.504689", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> Can we get an updated (rough) timeline for the items on the <https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=460732198|Project Plan sheet>? And please correct the 'owner' column if any of those are wrong :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1711064454.504689", "reply_count": 3, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F06NZPVENGG", "created": 1709776875, "timestamp": 1709776875, "name": "Projects", "title": "Roadmap", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk", "external_url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=1511321073", "url_private": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=1511321073", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NZPVENGG-821ce7912b/projects_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NZPVENGG-821ce7912b/projects_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NZPVENGG-821ce7912b/projects_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NZPVENGG-821ce7912b/projects_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NZPVENGG-821ce7912b/projects_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NZPVENGG-821ce7912b/projects_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NZPVENGG-821ce7912b/projects_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NZPVENGG-821ce7912b/projects_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NZPVENGG-821ce7912b/projects_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSJ+bFLUbbt/GMYpwxQApJzS0hGSD6UtABRRRQAmDnNGKWigAooooAKKKKACiiigAooooAKKKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06NZPVENGG/projects", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "xd7SI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can we get an updated (rough) timeline for the items on the "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=460732198", "text": "Project Plan sheet"}, {"type": "text", "text": "? And please correct the 'owner' column if any of those are wrong "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1711121714.630549", "text": "<@U0690EB5JE5> are we still on for people insights work review?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1711121714.630549", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "E+bJt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " are we still on for people insights work review?"}]}]}]}, {"ts": "1711121746.531659", "text": "lets use this link <https://us06web.zoom.us/j/84157213048?pwd=5IaQsMQYo9bRv6BNFv3wdRfACI1CNX.1>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R06R9MWKBQ9", "block_id": "1o7to", "api_decoration_available": false, "call": {"v1": {"id": "R06R9MWKBQ9", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1711121747, "active_participants": [], "all_participants": [], "display_id": "841-5721-3048", "join_url": "https://us06web.zoom.us/j/84157213048?pwd=5IaQsMQYo9bRv6BNFv3wdRfACI1CNX.1", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1711218219, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/84157213048?pwd=5IaQsMQYo9bRv6BNFv3wdRfACI1CNX.1", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "qjtOT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "lets use this link "}, {"type": "link", "url": "https://us06web.zoom.us/j/84157213048?pwd=5IaQsMQYo9bRv6BNFv3wdRfACI1CNX.1"}]}]}]}, {"ts": "1711122564.196039", "text": "Extremely sorry. I have been thinking its at 9am PST vs 9PM IST", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1711121714.630549", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "meTYa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Extremely sorry. I have been thinking its at 9am PST vs 9PM IST"}]}]}]}, {"ts": "1711134610.669459", "text": "<@U04DKEFP1K8> Our beta customer Rightway will need Okta SSO. What will we need from their IT team in order to make that work?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1711134610.669459", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "CraEG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Our beta customer Rightway will need Okta SSO. What will we need from their IT team in order to make that work?"}]}]}]}, {"ts": "1711147527.498539", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I noticed during a customer demo that <PERSON>'s view on staging still has some broken css styling, and also does not have the latest build. I was trying to make some changes to the budget for the demo cycle, and found that I could not edit equity budget for \"<PERSON>'s directs\" team.\n• Would these be addressed when we push the fixes from the PR queue?\n• What's the timing for testing/validating all those merged PRs now?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1711147527.498539", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "NsOIH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I noticed during a customer demo that <PERSON>'s view on staging still has some broken css styling, and also does not have the latest build. I was trying to make some changes to the budget for the demo cycle, and found that I could not edit equity budget for \"<PERSON>'s directs\" team.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Would these be addressed when we push the fixes from the PR queue?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "What's the timing for testing/validating all those merged PRs now?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1711153407.363549", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> What's the status of DA's adjustment letters? Last update on <https://compiify.atlassian.net/browse/COM-2291|this ticket> was 3/13, and in the QA environment I see a broken image for the signature and it's still using Emnet's name instead of <PERSON><PERSON><PERSON>'s. When will we be able to fix these and upload to the DA production environment for <PERSON>?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1711153407.363549", "reply_count": 7, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12352::926135c0e8ab11eeaa5f7b2b9520719e", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2291?atlOrigin=eyJpIjoiMzYwNjg0ZTUzZjlhNDU3M2FlMDE2OGE5YzYwN2Y0ZWUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2291 Set up adjustment letter templates for DA>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12352::926135c2e8ab11eeaa5f7b2b9520719e", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/606486a6aee240006877c236/2b20eeb2-9337-436b-afa5-c418e47dbe4c/48?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12352::926135c1e8ab11eeaa5f7b2b9520719e", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12352\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12352\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2291", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "egfxs", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " What's the status of DA's adjustment letters? Last update on "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2291", "text": "this ticket"}, {"type": "text", "text": " was 3/13, and in the QA environment I see a broken image for the signature and it's still using Emnet's name instead of <PERSON><PERSON><PERSON>'s. When will we be able to fix these and upload to the DA production environment for <PERSON>?"}]}]}]}, {"ts": "1711218325.487639", "text": "<@U04DKEFP1K8> For People Insights, we'll also need to include sections for bonus awards & equity. I've updated the <https://docs.google.com/document/d/111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc/edit|PRD> as well as the <https://www.figma.com/file/ESRnESN3izSXxWp7nNR6Gl/Design-file?type=design&node-id=3498-85611&mode=design&t=6DEn48w0oyFBTMC7-0|design file> to capture those additional sections; can you add whatever is needed in the <https://compiify.atlassian.net/browse/COM-2539|JIRA epic>?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1711218325.487639", "reply_count": 2, "edited": {"user": "U065H3M6WJV", "ts": "1711218343.000000"}, "files": [{"id": "F06K0N7TDAR", "created": 1708041932, "timestamp": 1708041932, "name": "Analytics & Insights PRD", "title": "People Insights PRD", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc", "external_url": "https://docs.google.com/document/d/111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc/edit", "url_private": "https://docs.google.com/document/d/111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSOc8MB+FJhv7w/KlYZ+tJhqAFAIJyc/hS03DUc/5NADqKKKAEYc9T+FIOP7x+tK3SmjGP/r0APz7UU3j0/Wjj0/WgB1FFFACN0puR/dpxIHWkCg85oAT/AIDSgn0pdtG2gBaKKKAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06K0N7TDAR/analytics___insights_prd", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12600::c2a76e10e94211eeaa5f7b2b9520719e", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2539?atlOrigin=eyJpIjoiNjJmODhjZTY1YjM5NDI2N2EzMDUyNDQyYjdmZmE3MjAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2539 People Insights>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12600::c2a76e12e94211eeaa5f7b2b9520719e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12600::c2a76e11e94211eeaa5f7b2b9520719e", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12600\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12600\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2539", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "6tO+Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For People Insights, we'll also need to include sections for bonus awards & equity. I've updated the "}, {"type": "link", "url": "https://docs.google.com/document/d/111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc/edit", "text": "PRD"}, {"type": "text", "text": " as well as the "}, {"type": "link", "url": "https://www.figma.com/file/ESRnESN3izSXxWp7nNR6Gl/Design-file?type=design&node-id=3498-85611&mode=design&t=6DEn48w0oyFBTMC7-0", "text": "design file"}, {"type": "text", "text": " to capture those additional sections; can you add whatever is needed in the "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2539", "text": "JIRA epic"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1711383239.028219", "text": "Hey <@U04DKEFP1K8> <@U0690EB5JE5>, I'm still seeing some examples where engineers are using customer data sets in their environments/demos. <https://www.loom.com/share/a9d968b2a3044c7b917671e6cea37f91|Here's an example> where the visible data is SDF's employees.\n\nCan we make sure all engineers are working off of artificial data sets on their local environments? (I don't think this example came from production, right?)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1711383239.028219", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Ps7If", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": ", I'm still seeing some examples where engineers are using customer data sets in their environments/demos. "}, {"type": "link", "url": "https://www.loom.com/share/a9d968b2a3044c7b917671e6cea37f91", "text": "Here's an example"}, {"type": "text", "text": " where the visible data is SDF's employees.\n\nCan we make sure all engineers are working off of artificial data sets on their local environments? (I don't think this example came from production, right?)"}]}]}]}, {"ts": "**********.238129", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> PlayQ data: See the 2 \"Updated\" xlsx files in <https://drive.google.com/drive/u/0/folders/1CDVw5sS2KRsy9sULCIpY1Oss-Qu47ywR|this folder>.\n\n• We will still need to fill in placeholder data for employee names (for example, using \"Employee [IDNumber]\" format)\n• Employee &amp; salary band data should now use consistent categories for regions\nLet me know if we're able to load their account with these updated files! :pray:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.238129", "reply_count": 9, "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "g+QvM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " PlayQ data: See the 2 \"Updated\" xlsx files in "}, {"type": "link", "url": "https://drive.google.com/drive/u/0/folders/1CDVw5sS2KRsy9sULCIpY1Oss-Qu47ywR", "text": "this folder"}, {"type": "text", "text": ".\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We will still need to fill in placeholder data for employee names (for example, using \"Employee [IDNumber]\" format)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Employee & salary band data should now use consistent categories for regions"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nLet me know if we're able to load their account with these updated files! "}, {"type": "emoji", "name": "pray", "unicode": "1f64f"}]}]}]}, {"ts": "**********.315169", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> can we pls put the weekly demo calls on the calendar?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.315169", "reply_count": 1, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "1nJhR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we pls put the weekly demo calls on the calendar?"}]}]}]}, {"ts": "**********.366839", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> Have y'all had a chance to review the updated equity data for Nauto? Anything we need from <PERSON>? She said the current \"fair market value\" for their equity is $0.28/unit.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.366839", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "Cyo8H", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Have y'all had a chance to review the updated equity data for Nauto? Anything we need from <PERSON>? She said the current \"fair market value\" for their equity is $0.28/unit."}]}]}]}]}