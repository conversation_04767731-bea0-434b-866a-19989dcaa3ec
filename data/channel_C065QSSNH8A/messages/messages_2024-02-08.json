{"date": "2024-02-08", "channel_id": "C065QSSNH8A", "message_count": 17, "messages": [{"ts": "1707356649.121029", "text": "Big thanks to <@U04DKEFP1K8> <@U065H3M6WJV> for working round the clock to get them started and to give them the amazing customer experience that they could only get with Compiify.:pray::heart:", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "panda_work", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "b04d9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Big thanks to "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " for working round the clock to get them started and to give them the amazing customer experience that they could only get with Compiify."}, {"type": "emoji", "name": "pray", "unicode": "1f64f"}, {"type": "emoji", "name": "heart", "unicode": "2764-fe0f"}]}]}]}, {"ts": "**********.307299", "text": "Sharing for visibility: We're about a day and a half overdue for bringing up SDF's full cycle. <PERSON><PERSON><PERSON><PERSON> is currently troubleshooting a showstopper bug, which seems to be related to recent changes that were part of SDF's customizations.\n• They'd asked us to include \"last raise date\" and \"previous salary\" info in the Merit view\n• This was working when it was patched in, but seems to have introduced an issue when there was a full reload of data from scratch. \n<PERSON><PERSON>'s been kind and patient and we're giving her updates / ETAs along the way.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.307299", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "3DHjy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sharing for visibility: We're about a day and a half overdue for bringing up SDF's full cycle. <PERSON><PERSON><PERSON><PERSON> is currently troubleshooting a showstopper bug, which seems to be related to recent changes that were part of SDF's customizations.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They'd asked us to include \"last raise date\" and \"previous salary\" info in the Merit view"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "This was working when it was patched in, but seems to have introduced an issue when there was a full reload of data from scratch. "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n<PERSON><PERSON>'s been kind and patient and we're giving her updates / ETAs along the way."}]}]}]}, {"ts": "**********.193739", "text": "<@U04DKEFP1K8> Eng priorities for next day are updated on the <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities board>\n• Highest priority continues to be *Password Reset flow*, since we're about to open up to ~10 employees at SDF with local login\n• Make sure the \"In progress\" items are treated as higher priority to complete before pulling any new \"To dos\" since none of those are higher priority tix\n• Pulled in some of the next several tickets in priority from Wave 3 to make sure we're focused on getting through that wave\n• If all tickets on this board are done, or someone is done and all others are assigned, they can continue working on <https://compiify.atlassian.net/browse/COM-2087|Wave 3> tickets.\n", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FnW16", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are updated on the "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities board"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Highest priority continues to be"}, {"type": "text", "text": " Password Reset flow", "style": {"bold": true}}, {"type": "text", "text": ", since we're about to open up to ~10 employees at SDF with local login"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Make sure the \"In progress\" items are treated as higher priority to complete before pulling any new \"To dos\" since none of those are higher priority tix"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Pulled in some of the next several tickets in priority from Wave 3 to make sure we're focused on getting through that wave"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "If all tickets on this board are done, or someone is done and all others are assigned, they can continue working on "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " tickets."}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1707351552.438879", "text": "FYI: Here's what \"<https://docs.google.com/presentation/d/1eKN9U_ENFZB_kPUm3c0KNUI3awMe31_Fv_xND6uTCB4/edit#slide=id.g2317715dbc6_0_784|Compiify training>\" looks like for SDF's leaders. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1707351552.438879", "reply_count": 5, "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}, {"name": "moneybag", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "blob-yes", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06HBQ0SZD5", "created": 1707351554, "timestamp": 1707351554, "name": "Manager Guide to Compiify", "title": "Manager Guide to Compiify", "mimetype": "application/vnd.google-apps.presentation", "filetype": "gpres", "pretty_type": "Google Slides", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 51632, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1eKN9U_ENFZB_kPUm3c0KNUI3awMe31_Fv_xND6uTCB4", "external_url": "https://docs.google.com/presentation/d/1eKN9U_ENFZB_kPUm3c0KNUI3awMe31_Fv_xND6uTCB4/edit#slide=id.g2317715dbc6_0_784", "url_private": "https://docs.google.com/presentation/d/1eKN9U_ENFZB_kPUm3c0KNUI3awMe31_Fv_xND6uTCB4/edit#slide=id.g2317715dbc6_0_784", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_360.png", "thumb_360_w": 360, "thumb_360_h": 203, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_480.png", "thumb_480_w": 480, "thumb_480_h": 270, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_720.png", "thumb_720_w": 720, "thumb_720_h": 405, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_800.png", "thumb_800_w": 800, "thumb_800_h": 450, "original_w": 800, "original_h": 450, "thumb_tiny": "AwAbADC3g56/pS1VnT9+3Lghc8dKiPX70tcvs2Xcv0VUSIzs5yydyKsxp5aBdxb3NTKNhpjqKKKkBdx9aNx9TSUU7sBdx9aSiii9wCiiikB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06HBQ0SZD5/manager_guide_to_compiify", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "FYI: Here's what \""}, {"type": "link", "url": "https://docs.google.com/presentation/d/1eKN9U_ENFZB_kPUm3c0KNUI3awMe31_Fv_xND6uTCB4/edit#slide=id.g2317715dbc6_0_784", "text": "Compiify training"}, {"type": "text", "text": "\" looks like for SDF's leaders. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1707350331.595019", "text": "but we can be flexible there as we are still early", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1707350331.595019", "reply_count": 2, "edited": {"user": "U0690EB5JE5", "ts": "1707350382.000000"}, "blocks": [{"type": "rich_text", "block_id": "MO/Ll", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "but we can be flexible there as we are still early"}]}]}]}, {"ts": "1707350315.598739", "text": "Yes. Also as I said earlier should we show the integrations as well that we haven't tested?. Example. ADP we need to test the integration before we show it to customers on supported list.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hxsfR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes. Also as I said earlier should we show the integrations as well that we haven't tested?. Example. ADP we need to test the integration before we show it to customers on supported list."}]}]}]}, {"ts": "1707350169.634329", "text": "I think 10 to 15 is what we start with and then increase it gradually ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zVmxq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think 10 to 15 is what we start with and then increase it gradually "}]}]}]}, {"ts": "1707350140.992979", "text": "60 might be too much given we are so early and it can create a dent on our credibility ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "bi/nL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "60 might be too much given we are so early and it can create a dent on our credibility "}]}]}]}, {"ts": "1707350049.737899", "text": "there is no timeline confirmed on that though", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1707350110.000000"}, "blocks": [{"type": "rich_text", "block_id": "iAyom", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "there is no timeline confirmed on that though"}]}]}]}, {"ts": "1707350036.424309", "text": "And reg. <PERSON><PERSON><PERSON>, from what I heard, <PERSON><PERSON> is working with <PERSON><PERSON><PERSON> on a formal partnership.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1707350075.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "OUi8a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And reg. <PERSON><PERSON><PERSON>, from what I heard, <PERSON><PERSON> is working with <PERSON><PERSON><PERSON> on a formal partnership."}]}]}]}, {"ts": "1707349985.496729", "text": "<@U065H3M6WJV> I think we shouldn't show all the integrations that supported by Merge but by us as every integration though effort is very less it would need an effort to test enable any integration.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+R4jD", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I think we shouldn't show all the integrations that supported by Merge but by us as every integration though effort is very less it would need an effort to test enable any integration."}]}]}]}, {"ts": "1707349500.809599", "text": "Ok but for now -- in terms of what we can \"light up\" when Merge is online -- which of their ~60 supported ones do you want to have most prominent? Or do you prefer to present everything with equal real estate?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nBpDm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok but for now -- in terms of what we can \"light up\" when Merge is online -- which of their ~60 supported ones do you want to have most prominent? Or do you prefer to present everything with equal real estate?"}]}]}]}, {"ts": "1707349400.292739", "text": "Thats what they said but we might have to build that integration natively", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zpq4O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thats what they said but we might have to build that integration natively"}]}]}]}, {"ts": "1707349121.606599", "text": "They don't actually support <PERSON><PERSON><PERSON> though, unless I'm mistaken? Wasn't <PERSON><PERSON><PERSON> the one that is retracting API access / not providing it to new clients?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wDNGw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They don't actually support <PERSON><PERSON><PERSON> though, unless I'm mistaken? Wasn't <PERSON><PERSON><PERSON> the one that is retracting API access / not providing it to new clients?"}]}]}]}, {"ts": "1707349015.425919", "text": "Add SFTP connector too", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9GE+C", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Add SFTP connector too"}]}]}]}, {"ts": "**********.508609", "text": "Rippling", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "utHTH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Rippling"}]}]}]}, {"ts": "**********.690089", "text": "Integration-related question: Merge lists around <https://www.merge.dev/categories/hr-payroll-api|60 different HRIS and payroll providers >that they'll support. Do we expect to show all of these logos in our integrations section, or should we just highlight the top 10-20 using logos and have the others less prominent?\n\nAnd if we wanted to highlight a shorter list, which ones are most important to show?\n• ADP\n• BambooHR\n• Gusto\n• HiBob\n• Paylocity\n• UKG\n• Workday\n• Zenefits\n• ?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "guWMJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Integration-related question: <PERSON><PERSON> lists around "}, {"type": "link", "url": "https://www.merge.dev/categories/hr-payroll-api", "text": "60 different HRIS and payroll providers "}, {"type": "text", "text": "that they'll support. Do we expect to show all of these logos in our integrations section, or should we just highlight the top 10-20 using logos and have the others less prominent?\n\nAnd if we wanted to highlight a shorter list, which ones are most important to show?\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ADP"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "BambooHR"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON>"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON>"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Paylocity"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "UKG"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Workday"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Zenefits"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.583473"}