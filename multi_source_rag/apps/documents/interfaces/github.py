"""
GitHub interface for document source.
"""

import hashlib
import json
import logging
import re
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlparse

from django.utils import timezone
from github import Github, GithubException, RateLimitExceededException
from github.Issue import Issue
from github.PullRequest import PullRequest
from github.Repository import Repository

from .base import DocumentSourceInterface

# Set up logging
logger = logging.getLogger(__name__)


class DateTimeEncoder(json.JSONEncoder):
    """
    Custom JSON encoder that handles datetime objects.
    """

    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


class RateLimitHandler:
    """
    Handler for GitHub API rate limiting.
    """

    def __init__(self, max_retries: int = 3, retry_delay: int = 5):
        """
        Initialize the rate limit handler.

        Args:
            max_retries: Maximum number of retries
            retry_delay: Delay between retries in seconds
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def handle_rate_limit(self, client: Github) -> bool:
        """
        Handle rate limit by waiting for reset.

        Args:
            client: GitHub client

        Returns:
            bool: True if rate limit was handled, False otherwise
        """
        try:
            rate_limit = client.get_rate_limit()
            reset_timestamp = rate_limit.core.reset.timestamp()
            current_timestamp = datetime.now().timestamp()

            # Calculate wait time
            wait_time = reset_timestamp - current_timestamp

            if wait_time > 0:
                logger.warning(
                    f"GitHub API rate limit reached. Waiting {wait_time:.2f} seconds for reset."
                )
                time.sleep(wait_time + 1)  # Add 1 second buffer
                return True

            return False
        except Exception as e:
            logger.error(f"Error handling rate limit: {str(e)}")
            return False


class GitHubSourceInterface(DocumentSourceInterface):
    """
    Interface for GitHub document source.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the GitHub source interface.

        Args:
            config: Configuration for the GitHub source
        """
        super().__init__(config)
        self.token = config.get("token")
        self.owner = config.get("owner")
        self.repo = config.get("repo")
        self.client = None
        self.rate_limit_handler = RateLimitHandler()
        self.initialize_client()

    def initialize_client(self) -> None:
        """
        Initialize the GitHub client.
        """
        if not self.token:
            raise ValueError("GitHub token is required")

        try:
            self.client = Github(self.token, per_page=100)
        except Exception as e:
            raise ValueError(f"Failed to initialize GitHub client: {str(e)}")

    def validate_config(self) -> bool:
        """
        Validate the configuration.

        Returns:
            bool: True if the configuration is valid, False otherwise
        """
        required_fields = ["token", "owner", "repo"]
        for field in required_fields:
            if not self.config.get(field):
                return False

        return True

    def get_source_type(self) -> str:
        """
        Get the type of the source.

        Returns:
            str: Type of the source
        """
        return "github"

    def fetch_documents(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch documents from GitHub.

        Args:
            **kwargs: Additional arguments for fetching documents
                - days: Number of days to fetch (default: 30)
                - content_types: Types of content to fetch (default: ['pull_request', 'issue'])
                - state: State of PRs/issues to fetch (default: 'all')
                - since: Fetch documents since this date (datetime object)

        Returns:
            List[Dict[str, Any]]: List of documents
        """
        if not self.client:
            self.initialize_client()

        # Parse arguments
        days = kwargs.get("days", 30)
        content_types = kwargs.get("content_types", ["pull_request", "issue"])
        state = kwargs.get("state", "all")
        since = kwargs.get("since")

        # Calculate since date if not provided
        if not since:
            since = datetime.now() - timedelta(days=days)
        # Convert string date to datetime if needed
        elif isinstance(since, str):
            try:
                since = datetime.fromisoformat(since.replace("Z", "+00:00"))
            except ValueError:
                # Try different format if ISO format fails
                try:
                    since = datetime.strptime(since, "%Y-%m-%d")
                except ValueError:
                    logger.warning(
                        f"Could not parse since date: {since}, using default"
                    )
                    since = datetime.now() - timedelta(days=days)

        documents = []

        try:
            # Get repository
            repo = self.client.get_repo(f"{self.owner}/{self.repo}")

            # Fetch pull requests
            if "pull_request" in content_types:
                prs = self._fetch_pull_requests(repo, state, since)
                documents.extend(prs)

            # Fetch issues (excluding PRs)
            if "issue" in content_types:
                issues = self._fetch_issues(repo, state, since)
                documents.extend(issues)

            return documents

        except RateLimitExceededException:
            # Handle rate limit
            if self.rate_limit_handler.handle_rate_limit(self.client):
                # Retry after waiting
                return self.fetch_documents(**kwargs)
            else:
                raise ValueError(
                    "GitHub API rate limit exceeded and could not be handled"
                )

        except GithubException as e:
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            raise ValueError(f"Error fetching documents from GitHub: {str(e)}")

    def get_document(self, document_id: str, **kwargs) -> Dict[str, Any]:
        """
        Get a specific document from GitHub.

        Args:
            document_id: ID of the document to get
            **kwargs: Additional arguments for getting the document
                - content_type: Type of content ('pull_request' or 'issue')

        Returns:
            Dict[str, Any]: Document
        """
        if not self.client:
            self.initialize_client()

        content_type = kwargs.get("content_type")

        try:
            # Get repository
            repo = self.client.get_repo(f"{self.owner}/{self.repo}")

            # Parse document ID
            parts = document_id.split("/")
            if len(parts) != 2:
                raise ValueError(
                    f"Invalid document ID: {document_id}. Expected format: 'type/number'"
                )

            doc_type, number = parts
            number = int(number)

            # Get document based on type
            if doc_type == "pr" or (not content_type and doc_type == "pull"):
                return self._process_pull_request(repo.get_pull(number))
            elif doc_type == "issue" or (not content_type and doc_type == "issues"):
                return self._process_issue(repo.get_issue(number))
            else:
                raise ValueError(f"Unsupported document type: {doc_type}")

        except RateLimitExceededException:
            # Handle rate limit
            if self.rate_limit_handler.handle_rate_limit(self.client):
                # Retry after waiting
                return self.get_document(document_id, **kwargs)
            else:
                raise ValueError(
                    "GitHub API rate limit exceeded and could not be handled"
                )

        except GithubException as e:
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            raise ValueError(f"Error getting document from GitHub: {str(e)}")

    def search_documents(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for documents in GitHub.

        Args:
            query: Query to search for
            **kwargs: Additional arguments for searching documents
                - content_types: Types of content to search (default: ['pull_request', 'issue'])
                - state: State of PRs/issues to search (default: 'all')
                - max_results: Maximum number of results to return (default: 100)

        Returns:
            List[Dict[str, Any]]: List of documents
        """
        if not self.client:
            self.initialize_client()

        # Parse arguments
        content_types = kwargs.get("content_types", ["pull_request", "issue"])
        state = kwargs.get("state", "all")
        max_results = kwargs.get("max_results", 100)

        documents = []

        try:
            # Construct search query
            search_query = f"{query} repo:{self.owner}/{self.repo}"
            if state != "all":
                search_query += f" state:{state}"

            # Search for issues and PRs
            search_results = self.client.search_issues(search_query)

            # Process results
            count = 0
            for item in search_results:
                if count >= max_results:
                    break

                # Check if it's a PR or issue
                is_pr = hasattr(item, "pull_request") and item.pull_request is not None

                if (is_pr and "pull_request" in content_types) or (
                    not is_pr and "issue" in content_types
                ):
                    # Get full PR or issue
                    if is_pr:
                        pr_number = item.number
                        repo = self.client.get_repo(f"{self.owner}/{self.repo}")
                        document = self._process_pull_request(repo.get_pull(pr_number))
                    else:
                        document = self._process_issue(item)

                    documents.append(document)
                    count += 1

            return documents

        except RateLimitExceededException:
            # Handle rate limit
            if self.rate_limit_handler.handle_rate_limit(self.client):
                # Retry after waiting
                return self.search_documents(query, **kwargs)
            else:
                raise ValueError(
                    "GitHub API rate limit exceeded and could not be handled"
                )

        except GithubException as e:
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            raise ValueError(f"Error searching documents in GitHub: {str(e)}")

    def _fetch_pull_requests(
        self, repo: Repository, state: str, since: datetime
    ) -> List[Dict[str, Any]]:
        """
        Fetch pull requests from the repository.

        Args:
            repo: GitHub repository
            state: State of PRs to fetch ('open', 'closed', 'all')
            since: Fetch PRs updated since this date

        Returns:
            List[Dict[str, Any]]: List of processed pull requests
        """
        documents = []

        try:
            # Get pull requests
            pulls = repo.get_pulls(state=state, sort="updated", direction="desc")

            # Process each PR
            for pr in pulls:
                # Skip if PR was last updated before since date
                # Make sure both dates are timezone-aware for comparison
                pr_updated_at = pr.updated_at
                if timezone.is_naive(pr_updated_at):
                    pr_updated_at = timezone.make_aware(pr_updated_at)

                since_aware = since
                if timezone.is_naive(since_aware):
                    since_aware = timezone.make_aware(since_aware)

                if pr_updated_at < since_aware:
                    break

                document = self._process_pull_request(pr)
                documents.append(document)

            return documents

        except Exception as e:
            logger.error(f"Error fetching pull requests: {str(e)}")
            raise

    def _fetch_issues(
        self, repo: Repository, state: str, since: datetime
    ) -> List[Dict[str, Any]]:
        """
        Fetch issues from the repository (excluding PRs).

        Args:
            repo: GitHub repository
            state: State of issues to fetch ('open', 'closed', 'all')
            since: Fetch issues updated since this date

        Returns:
            List[Dict[str, Any]]: List of processed issues
        """
        documents = []

        try:
            # Get issues
            issues = repo.get_issues(state=state, sort="updated", direction="desc")

            # Process each issue
            for issue in issues:
                # Skip if issue was last updated before since date
                # Make sure both dates are timezone-aware for comparison
                issue_updated_at = issue.updated_at
                if timezone.is_naive(issue_updated_at):
                    issue_updated_at = timezone.make_aware(issue_updated_at)

                since_aware = since
                if timezone.is_naive(since_aware):
                    since_aware = timezone.make_aware(since_aware)

                if issue_updated_at < since_aware:
                    break

                # Skip if it's a PR (issues endpoint returns PRs too)
                if hasattr(issue, "pull_request") and issue.pull_request is not None:
                    continue

                document = self._process_issue(issue)
                documents.append(document)

            return documents

        except Exception as e:
            logger.error(f"Error fetching issues: {str(e)}")
            raise

    def _process_pull_request(self, pr: PullRequest) -> Dict[str, Any]:
        """
        Process a pull request into a document.

        Args:
            pr: GitHub pull request

        Returns:
            Dict[str, Any]: Processed document

        Raises:
            ValueError: If there's an error processing the pull request
        """
        try:
            # Extract PR data
            pr_id = f"pr/{pr.number}"
            title = pr.title
            body = pr.body or ""

            # Prepare PR data for cleaning
            pr_data = {
                "id": pr.id,
                "number": pr.number,
                "title": title,
                "body": body,
                "state": pr.state,
                "created_at": pr.created_at.isoformat() if pr.created_at else None,
                "updated_at": pr.updated_at.isoformat() if pr.updated_at else None,
                "closed_at": pr.closed_at.isoformat() if pr.closed_at else None,
                "merged_at": pr.merged_at.isoformat() if pr.merged_at else None,
                "html_url": pr.html_url,
                "user": {
                    "id": pr.user.id,
                    "login": pr.user.login,
                    "avatar_url": pr.user.avatar_url,
                    "html_url": pr.user.html_url,
                },
                "labels": [
                    {"name": label.name, "color": label.color} for label in pr.labels
                ],
                "draft": pr.draft,
                "merged": pr.merged,
                "mergeable": pr.mergeable,
                "mergeable_state": pr.mergeable_state,
                "comments": pr.comments,
                "review_comments": pr.review_comments,
                "commits": pr.commits,
                "additions": pr.additions,
                "deletions": pr.deletions,
                "changed_files": pr.changed_files,
                "type": "pull_request",
            }

            # Add assignees if available
            if pr.assignees:
                pr_data["assignees"] = [
                    {
                        "id": assignee.id,
                        "login": assignee.login,
                        "avatar_url": assignee.avatar_url,
                        "html_url": assignee.html_url,
                    }
                    for assignee in pr.assignees
                ]

            # Add requested reviewers if available
            if hasattr(pr, "get_review_requests"):
                reviewers, _ = pr.get_review_requests()
                pr_data["requested_reviewers"] = [
                    {
                        "id": reviewer.id,
                        "login": reviewer.login,
                        "avatar_url": reviewer.avatar_url,
                        "html_url": reviewer.html_url,
                    }
                    for reviewer in reviewers
                ]

            # Process comments
            comments_data = []
            for comment in pr.get_comments():
                if not self._is_bot_comment(comment):
                    comment_data = {
                        "id": comment.id,
                        "body": comment.body,
                        "created_at": (
                            comment.created_at.isoformat()
                            if comment.created_at
                            else None
                        ),
                        "updated_at": (
                            comment.updated_at.isoformat()
                            if comment.updated_at
                            else None
                        ),
                        "html_url": comment.html_url,
                        "user": {
                            "id": comment.user.id,
                            "login": comment.user.login,
                            "avatar_url": comment.user.avatar_url,
                            "html_url": comment.user.html_url,
                        },
                    }
                    comments_data.append(comment_data)

            pr_data["comments"] = comments_data

            # Process review comments
            review_comments_data = []
            for comment in pr.get_review_comments():
                if not self._is_bot_comment(comment):
                    comment_data = {
                        "id": comment.id,
                        "body": comment.body,
                        "created_at": (
                            comment.created_at.isoformat()
                            if comment.created_at
                            else None
                        ),
                        "updated_at": (
                            comment.updated_at.isoformat()
                            if comment.updated_at
                            else None
                        ),
                        "html_url": comment.html_url,
                        "user": {
                            "id": comment.user.id,
                            "login": comment.user.login,
                            "avatar_url": comment.user.avatar_url,
                            "html_url": comment.user.html_url,
                        },
                        "path": comment.path,
                        "position": comment.position,
                        "commit_id": comment.commit_id,
                        "diff_hunk": comment.diff_hunk,
                    }
                    review_comments_data.append(comment_data)

            pr_data["review_comments"] = review_comments_data

            # Process files
            files_data = []
            for file in pr.get_files():
                file_data = {
                    "filename": file.filename,
                    "status": file.status,
                    "additions": file.additions,
                    "deletions": file.deletions,
                    "changes": file.changes,
                    "blob_url": file.blob_url,
                    "raw_url": file.raw_url,
                    "contents_url": file.contents_url,
                    "patch": file.patch,
                }
                files_data.append(file_data)

            pr_data["files"] = files_data

            # Use the GitHub cleaners if available
            try:
                from ..cleaners.chunking import SemanticChunker
                from ..cleaners.github import (GitHubContentQualityAssessor,
                                               GitHubPRCleaner)

                # Initialize cleaners
                pr_cleaner = GitHubPRCleaner()
                quality_assessor = GitHubContentQualityAssessor()
                chunker = SemanticChunker(max_chunk_size=1000, overlap=100)

                # Clean and enhance the PR
                cleaned_pr = pr_cleaner.clean_pr(pr_data)

                # Assess quality
                quality = quality_assessor.assess_quality(cleaned_pr)
                cleaned_pr["quality"] = quality

                # Create chunks
                chunks = chunker.chunk_content(cleaned_pr)

                # Create document with enhanced data
                document = {
                    "id": pr_id,
                    "title": title,
                    "content": cleaned_pr.get("body", body),
                    "content_type": "github_pr",
                    "metadata": {
                        "number": pr.number,
                        "state": pr.state,
                        "created_at": pr.created_at,
                        "updated_at": pr.updated_at,
                        "closed_at": pr.closed_at,
                        "merged_at": pr.merged_at,
                        "merged": pr.merged,
                        "mergeable": pr.mergeable,
                        "author": self._get_user_info(pr.user),
                        "assignees": [
                            self._get_user_info(assignee) for assignee in pr.assignees
                        ],
                        "requested_reviewers": (
                            [
                                self._get_user_info(reviewer)
                                for reviewer in pr.get_review_requests()[0]
                            ]
                            if hasattr(pr, "get_review_requests")
                            else []
                        ),
                        "labels": [label.name for label in pr.labels],
                        "comments": cleaned_pr.get("comments", []),
                        "review_comments": cleaned_pr.get("review_comments", []),
                        "commits": pr.commits,
                        "additions": pr.additions,
                        "deletions": pr.deletions,
                        "changed_files": pr.changed_files,
                        "quality_score": quality.get("quality_score", 0.0),
                        "is_high_quality": quality.get("is_high_quality", False),
                        "quality_reasons": quality.get("reasons", []),
                        "technical_entities": cleaned_pr.get("technical_entities", {}),
                        "chunks": chunks,
                    },
                    "source": "github",
                    "url": pr.html_url,
                }
            except ImportError:
                # Fall back to basic processing
                cleaned_content = self._clean_markdown_content(body)
                template_fields = self._extract_pr_template_fields(cleaned_content)
                references = self._extract_references(cleaned_content)

                # Process comments for basic document
                comments = []
                try:
                    for comment in pr.get_issue_comments():
                        if not self._is_bot_comment(comment):
                            comments.append(
                                {
                                    "id": comment.id,
                                    "user": self._get_user_info(comment.user),
                                    "body": comment.body,
                                    "created_at": comment.created_at,
                                    "updated_at": comment.updated_at,
                                }
                            )
                except Exception as e:
                    logger.warning(f"Error fetching PR comments: {e}")

                # Process reviews for basic document
                reviews = []
                for review in pr.get_reviews():
                    if not self._is_bot_comment(review):
                        reviews.append(
                            {
                                "id": review.id,
                                "user": self._get_user_info(review.user),
                                "body": review.body,
                                "state": review.state,
                                "submitted_at": review.submitted_at,
                            }
                        )

                # Create basic document
                document = {
                    "id": pr_id,
                    "title": title,
                    "content": cleaned_content,
                    "content_type": "github_pr",
                    "metadata": {
                        "number": pr.number,
                        "state": pr.state,
                        "created_at": pr.created_at,
                        "updated_at": pr.updated_at,
                        "closed_at": pr.closed_at,
                        "merged_at": pr.merged_at,
                        "merged": pr.merged,
                        "mergeable": pr.mergeable,
                        "author": self._get_user_info(pr.user),
                        "assignees": [
                            self._get_user_info(assignee) for assignee in pr.assignees
                        ],
                        "requested_reviewers": (
                            [
                                self._get_user_info(reviewer)
                                for reviewer in pr.get_review_requests()[0]
                            ]
                            if hasattr(pr, "get_review_requests")
                            else []
                        ),
                        "labels": [label.name for label in pr.labels],
                        "template_fields": template_fields,
                        "references": references,
                        "comments": comments,
                        "reviews": reviews,
                        "commits": pr.commits,
                        "additions": pr.additions,
                        "deletions": pr.deletions,
                        "changed_files": pr.changed_files,
                        "quality_score": self._calculate_quality_score(pr),
                    },
                    "source": "github",
                    "url": pr.html_url,
                }

            return document

        except Exception as e:
            logger.error(f"Error processing pull request {pr.number}: {str(e)}")
            raise

    def _process_issue(self, issue: Issue) -> Dict[str, Any]:
        """
        Process an issue into a document.

        Args:
            issue: GitHub issue

        Returns:
            Dict[str, Any]: Processed document

        Raises:
            ValueError: If there's an error processing the issue
        """
        try:
            # Extract issue data
            issue_id = f"issue/{issue.number}"
            title = issue.title
            body = issue.body or ""

            # Prepare issue data for cleaning
            issue_data = {
                "id": issue.id,
                "number": issue.number,
                "title": title,
                "body": body,
                "state": issue.state,
                "created_at": (
                    issue.created_at.isoformat() if issue.created_at else None
                ),
                "updated_at": (
                    issue.updated_at.isoformat() if issue.updated_at else None
                ),
                "closed_at": issue.closed_at.isoformat() if issue.closed_at else None,
                "html_url": issue.html_url,
                "user": {
                    "id": issue.user.id,
                    "login": issue.user.login,
                    "avatar_url": issue.user.avatar_url,
                    "html_url": issue.user.html_url,
                },
                "labels": [
                    {"name": label.name, "color": label.color} for label in issue.labels
                ],
                "comments": issue.comments,
                "locked": issue.locked,
                "type": "issue",
            }

            # Add assignees if available
            if issue.assignees:
                issue_data["assignees"] = [
                    {
                        "id": assignee.id,
                        "login": assignee.login,
                        "avatar_url": assignee.avatar_url,
                        "html_url": assignee.html_url,
                    }
                    for assignee in issue.assignees
                ]

            # Add milestone if available
            if issue.milestone:
                issue_data["milestone"] = {
                    "id": issue.milestone.id,
                    "number": issue.milestone.number,
                    "title": issue.milestone.title,
                    "state": issue.milestone.state,
                    "created_at": (
                        issue.milestone.created_at.isoformat()
                        if issue.milestone.created_at
                        else None
                    ),
                    "updated_at": (
                        issue.milestone.updated_at.isoformat()
                        if issue.milestone.updated_at
                        else None
                    ),
                    "due_on": (
                        issue.milestone.due_on.isoformat()
                        if issue.milestone.due_on
                        else None
                    ),
                }

            # Process comments
            comments_data = []
            for comment in issue.get_comments():
                if not self._is_bot_comment(comment):
                    comment_data = {
                        "id": comment.id,
                        "body": comment.body,
                        "created_at": (
                            comment.created_at.isoformat()
                            if comment.created_at
                            else None
                        ),
                        "updated_at": (
                            comment.updated_at.isoformat()
                            if comment.updated_at
                            else None
                        ),
                        "html_url": comment.html_url,
                        "user": {
                            "id": comment.user.id,
                            "login": comment.user.login,
                            "avatar_url": comment.user.avatar_url,
                            "html_url": comment.user.html_url,
                        },
                    }

                    # Add reactions if available
                    if hasattr(comment, "reactions") and comment.reactions:
                        comment_data["reactions"] = {
                            "total_count": comment.reactions.total_count,
                            "+1": comment.reactions.plus1,
                            "-1": comment.reactions.minus1,
                            "laugh": comment.reactions.laugh,
                            "hooray": comment.reactions.hooray,
                            "confused": comment.reactions.confused,
                            "heart": comment.reactions.heart,
                            "rocket": comment.reactions.rocket,
                            "eyes": comment.reactions.eyes,
                        }

                    comments_data.append(comment_data)

            issue_data["comments_data"] = comments_data

            # Use the GitHub cleaners if available
            try:
                from ..cleaners.chunking import SemanticChunker
                from ..cleaners.github import (GitHubContentQualityAssessor,
                                               GitHubIssueCleaner)

                # Initialize cleaners
                issue_cleaner = GitHubIssueCleaner()
                quality_assessor = GitHubContentQualityAssessor()
                chunker = SemanticChunker(max_chunk_size=1000, overlap=100)

                # Clean and enhance the issue
                cleaned_issue = issue_cleaner.clean_issue(issue_data)

                # Assess quality
                quality = quality_assessor.assess_quality(cleaned_issue)
                cleaned_issue["quality"] = quality

                # Create chunks
                chunks = chunker.chunk_content(cleaned_issue)

                # Create document with enhanced data
                document = {
                    "id": issue_id,
                    "title": title,
                    "content": cleaned_issue.get("body", body),
                    "content_type": "github_issue",
                    "metadata": {
                        "number": issue.number,
                        "state": issue.state,
                        "created_at": issue.created_at,
                        "updated_at": issue.updated_at,
                        "closed_at": issue.closed_at,
                        "author": self._get_user_info(issue.user),
                        "assignees": [
                            self._get_user_info(assignee)
                            for assignee in issue.assignees
                        ],
                        "labels": [label.name for label in issue.labels],
                        "comments": cleaned_issue.get("comments", []),
                        "quality_score": quality.get("quality_score", 0.0),
                        "is_high_quality": quality.get("is_high_quality", False),
                        "quality_reasons": quality.get("reasons", []),
                        "technical_entities": cleaned_issue.get(
                            "technical_entities", {}
                        ),
                        "chunks": chunks,
                    },
                    "source": "github",
                    "url": issue.html_url,
                }
            except ImportError:
                # Fall back to basic processing
                cleaned_content = self._clean_markdown_content(body)
                template_fields = self._extract_issue_template_fields(cleaned_content)
                references = self._extract_references(cleaned_content)

                # Process comments for basic document
                comments = []
                for comment in issue.get_comments():
                    if not self._is_bot_comment(comment):
                        comments.append(
                            {
                                "id": comment.id,
                                "user": self._get_user_info(comment.user),
                                "body": comment.body,
                                "created_at": comment.created_at,
                                "updated_at": comment.updated_at,
                            }
                        )

                # Create basic document
                document = {
                    "id": issue_id,
                    "title": title,
                    "content": cleaned_content,
                    "content_type": "github_issue",
                    "metadata": {
                        "number": issue.number,
                        "state": issue.state,
                        "created_at": issue.created_at,
                        "updated_at": issue.updated_at,
                        "closed_at": issue.closed_at,
                        "author": self._get_user_info(issue.user),
                        "assignees": [
                            self._get_user_info(assignee)
                            for assignee in issue.assignees
                        ],
                        "labels": [label.name for label in issue.labels],
                        "template_fields": template_fields,
                        "references": references,
                        "comments": comments,
                        "quality_score": self._calculate_quality_score(issue),
                    },
                    "source": "github",
                    "url": issue.html_url,
                }

            return document

        except Exception as e:
            logger.error(f"Error processing issue {issue.number}: {str(e)}")
            raise

    def _clean_markdown_content(self, content: str) -> str:
        """
        Clean and normalize markdown content.

        Args:
            content: Markdown content to clean

        Returns:
            str: Cleaned content
        """
        if not content:
            return ""

        # Remove HTML comments
        content = re.sub(r"<!--.*?-->", "", content, flags=re.DOTALL)

        # Normalize line endings
        content = content.replace("\r\n", "\n")

        # Remove excessive whitespace
        content = re.sub(r"\n{3,}", "\n\n", content)

        # Normalize code blocks
        content = re.sub(r"```\s*(\w+)\s*\n", r"```\1\n", content)

        return content.strip()

    def _extract_pr_template_fields(self, description: str) -> dict:
        """
        Extract structured fields from PR template.

        Args:
            description: PR description

        Returns:
            dict: Extracted template fields
        """
        fields = {}

        # Common PR template patterns
        patterns = {
            "summary": r"(?:## Summary|# Summary|### Summary)\s*\n(.*?)(?=\n##|\n#|\Z)",
            "type": r"(?:## Type|# Type|### Type).*?\n(.*?)(?=\n##|\n#|\Z)",
            "breaking_changes": r"(?:## Breaking Changes|# Breaking Changes).*?\n(.*?)(?=\n##|\n#|\Z)",
            "testing": r"(?:## Testing|# Testing|### Testing).*?\n(.*?)(?=\n##|\n#|\Z)",
            "checklist": r"(?:## Checklist|# Checklist).*?\n(.*?)(?=\n##|\n#|\Z)",
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, description, re.MULTILINE | re.DOTALL)
            if match:
                fields[field] = match.group(1).strip()

        return fields

    def _extract_issue_template_fields(self, description: str) -> dict:
        """
        Extract structured fields from issue template.

        Args:
            description: Issue description

        Returns:
            dict: Extracted template fields
        """
        fields = {}

        # Detect template type
        template_type = self._detect_template_type(description)

        # Common issue template patterns
        patterns = {
            "bug_report": {
                "expected_behavior": r"(?:## Expected behavior|# Expected behavior).*?\n(.*?)(?=\n##|\n#|\Z)",
                "actual_behavior": r"(?:## Actual behavior|# Actual behavior).*?\n(.*?)(?=\n##|\n#|\Z)",
                "steps_to_reproduce": r"(?:## Steps to reproduce|# Steps to reproduce).*?\n(.*?)(?=\n##|\n#|\Z)",
                "environment": r"(?:## Environment|# Environment).*?\n(.*?)(?=\n##|\n#|\Z)",
            },
            "feature_request": {
                "problem": r"(?:## Problem|# Problem|### What problem).*?\n(.*?)(?=\n##|\n#|\Z)",
                "solution": r"(?:## Solution|# Solution|### Describe the solution).*?\n(.*?)(?=\n##|\n#|\Z)",
                "alternatives": r"(?:## Alternatives|# Alternatives).*?\n(.*?)(?=\n##|\n#|\Z)",
            },
        }

        if template_type in patterns:
            for field, pattern in patterns[template_type].items():
                match = re.search(pattern, description, re.MULTILINE | re.DOTALL)
                if match:
                    fields[field] = match.group(1).strip()

        return fields

    def _detect_template_type(self, description: str) -> str:
        """
        Detect the type of template used in the description.

        Args:
            description: Issue/PR description

        Returns:
            str: Template type
        """
        # Check for bug report template
        bug_indicators = [
            "expected behavior",
            "actual behavior",
            "steps to reproduce",
            "bug report",
        ]
        for indicator in bug_indicators:
            if indicator.lower() in description.lower():
                return "bug_report"

        # Check for feature request template
        feature_indicators = [
            "feature request",
            "proposed solution",
            "describe the solution",
        ]
        for indicator in feature_indicators:
            if indicator.lower() in description.lower():
                return "feature_request"

        # Default to generic template
        return "generic"

    def _extract_references(self, text: str) -> List[dict]:
        """
        Extract various types of references from text.

        Args:
            text: Text to extract references from

        Returns:
            List[dict]: List of references
        """
        references = []

        # Issue references (#123, fixes #456)
        issue_refs = re.findall(
            r"(?:closes|fixes|resolves|fix|close|resolve)\s*#(\d+)", text, re.IGNORECASE
        )
        for ref in issue_refs:
            references.append({"type": "fixes_issue", "id": ref, "url": f"#{ref}"})

        # PR references (#789)
        pr_refs = re.findall(r"(?<!fixes\s)(?<!closes\s)(?<!resolves\s)#(\d+)", text)
        for ref in pr_refs:
            references.append({"type": "references_pr", "id": ref, "url": f"#{ref}"})

        # External links
        urls = re.findall(r'https?://[^\s<>"{}|\\^`\[\]]+', text)
        for url in urls:
            references.append(
                {"type": "external_link", "url": url, "domain": urlparse(url).netloc}
            )

        return references

    def _is_bot_comment(self, comment) -> bool:
        """
        Check if a comment is from a bot.

        Args:
            comment: GitHub comment

        Returns:
            bool: True if the comment is from a bot, False otherwise
        """
        # Check if comment is None
        if comment is None:
            return False

        # Check if user is a bot
        if hasattr(comment, "user") and comment.user:
            # Check if user.login exists
            if not hasattr(comment.user, "login"):
                return False

            # GitHub marks bot users with '[bot]' in their login
            if "[bot]" in comment.user.login:
                return True

            # Common bot usernames
            bot_usernames = ["github-actions", "dependabot", "codecov", "renovate"]
            if any(bot in comment.user.login.lower() for bot in bot_usernames):
                return True

        # Check comment content for bot signatures
        if hasattr(comment, "body") and comment.body:
            bot_signatures = [
                "This PR was automatically created",
                "This issue was automatically created",
                "Automated pull request",
                "Created by GitHub Action",
            ]
            if any(sig in comment.body for sig in bot_signatures):
                return True

        return False

    def _get_user_info(self, user) -> dict:
        """
        Get user information.

        Args:
            user: GitHub user

        Returns:
            dict: User information
        """
        if not user:
            return {"login": "unknown", "name": "Unknown User", "avatar_url": None}

        # Initialize with default values
        user_info = {"login": "unknown", "name": "Unknown User", "avatar_url": None}

        # Add login if available
        if hasattr(user, "login"):
            user_info["login"] = user.login
            # Default name to login if name not available
            user_info["name"] = user.login

        # Add avatar_url if available
        if hasattr(user, "avatar_url"):
            user_info["avatar_url"] = user.avatar_url

        # Add name if available
        if hasattr(user, "name") and user.name:
            user_info["name"] = user.name

        return user_info

    def _calculate_quality_score(self, item) -> float:
        """
        Calculate a quality score for the item.

        Args:
            item: GitHub item (PR or issue)

        Returns:
            float: Quality score between 0 and 1
        """
        quality_score = 0.0

        # Content length and completeness
        body = item.body or ""
        if len(body) > 100:
            quality_score += 0.2

        # Has proper formatting (headers, lists, etc.)
        if re.search(r"#{1,6}\s+\w+", body) or re.search(r"[-*]\s+\w+", body):
            quality_score += 0.2

        # Contains technical details
        technical_indicators = [
            "code",
            "implementation",
            "bug",
            "error",
            "function",
            "method",
            "class",
            "api",
        ]
        if any(indicator in body.lower() for indicator in technical_indicators):
            quality_score += 0.2

        # Has engagement (comments)
        if hasattr(item, "comments") and item.comments > 0:
            quality_score += 0.1

        # Has labels
        if hasattr(item, "labels") and len(item.labels) > 0:
            quality_score += 0.1

        # Is recent (within last 30 days)
        if hasattr(item, "updated_at") and item.updated_at:
            try:
                # Use timezone-aware comparison
                now = timezone.now()
                updated_at = item.updated_at

                # Make sure updated_at is timezone-aware
                if timezone.is_naive(updated_at):
                    updated_at = timezone.make_aware(updated_at)

                if (now - updated_at).days < 30:
                    quality_score += 0.1
            except Exception as e:
                logger.warning(f"Error calculating recency score: {e}")

        return min(quality_score, 1.0)
