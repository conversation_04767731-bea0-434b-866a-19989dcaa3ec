#!/usr/bin/env python3
"""
Upgrade Embedding Dimensions from 384d to 768d
Handles the migration from all-MiniLM-L6-v2 (384d) to BAAI/bge-base-en-v1.5 (768d)
"""

import os
import sys
import django
from datetime import datetime

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def analyze_current_setup():
    """Analyze the current embedding setup."""

    print("🔍 ANALYZING CURRENT EMBEDDING SETUP")
    print("=" * 60)

    from apps.core.utils.embedding_consistency import get_embedding_model_info
    from apps.core.models import EmbeddingModel
    from apps.documents.models import EmbeddingMetadata

    # Current configuration
    model_info = get_embedding_model_info()
    print(f"📊 Current Model: {model_info['model_name']}")
    print(f"📊 Current Dimensions: {model_info['dimensions']}")

    # Database records
    embedding_models = EmbeddingModel.objects.all()
    print(f"\n📚 Database Embedding Models: {embedding_models.count()}")
    for model in embedding_models:
        print(f"   - {model.model_name}: {model.dimension}d (default: {model.is_default})")

    # Existing embeddings
    embeddings_count = EmbeddingMetadata.objects.count()
    print(f"\n🗄️  Existing Embeddings: {embeddings_count}")

    if embeddings_count > 0:
        sample_embedding = EmbeddingMetadata.objects.first()
        if sample_embedding:
            current_dims = sample_embedding.vector_dimensions
            print(f"   Current vector dimensions: {current_dims}")
        else:
            print("   No embedding metadata found")

    return {
        'current_model': model_info['model_name'],
        'current_dimensions': model_info['dimensions'],
        'embeddings_count': embeddings_count,
        'needs_migration': embeddings_count > 0 and model_info['dimensions'] != 768
    }

def backup_current_embeddings():
    """Create a backup of current embeddings before migration."""

    print("\n💾 CREATING BACKUP OF CURRENT EMBEDDINGS")
    print("=" * 60)

    from apps.documents.models import EmbeddingMetadata
    import json

    embeddings = EmbeddingMetadata.objects.all()
    backup_data = []

    for embedding in embeddings:
        backup_data.append({
            'id': embedding.id,
            'chunk_id': embedding.chunk.id if embedding.chunk else None,
            'vector_id': embedding.vector_id,
            'model_name': embedding.model_name,
            'vector_dimensions': embedding.vector_dimensions,
            'embedded_at': embedding.embedded_at.isoformat(),
        })

    backup_file = f"data/embedding_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    os.makedirs(os.path.dirname(backup_file), exist_ok=True)

    with open(backup_file, 'w') as f:
        json.dump(backup_data, f, indent=2)

    print(f"✅ Backup created: {backup_file}")
    print(f"📊 Backed up {len(backup_data)} embedding records")

    return backup_file

def clear_old_embeddings():
    """Clear old 384d embeddings to prepare for 768d embeddings."""

    print("\n🗑️  CLEARING OLD EMBEDDINGS")
    print("=" * 60)

    from apps.documents.models import EmbeddingMetadata
    from apps.core.models import EmbeddingModel

    # Delete old embedding metadata
    old_count = EmbeddingMetadata.objects.count()
    EmbeddingMetadata.objects.all().delete()
    print(f"✅ Deleted {old_count} old embedding records")

    # Get the default tenant
    from apps.accounts.models import Tenant
    tenant = Tenant.objects.first()
    if not tenant:
        print("❌ No tenant found - creating default tenant")
        tenant = Tenant.objects.create(name="Default", slug="stride")

    # Update or create new embedding model record
    new_model, created = EmbeddingModel.objects.get_or_create(
        model_name="BAAI/bge-base-en-v1.5",
        tenant=tenant,
        defaults={
            'name': 'BGE Base English v1.5',
            'dimension': 768,
            'is_default': True,
        }
    )

    # Set old models as non-default
    EmbeddingModel.objects.exclude(id=new_model.id).update(is_default=False)

    if created:
        print(f"✅ Created new embedding model: {new_model.model_name} (768d)")
    else:
        print(f"✅ Updated existing embedding model: {new_model.model_name} (768d)")

    return new_model

def clear_vector_store():
    """Clear the vector store to remove old 384d vectors."""

    print("\n🗄️  CLEARING VECTOR STORE")
    print("=" * 60)

    try:
        import qdrant_client
        from apps.core.utils.llama_index_vectorstore import get_collection_name

        # Connect to Qdrant
        client = qdrant_client.QdrantClient(host="localhost", port=6333)

        # Get collection name
        collection_name = get_collection_name("stride", intent="conversation")

        # Check if collection exists
        collections = client.get_collections()
        collection_exists = any(col.name == collection_name for col in collections.collections)

        if collection_exists:
            # Delete the collection
            client.delete_collection(collection_name)
            print(f"✅ Deleted vector collection: {collection_name}")
        else:
            print(f"ℹ️  Collection {collection_name} does not exist")

        print("✅ Vector store cleared successfully")

    except Exception as e:
        print(f"⚠️  Error clearing vector store: {e}")
        print("💡 You may need to manually clear the vector store")

def test_new_embedding_model():
    """Test the new 768d embedding model."""

    print("\n🧪 TESTING NEW EMBEDDING MODEL")
    print("=" * 60)

    try:
        from apps.core.utils.embedding_consistency import get_consistent_embedding_model, get_embedding_model_info

        # Get model info
        model_info = get_embedding_model_info()
        print(f"📊 New Model: {model_info['model_name']}")
        print(f"📊 New Dimensions: {model_info['dimensions']}")

        # Test embedding generation
        embedding_model = get_consistent_embedding_model()
        test_text = "This is a test document for embedding generation."

        print(f"🧪 Testing embedding generation...")
        embedding = embedding_model.get_text_embedding(test_text)

        print(f"✅ Generated embedding with {len(embedding)} dimensions")
        print(f"✅ Expected: 768, Actual: {len(embedding)}")

        if len(embedding) == 768:
            print("🎉 Embedding model upgrade successful!")
            return True
        else:
            print("❌ Embedding dimension mismatch!")
            return False

    except Exception as e:
        print(f"❌ Error testing embedding model: {e}")
        return False

def main():
    """Main migration function."""

    print("🚀 EMBEDDING DIMENSION UPGRADE: 384d → 768d")
    print("=" * 80)
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Step 1: Analyze current setup
    analysis = analyze_current_setup()

    if not analysis['needs_migration']:
        print("\n✅ No migration needed - system already using 768d embeddings")
        return

    print(f"\n📋 MIGRATION PLAN:")
    print(f"   - Current: {analysis['current_model']} ({analysis['current_dimensions']}d)")
    print(f"   - Target: BAAI/bge-base-en-v1.5 (768d)")
    print(f"   - Embeddings to migrate: {analysis['embeddings_count']}")

    # Confirm migration
    response = input("\n❓ Proceed with migration? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ Migration cancelled")
        return

    try:
        # Step 2: Backup current embeddings
        backup_file = backup_current_embeddings()

        # Step 3: Clear old embeddings
        new_model = clear_old_embeddings()

        # Step 4: Clear vector store
        clear_vector_store()

        # Step 5: Test new embedding model
        success = test_new_embedding_model()

        if success:
            print("\n🎉 MIGRATION COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print("✅ Upgraded from 384d to 768d embeddings")
            print("✅ Old embeddings backed up")
            print("✅ Vector store cleared")
            print("✅ New embedding model tested")
            print()
            print("🔄 NEXT STEPS:")
            print("1. Re-ingest your data to generate new 768d embeddings")
            print("2. Test search performance with new embeddings")
            print("3. Monitor quality improvements")
            print()
            print("💡 Re-ingestion command:")
            print("   poetry run python scripts/ingest_slack_data.py")
        else:
            print("\n❌ MIGRATION FAILED!")
            print("💡 Check the error messages above and try again")

    except Exception as e:
        print(f"\n❌ Migration failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
