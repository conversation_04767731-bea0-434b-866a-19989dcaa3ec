{"date": "2024-11-21", "channel_id": "C065QSSNH8A", "message_count": 10, "messages": [{"ts": "1732213208.607749", "text": "Timestamps bug <https://compiify.atlassian.net/browse/COM-4000>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14083::47fd0da638e345e49efcc7d0eed7d1e1", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4000?atlOrigin=eyJpIjoiMTVlMDAxY2FmMjliNDFhNWE1ZWNmNjgzZGEwNTdkMGQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4000 Bug in Timestamps in Audit Log>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14083::3df8638a352440f585cc4e78e326f768", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14083\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4000\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4000", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "uPOXJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Timestamps bug "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4000"}]}]}]}, {"ts": "1732212223.222029", "text": "Highest priority degenkolb promotion bug: <https://compiify.atlassian.net/browse/COM-3999>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732212223.222029", "reply_count": 21, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14082::25a4e90e51a54125a109dd5475e0fcf1", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3999?atlOrigin=eyJpIjoiMGQzNDhlMjM5MWQxNDc3NWIwYjZjOWEyZmI5ZmZkY2MiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3999 Bug: Promotion Details Wiped Out After Employee Update>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14082::e7eb7adc8e5946ad8620bd7b907b9466", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14082\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3999\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3999", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "GpJTs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Highest priority degenkolb promotion bug: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3999"}]}]}]}, {"ts": "1732209645.355079", "text": "<@U0690EB5JE5> here is the ticket to include position in range data into the \"full employee export report\"\n<https://compiify.atlassian.net/browse/COM-3998>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1732209645.355079", "reply_count": 6, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14081::b45bf64b1abb4eebaff3720f6dcb1a76", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3998?atlOrigin=eyJpIjoiNjdhYmZmY2M1OWYzNGU5ZWFjNDdhOTY2YzNkMmNmNDAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3998 Add Position in Range Column to Full Employee Data Export Report>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14081::cdaf0194286941eca043d09c176b0a73", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14081\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3998\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3998", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "tp8ND", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " here is the ticket to include position in range data into the \"full employee export report\"\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3998"}]}]}]}, {"ts": "1732208403.828099", "text": "<@U0690EB5JE5> <https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IWg3Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1732207908.073969", "text": "<@U0690EB5JE5> File that failed for diversified:", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F081XETLPA6", "created": 1732207903, "timestamp": 1732207903, "name": "DivEnaddbacksalary.csv", "title": "DivEnaddbacksalary.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 2480, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F081XETLPA6/divenaddbacksalary.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F081XETLPA6/download/divenaddbacksalary.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F081XETLPA6/divenaddbacksalary.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F081XETLPA6-3af41b75a9", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F081XETLPA6/divenaddbacksalary.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">8155</div><div class=\"cm-col\">Nicholas</div><div class=\"cm-col\">Armstrong</div><div class=\"cm-col\">Nicholas Armstrong</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">7/1/16</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">19081</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">7/1/16</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">023- INDIANA</div><div class=\"cm-col\">VP</div><div class=\"cm-col\">AdminSer:Bham:Indiana</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Full Time</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">185612.96</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">74245.18</div><div class=\"cm-col cm-num\">29698.07</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">44547.11</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">29698.07</div><div class=\"cm-col cm-num\">90</div><div class=\"cm-col cm-num\">44547.11</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">89.24</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">8658</div><div class=\"cm-col\">Allan</div><div class=\"cm-col\">Smith</div><div class=\"cm-col\">Allan Smith</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">2/1/14</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16594</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">2/1/14</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">023- INDIANA</div><div class=\"cm-col\">DIRECTOR</div><div class=\"cm-col\">Admin:Indiana</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Full Time</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">126765.6</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">20</div><div class=\"cm-col cm-num\">25353.12</div><div class=\"cm-col cm-num\">10141.25</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">15211.87</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">10141.25</div><div class=\"cm-col cm-num\">97</div><div class=\"cm-col cm-num\">15211.87</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">60.95</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">10242</div><div class=\"cm-col\">Justin</div><div class=\"cm-col\">Smith</div><div class=\"cm-col\">Justin Smith</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">4/18/16</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">17582</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">4/18/16</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">008- CANTON</div><div class=\"cm-col\">DIRECTOR</div><div class=\"cm-col\">Field Land:Canton</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Full Time</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">151965.84</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">20</div><div class=\"cm-col cm-num\">30393.17</div><div class=\"cm-col cm-num\">12157.27</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">18235.9</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">12157.27</div><div class=\"cm-col cm-num\">65</div><div class=\"cm-col cm-num\">18235.9</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">73.06</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">10363</div><div class=\"cm-col\">Kellie</div><div class=\"cm-col\">Green</div><div class=\"cm-col\">Kellie Green</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">2/24/14</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16512</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">2/24/14</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">008- CANTON</div><div class=\"cm-col\">MANAGER</div><div class=\"cm-col\">Accounting:Canton</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Full Time</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">114514.66</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col cm-num\">17177.2</div><div class=\"cm-col cm-num\">6870.88</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">10306.32</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">6870.88</div><div class=\"cm-col cm-num\">89</div><div class=\"cm-col cm-num\">10306.32</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">55.06</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 1, "lines_more": 0, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "owxu6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " File that failed for diversified:"}]}]}]}, {"ts": "1732207904.573399", "text": "both SOC2 type 2 and GDPR", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07NBMXTL1E"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "fvTHS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "both SOC2 type 2 and GDPR"}]}]}]}, {"ts": "1732206014.440879", "text": "Just a quick Q on which security certifications we have - SOC2 Type 2? GDPR?", "user": "U07NBMXTL1E", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6sueQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just a quick Q on which security certifications we have - SOC2 Type 2? GDPR?"}]}]}]}, {"ts": "1732205303.874199", "text": "<@U0690EB5JE5> Users for diversified:\n\nDiversified\t<PERSON>\tEn<PERSON>nkin\tSuper Admin\t<https://div-energy.stridehr.io/>\t<mailto:<EMAIL>|<EMAIL>>\t4xBnR=DC6X1*\t8/21\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\nDiversified\tMark\t<PERSON>\tSuper Admin\t<https://div-energy.stridehr.io/>\t<mailto:<EMAIL>|<EMAIL>>\t~A&lt;9x6~']_36\t8/21", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"from_url": "https://div-energy.stridehr.io/", "service_icon": "https://div-energy.stridehr.io/apple-touch-icon.png", "id": 1, "original_url": "https://div-energy.stridehr.io/", "fallback": "Stride", "text": "Web site created using create-react-app", "title": "Stride", "title_link": "https://div-energy.stridehr.io/", "service_name": "div-energy.stridehr.io"}], "blocks": [{"type": "rich_text", "block_id": "tTsFF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Users for diversified:\n\nDiversified\tCindy\tEntrenkin\tSuper Admin\t"}, {"type": "link", "url": "https://div-energy.stridehr.io/"}, {"type": "text", "text": "\t"}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": "\t4xBnR=DC6X1*\t8/21\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\nDiversified\tMark\t<PERSON>\tSuper Admin\t"}, {"type": "link", "url": "https://div-energy.stridehr.io/"}, {"type": "text", "text": "\t"}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": "\t~A<9x6~']_36\t8/21"}]}]}]}, {"ts": "1732130391.901879", "text": "<@U07EJ2LP44S> this is what D<PERSON> requested:\n\nReorder the ones in *black* for the merit cycle/org information. Do you have the information for the ones in *purple* so we can include them? Questions and comments next to each one.\n\n• *EE ID*\n• *Name*\n• *Comments*\n• *Job Title*\n• *Department*\n• *Manager* (Manager 4?)\n• *Region* (Location for Pay Range. Region in the org chart is different to Region in the merit cycle. It should be location for pay range)\n• *Hire Date*\n• *Tenure* (does this include external experience or just DE experience?)\n• *Full Time* - Is everyone showing as FT? If PT how does that impact pay and ranges? Would we base it on FTE?\n• *Performance Rating* - to be uploaded on 11/12\n• *Eligible for Promotion* (Yes/No) Go to “Yes” if entering promotion\n• *New Job Title* (we need to limit the options here just to next step options or custom job title)\n• *Previous Salary* (do you have this information?)\n• *Last Raise Date* (do you have this information?)\n• *Current Salary* (FTE?)\n• *Promotion Pay Increase* (Can add $ or adjust percentage)\n• *Proposed Salary Increase* (this is not available if entered raise for promotion)\n• *Prorated Salary Increase* – do we need this?\n• *New Salary*\n• *Compa Ratio* (based on mid-point, UKG has primary and secondary added together)\n• *Position in Band* (Hover over and it will show more information about pay ranges)\n• *Target Bonus %* (Do you have this information? If so, can we add it to the merit cycle? <mailto:<EMAIL>|@Aimee>, do you think we should include or remove FAR since we also have PF but we can’t add both?)\n• *Target Bonus $*\n• *Total Target Cas*h\n• *Gender*\n• *Race*\nRemove the following from the managers view either in merit cycle or org information, it’s either not relevant to us or will prompt unnecessary questions!\n\n• ~Status~ - do we need this? Wouldn't everyone be Active?\n• ~Job Category~ (IC/Manager - Do we need this to be included? Are they correct? I see AP/Principals are showing as IC. Could still be useful for reporting)\n• ~Job Leve~l - Do we need to include this? The managers won't understand the levels. Could still be useful for reporting.\n• ~Job Family~\n• ~HRBP~\n• ~Local Currency~\n• ~Team~\n• ~Country~\n• ~Incentives~\n• ~Compensation Type~\n• ~Equity Target Units~\n• ~Equity Target~\n• ~Vested Equirt~\n• ~Unvested Equity~\n", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1732130391.901879", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "sJjT8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " this is what DK requested:\n\nReorder the ones in "}, {"type": "text", "text": "black ", "style": {"bold": true}}, {"type": "text", "text": "for the merit cycle/org information. Do you have the information for the ones in "}, {"type": "text", "text": "purple ", "style": {"bold": true}}, {"type": "text", "text": "so we can include them? Questions and comments next to each one.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "EE ID", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Name", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Comments", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Title", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Department", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Manager", "style": {"bold": true}}, {"type": "text", "text": " (Manager 4?)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Region ", "style": {"bold": true}}, {"type": "text", "text": "(Location for Pay Range. Region in the org chart is different to Region in the merit cycle. It should be location for pay range)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Hire Date", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Tenure", "style": {"bold": true}}, {"type": "text", "text": " (does this include external experience or just DE experience?)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Full Time", "style": {"bold": true}}, {"type": "text", "text": " - Is everyone showing as FT? If PT how does that impact pay and ranges? Would we base it on FTE?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Performance Rating", "style": {"bold": true}}, {"type": "text", "text": " - to be uploaded on 11/12"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Eligible for Promotion", "style": {"bold": true}}, {"type": "text", "text": " (Yes/No) Go to “Yes” if entering promotion"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "New Job Title ", "style": {"bold": true}}, {"type": "text", "text": "(we need to limit the options here just to next step options or custom job title)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Previous Salary ", "style": {"bold": true}}, {"type": "text", "text": "(do you have this information?)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Last Raise Date", "style": {"bold": true}}, {"type": "text", "text": " (do you have this information?)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Current Salary", "style": {"bold": true}}, {"type": "text", "text": " (FTE?)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Promotion Pay Increase ", "style": {"bold": true}}, {"type": "text", "text": "(Can add $ or adjust percentage)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Proposed Salary Increase", "style": {"bold": true}}, {"type": "text", "text": " (this is not available if entered raise for promotion)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Prorated Salary Increase", "style": {"bold": true}}, {"type": "text", "text": " – do we need this?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "New Salary", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Compa Ratio ", "style": {"bold": true}}, {"type": "text", "text": "(based on mid-point, UKG has primary and secondary added together)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Position in Band ", "style": {"bold": true}}, {"type": "text", "text": "(Hover over and it will show more information about pay ranges)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Target Bonus % ", "style": {"bold": true}}, {"type": "text", "text": "(Do you have this information? If so, can we add it to the merit cycle? "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "@Aimee", "unsafe": true}, {"type": "text", "text": ", do you think we should include or remove FAR since we also have PF but we can’t add both?)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Target Bonus $", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Total Target Cas", "style": {"bold": true}}, {"type": "text", "text": "h"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Gender", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Race", "style": {"bold": true}}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nRemove the following from the managers view either in merit cycle or org information, it’s either not relevant to us or will prompt unnecessary questions!\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Status", "style": {"strike": true}}, {"type": "text", "text": " - do we need this? Wouldn't everyone be Active?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Category", "style": {"strike": true}}, {"type": "text", "text": " (IC/Manager - Do we need this to be included? Are they correct? I see AP/Principals are showing as IC. Could still be useful for reporting)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>", "style": {"strike": true}}, {"type": "text", "text": "l - Do we need to include this? The managers won't understand the levels. Could still be useful for reporting."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Family", "style": {"strike": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "HRBP", "style": {"strike": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Local Currency", "style": {"strike": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Team", "style": {"strike": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Country", "style": {"strike": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Incentives", "style": {"strike": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Compensation Type", "style": {"strike": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Equity Target Units", "style": {"strike": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Equity Target", "style": {"strike": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Vested Equirt", "style": {"strike": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Unvested Equity", "style": {"strike": true}}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1732129389.129169", "text": "<@U07EJ2LP44S> can you share the ticket which contains the UI improvements Val<PERSON> requested?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1732129389.129169", "reply_count": 9, "edited": {"user": "U07M6QKHUC9", "ts": "1732129396.000000"}, "blocks": [{"type": "rich_text", "block_id": "/LuAC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can you share the ticket which contains the UI improvements Valgenesis requested?"}]}]}]}], "created_at": "2025-05-22T21:35:34.686044"}