{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2025-03", "message_count": 117, "messages": [{"ts": "1740768454.390989", "text": "<@U0690EB5JE5> <PERSON><PERSON><PERSON> accidentally uploaded someone as an IC instead of a Manager. They changed them, added to the recommenders list, but cannot reassign directs. She's not listed as an option when you try to do so in the org view edit. How can we get this adjusted?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740768454.390989", "reply_count": 8, "files": [{"id": "F08FLQ7FUAX", "created": 1740768444, "timestamp": 1740768444, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 121888, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FLQ7FUAX/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FLQ7FUAX/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FLQ7FUAX-653d3e8c94/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FLQ7FUAX-653d3e8c94/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FLQ7FUAX-653d3e8c94/image_360.png", "thumb_360_w": 360, "thumb_360_h": 135, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FLQ7FUAX-653d3e8c94/image_480.png", "thumb_480_w": 480, "thumb_480_h": 180, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FLQ7FUAX-653d3e8c94/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FLQ7FUAX-653d3e8c94/image_720.png", "thumb_720_w": 720, "thumb_720_h": 270, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FLQ7FUAX-653d3e8c94/image_800.png", "thumb_800_w": 800, "thumb_800_h": 300, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FLQ7FUAX-653d3e8c94/image_960.png", "thumb_960_w": 960, "thumb_960_h": 360, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FLQ7FUAX-653d3e8c94/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 384, "original_w": 1598, "original_h": 600, "thumb_tiny": "AwASADDQ5z0/Wl3N/dH500/ePI/KjB/ytADtx9B+dG5v7o/OkAJ//VRtPqPyoAMn0/WnDPt+dN2n1H5UoBHf8qAG/wAf40+mfx/jT6ACiiigAooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FLQ7FUAX/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FLQ7FUAX-137a91cf32", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "f7HW5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> accidentally uploaded someone as an IC instead of a Manager. They changed them, added to the recommenders list, but cannot reassign directs. She's not listed as an option when you try to do so in the org view edit. How can we get this adjusted?"}]}]}]}, {"ts": "**********.658649", "text": "<@U0690EB5JE5> I am not in front of my computer now so I can’t upload it to slack, but I forwarded you an email from Curana. They would like the file that is attached to be uploaded into their MIP account. ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.658649", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "4xLj8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am not in front of my computer now so I can’t upload it to slack, but I forwarded you an email from Curana. They would like the file that is attached to be uploaded into their MIP account. "}]}]}]}, {"ts": "**********.294799", "text": "Will take care", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QnCVU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take care"}]}]}]}, {"ts": "**********.065529", "text": "<@U07EJ2LP44S> Can I ?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.675179", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "a3nAF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Can I ?"}]}]}]}, {"ts": "**********.922969", "text": "<@U07EJ2LP44S> couple of questions:\n• So we need to generate report with sum of all salaries bottom up and then bonus paid. Could you please help me with mapping to merit table columns to the report above. \n• Need little more clarity on the second table", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1741004713.000000"}, "blocks": [{"type": "rich_text", "block_id": "PxCie", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " couple of questions:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So we need to generate report with sum of all salaries bottom up and then bonus paid. Could you please help me with mapping to merit table columns to the report above. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Need little more clarity on the second table"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1741016402.109749", "text": "<@U07EJ2LP44S> FYI... I will be in a family wedding from My Tuesday evening and I won't be able to connect from laptop at least Wednesday and Thursday. I will come back on how to work around the support for those two days.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741016402.109749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "NbTqw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " FYI... I will be in a family wedding from My Tuesday evening and I won't be able to connect from laptop at least Wednesday and Thursday. I will come back on how to work around the support for those two days."}]}]}]}, {"ts": "1741016436.899319", "text": "I am OOO from Wednesday until end of the week, But will make sure you have the same level of support.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1741016444.000000"}, "blocks": [{"type": "rich_text", "block_id": "b0A2t", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am OOO from Wednesday until end of the week, But will make sure you have the same level of support."}]}]}]}, {"ts": "1741016581.479669", "text": "I have one final request from <PERSON><PERSON><PERSON> on Total rewards:\n\nFor the summary at the top of the salaries table, they would like to see the first salary, previous salary (coming into this cycle), and current salary (after this cycle). Can we list those three items above or below the Total % Increase?\n\nThey would like to keep the salary history just to the summary of 5 (or however many are showing there). They do not like the full list view.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741016581.479669", "reply_count": 6, "edited": {"user": "U07EJ2LP44S", "ts": "1741016723.000000"}, "blocks": [{"type": "rich_text", "block_id": "ghRJo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have one final request from <PERSON><PERSON><PERSON> on Total rewards:\n\nFor the summary at the top of the salaries table, they would like to see the first salary, previous salary (coming into this cycle), and current salary (after this cycle). Can we list those three items above or below the Total % Increase?\n\nThey would like to keep the salary history just to the summary of 5 (or however many are showing there). They do not like the full list view."}]}]}]}, {"ts": "1741016754.980389", "text": "Also I have a couple paybands to load for them, and they want to change all their hourly people to annual. I will work on those sheets", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HN2te", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also I have a couple paybands to load for them, and they want to change all their hourly people to annual. I will work on those sheets"}]}]}]}, {"ts": "**********.365679", "text": "<@U0690EB5JE5> I'm not sure what it is right now, but <PERSON><PERSON><PERSON> just scheduled a call with me in 15 to talk about a bug in the MIP account", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FCzJm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I'm not sure what it is right now, but <PERSON><PERSON><PERSON> just scheduled a call with me in 15 to talk about a bug in the MIP account"}]}]}]}, {"ts": "**********.133129", "text": "Ok. I will be available mostly after one or two hours if there is anything urgent.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sHl53", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. I will be available mostly after one or two hours if there is anything urgent."}]}]}]}, {"ts": "**********.162279", "text": "Ok, thank you. I am not going to be able to get through today on my own, they are all coming at me", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ylup3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok, thank you. I am not going to be able to get through today on my own, they are all coming at me"}]}]}]}, {"ts": "**********.945549", "text": "I have a call with curana, diversified, and a training with tithely, which i'm sure will all bring up additional things", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "T/Ffy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have a call with curana, diversified, and a training with tithely, which i'm sure will all bring up additional things"}]}]}]}, {"ts": "1741017206.424199", "text": "ok", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1741021820.496829", "text": "<@U0690EB5JE5> Tithely Payband Additions:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741021820.496829", "reply_count": 12, "files": [{"id": "F08FU9LPQJZ", "created": 1741021819, "timestamp": 1741021819, "name": "TithelyNewBandsMarch3.csv", "title": "TithelyNewBandsMarch3.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1266, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FU9LPQJZ/tithelynewbandsmarch3.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FU9LPQJZ/download/tithelynewbandsmarch3.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FU9LPQJZ/tithelynewbandsmarch3.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FU9LPQJZ-13c4f5d2f9", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FU9LPQJZ/tithelynewbandsmarch3.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Band Id,Effective Start Date,Effective End Date,Compensation Grade,Location (Country),Location (Region),Team,Department,Job Family,Job Subfamily,Description,Tier,Job Category,Job Level,Job Title,Frequency,Salary Currency,Salary Min Amount,Salary Target Amount,Salary Max Amount,Variable Pay Currency,Variable Pay Target (%),Variable Pay Target Amount,Equity Currency,Equity Target Amount,Equity Target (unit),Equity Target (%),Min Equity (unit),Max Equity (unit),Equity Participation Rate (%),OTE Min Amount,OTE Target Amount,OTE Max Amount,Pay Mix\r\nA,112,01/01/2021,,,US,,,Marketing,,,,,IC,,Content Manager,Annual,USD,\"$89,680.00 \",\"112,100.00\",\"$134,520.00 \",,,,,,,,,,,,,,\r\nA,113,01/01/2021,,,US,,,Product Development,,,,,M,,Director of Product,Annual,USD,\"$158,800.00 \",\"198,500.00\",\"$238,200.00 \",,,,,,,,,,,,,,\r\nA,114,01/01/2021,,,AUS,,,Engineering,,,,,IC,,Senior Software Engineer II,Annual,AUD,\"$111,384.62 \",\"144,800.00\",\"$167,076.92 \",,,,,,,,,,,,,,\r\nA,115,01/01/2021,,,AUS,,,Engineering,,,,,...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Band Id</div><div class=\"cm-col\">Effective Start Date</div><div class=\"cm-col\">Effective End Date</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Location (Country)</div><div class=\"cm-col\">Location (Region)</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">Job Subfamily</div><div class=\"cm-col\">Description</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Frequency</div><div class=\"cm-col\">Salary Currency</div><div class=\"cm-col\">Salary Min Amount</div><div class=\"cm-col\">Salary Target Amount</div><div class=\"cm-col\">Salary Max Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay Target (%)</div><div class=\"cm-col\">Variable Pay Target Amount</div><div class=\"cm-col\">Equity Currency</div><div class=\"cm-col\">Equity Target Amount</div><div class=\"cm-col\">Equity Target (unit)</div><div class=\"cm-col\">Equity Target (%)</div><div class=\"cm-col\">Min Equity (unit)</div><div class=\"cm-col\">Max Equity (unit)</div><div class=\"cm-col\">Equity Participation Rate (%)</div><div class=\"cm-col\">OTE Min Amount</div><div class=\"cm-col\">OTE Target Amount</div><div class=\"cm-col\">OTE Max Amount</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">112</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Marketing</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col\"></div><div class=\"cm-col\">Content Manager</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">$89,680.00 </div><div class=\"cm-col\">112,100.00</div><div class=\"cm-col\">$134,520.00 </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">113</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Product Development</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col\"></div><div class=\"cm-col\">Director of Product</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">$158,800.00 </div><div class=\"cm-col\">198,500.00</div><div class=\"cm-col\">$238,200.00 </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">114</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">AUS</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col\"></div><div class=\"cm-col\">Senior Software Engineer II</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">AUD</div><div class=\"cm-col\">$111,384.62 </div><div class=\"cm-col\">144,800.00</div><div class=\"cm-col\">$167,076.92 </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">115</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">AUS</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col\"></div><div class=\"cm-col\">Software Engineer III</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">AUD</div><div class=\"cm-col\">$86,538.46 </div><div class=\"cm-col\">112,500.00</div><div class=\"cm-col\">$129,807.69 </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">116</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">AUS</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col\"></div><div class=\"cm-col\">Sr. Staff Software Engineer (PT)</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">AUD</div><div class=\"cm-col\">$72,269.23 </div><div class=\"cm-col\">$93,950.00 </div><div class=\"cm-col\">$112,740.00 </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 6, "lines_more": 1, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Nnrz3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Tithely Payband Additions:"}]}]}]}, {"ts": "1741021956.844309", "text": "will do in sometime.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "23TTZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will do in sometime."}]}]}]}, {"ts": "1741022222.570209", "text": "I don't know why it let me do this one and not others, but I was able to update all their hourly employees over to Regular/FT employees", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "D6tRT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I don't know why it let me do this one and not others, but I was able to update all their hourly employees over to Regular/FT employees"}]}]}]}, {"ts": "1741022274.442609", "text": ":+1:", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qF+xl", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "+1", "unicode": "1f44d"}]}]}]}, {"ts": "1741025158.888799", "text": "<@U07EJ2LP44S> brad just texted me “I’m on the Stride Manager kickoff call! So cool!”", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ntz98", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " brad just texted me “I’m on the Stride Manager kickoff call! So cool!”"}]}]}]}, {"ts": "1741025260.662519", "text": "Great job <@U0690EB5JE5> in building a product that’s praised by customers. <PERSON> was <PERSON> and <PERSON>’s manager at 15five", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1741025268.000000"}, "blocks": [{"type": "rich_text", "block_id": "23o+f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Great job "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " in building a product that’s praised by customers. <PERSON> was <PERSON> and <PERSON>’s manager "}, {"type": "text", "text": "at"}, {"type": "text", "text": " 15five"}]}]}]}, {"ts": "1741025591.189269", "text": "sounds great. Did we demo this to him?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RvB0J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "sounds great. Did we demo this to him?"}]}]}]}, {"ts": "1741025595.728489", "text": "<@U07M6QKHUC9>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lAMKj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}]}]}]}, {"ts": "1741025657.418149", "text": "He is in tithely training that <PERSON> is doing now", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LtbSX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "He is in tithely training that <PERSON> is doing now"}]}]}]}, {"ts": "1741025680.953409", "text": "ah okay.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "dnTsL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ah okay."}]}]}]}, {"ts": "1741026838.099729", "text": "just finished it", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nIngP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "just finished it"}]}]}]}, {"ts": "1741027011.813299", "text": "This is Tithelys timeframe", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "files": [{"id": "F08GL3CUMB2", "created": 1741026992, "timestamp": 1741026992, "name": "IMG_4986.jpg", "title": "IMG_4986", "mimetype": "image/jpeg", "filetype": "jpg", "pretty_type": "JPEG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 4164839, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GL3CUMB2/img_4986.jpg", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GL3CUMB2/download/img_4986.jpg", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GL3CUMB2-2614d4d20e/img_4986_64.jpg", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GL3CUMB2-2614d4d20e/img_4986_80.jpg", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GL3CUMB2-2614d4d20e/img_4986_360.jpg", "thumb_360_w": 270, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GL3CUMB2-2614d4d20e/img_4986_480.jpg", "thumb_480_w": 360, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GL3CUMB2-2614d4d20e/img_4986_160.jpg", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GL3CUMB2-2614d4d20e/img_4986_720.jpg", "thumb_720_w": 540, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GL3CUMB2-2614d4d20e/img_4986_800.jpg", "thumb_800_w": 800, "thumb_800_h": 1067, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GL3CUMB2-2614d4d20e/img_4986_960.jpg", "thumb_960_w": 720, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GL3CUMB2-2614d4d20e/img_4986_1024.jpg", "thumb_1024_w": 768, "thumb_1024_h": 1024, "original_w": 4284, "original_h": 5712, "thumb_tiny": "AwAwACSHfcf3G/75o33P9x/++avj6UcMcc/yoAoBrn+43/fNBe4H8B/75rQVQpzz+dBoAzvNm/uH/vmjzZv7h/75q+RRigBw3Y7ZpRuzzjFNG0ADePzFKNoOd4/OgBTjHNBAqGe4MbAKFYEZ61F9tbIyi8+9AFkY7UtKSufvL+dGV/vL+dAGKY27I35UCNs8o35VoC9g/vN/3zTvt0H98/lQBnuoDHy1kC9sikwf7r/lWj9vg/vN+RpDfw/3ifwoAzvKbsjflR5T/wBxvyq/9uh9/wAqPt0Pv+VAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GL3CUMB2/img_4986.jpg", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GL3CUMB2-5757c46db6", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "zoAyi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is Tithelys timeframe"}]}]}]}, {"ts": "1741028366.873029", "text": "<@U07M6QKHUC9> I need to let these folks know pretty soon my last day is a week from tomorrow. I'm ok waiting until <PERSON><PERSON> to tell them, but they're going to ask questions and we need to be prepared to answer. They're going to find out it's shutting down soon, so I would recommend being straightforward", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NlWYX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I need to let these folks know pretty soon my last day is a week from tomorrow. I'm ok waiting until <PERSON><PERSON> to tell them, but they're going to ask questions and we need to be prepared to answer. They're going to find out it's shutting down soon, so I would recommend being straightforward"}]}]}]}, {"ts": "1741028378.884209", "text": "But let me know how you want me to share the news", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "x8Fzf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "But let me know how you want me to share the news"}]}]}]}, {"ts": "1741028537.711709", "text": "I think it's better to share the news about shutting down only after their cycle in complete to avoid any panic during the cycle, which I will do in late March.  It's okay to share your departure with them now so they can be more prepared to handle the transition.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "04rDp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think it's better to share the news about shutting down only after their cycle in complete to avoid any panic during the cycle, which I will do in late March.  It's okay to share your departure with them now so they can be more prepared to handle the transition."}]}]}]}, {"ts": "1741028591.276979", "text": "do we still have your weekly call with them this Thursday? if yes. then I can join that call.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/UEXy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "do we still have your weekly call with them this Thursday? if yes. then I can join that call."}]}]}]}, {"ts": "1741028644.848279", "text": "we can tell them I will be taking over after your departure.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qalnJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we can tell them I will be taking over after your departure."}]}]}]}, {"ts": "1741028805.906049", "text": "What reason do you want me to give? I guess I could say that this was a contract job and I've been looking for something more permanent", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "4vW43", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "What reason do you want me to give? I guess I could say that this was a contract job and I've been looking for something more permanent"}]}]}]}, {"ts": "1741028878.713059", "text": "I just think people are going to be pretty upset if they feel we already knew when I left and didn't tell them, once they find out in a couple weeks. Ultimately its up to you but I do think these people are going to be pretty upset overall when they find out", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IcbmD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I just think people are going to be pretty upset if they feel we already knew when I left and didn't tell them, once they find out in a couple weeks. Ultimately its up to you but I do think these people are going to be pretty upset overall when they find out"}]}]}]}, {"ts": "1741028940.595919", "text": "Yes I like that. We can say it was a contract job and you find something better", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "B2yMZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes I like that. We can say it was a contract job and you find something better"}]}]}]}, {"ts": "1741028961.084729", "text": "Diversified is done, I'm not sure we'll have another call but maybe. Same for <PERSON><PERSON>, I am not sure if we'll keep the call", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6Oh0J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified is done, I'm not sure we'll have another call but maybe. Same for <PERSON><PERSON>, I am not sure if we'll keep the call"}]}]}]}, {"ts": "1741028970.707439", "text": "Tithely probably yes, since they're just entering their cycle", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ehb03", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Tithely probably yes, since they're just entering their cycle"}]}]}]}, {"ts": "1741028982.792569", "text": "Tomorrow is the call with <PERSON><PERSON><PERSON> so it might be better to tell them then", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "UNLq+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Tomorrow is the call with <PERSON><PERSON><PERSON> so it might be better to tell them then"}]}]}]}, {"ts": "1741028989.058539", "text": "so far we managed to share it with AlayaCare and Cainwattters without any issue. I had a call with <PERSON> on Friday and it was fine", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "UE/zZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "so far we managed to share it with AlayaCare and Cainwattters without any issue. I had a call with <PERSON> on Friday and it was fine"}]}]}]}, {"ts": "1741029021.388549", "text": "I know, but these people are different beasts. They're already planning their next cycles and have told their company they are using us going forward", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RG7n/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I know, but these people are different beasts. They're already planning their next cycles and have told their company they are using us going forward"}]}]}]}, {"ts": "1741029039.938619", "text": "Diversified, Tithely, and Curana all have told their companies that", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ATyDr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified, Tithely, and Curana all have told their companies that"}]}]}]}, {"ts": "1741029054.716529", "text": "Within the last couple of weeks", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WMZyt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Within the last couple of weeks"}]}]}]}, {"ts": "1741029065.561699", "text": "Ok, then let's tell them that", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BXV+B", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok, then let's tell them that"}]}]}]}, {"ts": "1741029089.951049", "text": "let's share it during our next calls with them and i will be joining those calls", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wNA1X", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "let's share it during our next calls with them and i will be joining those calls"}]}]}]}, {"ts": "1741029130.947789", "text": "Tell them that I'm leaving, and that after the cycles, <PERSON><PERSON> won't be supported anymore?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WJIhj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Tell them that I'm leaving, and that after the cycles, <PERSON><PERSON> won't be supported anymore?"}]}]}]}, {"ts": "1741029171.543229", "text": "do you think it's better to share news of Stride shutting down now or after the cycle?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lgo2G", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "do you think it's better to share news of Stride shutting down now or after the cycle?"}]}]}]}, {"ts": "1741029213.092389", "text": "I think my departure is already going to give them pause, make them say wait what's going on", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7F0DC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think my departure is already going to give them pause, make them say wait what's going on"}]}]}]}, {"ts": "1741029284.123459", "text": "people leaving is pretty common so I am not too concerned about sharing the news of your dept. Yes it will be a surprise but not something they are not used to", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "s+S9h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "people leaving is pretty common so I am not too concerned about sharing the news of your dept. Yes it will be a surprise but not something they are not used to"}]}]}]}, {"ts": "1741029320.614439", "text": "I could just tell everyone now, and give them the contract reason thing and tell them you'll be stepping in to support them, and that i'll be here for another week", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "usSaE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I could just tell everyone now, and give them the contract reason thing and tell them you'll be stepping in to support them, and that i'll be here for another week"}]}]}]}, {"ts": "1741029327.772389", "text": "and then try not to talk about it much", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NIx9z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "and then try not to talk about it much"}]}]}]}, {"ts": "1741029382.700329", "text": "let me just call you on your phone", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "bvOsc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "let me just call you on your phone"}]}]}]}, {"ts": "1741029384.639159", "text": "and if i do that via email or chat that might be better", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7W/xY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "and if i do that via email or chat that might be better"}]}]}]}, {"ts": "**********.278439", "text": "k", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fpZmE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "k"}]}]}]}, {"ts": "**********.557699", "text": "<https://curanahealthmip.stridehr.io/>", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "attachments": [{"from_url": "https://curanahealthmip.stridehr.io/", "service_icon": "https://curanahealthmip.stridehr.io/apple-touch-icon.png", "id": 1, "original_url": "https://curanahealthmip.stridehr.io/", "fallback": "Stride", "text": "Web site created using create-react-app", "title": "Stride", "title_link": "https://curanahealthmip.stridehr.io/", "service_name": "curanahealthmip.stridehr.io"}], "blocks": [{"type": "rich_text", "block_id": "JwHod", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://curanahealthmip.stridehr.io/"}]}]}]}, {"ts": "**********.420669", "text": "<@U0690EB5JE5> Can you add this user to the Curana main account", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.420669", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "files": [{"id": "F08GM4YRMRN", "created": **********, "timestamp": **********, "name": "CuranaMainVanessaRodriguez.csv", "title": "CuranaMainVanessaRodriguez.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1962, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GM4YRMRN/curanamainvanessarodriguez.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GM4YRMRN/download/curanamainvanessarodriguez.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GM4YRMRN/curanamainvanessarodriguez.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GM4YRMRN-207ef3c3d7", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GM4YRMRN/curanamainvanessarodriguez.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">1000</div><div class=\"cm-col\">VANESSA</div><div class=\"cm-col\">RODRIGUEZ</div><div class=\"cm-col\">VANESSA RODRIGUEZ</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">08/04/2021</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">1476</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">08/04/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\">Hispanic or Latino</div><div class=\"cm-col\">FL</div><div class=\"cm-col\">Appeals and Grievances Specialist</div><div class=\"cm-col\">Appeals &amp; Grievances</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">49646.48</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">3/23/24</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 8, "lines_more": 7, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "pxxUz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you add this user to the Curana main account"}]}]}]}, {"ts": "**********.870709", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> FYI... All non-prod ENVs are stopped. Please let me know if the ENVs are still required.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.870709", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "eq7oz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " FYI... All non-prod ENVs are stopped. Please let me know if the ENVs are still required."}]}]}]}, {"ts": "**********.847379", "text": "<@U0690EB5JE5> another file to add to curana main, 3 new employees", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.847379", "reply_count": 1, "files": [{"id": "F08G5QM6K44", "created": **********, "timestamp": **********, "name": "CuranaMainAddsMar4.csv", "title": "CuranaMainAddsMar4.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 2360, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08G5QM6K44/curanamainaddsmar4.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08G5QM6K44/download/curanamainaddsmar4.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08G5QM6K44/curanamainaddsmar4.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08G5QM6K44-dc3004c774", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08G5QM6K44/curanamainaddsmar4.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">2926</div><div class=\"cm-col\">STEPHANIE</div><div class=\"cm-col\">HARRELSON</div><div class=\"cm-col\">STEPHANIE HARRELSON</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">01/17/2023</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">2272</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">01/17/2023</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">VA</div><div class=\"cm-col\">Case Manager, RN</div><div class=\"cm-col\">Case Management</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">07/29/2024</div><div class=\"cm-col\">Med Grp Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\">86,320.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">07/29/2024</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">3311</div><div class=\"cm-col\">SHANNON</div><div class=\"cm-col\">ROBERS</div><div class=\"cm-col\">SHANNON ROBERS</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">07/12/2023</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">2272</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">07/12/2023</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">VA</div><div class=\"cm-col\">Case Manager, RN</div><div class=\"cm-col\">Case Management</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">03/23/2024</div><div class=\"cm-col\">Med Grp Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\">81,600.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">03/23/2024</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">2979</div><div class=\"cm-col\">CHRISTIN</div><div class=\"cm-col\">WALLACE</div><div class=\"cm-col\">CHRISTIN WALLACE</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">02/20/2023</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">1167</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">02/20/2023</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">TN</div><div class=\"cm-col\">Employee Services Representative</div><div class=\"cm-col\">Human Resources</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">03/23/2024</div><div class=\"cm-col\">Shared Services</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\">75,000.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">03/23/2024</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"> </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 7, "lines_more": 6, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "/13BD", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " another file to add to curana main, 3 new employees"}]}]}]}, {"ts": "**********.013829", "text": "<@U0690EB5JE5> That one person at Curana still can't access his account. Can you please investigate? I just forwarded an email.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.013829", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "+CfhE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " That one person at Curana still can't access his account. Can you please investigate? I just forwarded an email."}]}]}]}, {"ts": "**********.156199", "text": "<@U0690EB5JE5> We have a bug with comments in Curana Main: when a manager is trying to review a team and add a comment. It will not accept the comment. I impersonated the manager <PERSON> and was able to reproduce. It WILL allow her to add a comment if she uses the search bar to find a user, but not if she's in the regular list view and tries to add a comment.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.156199", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "hNjHQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We have a bug with comments in Curana Main: when a manager is trying to review a team and add a comment. It will not accept the comment. I impersonated the manager <PERSON> and was able to reproduce. It WILL allow her to add a comment if she uses the search bar to find a user, but not if she's in the regular list view and tries to add a comment."}]}]}]}, {"ts": "1741116837.232479", "text": "<@U0690EB5JE5> <@U07M6QKHUC9> The currency toggle in Tithely wasn't changing the currency, it was only changing the name of the currency. Once I hit save again on the localization numbers it started working (no changes were made to the numbers, I just hit save on the currency conversion rates page). Just FYI in case something happens mid-cycle.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741116837.232479", "reply_count": 9, "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "IP57/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " The currency toggle in Tithely wasn't changing the currency, it was only changing the name of the currency. Once I hit save again on the localization numbers it started working (no changes were made to the numbers, I just hit save on the currency conversion rates page). Just FYI in case something happens mid-cycle."}]}]}]}, {"ts": "1741116976.219409", "text": "<@U0690EB5JE5> I have a specific question on the budget allocation page. Tithelys budget is based on ALL salaries, not just eligible salaries. So they will likely need to overwrite budgets on that page. If they do, do they have to edit EVERY field or will any of it 'roll down'? So for example, if a department has two recommenders under it, do they need to update the budget on all three of those lines? Or if they update the topline number will it roll down the new amounts to the 2 managers? I think the answer is they have to update every single amount.\n\nA secondary question, what about 'left for team' amounts. Do they need to overwrite that field as well when they change the department budgets?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741116976.219409", "reply_count": 1, "reactions": [{"name": "eyes", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "qM5ZM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I have a specific question on the budget allocation page. Tithelys budget is based on ALL salaries, not just eligible salaries. So they will likely need to overwrite budgets on that page. If they do, do they have to edit EVERY field or will any of it 'roll down'? So for example, if a department has two recommenders under it, do they need to update the budget on all three of those lines? Or if they update the topline number will it roll down the new amounts to the 2 managers? I think the answer is they have to update every single amount.\n\nA secondary question, what about 'left for team' amounts. Do they need to overwrite that field as well when they change the department budgets?"}]}]}]}, {"ts": "1741117294.079889", "text": "<@U0690EB5JE5> <@U07M6QKHUC9> FYI On curana, every time I edit the cycle it continues to change the budget %. I am having to change it to 3.7 every time I publish (which I've had to do a couple times to include employees). So if you make edits make sure you reset the budget.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741117294.079889", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "j/XKC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " FYI On curana, every time I edit the cycle it continues to change the budget %. I am having to change it to 3.7 every time I publish (which I've had to do a couple times to include employees). So if you make edits make sure you reset the budget."}]}]}]}, {"ts": "1741138728.242149", "text": "<@U0690EB5JE5> <PERSON> is going to post a couple of sheets to the main title Slack channel. One is some additional last race dates, and when is the performance readings. Can you please upload these so that they’re ready for their cycle tomorrow morning?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741138728.242149", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "9glaO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON> is going to post a couple of sheets to the main title Slack channel. One is some additional last race dates, and when is the performance readings. Can you please upload these so that they’re ready for their cycle tomorrow morning?"}]}]}]}, {"ts": "1741139997.441429", "text": "<@U07EJ2LP44S> Done. employee id `365` doesn't exist in the ENV and the rating is cannot be updated. Rest all updates are done,", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741138728.242149", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1741142239.000000"}, "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "A4Hzq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Done. employee id "}, {"type": "text", "text": "365", "style": {"code": true}}, {"type": "text", "text": " doesn't exist in the ENV and the rating is cannot be updated. Rest all updates are done,"}]}]}]}, {"ts": "1741140514.891209", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> Gentle Reminder! one of our engineers will be available to help with uploads. If there are any bugs, Bug fixes might take 24hrs.\n<@U07MH77PUBV> will be on call Wednesday and <@U06HN8XDC5A> will be on call on Thursday. Friday I am OOO but can be available my late evening.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741016402.109749", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1741141222.000000"}, "blocks": [{"type": "rich_text", "block_id": "Q/zXE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Gentle Reminder! one of our engineers will be available to help with uploads. If there are any bugs, Bug fixes might take 24hrs.\n"}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " will be on call Wednesday and "}, {"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " will be on call on Thursday. Friday I am OOO but can be available my late evening."}]}]}]}, {"ts": "1741140522.990729", "text": "<@U0690EB5JE5> I just forwarded you a couple of emails from Curana. Can you please address those?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741140522.990729", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "j1nub", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I just forwarded you a couple of emails from <PERSON><PERSON><PERSON>. Can you please address those?"}]}]}]}, {"ts": "1741140679.884499", "text": "<@U0690EB5JE5> my understanding was that the budgets are prorated in the comp builder when we calculate the budget. But that’s is not happening for curana. Can you please clarify if budget calculations in the comp builder incorporate, probation, or not?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741140679.884499", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "jpKtz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " my understanding was that the budgets are prorated in the comp builder when we calculate the budget. But that’s is not happening for curana. Can you please clarify if budget calculations in the comp builder incorporate, probation, or not?"}]}]}]}, {"ts": "1741184853.592609", "text": "<@U0690EB5JE5> Tithely is asking if there's any way to change the performance matrix table one more time to:\n\nWould there be any chance to change the compa ratio ranges again? It should be:\n&lt;0.80 / 0.80-0.90 / 0.91-1.10 / &gt;1.10", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741184853.592609", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "QC6cw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Tithely is asking if there's any way to change the performance matrix table one more time to:\n\nWould there be any chance to change the compa ratio ranges again? It should be:\n<0.80 / 0.80-0.90 / 0.91-1.10 / >1.10"}]}]}]}, {"ts": "1741185329.034079", "text": "Ooooh wait you're out so maybe not", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ukqOQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ooooh wait you're out so maybe not"}]}]}]}, {"ts": "1741185520.684429", "text": "<@U07MH77PUBV> Can you upload this file to tithely's paybands? We were missing the Job Family and it didn't work last time.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741185520.684429", "reply_count": 1, "files": [{"id": "F08GLBZTS2D", "created": 1741185519, "timestamp": 1741185519, "name": "TithelyBandsUpdateMar5.csv", "title": "TithelyBandsUpdateMar5.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1292, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GLBZTS2D/tithelybandsupdatemar5.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GLBZTS2D/download/tithelybandsupdatemar5.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GLBZTS2D/tithelybandsupdatemar5.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GLBZTS2D-36e1995924", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GLBZTS2D/tithelybandsupdatemar5.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Band Id,Effective Start Date,Effective End Date,Compensation Grade,Location (Country),Location (Region),Team,Department,Job Family,Job Subfamily,Description,Tier,Job Category,Job Level,Job Title,Frequency,Salary Currency,Salary Min Amount,Salary Target Amount,Salary Max Amount,Variable Pay Currency,Variable Pay Target (%),Variable Pay Target Amount,Equity Currency,Equity Target Amount,Equity Target (unit),Equity Target (%),Min Equity (unit),Max Equity (unit),Equity Participation Rate (%),OTE Min Amount,OTE Target Amount,OTE Max Amount,Pay Mix\r\nU,112,1/1/21,,N/A,US,N/A,N/A,Marketing,Marketing,N/A,N/A,0,IC,0,Content Manager,Annual,USD,89680,112100,134520,,,,,,,,,,,,,,\r\nU,113,1/1/21,,N/A,US,N/A,N/A,Product Development,Product Development,N/A,N/A,0,M,0,Director of Product,Annual,USD,158800,198500,238200,,,,,,,,,,,,,,\r\nU,114,1/1/21,,N/A,AU,N/A,N/A,Engineering,Engineering,N/A,N/A,0,IC,0,Senior Software Engineer II,Annual,AUD,111384.62,144800,167076.92,,,,,,,,,,,,,,\r\nU,115,1/1/21,,N/A,AU,N/A...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Band Id</div><div class=\"cm-col\">Effective Start Date</div><div class=\"cm-col\">Effective End Date</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Location (Country)</div><div class=\"cm-col\">Location (Region)</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">Job Subfamily</div><div class=\"cm-col\">Description</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Frequency</div><div class=\"cm-col\">Salary Currency</div><div class=\"cm-col\">Salary Min Amount</div><div class=\"cm-col\">Salary Target Amount</div><div class=\"cm-col\">Salary Max Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay Target (%)</div><div class=\"cm-col\">Variable Pay Target Amount</div><div class=\"cm-col\">Equity Currency</div><div class=\"cm-col\">Equity Target Amount</div><div class=\"cm-col\">Equity Target (unit)</div><div class=\"cm-col\">Equity Target (%)</div><div class=\"cm-col\">Min Equity (unit)</div><div class=\"cm-col\">Max Equity (unit)</div><div class=\"cm-col\">Equity Participation Rate (%)</div><div class=\"cm-col\">OTE Min Amount</div><div class=\"cm-col\">OTE Target Amount</div><div class=\"cm-col\">OTE Max Amount</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">112</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">US</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Marketing</div><div class=\"cm-col\">Marketing</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Content Manager</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">89680</div><div class=\"cm-col cm-num\">112100</div><div class=\"cm-col cm-num\">134520</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">113</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">US</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Product Development</div><div class=\"cm-col\">Product Development</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Director of Product</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">158800</div><div class=\"cm-col cm-num\">198500</div><div class=\"cm-col cm-num\">238200</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">114</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">AU</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Senior Software Engineer II</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">AUD</div><div class=\"cm-col cm-num\">111384.62</div><div class=\"cm-col cm-num\">144800</div><div class=\"cm-col cm-num\">167076.92</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">115</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">AU</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Software Engineer III</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">AUD</div><div class=\"cm-col cm-num\">86538.46</div><div class=\"cm-col cm-num\">112500</div><div class=\"cm-col cm-num\">129807.69</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">116</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">AU</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Sr. Staff Software Engineer (PT)</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">AUD</div><div class=\"cm-col cm-num\">72269.23</div><div class=\"cm-col cm-num\">93950</div><div class=\"cm-col cm-num\">112740</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 6, "lines_more": 1, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ihKAR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " Can you upload this file to tithely's paybands? We were missing the Job Family and it didn't work last time."}]}]}]}, {"ts": "1741187003.554229", "text": "Let me do it in 30 min", "user": "U07MH77PUBV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fUoQh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me do it in 30 min"}]}]}]}, {"ts": "1741196471.285449", "text": "<@U0690EB5JE5> <@U07MH77PUBV> Another update to a Tithely payband", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741196471.285449", "reply_count": 2, "files": [{"id": "F08GDUKVDTN", "created": 1741196470, "timestamp": 1741196470, "name": "TithelyPeopleOpsUpdate.csv", "title": "TithelyPeopleOpsUpdate.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 704, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GDUKVDTN/tithelypeopleopsupdate.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GDUKVDTN/download/tithelypeopleopsupdate.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GDUKVDTN/tithelypeopleopsupdate.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GDUKVDTN-2fa77e5ee7", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GDUKVDTN/tithelypeopleopsupdate.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Band Id,Effective Start Date,Effective End Date,Compensation Grade,Location (Country),Location (Region),Team,Department,Job Family,Job Subfamily,Description,Tier,Job Category,Job Level,Job Title,Frequency,Salary Currency,Salary Min Amount,Salary Target Amount,Salary Max Amount,Variable Pay Currency,Variable Pay Target (%),Variable Pay Target Amount,Equity Currency,Equity Target Amount,Equity Target (unit),Equity Target (%),Min Equity (unit),Max Equity (unit),Equity Participation Rate (%),OTE Min Amount,OTE Target Amount,OTE Max Amount,Pay Mix\r\nU,59,1/1/21,,,US,,,People Ops,People Ops,,,0,Mgr,0,Head of People Ops,Annual,USD,126615,164600,189923,,0,0,,0,0,0,0,0,0,0,0,0,", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Band Id</div><div class=\"cm-col\">Effective Start Date</div><div class=\"cm-col\">Effective End Date</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Location (Country)</div><div class=\"cm-col\">Location (Region)</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">Job Subfamily</div><div class=\"cm-col\">Description</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Frequency</div><div class=\"cm-col\">Salary Currency</div><div class=\"cm-col\">Salary Min Amount</div><div class=\"cm-col\">Salary Target Amount</div><div class=\"cm-col\">Salary Max Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay Target (%)</div><div class=\"cm-col\">Variable Pay Target Amount</div><div class=\"cm-col\">Equity Currency</div><div class=\"cm-col\">Equity Target Amount</div><div class=\"cm-col\">Equity Target (unit)</div><div class=\"cm-col\">Equity Target (%)</div><div class=\"cm-col\">Min Equity (unit)</div><div class=\"cm-col\">Max Equity (unit)</div><div class=\"cm-col\">Equity Participation Rate (%)</div><div class=\"cm-col\">OTE Min Amount</div><div class=\"cm-col\">OTE Target Amount</div><div class=\"cm-col\">OTE Max Amount</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">59</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">People Ops</div><div class=\"cm-col\">People Ops</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Mgr</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Head of People Ops</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">126615</div><div class=\"cm-col cm-num\">164600</div><div class=\"cm-col cm-num\">189923</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 2, "lines_more": 0, "preview_is_truncated": false, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "mF2pA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " Another update to a Tithely payband"}]}]}]}, {"ts": "1741197243.744769", "text": "<@U07MH77PUBV> Can you tell why the employee Director of Product <PERSON> / Director of Product is not mapping to the Director of Product payband? He used to be Group Product Manager and even after his title change he is not remapping to the band. This is still Tithely", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741197243.744769", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "z41Gi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " Can you tell why the employee Director of Product <PERSON> / Director of Product is not mapping to the Director of Product payband? He used to be Group Product Manager and even after his title change he is not remapping to the band. This is still Tithely"}]}]}]}, {"ts": "1741209936.928769", "text": "<@U0690EB5JE5> can we pls add this emp to Curana merit cycle only?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741209936.928769", "reply_count": 4, "files": [{"id": "F08GF7CEG4C", "created": 1741209924, "timestamp": 1741209924, "name": "<PERSON> Add_Employees_Curana-Stride 01.23 (1) (1).xlsx", "title": "<PERSON> Add_Employees_Curana-Stride 01.23 (1) (1).xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 973497, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GF7CEG4C/carlson_add_employees_curana-stride_01.23__1___1_.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GF7CEG4C/download/carlson_add_employees_curana-stride_01.23__1___1_.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GF7CEG4C-c0c020be54/carlson_add_employees_curana-stride_01.23__1___1__converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GF7CEG4C-c0c020be54/carlson_add_employees_curana-stride_01.23__1___1__thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GF7CEG4C/carlson_add_employees_curana-stride_01.23__1___1_.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GF7CEG4C-2ebc79486e", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "DgbHN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we pls add this emp to Curana merit cycle only?"}]}]}]}, {"ts": "1741220267.544949", "text": "<@U0690EB5JE5> just forwarded you an email from curana? Can you please address it?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741220267.544949", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "4xmbH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " just forwarded you an email from curana? Can you please address it?"}]}]}]}, {"ts": "1741274017.744759", "text": "<@U07EJ2LP44S> This automatically got fixed when I just refreshed payband identifiers to trigger band calculation. Could you please confirm if things look good now. It looks fine to me now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741197243.744769", "subtype": "thread_broadcast", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ERz5z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This automatically got fixed when I just refreshed payband identifiers to trigger band calculation. Could you please confirm if things look good now. It looks fine to me now."}]}]}]}, {"ts": "1741274110.605149", "text": "<@U07M6QKHUC9> This is a case we have hit for the first time. We have the fix but need to test a bit more before deployment for regression. I will get some time my morning and will get it fixed that time. Please buy another day for this fix. The issue due to HireDate being greater then cut-off date in eligibility rules which is an exception system never supported this case.\nWe have to handle this exception in case of proration. Proration is set to 0 due to this scenario and adjustments have been calculated to 0.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741220267.544949", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1741274519.000000"}, "blocks": [{"type": "rich_text", "block_id": "9gF+X", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " This is a case we have hit for the first time. We have the fix but need to test a bit more before deployment for regression. I will get some time my morning and will get it fixed that time. Please buy another day for this fix. The issue due to HireDate being greater then cut-off date in eligibility rules which is an exception system never supported this case.\nWe have to handle this exception in case of proration. Proration is set to 0 due to this scenario and adjustments have been calculated to 0."}]}]}]}, {"ts": "1741285260.775169", "text": "<@U07EJ2LP44S> For Tithely. I am not seeing any planners except root emp and <PERSON> under My Tasks. Is that expected?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741285260.775169", "reply_count": 4, "files": [{"id": "F08H7FQLKGQ", "created": 1741285208, "timestamp": 1741285208, "name": "Screenshot 2025-03-06 at 10.19.50 AM.png", "title": "Screenshot 2025-03-06 at 10.19.50 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 193544, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08H7FQLKGQ/screenshot_2025-03-06_at_10.19.50___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08H7FQLKGQ/download/screenshot_2025-03-06_at_10.19.50___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_360.png", "thumb_360_w": 360, "thumb_360_h": 134, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_480.png", "thumb_480_w": 480, "thumb_480_h": 179, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_720.png", "thumb_720_w": 720, "thumb_720_h": 269, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_800.png", "thumb_800_w": 800, "thumb_800_h": 299, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_960.png", "thumb_960_w": 960, "thumb_960_h": 359, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 383, "original_w": 2698, "original_h": 1008, "thumb_tiny": "AwARADDRI560oHrzQaO3SgBaKQUtABRRRQA00NQaGoAUdKWkHSloAKKKKAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08H7FQLKGQ/screenshot_2025-03-06_at_10.19.50___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08H7FQLKGQ-2d5fc6f9a8", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Z/l9X", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " For Tithely. I am not seeing any planners except root emp and <PERSON> under My Tasks. Is that expected?"}]}]}]}, {"ts": "1741287432.768859", "text": "<@U0690EB5JE5> For total rewards, can we change the language in that area above the salaries:\n\nCurrent Salary = New Salary\nPrevious Salary (keep same)\nFirst Salary = Starting Salary", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741287432.768859", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "YtNNH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " For total rewards, can we change the language in that area above the salaries:\n\nCurrent Salary = New Salary\nPrevious Salary (keep same)\nFirst Salary = Starting Salary"}]}]}]}, {"ts": "1741287529.741899", "text": "And also, they would like to exclude the change history items that don't include a salary change. So for example if there wa a title change and it didn't impact the salary. Could we either exclude these items ourselves, or could they send over a file that just includes those salary changes and we could use that data instead?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741287529.741899", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "OHylN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And also, they would like to exclude the change history items that don't include a salary change. So for example if there wa a title change and it didn't impact the salary. Could we either exclude these items ourselves, or could they send over a file that just includes those salary changes and we could use that data instead?"}]}]}]}, {"ts": "1741287658.545099", "text": "<@U07EJ2LP44S> regarding <PERSON>'s latest question on allowing managers to make changes after salary increases have been accepted by them, how do you want to handle this?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741287658.545099", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "BzgHO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " regarding <PERSON>'s latest question on allowing managers to make changes after salary increases have been accepted by them, how do you want to handle this?"}]}]}]}, {"ts": "1741292496.133329", "text": "<@U0690EB5JE5> A few updates to Tithely bands. One of the deletes doesn't have a band ID, so I'm not sure if that one will come out? It doesn't have one in the system at all.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741292496.133329", "reply_count": 1, "files": [{"id": "F08GN1LGWAG", "created": 1741292492, "timestamp": 1741292492, "name": "TithelyBandsUpdateMar6.csv", "title": "TithelyBandsUpdateMar6.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1346, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GN1LGWAG/tithelybandsupdatemar6.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GN1LGWAG/download/tithelybandsupdatemar6.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GN1LGWAG/tithelybandsupdatemar6.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GN1LGWAG-2b288c3b96", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GN1LGWAG/tithelybandsupdatemar6.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Band Id,Effective Start Date,Effective End Date,Compensation Grade,Location (Country),Location (Region),Team,Department,Job Family,Job Subfamily,Description,Tier,Job Category,Job Level,Job Title,Frequency,Salary Currency,Salary Min Amount,Salary Target Amount,Salary Max Amount,Variable Pay Currency,Variable Pay Target (%),Variable Pay Target Amount,Equity Currency,Equity Target Amount,Equity Target (unit),Equity Target (%),Min Equity (unit),Max Equity (unit),Equity Participation Rate (%),OTE Min Amount,OTE Target Amount,OTE Max Amount,Pay Mix\r\nU,84,01/01/2021,,,CA,,,Customer Experience,Customer Experience,,,0,IC,0,Education Specialist II & Ascend Bookkeeping,Annual,CAD,20080,25100,30120,,0,0,,0,0,0,0,0,0,0,0,0,\r\nD,,01/01/2021,,,CA,,,Engineering,Engineering,,,0,Mgr,0,Sr. Engineering Manager I,Annual ,CAD,122600,153200,183800,,0,0,,0,0,0,0,0,0,0,0,0,\r\nD,18,01/01/2021,,,US,,,Customer Experience,Customer Experience,,,0,IC,0,Education Specialist,Annual,USD,47520,59400,71280,,0,0,,0,0,0,0,0...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Band Id</div><div class=\"cm-col\">Effective Start Date</div><div class=\"cm-col\">Effective End Date</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Location (Country)</div><div class=\"cm-col\">Location (Region)</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">Job Subfamily</div><div class=\"cm-col\">Description</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Frequency</div><div class=\"cm-col\">Salary Currency</div><div class=\"cm-col\">Salary Min Amount</div><div class=\"cm-col\">Salary Target Amount</div><div class=\"cm-col\">Salary Max Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay Target (%)</div><div class=\"cm-col\">Variable Pay Target Amount</div><div class=\"cm-col\">Equity Currency</div><div class=\"cm-col\">Equity Target Amount</div><div class=\"cm-col\">Equity Target (unit)</div><div class=\"cm-col\">Equity Target (%)</div><div class=\"cm-col\">Min Equity (unit)</div><div class=\"cm-col\">Max Equity (unit)</div><div class=\"cm-col\">Equity Participation Rate (%)</div><div class=\"cm-col\">OTE Min Amount</div><div class=\"cm-col\">OTE Target Amount</div><div class=\"cm-col\">OTE Max Amount</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">84</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">CA</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Education Specialist II &amp; Ascend Bookkeeping</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">CAD</div><div class=\"cm-col cm-num\">20080</div><div class=\"cm-col cm-num\">25100</div><div class=\"cm-col cm-num\">30120</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col\"></div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">CA</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Mgr</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Sr. Engineering Manager I</div><div class=\"cm-col\">Annual </div><div class=\"cm-col\">CAD</div><div class=\"cm-col cm-num\">122600</div><div class=\"cm-col cm-num\">153200</div><div class=\"cm-col cm-num\">183800</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">18</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Education Specialist</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">47520</div><div class=\"cm-col cm-num\">59400</div><div class=\"cm-col cm-num\">71280</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">21</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Lifetime Customer Success Manager</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">49200</div><div class=\"cm-col cm-num\">61500</div><div class=\"cm-col cm-num\">73800</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">24</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Operations Specialist</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">48320</div><div class=\"cm-col cm-num\">60400</div><div class=\"cm-col cm-num\">72480</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 6, "lines_more": 2, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "tfCFl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " A few updates to Tithely bands. One of the deletes doesn't have a band ID, so I'm not sure if that one will come out? It doesn't have one in the system at all."}]}]}]}, {"ts": "1741293660.038459", "text": "<@U0690EB5JE5> This looks like a bug for Tithely:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741293660.038459", "reply_count": 2, "files": [{"id": "F08G4708N15", "created": 1741293656, "timestamp": 1741293656, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 150781, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08G4708N15/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08G4708N15/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_360.png", "thumb_360_w": 360, "thumb_360_h": 121, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_480.png", "thumb_480_w": 480, "thumb_480_h": 161, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_720.png", "thumb_720_w": 720, "thumb_720_h": 241, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_800.png", "thumb_800_w": 800, "thumb_800_h": 268, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_960.png", "thumb_960_w": 960, "thumb_960_h": 322, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 343, "original_w": 1498, "original_h": 502, "thumb_tiny": "AwAQADC+YwCeScnPNOwfWlNFACc+tGD/AHqU9KUdKAEwfWgA55NLRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08G4708N15/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08G4708N15-db638fb6e3", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "6dxeg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " This looks like a bug for <PERSON><PERSON><PERSON>:"}]}]}]}, {"ts": "1741320608.317919", "text": "Will take a look at the requests/issues my evening.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "iFAXu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take a look at the requests/issues my evening."}]}]}]}, {"ts": "1741344153.351829", "text": "<@U07M6QKHUC9> fix is deployed. Please note for this employee proration is 0.16 and prorated increase will be way lesser than the actual proposed value.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741220267.544949", "subtype": "thread_broadcast", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "VTs+8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " fix is deployed. Please note for this employee proration is 0.16 and prorated increase will be way lesser than the actual proposed value."}]}]}]}, {"ts": "1741344252.124149", "text": "<@U07EJ2LP44S> This is taken care. Unlike other customers we didn't delete and create a new cycle after the data updates and there is an issue with uploads which doesn't carry title changes to cycle, However I have mitigated the issue should be good now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741293660.038459", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "F/wki", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is taken care. Unlike other customers we didn't delete and create a new cycle after the data updates and there is an issue with uploads which doesn't carry title changes to cycle, However I have mitigated the issue should be good now."}]}]}]}, {"ts": "1741344297.283169", "text": "I found the band Id based on the title and deleted. Done.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741292496.133329", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1741348705.000000"}, "blocks": [{"type": "rich_text", "block_id": "7M6uv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I found the band Id based on the title and deleted. Done."}]}]}]}, {"ts": "1741361503.455639", "text": "<@U0690EB5JE5> Another addition for Curana, and I wonder if this will have the same issue?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741361503.455639", "reply_count": 1, "files": [{"id": "F08GR1W38SF", "created": 1741361498, "timestamp": 1741361498, "name": "<PERSON><PERSON>d_Employees_Curana-Stride 01.23 (1) (1).xlsx", "title": "<PERSON><PERSON>d_Employees_Curana-Stride 01.23 (1) (1).xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 971968, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GR1W38SF/mariah_hanson_add_employees_curana-stride_01.23__1___1_.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GR1W38SF/download/mariah_hanson_add_employees_curana-stride_01.23__1___1_.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GR1W38SF-1f18d172a0/maria<PERSON>_hanson_add_employees_curana-stride_01.23__1___1__converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GR1W38SF-1f18d172a0/mariah_hanson_add_employees_curana-stride_01.23__1___1__thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GR1W38SF/mariah_hanson_add_employees_curana-stride_01.23__1___1_.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GR1W38SF-64d1b57c90", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "BF5VU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Another addition for <PERSON><PERSON><PERSON>, and I wonder if this will have the same issue?"}]}]}]}, {"ts": "1741361527.664849", "text": "Because her hire date is after the cutoff.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BsjPN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Because her hire date is after the cutoff."}]}]}]}, {"ts": "1741361559.316839", "text": "Will add the employee. That issue is fixed.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5GZsA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will add the employee. That issue is fixed."}]}]}]}, {"ts": "1741365433.652329", "text": "<@U07EJ2LP44S> Is tithely sending us a file that just includes those salary changes?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741287529.741899", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "zQVRB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Is tithely sending us a file that just includes those salary changes?"}]}]}]}, {"ts": "1741369142.896779", "text": "Yes I’ll have them do that. They also do need to update the 401(k) number, so is it better just to have that all together?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741369142.896779", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "JCvZh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes "}, {"type": "text", "text": "I’ll"}, {"type": "text", "text": " have them do that. They also do need to update the 401(k) number, so is it better just to have that all together?"}]}]}]}, {"ts": "1741372788.428329", "text": "<@U0690EB5JE5> <@U07M6QKHUC9> We have an issue. Something happened last night and the cycle seems to have republished and reset everyone to 100%", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741372788.428329", "reply_count": 40, "reactions": [{"name": "eyes", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "yvqtk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " We have an issue. Something happened last night and the cycle seems to have republished and reset everyone to 100%"}]}]}]}, {"ts": "1741372840.797879", "text": "is this for <PERSON><PERSON><PERSON>?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3fTvr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "is this for <PERSON><PERSON><PERSON>?"}]}]}]}, {"ts": "1741385573.728429", "text": "<@U0690EB5JE5> can we make this update to the Tithely env. The first is a new role and the second is an update to a current payband", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741385573.728429", "reply_count": 11, "files": [{"id": "F08GVJ4UQSG", "created": 1741385567, "timestamp": 1741385567, "name": "Bands 3.7 Tithe.ly_CompensationBandsTemplate_202502252051_utc (1).xlsx", "title": "Bands 3.7 Tithe.ly_CompensationBandsTemplate_202502252051_utc (1).xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 10751, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GVJ4UQSG/bands_3.7_tithe.ly_compensationbandstemplate_202502252051_utc__1_.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GVJ4UQSG/download/bands_3.7_tithe.ly_compensationbandstemplate_202502252051_utc__1_.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GVJ4UQSG-8e58994b2e/bands_3.7_tithe.ly_compensationbandstemplate_202502252051_utc__1__converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GVJ4UQSG-8e58994b2e/bands_3.7_tithe.ly_compensationbandstemplate_202502252051_utc__1__thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GVJ4UQSG/bands_3.7_tithe.ly_compensationbandstemplate_202502252051_utc__1_.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GVJ4UQSG-d3c3033268", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "6oPKf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we make this update to the Tithely env. The first is a new role and the second is an update to a current payband"}]}]}]}, {"ts": "1741707997.936689", "text": "<@U07EJ2LP44S> we do show the breakdown of merit components in both my tasks and in comp planner view. It's not perfect but we do have this feature.  We also have the ability to adjust columns freeze. Is there something more to what <PERSON> is asking?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ATVM8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " we do show the breakdown of merit components in both my tasks and in comp planner view. It's not perfect but we do have this feature.  We also have the ability to adjust columns freeze. Is there something more to what <PERSON> is asking?"}]}]}]}, {"ts": "1741708188.658629", "text": "She’s wanting column freeze by user", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741708188.658629", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Nq02i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She’s wanting column freeze by user"}]}]}]}, {"ts": "1741712253.787109", "text": "<@U0690EB5JE5> Check out <PERSON>'s comments on Tithely channel. Can you address this?\n\nHello! I have a question about the cycle insights tab. It looks like the numbers aren’t reflective of the cycle. For example, the total CX budget is $145k but the insights are showing it as $118k (screenshots attached). This is the same case for all departments. The total number is off because of that as well. Any ideas on how to correct this?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "as7pg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Check out <PERSON>'s comments on Tithely channel. Can you address this?\n\nHello! I have a question about the cycle insights tab. It looks like the numbers aren’t reflective of the cycle. For example, the total CX budget is $145k but the insights are showing it as $118k (screenshots attached). This is the same case for all departments. The total number is off because of that as well. Any ideas on how to correct this?"}]}]}]}, {"ts": "1741712840.084959", "text": "<@U07M6QKHUC9> will check in sometime, will need to debug. Can this wait till tomorrow as this needs code fix and deployment if there is a bug.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "2UCrE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " will check in sometime, will need to debug. Can this wait till tomorrow as this needs code fix and deployment if there is a bug."}]}]}]}, {"ts": "1741712868.281679", "text": "ideally today since they are in the middle of cycle", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nOTLm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ideally today since they are in the middle of cycle"}]}]}]}, {"ts": "1741713163.764809", "text": "Let me check, but would push back deployments at this time.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9xv/f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me check, but would push back deployments at this time."}]}]}]}, {"ts": "1741714879.962809", "text": "It might be because they did an override of the budget? I am wondering if the original % allocation is being applied, not the edited allocation changes they made.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nom04", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It might be because they did an override of the budget? I am wondering if the original % allocation is being applied, not the edited allocation changes they made."}]}]}]}, {"ts": "1741714937.944599", "text": "<@U07EJ2LP44S> did they edit the budgets?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "JdOuJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " did they edit the budgets?"}]}]}]}, {"ts": "1741714968.490079", "text": "Yes they edited in the allocations page", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "bInIU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes they edited in the allocations page"}]}]}]}, {"ts": "1741714973.271669", "text": "before the cycle started but yes", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6VchR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "before the cycle started but yes"}]}]}]}, {"ts": "1741714984.687119", "text": "The calculation logic looks complicated in the code for insights, its difficult to debug without any details.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mqfG6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The calculation logic looks complicated in the code for insights, its difficult to debug without any details."}]}]}]}, {"ts": "1741715057.490489", "text": "ok. <@U07M6QKHUC9> I will confirm if its because of the edited numbers, if not then This definitely takes time as the code is very complicated to debug and fix as well.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RF495", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok. "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I will confirm if its because of the edited numbers, if not then This definitely takes time as the code is very complicated to debug and fix as well."}]}]}]}, {"ts": "1741715093.490609", "text": "on a call. will review later", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qRCfI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "on a call. will review later"}]}]}]}, {"ts": "1741715465.272579", "text": "<@U07M6QKHUC9> yes <PERSON> is correct. The numbers in insights are system calculated but merit table is manually edited numbers. This cannot be fixed as the edited numbers are at the manager level.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XzlYK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " yes <PERSON> is correct. The numbers in insights are system calculated but merit table is manually edited numbers. This cannot be fixed as the edited numbers are at the manager level."}]}]}]}, {"ts": "1741715493.195519", "text": "Thank You <@U07EJ2LP44S> , You saved me quite some time :slightly_smiling_face: .", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pq+zh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank You "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " , You saved me quite some time "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " ."}]}]}]}, {"ts": "1741715611.834709", "text": "You're welcome L<PERSON>. I try.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3ta6c", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "You're welcome L<PERSON>. I try."}]}]}]}, {"ts": "1741716018.720809", "text": "are we saying that if admin makes changes to allocation in comp builder, and publishes the cycle, then cycle insights are meaningless for budget utilization metrics?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "2I3hl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "are we saying that if admin makes changes to allocation in comp builder, and publishes the cycle, then cycle insights are meaningless for budget utilization metrics?"}]}]}]}, {"ts": "1741716225.308839", "text": "Yes that’s correct ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "B/TgJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes that’s correct "}]}]}]}, {"ts": "1741716285.391739", "text": "Functionally we cannot fix this as numbers are edited at manager level and there is no way we can fix this with current allocation behaviour ", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1741716388.000000"}, "blocks": [{"type": "rich_text", "block_id": "FVDqu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Functionally we cannot"}, {"type": "text", "text": " fix this"}, {"type": "text", "text": " as numbers are edited at manager level and there is no way we can fix this with current allocation behaviour "}]}]}]}, {"ts": "1741716511.279239", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1741727623.563449", "text": "Alright y'all, logging off! It's been a pleasure.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9", "U0690EB5JE5", "U06HN8XDC5A"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "TDbSa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Alright y'all, logging off! It's been a pleasure."}]}]}]}, {"ts": "1741745000.103309", "text": "Good luck <PERSON>!", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rsMTs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Good luck <PERSON>!"}]}]}]}, {"ts": "1741745089.257159", "text": "All the best Amanda!", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "k2Hen", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All the best Amanda!"}]}]}]}]}