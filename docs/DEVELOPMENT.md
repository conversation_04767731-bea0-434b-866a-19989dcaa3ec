# RAGSearch Development Guide

This document provides comprehensive guidelines for developers working on the RAGSearch project.

## Development Environment

### Prerequisites

- Python 3.10+
- PostgreSQL 14+ with pgvector extension
- Qdrant (vector database)
- Poetry (dependency management)

### Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd RAGSearch
   ```

2. Set up a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   poetry install
   ```

4. Create a `.env` file based on `.env.example`:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. Set up the database:
   ```bash
   python manage.py migrate
   ```

6. Create a superuser:
   ```bash
   python manage.py createsuperuser
   ```

7. Run the development server:
   ```bash
   python manage.py runserver
   ```

### Docker Development Environment

1. Build and start the containers:
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

2. Run migrations:
   ```bash
   docker-compose -f docker-compose.dev.yml exec web python manage.py migrate
   ```

3. Create a superuser:
   ```bash
   docker-compose -f docker-compose.dev.yml exec web python manage.py createsuperuser
   ```

## Project Structure

The project follows a Django-based structure with a focus on modularity:

```
RAGSearch/
├── multi_source_rag/         # Main Django project
│   ├── apps/                 # Django applications
│   │   ├── accounts/         # User and tenant management
│   │   ├── api/              # REST API endpoints
│   │   ├── core/             # Core utilities and shared functionality
│   │   ├── documents/        # Document processing and storage
│   │   └── search/           # Search and RAG functionality
│   ├── settings/             # Django settings
│   └── urls.py               # URL routing
├── scripts/                  # Utility scripts
├── tests/                    # Test suite
└── docs/                     # Documentation
```

### Key Modules

#### apps/accounts

Handles user authentication, tenant management, and user profiles.

#### apps/documents

Manages document sources, ingestion, processing, and storage.

Key components:
- `interfaces/`: Source-specific interfaces for document retrieval
- `models/`: Django models for document storage
- `processors/`: Document processing components
- `services/`: Business logic for document operations

#### apps/search

Implements search functionality and RAG capabilities.

Key components:
- `services/`: Search and RAG services
- `vector_store/`: Vector database integration
- `query_engine/`: Query processing and enhancement

#### apps/core

Provides shared utilities and core functionality.

Key components:
- `utils/`: Shared utility functions
- `vector_store/`: Vector database abstraction
- `llm/`: Language model integration

## Coding Standards

### Python Style Guide

- Follow PEP 8 style guide
- Use 4 spaces for indentation
- Maximum line length of 100 characters
- Use docstrings for all public classes and methods
- Use type hints for function parameters and return values

### Django Best Practices

- Use Django's ORM for database operations
- Keep views thin, move business logic to services
- Use Django's form validation for input validation
- Follow Django's security best practices

### Testing

- Write tests for all new functionality
- Maintain test coverage above 80%
- Use pytest for writing tests
- Use factories for test data creation

## Development Workflow

### Feature Development

1. Create a new branch from `main`:
   ```bash
   git checkout -b feature/feature-name
   ```

2. Implement the feature with tests

3. Run the test suite:
   ```bash
   python manage.py test
   ```

4. Submit a pull request to `main`

### Code Review Process

1. All code changes require at least one review
2. Reviewers should check for:
   - Functionality
   - Code quality
   - Test coverage
   - Documentation
   - Performance considerations

### Continuous Integration

The project uses GitHub Actions for CI/CD:

- Automated tests run on all pull requests
- Code quality checks (flake8, black)
- Test coverage reporting

## Advanced Development Topics

### Adding a New Document Source

1. Create a new source interface in `apps/documents/interfaces/`:

```python
from .base import BaseSourceInterface

class NewSourceInterface(BaseSourceInterface):
    source_type = "new_source"
    
    def __init__(self, config):
        super().__init__(config)
        # Initialize source-specific configuration
        
    def fetch_documents(self):
        # Implement document fetching logic
        pass
        
    def process_documents(self, documents):
        # Implement document processing logic
        pass
```

2. Register the interface in `apps/documents/interfaces/__init__.py`:

```python
from .new_source import NewSourceInterface

SOURCE_INTERFACES = {
    "local_slack": LocalSlackSourceInterface,
    "github": GitHubSourceInterface,
    "new_source": NewSourceInterface,
}
```

3. Add source-specific models if needed

4. Implement tests for the new source

### Implementing a New Chunking Strategy

1. Create a new chunker in `apps/documents/processors/`:

```python
from .base import BaseChunker

class NewChunker(BaseChunker):
    def __init__(self, config):
        super().__init__(config)
        # Initialize chunker-specific configuration
        
    def chunk_document(self, document):
        # Implement chunking logic
        pass
```

2. Register the chunker in `apps/documents/processors/__init__.py`

3. Add configuration options to the document source model

### Extending the Vector Store

1. Create a new vector store implementation in `apps/core/vector_store/`:

```python
from .base import BaseVectorStore

class NewVectorStore(BaseVectorStore):
    def __init__(self, config):
        super().__init__(config)
        # Initialize vector store-specific configuration
        
    def create_collection(self, name, dimension):
        # Implement collection creation
        pass
        
    def add_documents(self, collection_name, documents, embeddings):
        # Implement document addition
        pass
        
    def search(self, collection_name, query_embedding, top_k=10):
        # Implement search
        pass
```

2. Register the vector store in `apps/core/vector_store/__init__.py`

3. Update the vector store factory to support the new implementation

### Performance Optimization

#### Database Optimization

- Use select_related and prefetch_related for related objects
- Create appropriate indexes for frequently queried fields
- Use bulk operations for batch processing

#### Vector Search Optimization

- Use approximate nearest neighbor search for large collections
- Implement caching for frequent queries
- Use batch processing for embeddings generation

#### Asynchronous Processing

- Use Celery for background tasks
- Implement task queues for document processing
- Use Django Channels for real-time updates

## Troubleshooting

### Common Issues

#### Vector Database Connection Issues

- Check that Qdrant is running and accessible
- Verify connection settings in `.env`
- Check for firewall or network issues

#### Document Processing Failures

- Check document format and encoding
- Verify source configuration
- Check for rate limiting or API issues

#### Search Performance Issues

- Monitor query execution time
- Check vector index performance
- Consider index optimization or sharding

### Debugging Tools

- Django Debug Toolbar for request profiling
- pgAdmin for database inspection
- Qdrant dashboard for vector database monitoring

## Deployment

### Production Deployment

1. Build the production Docker image:
   ```bash
   docker build -t ragsearch:latest .
   ```

2. Deploy using Docker Compose:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

3. Run migrations:
   ```bash
   docker-compose -f docker-compose.prod.yml exec web python manage.py migrate
   ```

### Scaling Considerations

- Use connection pooling for database connections
- Implement caching for frequent queries
- Consider horizontal scaling for the web tier
- Use load balancing for multiple instances
- Implement database replication for read scaling
