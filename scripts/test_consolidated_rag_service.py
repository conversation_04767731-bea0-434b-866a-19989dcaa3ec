#!/usr/bin/env python
"""
Comprehensive Test Script for Consolidated RAG Service

This script tests the new ConsolidatedRAGService against the existing RAGService
to ensure identical functionality and performance before migration.

Usage:
    python scripts/test_consolidated_rag_service.py
"""

import os
import sys
import django
import logging
import time
import json
from datetime import datetime
from typing import Dict, Any, List, Tuple

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.search.services.rag_service import RAGService
from apps.search.services.rag_service_new import ConsolidatedRAGService
from apps.search.models import SearchResult, ResultCitation

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class RAGServiceComparator:
    """Compare old and new RAG services for identical functionality."""
    
    def __init__(self):
        """Initialize the comparator with test data."""
        self.test_queries = [
            {
                "query": "What are the latest issues reported by Amanda?",
                "description": "Issue tracking query",
                "features": {"use_query_expansion": False, "use_multi_step_reasoning": False}
            },
            {
                "query": "What problems did Rachel mention in the engineering channel?",
                "description": "Technical troubleshooting query",
                "features": {"use_query_expansion": True, "use_multi_step_reasoning": False}
            },
            {
                "query": "What's the latest on Curana project development?",
                "description": "Project status query with expansion",
                "features": {"use_query_expansion": True, "use_multi_step_reasoning": False}
            },
            {
                "query": "Explain the authentication implementation and related issues",
                "description": "Complex multi-step reasoning query",
                "features": {"use_query_expansion": False, "use_multi_step_reasoning": True}
            },
            {
                "query": "Show me all bug reports and their solutions",
                "description": "Comprehensive query with all features",
                "features": {"use_query_expansion": True, "use_multi_step_reasoning": True}
            }
        ]
        
        self.results = {
            "old_service": [],
            "new_service": [],
            "comparisons": []
        }
        
        # Get test user and tenant
        self.user = User.objects.filter(username__in=['mahesh', 'testuser', 'admin']).first()
        self.tenant = Tenant.objects.filter(slug='stride').first()
        
        if not self.user:
            raise ValueError("No test user found. Please create a user first.")
        if not self.tenant:
            raise ValueError("No 'stride' tenant found. Please create tenant first.")
            
        logger.info(f"Using test user: {self.user.username}, tenant: {self.tenant.slug}")

    def test_service_initialization(self):
        """Test that both services initialize correctly."""
        logger.info("🔧 Testing service initialization...")
        
        try:
            # Test old service
            old_service = RAGService(user=self.user, tenant_slug=self.tenant.slug)
            logger.info("✅ Old RAGService initialized successfully")
            
            # Test new service
            new_service = ConsolidatedRAGService(user=self.user, tenant_slug=self.tenant.slug)
            logger.info("✅ New ConsolidatedRAGService initialized successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Service initialization failed: {str(e)}")
            return False

    def run_search_comparison(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run a search on both services and compare results."""
        query = query_data["query"]
        features = query_data["features"]
        
        logger.info(f"🔍 Testing query: '{query}'")
        logger.info(f"   Features: {features}")
        
        comparison = {
            "query": query,
            "description": query_data["description"],
            "features": features,
            "old_service": {},
            "new_service": {},
            "comparison": {}
        }
        
        # Test old service
        try:
            old_service = RAGService(user=self.user, tenant_slug=self.tenant.slug)
            
            start_time = time.time()
            old_result, old_docs = old_service.search(
                query_text=query,
                top_k=20,
                min_relevance_score=0.15,
                **features
            )
            old_time = time.time() - start_time
            
            old_citations = old_result.citations.count()
            old_stats = old_service.get_stats()
            
            comparison["old_service"] = {
                "processing_time": old_time,
                "answer_length": len(old_result.generated_answer),
                "citations_count": old_citations,
                "documents_count": len(old_docs),
                "retriever_score": old_result.retriever_score_avg,
                "confidence_score": old_result.llm_confidence_score,
                "stats": old_stats,
                "success": True
            }
            
            logger.info(f"   Old service: {old_time:.2f}s, {old_citations} citations, {len(old_docs)} docs")
            
        except Exception as e:
            logger.error(f"   ❌ Old service failed: {str(e)}")
            comparison["old_service"] = {"success": False, "error": str(e)}
        
        # Test new service
        try:
            new_service = ConsolidatedRAGService(user=self.user, tenant_slug=self.tenant.slug)
            
            start_time = time.time()
            new_result, new_docs = new_service.search(
                query_text=query,
                top_k=20,
                min_relevance_score=0.15,
                **features
            )
            new_time = time.time() - start_time
            
            new_citations = new_result.citations.count()
            new_stats = new_service.get_stats()
            
            comparison["new_service"] = {
                "processing_time": new_time,
                "answer_length": len(new_result.generated_answer),
                "citations_count": new_citations,
                "documents_count": len(new_docs),
                "retriever_score": new_result.retriever_score_avg,
                "confidence_score": new_result.llm_confidence_score,
                "stats": new_stats,
                "success": True
            }
            
            logger.info(f"   New service: {new_time:.2f}s, {new_citations} citations, {len(new_docs)} docs")
            
        except Exception as e:
            logger.error(f"   ❌ New service failed: {str(e)}")
            comparison["new_service"] = {"success": False, "error": str(e)}
        
        # Compare results
        if comparison["old_service"].get("success") and comparison["new_service"].get("success"):
            old_data = comparison["old_service"]
            new_data = comparison["new_service"]
            
            comparison["comparison"] = {
                "time_difference": new_data["processing_time"] - old_data["processing_time"],
                "time_improvement": ((old_data["processing_time"] - new_data["processing_time"]) / old_data["processing_time"]) * 100,
                "citations_match": old_data["citations_count"] == new_data["citations_count"],
                "documents_match": old_data["documents_count"] == new_data["documents_count"],
                "answer_length_difference": new_data["answer_length"] - old_data["answer_length"],
                "score_difference": new_data["retriever_score"] - old_data["retriever_score"]
            }
            
            # Log comparison
            time_diff = comparison["comparison"]["time_improvement"]
            citations_match = comparison["comparison"]["citations_match"]
            docs_match = comparison["comparison"]["documents_match"]
            
            logger.info(f"   📊 Comparison: {time_diff:+.1f}% time, citations match: {citations_match}, docs match: {docs_match}")
        
        return comparison

    def run_comprehensive_test(self):
        """Run comprehensive test suite comparing both services."""
        logger.info("🚀 Starting comprehensive RAG service comparison...")
        logger.info("=" * 80)
        
        # Test initialization
        if not self.test_service_initialization():
            logger.error("❌ Service initialization failed. Aborting tests.")
            return False
        
        # Run search comparisons
        logger.info("\n📋 Running search comparisons...")
        
        for i, query_data in enumerate(self.test_queries, 1):
            logger.info(f"\n--- Test {i}/{len(self.test_queries)} ---")
            comparison = self.run_search_comparison(query_data)
            self.results["comparisons"].append(comparison)
        
        # Generate summary
        self.generate_summary()
        
        return True

    def generate_summary(self):
        """Generate comprehensive test summary."""
        logger.info("\n" + "=" * 80)
        logger.info("📊 COMPREHENSIVE TEST SUMMARY")
        logger.info("=" * 80)
        
        successful_tests = [c for c in self.results["comparisons"] 
                          if c["old_service"].get("success") and c["new_service"].get("success")]
        
        if not successful_tests:
            logger.error("❌ No successful tests to compare!")
            return
        
        # Calculate averages
        avg_old_time = sum(c["old_service"]["processing_time"] for c in successful_tests) / len(successful_tests)
        avg_new_time = sum(c["new_service"]["processing_time"] for c in successful_tests) / len(successful_tests)
        avg_improvement = ((avg_old_time - avg_new_time) / avg_old_time) * 100
        
        total_old_citations = sum(c["old_service"]["citations_count"] for c in successful_tests)
        total_new_citations = sum(c["new_service"]["citations_count"] for c in successful_tests)
        
        citations_match_count = sum(1 for c in successful_tests if c["comparison"]["citations_match"])
        docs_match_count = sum(1 for c in successful_tests if c["comparison"]["documents_match"])
        
        logger.info(f"✅ Successful Tests: {len(successful_tests)}/{len(self.test_queries)}")
        logger.info(f"⏱️  Average Processing Time:")
        logger.info(f"   Old Service: {avg_old_time:.2f}s")
        logger.info(f"   New Service: {avg_new_time:.2f}s")
        logger.info(f"   Improvement: {avg_improvement:+.1f}%")
        logger.info(f"📚 Citations:")
        logger.info(f"   Old Service Total: {total_old_citations}")
        logger.info(f"   New Service Total: {total_new_citations}")
        logger.info(f"   Exact Matches: {citations_match_count}/{len(successful_tests)}")
        logger.info(f"📄 Document Matches: {docs_match_count}/{len(successful_tests)}")
        
        # Feature-specific analysis
        logger.info(f"\n🎯 Feature-Specific Analysis:")
        
        expansion_tests = [c for c in successful_tests if c["features"]["use_query_expansion"]]
        multi_step_tests = [c for c in successful_tests if c["features"]["use_multi_step_reasoning"]]
        
        if expansion_tests:
            avg_expansion_improvement = sum(c["comparison"]["time_improvement"] for c in expansion_tests) / len(expansion_tests)
            logger.info(f"   Query Expansion: {len(expansion_tests)} tests, {avg_expansion_improvement:+.1f}% avg improvement")
        
        if multi_step_tests:
            avg_multi_step_improvement = sum(c["comparison"]["time_improvement"] for c in multi_step_tests) / len(multi_step_tests)
            logger.info(f"   Multi-Step Reasoning: {len(multi_step_tests)} tests, {avg_multi_step_improvement:+.1f}% avg improvement")
        
        # Overall assessment
        logger.info(f"\n🎯 OVERALL ASSESSMENT:")
        
        if len(successful_tests) == len(self.test_queries):
            logger.info("✅ ALL TESTS PASSED - Services are functionally equivalent")
        else:
            logger.warning(f"⚠️  {len(self.test_queries) - len(successful_tests)} tests failed")
        
        if avg_improvement > 0:
            logger.info(f"🚀 PERFORMANCE IMPROVED by {avg_improvement:.1f}%")
        elif avg_improvement < -5:
            logger.warning(f"⚠️  PERFORMANCE DEGRADED by {abs(avg_improvement):.1f}%")
        else:
            logger.info("⚖️  PERFORMANCE EQUIVALENT")
        
        if citations_match_count == len(successful_tests):
            logger.info("✅ CITATION CONSISTENCY - Perfect match")
        else:
            logger.warning(f"⚠️  CITATION VARIANCE - {citations_match_count}/{len(successful_tests)} exact matches")


def main():
    """Main test execution."""
    try:
        comparator = RAGServiceComparator()
        success = comparator.run_comprehensive_test()
        
        if success:
            logger.info("\n🎉 Test suite completed successfully!")
            return 0
        else:
            logger.error("\n❌ Test suite failed!")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Test execution failed: {str(e)}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit(main())
