{"date": "2024-08-28", "channel_id": "C065QSSNH8A", "message_count": 17, "messages": [{"ts": "1724860800.274209", "text": "Sure", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "3dmZ7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure"}]}]}]}, {"ts": "1724860767.240129", "text": "<@U0690EB5JE5> can we make this an agenda time on next week Tuesday's eng meeting? We need to double check if all the fixes as mentioned in this doc are complete without bugs, and also discuss plan for TBD items", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722856847.816419", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "ftwkG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we make this an agenda time on next week Tuesday's eng meeting? We need to double check if all the fixes as mentioned in this doc are complete without bugs, and also discuss plan for TBD items"}]}]}]}, {"ts": "1724859219.456459", "text": "First call for feedback on closing a cycle: <PERSON><PERSON> with CWA <https://us06web.zoom.us/rec/share/S6fLy_xXJeZn3jwLSm7Y-Gd1KID2V5bXDdoVY5Qc0c9v-_XiCXBpUkjyQmeshFZq.Uwi3RW6FQO86LcnY>\nPasscode: 9+i&amp;xUcN", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "M9CGd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "First call for feedback on closing a cycle: Alyssa with CWA "}, {"type": "link", "url": "https://us06web.zoom.us/rec/share/S6fLy_xXJeZn3jwLSm7Y-Gd1KID2V5bXDdoVY5Qc0c9v-_XiCXBpUkjyQmeshFZq.Uwi3RW6FQO86LcnY"}, {"type": "text", "text": "\nPasscode: 9+i&xUcN"}]}]}]}, {"ts": "1724854244.584199", "text": "Thats due to the access tokens we have in the db are invalid. I will fix that", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RIXdO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thats due to the access tokens we have in the db are invalid. I will fix that"}]}]}]}, {"ts": "1724854196.871249", "text": "<@U07EJ2LP44S> will take a look ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5tpRa", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " will take a look "}]}]}]}, {"ts": "1724853795.976939", "text": "on Valegenesis integrations page:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1724853795.976939", "reply_count": 2, "files": [{"id": "F07JWQ0R0F5", "created": 1724853794, "timestamp": 1724853794, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 29450, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07JWQ0R0F5/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07JWQ0R0F5/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JWQ0R0F5-40c1b64c65/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JWQ0R0F5-40c1b64c65/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JWQ0R0F5-40c1b64c65/image_360.png", "thumb_360_w": 360, "thumb_360_h": 248, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JWQ0R0F5-40c1b64c65/image_480.png", "thumb_480_w": 480, "thumb_480_h": 331, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JWQ0R0F5-40c1b64c65/image_160.png", "original_w": 540, "original_h": 372, "thumb_tiny": "AwAhADDTooPSmGMeZ5mTnGMUALk/3TRuP9w038HFHGOj0AO3H+6aUHJ+6RTMD/bo7dHoAkpmz/bb86fRQAzZ/tt+dKq4/iJ+tOooC4UhO<PERSON>ppaCM9aACiiigAooooAKKKKAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07JWQ0R0F5/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07JWQ0R0F5-d89974ea8a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "U0lMN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "on Valegenesis integrations page:"}]}]}]}, {"ts": "1724822415.555999", "text": "Agenda for today:\n• Comp bands for hourly/part time employees\n• New customer requirements from CWA and DegenKolb\n", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724822816.000000"}, "blocks": [{"type": "rich_text", "block_id": "yeBk1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Comp bands for hourly/part time employees"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "New customer requirements from CWA and DegenKolb"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1724805821.016369", "text": "<@U04DS2MBWP4> <@U07EJ2LP44S> Here is the initial product brief for Manager Enablement, with recommended customer interview goals and prompts at the end of the doc, in blue.\n\nPlease let me know what questions and feedback you have. I'm happy to connect over <PERSON>m with the two of you to review tomorrow.\n\nI'd love to help with customer interviews! Please let me know if you'd like to connect me with managers and HR folks from our current customer or prospect base. Otherwise, I can reach out to some personal contacts who fit our ICP.\n\n<https://docs.google.com/document/d/1UKkBEvaSakzhgg5ZT4UA5feoXc-9H6Ec0nRtl1jBHiI/edit>", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724805821.016369", "reply_count": 4, "files": [{"id": "F07JTJNMJ6N", "created": 1724805823, "timestamp": 1724805823, "name": "Manager Enablement Product Brief", "title": "Manager Enablement Product Brief &amp; PRD", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07HCJ07H7G", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1UKkBEvaSakzhgg5ZT4UA5feoXc-9H6Ec0nRtl1jBHiI", "external_url": "https://docs.google.com/document/d/1UKkBEvaSakzhgg5ZT4UA5feoXc-9H6Ec0nRtl1jBHiI/edit", "url_private": "https://docs.google.com/document/d/1UKkBEvaSakzhgg5ZT4UA5feoXc-9H6Ec0nRtl1jBHiI/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTRYnIx/Km7n9/++ac4GRkD8aRR6YH0FACbm9/++acpJPOfyxTqKACiiigBjjkUq8cUN0zTQM+v60ASUU3J/wAijJ/yKAHUUUUANbPbP4Ugz15/OlfqKbj/ADigB+T6D86Mn0poH1/KlAAPf8qAHUUUUAf/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07JTJNMJ6N/manager_enablement_product_brief", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "mqkCC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Here is the initial product brief for Manager Enablement, with recommended customer interview goals and prompts at the end of the doc, in blue.\n\nPlease let me know what questions and feedback you have. I'm happy to connect over <PERSON><PERSON> with the two of you to review tomorrow.\n\nI'd love to help with customer interviews! Please let me know if you'd like to connect me with managers and HR folks from our current customer or prospect base. Otherwise, I can reach out to some personal contacts who fit our ICP.\n\n"}, {"type": "link", "url": "https://docs.google.com/document/d/1UKkBEvaSakzhgg5ZT4UA5feoXc-9H6Ec0nRtl1jBHiI/edit"}]}]}]}, {"ts": "1724794605.466969", "text": "<@U07EJ2LP44S> I have prepared instruction to be shared with Cainwatters IT team here so we can use SAML for them. I need to sync up with <PERSON><PERSON><PERSON> later in evening once and then we can share these with CWA <https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724794605.466969", "reply_count": 1, "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "kdeNd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have prepared instruction to be shared with Cainwatters IT team here so we can use SAML for them. I need to sync up with <PERSON><PERSON><PERSON> later in evening once and then we can share these with CWA "}, {"type": "link", "url": "https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit"}]}]}]}, {"ts": "1724792004.434219", "text": "<@U04DKEFP1K8> what;s the status of putting ROI calculator on the pricing page", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1724792004.434219", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "oSkCs", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what;s the status of putting ROI calculator on the pricing page"}]}]}]}, {"ts": "1724791794.389569", "text": "<@U07EJ2LP44S> :point_up:", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eLf5K", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "point_up", "unicode": "261d-fe0f"}]}]}]}, {"ts": "1724791773.997109", "text": "our ROI calculator\n<https://docs.google.com/spreadsheets/d/1m067Ffb6I4iWHFw8ld-yYR9Z1zVkFv2noaj6htpUzLc/edit?gid=0#gid=0>", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F07JSLXC8PM", "created": 1724791776, "timestamp": 1724791776, "name": "Compiify ROI Calculatorv2", "title": "Compiify ROI Calculatorv2", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1m067Ffb6I4iWHFw8ld-yYR9Z1zVkFv2noaj6htpUzLc", "external_url": "https://docs.google.com/spreadsheets/d/1m067Ffb6I4iWHFw8ld-yYR9Z1zVkFv2noaj6htpUzLc/edit?gid=0#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1m067Ffb6I4iWHFw8ld-yYR9Z1zVkFv2noaj6htpUzLc/edit?gid=0#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHRJO7AI/GlyR1xTXOGxg/lRuHofyoAdmlpobPY/lTqACiiigCOQ4bqR+FG4f3z+VSUUANH+8T+FOoooAKKKKACiiigAooooAKKKKAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07JSLXC8PM/compiify_roi_calculatorv2", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "zKB+M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "our ROI calculator\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1m067Ffb6I4iWHFw8ld-yYR9Z1zVkFv2noaj6htpUzLc/edit?gid=0#gid=0"}]}]}]}, {"ts": "1724791713.287069", "text": "<@U07EJ2LP44S> but also a good read for everyone\n\n<https://www.novoinsights.com/insights/measure-the-successof-your-annual-compensation-cycle>\n<https://www.aeqium.com/post/how-to-measure-the-effectiveness-of-your-compensation-strategy>\n<https://www.pave.com/blog-posts/3-key-metrics-to-measure-and-report-the-success-of-your-merit-cycle>", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"image_url": "https://www.novoinsights.com/hubfs/Measure%20the%20Success%20of%20Your%20Cycle.png", "image_width": 5000, "image_height": 2613, "image_bytes": 261203, "from_url": "https://www.novoinsights.com/insights/measure-the-successof-your-annual-compensation-cycle", "id": 1, "original_url": "https://www.novoinsights.com/insights/measure-the-successof-your-annual-compensation-cycle", "fallback": "Measure the Success of Your Annual Compensation Cycle", "text": "annual compensation cycle metris", "title": "Measure the Success of Your Annual Compensation Cycle", "title_link": "https://www.novoinsights.com/insights/measure-the-successof-your-annual-compensation-cycle", "service_name": "novoinsights.com"}, {"from_url": "https://www.aeqium.com/post/how-to-measure-the-effectiveness-of-your-compensation-strategy", "id": 2, "original_url": "https://www.aeqium.com/post/how-to-measure-the-effectiveness-of-your-compensation-strategy", "fallback": "How to Measure the Effectiveness of your Compensation Strategy", "text": "Learn how forward-thinking people leaders from Airbnb, Meta, and Impossible Foods measure the efficiency of their compensation strategies.", "title": "How to Measure the Effectiveness of your Compensation Strategy", "title_link": "https://www.aeqium.com/post/how-to-measure-the-effectiveness-of-your-compensation-strategy", "service_name": "aeqium.com"}, {"image_url": "https://cdn.prod.website-files.com/63722fa7f631896277feb5c6/662fd00841adda44173be30d_merit-cycles-01.jpg", "image_width": 800, "image_height": 432, "image_bytes": 56185, "from_url": "https://www.pave.com/blog-posts/3-key-metrics-to-measure-and-report-the-success-of-your-merit-cycle", "id": 3, "original_url": "https://www.pave.com/blog-posts/3-key-metrics-to-measure-and-report-the-success-of-your-merit-cycle", "fallback": "3 Key Metrics to Measure and Report the Success of Your Merit Cycle | Pave | Data-Driven Total Compensation Platform", "text": "Analyzing your merit cycle is a necessary step in the process of delivering an exceptional compensation program.", "title": "3 Key Metrics to Measure and Report the Success of Your Merit Cycle | Pave | Data-Driven Total Compensation Platform", "title_link": "https://www.pave.com/blog-posts/3-key-metrics-to-measure-and-report-the-success-of-your-merit-cycle", "service_name": "pave.com"}], "blocks": [{"type": "rich_text", "block_id": "klu6O", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " but also a good read for everyone\n\n"}, {"type": "link", "url": "https://www.novoinsights.com/insights/measure-the-successof-your-annual-compensation-cycle"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://www.aeqium.com/post/how-to-measure-the-effectiveness-of-your-compensation-strategy"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://www.pave.com/blog-posts/3-key-metrics-to-measure-and-report-the-success-of-your-merit-cycle"}]}]}]}, {"ts": "1724790241.651709", "text": "Question about the future of Stride's pricing model... is it $8 pepm for all company employees, regardless of usage, or only $8 per Stride user seat? Wondering if there is financial incentive to get more types of users into Stride as part of manager enablement.", "user": "U07HCJ07H7G", "type": "message", "blocks": [{"type": "rich_text", "block_id": "cUrZf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Question about the future of Stride's pricing model... is it $8 pepm for all company employees, regardless of usage, or only $8 per Stride user seat? Wondering if there is financial incentive to get more types of users into Stride as part of manager enablement."}]}]}]}, {"ts": "1724785913.829499", "text": "Let's take off and recharge our batteries over the long weekend plus it's a national holiday.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1724785913.829499", "reply_count": 1, "reactions": [{"name": "heart", "users": ["U07EJ2LP44S", "U07HCJ07H7G", "U04DKEFP1K8"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "6hSrQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let's take off and recharge our batteries over the long weekend plus it's a national holiday."}]}]}]}, {"ts": "1724785839.309009", "text": "are others planning on working? my kids will be home and would love to spend some time with them", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3xeqK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "are others planning on working? my kids will be home and would love to spend some time with them"}]}]}]}, {"ts": "1724785556.334349", "text": "<!here> i will OOO of Monday Sep 2 ( labor day)", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "clap", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "s<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " i will OOO of Monday Sep 2 ( labor day)"}]}]}]}], "created_at": "2025-05-22T21:35:34.644008"}