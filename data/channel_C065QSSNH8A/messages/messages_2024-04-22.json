{"date": "2024-04-22", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1713726096.738279", "text": "<@U065H3M6WJV> I have merged all the jira's from different UAT as well as few other tasks like social login which are pending. Here is the link to the file <https://docs.google.com/spreadsheets/d/1NxksYgZAwiSREK0LDf6Ywmu5lsg62qLp/edit?usp=sharing&amp;ouid=113251126259695967737&amp;rtpof=true&amp;sd=true>\n\nCan you please review it ( especially the ones which are in to do state) and may be add a new column to define updated priority based on your analysis?\n\n<@U0690EB5JE5> Once <PERSON>'s reviews is complete, we can distribute them to the team ( at least critical and high priority ones).", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1713726096.738279", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "D/TGp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I have merged all the jira's from different UAT as well as few other tasks like social login which are pending. Here is the link to the file "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1NxksYgZAwiSREK0LDf6Ywmu5lsg62qLp/edit?usp=sharing&ouid=113251126259695967737&rtpof=true&sd=true"}, {"type": "text", "text": "\n\nCan you please review it ( especially the ones which are in to do state) and may be add a new column to define updated priority based on your analysis?\n\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Once <PERSON>'s reviews is complete, we can distribute them to the team ( at least critical and high priority ones)."}]}]}]}], "created_at": "2025-05-22T21:35:34.603516"}