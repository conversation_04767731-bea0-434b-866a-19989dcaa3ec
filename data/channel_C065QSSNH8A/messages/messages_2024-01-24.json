{"date": "2024-01-24", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1706119309.168149", "text": "<@U0658EW4B8D> How's your testing going - found any major issues for us yet? :wink:\n\nAnd <@U04DKEFP1K8>, will we need to have <PERSON> on a Zoom to walk through any of the adjustment letter process?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706119309.168149", "reply_count": 13, "blocks": [{"type": "rich_text", "block_id": "EP1Nm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " How's your testing going - found any major issues for us yet? "}, {"type": "emoji", "name": "wink", "unicode": "1f609"}, {"type": "text", "text": "\n\nAnd "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": ", will we need to have <PERSON> on a Zoom to walk through any of the adjustment letter process?"}]}]}]}, {"ts": "1706051197.903799", "text": "Next steps for DA production cycle:\n• Change to have blank prefills / no recommendations\n• Hide bonus &amp; pay band / compa ratio columns\n• Flatten the hierarchy for planning\n• Ensure we have the flag for &gt;9% bonus (COM-2113)\nWaiting on them:\n• Updated performance ratings\n• Updated ineligible-employee list\n• Final currency conversion values\n• Exact budget", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ngG12", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Next steps for DA production cycle:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Change to have blank prefills / no recommendations"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Hide bonus & pay band / compa ratio columns"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Flatten the hierarchy for planning"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Ensure we have the flag for >9% bonus (COM-2113)"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nWaiting on them:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Updated performance ratings"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Updated ineligible-employee list"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Final currency conversion values"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Exact budget"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1706050756.724549", "text": "Priorities for Eng for the next day:\n• :repeat: SDF: Allow blank prefill while still giving recommendations (<https://compiify.atlassian.net/browse/COM-2200|COM-2200> and <https://compiify.atlassian.net/browse/COM-2206|COM-2206>)\n• :repeat: SDF: Load Stellar's custom performance rating scale (<https://compiify.atlassian.net/browse/COM-2020|COM-2020>)\n• :repeat: Export / download support (<https://compiify.atlassian.net/browse/COM-2060|COM-2060>)\n• :repeat: Various fixes for filters (<https://compiify.atlassian.net/browse/COM-2204|COM-2204>, <https://compiify.atlassian.net/browse/COM-2139|COM-2139>)\n• DA: Flag for increases over 9% (<https://compiify.atlassian.net/browse/COM-2113|COM-2113>)\n• SDF: Allow inputting new titles for promotions (<https://compiify.atlassian.net/browse/COM-2198|COM-2198>)\n• Neuroflow / DA / SDF: Replace hardcoded cycle dates (<https://compiify.atlassian.net/browse/COM-1914|COM-1914>)\n• Various fixes for table sort (<https://compiify.atlassian.net/browse/COM-2205|COM-2205>, <https://compiify.atlassian.net/browse/COM-2054|COM-2054>, <https://compiify.atlassian.net/browse/COM-1990|COM-1990>)\nIf all these are addressed/assigned, next priorities are completing anything else in <https://compiify.atlassian.net/browse/COM-2086|Wave 2> and starting on <https://compiify.atlassian.net/browse/COM-2087|Wave 3> (top to bottom)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706050756.724549", "reply_count": 12, "edited": {"user": "U065H3M6WJV", "ts": "1706056718.000000"}, "blocks": [{"type": "rich_text", "block_id": "2mYV9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " SDF: Allow blank prefill while still giving recommendations ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2200", "text": "COM-2200"}, {"type": "text", "text": " and "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2206", "text": "COM-2206"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " SDF: Load Stellar's custom performance rating scale ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2020", "text": "COM-2020"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Export / download support ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2060", "text": "COM-2060"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Various fixes for filters ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2204", "text": "COM-2204"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2139", "text": "COM-2139"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "DA: Flag for increases over 9% ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2113", "text": "COM-2113"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF: Allow inputting new titles for promotions ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2198", "text": "COM-2198"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Neuroflow / DA / SDF: Replace hardcoded cycle dates ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1914", "text": "COM-1914"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Various fixes for table sort ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2205", "text": "COM-2205"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2054", "text": "COM-2054"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1990", "text": "COM-1990"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nIf all these are addressed/assigned, next priorities are completing anything else in "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2086", "text": "Wave 2"}, {"type": "text", "text": " and starting on "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " (top to bottom)"}]}]}]}, {"ts": "1706043950.613099", "text": "DA folks are able to login with their google login's :slightly_smiling_face:", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "meow_attention", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "CEyaV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA folks are able to login with their google login's "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}], "created_at": "2025-05-22T21:35:34.589286"}