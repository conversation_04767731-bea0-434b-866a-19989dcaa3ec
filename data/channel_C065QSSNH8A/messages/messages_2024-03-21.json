{"date": "2024-03-21", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1711045775.281219", "text": "pequity just launched their benchmarking data. It's AI powered\n<https://pequity.com/solutions/data>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1711045775.281219", "reply_count": 5, "edited": {"user": "U04DS2MBWP4", "ts": "1711045790.000000"}, "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "attachments": [{"image_url": "https://uploads-ssl.webflow.com/65d3793cb48b155bbd2ad553/65f8abbb3b7621b6719421ef_pequity_data_opengraph.png", "image_width": 1200, "image_height": 630, "image_bytes": 385073, "from_url": "https://pequity.com/solutions/data", "id": 1, "original_url": "https://pequity.com/solutions/data", "fallback": "Pequity Solutions | Compensation Data", "text": "Compensation data you don't have to share your employee census data to access.", "title": "Pequity Solutions | Compensation Data", "title_link": "https://pequity.com/solutions/data", "service_name": "pequity.com"}], "blocks": [{"type": "rich_text", "block_id": "uJham", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "pequity just launched their benchmarking data. It's AI powered\n"}, {"type": "link", "url": "https://pequity.com/solutions/data"}]}]}]}, {"ts": "1711036219.405269", "text": "<!here> Planned update is in progress currently ETA to bring services online 10am PST", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1711036219.405269", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Ud/GD", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Planned update is in progress currently ETA to bring services online 10am PST"}]}]}]}, {"ts": "1710992267.675949", "text": "<@U065H3M6WJV> Engineering is planning to update db configuration for compliance requirement tomorrow morning. There will be a downtime of 1.5 hours for upgrade to complete for DA and SDF between. Target is to complete everything before 10am. Let me know if the plan is okay and eng can proceed?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710992267.675949", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "6e7NX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Engineering is planning to update db configuration for compliance requirement tomorrow morning. There will be a downtime of 1.5 hours for upgrade to complete for DA and SDF between. Target is to complete everything before 10am. Let me know if the plan is okay and eng can proceed?"}]}]}]}, {"ts": "1710976375.075729", "text": "<@U065H3M6WJV> what's our current strategy/stop gap for customers who want to have custom fields or formula for say salary, bonus and equity calculations for merit planning?\n\nJust do it in the backend for now?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1710976375.075729", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "SdvlY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " what's our current strategy/stop gap for customers who want to have custom fields or formula for say salary, bonus and equity calculations for merit planning?\n\nJust do it in the backend for now?"}]}]}]}, {"ts": "1710972361.059869", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I met with <PERSON> from Nauto today, to walk through the cycle builder and capture more of their requirements &amp; feedback. Short version of <https://docs.google.com/document/d/1LvHRsNc_8LUKHm3MM2QdK3nbXw8VVicGQmHelxLfnX4/edit|notes here>, some of these are existing JIRAs but I'll need to create some new ones as well.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710972361.059869", "reply_count": 4, "edited": {"user": "U065H3M6WJV", "ts": "1710972366.000000"}, "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "hEo9Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I met with <PERSON> from Nauto today, to walk through the cycle builder and capture more of their requirements & feedback. Short version of "}, {"type": "link", "url": "https://docs.google.com/document/d/1LvHRsNc_8LUKHm3MM2QdK3nbXw8VVicGQmHelxLfnX4/edit", "text": "notes here"}, {"type": "text", "text": ", some of these are existing JIRAs but I'll need to create some new ones as well."}]}]}]}], "created_at": "2025-05-22T21:35:34.607830"}