"""
Configuration Management for Conversation-Aware RAG Features

This module provides configuration classes and utilities for managing
conversation-aware processing settings across the RAG system.
"""

import logging
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional
from enum import Enum

logger = logging.getLogger(__name__)


class ChunkingStrategy(Enum):
    """Available chunking strategies."""
    TIME_BASED = "time_based"
    CONVERSATION_AWARE = "conversation_aware"
    HYBRID = "hybrid"


class ConversationType(Enum):
    """Types of conversations that can be detected."""
    DISCUSSION = "discussion"
    THREAD = "thread"
    Q_AND_A = "q_and_a"
    ANNOUNCEMENT = "announcement"


@dataclass
class ConversationParsingConfig:
    """Configuration for conversation-aware parsing."""
    
    # Chunking strategy
    chunking_strategy: ChunkingStrategy = ChunkingStrategy.TIME_BASED
    
    # Conversation detection parameters
    max_conversation_gap_minutes: int = 30
    min_conversation_messages: int = 2
    preserve_thread_boundaries: bool = True
    
    # Node parser parameters
    chunk_size: int = 2048
    chunk_overlap: int = 200
    include_prev_next_rel: bool = True
    
    # Quality thresholds
    min_engagement_score: float = 0.1
    quality_threshold: float = 0.3
    
    # Content filtering
    filter_bots: bool = True
    include_system_messages: bool = False
    min_message_length: int = 10
    
    # Metadata enhancement
    extract_topics: bool = True
    calculate_engagement_metrics: bool = True
    detect_conversation_types: bool = True
    
    # Performance settings
    enable_caching: bool = True
    batch_size: int = 100
    max_memory_usage_mb: int = 512


@dataclass
class QueryEngineConfig:
    """Configuration for conversation-aware query engine."""
    
    # Query processing
    enable_conversation_context: bool = True
    max_conversation_history: int = 10
    context_window_size: int = 3
    
    # Retrieval parameters
    similarity_top_k: int = 10
    similarity_cutoff: float = 0.1
    
    # Response synthesis
    response_mode: str = "tree_summarize"
    use_async: bool = False
    
    # Conversation-specific features
    boost_recent_conversations: bool = True
    boost_high_engagement: bool = True
    prefer_thread_completeness: bool = True
    
    # Query enhancement
    enable_query_expansion: bool = False
    enable_context_injection: bool = True
    max_context_terms: int = 5


@dataclass
class IngestionConfig:
    """Configuration for conversation-aware ingestion."""
    
    # Pipeline selection
    use_conversation_pipeline: bool = False
    fallback_to_standard: bool = True
    
    # Processing parameters
    batch_size: int = 50
    max_retries: int = 3
    timeout_seconds: int = 300
    
    # Content type detection
    auto_detect_conversations: bool = True
    conversation_indicators: List[str] = field(default_factory=lambda: [
        'slack', 'chat', 'discussion', 'thread'
    ])
    
    # Error handling
    continue_on_error: bool = True
    create_fallback_chunks: bool = True
    log_processing_errors: bool = True


class ConversationConfigManager:
    """Manager for conversation-aware configuration."""
    
    def __init__(self):
        """Initialize configuration manager."""
        self._configs = {}
        self._default_configs = self._create_default_configs()
    
    def _create_default_configs(self) -> Dict[str, Any]:
        """Create default configurations."""
        return {
            'parsing': ConversationParsingConfig(),
            'query_engine': QueryEngineConfig(),
            'ingestion': IngestionConfig()
        }
    
    def get_parsing_config(self, tenant_slug: str = None, **overrides) -> ConversationParsingConfig:
        """
        Get parsing configuration for a tenant.
        
        Args:
            tenant_slug: Tenant identifier
            **overrides: Configuration overrides
            
        Returns:
            ConversationParsingConfig instance
        """
        base_config = self._default_configs['parsing']
        
        # Apply tenant-specific overrides if available
        if tenant_slug and tenant_slug in self._configs:
            tenant_config = self._configs[tenant_slug].get('parsing', {})
            base_config = self._merge_config(base_config, tenant_config)
        
        # Apply method overrides
        if overrides:
            base_config = self._merge_config(base_config, overrides)
        
        return base_config
    
    def get_query_engine_config(self, tenant_slug: str = None, **overrides) -> QueryEngineConfig:
        """
        Get query engine configuration for a tenant.
        
        Args:
            tenant_slug: Tenant identifier
            **overrides: Configuration overrides
            
        Returns:
            QueryEngineConfig instance
        """
        base_config = self._default_configs['query_engine']
        
        # Apply tenant-specific overrides if available
        if tenant_slug and tenant_slug in self._configs:
            tenant_config = self._configs[tenant_slug].get('query_engine', {})
            base_config = self._merge_config(base_config, tenant_config)
        
        # Apply method overrides
        if overrides:
            base_config = self._merge_config(base_config, overrides)
        
        return base_config
    
    def get_ingestion_config(self, tenant_slug: str = None, **overrides) -> IngestionConfig:
        """
        Get ingestion configuration for a tenant.
        
        Args:
            tenant_slug: Tenant identifier
            **overrides: Configuration overrides
            
        Returns:
            IngestionConfig instance
        """
        base_config = self._default_configs['ingestion']
        
        # Apply tenant-specific overrides if available
        if tenant_slug and tenant_slug in self._configs:
            tenant_config = self._configs[tenant_slug].get('ingestion', {})
            base_config = self._merge_config(base_config, tenant_config)
        
        # Apply method overrides
        if overrides:
            base_config = self._merge_config(base_config, overrides)
        
        return base_config
    
    def set_tenant_config(self, tenant_slug: str, config_type: str, config: Dict[str, Any]) -> None:
        """
        Set tenant-specific configuration.
        
        Args:
            tenant_slug: Tenant identifier
            config_type: Type of configuration ('parsing', 'query_engine', 'ingestion')
            config: Configuration dictionary
        """
        if tenant_slug not in self._configs:
            self._configs[tenant_slug] = {}
        
        self._configs[tenant_slug][config_type] = config
        logger.info(f"Updated {config_type} configuration for tenant {tenant_slug}")
    
    def _merge_config(self, base_config: Any, overrides: Dict[str, Any]) -> Any:
        """
        Merge configuration overrides with base configuration.
        
        Args:
            base_config: Base configuration object
            overrides: Override values
            
        Returns:
            Merged configuration object
        """
        # Create a copy of the base config
        if hasattr(base_config, '__dict__'):
            merged_dict = base_config.__dict__.copy()
        else:
            merged_dict = dict(base_config)
        
        # Apply overrides
        for key, value in overrides.items():
            if hasattr(base_config, key):
                merged_dict[key] = value
        
        # Create new config object of the same type
        config_class = type(base_config)
        return config_class(**merged_dict)
    
    def get_config_summary(self, tenant_slug: str = None) -> Dict[str, Any]:
        """
        Get configuration summary for a tenant.
        
        Args:
            tenant_slug: Tenant identifier
            
        Returns:
            Configuration summary dictionary
        """
        parsing_config = self.get_parsing_config(tenant_slug)
        query_config = self.get_query_engine_config(tenant_slug)
        ingestion_config = self.get_ingestion_config(tenant_slug)
        
        return {
            'tenant_slug': tenant_slug,
            'parsing': {
                'chunking_strategy': parsing_config.chunking_strategy.value,
                'conversation_gap_minutes': parsing_config.max_conversation_gap_minutes,
                'chunk_size': parsing_config.chunk_size,
                'quality_threshold': parsing_config.quality_threshold
            },
            'query_engine': {
                'conversation_context_enabled': query_config.enable_conversation_context,
                'similarity_top_k': query_config.similarity_top_k,
                'response_mode': query_config.response_mode
            },
            'ingestion': {
                'conversation_pipeline_enabled': ingestion_config.use_conversation_pipeline,
                'batch_size': ingestion_config.batch_size,
                'auto_detect_conversations': ingestion_config.auto_detect_conversations
            }
        }


# Global configuration manager instance
config_manager = ConversationConfigManager()


def get_conversation_config(tenant_slug: str = None, config_type: str = 'parsing', **overrides) -> Any:
    """
    Convenience function to get conversation configuration.
    
    Args:
        tenant_slug: Tenant identifier
        config_type: Type of configuration to retrieve
        **overrides: Configuration overrides
        
    Returns:
        Configuration object
    """
    if config_type == 'parsing':
        return config_manager.get_parsing_config(tenant_slug, **overrides)
    elif config_type == 'query_engine':
        return config_manager.get_query_engine_config(tenant_slug, **overrides)
    elif config_type == 'ingestion':
        return config_manager.get_ingestion_config(tenant_slug, **overrides)
    else:
        raise ValueError(f"Unknown config type: {config_type}")


def set_conversation_config(tenant_slug: str, config_type: str, **config_values) -> None:
    """
    Convenience function to set conversation configuration.
    
    Args:
        tenant_slug: Tenant identifier
        config_type: Type of configuration to set
        **config_values: Configuration values to set
    """
    config_manager.set_tenant_config(tenant_slug, config_type, config_values)
