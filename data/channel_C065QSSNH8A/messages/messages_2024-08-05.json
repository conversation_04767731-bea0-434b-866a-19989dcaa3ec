{"date": "2024-08-05", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1722872827.014349", "text": "I am making my way through the data templates; do we have a list of required fields vs optional ones? Is there any additional definition of fields outside of what is on the template? For example, there is job category and job family on the Employee Data template; how do they differ?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722872827.014349", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "mvOHX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am making my way through the data templates; do we have a list of required fields vs optional ones? Is there any additional definition of fields outside of what is on the template? For example, there is job category and job family on the Employee Data template; how do they differ?"}]}]}]}, {"ts": "1722856847.816419", "text": "Please review and comment on the doc and we can discuss one of eng discussion meetings.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1722856847.816419", "reply_count": 2, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "9sDjW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please review and comment on the doc and we can discuss one of eng discussion meetings."}]}]}]}, {"ts": "1722856821.349229", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> I reviewed SDF doc and created tickets wherever missing and included them in the doc itself. We have completed 90% of the work.\n<https://docs.google.com/document/d/1yYiSQ11gfk5sFnWzypMmyF0O6FrIrLiQsREY4bHMdn4/edit#heading=h.d9bd51bdpb2>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1722857223.000000"}, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F0735MHHZ2R", "created": 1715633202, "timestamp": 1715633202, "name": "SDF - Compiify improvements", "title": "[Internal doc] SDF - Compiify improvements", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1yYiSQ11gfk5sFnWzypMmyF0O6FrIrLiQsREY4bHMdn4", "external_url": "https://docs.google.com/document/d/1yYiSQ11gfk5sFnWzypMmyF0O6FrIrLiQsREY4bHMdn4/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1yYiSQ11gfk5sFnWzypMmyF0O6FrIrLiQsREY4bHMdn4/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTRbPb+VJlv8ilb64/GjBPRj+dACZb1/SlG7PPT6UYPqacPegAooooAY/UUmac4po9x+tAAfrQD707APelCigBpBJ4z+dJg+/50/AowKAEfqKbgmnscHrSbvcUAIetH+elLu56ijcfUUAJ/npRkf5FBGTSY/wA80Af/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0735MHHZ2R/sdf_-_compiify_improvements", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "m8zP7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I reviewed SDF doc and created tickets wherever missing and included them in the doc itself. We have completed 90% of the work.\n"}, {"type": "link", "url": "https://docs.google.com/document/d/1yYiSQ11gfk5sFnWzypMmyF0O6FrIrLiQsREY4bHMdn4/edit#heading=h.d9bd51bdpb2"}]}]}]}], "created_at": "2025-05-22T21:35:34.632954"}