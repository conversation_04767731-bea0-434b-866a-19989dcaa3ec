{"date": "2025-01-09", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1736440925.513749", "text": "Valgenesis comment upload: <https://compiify.atlassian.net/browse/COM-4050>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14133::99cb7489c38344d7b76b81a5ac288c96", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4050?atlOrigin=eyJpIjoiMzVmMWIxNDQ1YWIxNGNkNGE2NjI3YjM3YjdlYTNlNmYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4050 Upload comments for last raise date reason>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14133::2d4247d5017845c990e0ea95d376fda2", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14133\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4050\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4050", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "06L6x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis comment upload: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4050"}]}]}]}, {"ts": "1736423402.455019", "text": "<@U07EJ2LP44S> for this ticket\n<https://compiify.atlassian.net/browse/COM-4045>\nPlease share the list of recommenders, I will get them added from backend.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736423402.455019", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14128::d92e77553dac4482a047a702b6be8f60", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4045?atlOrigin=eyJpIjoiNDRlZDVmODE5OWQ0NDMzMDliMjhhOThmNzcwNzU2NDEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4045 Enhancement Request for CycleBuilder: Recommender Summary and Hiera…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14128::52c462de69d247e38b5a3f4024a33ff6", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14128\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4045\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4045", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "DXS84", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " for this ticket\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4045"}, {"type": "text", "text": "\nPlease share the list of recommenders, I will get them added from backend."}]}]}]}, {"ts": "1736397159.658939", "text": "<@U06HN8XDC5A> Can you please look into this? <@U07MH77PUBV> <PERSON><PERSON> can help here.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "4uiWB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " Can you please look into this? "}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " <PERSON><PERSON> can help here."}]}]}]}, {"ts": "1736365016.067139", "text": "I was looking at the column configurator, and the new bonus cycle fields (that Diversified is using) aren't present yet. Will they be added before release?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736365016.067139", "reply_count": 21, "blocks": [{"type": "rich_text", "block_id": "8TKmH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I was looking at the column configurator, and the new bonus cycle fields (that Diversified is using) aren't present yet. Will they be added before release?"}]}]}]}, {"ts": "1736364893.302109", "text": "Here's the document for the current cycles, including the outstanding tickets: <https://docs.google.com/document/d/1M512rn9hMCM5WiIIv_UJ8Ujn3J09EKViLVxG1yd4J4g/edit?usp=sharing>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736364893.302109", "reply_count": 3, "files": [{"id": "F087X1PD67N", "created": 1736364894, "timestamp": 1736364894, "name": "Q1 25 Cycles ", "title": "Q1 25 Cycles", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 211558, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1M512rn9hMCM5WiIIv_UJ8Ujn3J09EKViLVxG1yd4J4g", "external_url": "https://docs.google.com/document/d/1M512rn9hMCM5WiIIv_UJ8Ujn3J09EKViLVxG1yd4J4g/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1M512rn9hMCM5WiIIv_UJ8Ujn3J09EKViLVxG1yd4J4g/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F087X1PD67N-3e61651edb/q1_25_cycles__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F087X1PD67N-3e61651edb/q1_25_cycles__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F087X1PD67N-3e61651edb/q1_25_cycles__360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F087X1PD67N-3e61651edb/q1_25_cycles__480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F087X1PD67N-3e61651edb/q1_25_cycles__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F087X1PD67N-3e61651edb/q1_25_cycles__720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F087X1PD67N-3e61651edb/q1_25_cycles__800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F087X1PD67N-3e61651edb/q1_25_cycles__960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F087X1PD67N-3e61651edb/q1_25_cycles__1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTRYnNGT7UpGTRtoATJ9qdz6UAYooAKKKKAGknOBRk0pGTSYNAC5PpS5pMe1LgUAFFFFACHOeKXmo5mcY2Lu/HFR75d2PL47/NTsBYoquHmxnyv/HqRjK5GYQf+BUWHYs0UigKoA6ClpCP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F087X1PD67N/q1_25_cycles_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Hdr7I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here's the document for the current cycles, including the outstanding tickets: "}, {"type": "link", "url": "https://docs.google.com/document/d/1M512rn9hMCM5WiIIv_UJ8Ujn3J09EKViLVxG1yd4J4g/edit?usp=sharing"}]}]}]}], "created_at": "2025-05-22T21:35:34.695238"}