{"date": "2024-01-18", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1705601396.971189", "text": "<@U065H3M6WJV> Any updates from <PERSON> on the data upload", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1705601396.971189", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "CmZwt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Any updates from <PERSON> on the data upload"}]}]}]}, {"ts": "1705526203.186879", "text": "Just shared the Otter call with you both", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "H/325", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just shared the Otter call with you both"}]}]}]}, {"ts": "1705525966.538289", "text": "<@U04DS2MBWP4> How can I set up a shared Slack channel with external customers, do I just create a channel and add them, or is there any extra step?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705525966.538289", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "/mG3a", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " How can I set up a shared Slack channel with external customers, do I just create a channel and add them, or is there any extra step?"}]}]}]}, {"ts": "1705525941.497079", "text": "<@U04DKEFP1K8> Neuroflow found bugs! I'll file these, and might need to re-listen to the call to capture it all, but:\n• Bonus amounts aren't saving properly\n• Some employee rows aren't allowing promotion to be selected at all\n• Some employee salaries are wrong / don't match the \"1.11\" spreadsheet data\n• At least one employee (<PERSON>) is reporting to the wrong manager, but I think that might've been wrong in the spreadsheet", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705525941.497079", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "c3UOm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " <PERSON><PERSON><PERSON><PERSON> found bugs! I'll file these, and might need to re-listen to the call to capture it all, but:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bonus amounts aren't saving properly"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Some employee rows aren't allowing promotion to be selected at all"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Some employee salaries are wrong / don't match the \"1.11\" spreadsheet data"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "At least one employee (<PERSON>) is reporting to the wrong manager, but I think that might've been wrong in the spreadsheet"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1705524653.133879", "text": "<PERSON><PERSON><PERSON><PERSON> has questions - trying to set up a call with them today (they didn't share specifics yet)", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "QxM5x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON> has questions - trying to set up a call with them today (they didn't share specifics yet)"}]}]}]}], "created_at": "2025-05-22T21:35:34.590990"}