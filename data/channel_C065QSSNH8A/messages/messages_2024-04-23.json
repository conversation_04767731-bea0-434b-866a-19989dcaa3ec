{"date": "2024-04-23", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1713895994.454079", "text": "Quick update on <https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit|Merit 2.0 designs>: I dropped these in a doc just so it's easier review &amp; comment. Let me know if you have questions -- my hope was that we could get started on the new \"manager view\" sooner, while we iron out the remaining questions for the \"manager of managers\" view", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713895994.454079", "reply_count": 1, "files": [{"id": "F07033J43GE", "created": 1713895996, "timestamp": 1713895996, "name": "Merit 2.0 designs (in progress)", "title": "Merit 2.0 designs (in progress)", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10", "external_url": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit", "url_private": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSJ+v4Ckz9fyobGRnNIMepoAeDmimYH96l+X1oAdRQOlFACEA0mB6CnGkFACYHpR0FLRQAA5GaWgUUAITikyP8ilLAGkDA+tABuH+RS5+tJlfWgbQetADhRQDmigD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07033J43GE/merit_2", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "qEIqE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Quick update on "}, {"type": "link", "url": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit", "text": "Merit 2.0 designs"}, {"type": "text", "text": ": I dropped these in a doc just so it's easier review & comment. Let me know if you have questions -- my hope was that we could get started on the new \"manager view\" sooner, while we iron out the remaining questions for the \"manager of managers\" view"}]}]}]}, {"ts": "1713820062.053239", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> The load time for HR Admin and Merit &gt; organization subtabs seem to have increased on the test environment. Do we know what's affecting that performance?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713820062.053239", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "pHAGx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " The load time for HR Admin and Merit > organization subtabs seem to have increased on the test environment. Do we know what's affecting that performance?"}]}]}]}], "created_at": "2025-05-22T21:35:34.603316"}