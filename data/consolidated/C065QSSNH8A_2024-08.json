{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2024-08", "message_count": 209, "messages": [{"ts": "1722450733.952199", "text": "<@U04DKEFP1K8> great job in handling vercara questions!", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "CUDaA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " great job in handling vercara questions!"}]}]}]}, {"ts": "1722456066.677389", "text": "Where did we end up on a Paycom integration (listening to <PERSON><PERSON><PERSON><PERSON>'s call)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722456066.677389", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "6Snta", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Where did we end up on a Paycom integration (listening to <PERSON><PERSON><PERSON><PERSON>'s call)"}]}]}]}, {"ts": "1722456398.913079", "text": "<PERSON><PERSON><PERSON> is prioritizing integrations now and we should be able to sync data with Paycom.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722456398.913079", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "0NBWu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> is prioritizing integrations now and we should be able to sync data with Paycom."}]}]}]}, {"ts": "1722457580.222469", "text": "<@U04DKEFP1K8>  Rightway call is set up for 9 am on Tue next week. Can we pls move the eng meeting to 10 am and make it optional for <@U0690EB5JE5> ?", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1722457591.000000"}, "reactions": [{"name": "done", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "IYf9m", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "  Rightway call is set up for 9 am on Tue next week. Can we pls move the eng meeting to 10 am and make it optional for "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " ?"}]}]}]}, {"ts": "1722465434.747909", "text": "<!here> here are my 2 cents for call with <PERSON>. Please suggest if you want to add anything.\n\n1. Intros (2 mins)\n2. Product demo (15 mins) <@U07EJ2LP44S> are you comfortable with giving the product demo? If you are not up to speed on it, then either <@U04DKEFP1K8> or I can do that\n3. Product Roadmap themes  (integrations + benchmarking, manager enablement, customer feedback/bugs, Self-Service capabilities)(10 mins) -Kapil\n4. PRDs/Docs overview (People Insights, Total Rewards, Manager Communication for Manager Enablement theme (10 mins)\n5. Jira process for development (10 mins) (<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>h)\n6. Current challenges (10 mins) -Everyone\n7. Next steps (5 min)", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RrGW0", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " here are my 2 cents for call with <PERSON>. Please suggest if you want to add anything.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Intros (2 mins)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Product demo (15 mins) "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " are you comfortable with giving the product demo? If you are not up to speed on it, then either "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " or I can do that"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Product Roadmap themes  (integrations + benchmarking, manager enablement, customer feedback/bugs, Self-Service capabilities)(10 mins) -Kapil"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "PRDs/Docs overview (People Insights, Total Rewards, Manager Communication for Manager Enablement theme (10 mins)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Jira process for development (10 mins) (<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Current challenges (10 mins) -Everyone"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Next steps (5 min)"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1722465527.647649", "text": "I am not ready to demo yet; I'm going to do a test cycle hopefully by Friday and then I will be in better shape.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am not ready to demo yet; I'm going to do a test cycle hopefully by Friday and then I will be in better shape."}]}]}]}, {"ts": "**********.588909", "text": "<@U07EJ2LP44S> <@U04DS2MBWP4> Paycor integration requirement\n<https://help.merge.dev/en/articles/7183529-paycor-how-do-i-link-my-account>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "attachments": [{"image_url": "https://downloads.intercomcdn.com/i/o/379082/107841514fe57a3f0b2a855a/065e08fde79f8a1ee25eb6318655bdc9.jpg", "image_width": 1200, "image_height": 628, "image_bytes": 74161, "from_url": "https://help.merge.dev/en/articles/7183529-paycor-how-do-i-link-my-account", "id": 1, "original_url": "https://help.merge.dev/en/articles/7183529-paycor-how-do-i-link-my-account", "fallback": "Paycor - How do I link my account? | Merge Help Center", "text": "How to link your Paycor account", "title": "Paycor - How do I link my account? | Merge Help Center", "title_link": "https://help.merge.dev/en/articles/7183529-paycor-how-do-i-link-my-account", "service_name": "help.merge.dev"}], "blocks": [{"type": "rich_text", "block_id": "5OSRq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Paycor integration requirement\n"}, {"type": "link", "url": "https://help.merge.dev/en/articles/7183529-paycor-how-do-i-link-my-account"}]}]}]}, {"ts": "**********.250379", "text": "Which page is the 'view as' icon on? Where I can see what a specific person sees?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.250379", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "FuaUj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Which page is the 'view as' icon on? Where I can see what a specific person sees?"}]}]}]}, {"ts": "**********.977449", "text": "As I have UX feedback, what's the most useful way to document that? I have a video I captured and talked through. I can post that here, a different channel, or write it up and put it in on drive. Example attached", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.977449", "reply_count": 5, "files": [{"id": "F07FHK3GRBK", "created": **********, "timestamp": **********, "name": "Screen Recording 2024-08-01 at 2.46.49 PM.mov", "title": "Screen Recording 2024-08-01 at 2.46.49 PM.mov", "mimetype": "video/quicktime", "filetype": "mov", "pretty_type": "QuickTime Movie", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 246595469, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "transcription": {"status": "complete", "locale": "en-US", "preview": {"content": "Ok. So I was going through nato's cycle just to get up to speed and going through the merit configuration. And I'm in a reasonably sized, like kind", "has_more": true}}, "mp4": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FHK3GRBK-e626578be7/screen_recording_2024-08-01_at_2.46.49___pm.mp4", "url_private": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FHK3GRBK-e626578be7/screen_recording_2024-08-01_at_2.46.49___pm.mp4", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07FHK3GRBK/download/screen_recording_2024-08-01_at_2.46.49___pm.mov", "vtt": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FHK3GRBK-e626578be7/file.vtt?_xcb=64f96", "hls": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FHK3GRBK-e626578be7/file.m3u8?_xcb=64f96", "hls_embed": "data:application/vnd.apple.mpegurl;base64,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", "mp4_low": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FHK3GRBK-e626578be7/file_trans.mp4", "duration_ms": 100100, "media_display_type": "video", "thumb_video": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FHK3GRBK-e626578be7/screen_recording_2024-08-01_at_2.46.49___pm_thumb_video.jpeg", "thumb_video_w": 1920, "thumb_video_h": 1080, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07FHK3GRBK/screen_recording_2024-08-01_at_2.46.49___pm.mov", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07FHK3GRBK-38f4f5f66a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "5nyiH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "As I have UX feedback, what's the most useful way to document that? I have a video I captured and talked through. I can post that here, a different channel, or write it up and put it in on drive. Example attached"}]}]}]}, {"ts": "1722547151.422019", "text": "<@U07EJ2LP44S> link to jira product discovery <https://compiify.atlassian.net/jira/projects?selectedProjectType=product_discovery>", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "l3vxC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " link to jira product discovery "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/projects?selectedProjectType=product_discovery"}]}]}]}, {"ts": "1722551944.438199", "text": "<@U04DKEFP1K8> could you remind me again why we have flags on the current allocation page and on the eligibility criteria page within comp builder?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722551944.438199", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "iW/bz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " could you remind me again why we have flags on the current allocation page and on the eligibility criteria page within comp builder?"}]}]}]}, {"ts": "1722552411.085459", "text": "<@U04DKEFP1K8> did SDF and Nuato use the same currency conversion factor for both cycle configuration and org view? or do we have to have separate  currency conversion factor for cycle configuration and org view", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722552411.085459", "reply_count": 14, "blocks": [{"type": "rich_text", "block_id": "vQgvS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " did SDF and Nuato use the same currency conversion factor for both cycle configuration and org view? or do we have to have separate  currency conversion factor for cycle configuration and org view"}]}]}]}, {"ts": "1722554574.380809", "text": "<@U04DKEFP1K8> currently eligibility cut off date in the proration page is not used in the proration calculations..correct? Any downside to removing that field?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722554574.380809", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "PuvhF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " currently eligibility cut off date in the proration page is not used in the proration calculations..correct? Any downside to removing that field?"}]}]}]}, {"ts": "1722554725.943209", "text": "It is duplicated currently and can be removed", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ge15d", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It is duplicated currently and can be removed"}]}]}]}, {"ts": "1722554839.160279", "text": "<@U0690EB5JE5> can we add functionality to track and show promotions budget separately in merit planning even if the promotions budget is combined with salary? Same logic for salary, market adjustments and promotions even if customers has combined each of these components. I think we have a ticket for this in Jira already.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722554839.160279", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "CMcyD", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we add functionality to track and show promotions budget separately in merit planning even if the promotions budget is combined with salary? Same logic for salary, market adjustments and promotions even if customers has combined each of these components. I think we have a ticket for this in Jira already."}]}]}]}, {"ts": "1722556942.583589", "text": "<@U04DS2MBWP4> not sure if you have seen this but since you are redesigning comp builder , it might worth a watch <https://help.lattice.com/hc/en-us/articles/11659407874327-Create-a-Test-Compensation-Cycle> <PERSON><PERSON><PERSON>'s latest update to comp module", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722556942.583589", "reply_count": 1, "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "bmiYU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " not sure if you have seen this but since you are redesigning comp builder , it might worth a watch "}, {"type": "link", "url": "https://help.lattice.com/hc/en-us/articles/11659407874327-Create-a-Test-Compensation-Cycle"}, {"type": "text", "text": " <PERSON><PERSON><PERSON>'s latest update to comp module"}]}]}]}, {"ts": "1722573514.886629", "text": "<@U04DS2MBWP4> in the new comp cycle builder figma design. Please do add this feature as well <https://compiify.atlassian.net/browse/COM-2565>", "user": "U0690EB5JE5", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12626::14a935b0508911efb1683337b6fcd8e2", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2565?atlOrigin=eyJpIjoiNmQ1NjllNjBkYWE3NGYwN2IzMmZjZjdiNThjMDNhNjYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2565 Additional planning levels>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12626::14a935b2508911efb1683337b6fcd8e2", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/082e10750208b91e38660f50da682c58?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12626::14a935b1508911efb1683337b6fcd8e2", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12626\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12626\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2565", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "I35at", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " in the new comp cycle builder figma design. Please do add this feature as well "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2565"}]}]}]}, {"ts": "1722573577.356319", "text": "Also each item in this nav bar will be clickable? meaning we can go to any section after its completed?", "user": "U0690EB5JE5", "type": "message", "files": [{"id": "F07F43SE9L5", "created": 1722573551, "timestamp": 1722573551, "name": "Screenshot 2024-08-02 at 10.09.05 AM.png", "title": "Screenshot 2024-08-02 at 10.09.05 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 27726, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07F43SE9L5/screenshot_2024-08-02_at_10.09.05___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07F43SE9L5/download/screenshot_2024-08-02_at_10.09.05___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F43SE9L5-01f6a51662/screenshot_2024-08-02_at_10.09.05___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F43SE9L5-01f6a51662/screenshot_2024-08-02_at_10.09.05___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F43SE9L5-01f6a51662/screenshot_2024-08-02_at_10.09.05___am_360.png", "thumb_360_w": 237, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F43SE9L5-01f6a51662/screenshot_2024-08-02_at_10.09.05___am_480.png", "thumb_480_w": 316, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F43SE9L5-01f6a51662/screenshot_2024-08-02_at_10.09.05___am_160.png", "original_w": 398, "original_h": 604, "thumb_tiny": "AwAwAB/RXt06UvPtSDt06UdOm2mA4e+Ka/3aXn2pH+7SAUDgcdqTaP7q0q/dFG0UACgDsB9KR/u0u0Uj/doAVfuilpF+6KWgApr/AHadTX+7QB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07F43SE9L5/screenshot_2024-08-02_at_10.09.05___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07F43SE9L5-2aeaefafd1", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "bqpvn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also each item in this nav bar will be clickable? meaning we can go to any section after its completed?"}]}]}]}, {"ts": "1722575545.174949", "text": "Yes thats how it is", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Gd9Yc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes thats how it is"}]}]}]}, {"ts": "1722579288.747429", "text": "Did we have requirement of supporting hourly/part time employees from any customers?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mJAMs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Did we have requirement of supporting hourly/part time employees from any customers?"}]}]}]}, {"ts": "1722608414.749099", "text": "Are there any practifi calls recorded? <@U04DS2MBWP4><@U04DKEFP1K8>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722608414.749099", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "KBf6h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Are there any practifi calls recorded? "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1722612157.697279", "text": "No meeting today?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HXcz4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No meeting today?"}]}]}]}, {"ts": "1722612168.762789", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FUpwS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1722612241.146819", "text": "<@U07EJ2LP44S> and I are meeting 9am PT today, let me add both of you to that invite and we can discuss any pressing item before <PERSON> and i discuss data items", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4UPO0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " and I are meeting 9am PT today, let me add both of you to that invite and we can discuss any pressing item before <PERSON> and i discuss data items"}]}]}]}, {"ts": "1722612701.634649", "text": "I have a little more info about the Paycom integration question I can share", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "P5OIC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have a little more info about the Paycom integration question I can share"}]}]}]}, {"ts": "1722617061.292289", "text": "<@U04DS2MBWP4> <@U07EJ2LP44S> I just checked again and found that We need to request Merge team to enable Paycom for us as its in beta phase.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1722617126.000000"}, "files": [{"id": "F07FA9AVC2W", "created": 1722617033, "timestamp": 1722617033, "name": "Screenshot 2024-08-02 at 10.13.41 PM.png", "title": "Screenshot 2024-08-02 at 10.13.41 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 35645, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07FA9AVC2W/screenshot_2024-08-02_at_10.13.41___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07FA9AVC2W/download/screenshot_2024-08-02_at_10.13.41___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 77, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 103, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 154, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 172, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 206, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 220, "original_w": 1263, "original_h": 271, "thumb_tiny": "AwAKADDTppOD3p1N/ioAN3saXd7GlooAbu9jTqKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07FA9AVC2W/screenshot_2024-08-02_at_10.13.41___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07FA9AVC2W-e6f6111b2d", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "RZk9w", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I just checked again and found that We need to request Merge team to enable Paycom for us as its in beta phase."}]}]}]}, {"ts": "1722617076.527629", "text": "When is the earlier we need to have it enabled?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Y/SAi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "When is the earlier we need to have it enabled?"}]}]}]}, {"ts": "1722618279.678149", "text": "They are just waiting on us to get the data, so ideally we'd get it ASAP.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722618279.678149", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "5p+cg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They are just waiting on us to get the data, so ideally we'd get it ASAP."}]}]}]}, {"ts": "1722625200.407449", "text": "<!here> here is the feedback that <PERSON> (VP of operations at <http://Tithly.io|Tithly.io>) gave during the demo today\n<https://app.fireflies.ai/soundbites/5N28VaD0c5C>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722625200.407449", "reply_count": 13, "attachments": [{"from_url": "https://app.fireflies.ai/soundbites/5N28VaD0c5C", "image_url": "https://files.fireflies.ai/app/img/preview.png", "image_width": 1280, "image_height": 720, "image_bytes": 188965, "service_icon": "https://app.fireflies.ai/favicon.ico", "id": 1, "original_url": "https://app.fireflies.ai/soundbites/5N28VaD0c5C", "fallback": "Fireflies.ai - Free Meeting Recorder", "text": "Record, transcribe, search and collaborate across your meetings. Fireflies takes notes for your meetings and turn words into actions.", "title": "Fireflies.ai - Free Meeting Recorder", "title_link": "https://app.fireflies.ai/soundbites/5N28VaD0c5C", "service_name": "app.fireflies.ai"}], "blocks": [{"type": "rich_text", "block_id": "LUp0r", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " here is the feedback that <PERSON> (VP of operations at "}, {"type": "link", "url": "http://Tithly.io", "text": "Tithly.io"}, {"type": "text", "text": ") gave during the demo today\n"}, {"type": "link", "url": "https://app.fireflies.ai/soundbites/5N28VaD0c5C"}]}]}]}, {"ts": "1722625811.060929", "text": "<@U04DS2MBWP4> <@U07EJ2LP44S> Following updates can be sent to Vercara, their environment is now upgraded with latest changes\n\n        1.\tAdministrators can now change an employee’s compensation type to OTE directly through the UI. Just select the employee name, click Edit, and change the Compensation Type to OTE.\n\t2.\tFor OTE employees, you can now adjust both Base Pay and Variable Pay via the UI. Additionally, you can modify the Pay mix percentages directly from the UI.\n\t3.\tYou can now input the Target Bonus % through the UI as well.\n\t4.\tThe CSV templates now include an employee name column, which is read-only.\n\t5.\tColumns in the Compensation Data template are now optional. You don’t need to fill in columns with dummy data if you don’t have any input for them.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722625811.060929", "reply_count": 3, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "wOQP8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Following updates can be sent to Vercara, their environment is now upgraded with latest changes\n\n        1.\tAdministrators can now change an employee’s compensation type to OTE directly through the UI. Just select the employee name, click Edit, and change the Compensation Type to OTE.\n\t2.\tFor OTE employees, you can now adjust both Base Pay and Variable Pay via the UI. Additionally, you can modify the Pay mix percentages directly from the UI.\n\t3.\tYou can now input the Target Bonus % through the UI as well.\n\t4.\tThe CSV templates now include an employee name column, which is read-only.\n\t5.\tColumns in the Compensation Data template are now optional. You don’t need to fill in columns with dummy data if you don’t have any input for them."}]}]}]}, {"ts": "1722634829.913499", "text": "<!here> FYI, I will OOO on Monday", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8", "U0690EB5JE5"], "count": 2}, {"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "QYuJ3", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " FYI, I will OOO on Monday"}]}]}]}, {"ts": "1722638955.314539", "text": "I hope it’s for a fun reason! ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722638955.314539", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "ikxnz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I hope it’s for a fun reason! "}]}]}]}, {"ts": "1722783574.956329", "text": "<@U0690EB5JE5> It validates the current approach ", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722783574.956329", "reply_count": 2, "files": [{"id": "F07F82MJ3N2", "created": 1722783530, "timestamp": 1722783530, "name": "IMG_0790.png", "title": "IMG_0790", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 2213605, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07F82MJ3N2/img_0790.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07F82MJ3N2/download/img_0790.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F82MJ3N2-765d32951a/img_0790_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F82MJ3N2-765d32951a/img_0790_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F82MJ3N2-765d32951a/img_0790_360.png", "thumb_360_w": 166, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F82MJ3N2-765d32951a/img_0790_480.png", "thumb_480_w": 221, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F82MJ3N2-765d32951a/img_0790_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F82MJ3N2-765d32951a/img_0790_720.png", "thumb_720_w": 332, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F82MJ3N2-765d32951a/img_0790_800.png", "thumb_800_w": 800, "thumb_800_h": 1734, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F82MJ3N2-765d32951a/img_0790_960.png", "thumb_960_w": 443, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F82MJ3N2-765d32951a/img_0790_1024.png", "thumb_1024_w": 472, "thumb_1024_h": 1024, "original_w": 1290, "original_h": 2796, "thumb_tiny": "AwAwABa+JATkA8celPznsabznv8AnRz/AJNADgMEkd/el5pF/wA80tAEHm+1Hme1ZtxNIs7qrEAEY59qi+0XHaQ0xGwJcdqXzv8AZ/Ws6ymkklZZG3YXNXeKAKF1CPMLbj83YVFHb+Y21WI+tahtIiOU4HT5jSrBCrZQDOMD5qQypb2phcsWzkY61Yx71N5K/wB0fnS+SvpQB//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07F82MJ3N2/img_0790.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07F82MJ3N2-17cfd123b1", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "K7DLD", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " It validates the current approach "}]}]}]}, {"ts": "1722856821.349229", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> I reviewed SDF doc and created tickets wherever missing and included them in the doc itself. We have completed 90% of the work.\n<https://docs.google.com/document/d/1yYiSQ11gfk5sFnWzypMmyF0O6FrIrLiQsREY4bHMdn4/edit#heading=h.d9bd51bdpb2>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1722857223.000000"}, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F0735MHHZ2R", "created": 1715633202, "timestamp": 1715633202, "name": "SDF - Compiify improvements", "title": "[Internal doc] SDF - Compiify improvements", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1yYiSQ11gfk5sFnWzypMmyF0O6FrIrLiQsREY4bHMdn4", "external_url": "https://docs.google.com/document/d/1yYiSQ11gfk5sFnWzypMmyF0O6FrIrLiQsREY4bHMdn4/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1yYiSQ11gfk5sFnWzypMmyF0O6FrIrLiQsREY4bHMdn4/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0735MHHZ2R-2fbb450ab6/sdf_-_compiify_improvements_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTRbPb+VJlv8ilb64/GjBPRj+dACZb1/SlG7PPT6UYPqacPegAooooAY/UUmac4po9x+tAAfrQD707APelCigBpBJ4z+dJg+/50/AowKAEfqKbgmnscHrSbvcUAIetH+elLu56ijcfUUAJ/npRkf5FBGTSY/wA80Af/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0735MHHZ2R/sdf_-_compiify_improvements", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "m8zP7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I reviewed SDF doc and created tickets wherever missing and included them in the doc itself. We have completed 90% of the work.\n"}, {"type": "link", "url": "https://docs.google.com/document/d/1yYiSQ11gfk5sFnWzypMmyF0O6FrIrLiQsREY4bHMdn4/edit#heading=h.d9bd51bdpb2"}]}]}]}, {"ts": "1722856847.816419", "text": "Please review and comment on the doc and we can discuss one of eng discussion meetings.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1722856847.816419", "reply_count": 2, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "9sDjW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please review and comment on the doc and we can discuss one of eng discussion meetings."}]}]}]}, {"ts": "1722872827.014349", "text": "I am making my way through the data templates; do we have a list of required fields vs optional ones? Is there any additional definition of fields outside of what is on the template? For example, there is job category and job family on the Employee Data template; how do they differ?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722872827.014349", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "mvOHX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am making my way through the data templates; do we have a list of required fields vs optional ones? Is there any additional definition of fields outside of what is on the template? For example, there is job category and job family on the Employee Data template; how do they differ?"}]}]}]}, {"ts": "1722950267.199889", "text": "<!here> I have created an epic for Integrations work. Early next week, We will start work on making implementation completely self serve via integration.\nI will keep filling the details in each ticket this week.\n<https://compiify.atlassian.net/browse/COM-3482>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1722950288.000000"}, "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}, {"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13543::46824f2053f611efa48115f6026822d7", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3482?atlOrigin=eyJpIjoiZTUxMjZiOTcxYzk5NGMyYmI2YmY1ZmM4ZWE3MTU3ZDAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3482 Integrations Enhancements>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13543::46824f2253f611efa48115f6026822d7", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13543::46824f2153f611efa48115f6026822d7", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13543\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13543\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3482", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Li5ow", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I have created an epic for Integrations work. Early next week, We will start work on making implementation completely self serve via integration.\nI will keep filling the details in each ticket this week.\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3482"}]}]}]}, {"ts": "1722954531.777709", "text": "<@U04DKEFP1K8> the meeting with Rightway is pushed to next week  so now we can move the eng meeting to regular 9 AM PST today? \n\n<@U0690EB5JE5> does that work for you or we can keep it at 10 AM PST", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722954531.777709", "reply_count": 1, "reactions": [{"name": "done", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "R8flc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " the meeting with Rightway is pushed to next week  so now we can move the eng meeting to regular 9 AM PST today? \n\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " does that work for you or we can keep it at 10 AM PST"}]}]}]}, {"ts": "1722955137.292129", "text": "I just created a Practifi channel, but did I do it incorrectly? I created it with an external user but it's not showing under the external connections area (even though I created the channel through that section).", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722955137.292129", "reply_count": 2, "edited": {"user": "U07EJ2LP44S", "ts": "1722955272.000000"}, "blocks": [{"type": "rich_text", "block_id": "vCJX3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I just created a Practifi channel, but did I do it incorrectly? I created it with an external user but it's not showing under the external connections area (even though I created the channel through that section)."}]}]}]}, {"ts": "1722959983.395709", "text": "<@U04DKEFP1K8> Fixes in BE-dev and FE-dev (<http://test.stridehr.io|test.stridehr.io>) pushed today.\n• All the feedback/bugs on Vercara edit\n• minor fix on practifi merit page load issue. This is still an issue due to the way cycle is configured and also a gap. We can discuss on this.\n• fix for bug <https://compiify.atlassian.net/browse/COM-3378>\n• Add new job title,  dev complete and testing in progress should be ready tomorrow", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1722959983.395709", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1722960051.000000"}, "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "3rQqL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Fixes in BE-dev and FE-dev ("}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": ") pushed today.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All the feedback/bugs on Vercara edit"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "minor fix on practifi merit page load issue. This is still an issue due to the way cycle is configured and also a gap. We can discuss on this."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "fix for bug "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3378"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Add new job title,  dev complete and testing in progress should be ready tomorrow"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1722960021.685999", "text": "Also `sdf-test`, `cwa-test` and `dev-energy` ENV are stopped to save some cost.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "yCp58", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also "}, {"type": "text", "text": "sdf-test", "style": {"code": true}}, {"type": "text", "text": ", "}, {"type": "text", "text": "cwa-test", "style": {"code": true}}, {"type": "text", "text": " and "}, {"type": "text", "text": "dev-energy", "style": {"code": true}}, {"type": "text", "text": " ENV are stopped to save some cost."}]}]}]}, {"ts": "1722963797.432249", "text": "<@U07EJ2LP44S> <PERSON>'s local auth details are present here in case she cannot use google auth <https://docs.google.com/spreadsheets/d/1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q/edit?gid=0#gid=0>", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ylGQk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " <PERSON>'s local auth details are present here in case she cannot use google auth "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q/edit?gid=0#gid=0"}]}]}]}, {"ts": "1722971044.705809", "text": "Do we need to have our implementation meeting this afternoon? I'm running down a bit on energy", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722971044.705809", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "fDu8u", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we need to have our implementation meeting this afternoon? I'm running down a bit on energy"}]}]}]}, {"ts": "1722971224.258189", "text": "yep, we can cancel it", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vE90M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yep, we can cancel it"}]}]}]}, {"ts": "1722971228.934379", "text": "feel better soon", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "y6Bmy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "feel better soon"}]}]}]}, {"ts": "1722981864.687969", "text": "<!here> Confirming <PERSON> and I were on the call with vercara earlier in the day and we have resolved their issue. Their were couple minor issue that were observed , i will discuss the same with <PERSON><PERSON><PERSON> later tonight.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722981864.687969", "reply_count": 4, "edited": {"user": "U04DKEFP1K8", "ts": "1722981880.000000"}, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8qXyP", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Confirming <PERSON> and I were on the call with vercara earlier in the day and we have resolved their issue. Their were couple minor issue that were observed , i will discuss the same with <PERSON><PERSON><PERSON> later tonight."}]}]}]}, {"ts": "1723019940.385069", "text": "<@U04DS2MBWP4> whats the plan on cycle builder v2 and merit planning new design? There are some small enhancements we need to pick up on cycle builder (from SDF list). Based on when we would have the new designs ready we can prioritize them whether to address them now or along with new designs to avoid rework.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723019940.385069", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "4vA/R", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " whats the plan on cycle builder v2 and merit planning new design? There are some small enhancements we need to pick up on cycle builder (from SDF list). Based on when we would have the new designs ready we can prioritize them whether to address them now or along with new designs to avoid rework."}]}]}]}, {"ts": "1723037871.800629", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> For Degenkolb's multilple line items for one employee, do we need to edit the employee id to be xxxxxx-1 and xxxxxx-2 or similar so they will upload as two unique lines?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723037871.800629", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Vw2jl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For Degenkolb's multilple line items for one employee, do we need to edit the employee id to be xxxxxx-1 and xxxxxx-2 or similar so they will upload as two unique lines?"}]}]}]}, {"ts": "1723041437.710559", "text": "I had come to meet a friend from UK and getting late. I might be late to eng discussion or miss it.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eu5fI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I had come to meet a friend from UK and getting late. I might be late to eng discussion or miss it."}]}]}]}, {"ts": "1723041452.028829", "text": "Will share eng updates. Sorry about the short notice.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1723044336.000000"}, "blocks": [{"type": "rich_text", "block_id": "lSsCZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will share eng updates. Sorry about the short notice"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1723045484.161579", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> Do you have any specific agenda items to discuss during the eng call? If not we can cancel it today and I can focus on reviewing resumes of 120 AE candidates who have applied so far.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "SWpzJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Do you have any specific agenda items to discuss during the eng call? If not we can cancel it today and I can focus on reviewing resumes of 120 AE candidates who have applied so far."}]}]}]}, {"ts": "1723045889.609799", "text": "Nothing on my end!", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yVwDH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Nothing on my end!"}]}]}]}, {"ts": "1723046421.520119", "text": "Sure\nHere are top things on my mind at this point\n1. We now have 3 customer implementation for Nov'24 ( Practifi, Alayacare and Degenkolb). We need to finalize customer specific feature development asap, it feels like we need a bit of push here to get things in motion.\n    a. Alayacare needs a major feature ( support budgeting in CAD)\n2. Let's keep sharing daily update on Diversified Energy", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "crZO2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure\nHere are top things on my mind at this point\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We now have 3 customer implementation for Nov'24 ( Practifi, Alayacare and Degenkolb). We need to finalize customer specific feature development asap, it feels like we need a bit of push here to get things in motion."}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Alayacare needs a major feature ( support budgeting in CAD)"}]}], "style": "ordered", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let's keep sharing daily update on Diversified Energy"}]}], "style": "ordered", "indent": 0, "offset": 1, "border": 0}]}]}, {"ts": "1723046798.079189", "text": "<@U04DKEFP1K8> Were templates shared with Diversified?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723046798.079189", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "tA/Ld", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Were templates shared with Diversified?"}]}]}]}, {"ts": "1723068822.839639", "text": "hey <@U07EJ2LP44S> could we  pls set up an implementation call with Sonendo? I am not sure when is their cycle", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1723068822.839639", "reply_count": 3, "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "qjwmi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "hey "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " could we  pls set up an implementation call with Sonendo? I am not sure when is their cycle"}]}]}]}, {"ts": "1723068932.871229", "text": "Just a heads up for everyone, the hurricane is running through here overnight and through tomorrow. It will probably be OK, but I might lose power as a result. I should still have cell service though", "user": "U07EJ2LP44S", "type": "message", "edited": {"user": "U07EJ2LP44S", "ts": "1723070091.000000"}, "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "+<PERSON>zo<PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just a heads up for everyone, the hurricane is running through here overnight and through tomorrow. It will probably be OK, but I might lose power as a result. I should still have cell service though"}]}]}]}, {"ts": "1723094507.051369", "text": "Agenda for next eng discussion\n• Review customer specific requirements and priority\n• Review OTE merit planning functionality", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "+H8PP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for next eng discussion\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Review customer specific requirements and priority"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Review OTE merit planning functionality"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1723134772.905769", "text": "<!here> need a quick 5 min break", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+5W2/", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " need a quick 5 min break"}]}]}]}, {"ts": "1723138376.777669", "text": "sorry all my smoke detectors just went off", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723138376.777669", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "tBu9H", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "sorry all my smoke detectors just went off"}]}]}]}, {"ts": "1723138382.427029", "text": "trying to figure it out", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723138382.427029", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "qfOwe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "trying to figure it out"}]}]}]}, {"ts": "1723140909.863879", "text": "<!here> confirming the Avg Salary Increase Percent is correct (6.13%). See highlighted cell yellow in attached excel for calculations", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1723140909.863879", "reply_count": 1, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "partyparrot", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F07GTTR5QKS", "created": 1723140842, "timestamp": 1723140842, "name": "NDP July 2024 Cycle - 2024-08-08.csv", "title": "NDP July 2024 Cycle - 2024-08-08.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": true, "size": 2853, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07GTTR5QKS/ndp_july_2024_cycle_-_2024-08-08.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07GTTR5QKS/download/ndp_july_2024_cycle_-_2024-08-08.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07GTTR5QKS/ndp_july_2024_cycle_-_2024-08-08.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07GTTR5QKS-e537f7d482", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F07GTTR5QKS/ndp_july_2024_cycle_-_2024-08-08.csv/edit", "preview": "Employee,Current Job Title,Performance Rating,Local Currency,Current Salary / OTE (Local),Market Adjustment (Local),Proposed Salary Increase / OTE Increase (Local),Sum of Merit Increase,,Proposed Salary Increase / OTE Increase Percent,Proration,Prorated Salary Increase / OTE (Local),New Salary / OTE (Local),Current Compa Ratio,One Time Bonus (Local),Total Target Cash Old (Local),Total Target Cash New (Local),Total Target Cash Percent,Comments,Team,Manager Name,Country,Region,Hire Date,Tenure,Current Pay Mix,Current Base Pay,Current Variable Pay,New Pay Mix,New Base Pay,New Variable Pay\r\"Youree, Alivia\",Dental Practice Valuation Coordinator,3,USD,\"85,699.00\",0,\"1,713.98\",\"1,713.98\",2.00,2.00%,1,\"1,713.98\",\"87,412.98\",N/A,0,\"85,699.00\",\"87,412.98\",2.00%,,NA,\"Whitehurst, Dawn\",US,Frisco,6/20/22,2 y 1 m 3 d,,,,,,\r\"<PERSON>, <PERSON>\",Senior Transition Consultant,3.16667,USD,\"120,000.00\",0,\"2,400.00\",\"2,400.00\",2.00,2.00%,1,\"2,400.00\",\"122,400.00\",N/A,0,\"120,000.00\",\"122,400.00\",2.00%,,NA,\"Schwebke, Bridget\",US,Frisco...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Employee</div><div class=\"cm-col\">Current Job Title</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Local Currency</div><div class=\"cm-col\">Current Salary / OTE (Local)</div><div class=\"cm-col\">Market Adjustment (Local)</div><div class=\"cm-col\">Proposed Salary Increase / OTE Increase (Local)</div><div class=\"cm-col\">Sum of Merit Increase</div><div class=\"cm-col\"></div><div class=\"cm-col\">Proposed Salary Increase / OTE Increase Percent</div><div class=\"cm-col\">Proration</div><div class=\"cm-col\">Prorated Salary Increase / OTE (Local)</div><div class=\"cm-col\">New Salary / OTE (Local)</div><div class=\"cm-col\">Current Compa Ratio</div><div class=\"cm-col\">One Time Bonus (Local)</div><div class=\"cm-col\">Total Target Cash Old (Local)</div><div class=\"cm-col\">Total Target Cash New (Local)</div><div class=\"cm-col\">Total Target Cash Percent</div><div class=\"cm-col\">Comments</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Manager Name</div><div class=\"cm-col\">Country</div><div class=\"cm-col\">Region</div><div class=\"cm-col\">Hire Date</div><div class=\"cm-col\">Tenure</div><div class=\"cm-col\">Current Pay Mix</div><div class=\"cm-col\">Current Base Pay</div><div class=\"cm-col\">Current Variable Pay</div><div class=\"cm-col\">New Pay Mix</div><div class=\"cm-col\">New Base Pay</div><div class=\"cm-col\">New Variable Pay</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Youree, Alivia</div><div class=\"cm-col\">Dental Practice Valuation Coordinator</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">85,699.00</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">1,713.98</div><div class=\"cm-col\">1,713.98</div><div class=\"cm-col cm-num\">2.00</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">1,713.98</div><div class=\"cm-col\">87,412.98</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">85,699.00</div><div class=\"cm-col\">87,412.98</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col\"></div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Whitehurst, Dawn</div><div class=\"cm-col\">US</div><div class=\"cm-col\">Frisco</div><div class=\"cm-col\">6/20/22</div><div class=\"cm-col\">2 y 1 m 3 d</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Ramsey, Morgan</div><div class=\"cm-col\">Senior Transition Consultant</div><div class=\"cm-col cm-num\">3.16667</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">120,000.00</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">2,400.00</div><div class=\"cm-col\">2,400.00</div><div class=\"cm-col cm-num\">2.00</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">2,400.00</div><div class=\"cm-col\">122,400.00</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">120,000.00</div><div class=\"cm-col\">122,400.00</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col\"></div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Schwebke, Bridget</div><div class=\"cm-col\">US</div><div class=\"cm-col\">Frisco</div><div class=\"cm-col\">11/2/20</div><div class=\"cm-col\">3 y 8 m 21 d</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Schwebke, Bridget</div><div class=\"cm-col\">Head of Consulting</div><div class=\"cm-col cm-num\">3.16667</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">140,000.00</div><div class=\"cm-col\">12,600.00</div><div class=\"cm-col\">2,800.00</div><div class=\"cm-col\">15,400.00</div><div class=\"cm-col cm-num\">11.00</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">2,800.00</div><div class=\"cm-col\">155,400.00</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">140,000.00</div><div class=\"cm-col\">155,400.00</div><div class=\"cm-col\">11.00%</div><div class=\"cm-col\"></div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Ratcliff, Christy</div><div class=\"cm-col\">US</div><div class=\"cm-col\">Frisco</div><div class=\"cm-col\">7/8/19</div><div class=\"cm-col\">5 y 0 m 15 d</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Sandy, August</div><div class=\"cm-col\">Transition Consultant</div><div class=\"cm-col cm-num\">2.5</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">70,000.00</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">1,400.00</div><div class=\"cm-col\">1,400.00</div><div class=\"cm-col cm-num\">2.00</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">1,400.00</div><div class=\"cm-col\">71,400.00</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">70,000.00</div><div class=\"cm-col\">71,400.00</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col\"></div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Schwebke, Bridget</div><div class=\"cm-col\">US</div><div class=\"cm-col\">Frisco</div><div class=\"cm-col\">6/6/22</div><div class=\"cm-col\">2 y 1 m 17 d</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Pecora, Brandy</div><div class=\"cm-col\">Transition Coordinator</div><div class=\"cm-col cm-num\">3.16667</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">81,123.00</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">1,622.46</div><div class=\"cm-col\">1,622.46</div><div class=\"cm-col cm-num\">2.00</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">1,622.46</div><div class=\"cm-col\">82,745.46</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">81,123.00</div><div class=\"cm-col\">82,745.46</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col\"></div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Schwebke, Bridget</div><div class=\"cm-col\">US</div><div class=\"cm-col\">Frisco</div><div class=\"cm-col\">6/6/22</div><div class=\"cm-col\">2 y 1 m 17 d</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">MacLachlan, Catriona</div><div class=\"cm-col\">Senior Listing Specialist</div><div class=\"cm-col cm-num\">2.66667</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">72,708.00</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">1,454.16</div><div class=\"cm-col\">1,454.16</div><div class=\"cm-col cm-num\">2.00</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">1,454.16</div><div class=\"cm-col\">74,162.16</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">72,708.00</div><div class=\"cm-col\">74,162.16</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col\"></div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Ratcliff, Christy</div><div class=\"cm-col\">US</div><div class=\"cm-col\">Frisco</div><div class=\"cm-col\">6/30/14</div><div class=\"cm-col\">10 y 0 m 23 d</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Martinez, Grace</div><div class=\"cm-col\">Financial Analyst</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">72,657.00</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">1,453.14</div><div class=\"cm-col\">1,453.14</div><div class=\"cm-col cm-num\">2.00</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">1,453.14</div><div class=\"cm-col\">74,110.14</div><div class=\"cm-col cm-num\">0.87</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">72,657.00</div><div class=\"cm-col\">74,110.14</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col\"></div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Whitehurst, Dawn</div><div class=\"cm-col\">US</div><div class=\"cm-col\">Frisco</div><div class=\"cm-col\">4/11/22</div><div class=\"cm-col\">2 y 3 m 12 d</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Doyle, Matthew</div><div class=\"cm-col\">Senior Transition Consultant</div><div class=\"cm-col cm-num\">2.16667</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">104,000.00</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">2,080.00</div><div class=\"cm-col\">2,080.00</div><div class=\"cm-col cm-num\">2.00</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">2,080.00</div><div class=\"cm-col\">106,080.00</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">104,000.00</div><div class=\"cm-col\">106,080.00</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col\"></div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Schwebke, Bridget</div><div class=\"cm-col\">US</div><div class=\"cm-col\">Frisco</div><div class=\"cm-col\">6/28/21</div><div class=\"cm-col\">3 y 0 m 25 d</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Whitehurst, Dawn</div><div class=\"cm-col\">Head of Valuation</div><div class=\"cm-col cm-num\">3.66667</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">115,000.00</div><div class=\"cm-col\">23,000.00</div><div class=\"cm-col\">2,300.00</div><div class=\"cm-col\">25,300.00</div><div class=\"cm-col cm-num\">22.00</div><div class=\"cm-col\">2.00%</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">2,300.00</div><div class=\"cm-col\">140,300.00</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">115,000.00</div><div class=\"cm-col\">140,300.00</div><div class=\"cm-col\">22.00%</div><div class=\"cm-col\"></div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Ratcliff, Christy</div><div class=\"cm-col\">US</div><div class=\"cm-col\">Frisco</div><div class=\"cm-col\">6/26/17</div><div class=\"cm-col\">7 y 0 m 27 d</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 1, "lines_more": 0, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "/VlnU", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " confirming the Avg Salary Increase Percent is correct (6.13%). See highlighted cell yellow in attached excel for calculations"}]}]}]}, {"ts": "**********.372309", "text": "Job title does not appear to be a field in the CompensationBandsTemplate download from an account", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.372309", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "f5npJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Job title does not appear to be a field in the CompensationBandsTemplate download from an account"}]}]}]}, {"ts": "**********.009279", "text": "\"Job Title\"", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OU+Jh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "\"Job Title\""}]}]}]}, {"ts": "**********.129069", "text": "<@U04DS2MBWP4> <PERSON> and I are on a call to sort out practifi issue", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4EN6s", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " <PERSON> and I are on a call to sort out practifi issue"}]}]}]}, {"ts": "**********.124339", "text": "øk", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0H+eo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "øk"}]}]}]}, {"ts": "**********.500709", "text": "We are ready (or at least I am!0", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XyEHk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We are ready (or at least I am!0"}]}]}]}, {"ts": "1723151479.943489", "text": "we just finished the call and i am running behind on updating na<PERSON>'s env so will use the time for it", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/qCJz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we just finished the call and i am running behind on updating na<PERSON>'s env so will use the time for it"}]}]}]}, {"ts": "1723151493.952239", "text": "do we need to connect Kapil?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sOy+1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "do we need to connect Kapil?"}]}]}]}, {"ts": "1723151504.292899", "text": "We have quite a list of to dos :slightly_smiling_face:", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oNM1+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We have quite a list of to dos "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1723151590.034019", "text": "The only question I have is <PERSON>/<PERSON>vers<PERSON> - she said she was expecting templates but then she said she sent everything over. From what I can tell her files don't contain all the info but I can look again and see if I'm wrong", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZxYvk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The only question I have is <PERSON>/<PERSON>vers<PERSON> - she said she was expecting templates but then she said she sent everything over. From what I can tell her files don't contain all the info but I can look again and see if I'm wrong"}]}]}]}, {"ts": "1723151686.379129", "text": "I think she's a payroll only person so I'm not really sure how to deal with her and she seems a bit snippy about this. I can try and remap the data and then tell her what we're missing, perhaps.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "UGDKw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think she's a payroll only person so I'm not really sure how to deal with her and she seems a bit snippy about this. I can try and remap the data and then tell her what we're missing, perhaps."}]}]}]}, {"ts": "1723154465.917399", "text": "<@U07EJ2LP44S> can you please work with <PERSON><PERSON><PERSON><PERSON> on the Diversified?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "15Vr4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can you please work with <PERSON><PERSON><PERSON><PERSON> on the Diversified?"}]}]}]}, {"ts": "1723154506.605739", "text": "we can also check with her on the ETA for filled up templates if the files do not contain all the data", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BipCQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we can also check with her on the ETA for filled up templates if the files do not contain all the data"}]}]}]}, {"ts": "1723183220.172729", "text": "Do we have any different expectation from Practifi/Valgenesis other than what is being implemented?\n<https://compiify.atlassian.net/browse/COM-3369>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1723186845.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13430::a9672aa0561411efae3a1b0f8cac9ce6", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3369?atlOrigin=eyJpIjoiYjU0ZTQxYjg5MGU5NDAyNWIzZWFiMGI4OTc2ZGU3MWMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3369 Manage OTE workflows for Practifi>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13430::a9672aa2561411efae3a1b0f8cac9ce6", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13430::a9672aa1561411efae3a1b0f8cac9ce6", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13430\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13430\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3369", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "LKFXC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we have any different expectation from Practifi/Valgenesis other than what is being implemented?\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3369"}]}]}]}, {"ts": "1723190831.756249", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> Do we have the file they were trying to upload?\n<https://stride-hr.slack.com/archives/C0702497X55/p1723154498825179>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723190831.756249", "reply_count": 4, "attachments": [{"from_url": "https://stride-hr.slack.com/archives/C0702497X55/p1723154498825179", "ts": "1723154498.825179", "author_id": "U06RSCZ3EFP", "channel_id": "C0702497X55", "channel_team": "T04DM97F1UM", "is_msg_unfurl": true, "is_thread_root_unfurl": true, "message_blocks": [{"team": "T04DM97F1UM", "channel": "C0702497X55", "ts": "1723154498.825179", "message": {"blocks": [{"type": "rich_text", "block_id": "8q8m7", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " this afternoon i've been attempting to upload updated data to the stride system yet every time it appears the upload is nearly complete i get an \"internal error\" message. might someone be able to help me with this?"}]}]}]}}], "id": 1, "original_url": "https://stride-hr.slack.com/archives/C0702497X55/p1723154498825179", "fallback": "[August 8th, 2024 3:01 PM] j<PERSON><PERSON><PERSON>: <!here> this afternoon i've been attempting to upload updated data to the stride system yet every time it appears the upload is nearly complete i get an \"internal error\" message. might someone be able to help me with this?", "text": "<!here> this afternoon i've been attempting to upload updated data to the stride system yet every time it appears the upload is nearly complete i get an \"internal error\" message. might someone be able to help me with this?", "author_name": "jennifer ho<PERSON> (she/her/hers)", "author_link": "https://stride-hr.slack.com/team/U06RSCZ3EFP", "author_icon": "https://avatars.slack-edge.com/2024-04-01/6892202092435_985e2599a3e466ccd31e_48.jpg", "author_subname": "jennifer ho<PERSON> (she/her/hers)", "mrkdwn_in": ["text"], "footer": "Thread in Slack Conversation"}], "blocks": [{"type": "rich_text", "block_id": "z7tVw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Do we have the file they were trying to upload?\n"}, {"type": "link", "url": "https://stride-hr.slack.com/archives/C0702497X55/p1723154498825179"}]}]}]}, {"ts": "1723195696.484049", "text": "<!here> We have minor enhancement which was not finished and dev complete now. Basically a feature to remind managers to complete their review. changes are in <http://test.stridehr.io|test.stridehr.io>\n<https://www.loom.com/share/b161a29a22d94e3b92eec1041af4f104?sid=ccfcbe60-add3-4d9b-aed9-1101744f3047>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723195696.484049", "reply_count": 3, "edited": {"user": "U0690EB5JE5", "ts": "1723200957.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "q7HbY", "video_url": "https://www.loom.com/embed/b161a29a22d94e3b92eec1041af4f104?sid=ccfcbe60-add3-4d9b-aed9-1101744f3047&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/b161a29a22d94e3b92eec1041af4f104-9cab8558dcc5828e-4x3.jpg", "alt_text": "Merit View | Stride - 9 August 2024", "title": {"type": "plain_text", "text": "Merit View | Stride - 9 August 2024", "emoji": true}, "title_url": "https://www.loom.com/share/b161a29a22d94e3b92eec1041af4f104", "author_name": "<PERSON><PERSON><PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 56 sec  ", "emoji": true}}, {"type": "actions", "block_id": "WNo/P", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/b161a29a22d94e3b92eec1041af4f104?sid=ccfcbe60-add3-4d9b-aed9-1101744f3047"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"b161a29a22d94e3b92eec1041af4f104\",\"videoName\":\"Merit View | Stride - 9 August 2024\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/b161a29a22d94e3b92eec1041af4f104?sid=ccfcbe60-add3-4d9b-aed9-1101744f3047", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "4WbRt", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " We have minor enhancement which was not finished and dev complete now. Basically a feature to remind managers to complete their review. changes are in "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://www.loom.com/share/b161a29a22d94e3b92eec1041af4f104?sid=ccfcbe60-add3-4d9b-aed9-1101744f3047"}]}]}]}, {"ts": "1723195841.274629", "text": "Also please note that we already have feature in place to automatically notify mangers when the manager down level submits but is disabled by default.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723195841.274629", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1723195855.000000"}, "files": [{"id": "F07G8F68AN6", "created": 1723195837, "timestamp": 1723195837, "name": "Screenshot 2024-08-09 at 2.59.52 PM.png", "title": "Screenshot 2024-08-09 at 2.59.52 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 169274, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07G8F68AN6/screenshot_2024-08-09_at_2.59.52___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07G8F68AN6/download/screenshot_2024-08-09_at_2.59.52___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 181, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 242, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 363, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 403, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 484, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 516, "original_w": 1710, "original_h": 862, "thumb_tiny": "AwAYADDRx0Pf6U6m9R0NOoAKKKOaACkYDB+lLSN90/SgANFFFABR36UUo6UAFI33T9KWkb7p+lAH/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07G8F68AN6/screenshot_2024-08-09_at_2.59.52___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07G8F68AN6-bd42823a7f", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "K7u5N", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also please note that we already have feature in place to automatically notify mangers when the manager down level submits but is disabled by default."}]}]}]}, {"ts": "1723200060.694409", "text": "<!here> Revoke feature after approval for more flexibility. This is released to <http://test.stridehr.io|test.stridehr.io>\n<https://www.loom.com/share/73de84896407492ea6b3b78f3cb251b3?sid=8613e12d-ebfa-413a-aa8e-19b1ccdef1c2>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723200060.694409", "reply_count": 2, "edited": {"user": "U0690EB5JE5", "ts": "1723200093.000000"}, "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "GVyDa", "video_url": "https://www.loom.com/embed/73de84896407492ea6b3b78f3cb251b3?sid=8613e12d-ebfa-413a-aa8e-19b1ccdef1c2&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/73de84896407492ea6b3b78f3cb251b3-6d687821564de0e9-4x3.jpg", "alt_text": "Merit View | Stride - 9 August 2024", "title": {"type": "plain_text", "text": "Merit View | Stride - 9 August 2024", "emoji": true}, "title_url": "https://www.loom.com/share/73de84896407492ea6b3b78f3cb251b3", "author_name": "<PERSON><PERSON><PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 1 min  ", "emoji": true}}, {"type": "actions", "block_id": "cH9yE", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/73de84896407492ea6b3b78f3cb251b3?sid=8613e12d-ebfa-413a-aa8e-19b1ccdef1c2"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"73de84896407492ea6b3b78f3cb251b3\",\"videoName\":\"Merit View | Stride - 9 August 2024\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/73de84896407492ea6b3b78f3cb251b3?sid=8613e12d-ebfa-413a-aa8e-19b1ccdef1c2", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "lLgVJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Revoke feature after approval for more flexibility. This is released to "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://www.loom.com/share/73de84896407492ea6b3b78f3cb251b3?sid=8613e12d-ebfa-413a-aa8e-19b1ccdef1c2"}]}]}]}, {"ts": "1723212549.569189", "text": "What is the intention of the 'Compensation Data Template' that has only 6 columns", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723212549.569189", "reply_count": 1, "files": [{"id": "F07GC7WNA5A", "created": 1723212542, "timestamp": 1723212542, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 26243, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07GC7WNA5A/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07GC7WNA5A/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_360.png", "thumb_360_w": 360, "thumb_360_h": 33, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_480.png", "thumb_480_w": 480, "thumb_480_h": 44, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_720.png", "thumb_720_w": 720, "thumb_720_h": 66, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_800.png", "thumb_800_w": 800, "thumb_800_h": 73, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_960.png", "thumb_960_w": 960, "thumb_960_h": 87, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 93, "original_w": 1110, "original_h": 101, "thumb_tiny": "AwAEADDRT7tAAJPA60J92lHf60ANx+8/CnADJPvSf8tD9KUd/rQAij5moAG48UL95qB1NAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07GC7WNA5A/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07GC7WNA5A-7f8559094d", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "wrFjX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "What is the intention of the 'Compensation Data Template' that has only 6 columns"}]}]}]}, {"ts": "**********.248149", "text": "In the Compensation Data Template downloaded from an account, there is a duplicate Target Bonus Currency column.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.248149", "reply_count": 3, "files": [{"id": "F07GA44KEG2", "created": **********, "timestamp": **********, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 25159, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07GA44KEG2/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07GA44KEG2/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GA44KEG2-47c5802ad4/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GA44KEG2-47c5802ad4/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GA44KEG2-47c5802ad4/image_360.png", "thumb_360_w": 360, "thumb_360_h": 136, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GA44KEG2-47c5802ad4/image_480.png", "thumb_480_w": 480, "thumb_480_h": 181, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GA44KEG2-47c5802ad4/image_160.png", "original_w": 690, "original_h": 260, "thumb_tiny": "AwASADDQ+c8jGKMP6inL90UtADfn9vzoy3tTqQ9RQAnz/wCz+dGW74p1I3T8aAET7i/SnU2P/Vr9KdQAU0/eWnU1vvrQA6mv0/EU6mv938R/OgD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07GA44KEG2/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07GA44KEG2-68d68167c4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "01M/3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In the Compensation Data Template downloaded from an account, there is a duplicate Target Bonus Currency column."}]}]}]}, {"ts": "**********.089519", "text": "<!here> my daughter has fractured her finger so I’m at the hospital right now. I’ll likely have to skip the meeting, but I’ll try to dial if I can through the phone and will be in the listening mode.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.089519", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "mJSlE", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " my daughter has fractured her finger so I’m at the hospital right now. I’ll likely have to skip the meeting, but I’ll try to dial if I can through the phone and will be in the listening mode."}]}]}]}, {"ts": "**********.532679", "text": "<@U04DS2MBWP4> can you share the <PERSON> Watters call from fireflies? Also Valgenesis? Would it be easier if I used it myself?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532679", "reply_count": 6, "edited": {"user": "U07EJ2LP44S", "ts": "1723234523.000000"}, "blocks": [{"type": "rich_text", "block_id": "wofQO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " can you share the <PERSON> Watt<PERSON> call from fireflies? Also Valgenesis? Would it be easier if I used it myself?"}]}]}]}, {"ts": "1723235314.942249", "text": "<@U07EJ2LP44S> were we able to address the data upload issues playq and practifi were having?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1723235314.942249", "reply_count": 10, "blocks": [{"type": "rich_text", "block_id": "6mawE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " were we able to address the data upload issues playq and practifi were having?"}]}]}]}, {"ts": "1723245239.495359", "text": "<@U04DKEFP1K8> When you get a chance, can you please send the  CWA requirements for different planning levels? so that we can incorporate into the new comp builder design", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1723245239.495359", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "B2zpu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " When you get a chance, can you please send the  CWA requirements for different planning levels? so that we can incorporate into the new comp builder design"}]}]}]}, {"ts": "1723253903.340039", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> <@U07EJ2LP44S> I am see that customers are facing issues with upload feature and I had already anticipated how painful it is now for any one uses as error messaging is not clear and started working on making it more flexible, stable and helpful error messages, we will mostly be done with this early next week with UX work as well. Keep you posted on the status.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "heart", "users": ["U04DS2MBWP4", "U07EJ2LP44S"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "ctLKb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am see that customers are facing issues with upload feature and I had already anticipated how painful it is now for any one uses as error messaging is not clear and started working on making it more flexible, stable and helpful error messages, we will mostly be done with this early next week with UX work as well. Keep you posted on the status."}]}]}]}, {"ts": "1723427290.892939", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> Would like understand the scope of supporting international currency in budgetting, Is this just limited to introducing currency toggle work for budget info as well? if yes then we can try finishing this early next month. So the base currency still remains US but they can use the toggle. Can we try convincing customers for this scope? We can work on the larger scope of giving flexibility to keep any currency as base currency across product if we get more such requests.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1723427369.000000"}, "blocks": [{"type": "rich_text", "block_id": "JPhfK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Would like understand the scope of supporting international currency in budgetting, Is this just limited to introducing currency toggle work for budget info as well? if yes then we can try finishing this early next month. So the base currency still remains US but they can use the toggle. Can we try convincing customers for this scope? We can work on the larger scope of giving flexibility to keep any currency as base currency across product if we get more such requests."}]}]}]}, {"ts": "1723427356.029129", "text": "Also how about Pay bands as well? Do we need to introduce toggle there as well?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Fei<PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also how about Pay bands as well? Do we need to introduce toggle there as well?"}]}]}]}, {"ts": "1723427418.101019", "text": "I think for now we will keep only the US dollars for paybands", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Wkq8L", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think for now we will keep only the US dollars for paybands"}]}]}]}, {"ts": "1723427511.467129", "text": "So far it looks like only Alayacare has a need for Canadian dollars for the best currency. We can discuss this more during the eng call", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "GnBq2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So far it looks like only Alayacare has a need for Canadian dollars for the best currency. We can discuss this more during the eng call"}]}]}]}, {"ts": "1723427534.125269", "text": "Sure sounds good.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MKL8q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure sounds good."}]}]}]}, {"ts": "1723428018.486789", "text": "<@U04DKEFP1K8> FYI... I have synced all the changes from BE-dev till Production/Demo env. There were some fixes and minor merit planning enhancements i.e. nudge and revoke features.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1723428037.000000"}, "blocks": [{"type": "rich_text", "block_id": "+7FTQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " FYI... I have synced all the changes from BE-dev till Production/Demo env. There were some fixes and minor merit planning enhancements i.e. nudge and revoke features."}]}]}]}, {"ts": "1723428063.401379", "text": "<@U0690EB5JE5> please let us know the status of fixing date upload issues ", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1723428063.401379", "reply_count": 13, "blocks": [{"type": "rich_text", "block_id": "Tjhq4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " please let us know the status of fixing date upload issues "}]}]}]}, {"ts": "1723428569.409729", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> Can you please help me with slack threads/tickets related upload issues? there were too many slack threads and I am unable to track those issues clearly.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723428063.401379", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1723428643.000000"}, "blocks": [{"type": "rich_text", "block_id": "BVvMX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can you please help me with slack threads/tickets related upload issues? there were too many slack threads and I am unable to track those issues clearly."}]}]}]}, {"ts": "1723477962.358459", "text": "<@U04DKEFP1K8> Fixes for the three issues discussed last night PST are deployed to TEST ENV (`developmet`/`dev`). I didn't get a chance to test those fixes. Please do verify and merge the changes to PROD.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723428063.401379", "subtype": "thread_broadcast", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "BwAtO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Fixes for the three issues discussed last night PST are deployed to TEST ENV ("}, {"type": "text", "text": "developmet", "style": {"code": true}}, {"type": "text", "text": "/"}, {"type": "text", "text": "dev", "style": {"code": true}}, {"type": "text", "text": "). I didn't get a chance to test those fixes. Please do verify and merge the changes to PROD."}]}]}]}, {"ts": "1723478243.434799", "text": "<!here> I am very tired and won't be able to join eng discussion call. updates from my end\n\n• OTE merit planning dev work is complete and ready for QA\n• Fixes for upload issues are deployed to TEST ENV. Requested <PERSON><PERSON>bh to verify and deploy to production\n• Need to finalize the scope of work for supporting international currency in budgetting. \n• Please let me know if there are any other new requirements from customers that needs to prioritized over other known work.\n• work for improving data upload user experience is in progress and ETA is by EOW worst case", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723478243.434799", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1723478774.000000"}, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "RbK1U", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I am very tired and won't be able to join eng discussion call. updates from my end\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OTE merit planning dev work is complete and ready for QA"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Fixes for upload issues are deployed to TEST ENV. Requested <PERSON><PERSON>bh to verify and deploy to production"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Need to finalize the scope of work for supporting international currency in budgetting. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Please let me know if there are any other new requirements from customers that needs to prioritized over other known work."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "work for improving data upload user experience is in progress and ETA is by EOW worst case"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1723479592.043299", "text": "<!here> As just discussed in the eng call, let's use this <https://docs.google.com/spreadsheets/d/1YZOTN_AAVx_-a3faQfQM2IseXRo-qUDfXlLgmp6T_ws/edit?gid=891744166#gid=891744166|sheet> to manage customer roadmap and their timelines", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F07EKKX5954", "created": 1722369064, "timestamp": 1722369064, "name": "Customer Tracker", "title": "Customer Tracker - No Longer Updating", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1YZOTN_AAVx_-a3faQfQM2IseXRo-qUDfXlLgmp6T_ws", "external_url": "https://docs.google.com/spreadsheets/d/1YZOTN_AAVx_-a3faQfQM2IseXRo-qUDfXlLgmp6T_ws/edit?usp=sharing", "url_private": "https://docs.google.com/spreadsheets/d/1YZOTN_AAVx_-a3faQfQM2IseXRo-qUDfXlLgmp6T_ws/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSJoyaWigBMmloooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07EKKX5954/customer_tracker", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Kz1li", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " As just discussed in the eng call, let's use this "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1YZOTN_AAVx_-a3faQfQM2IseXRo-qUDfXlLgmp6T_ws/edit?gid=891744166#gid=891744166", "text": "sheet"}, {"type": "text", "text": " to manage customer roadmap and their timelines"}]}]}]}, {"ts": "1723484586.497869", "text": "<@U07EJ2LP44S> For playQ updates and deletes have been processed successfully after recent code changes were merged. <PERSON><PERSON> mentioned She wants to add new employees too Row no 90-93 I do not see these rows in the csv she had provided. lets confirm that in the meeting at 11", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1723484586.497869", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "gHYTx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " For playQ updates and deletes have been processed successfully after recent code changes were merged. <PERSON><PERSON> mentioned She wants to add new employees too Row no 90-93 I do not see these rows in the csv she had provided. lets confirm that in the meeting at 11"}]}]}]}, {"ts": "1723497573.287809", "text": "<!here> upload issue for Nauto, Practifi and PlayQ have been resolved. These will be deployed in Nauto's production environment at the end of the day. For playq and practifi these are already available in their environment", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1723497573.287809", "reply_count": 1, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "partyparrot", "users": ["U04DS2MBWP4", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "EWUoG", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " upload issue for Nauto, Practifi and PlayQ have been resolved. These will be deployed in Nauto's production environment at the end of the day. For playq and practifi these are already available in their environment"}]}]}]}, {"ts": "1723512814.417909", "text": "Is All good with <PERSON><PERSON> for now?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723512814.417909", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "jP9Fy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is All good with <PERSON><PERSON> for now?"}]}]}]}, {"ts": "1723551930.264039", "text": "Production deployment done. We also have pushed a minor improvement i.e. the rows in Org View and Merit view table will be sorted by Employee names in alphabetical order by default.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723497573.287809", "subtype": "thread_broadcast", "reactions": [{"name": "heart", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "7Pfxb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Production deployment done. We also have pushed a minor improvement i.e. the rows in Org View and Merit view table will be sorted by Employee names in alphabetical order by default."}]}]}]}, {"ts": "1723571061.721049", "text": "We need to establish the Rippling integration. PlayQ would like to leverage it, but there is a specific process through Merge: <https://help.merge.dev/en/articles/9265388-how-can-i-access-the-rippling-integration> Who should own this?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723571061.721049", "reply_count": 1, "attachments": [{"image_url": "https://downloads.intercomcdn.com/i/o/379082/107841514fe57a3f0b2a855a/065e08fde79f8a1ee25eb6318655bdc9.jpg", "image_width": 1200, "image_height": 628, "image_bytes": 74161, "from_url": "https://help.merge.dev/en/articles/9265388-how-can-i-access-the-rippling-integration", "id": 1, "original_url": "https://help.merge.dev/en/articles/9265388-how-can-i-access-the-rippling-integration", "fallback": "How can I access the Rippling integration? | Merge Help Center", "text": "An overview of <PERSON><PERSON>’s partnership with R<PERSON><PERSON> and instructions on how to access the integration", "title": "How can I access the Rippling integration? | Merge Help Center", "title_link": "https://help.merge.dev/en/articles/9265388-how-can-i-access-the-rippling-integration", "service_name": "help.merge.dev"}], "blocks": [{"type": "rich_text", "block_id": "m0GJG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We need to establish the Rippling integration. PlayQ would like to leverage it, but there is a specific process through Merge: "}, {"type": "link", "url": "https://help.merge.dev/en/articles/9265388-how-can-i-access-the-rippling-integration"}, {"type": "text", "text": " Who should own this?"}]}]}]}, {"ts": "1723582959.778789", "text": "2pm meeting <@U04DS2MBWP4>", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3dJ8I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "2pm meeting "}, {"type": "user", "user_id": "U04DS2MBWP4"}]}]}]}, {"ts": "1723582970.812139", "text": "<https://us06web.zoom.us/j/86735736875?pwd=b6ZOakFcr5WWbeFjSI2as7hasbPGra.1>", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R07H5TV9KG9", "block_id": "JqU8r", "api_decoration_available": false, "call": {"v1": {"id": "R07H5TV9KG9", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1723582971, "active_participants": [], "all_participants": [], "display_id": "867-3573-6875", "join_url": "https://us06web.zoom.us/j/86735736875?pwd=b6ZOakFcr5WWbeFjSI2as7hasbPGra.1", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1723735848, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/86735736875?pwd=b6ZOakFcr5WWbeFjSI2as7hasbPGra.1", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "l/5rb", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://us06web.zoom.us/j/86735736875?pwd=b6ZOakFcr5WWbeFjSI2as7hasbPGra.1"}]}]}]}, {"ts": "1723584603.756699", "text": "Anything else I should add?\n---\nHi <PERSON> and <PERSON>,\n\nJust a reminder, I scheduled a call for tomorrow; can you confirm if you are available to join?\n\nWe have reviewed the data and are missing some elements, but we should have a first draft in the application to show you tomorrow. Additionally, we'd like to gather some additional information about your upcoming cycle, including dates. We'd like to cover:\n\n• first data review \n• cycle timeline, including start date and approval date \n• specific cycle components -eg are hourly employees included, exclusions by hire date etc \n• paycor authentication so we can begin the integration process\n\nLooking forward to connecting!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723584603.756699", "reply_count": 8, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "cflG9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Anything else I should add?\n---\nHi <PERSON> and <PERSON>,\n\nJust a reminder, I scheduled a call for tomorrow; can you confirm if you are available to join?\n\nWe have reviewed the data and are missing some elements, but we should have a first draft in the application to show you tomorrow. Additionally, we'd like to gather some additional information about your upcoming cycle, including dates. We'd like to cover:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "first data review "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "cycle timeline, including start date and approval date "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "specific cycle components -eg are hourly employees included, exclusions by hire date etc "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "paycor authentication so we can begin the integration process"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n\nLooking forward to connecting!"}]}]}]}, {"ts": "**********.292099", "text": "<!here> <https://div-energy.stridehr.io/> has been updated to have diversified energy data. Need to enable google login account for all of us. <@U07EJ2LP44S> you can use the local credential which you had created with api.", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "attachments": [{"from_url": "https://div-energy.stridehr.io/", "service_icon": "https://div-energy.stridehr.io/apple-touch-icon.png", "id": 1, "original_url": "https://div-energy.stridehr.io/", "fallback": "Stride", "text": "Web site created using create-react-app", "title": "Stride", "title_link": "https://div-energy.stridehr.io/", "service_name": "div-energy.stridehr.io"}], "blocks": [{"type": "rich_text", "block_id": "y1ei2", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://div-energy.stridehr.io/"}, {"type": "text", "text": " has been updated to have diversified energy data. Need to enable google login account for all of us. "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " you can use the local credential which you had created with api."}]}]}]}, {"ts": "**********.416179", "text": "Please record CWA meeting <!here> ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.416179", "reply_count": 1, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "AHHe2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please record CWA meeting "}, {"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}]}]}]}, {"ts": "**********.650789", "text": "<!here> just wrapped up CWA meeting, will start the eng meeting at 905am PT", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "N/7rd", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " just wrapped up CWA meeting, will start the eng meeting at 905am PT"}]}]}]}, {"ts": "**********.600149", "text": "<@U07EJ2LP44S> Here are some questions we need clarity on to effectively manage compensation adjustments for hourly employees:, ( some of answers are obvious but lets get confirmation in any case)\n\n\t•\tWhen preparing the compensation budget, should we convert hourly wages to an annualized salary?\n\t•\tIt would be helpful to have an example of how the salary budget is prepared when the employee population includes hourly workers.\n\t•\tHow should hours per week be accounted for in this process?\n\t•\tShould salary adjustments for hourly employees be based on their hourly rate, or should we use the annualized figures?\n        •\tIf the annualized approach is preferred, could you provide a sample conversion formula, if one has been used?\n• Also please confirm hourly wages uses how many decimal digits", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.600149", "reply_count": 3, "edited": {"user": "U04DKEFP1K8", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "B06Jx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Here are some questions we need clarity on to effectively manage compensation adjustments for hourly employees:, ( some of answers are obvious but lets get confirmation in any case)\n\n\t•\tWhen preparing the compensation budget, should we convert hourly wages to an annualized salary?\n\t•\tIt would be helpful to have an example of how the salary budget is prepared when the employee population includes hourly workers.\n\t•\tHow should hours per week be accounted for in this process?\n\t•\tShould salary adjustments for hourly employees be based on their hourly rate, or should we use the annualized figures?\n        •\tIf the annualized approach is preferred, could you provide a sample conversion formula, if one has been used?\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also please confirm hourly wages uses how many decimal digits"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "**********.606129", "text": "<@U04DS2MBWP4> Can you cancel the rightway call that was scheduled for tomorrow?", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "done", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "fT7Qd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Can you cancel the rightway call that was scheduled for tomorrow?"}]}]}]}, {"ts": "1723657870.847719", "text": "Does anyone know if Alayacare was sent info on the Bamboo integration?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723657870.847719", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "weHaH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Does anyone know if Alayacare was sent info on the Bamboo integration?"}]}]}]}, {"ts": "1723729593.158479", "text": "Good morning. I woke up under the weather today as well. I just packed up but will be a little too late for engineering call. If I am <PERSON><PERSON><PERSON><PERSON> let me know. otherwise I’ll be back shortly. After ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723729593.158479", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "vXJxG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Good morning. I woke up under the weather today as well. I just packed up but will be a little too late for engineering call. If I am <PERSON><PERSON><PERSON><PERSON> let me know. otherwise I’ll be back shortly. After "}]}]}]}, {"ts": "1723743055.227639", "text": "<!here> engg team ( na<PERSON> and muke<PERSON>) are looking into the issue now. I will keep this thread updated as i receive more updates from them ", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1723743055.227639", "reply_count": 3, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "yoERx", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " engg team ( na<PERSON> and muke<PERSON>) are looking into the issue now. I will keep this thread updated as i receive more updates from them "}]}]}]}, {"ts": "1723755763.335139", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> are we meeting?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1723755763.335139", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "5J6CX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " are we meeting?"}]}]}]}, {"ts": "1723770450.844349", "text": "<!here> We need to fix following issues asap for Nauto\n• <https://compiify.atlassian.net/browse/COM-3504> (Budget % (used) should exclude equity utilization) - this is an enhancement and not a bug \n• <https://compiify.atlassian.net/browse/COM-3505> (“*Do you have one combined budget for salary increase?*” is set to Yes and Market adjustment is chosen as one of the comp component then column for market adjustment is not visible in merit view table and no adjustment can be input.) - this is a bug", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1723770450.844349", "reply_count": 3, "edited": {"user": "U04DKEFP1K8", "ts": "1723770469.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13565::ea11fb505b6b11ef9b0229fa6cc801bd", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3504?atlOrigin=eyJpIjoiNjJlY2RhZjU1NWQ1NDQ5ZWIxMTkwYjc4MTRjMjI2YTEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3504 Budget % (used) should exclude equity utilization>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13565::ea11fb545b6b11ef9b0229fa6cc801bd", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13565::ea11fb515b6b11ef9b0229fa6cc801bd", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13565\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13565\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3504", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:13566::ea11fb525b6b11ef9b0229fa6cc801bd", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3505?atlOrigin=eyJpIjoiYjJmOTMwMDEwOWJlNDQ5Yjk2MDZmMWViYTNkOTQ2ODMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3505 Enhancement required for option [Do you have one combined budget fo…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13566::ea11fb555b6b11ef9b0229fa6cc801bd", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13566::ea11fb535b6b11ef9b0229fa6cc801bd", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13566\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13566\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3505", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "mT3WJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " We need to fix following issues asap for Nauto\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3504"}, {"type": "text", "text": " (Budget % (used) should exclude equity utilization) - this is an enhancement and not a bug "}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3505"}, {"type": "text", "text": " (“"}, {"type": "text", "text": "Do you have one combined budget for salary increase?", "style": {"bold": true}}, {"type": "text", "text": "” is set to Yes and Market adjustment is chosen as one of the comp component then column for market adjustment is not visible in merit view table and no adjustment can be input.) - this is a bug"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1723832155.980879", "text": "<@U0690EB5JE5> we should a explore if there is a easy way to restrict access to environment during maintenance. just a thought", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1723832155.980879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "KhtvZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we should a explore if there is a easy way to restrict access to environment during maintenance. just a thought"}]}]}]}, {"ts": "1723860001.347229", "text": "<@U04DKEFP1K8> Hope all is well with <PERSON><PERSON> :slightly_smiling_face: ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723860001.347229", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "xJBl6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Hope all is well with <PERSON><PERSON> "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1723860006.036469", "text": "So far", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "r7msv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So far"}]}]}]}, {"ts": "1723860712.151959", "text": "We need to check with them what they expect out of cycle closure and be ready. We don’t know what happens today on cycle closure and is never tested.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723860001.347229", "subtype": "thread_broadcast", "reactions": [{"name": "100-blink", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "mbaXs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We need to check with them what they expect out of cycle closure and be ready. We "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " know what happens today on cycle closure and is never tested."}]}]}]}, {"ts": "1724038764.074129", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> It looks like people insights is still including equity in the  budget calculations for Nauto.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qoGzo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " It looks like people insights is still including equity in the  budget calculations for Nauto."}]}]}]}, {"ts": "1724038881.535579", "text": "<!here> In the eng call tomorrow, let's spend some time going thru the people insights section of Nauto (post recent submissions by their manager's) to look for any obvious errors/bugs unless <@U04DKEFP1K8> has already looked into it,", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1724038881.535579", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "EjMRG", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " In the eng call tomorrow, let's spend some time going thru the people insights section of Nauto (post recent submissions by their manager's) to look for any obvious errors/bugs unless "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " has already looked into it,"}]}]}]}, {"ts": "1724039684.189019", "text": "<@U07EJ2LP44S> I have not received, complete list of customer requirements as discussed in Wednesday meeting. My knowledge of this is same as of Wednesday. Was there any progress on this front? Please do post here once you have created all the tickets.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724039684.189019", "reply_count": 4, "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "DxXhn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have not received, complete list of customer requirements as discussed in Wednesday meeting. My knowledge of this is same as of Wednesday. Was there any progress on this front? Please do post here once you have created all the tickets."}]}]}]}, {"ts": "1724055771.590829", "text": "Agenda for today:\n• update on Customer requirements\n• Nauto: Review and Cycle closure", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724055771.590829", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1724055859.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "cVHfW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "update on Customer requirements"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Nauto: Review and Cycle closure"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724076235.115839", "text": "<!here> i have bit of a headache and will start today between 10-1030am ( just need bit more sleep)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lE1QR", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " i have bit of a headache and will start today between 10-1030am ( just need bit more sleep)"}]}]}]}, {"ts": "1724077355.128939", "text": "I am travelling to Bangalore. Getting delayed due to traffic and rain", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Xc1KP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am travelling to Bangalore. Getting delayed due to traffic and rain"}]}]}]}, {"ts": "1724077447.095969", "text": "We can  push the eng call to 10 AM if that works better for everybody", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1724077447.095969", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "FyY7M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can  push the eng call to 10 AM if that works better for everybody"}]}]}]}, {"ts": "**********.124429", "text": "Fine with me", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+gcka", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Fine with me"}]}]}]}, {"ts": "**********.367339", "text": "I am getting an error in diversified's account on the integrations page; it will not load, 503 error pops up. This was happening last week to other accounts but it is still happening to Diversified", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.367339", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "2tX7M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am getting an error in diversified's account on the integrations page; it will not load, 503 error pops up. This was happening last week to other accounts but it is still happening to Diversified"}]}]}]}, {"ts": "**********.126679", "text": "<!here> I will send a new meeting invite for 10 am for the eng call", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nf6VA", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I will send a new meeting invite for 10 am for the eng call"}]}]}]}, {"ts": "**********.637889", "text": "<@U04DS2MBWP4> Reg. filters not showing up in insights. for filters there was fix pushed to test ENV today. but not in production ENV.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.637889", "reply_count": 6, "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "a2Ar/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Reg. filters not showing up in insights. for filters there was fix pushed to test ENV today. but not in production ENV."}]}]}]}, {"ts": "1724092948.055539", "text": "More updates:\n• Alerts includes all the employees and equity as well\n• There few sections intentionally designed to have all the employees and I agree its difficult explain this and should change it\n• Mayank will share the list of sections have submitted vs not submitted filter.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.637889", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1724092990.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "84LJB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "More updates:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Alerts includes all the employees and equity as well"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "There few sections intentionally designed to have all the employees and I agree its difficult explain this and should change it"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Mayank will share the list of sections have submitted vs not submitted filter."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724105860.668249", "text": "Hey <@U07EJ2LP44S> I have updated valgenesis environment with latest resynced data. There are 2 errors currently which engineering needs to resolve before the environment can be handed off to <PERSON>.\n• <https://compiify.atlassian.net/browse/COM-3517>\n• <https://compiify.atlassian.net/browse/COM-3518>\nI will discuss these further with <PERSON><PERSON><PERSON> tonight.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724105860.668249", "reply_count": 4, "edited": {"user": "U04DKEFP1K8", "ts": "1724105893.000000"}, "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}, {"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13578::ed1705105e7811efa0abf34f6a52929b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3517?atlOrigin=eyJpIjoiMjlmM2VjOWYwZjEyNDkyYmJiNTgxZjEwOWIyMzQ4ZmMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3517 Unable to access Settings Page on Valgenesis environment>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13578::ed172c205e7811efa0abf34f6a52929b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13578::ed1705115e7811efa0abf34f6a52929b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13578\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13578\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3517", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:13579::ed1705125e7811efa0abf34f6a52929b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3518?atlOrigin=eyJpIjoiM2VkMTNiZDQzMWE3NGFjMmE2MWQ3YTUwN2UyMTI0MWQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3518 Unable to collect annual salary data via hibob integration>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13579::ed172c215e7811efa0abf34f6a52929b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13579::ed1705135e7811efa0abf34f6a52929b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13579\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13579\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3518", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "YQ43g", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have updated valgenesis environment with latest resynced data. There are 2 errors currently which engineering needs to resolve before the environment can be handed off to <PERSON>.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3517"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3518"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nI will discuss these further with <PERSON><PERSON><PERSON> tonight."}]}]}]}, {"ts": "1724107788.050499", "text": "<@U04DS2MBWP4> Lowering the prioirty to review people insights for nauto ( <PERSON> mentioned in the channel they are not planning to use them in this cycle, will focus on other tasks)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724107788.050499", "reply_count": 3, "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Bma69", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Lowering the prioirty to review people insights for nauto ( <PERSON> mentioned in the channel they are not planning to use them in this cycle, will focus on other tasks)"}]}]}]}, {"ts": "1724139543.665269", "text": "<!here> I synced with <@U04DKEFP1K8> and listed all the requirements in my <https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=1270401060#gid=1270401060|roadmap sheet>(`Latest Prioroties` sheet)\nI have put rough estimates and also assigned priority. We will review/update this in the eng call and add any missing requirements and finalize the priorities for next 2 to 3 weeks as beyond that or even before that the list would change a bit :slightly_smiling_face: .\n\nAlso I have created place holder tickets for all the requirements listed. <@U07EJ2LP44S> We will add details to the tickets as we go. Please feel free to edit tickets with correct/more details.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724140002.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F06MMTVHJCA", "created": 1709446944, "timestamp": 1709446944, "name": "Project Plan - March to May", "title": "Roadmap - <PERSON><PERSON><PERSON>", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk", "external_url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSJOelJlvSnUUANy3pSjPelooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06MMTVHJCA/project_plan_-_march_to_may", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "taLwz", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I synced with "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " and listed all the requirements in my "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=1270401060#gid=1270401060", "text": "roadmap sheet"}, {"type": "text", "text": "("}, {"type": "text", "text": "Latest Prioroties", "style": {"code": true}}, {"type": "text", "text": " sheet)\nI have put rough estimates and also assigned priority. We will review/update this in the eng call and add any missing requirements and finalize the priorities for next 2 to 3 weeks as beyond that or even before that the list would change a bit "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " .\n\nAlso I have created place holder tickets for all the requirements listed. "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We will add details to the tickets as we go. Please feel free to edit tickets with correct/more details."}]}]}]}, {"ts": "1724165439.670789", "text": "<@U07EJ2LP44S> I am going to fix the integration page error. We need to look at cycle page error. This could be due to missing data.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.367339", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "0UsqH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am going to fix the integration page error. We need to look at cycle page error. This could be due to missing data."}]}]}]}, {"ts": "1724168001.005549", "text": "<@U04DKEFP1K8> I have fixed both the issues in my PR. Please try again and let me know.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724105860.668249", "subtype": "thread_broadcast", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "0MpeT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I have fixed both the issues in my PR. Please try again and let me know."}]}]}]}, {"ts": "1724182792.092689", "text": "Hi everyone! Thank you for a great first day, I learned a ton in a short time. <@U04DKEFP1K8> and <@U0690EB5JE5>, are the two of you available to meet *tomorrow* for about 30-45 minutes after the engineering sync so I can ask a few questions regarding engineering-specific workflows and challenges?\n\nWe can also discuss with all of us if the group prefers, just depends on what the agenda is for tomorrow's call.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724182792.092689", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "+aGAL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hi everyone! Thank you for a great first day, I learned a ton in a short time. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " and "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": ", are the two of you available to meet "}, {"type": "text", "text": "tomorrow", "style": {"bold": true}}, {"type": "text", "text": " for about 30-45 minutes after the engineering sync so I can ask a few questions regarding engineering-specific workflows and challenges?\n\nWe can also discuss with all of us if the group prefers, just depends on what the agenda is for tomorrow's call."}]}]}]}, {"ts": "1724182841.943479", "text": "Also if someone can give me <PERSON><PERSON> access, that would be great.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724182841.943479", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "I9RbK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also if someone can give me <PERSON><PERSON> access, that would be great."}]}]}]}, {"ts": "1724188121.586739", "text": "Re: SFTP for CWA - this came from exponent HR directly", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1724188121.586739", "reply_count": 12, "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "files": [{"id": "F07HS9V2UHG", "created": 1724188113, "timestamp": 1724188113, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 46104, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07HS9V2UHG/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07HS9V2UHG/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HS9V2UHG-6903c16d69/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HS9V2UHG-6903c16d69/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HS9V2UHG-6903c16d69/image_360.png", "thumb_360_w": 360, "thumb_360_h": 134, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HS9V2UHG-6903c16d69/image_480.png", "thumb_480_w": 480, "thumb_480_h": 179, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HS9V2UHG-6903c16d69/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HS9V2UHG-6903c16d69/image_720.png", "thumb_720_w": 720, "thumb_720_h": 268, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HS9V2UHG-6903c16d69/image_800.png", "thumb_800_w": 800, "thumb_800_h": 298, "original_w": 895, "original_h": 333, "thumb_tiny": "AwARADDRxz0NGPY0ZIJpQSfSgAz7GjPsaWigBM+xoz7GlooAY1KnSkalTpQA6iiigAooooA//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07HS9V2UHG/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07HS9V2UHG-43174bd700", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Ksj6P", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Re: SFTP for CWA - this came from exponent HR directly"}]}]}]}, {"ts": "1724215456.747459", "text": "<!here> Just fyi, I will not be able to join the eng call tomorrow. Need that time to focus on the 10 am webinar", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Rf5ZZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Just fyi, I will not be able to join the eng call tomorrow. Need that time to focus on the 10 am webinar"}]}]}]}, {"ts": "1724239464.109269", "text": "<!here> Updates:\n• <@U04DKEFP1K8> I have synced the recent fixes to production except adjustment letter changes.\n• COLA feature work is dev complete and ready for QA", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724242826.000000"}, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "driwG", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Updates:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I have synced the recent fixes to production except adjustment letter changes."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "COLA feature work is dev complete and ready for QA"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724240440.797149", "text": "Also I am looking at people insights and I feel it definitely needs some improvements/rework for better usability. This would have been not a good experience if <PERSON><PERSON> wanted to use this time. We can discuss this in Thursday's eng discussion.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "ygntS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also I am looking at people insights and I feel it definitely needs some improvements/rework for better usability. This would have been not a good experience if <PERSON><PERSON> wanted to use this time. We can discuss this in Thursday's eng discussion."}]}]}]}, {"ts": "**********.458749", "text": "I am getting this in Diversified's account, but there are 16 employees in there.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.458749", "reply_count": 5, "files": [{"id": "F07HU05TMQD", "created": **********, "timestamp": **********, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 30071, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07HU05TMQD/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07HU05TMQD/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HU05TMQD-45c1f9d20a/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HU05TMQD-45c1f9d20a/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HU05TMQD-45c1f9d20a/image_360.png", "thumb_360_w": 360, "thumb_360_h": 181, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HU05TMQD-45c1f9d20a/image_480.png", "thumb_480_w": 480, "thumb_480_h": 242, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HU05TMQD-45c1f9d20a/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HU05TMQD-45c1f9d20a/image_720.png", "thumb_720_w": 720, "thumb_720_h": 363, "original_w": 796, "original_h": 401, "thumb_tiny": "AwAYADDToopM+xoACcUA5pDz2NL+BoAWikz7GlFABRRRQBE0CsxYs3PoaT7Ov95/zqaindgFFFFID//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07HU05TMQD/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07HU05TMQD-802f0f56d5", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "HU0Wu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am getting this in Diversified's account, but there are 16 employees in there."}]}]}]}, {"ts": "**********.395069", "text": "It also shows two errors upon loading the page (this is the home/org view)", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "a5tbf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It also shows two errors upon loading the page (this is the home/org view)"}]}]}]}, {"ts": "**********.258699", "text": "Diversified: employee count graph has repeated departments:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.258699", "reply_count": 1, "files": [{"id": "F07HWTHBJ2X", "created": **********, "timestamp": **********, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 21324, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07HWTHBJ2X/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07HWTHBJ2X/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HWTHBJ2X-5e64db92b7/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HWTHBJ2X-5e64db92b7/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HWTHBJ2X-5e64db92b7/image_360.png", "thumb_360_w": 360, "thumb_360_h": 165, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HWTHBJ2X-5e64db92b7/image_480.png", "thumb_480_w": 480, "thumb_480_h": 220, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HWTHBJ2X-5e64db92b7/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07HWTHBJ2X-5e64db92b7/image_720.png", "thumb_720_w": 720, "thumb_720_h": 330, "original_w": 756, "original_h": 347, "thumb_tiny": "AwAWADDSxnuaWkyBgEgZoyM4yM+lACb137e9OqH/AJej9KlBBOARkU2gFopAQc4IOKAQehB+lIBGQMQT1HSgoCwbuOlOooAbsG/d3oCAMWHU06igBqoFJx360IgQYFOooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07HWTHBJ2X/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07HWTHBJ2X-2c5e3c567d", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "brNh/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified: employee count graph has repeated departments:"}]}]}]}, {"ts": "1724268434.581099", "text": "*Recommendation* - if you can spare some time on a drive or walk, I highly recommend *<https://www.lennysnewsletter.com/p/a-framework-for-finding-product-market|this podcast episode>* about the four stages of Product/Market Fit and what success means at each stage. I believe Stride is mostly in Level 1 right now, about to enter the beginning of Level 2 (out of 4 levels). How a startup prioritizes is highly dependent on the stage, especially for those first two levels.\n\nIf you don't have a full hour+, you could start at 9 minutes, and stop around 49 minutes.", "user": "U07HCJ07H7G", "type": "message", "edited": {"user": "U07HCJ07H7G", "ts": "1724268466.000000"}, "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"image_url": "https://substackcdn.com/image/fetch/w_1200,h_600,c_fill,f_jpg,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F23538ef0-2702-41c4-8083-2d54c4c479f9_2048x2048.png", "image_width": 1200, "image_height": 600, "image_bytes": 60657, "from_url": "https://www.lennysnewsletter.com/p/a-framework-for-finding-product-market", "service_icon": "https://substackcdn.com/image/fetch/f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fbucketeer-e05bbc84-baa3-437e-9518-adb32be77984.s3.amazonaws.com%2Fpublic%2Fimages%2Fc7cde267-8f9e-47fa-9aef-5be03bad95ed%2Fapple-touch-icon-57x57.png", "id": 1, "original_url": "https://www.lennysnewsletter.com/p/a-framework-for-finding-product-market", "fallback": "A framework for finding product-market fit | <PERSON> (First Round Capital)", "text": "<PERSON> on why product-market fit matters, how to get unstuck, and First Round Capital’s four-part PMF framework.", "title": "A framework for finding product-market fit | <PERSON> (First Round Capital)", "title_link": "https://www.lennysnewsletter.com/p/a-framework-for-finding-product-market", "service_name": "lennysnewsletter.com"}], "blocks": [{"type": "rich_text", "block_id": "dwBG2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Recommendation", "style": {"bold": true}}, {"type": "text", "text": " - if you can spare some time on a drive or walk, I highly recommend "}, {"type": "link", "url": "https://www.lennysnewsletter.com/p/a-framework-for-finding-product-market", "text": "this podcast episode", "style": {"bold": true}}, {"type": "text", "text": " about the four stages of Product/Market Fit and what success means at each stage. I believe Stride is mostly in Level 1 right now, about to enter the beginning of Level 2 (out of 4 levels). How a startup prioritizes is highly dependent on the stage, especially for those first two levels.\n\nIf you don't have a full hour+, you could start at 9 minutes, and stop around 49 minutes."}]}]}]}, {"ts": "1724272253.098129", "text": "<@U0690EB5JE5> <https://success.15five.com/hc/en-us/articles/4404196285339-Manage-data-in-15Five-using-SFTP>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1724272253.098129", "reply_count": 3, "attachments": [{"from_url": "https://success.15five.com/hc/en-us/articles/4404196285339-Manage-data-in-15Five-using-SFTP", "service_icon": "https://success.15five.com/hc/theming_assets/01HZPQTR0PZ1MH6EEAJES12EH9", "id": 1, "original_url": "https://success.15five.com/hc/en-us/articles/4404196285339-Manage-data-in-15Five-using-SFTP", "fallback": "15Five Help Center: Manage data in 15Five using SFTP", "text": "Similar to uploading employee information via CSV or API, you can automatically manage employee data via SFTP using 15Five and your HRIS. SFTP (Secure File Transfer Protocol or SSH File Transfer Pr...", "title": "Manage data in 15Five using SFTP", "title_link": "https://success.15five.com/hc/en-us/articles/4404196285339-Manage-data-in-15Five-using-SFTP", "service_name": "15Five Help Center"}], "blocks": [{"type": "rich_text", "block_id": "BoiK9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://success.15five.com/hc/en-us/articles/4404196285339-Manage-data-in-15Five-using-SFTP"}]}]}]}, {"ts": "1724272376.452749", "text": "<@U07EJ2LP44S> <@U04DS2MBWP4> i am working on adjustment letters for nauto, do you guys need me to join implementation standup?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724272376.452749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "oHeyG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " i am working on adjustment letters for na<PERSON>, do you guys need me to join implementation standup?"}]}]}]}, {"ts": "1724291921.820139", "text": "<@U07EJ2LP44S> sorry I should have updated. We have disabled reset entirely for non stride admin users.\n<https://stride-hr.slack.com/archives/C0702497X55/p**********653359|https://stride-hr.slack.com/archives/C0702497X55/p**********653359>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724291921.820139", "reply_count": 2, "attachments": [{"from_url": "https://stride-hr.slack.com/archives/C0702497X55/p**********653359", "ts": "**********.653359", "author_id": "U07EJ2LP44S", "channel_id": "C0702497X55", "channel_team": "T04DM97F1UM", "is_msg_unfurl": true, "message_blocks": [{"team": "T04DM97F1UM", "channel": "C0702497X55", "ts": "**********.653359", "message": {"blocks": [{"type": "rich_text", "block_id": "jPU/f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Quick follow up from our call yesterday; we are working on getting the Rippling integration set up. It will require 'Rippling App Management', which allows access to the app necessary to make the connection. Will this be feasible on your end?\n\nIn the meantime you can do a full account reset using the CSV templates. Let me know if you have any questions on that or run into any issues!"}]}]}]}}], "private_channel_prompt": true, "id": 1, "original_url": "https://stride-hr.slack.com/archives/C0702497X55/p**********653359", "fallback": "[August 13th, 2024 3:33 PM] amanda: Quick follow up from our call yesterday; we are working on getting the Rippling integration set up. It will require 'Rippling App Management', which allows access to the app necessary to make the connection. Will this be feasible on your end?\n\nIn the meantime you can do a full account reset using the CSV templates. Let me know if you have any questions on that or run into any issues!", "text": "Quick follow up from our call yesterday; we are working on getting the Rippling integration set up. It will require 'Rippling App Management', which allows access to the app necessary to make the connection. Will this be feasible on your end?\n\nIn the meantime you can do a full account reset using the CSV templates. Let me know if you have any questions on that or run into any issues!", "author_name": "<PERSON>", "author_link": "https://stride-hr.slack.com/team/U07EJ2LP44S", "author_icon": "https://avatars.slack-edge.com/2024-07-30/7497943231509_d34fe1f848690e857cca_48.png", "author_subname": "<PERSON>", "mrkdwn_in": ["text"], "footer": "Slack Conversation"}], "blocks": [{"type": "rich_text", "block_id": "I8xuX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " sorry I should have updated. We have disabled reset entirely for non stride admin users.\n"}, {"type": "link", "url": "https://stride-hr.slack.com/archives/C0702497X55/p**********653359", "text": "https://stride-hr.slack.com/archives/C0702497X55/p**********653359"}]}]}]}, {"ts": "**********.453869", "text": "<@U04DKEFP1K8> Could you please help me create a <https://docs.aws.amazon.com/transfer/latest/userguide/create-server-sftp.html|SFTP server on AWS>?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724188121.586739", "subtype": "thread_broadcast", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "zJsii", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Could you please help me create a "}, {"type": "link", "url": "https://docs.aws.amazon.com/transfer/latest/userguide/create-server-sftp.html", "text": "SFTP server on AWS"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1724318481.479989", "text": "Agenda for today:\n• Review and finalize the target list of work until end of September.\n• How to approach ambiguous/unclear requirements - me and <@U04DKEFP1K8> have discussed on this already.\n• Discuss a bit about support for part time employees in the product\n• COLA feature release", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724318481.479989", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "1724319006.000000"}, "blocks": [{"type": "rich_text", "block_id": "gBLkX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Review and finalize the target list of work until end of September."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "How to approach ambiguous/unclear requirements - me and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " have discussed on this already."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Discuss a bit about support for part time employees in the product"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "COLA feature release"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724335378.025319", "text": "I did a quick mockup of a prioritization spreadsheet yesterday that I'd love to show you all as well.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724318481.479989", "subtype": "thread_broadcast", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "uzZJL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I did a quick mockup of a prioritization spreadsheet yesterday that I'd love to show you all as well."}]}]}]}, {"ts": "1724346473.518559", "text": "<@U07EJ2LP44S> you mentioned that you found some issues during cycle creation in customer call. Did you make a note of those by any chance? ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724346473.518559", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "V5U4r", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " you mentioned that you found some issues during cycle creation in customer call. Did you make a note of those by any chance"}, {"type": "text", "text": "?"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1724354839.783569", "text": "<@U04DS2MBWP4> <@U07EJ2LP44S> is there a place where I can find recordings of customer or internal calls that might have any sort of discussion that would be relevant to the theme of manager enablement? I like to take short walk breaks during the work day, and would love to be able to listen to some calls to start getting immersed in the problem space.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724354839.783569", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "FbTHZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " is there a place where I can find recordings of customer or internal calls that might have any sort of discussion that would be relevant to the theme of manager enablement? I like to take short walk breaks during the work day, and would love to be able to listen to some calls to start getting immersed in the problem space."}]}]}]}, {"ts": "1724355502.510709", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> I don't think we need the customer implementation call today, unless there are outstanding questions", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1724355502.510709", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "NDlNr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I don't think we need the customer implementation call today, unless there are outstanding questions"}]}]}]}, {"ts": "1724359847.308329", "text": "Quick clarification question - for testing out the prioritization spreadsheet on Monday, would you like to EXCLUDE all of the current \"Priority 0\" items and only focus on prioritizing everything after the \"by end of Sept\" push?\n\nI am assuming so because the Priority 0 items most likely have to get done no matter what, but please correct me if I misunderstood.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724359847.308329", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "zin1/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Quick clarification question - for testing out the prioritization spreadsheet on Monday, would you like to EXCLUDE all of the current \"Priority 0\" items and only focus on prioritizing everything after the \"by end of Sept\" push?\n\nI am assuming so because the Priority 0 items most likely have to get done no matter what, but please correct me if I misunderstood."}]}]}]}, {"ts": "1724378565.164089", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> would be great if you could add me as optional to every customer call. This is just for my awareness so that I can follow up on the same and also would like listen to every customer call if you could record and share it. Please let me know if the calls are already being recorded and stored somewhere in any form.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724378690.000000"}, "blocks": [{"type": "rich_text", "block_id": "OzYTK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " would be great if you could add me as optional to every customer call. This is just for my awareness so that I can follow up on the same and also would like listen to every customer call if you could record and share it. Please let me know if the calls are already being recorded and stored somewhere in any form."}]}]}]}, {"ts": "1724393311.658739", "text": "Agenda for today\n• Queries on CWA SFTP Integration\n• Issues faced by <@U07EJ2LP44S> with data upload\n• Support CAD as base currency. Should we support base currency for org and cycle separately? Which is not recommended IMO.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724393311.658739", "reply_count": 2, "edited": {"user": "U0690EB5JE5", "ts": "1724408685.000000"}, "blocks": [{"type": "rich_text", "block_id": "IIQ/f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Queries on CWA SFTP Integration"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Issues faced by "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " with data upload"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Support CAD as base currency. Should we support base currency for org and cycle separately? Which is not recommended IMO."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724426818.862589", "text": "I forgot to confirm; did we get the requested fields in HiBob for valgenesis? Or do we need to communicate that some are missing? <@U04DKEFP1K8>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1724426818.862589", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "VPdWq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I forgot to confirm; did we get the requested fields in HiBob for valgenesis? Or do we need to communicate that some are missing? "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1724434275.072249", "text": "<PERSON><PERSON><PERSON><PERSON> just emailed; they have been acquired by a much larger org - Digicert - they appear to be ~1500 people", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "tiUVB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON> just emailed; they have been acquired by a much larger org - Digicert - they appear to be ~1500 people"}]}]}]}, {"ts": "1724435822.969229", "text": "<@U07EJ2LP44S> lets plan to ask larger customers like valgenesis who plan to use role like HRBP if they will need any explicit data permission. Any requirements will need larger implementation time.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724435822.969229", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "prH5r", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " lets plan to ask larger customers like valgenesis who plan to use role like HRBP if they will need any explicit data permission. Any requirements will need larger implementation time."}]}]}]}, {"ts": "1724450209.616499", "text": "*Monday Prioritization POC Agenda*\n\nI updated my spreadsheet and would love to review the following with you all on Monday:\n\n1. Discuss an illustration of how your prioritization criteria and their weights may/should change at different company stages, and what types of success criteria and metrics to use at each stage based on startup benchmarks \n2. Go through a live prioritization exercise for the \"MUST DO NOW\" P0 and P1 items I pulled over from <PERSON><PERSON><PERSON>'s <https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=1270401060#gid=1270401060|\"Latest Priorities\" tab> _(I no longer see P2 in there so just let me know if you  want me to add those)_\n3. Go through a live prioritization exercise for items I pulled from the <https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=0#gid=0|\"All Projects\" tab> \n4. Discuss any potential changes to the weights and formula _(as this is a completely custom formula for Stride's specific situation/stage, I expect to need to make some minor adjustments after we go through this POC and see how things shake out)_", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724450209.616499", "reply_count": 1, "edited": {"user": "U07HCJ07H7G", "ts": "1724450381.000000"}, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "OF3w9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Monday Prioritization POC Agenda", "style": {"bold": true}}, {"type": "text", "text": "\n\nI updated my spreadsheet and would love to review the following with you all on Monday:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Discuss an illustration of how your prioritization criteria and their weights may/should change at different company stages, and what types of success criteria and metrics to use at each stage based on startup benchmarks "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Go through a live prioritization exercise for the \"MUST DO NOW\" P0 and P1 items I pulled over from <PERSON><PERSON><PERSON>'s "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=1270401060#gid=1270401060", "text": "\"Latest Priorities\" tab"}, {"type": "text", "text": " "}, {"type": "text", "text": "(I no longer see <PERSON><PERSON> in there so just let me know if you  want me to add those)", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Go through a live prioritization exercise for items I pulled from the "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=0#gid=0", "text": "\"All Projects\" tab"}, {"type": "text", "text": " "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Discuss any potential changes to the weights and formula "}, {"type": "text", "text": "(as this is a completely custom formula for Stride's specific situation/stage, I expect to need to make some minor adjustments after we go through this POC and see how things shake out)", "style": {"italic": true}}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1724450308.201829", "text": "I have the spreadsheet ready and can share it in advance if you'd like, but it may be less overwhelming if I take you through it step by step in our call (it's actually pretty simple but may look like a lot initially if you look at all of its tabs at once, without the context I'm going to share)", "user": "U07HCJ07H7G", "type": "message", "edited": {"user": "U07HCJ07H7G", "ts": "1724450499.000000"}, "blocks": [{"type": "rich_text", "block_id": "niSg0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have the spreadsheet ready and can share it in advance if you'd like, but it may be less overwhelming if I take you through it step by step in our call (it's actually pretty simple but may look like a lot initially if you look at all of its tabs at once, without the context I'm going to share)"}]}]}]}, {"ts": "1724651360.977579", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> Based on our experience with Adjustment letter work we did for Nauto, This is very time consuming and can have lot of scenarios based on each customer. Would be great if you could let us know if there are any customer in this year planning to use adjustment letters. Also, we should try to understand how each customer generate adjustment letters today which will help us be ready for future.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724651452.000000"}, "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "+rlom", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Based on our experience with Adjustment letter work we did for Nauto, This is very time consuming and can have lot of scenarios based on each customer. Would be great if you could let us know if there are any customer in this year planning to use adjustment letters. Also, we should try to understand how each customer generate adjustment letters today which will help us be ready for future."}]}]}]}, {"ts": "1724651869.819389", "text": "Agenda for today:\n• Adjustment letters\n• Prioritization process by Vicky", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "z3esb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Adjustment letters"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Prioritization process by <PERSON>"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724652320.215309", "text": "<@U04DS2MBWP4> Would be great if you could add your inputs to this ticket\n<https://compiify.atlassian.net/browse/COM-3532>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724652320.215309", "reply_count": 1, "reactions": [{"name": "done", "users": ["U04DS2MBWP4"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13593::2d0e9780637111efb7238d34919ef61b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3532?atlOrigin=eyJpIjoiYjRkZjYxODkwOGNlNDk4Mzk3ZTllMDRhYzc0ZTI0ZGIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3532 Cycle Builder Usability enhancements>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13593::2d0e9782637111efb7238d34919ef61b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/082e10750208b91e38660f50da682c58?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13593::2d0e9781637111efb7238d34919ef61b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13593\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13593\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3532", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "cpEgt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Would be great if you could add your inputs to this ticket\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3532"}]}]}]}, {"ts": "1724665990.335549", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> Very sorry I accidentally clicked disconnect while I was working on a fix for Valgensis data and the connection is invalidated. We need to request customer to connect again to sync data if required. The button won't prompt unfortunately in our integrations page UI, Will add a prompt.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "QT32s", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Very sorry I accidentally clicked disconnect while I was working on a fix for Valgensis data and the connection is invalidated. We need to request customer to connect again to sync data if required. The button won't prompt unfortunately in our integrations page UI, Will add a prompt."}]}]}]}, {"ts": "1724693031.251609", "text": "I shared a copy of the prioritization spreadsheet with you all - please let me know if you have any questions! <@U07EJ2LP44S> and <@U04DKEFP1K8>, feel free to go ahead and rank all the columns in Tabs 1 and 2. Tab 2 has additional prioritization criteria related to sales. It's totally ok to take wild guesses on the sales impact - it's still a useful exercise as you start to think about prioritizing beyond these first customers, even if you don't have this info right now since there is no sales person.\n\nLooking forward to discussing the results tomorrow.\n\n<https://docs.google.com/spreadsheets/d/1du9AeHuDyWtaTiwr4Cvb5f2j-FUYk1CbBMBmbag591c/edit?gid=1207079711#gid=1207079711>", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724693031.251609", "reply_count": 1, "files": [{"id": "F07JMM6V5PW", "created": 1724693034, "timestamp": 1724693034, "name": "COPY: Stride Prioritization Proof of Concept (WIP)", "title": "COPY: Stride Prioritization Proof of Concept (WIP)", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U07HCJ07H7G", "user_team": "T04DM97F1UM", "editable": false, "size": 39644, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1du9AeHuDyWtaTiwr4Cvb5f2j-FUYk1CbBMBmbag591c", "external_url": "https://docs.google.com/spreadsheets/d/1du9AeHuDyWtaTiwr4Cvb5f2j-FUYk1CbBMBmbag591c/edit?gid=1207079711#gid=1207079711", "url_private": "https://docs.google.com/spreadsheets/d/1du9AeHuDyWtaTiwr4Cvb5f2j-FUYk1CbBMBmbag591c/edit?gid=1207079711#gid=1207079711", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JMM6V5PW-42cf81a257/copy__stride_prioritization_proof_of_concept__wip__1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACG+5fdhWUfVc/1pP3399P8Avk/41IRznFGKQEY80j76f98f/Xpy+ZuG51I9lx/WlAI7d6UDmmAtFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07JMM6V5PW/copy__stride_prioritization_proof_of_concept__wip_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "t2KDB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I shared a copy of the prioritization spreadsheet with you all - please let me know if you have any questions! "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": ", feel free to go ahead and rank all the columns in Tabs 1 and 2. Tab 2 has additional prioritization criteria related to sales. It's totally ok to take wild guesses on the sales impact - it's still a useful exercise as you start to think about prioritizing beyond these first customers, even if you don't have this info right now since there is no sales person.\n\nLooking forward to discussing the results tomorrow.\n\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1du9AeHuDyWtaTiwr4Cvb5f2j-FUYk1CbBMBmbag591c/edit?gid=1207079711#gid=1207079711"}]}]}]}, {"ts": "1724693562.530679", "text": "<!here> Vercara got acquired last week. I don't know what their acquiring company uses for compensation but it's likely that they already have a system in place and the new company might not use us. I am still trying to get more info. Let's not do anymore work on Vercara until we have clarity from them.", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1724693583.000000"}, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "le50q", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Vercara got acquired last week. I don't know what their acquiring company uses for compensation but it's likely that they already have a system in place and the new company might not use us. I am still trying to get more info. Let's not do anymore work on Vercara until we have clarity from them."}]}]}]}, {"ts": "1724693727.008229", "text": "*Manager Enablement Question*\nDo you have any other documents, notes or internal meeting recordings/transcriptions about Stride's ideas related to the concept of manager enablement? (aside from the <https://docs.google.com/document/d/13oJ8EhRUkr_wtHnCXRIjVxoDoq5Jpu9_Fg5NKPln148/edit#heading=h.e045j8nr4rcy|manager comms for adjustment letters concept>, which I already have)", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724693727.008229", "reply_count": 1, "edited": {"user": "U07HCJ07H7G", "ts": "1724693775.000000"}, "blocks": [{"type": "rich_text", "block_id": "pXCGy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Manager Enablement Question", "style": {"bold": true}}, {"type": "text", "text": "\nDo you have any other documents, notes or internal meeting recordings/transcriptions about Stride's ideas related to the concept of manager enablement? (aside from the "}, {"type": "link", "url": "https://docs.google.com/document/d/13oJ8EhRUkr_wtHnCXRIjVxoDoq5Jpu9_Fg5NKPln148/edit#heading=h.e045j8nr4rcy", "text": "manager comms for adjustment letters concept"}, {"type": "text", "text": ", which I already have)"}]}]}]}, {"ts": "1724703603.733609", "text": "<@U04DS2MBWP4> can you fill $ amount in row 8 <https://docs.google.com/spreadsheets/d/1BlWJ2Jq1cmLwSCRekshFyAgVu9cdfhBMcDA5Yiew-Gs/edit?gid=0#gid=0>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724703603.733609", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "tEf81", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " can you fill $ amount in row 8 "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1BlWJ2Jq1cmLwSCRekshFyAgVu9cdfhBMcDA5Yiew-Gs/edit?gid=0#gid=0"}]}]}]}, {"ts": "1724704642.503139", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> based on <PERSON>'s feedback from your earlier meeting, I made the following changes to the spreadsheet:\n\n1. Changed the first criteria from \"what portion of customers does this affect\" to *\"is this a universal need or a custom request\"*. At this stage it is less important whether it's 1, 2 or all customers requesting, and more important whether it's specific to their situation or fairly universal\n2. Added \"*Effort To Ship Estimate IN ADDITION to Dev\"* column - this will now be added to the initial dev estimate in calculating overall scope/priority.\n3. Separated the \"Level 1\" tab into two - the first one is items needed before this year's Sept/Nov cycles, the second one is items needed for the next set of cycles in Q1. You can just start with the first Nov tab for now.\nSo before the meeting tomorrow, if you could go through the 1st tab and select values for Column J, Universal Need vs Custom (<@U07EJ2LP44S>) and Column N - Effort to ship in addition to <PERSON> (<@U04DKEFP1K8>), then we can take a look at the new prioritization values tomorrow.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724704642.503139", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "3+<PERSON>qa", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " based on <PERSON>'s feedback from your earlier meeting, I made the following changes to the spreadsheet:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Changed the first criteria from \"what portion of customers does this affect\" to "}, {"type": "text", "text": "\"is this a universal need or a custom request\"", "style": {"bold": true}}, {"type": "text", "text": ". At this stage it is less important whether it's 1, 2 or all customers requesting, and more important whether it's specific to their situation or fairly universal"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Added \""}, {"type": "text", "text": "Effort To Ship Estimate IN ADDITION to Dev\" ", "style": {"bold": true}}, {"type": "text", "text": "column - this will now be added to the initial dev estimate in calculating overall scope/priority."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Separated the \"Level 1\" tab into two - the first one is items needed before this year's Sept/Nov cycles, the second one is items needed for the next set of cycles in Q1. You can just start with the first Nov tab for now."}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nSo before the meeting tomorrow, if you could go through the 1st tab and select values for Column J, Universal Need vs Custom ("}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": ") and Column N - Effort to ship in addition to Dev ("}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "), then we can take a look at the new prioritization values tomorrow."}]}]}]}, {"ts": "1724709194.797069", "text": "<@U07EJ2LP44S>  we need to check with CWA on how they plan to distribute the COLA adjustments. From what I understand, not all employees may be eligible for COLA, as organizations often use criteria like employment status (full-time, part-time), tenure, and performance to determine eligibility. Currently, we have the capability to set eligibility based on performance-based recommendations, and adjustments can be manually modified if needed. Could you please get details on how CWA intends to approach this?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724709194.797069", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "f5NJX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "  we need to check with CWA on how they plan to distribute the COLA adjustments. From what I understand, not all employees may be eligible for COLA, as organizations often use criteria like employment status (full-time, part-time), tenure, and performance to determine eligibility. Currently, we have the capability to set eligibility based on performance-based recommendations, and adjustments can be manually modified if needed. Could you please get details on how CWA intends to approach this?"}]}]}]}, {"ts": "1724723238.735399", "text": "*Agenda for tomorrow* (in addition to anything you all have)\n1. *Manager enablement* - I've drafted an interactive exercise for us to run through using Mir<PERSON> to kick off the project and align on the different compensation roles, manager types, customer pain points and benefits that we could solve for with this broad theme.\n2. *Prioritization* - I got some great feedback from <PERSON> and adjusted the spreadsheet accordingly. We can discuss the changes if <PERSON> and <PERSON><PERSON><PERSON><PERSON> have a chance to update the new columns before we meet (otherwise we can do this in the call or push the discussion to We<PERSON>)", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724723238.735399", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8", "U04DS2MBWP4", "U0690EB5JE5"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "kJdKy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for tomorrow", "style": {"bold": true}}, {"type": "text", "text": " (in addition to anything you all have)\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Manager enablement", "style": {"bold": true}}, {"type": "text", "text": " - I've drafted an interactive exercise for us to run through using Miro to kick off the project and align on the different compensation roles, manager types, customer pain points and benefits that we could solve for with this broad theme."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Prioritization", "style": {"bold": true}}, {"type": "text", "text": " - I got some great feedback from <PERSON> and adjusted the spreadsheet accordingly. We can discuss the changes if <PERSON> and <PERSON><PERSON><PERSON><PERSON> have a chance to update the new columns before we meet (otherwise we can do this in the call or push the discussion to We<PERSON>)"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1724774247.423269", "text": "<!here> I am bit tired and very sleepy today. Not sure if I will be able to stay for whole 1.5 hrs meeting.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ym5VU", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I am bit tired and very sleepy today. Not sure if I will be able to stay for whole 1.5 hrs meeting."}]}]}]}, {"ts": "1724778313.925969", "text": "<@U07EJ2LP44S> Reg. Active Integrations as of today.\n• Only *Vercara*, *Div Energy* have authenticated. \n• *Valgenesis* connection got invalidated, We need to ask them to re-authenticate. \n• *Alayacare* has not authenticated.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724778880.000000"}, "files": [{"id": "F07JB0JGTK9", "created": 1724778239, "timestamp": 1724778239, "name": "Screenshot 2024-08-27 at 10.33.34 PM.png", "title": "Screenshot 2024-08-27 at 10.33.34 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 107589, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07JB0JGTK9/screenshot_2024-08-27_at_10.33.34___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07JB0JGTK9/download/screenshot_2024-08-27_at_10.33.34___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 209, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 279, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 419, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 465, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 558, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 595, "original_w": 1309, "original_h": 761, "thumb_tiny": "AwAbADDRKgmlAx0o70GgA/GlpKKAFopKKACl70UUAFFFFAAOlIKWigD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07JB0JGTK9/screenshot_2024-08-27_at_10.33.34___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07JB0JGTK9-f45bc219d1", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "mlRQm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Reg. Active Integrations as of today.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Only "}, {"type": "text", "text": "Vercar<PERSON>", "style": {"bold": true}}, {"type": "text", "text": ", "}, {"type": "text", "text": "Div Energy", "style": {"bold": true}}, {"type": "text", "text": " have authenticated. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis", "style": {"bold": true}}, {"type": "text", "text": " connection got invalidated, We need to ask them to re-authenticate. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Alayacare", "style": {"bold": true}}, {"type": "text", "text": " has not authenticated."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724780247.656619", "text": "Thank You <@U07EJ2LP44S> :slightly_smiling_face:", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "zzz", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nGi6Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank You "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1724785556.334349", "text": "<!here> i will OOO of Monday Sep 2 ( labor day)", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "clap", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "s<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " i will OOO of Monday Sep 2 ( labor day)"}]}]}]}, {"ts": "1724785839.309009", "text": "are others planning on working? my kids will be home and would love to spend some time with them", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3xeqK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "are others planning on working? my kids will be home and would love to spend some time with them"}]}]}]}, {"ts": "1724785913.829499", "text": "Let's take off and recharge our batteries over the long weekend plus it's a national holiday.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1724785913.829499", "reply_count": 1, "reactions": [{"name": "heart", "users": ["U07EJ2LP44S", "U07HCJ07H7G", "U04DKEFP1K8"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "6hSrQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let's take off and recharge our batteries over the long weekend plus it's a national holiday."}]}]}]}, {"ts": "1724790241.651709", "text": "Question about the future of Stride's pricing model... is it $8 pepm for all company employees, regardless of usage, or only $8 per Stride user seat? Wondering if there is financial incentive to get more types of users into Stride as part of manager enablement.", "user": "U07HCJ07H7G", "type": "message", "blocks": [{"type": "rich_text", "block_id": "cUrZf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Question about the future of Stride's pricing model... is it $8 pepm for all company employees, regardless of usage, or only $8 per Stride user seat? Wondering if there is financial incentive to get more types of users into Stride as part of manager enablement."}]}]}]}, {"ts": "1724791713.287069", "text": "<@U07EJ2LP44S> but also a good read for everyone\n\n<https://www.novoinsights.com/insights/measure-the-successof-your-annual-compensation-cycle>\n<https://www.aeqium.com/post/how-to-measure-the-effectiveness-of-your-compensation-strategy>\n<https://www.pave.com/blog-posts/3-key-metrics-to-measure-and-report-the-success-of-your-merit-cycle>", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"image_url": "https://www.novoinsights.com/hubfs/Measure%20the%20Success%20of%20Your%20Cycle.png", "image_width": 5000, "image_height": 2613, "image_bytes": 261203, "from_url": "https://www.novoinsights.com/insights/measure-the-successof-your-annual-compensation-cycle", "id": 1, "original_url": "https://www.novoinsights.com/insights/measure-the-successof-your-annual-compensation-cycle", "fallback": "Measure the Success of Your Annual Compensation Cycle", "text": "annual compensation cycle metris", "title": "Measure the Success of Your Annual Compensation Cycle", "title_link": "https://www.novoinsights.com/insights/measure-the-successof-your-annual-compensation-cycle", "service_name": "novoinsights.com"}, {"from_url": "https://www.aeqium.com/post/how-to-measure-the-effectiveness-of-your-compensation-strategy", "id": 2, "original_url": "https://www.aeqium.com/post/how-to-measure-the-effectiveness-of-your-compensation-strategy", "fallback": "How to Measure the Effectiveness of your Compensation Strategy", "text": "Learn how forward-thinking people leaders from Airbnb, Meta, and Impossible Foods measure the efficiency of their compensation strategies.", "title": "How to Measure the Effectiveness of your Compensation Strategy", "title_link": "https://www.aeqium.com/post/how-to-measure-the-effectiveness-of-your-compensation-strategy", "service_name": "aeqium.com"}, {"image_url": "https://cdn.prod.website-files.com/63722fa7f631896277feb5c6/662fd00841adda44173be30d_merit-cycles-01.jpg", "image_width": 800, "image_height": 432, "image_bytes": 56185, "from_url": "https://www.pave.com/blog-posts/3-key-metrics-to-measure-and-report-the-success-of-your-merit-cycle", "id": 3, "original_url": "https://www.pave.com/blog-posts/3-key-metrics-to-measure-and-report-the-success-of-your-merit-cycle", "fallback": "3 Key Metrics to Measure and Report the Success of Your Merit Cycle | Pave | Data-Driven Total Compensation Platform", "text": "Analyzing your merit cycle is a necessary step in the process of delivering an exceptional compensation program.", "title": "3 Key Metrics to Measure and Report the Success of Your Merit Cycle | Pave | Data-Driven Total Compensation Platform", "title_link": "https://www.pave.com/blog-posts/3-key-metrics-to-measure-and-report-the-success-of-your-merit-cycle", "service_name": "pave.com"}], "blocks": [{"type": "rich_text", "block_id": "klu6O", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " but also a good read for everyone\n\n"}, {"type": "link", "url": "https://www.novoinsights.com/insights/measure-the-successof-your-annual-compensation-cycle"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://www.aeqium.com/post/how-to-measure-the-effectiveness-of-your-compensation-strategy"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://www.pave.com/blog-posts/3-key-metrics-to-measure-and-report-the-success-of-your-merit-cycle"}]}]}]}, {"ts": "1724791773.997109", "text": "our ROI calculator\n<https://docs.google.com/spreadsheets/d/1m067Ffb6I4iWHFw8ld-yYR9Z1zVkFv2noaj6htpUzLc/edit?gid=0#gid=0>", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F07JSLXC8PM", "created": 1724791776, "timestamp": 1724791776, "name": "Compiify ROI Calculatorv2", "title": "Compiify ROI Calculatorv2", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1m067Ffb6I4iWHFw8ld-yYR9Z1zVkFv2noaj6htpUzLc", "external_url": "https://docs.google.com/spreadsheets/d/1m067Ffb6I4iWHFw8ld-yYR9Z1zVkFv2noaj6htpUzLc/edit?gid=0#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1m067Ffb6I4iWHFw8ld-yYR9Z1zVkFv2noaj6htpUzLc/edit?gid=0#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JSLXC8PM-89edc3e475/compiify_roi_calculatorv2_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHRJO7AI/GlyR1xTXOGxg/lRuHofyoAdmlpobPY/lTqACiiigCOQ4bqR+FG4f3z+VSUUANH+8T+FOoooAKKKKACiiigAooooAKKKKAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07JSLXC8PM/compiify_roi_calculatorv2", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "zKB+M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "our ROI calculator\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1m067Ffb6I4iWHFw8ld-yYR9Z1zVkFv2noaj6htpUzLc/edit?gid=0#gid=0"}]}]}]}, {"ts": "1724791794.389569", "text": "<@U07EJ2LP44S> :point_up:", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eLf5K", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "point_up", "unicode": "261d-fe0f"}]}]}]}, {"ts": "1724792004.434219", "text": "<@U04DKEFP1K8> what;s the status of putting ROI calculator on the pricing page", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1724792004.434219", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "oSkCs", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what;s the status of putting ROI calculator on the pricing page"}]}]}]}, {"ts": "1724794605.466969", "text": "<@U07EJ2LP44S> I have prepared instruction to be shared with Cainwatters IT team here so we can use SAML for them. I need to sync up with <PERSON><PERSON><PERSON> later in evening once and then we can share these with CWA <https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724794605.466969", "reply_count": 1, "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "kdeNd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have prepared instruction to be shared with Cainwatters IT team here so we can use SAML for them. I need to sync up with <PERSON><PERSON><PERSON> later in evening once and then we can share these with CWA "}, {"type": "link", "url": "https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit"}]}]}]}, {"ts": "1724805821.016369", "text": "<@U04DS2MBWP4> <@U07EJ2LP44S> Here is the initial product brief for Manager Enablement, with recommended customer interview goals and prompts at the end of the doc, in blue.\n\nPlease let me know what questions and feedback you have. I'm happy to connect over <PERSON>m with the two of you to review tomorrow.\n\nI'd love to help with customer interviews! Please let me know if you'd like to connect me with managers and HR folks from our current customer or prospect base. Otherwise, I can reach out to some personal contacts who fit our ICP.\n\n<https://docs.google.com/document/d/1UKkBEvaSakzhgg5ZT4UA5feoXc-9H6Ec0nRtl1jBHiI/edit>", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724805821.016369", "reply_count": 4, "files": [{"id": "F07JTJNMJ6N", "created": 1724805823, "timestamp": 1724805823, "name": "Manager Enablement Product Brief", "title": "Manager Enablement Product Brief &amp; PRD", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07HCJ07H7G", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1UKkBEvaSakzhgg5ZT4UA5feoXc-9H6Ec0nRtl1jBHiI", "external_url": "https://docs.google.com/document/d/1UKkBEvaSakzhgg5ZT4UA5feoXc-9H6Ec0nRtl1jBHiI/edit", "url_private": "https://docs.google.com/document/d/1UKkBEvaSakzhgg5ZT4UA5feoXc-9H6Ec0nRtl1jBHiI/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JTJNMJ6N-014d6025e2/manager_enablement_product_brief_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTRYnIx/Km7n9/++ac4GRkD8aRR6YH0FACbm9/++acpJPOfyxTqKACiiigBjjkUq8cUN0zTQM+v60ASUU3J/wAijJ/yKAHUUUUANbPbP4Ugz15/OlfqKbj/ADigB+T6D86Mn0poH1/KlAAPf8qAHUUUUAf/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07JTJNMJ6N/manager_enablement_product_brief", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "mqkCC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Here is the initial product brief for Manager Enablement, with recommended customer interview goals and prompts at the end of the doc, in blue.\n\nPlease let me know what questions and feedback you have. I'm happy to connect over <PERSON><PERSON> with the two of you to review tomorrow.\n\nI'd love to help with customer interviews! Please let me know if you'd like to connect me with managers and HR folks from our current customer or prospect base. Otherwise, I can reach out to some personal contacts who fit our ICP.\n\n"}, {"type": "link", "url": "https://docs.google.com/document/d/1UKkBEvaSakzhgg5ZT4UA5feoXc-9H6Ec0nRtl1jBHiI/edit"}]}]}]}, {"ts": "1724822415.555999", "text": "Agenda for today:\n• Comp bands for hourly/part time employees\n• New customer requirements from CWA and DegenKolb\n", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724822816.000000"}, "blocks": [{"type": "rich_text", "block_id": "yeBk1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Comp bands for hourly/part time employees"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "New customer requirements from CWA and DegenKolb"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1724853795.976939", "text": "on Valegenesis integrations page:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1724853795.976939", "reply_count": 2, "files": [{"id": "F07JWQ0R0F5", "created": 1724853794, "timestamp": 1724853794, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 29450, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07JWQ0R0F5/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07JWQ0R0F5/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JWQ0R0F5-40c1b64c65/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JWQ0R0F5-40c1b64c65/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JWQ0R0F5-40c1b64c65/image_360.png", "thumb_360_w": 360, "thumb_360_h": 248, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JWQ0R0F5-40c1b64c65/image_480.png", "thumb_480_w": 480, "thumb_480_h": 331, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JWQ0R0F5-40c1b64c65/image_160.png", "original_w": 540, "original_h": 372, "thumb_tiny": "AwAhADDTooPSmGMeZ5mTnGMUALk/3TRuP9w038HFHGOj0AO3H+6aUHJ+6RTMD/bo7dHoAkpmz/bb86fRQAzZ/tt+dKq4/iJ+tOooC4UhO<PERSON>ppaCM9aACiiigAooooAKKKKAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07JWQ0R0F5/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07JWQ0R0F5-d89974ea8a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "U0lMN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "on Valegenesis integrations page:"}]}]}]}, {"ts": "1724854196.871249", "text": "<@U07EJ2LP44S> will take a look ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5tpRa", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " will take a look "}]}]}]}, {"ts": "1724854244.584199", "text": "Thats due to the access tokens we have in the db are invalid. I will fix that", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RIXdO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thats due to the access tokens we have in the db are invalid. I will fix that"}]}]}]}, {"ts": "1724859219.456459", "text": "First call for feedback on closing a cycle: <PERSON><PERSON> with CWA <https://us06web.zoom.us/rec/share/S6fLy_xXJeZn3jwLSm7Y-Gd1KID2V5bXDdoVY5Qc0c9v-_XiCXBpUkjyQmeshFZq.Uwi3RW6FQO86LcnY>\nPasscode: 9+i&amp;xUcN", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "M9CGd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "First call for feedback on closing a cycle: Alyssa with CWA "}, {"type": "link", "url": "https://us06web.zoom.us/rec/share/S6fLy_xXJeZn3jwLSm7Y-Gd1KID2V5bXDdoVY5Qc0c9v-_XiCXBpUkjyQmeshFZq.Uwi3RW6FQO86LcnY"}, {"type": "text", "text": "\nPasscode: 9+i&xUcN"}]}]}]}, {"ts": "1724860767.240129", "text": "<@U0690EB5JE5> can we make this an agenda time on next week Tuesday's eng meeting? We need to double check if all the fixes as mentioned in this doc are complete without bugs, and also discuss plan for TBD items", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722856847.816419", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "ftwkG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we make this an agenda time on next week Tuesday's eng meeting? We need to double check if all the fixes as mentioned in this doc are complete without bugs, and also discuss plan for TBD items"}]}]}]}, {"ts": "1724860800.274209", "text": "Sure", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "3dmZ7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure"}]}]}]}, {"ts": "1724871849.663419", "text": "Action items from today's fireflies recording:\n\n<PERSON>\nTake ownership of restructuring leadership meetings (02:39)\nCreate a new schedule for daily leadership meetings with specific focus areas (09:43)\nValidate SDF issues and go through their feedback document (57:00)\nContinue with manager enablement interviews (57:06)\n\n<PERSON><PERSON>h\nUpdate prioritization spreadsheet with recently created Jira tickets (56:42)\nDemo audit log feature (19:21)\nDemo hourly rate feature (41:13)\n\nSa<PERSON><PERSON>h\nProvide context on SDF feedback to <PERSON> (59:08)\n\nKa<PERSON>l\nMake a decision on hiring sales representatives by early next week (59:19)", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9ZDAA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Action items from today's fireflies recording:\n\n<PERSON>\nTake ownership of restructuring leadership meetings (02:39)\nCreate a new schedule for daily leadership meetings with specific focus areas (09:43)\nValidate SDF issues and go through their feedback document (57:00)\nContinue with manager enablement interviews (57:06)\n\n<PERSON><PERSON>h\nUpdate prioritization spreadsheet with recently created Jira tickets (56:42)\nDemo audit log feature (19:21)\nDemo hourly rate feature (41:13)\n\nSa<PERSON><PERSON>h\nProvide context on SDF feedback to <PERSON> (59:08)\n\nKa<PERSON>l\nMake a decision on hiring sales representatives by early next week (59:19)"}]}]}]}, {"ts": "1724881162.766139", "text": "<!here> common question to all: \"*proration is commonly applied to merit-based salary increases to reflect partial-year work\"*, it’s generally not applied to market adjustments or Cost of living adjustment (COLA) increases. Do you guys 100% agree with this statement?", "user": "U04DKEFP1K8", "type": "message", "edited": {"user": "U04DKEFP1K8", "ts": "1724881186.000000"}, "blocks": [{"type": "rich_text", "block_id": "TQ1eg", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " common question to all: \""}, {"type": "text", "text": "proration is commonly applied to merit-based salary increases to reflect partial-year work\"", "style": {"bold": true}}, {"type": "text", "text": ", it’s generally not applied to market adjustments or Cost of living adjustment (COLA) increases. Do you guys 100% agree with this statement?"}]}]}]}, {"ts": "1724882401.765189", "text": "<!here> here is the feedback from <PERSON> (our product advisor) based on her review of test environment\n<https://docs.google.com/document/d/19v8dloA_TMg8zlGEl-eoWemCqH7Z-NpviSxbdSn2ujk/edit>", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F07KNEQ2B4Y", "created": 1724882407, "timestamp": 1724882407, "name": "Stride Test Site feedback from Ang - August 2024", "title": "Stride Test Site feedback from Ang - August 2024", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 128534, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "19v8dloA_TMg8zlGEl-eoWemCqH7Z-NpviSxbdSn2ujk", "external_url": "https://docs.google.com/document/d/19v8dloA_TMg8zlGEl-eoWemCqH7Z-NpviSxbdSn2ujk/edit", "url_private": "https://docs.google.com/document/d/19v8dloA_TMg8zlGEl-eoWemCqH7Z-NpviSxbdSn2ujk/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1035, "thumb_tiny": "AwAwACXSJxS5pGGabtoAfRSDPvS59qACiiigBrjOOKAWpWx3pARQAvNLSZFICPagB1FFFADXOMcUgx3H608gHrRgUANwv+TS8etLRQAUUUUAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07KNEQ2B4Y/stride_test_site_feedback_from_ang_-_august_2024", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "yaodh", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " here is the feedback from <PERSON> (our product advisor) based on her review of test environment\n"}, {"type": "link", "url": "https://docs.google.com/document/d/19v8dloA_TMg8zlGEl-eoWemCqH7Z-NpviSxbdSn2ujk/edit"}]}]}]}, {"ts": "1724882548.981719", "text": "<@U07HCJ07H7G> <@U07EJ2LP44S> <PERSON> confirmed that other vendors are not working on this manager enablement functionality and managers struggle consistently struggle with the problems we are trying to solve. She thinks it would be a differentiator for us but she also said it's a hard problem to solve as every company will have a different comp philosophy and comp structure. Let me know if you want to interview <PERSON> for this. She is director of total rewards at Drata.", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1724883992.000000"}, "blocks": [{"type": "rich_text", "block_id": "a18uB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07HCJ07H7G"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " <PERSON> confirmed that other vendors are not working on this manager enablement functionality and managers struggle consistently struggle with the problems we are trying to solve. She thinks it would be a differentiator for us but she also said it's a hard problem to solve as every company will have a different comp philosophy and comp structure. Let me know if you want to interview <PERSON> for this. She is director of total rewards at Drata."}]}]}]}, {"ts": "1724889333.336329", "text": "Yes I think she would be great to interview!\n\n<@U04DS2MBWP4> when you say that <PERSON> confirmed that \"managers struggle consistently with the problems we are trying to solve\" - which problems were you discussing when she spoke about this? Manager problems with compensation in general (i.e. difficult to make decisions), or the specific communication support problem?", "user": "U07HCJ07H7G", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Arpvf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes I think she would be great to interview!\n\n"}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " when you say that <PERSON> confirmed that \"managers struggle consistently with the problems we are trying to solve\" - which problems were you discussing when she spoke about this? Manager problems with compensation in general (i.e. difficult to make decisions), or the specific communication support problem?"}]}]}]}, {"ts": "1724889672.591629", "text": "She was talking more about communication, support problem and giving them talking points", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "uAKQR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She was talking more about communication, support problem and giving them talking points"}]}]}]}, {"ts": "1724908236.067389", "text": "<@U07EJ2LP44S>  Please label every jira you create as `Prioritized` those will appear on this <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng Priorities board> for me to triage everyday. Since I am the default assignee, I will add the label in case missed.\ncc: <@U04DKEFP1K8>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724908255.000000"}, "reactions": [{"name": "white_check_mark", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "e3B4q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "  Please label every jira you create as "}, {"type": "text", "text": "Prioritized", "style": {"code": true}}, {"type": "text", "text": " those will appear on this "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng Priorities board"}, {"type": "text", "text": " for me to triage everyday. Since I am the default assignee, I will add the label in case missed.\ncc: "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1724934314.734769", "text": "Trying to update Diversified's Data - adding an additional (fake) employee. Screen gives me the confirmation of changes to two lines, then this:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1724934314.734769", "reply_count": 13, "files": [{"id": "F07K2QZGBH9", "created": 1724934311, "timestamp": 1724934311, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 186079, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07K2QZGBH9/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07K2QZGBH9/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_360.png", "thumb_360_w": 360, "thumb_360_h": 260, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_480.png", "thumb_480_w": 480, "thumb_480_h": 346, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_720.png", "thumb_720_w": 720, "thumb_720_h": 520, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_800.png", "thumb_800_w": 800, "thumb_800_h": 577, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_960.png", "thumb_960_w": 960, "thumb_960_h": 693, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 739, "original_w": 1704, "original_h": 1230, "thumb_tiny": "AwAiADCzgUDGOKXFCj5hQAZ5xQTjrVjav90flSbRn7ox9KAIKAc9KsbV/uj8qrkcmgBD+NKDz0opOaAJPOb0FHmnPQUyigB/nN/dpmcnpQelJj6/nQAtFFFABRRRQAUUUUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07K2QZGBH9/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07K2QZGBH9-dab2abba98", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "8uRgI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Trying to update Diversified's Data - adding an additional (fake) employee. Screen gives me the confirmation of changes to two lines, then this:"}]}]}]}, {"ts": "1724934444.977019", "text": "<@U07EJ2LP44S> this issue is fixed. Please let me know if any other issues.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724853795.976939", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "EPbE5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " this issue is fixed. Please let me know if any other issues."}]}]}]}, {"ts": "1724934950.365199", "text": "Proposed structure for daily meeting: <https://docs.google.com/document/d/1wttp2pB94E4lEF5ZB57HtxZGdw7LI3_jpbcM-l1uVGM/edit?usp=sharing>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1724934950.365199", "reply_count": 7, "files": [{"id": "F07K03Y178D", "created": 1724934951, "timestamp": 1724934951, "name": "Leadership Meeting", "title": "Leadership Meeting", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 58586, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1wttp2pB94E4lEF5ZB57HtxZGdw7LI3_jpbcM-l1uVGM", "external_url": "https://docs.google.com/document/d/1wttp2pB94E4lEF5ZB57HtxZGdw7LI3_jpbcM-l1uVGM/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1wttp2pB94E4lEF5ZB57HtxZGdw7LI3_jpbcM-l1uVGM/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTSJwaTP1/KhqMe1ABn6/lSjPvSfhSj6UALRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQB//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07K03Y178D/leadership_meeting", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "yvx/z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Proposed structure for daily meeting: "}, {"type": "link", "url": "https://docs.google.com/document/d/1wttp2pB94E4lEF5ZB57HtxZGdw7LI3_jpbcM-l1uVGM/edit?usp=sharing"}]}]}]}, {"ts": "1724952509.172769", "text": "<@U04DKEFP1K8> Following enhancements merged till test ENV today.\n• Edit and View Performance rating in Org View\n• Region column in Org View", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724952509.172769", "reply_count": 2, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "pxBfH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Following enhancements merged till test ENV today.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Edit and View Performance rating in Org View"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Region column in Org View"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724980746.399419", "text": "<@U07EJ2LP44S> I have added new jira tickets for Degenkolb here <https://compiify.atlassian.net/browse/COM-3553>.\n<@U0690EB5JE5> please update prioritization sheet with these requirements", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724980746.399419", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13614::daa32c90666d11ef892e27ae126fa8dd", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3553?atlOrigin=eyJpIjoiOWU5MjZiZDAzNTA0NGNjMmE5MTI1MWNkOTU2NjE2ZTkiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3553 Degenkolb UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13614::daa32c92666d11ef892e27ae126fa8dd", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13614::daa32c91666d11ef892e27ae126fa8dd", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13614\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13614\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3553", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "ib0ZM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have added new jira tickets for Degenkolb here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3553"}, {"type": "text", "text": ".\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " please update prioritization sheet with these requirements"}]}]}]}, {"ts": "1725020565.332379", "text": "<@U07EJ2LP44S> We have the fix. Unfortunately there is an unexpected infra issue causing deployment failure and can't deploy the fix today.\n<@U04DKEFP1K8> <http://test.stridehr.io|test.stridehr.io> FE deployed successfully but no BE for some changes. Vadym is working on the infra issues. We are so far unable to figure out the issue as it seems related to docker. Probably you will have to use local setup for QA.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724934314.734769", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1725020622.000000"}, "blocks": [{"type": "rich_text", "block_id": "LaH21", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We have the fix. Unfortunately there is an unexpected infra issue causing deployment failure and can't deploy the fix today.\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": " FE deployed successfully but no BE for some changes. Vadym is working on the infra issues. We are so far unable to figure out the issue as it seems related to docker. Probably you will have to use local setup for QA."}]}]}]}, {"ts": "1725023810.726949", "text": "General question: in what circumstance are flags triggered? Only upon submission? Only when there's a recommendation in place?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1725023810.726949", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "Lf6vH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "General question: in what circumstance are flags triggered? Only upon submission? Only when there's a recommendation in place?"}]}]}]}, {"ts": "1725030761.107409", "text": "<@U04DS2MBWP4> do you have any context on Sonendo before our call today?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yDDQZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " do you have any context on Sonendo before our call today?"}]}]}]}, {"ts": "1725032410.561479", "text": "I'll be following this format for the call today (under implementation call) and see how that flows. <https://stridehr.monday.com/docs/7332651062>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"from_url": "https://stridehr.monday.com/docs/7332651062", "service_icon": "https://cdn.monday.com/apple-touch-icon-120x120.png", "thumb_url": "https://s3.amazonaws.com/general-assets/monday-200x200.png", "thumb_width": 200, "thumb_height": 200, "id": 1, "original_url": "https://stridehr.monday.com/docs/7332651062", "fallback": "Welcome to monday.com | a new way of working", "text": "<http://monday.com|monday.com> Work OS is an open platform where anyone can create the tools they need to run every aspect of their work.", "title": "Welcome to monday.com | a new way of working", "title_link": "https://stridehr.monday.com/docs/7332651062", "service_name": "stridehr.monday.com"}], "blocks": [{"type": "rich_text", "block_id": "XGRQo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'll be following this format for the call today (under implementation call) and see how that flows. "}, {"type": "link", "url": "https://stridehr.monday.com/docs/7332651062"}]}]}]}]}