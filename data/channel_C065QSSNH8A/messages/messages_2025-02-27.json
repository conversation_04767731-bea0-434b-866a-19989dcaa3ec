{"date": "2025-02-27", "channel_id": "C065QSSNH8A", "message_count": 8, "messages": [{"ts": "**********.711599", "text": "<@U0690EB5JE5> These are updated scores from Curana for both accounts. Can you upload them? I am not able to get all of this done in PT hours, the customers are very active", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.711599", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "lBA46", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " These are updated scores from Curana for both accounts. Can you upload them? I am not able to get all of this done in PT hours, the customers are very active"}]}]}]}, {"ts": "**********.886039", "text": "<@U0690EB5JE5> Can you add this employee to the Curana main account?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.886039", "reply_count": 1, "files": [{"id": "F08FQDKNWD7", "created": **********, "timestamp": **********, "name": "AddTrevorRipley.csv", "title": "AddTrevorRipley.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 663, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FQDKNWD7/addtrevorripley.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FQDKNWD7/download/addtrevorripley.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FQDKNWD7/addtrevorripley.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FQDKNWD7-e89e5398bc", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FQDKNWD7/addtrevorripley.csv/edit", "preview": "Employee_Code,Legal_Firstname,Legal_Lastname,Name,Work_Email,Gender,Most_Recent_Hire_Date,Supervisor_Primary_Code,Employee_Status,Hire_Date,EEO1_Ethnicity,Lives-in_State,Position,Department_Desc,Manager_Level,Manager_Level,DOL_Status,Scheduled_Pay_Period_Hours,Emp._Type(1099?),Group_Desc,Pay_Type,Annual_Salary,Annual_Benefits_Base_Rate,TargetBonus,MIPAmount,MIPPercent,Last_Pay_Change,Rate_1\r\n1472,TREVOR,RIPLEY,TREVOR RIPLEY,<EMAIL>,Male,12/20/2021,0248,On Leave,12/20/2021,Black or African American,AZ,Member Services Specialist,Call Center,XXX,None,Full-Time,80,W2,<PERSON><PERSON><PERSON>,<PERSON>ly,\"42,556.80\",\"42,556.80\",,,,03/23/2024,20.46", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Employee_Code</div><div class=\"cm-col\">Legal_Firstname</div><div class=\"cm-col\">Legal_Lastname</div><div class=\"cm-col\">Name</div><div class=\"cm-col\">Work_Email</div><div class=\"cm-col\">Gender</div><div class=\"cm-col\">Most_Recent_Hire_Date</div><div class=\"cm-col\">Supervisor_Primary_Code</div><div class=\"cm-col\">Employee_Status</div><div class=\"cm-col\">Hire_Date</div><div class=\"cm-col\">EEO1_Ethnicity</div><div class=\"cm-col\">Lives-in_State</div><div class=\"cm-col\">Position</div><div class=\"cm-col\">Department_Desc</div><div class=\"cm-col\">Manager_Level</div><div class=\"cm-col\">Manager_Level</div><div class=\"cm-col\">DOL_Status</div><div class=\"cm-col\">Scheduled_Pay_Period_Hours</div><div class=\"cm-col\">Emp._Type(1099?)</div><div class=\"cm-col\">Group_Desc</div><div class=\"cm-col\">Pay_Type</div><div class=\"cm-col\">Annual_Salary</div><div class=\"cm-col\">Annual_Benefits_Base_Rate</div><div class=\"cm-col\">TargetBonus</div><div class=\"cm-col\">MIPAmount</div><div class=\"cm-col\">MIPPercent</div><div class=\"cm-col\">Last_Pay_Change</div><div class=\"cm-col\">Rate_1</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">1472</div><div class=\"cm-col\">TREVOR</div><div class=\"cm-col\">RIPLEY</div><div class=\"cm-col\">TREVOR RIPLEY</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">Male</div><div class=\"cm-col\">12/20/2021</div><div class=\"cm-col cm-num\">0248</div><div class=\"cm-col\">On Leave</div><div class=\"cm-col\">12/20/2021</div><div class=\"cm-col\">Black or African American</div><div class=\"cm-col\">AZ</div><div class=\"cm-col\">Member Services Specialist</div><div class=\"cm-col\">Call Center</div><div class=\"cm-col\">XXX</div><div class=\"cm-col\">None</div><div class=\"cm-col\">Full-Time</div><div class=\"cm-col cm-num\">80</div><div class=\"cm-col\">W2</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">Hourly</div><div class=\"cm-col\">42,556.80</div><div class=\"cm-col\">42,556.80</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">03/23/2024</div><div class=\"cm-col cm-num\">20.46</div></div></div>\n</div>\n", "lines": 2, "lines_more": 0, "preview_is_truncated": false, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "OQl9Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you add this employee to the Curana main account?"}]}]}]}, {"ts": "**********.597679", "text": "<@U0690EB5JE5> Is it possible to re-export the Valgenesis letters with the EEID in the name of the file? So for example 'EE ID - Name - Merit FY2025'? They are going to be uploading these into HiBob and they anticipated an issue with the names only.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.597679", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "gj1cd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Is it possible to re-export the Valgenesis letters with the EEID in the name of the file? So for example 'EE ID - Name - Merit FY2025'? They are going to be uploading these into HiBob and they anticipated an issue with the names only."}]}]}]}, {"ts": "**********.648159", "text": "Thank you!", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ye0AT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you!"}]}]}]}, {"ts": "1740673162.054169", "text": "<@U07EJ2LP44S> All the feedback is taken care.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "2u3dj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " All the feedback is taken care."}]}]}]}, {"ts": "1740672852.661089", "text": "<@U0690EB5JE5> Can you upload these band changes to Tithely", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740672852.661089", "reply_count": 7, "files": [{"id": "F08FQ0L64SD", "created": 1740672850, "timestamp": 1740672850, "name": "TithelyBandsUpdate.csv", "title": "TithelyBandsUpdate.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 4443, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FQ0L64SD/tithelybandsupdate.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FQ0L64SD/download/tithelybandsupdate.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FQ0L64SD/tithelybandsupdate.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FQ0L64SD-942041cb2b", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FQ0L64SD/tithelybandsupdate.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Band Id,Effective Start Date,Effective End Date,Compensation Grade,Location (Country),Location (Region),Team,Department,Job Family,Job Subfamily,Description,Tier,Job Category,Job Level,Job Title,Frequency,Salary Currency,Salary Min Amount,Salary Target Amount,Salary Max Amount,Variable Pay Currency,Variable Pay Target (%),Variable Pay Target Amount,Equity Currency,Equity Target Amount,Equity Target (unit),Equity Target (%),Min Equity (unit),Max Equity (unit),Equity Participation Rate (%),OTE Min Amount,OTE Target Amount,OTE Max Amount,Pay Mix\r\nU,32,01/01/2021,,,US,,,Engineering,Engineering,,,0,Mgr,0,Director of Engineering & Product Dev Operations,Annual,USD,163000,211900,244500,,0,0,,0,0,0,0,0,0,0,0,0,\r\nU,33,01/01/2021,,,US,,,Engineering,Engineering,,,0,Mgr,0,Engineering Manager I,Annual,USD,125154,162700,187731,,0,0,,0,0,0,0,0,0,0,0,0,\r\nU,34,01/01/2021,,,US,,,Engineering,Engineering,,,0,Mgr,0,Engineering Manager II,Annual,USD,129692,168600,194538,,0,0,,0,0,0,0,0,0,0,0,0,\r\nU,35,01/01...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Band Id</div><div class=\"cm-col\">Effective Start Date</div><div class=\"cm-col\">Effective End Date</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Location (Country)</div><div class=\"cm-col\">Location (Region)</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">Job Subfamily</div><div class=\"cm-col\">Description</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Frequency</div><div class=\"cm-col\">Salary Currency</div><div class=\"cm-col\">Salary Min Amount</div><div class=\"cm-col\">Salary Target Amount</div><div class=\"cm-col\">Salary Max Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay Target (%)</div><div class=\"cm-col\">Variable Pay Target Amount</div><div class=\"cm-col\">Equity Currency</div><div class=\"cm-col\">Equity Target Amount</div><div class=\"cm-col\">Equity Target (unit)</div><div class=\"cm-col\">Equity Target (%)</div><div class=\"cm-col\">Min Equity (unit)</div><div class=\"cm-col\">Max Equity (unit)</div><div class=\"cm-col\">Equity Participation Rate (%)</div><div class=\"cm-col\">OTE Min Amount</div><div class=\"cm-col\">OTE Target Amount</div><div class=\"cm-col\">OTE Max Amount</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">32</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Mgr</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Director of Engineering &amp; Product Dev Operations</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">163000</div><div class=\"cm-col cm-num\">211900</div><div class=\"cm-col cm-num\">244500</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">33</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Mgr</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Engineering Manager I</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">125154</div><div class=\"cm-col cm-num\">162700</div><div class=\"cm-col cm-num\">187731</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">34</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Mgr</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Engineering Manager II</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">129692</div><div class=\"cm-col cm-num\">168600</div><div class=\"cm-col cm-num\">194538</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">35</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Jr. QA Engineer</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">52846</div><div class=\"cm-col cm-num\">68700</div><div class=\"cm-col cm-num\">79269</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">36</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Principal Software Engineer</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">137077</div><div class=\"cm-col cm-num\">178200</div><div class=\"cm-col cm-num\">205615</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">37</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">QA Engineer</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">70462</div><div class=\"cm-col cm-num\">91600</div><div class=\"cm-col cm-num\">105692</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">38</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Mgr</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Quality Assurance Testing Manager</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">114154</div><div class=\"cm-col cm-num\">148400</div><div class=\"cm-col cm-num\">171231</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">39</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Senior Software Engineer I</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">113154</div><div class=\"cm-col cm-num\">147100</div><div class=\"cm-col cm-num\">169731</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Senior Software Engineer II</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">131615</div><div class=\"cm-col cm-num\">171100</div><div class=\"cm-col cm-num\">197423</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 29, "lines_more": 24, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Q//ZL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you upload these band changes to Tithely"}]}]}]}, {"ts": "1740656074.781259", "text": "<@U07EJ2LP44S> Letters post addressing feedback.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740580446.530429", "subtype": "thread_broadcast", "files": [{"id": "F08F3F30UMC", "created": 1740656066, "timestamp": 1740656066, "name": "VG_Adjustment_letters.zip", "title": "VG_Adjustment_letters.zip", "mimetype": "application/zip", "filetype": "zip", "pretty_type": "Zip", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 27315908, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F3F30UMC/vg_adjustment_letters.zip", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F3F30UMC/download/vg_adjustment_letters.zip", "media_display_type": "unknown", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F3F30UMC/vg_adjustment_letters.zip", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F3F30UMC-1f9234efa3", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Letters post addressing feedback."}]}]}]}, {"ts": "1740601677.743189", "text": "<@U0690EB5JE5> <PERSON> is trying to adjust individual performance $ for the executive team so that the total bonus amount is in whole dollars. However, the system won't you let input a number with decimals so she can't get what she needs. For example, <PERSON>'s FINAL bonus award should be 573750.00 But she can't adjust the individual amount to get that number to be round. How can we address this?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740601677.743189", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "9uCGj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON> is trying to adjust individual performance $ for the executive team so that the total bonus amount is in whole dollars. However, the system won't you let input a number with decimals so she can't get what she needs. For example, <PERSON>'s FINAL bonus award should be 573750.00 But she can't adjust the individual amount to get that number to be round. How can we address this?"}]}]}]}], "created_at": "2025-05-22T21:35:34.709056"}