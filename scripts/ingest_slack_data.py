#!/usr/bin/env python
"""
Simple script to ingest all Slack data from the data/ folder.

This script uses the LocalSlackSourceInterface and IngestionService to ingest
all Slack messages from data/channel_C065QSSNH8A/ using the 500-token chunking strategy.

Usage:
    cd multi_source_rag
    python ../scripts/ingest_slack_data.py
"""

import os
import sys
import django
import logging
from datetime import datetime

# Set up Django
sys.path.append('.')
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from apps.ingestion.services.llama_ingestion_service_unified import UnifiedIngestionService
from apps.documents.interfaces.local_slack import LocalSlackSourceInterface

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Run full Slack data ingestion."""
    print("🚀 STARTING SLACK DATA INGESTION")
    print("=" * 50)
    
    start_time = datetime.now()
    
    try:
        # Configure LocalSlackSourceInterface
        slack_config = {
            "data_dir": "../data/",
            "channel": "C065QSSNH8A",
            "max_tokens": 500,
            "overlap_tokens": 50,
            "time_period": "monthly",
            "include_threads": True,
            "filter_bots": True
        }
        
        print(f"📂 Data directory: {slack_config['data_dir']}")
        print(f"📺 Channel: {slack_config['channel']}")
        print(f"🔢 Max tokens per document: {slack_config['max_tokens']}")
        
        # Initialize LocalSlackSourceInterface
        print("\n📡 Initializing LocalSlackSourceInterface...")
        slack_interface = LocalSlackSourceInterface(slack_config)
        
        # Validate configuration
        if not slack_interface.validate_config():
            print("❌ Configuration validation failed")
            return False
        
        # Get data info
        info = slack_interface.get_staged_data_info()
        print(f"✅ Data validation successful")
        print(f"📊 Found {info.total_messages} messages in {len(info.available_dates)} files")
        print(f"👥 {info.user_count} users, 🧵 {info.thread_count} threads")
        
        # Initialize IngestionService
        print("\n🔧 Initializing IngestionService...")
        ingestion_service = UnifiedIngestionService()
        
        # Configure ingestion parameters
        ingestion_config = {
            "tenant_id": "default",
            "source_type": "slack",
            "source_id": "local_slack_C065QSSNH8A",
            "batch_size": 50,  # Process in batches for better performance
            "enable_chunking": False,  # Skip chunking since we use token-based pre-chunking
            "embedding_model": "local",  # Use local embeddings
            "force_reindex": False  # Set to True to reindex existing data
        }
        
        print(f"🏢 Tenant: {ingestion_config['tenant_id']}")
        print(f"📝 Source: {ingestion_config['source_type']} - {ingestion_config['source_id']}")
        print(f"📦 Batch size: {ingestion_config['batch_size']}")
        
        # Fetch all documents from LocalSlackSourceInterface
        print("\n📄 Fetching documents from LocalSlackSourceInterface...")
        documents = slack_interface.fetch_documents(
            limit=None,  # No limit - fetch all
            days_back=365 * 2  # Last 2 years
        )
        
        print(f"✅ Fetched {len(documents)} documents")
        
        if not documents:
            print("⚠️ No documents to ingest")
            return True
        
        # Analyze documents before ingestion
        token_counts = [doc['metadata']['estimated_tokens'] for doc in documents]
        avg_tokens = sum(token_counts) / len(token_counts)
        max_tokens = max(token_counts)
        min_tokens = min(token_counts)
        
        print(f"\n📈 Document Statistics:")
        print(f"   - Total documents: {len(documents)}")
        print(f"   - Average tokens: {avg_tokens:.1f}")
        print(f"   - Token range: {min_tokens} - {max_tokens}")
        print(f"   - Documents over 500 tokens: {sum(1 for t in token_counts if t > 500)}")
        
        # Start ingestion
        print(f"\n🔄 Starting ingestion...")
        
        result = ingestion_service.ingest_documents(
            documents=documents,
            tenant_id=ingestion_config["tenant_id"],
            source_type=ingestion_config["source_type"],
            source_id=ingestion_config["source_id"],
            batch_size=ingestion_config["batch_size"],
            enable_chunking=ingestion_config["enable_chunking"]
        )
        
        # Report results
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n🎉 INGESTION COMPLETED!")
        print("=" * 30)
        print(f"✅ Status: {result.get('status', 'Unknown')}")
        print(f"📄 Documents processed: {result.get('documents_processed', 0)}")
        print(f"🔗 Chunks created: {result.get('chunks_created', 0)}")
        print(f"⚡ Vectors stored: {result.get('vectors_stored', 0)}")
        print(f"⏱️ Duration: {duration.total_seconds():.1f} seconds")
        
        if result.get('errors'):
            print(f"⚠️ Errors encountered: {len(result['errors'])}")
            for error in result['errors'][:5]:  # Show first 5 errors
                print(f"   - {error}")
        
        print(f"\n✅ Slack data ingestion completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ INGESTION FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
