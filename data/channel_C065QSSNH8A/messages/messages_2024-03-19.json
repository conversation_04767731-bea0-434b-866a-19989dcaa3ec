{"date": "2024-03-19", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1710869506.597329", "text": "<@U065H3M6WJV> resuming the thread again on cookie consent manager, <PERSON><PERSON> consent maanger will be needed for application website only\n<https://compiify.atlassian.net/browse/COM-2532>. I will need your support to be finalize design , requirement for it", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710869506.597329", "reply_count": 36, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12593::90353860e61611ee818ce5792cd1a8e6", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2532?atlOrigin=eyJpIjoiN2M5YjUyNzVkZjliNDAyZWI5NWMwYzgxNzk0NjQyN2IiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2532 Cookie consent manager>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12593::90353862e61611ee818ce5792cd1a8e6", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/712020:cfb94840-2a5e-4f44-9a54-640524aeaa45/9bf71d6c-2eb3-4583-9fc6-290983a77c34/48?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12593::90353861e61611ee818ce5792cd1a8e6", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12593\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12593\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2532", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "8KEi9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " resuming the thread again on cookie consent manager, <PERSON><PERSON> consent maanger will be needed for application website only\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2532"}, {"type": "text", "text": ". I will need your support to be finalize design , requirement for it"}]}]}]}], "created_at": "2025-05-22T21:35:34.608454"}