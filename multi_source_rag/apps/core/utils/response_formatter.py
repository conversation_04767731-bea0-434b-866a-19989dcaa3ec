"""
Response formatting utilities for the Multi-Source RAG system.

This module provides functions for formatting RAG responses to be more human-friendly
and visually appealing.
"""

import logging
import re
from typing import Any, List, Optional

logger = logging.getLogger(__name__)


def apply_query_specific_formatting(response: str, query_text: str, source_nodes: Optional[List[Any]] = None) -> str:
    """
    Apply query-specific formatting based on the type of query.

    Args:
        response: The response text
        query_text: Original query text
        source_nodes: Source nodes for additional context

    Returns:
        str: Formatted response with query-specific structure
    """
    query_lower = query_text.lower()

    # Handle "List issues" queries
    if any(pattern in query_lower for pattern in ["list issues", "list problems", "what issues", "what problems"]):
        return format_issues_list_response(response, query_text, source_nodes)

    # Handle "Summarize issues" queries
    elif any(pattern in query_lower for pattern in ["summarize issues", "summarize problems", "summary of issues"]):
        return format_issues_summary_response(response, query_text, source_nodes)

    # Handle "reported by [person]" queries
    elif "reported by" in query_lower or "mentioned by" in query_lower:
        return format_person_specific_response(response, query_text, source_nodes)

    # Handle "about [topic]" queries
    elif "about" in query_lower and any(topic in query_lower for topic in ["curana", "paycom", "integration"]):
        return format_topic_specific_response(response, query_text, source_nodes)

    return response


def format_issues_list_response(response: str, query_text: str, source_nodes: Optional[List[Any]] = None) -> str:
    """Format response for 'list issues' type queries."""
    # Extract person name if present
    person_match = re.search(r'(?:reported by|mentioned by|from)\s+(\w+)', query_text.lower())
    person = person_match.group(1).title() if person_match else None

    # Create structured header (plain text)
    if person:
        header = f"Issues Reported by {person}:"
    else:
        header = "Issues Found:"

    # Try to extract issues from the response and format as a list
    formatted_response = _extract_and_format_issues(response)

    return f"{header}\n\n{formatted_response}"


def format_issues_summary_response(response: str, query_text: str, source_nodes: Optional[List[Any]] = None) -> str:
    """Format response for 'summarize issues' type queries."""
    # Extract topic if present
    topic_match = re.search(r'(?:about|regarding|with)\s+(\w+)', query_text.lower())
    topic = topic_match.group(1).title() if topic_match else "Issues"

    header = f"Summary of {topic} Issues:"

    # Add summary structure (plain text)
    formatted_response = _add_summary_structure(response)

    return f"{header}\n\n{formatted_response}"


def format_person_specific_response(response: str, query_text: str, source_nodes: Optional[List[Any]] = None) -> str:
    """Format response for person-specific queries."""
    # Extract person name
    person_match = re.search(r'(?:reported by|mentioned by|from)\s+(\w+)', query_text.lower())
    person = person_match.group(1).title() if person_match else "Person"

    header = f"Information from {person}:"

    # Add person context (plain text)
    formatted_response = _add_person_context(response, person)

    return f"{header}\n\n{formatted_response}"


def format_topic_specific_response(response: str, query_text: str, source_nodes: Optional[List[Any]] = None) -> str:
    """Format response for topic-specific queries."""
    # Extract topic
    topic_match = re.search(r'(?:about|regarding|with)\s+(\w+)', query_text.lower())
    topic = topic_match.group(1).title() if topic_match else "Topic"

    header = f"Information about {topic}:"

    # Add topic structure (plain text)
    formatted_response = _add_topic_structure(response, topic)

    return f"{header}\n\n{formatted_response}"


def _extract_and_format_issues(response: str) -> str:
    """
    Extract issues from response and format as a structured list.

    NOTE: If the response already contains detailed formatting from enhanced prompt templates,
    preserve it instead of breaking it down into simple sentences.
    """
    # Check if the response already has detailed formatting from enhanced prompt templates
    has_detailed_formatting = (
        "•" in response and  # Has bullet points
        len(response) > 300 and  # Is reasonably detailed
        ("Issues reported by" in response or "Issues Found:" in response)  # Has proper headers
    )

    if has_detailed_formatting:
        # The enhanced prompt template already provided good formatting, don't override it
        return response

    # Only apply legacy formatting if the response lacks detailed structure
    # Split response into sentences/paragraphs
    sentences = re.split(r'[.!?]\s+', response)

    issues = []
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence and any(keyword in sentence.lower() for keyword in
                          ['issue', 'problem', 'error', 'bug', 'fail', 'unable', 'cannot', 'difficulty']):
            # Clean up the sentence
            if not sentence.endswith(('.', '!', '?')):
                sentence += '.'
            issues.append(sentence)

    if issues:
        formatted_issues = '\n'.join([f"• {issue}" for issue in issues[:10]])  # Limit to 10 issues
        return formatted_issues
    else:
        return response


def _add_summary_structure(response: str) -> str:
    """Add summary structure to the response."""
    # Try to identify key points and structure them
    paragraphs = response.split('\n\n')

    if len(paragraphs) > 1:
        summary = paragraphs[0]
        details = '\n\n'.join(paragraphs[1:])

        return f"Overview: {summary}\n\nDetails:\n{details}"
    else:
        return response


def _add_person_context(response: str, person: str) -> str:
    """Add person context to the response."""
    return f"Based on messages from {person}:\n\n{response}"


def _add_topic_structure(response: str, topic: str) -> str:
    """Add topic structure to the response."""
    return f"Information about {topic}:\n\n{response}"


def detect_query_type_from_text(query_text: str) -> str:
    """
    Detect query type from the query text.

    Args:
        query_text: The query text

    Returns:
        str: Detected query type
    """
    if not query_text:
        return "general"

    query_lower = query_text.lower()

    # List queries
    if any(pattern in query_lower for pattern in ["list", "show me", "what are"]):
        return "list"

    # Summary queries
    elif any(pattern in query_lower for pattern in ["summarize", "summary", "overview"]):
        return "summary"

    # Person-specific queries
    elif any(pattern in query_lower for pattern in ["reported by", "mentioned by", "from"]):
        return "person"

    # Topic-specific queries
    elif "about" in query_lower:
        return "topic"

    # How-to queries
    elif any(pattern in query_lower for pattern in ["how to", "how do", "steps"]):
        return "procedural"

    return "general"


def format_response(
    response: str,
    output_format: str = "text",
    source_nodes: Optional[List[Any]] = None,
    query_text: Optional[str] = None
) -> str:
    """
    Format a RAG response to be more human-friendly.

    Args:
        response: The raw response from the LLM
        output_format: Output format ('text', 'json', 'markdown', 'table')
        source_nodes: Source nodes for additional context (optional)
        query_text: Original query text for context-aware formatting (optional)

    Returns:
        str: Formatted response
    """
    # Apply a series of formatting improvements
    formatted = response

    # 1. Apply query-specific formatting if query text is provided
    if query_text:
        formatted = apply_query_specific_formatting(formatted, query_text, source_nodes)

    # 2. Improve citation formatting
    formatted = format_citations(formatted)

    # 3. Enhance paragraph structure
    formatted = enhance_paragraphs(formatted)

    # 4. Improve list formatting
    formatted = enhance_lists(formatted)

    # 5. Format code blocks
    formatted = format_code_blocks(formatted)

    # 6. Add emphasis to key points
    formatted = add_emphasis(formatted)

    # 7. Apply humanization for better readability
    query_type = detect_query_type_from_text(query_text) if query_text else None
    formatted = humanize_response(formatted, query_type)

    # Log the formatted response for debugging
    logger.debug(f"Formatted response: {formatted[:200]}...")

    return formatted


def format_citations(response: str) -> str:
    """
    Improve citation formatting in the response for a more polished, professional look.

    Args:
        response: The response text

    Returns:
        str: Response with improved citation formatting (plain text)
    """
    # Clean up the References section if it exists
    references_pattern = (
        r"(\*\*References:\*\*|\bReferences:|\bSources:)(.*?)(?=\n\n|\Z)"
    )

    def clean_references(match):
        refs_header = match.group(1)
        refs_content = match.group(2)

        # Remove chunk IDs and clean up formatting
        cleaned_refs = re.sub(r"Chunk Id: [a-f0-9-]+", "", refs_content)
        cleaned_refs = re.sub(r"Document Id: \d+", "", cleaned_refs)
        cleaned_refs = re.sub(r"Created At: [^,]+,?\s*", "", cleaned_refs)

        # Keep citations simple - just [1], [2], etc.
        cleaned_refs = re.sub(r"\[Document (\d+):", r"[\1]", cleaned_refs)

        return f"{refs_header}\n{cleaned_refs}"

    response = re.sub(references_pattern, clean_references, response, flags=re.DOTALL)

    return response


def enhance_paragraphs(response: str) -> str:
    """
    Enhance paragraph structure for better readability and a more polished appearance.

    Args:
        response: The response text

    Returns:
        str: Response with enhanced paragraph structure (plain text)
    """
    # Ensure paragraphs have proper spacing
    paragraphs = response.split("\n\n")

    # Process each paragraph
    for i in range(len(paragraphs)):
        # Skip list items, code blocks, and references section
        if (
            paragraphs[i]
            and not paragraphs[i].startswith("- ")
            and not paragraphs[i].startswith("* ")
            and not paragraphs[i].startswith("```")
            and not paragraphs[i].startswith("References:")
        ):
            # Clean up the paragraph
            paragraphs[i] = paragraphs[i].strip()

    # Join paragraphs with proper spacing
    return "\n\n".join(paragraphs)


def enhance_lists(response: str) -> str:
    """
    Improve list formatting for better readability.

    Args:
        response: The response text

    Returns:
        str: Response with enhanced list formatting
    """
    # Identify list items
    lines = response.split("\n")
    in_list = False

    for i in range(len(lines)):
        # Check if line is a list item
        if re.match(r"^\s*[-*]\s", lines[i]):
            # This is a list item
            if not in_list:
                # Start of a new list, add spacing before it
                if i > 0 and lines[i - 1].strip():
                    lines[i] = "\n" + lines[i]
                in_list = True
        else:
            # Not a list item
            if in_list and lines[i].strip():
                # End of a list, add spacing after it
                lines[i] = "\n" + lines[i]
            in_list = False

    return "\n".join(lines)


def format_code_blocks(response: str) -> str:
    """
    Format code blocks for better readability.

    Args:
        response: The response text

    Returns:
        str: Response with formatted code blocks
    """
    # Identify code blocks with triple backticks
    code_block_pattern = r"```(?:\w+)?\n(.*?)```"

    def code_block_formatter(match):
        code = match.group(1)
        # Add syntax highlighting class
        return f"```\n{code}```"

    # Format code blocks with regex
    formatted = re.sub(
        code_block_pattern, code_block_formatter, response, flags=re.DOTALL
    )

    # Also format inline code
    inline_code_pattern = r"`([^`]+)`"
    formatted = re.sub(inline_code_pattern, r"`\1`", formatted)

    return formatted


def add_emphasis(response: str) -> str:
    """
    Add emphasis to key points in the response.

    Args:
        response: The response text

    Returns:
        str: Response with added emphasis
    """
    # Identify key phrases to emphasize
    emphasis_phrases = [
        "important to note",
        "key point",
        "critical",
        "essential",
        "significant",
        "best practice",
        "recommended",
    ]

    # Add emphasis to these phrases
    for phrase in emphasis_phrases:
        pattern = re.compile(r"\b" + re.escape(phrase) + r"\b", re.IGNORECASE)
        replacement = r"**\g<0>**"  # Bold the matched phrase
        response = pattern.sub(replacement, response)

    return response


def humanize_response(response: str, query_type: Optional[str] = None) -> str:
    """
    Transform the response into a more conversational, human-friendly format
    with natural language, varied phrasing, and appropriate tone.

    Args:
        response: The response text
        query_type: Type of query (factual, procedural, analytical, code, conceptual, general)

    Returns:
        str: A polished, human-friendly response
    """
    # For now, return the response as-is to avoid HTML formatting issues
    # The enhanced prompts already provide good structure
    return _clean_response_text(response)


def _detect_query_type(response: str) -> str:
    """
    Detect the type of query based on the response content.

    Args:
        response: The response text

    Returns:
        str: Detected query type
    """
    # Look for indicators in the response
    response_lower = response.lower()

    # Check for code blocks
    if "```" in response or "<code>" in response or "function" in response_lower or "class" in response_lower:
        return "code"

    # Check for procedural content
    if any(marker in response_lower for marker in ["step", "steps", "first", "then", "finally", "process", "procedure", "how to"]):
        return "procedural"

    # Check for analytical content
    if any(marker in response_lower for marker in ["analysis", "analyze", "consider", "comparison", "evaluate", "pros and cons"]):
        return "analytical"

    # Check for conceptual content
    if any(marker in response_lower for marker in ["concept", "understand", "principle", "theory", "framework", "idea"]):
        return "conceptual"

    # Check for factual content
    if any(marker in response_lower for marker in ["fact", "data", "information", "according to", "research", "study"]):
        return "factual"

    # Default to general
    return "general"


def _format_no_information_response(response: str) -> str:
    """
    Format a response when no relevant information was found.

    Args:
        response: The original "no information" response

    Returns:
        str: A more helpful and user-friendly "no information" response
    """
    import random

    # Extract any specific term that might be mentioned in the response
    term_match = re.search(r"about\s+['\"]?([^'\".,]+)['\"]?", response)
    term = term_match.group(1) if term_match else "your query"

    # Check if there's a fallback explanation in the response
    has_fallback = "general explanation" in response.lower() or "while i couldn't find" in response.lower()

    if has_fallback:
        # If there's already a fallback explanation, just wrap it nicely
        return f'<div class="humanized-response">{response}</div>'

    # Different ways to acknowledge the lack of information
    acknowledgments = [
        f"<p class='response-intro'>I've searched the available documents, but:</p>",
        f"<p class='response-intro'>After reviewing the available information:</p>",
        f"<p class='response-intro'>I've checked the knowledge base, but:</p>",
    ]

    # Different suggestions for the user
    suggestions = [
        "<div class='enhanced-paragraph'>To get better results, you might want to:</div><ul><li>Try rephrasing your question</li><li>Use more specific terms related to the documents in the system</li><li>Ask about a different but related topic</li></ul>",
        "<div class='enhanced-paragraph'>Here are some options to explore:</div><ul><li>Consider using different keywords in your query</li><li>Try asking about related concepts that might be covered in the documents</li><li>Specify a particular aspect you're interested in</li></ul>",
        "<div class='enhanced-paragraph'>To help me find relevant information:</div><ul><li>Try using terminology that might appear in the documents</li><li>Consider narrowing your question to a specific aspect</li><li>Ask about related topics that might be covered</li></ul>",
    ]

    # Different closing statements
    closings = [
        "<p class='response-closing'>Is there something else I can help you with?</p>",
        "<p class='response-closing'>Would you like to try a different approach to your question?</p>",
        "<p class='response-closing'>Is there a related topic you'd like to explore instead?</p>",
    ]

    # Construct the response
    formatted_response = f"""<div class="humanized-response">
    {random.choice(acknowledgments)}
    <div class="enhanced-paragraph"><strong>I couldn't find specific information about {term} in the available documents.</strong></div>
    {random.choice(suggestions)}
    {random.choice(closings)}
</div>"""

    return formatted_response

def _clean_response_text(response: str) -> str:
    """
    Clean up the response text by removing redundant phrases and formatting issues.

    Args:
        response: The response text

    Returns:
        str: Cleaned response text
    """
    # Remove common LLM prefixes
    prefixes_to_remove = [
        "I'll answer based on the provided context.",
        "Based on the provided context,",
        "According to the provided information,",
        "From the information provided,",
        "Based on the available information:",
        "Here's what I found:",
        "According to the documents:",
    ]

    for prefix in prefixes_to_remove:
        if response.startswith(prefix):
            response = response[len(prefix):].strip()

    # Remove redundant phrases
    redundant_phrases = [
        "I hope this helps!",
        "Let me know if you have any other questions!",
        "Is there anything else you'd like to know?",
        "Let me know if you need any clarification.",
        "I hope this information is helpful!",
        "Let me know if you have any follow-up questions.",
    ]

    for phrase in redundant_phrases:
        response = response.replace(phrase, "")
        response = response.replace(phrase.lower(), "")

    # Clean up any double spaces or excessive newlines
    response = re.sub(r'\s{2,}', ' ', response)
    response = re.sub(r'\n{3,}', '\n\n', response)

    return response.strip()