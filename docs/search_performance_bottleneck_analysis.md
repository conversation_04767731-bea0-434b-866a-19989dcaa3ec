# Search API Performance Bottleneck Analysis

**Date:** January 30, 2025
**Analysis Type:** Django Search API Performance Review
**Current Performance:** ~700+ seconds per query (12+ minutes)

## Executive Summary

The Django Search API is experiencing **CRITICAL** performance issues with search queries taking over 12 minutes to complete. This analysis identifies the major bottlenecks and provides actionable recommendations.

## Performance Test Results

### Observed Metrics
- **Service Initialization Time:**
  - RAGService: 48.6 seconds
  - UnifiedRAGService: 40.0 seconds
- **Single Query Time:** 729.25 seconds (12.15 minutes)
- **LLM API Calls:** 30+ sequential calls to Ollama per query
- **Vector Search Time:** <1 second (fast)
- **Overall Status:** CRITICAL - Unusable for production

## Critical Bottlenecks Identified

### 1. **CRITICAL: Excessive Service Initialization (48.6s)**
**Impact:** High - Every query reinitializes the entire service stack

**Root Causes:**
- Loading 12+ domain-specific embedding models on every request
- Reinitializing LlamaIndex components for each query
- Multiple SentenceTransformer model loads
- No service caching or connection pooling

**Evidence:**
```
Load pretrained SentenceTransformer: all-MiniLM-L6-v2 (x12 times)
Registered domain-specific embedding model for code: sentence-transformers/all-MiniLM-L6-v2
Registered domain-specific embedding model for python: sentence-transformers/all-MiniLM-L6-v2
... (repeated 12 times)
```

### 2. **CRITICAL: Ollama LLM Performance (700+ seconds)**
**Impact:** Extreme - 95%+ of query time spent on LLM calls

**Root Causes:**
- Using local Ollama with Llama3 model (CPU-bound)
- 30+ sequential API calls to Ollama per query
- No LLM response caching
- Inefficient citation generation process

**Evidence:**
```
HTTP Request: POST http://localhost:11434/api/chat (x30+ times)
Search completed in 729.25s with 0 results
```

### 3. **CRITICAL: Redundant Component Initialization**
**Impact:** High - Multiplies initialization overhead

**Root Causes:**
- Service components reinitialize for every query
- No singleton pattern or service caching
- Embedding models reload repeatedly
- Vector store connections recreated

### 4. **WARNING: Inefficient Citation Processing**
**Impact:** Medium - Additional database overhead

**Root Causes:**
- Multiple database queries for citation creation
- No bulk operations for citation insertion
- Redundant chunk lookups

**Evidence:**
```
Created 0 unique citations for search result 693
```

## Architecture Issues

### Current Flow Problems
1. **No Service Persistence:** Services are recreated for each request
2. **No Caching Layer:** No caching for LLM responses, embeddings, or search results
3. **Sequential Processing:** All operations are synchronous and sequential
4. **Resource Inefficiency:** Multiple model loads and connections per request

### LLM Configuration Issues
- **Model Choice:** Llama3 via Ollama is too slow for real-time search
- **API Calls:** Excessive number of LLM calls per query
- **No Streaming:** Blocking calls without streaming responses
- **No Fallback:** No faster LLM fallback options

## Immediate Recommendations (Priority Order)

### 1. **URGENT: Switch to Faster LLM (Expected: 90% improvement)**
```python
# Current: Ollama Llama3 (local, slow)
# Recommended: Gemini Flash (cloud, fast)
GEMINI_API_KEY = "your-api-key"
GEMINI_MODEL = "gemini-1.5-flash"  # Much faster than Llama3
```

### 2. **URGENT: Implement Service Caching (Expected: 80% improvement)**
```python
# Cache initialized services at application level
@lru_cache(maxsize=10)
def get_cached_rag_service(tenant_slug: str, user_id: int):
    return RAGService(user=user, tenant_slug=tenant_slug)
```

### 3. **HIGH: Reduce Embedding Model Loading (Expected: 60% improvement)**
```python
# Load models once at startup, not per request
# Use global model registry with lazy loading
# Reduce from 12 models to 2-3 essential models
```

### 4. **HIGH: Implement Response Caching (Expected: 95% improvement for repeated queries)**
```python
# Cache LLM responses for common queries
# Use Redis or in-memory cache with TTL
# Cache vector search results
```

### 5. **MEDIUM: Optimize Citation Processing (Expected: 20% improvement)**
```python
# Use bulk database operations
# Implement citation caching
# Reduce database queries with select_related/prefetch_related
```

## Technical Implementation Plan

### Phase 1: Emergency Fixes (1-2 days)
1. **Switch to Gemini LLM**
   - Configure Gemini API key
   - Update LLM initialization to prefer Gemini
   - Test with faster model

2. **Implement Basic Service Caching**
   - Add service-level caching
   - Cache embedding models globally
   - Implement connection pooling

### Phase 2: Performance Optimization (3-5 days)
1. **Add Response Caching**
   - Implement Redis caching layer
   - Cache LLM responses with TTL
   - Cache vector search results

2. **Optimize Database Operations**
   - Bulk citation creation
   - Optimize database queries
   - Add database indexes

### Phase 3: Architecture Improvements (1-2 weeks)
1. **Async Processing**
   - Implement async LLM calls
   - Add streaming responses
   - Background citation processing

2. **Advanced Caching**
   - Intelligent cache invalidation
   - Distributed caching
   - Query result optimization

## Expected Performance Improvements

| Optimization | Current Time | Expected Time | Improvement |
|--------------|--------------|---------------|-------------|
| Switch to Gemini | 729s | 73s | 90% |
| Service Caching | 73s | 15s | 80% |
| Response Caching | 15s | 0.5s | 97% |
| **Total Combined** | **729s** | **0.5-15s** | **98-99%** |

## Monitoring Recommendations

1. **Add Performance Metrics**
   - Track component timing
   - Monitor LLM response times
   - Measure cache hit rates

2. **Set Performance Alerts**
   - Alert if query time > 10 seconds
   - Monitor service initialization time
   - Track LLM API failures

3. **Regular Performance Testing**
   - Automated performance regression tests
   - Load testing with realistic queries
   - Performance benchmarking

## Code-Level Bottleneck Analysis

### Specific Performance Issues in Codebase

#### 1. **UnifiedRAGService.__init__() - 40+ seconds**
**File:** `apps/search/services/unified_rag_service.py:71`

**Problem Code:**
```python
def __init__(self, tenant, user):
    # This runs on EVERY query - should be cached
    self._initialize_components()  # 40+ seconds
```

**Bottlenecks:**
- `initialize_embedding_models()` loads 12 models
- `initialize_llms()` connects to Ollama
- `_build_query_engines()` creates 3 engines
- `_build_citation_engine()` initializes citation system

#### 2. **Embedding Model Loading - 30+ seconds**
**File:** `apps/core/utils/llama_index_embeddings.py:50`

**Problem Code:**
```python
def initialize_embedding_models():
    # Loads 12 different embedding models on every request
    for domain in ['code', 'python', 'javascript', 'java', ...]:
        domain_embedding = HuggingFaceEmbedding(
            model_name="sentence-transformers/all-MiniLM-L6-v2",
            embed_batch_size=32,
        )  # Each takes 2-3 seconds to load
```

#### 3. **Citation Engine Query - 700+ seconds**
**File:** `apps/search/services/unified_rag_service.py:301`

**Problem Code:**
```python
def search(self, query_text, ...):
    # This single line takes 700+ seconds
    response = self.citation_engine.query(query_text)
```

**Root Cause:** CitationQueryEngine makes 30+ sequential calls to Ollama LLM

#### 4. **RAGService Initialization Chain**
**File:** `apps/search/services/rag_service.py:52`

**Problem Code:**
```python
def __init__(self, user, tenant_slug):
    # Creates new UnifiedRAGService on every request
    self.unified_service = UnifiedRAGService(self.tenant_slug, user)
```

### LLM Configuration Analysis

#### Current LLM Setup (SLOW)
**File:** `apps/core/utils/llama_index_llm.py:87`

```python
ollama_llm = Ollama(
    model="llama3",  # Large model, CPU-bound
    base_url="http://localhost:11434",  # Local Ollama
    request_timeout=120.0,  # Long timeout
    temperature=0.1,
    context_window=4096,  # Large context
)
```

**Issues:**
- Llama3 is a large model (7B+ parameters)
- Running on CPU via Ollama (no GPU acceleration visible)
- Local processing is slower than cloud APIs
- Large context window increases processing time

#### Gemini Configuration (FAST)
**File:** `apps/core/utils/gemini_llm.py:46`

```python
gemini_llm = Gemini(
    model="gemini-1.5-flash",  # Optimized for speed
    api_key=GEMINI_API_KEY,  # Cloud-based, GPU-accelerated
    temperature=0.1,
    max_tokens=4096,
)
```

**Advantages:**
- Gemini Flash is optimized for speed
- Cloud-based with GPU acceleration
- Google's infrastructure handles scaling
- Much faster response times

### Vector Search Performance (GOOD)
**File:** `apps/core/utils/vectorstore.py:356`

Vector search is actually fast (<1 second):
```
HTTP Request: POST http://localhost:6333/collections/tenant_test-tenant_default/points/search "HTTP/1.1 200 OK"
```

**This is NOT a bottleneck** - Qdrant vector search is performing well.

## Conclusion

The current search API performance is **CRITICAL** and requires immediate attention. The primary bottlenecks are:

1. **Slow LLM (Ollama/Llama3)** - 95% of the problem
2. **No service caching** - Massive overhead
3. **Redundant initialization** - Multiplies delays

**Immediate Action Required:** Switch to Gemini LLM and implement basic caching to achieve 90%+ performance improvement within 1-2 days.

**Target Performance:** <5 seconds per query (currently 729 seconds)
**Priority:** CRITICAL - Production blocking issue
