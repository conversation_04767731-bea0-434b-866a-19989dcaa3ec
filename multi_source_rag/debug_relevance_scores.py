#!/usr/bin/env python
"""
Debug Relevance Score Issue

This script investigates the relevance score filtering issue where:
- Search generates answers but returns 0 documents
- filtered_nodes is empty due to min_relevance_score threshold (0.4)
- Need to check actual scores and adjust accordingly

Author: AI Assistant
Date: 2025-01-30
"""

import os
import sys
import django
import logging
from typing import List, Tuple, Any

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.search.services.rag_service import RAGService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def debug_search_with_scores(query: str, min_relevance_scores: List[float] = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5]):
    """
    Debug search functionality with different relevance score thresholds.
    
    Args:
        query: Search query to test
        min_relevance_scores: List of thresholds to test
    """
    print(f"\n🔍 DEBUGGING SEARCH: '{query}'")
    print("=" * 80)
    
    # Get tenant and user
    tenant = Tenant.objects.get(slug="test-tenant")
    user, _ = User.objects.get_or_create(
        username="test_user",
        defaults={"email": "<EMAIL>", "is_staff": True}
    )
    
    # Create RAG service
    rag_service = RAGService(user=user, tenant_slug=tenant.slug)
    
    for min_score in min_relevance_scores:
        print(f"\n📊 Testing with min_relevance_score = {min_score}")
        print("-" * 50)
        
        try:
            # Perform search
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=10,
                min_relevance_score=min_score,
                output_format="text"
            )
            
            print(f"✅ Search completed successfully")
            print(f"📄 Answer length: {len(search_result.generated_answer)} characters")
            print(f"📋 Retrieved documents: {len(retrieved_docs)}")
            
            # Show document scores
            if retrieved_docs:
                print(f"📈 Document relevance scores:")
                for i, (doc, score) in enumerate(retrieved_docs[:5]):  # Show top 5
                    print(f"   {i+1}. Score: {score:.4f}")
            else:
                print("❌ No documents retrieved (likely filtered out by relevance score)")
                
            # Show answer preview
            answer_preview = search_result.generated_answer[:200] + "..." if len(search_result.generated_answer) > 200 else search_result.generated_answer
            print(f"💬 Answer preview: {answer_preview}")
            
        except Exception as e:
            print(f"❌ Search failed: {str(e)}")
            logger.error(f"Search error with min_score {min_score}: {e}")


def debug_raw_vector_search(query: str):
    """
    Debug raw vector search to see actual similarity scores from Qdrant.
    """
    print(f"\n🔧 RAW VECTOR SEARCH DEBUG: '{query}'")
    print("=" * 80)
    
    try:
        from apps.core.utils.vectorstore import get_qdrant_client
        from apps.core.utils.embeddings import get_embedding_model
        
        # Get Qdrant client and embedding model
        client = get_qdrant_client()
        embedding_model = get_embedding_model()
        
        # Generate query embedding
        query_embedding = embedding_model.embed_query(query)
        
        # Search in Qdrant
        collection_name = "tenant_test-tenant_default"
        search_results = client.search(
            collection_name=collection_name,
            query_vector=query_embedding,
            limit=10
        )
        
        print(f"🎯 Raw Qdrant search results:")
        print(f"📊 Total results: {len(search_results)}")
        
        if search_results:
            print(f"📈 Raw similarity scores:")
            for i, result in enumerate(search_results):
                print(f"   {i+1}. Score: {result.score:.6f} | ID: {result.id}")
                
            # Statistics
            scores = [r.score for r in search_results]
            print(f"\n📊 Score Statistics:")
            print(f"   Max: {max(scores):.6f}")
            print(f"   Min: {min(scores):.6f}")
            print(f"   Avg: {sum(scores)/len(scores):.6f}")
            
            # Check how many would pass different thresholds
            thresholds = [0.1, 0.2, 0.3, 0.4, 0.5]
            print(f"\n🎯 Threshold Analysis:")
            for threshold in thresholds:
                passing = sum(1 for s in scores if s >= threshold)
                print(f"   >= {threshold}: {passing}/{len(scores)} documents")
        else:
            print("❌ No results from raw vector search")
            
    except Exception as e:
        print(f"❌ Raw vector search failed: {str(e)}")
        logger.error(f"Raw vector search error: {e}")


def suggest_optimal_threshold():
    """
    Suggest optimal relevance score threshold based on data analysis.
    """
    print(f"\n💡 THRESHOLD RECOMMENDATIONS")
    print("=" * 80)
    
    # Test queries that should return results
    test_queries = [
        "budget adherence testing",
        "bug reports",
        "manager recommendations", 
        "testing updates",
        "showstopper bugs"
    ]
    
    all_scores = []
    
    for query in test_queries:
        try:
            from apps.core.utils.vectorstore import get_qdrant_client
            from apps.core.utils.embeddings import get_embedding_model
            
            client = get_qdrant_client()
            embedding_model = get_embedding_model()
            query_embedding = embedding_model.embed_query(query)
            
            search_results = client.search(
                collection_name="tenant_test-tenant_default",
                query_vector=query_embedding,
                limit=5
            )
            
            scores = [r.score for r in search_results]
            all_scores.extend(scores)
            print(f"📝 '{query}': {len(scores)} results, max score: {max(scores):.4f}")
            
        except Exception as e:
            print(f"❌ Failed to test query '{query}': {e}")
    
    if all_scores:
        all_scores.sort(reverse=True)
        print(f"\n📊 Overall Score Analysis ({len(all_scores)} total scores):")
        print(f"   Max: {max(all_scores):.6f}")
        print(f"   Min: {min(all_scores):.6f}")
        print(f"   Avg: {sum(all_scores)/len(all_scores):.6f}")
        print(f"   Median: {all_scores[len(all_scores)//2]:.6f}")
        
        # Percentile analysis
        percentiles = [90, 75, 50, 25, 10]
        print(f"\n📈 Percentile Analysis:")
        for p in percentiles:
            idx = int((100-p) / 100 * len(all_scores))
            score = all_scores[min(idx, len(all_scores)-1)]
            print(f"   {p}th percentile: {score:.6f}")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        median_score = all_scores[len(all_scores)//2]
        p75_score = all_scores[int(0.25 * len(all_scores))]
        
        print(f"   🎯 Conservative (75th percentile): {p75_score:.3f}")
        print(f"   ⚖️  Balanced (median): {median_score:.3f}")
        print(f"   🔓 Liberal (25th percentile): {all_scores[int(0.75 * len(all_scores))]:.3f}")


def main():
    """Main function to debug relevance scores."""
    print("🔬 RELEVANCE SCORE DEBUGGING")
    print("=" * 80)
    
    # Test query that should match the ingested Slack data
    test_query = "budget adherence testing"
    
    # 1. Debug search with different thresholds
    debug_search_with_scores(test_query)
    
    # 2. Debug raw vector search
    debug_raw_vector_search(test_query)
    
    # 3. Suggest optimal threshold
    suggest_optimal_threshold()
    
    print(f"\n✅ DEBUGGING COMPLETE")
    print("=" * 80)


if __name__ == "__main__":
    main()
