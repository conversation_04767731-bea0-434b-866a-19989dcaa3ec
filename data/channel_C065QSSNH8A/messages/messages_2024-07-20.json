{"date": "2024-07-20", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1721447603.585839", "text": "To Indvidual ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Zp2uU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "To Indvidual "}]}]}]}, {"ts": "1721447593.045489", "text": "We should find secure way to share credentials ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721447593.045489", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Ap/Hd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We should find secure way to share credentials "}]}]}]}, {"ts": "1721447561.869259", "text": "<@U04DS2MBWP4> we should avoid sharing credentials via email as it’s unsecure and everyone in the email.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7DFKn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " we should avoid sharing credentials via email as "}, {"type": "text", "text": "it’s"}, {"type": "text", "text": " unsecure and everyone in the email."}]}]}]}, {"ts": "1721428347.939479", "text": "do we want to allow them to duplicate old cycle as well as an active cycle?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721428347.939479", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "FvZpb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "do we want to allow them to duplicate old cycle as well as an active cycle?"}]}]}]}, {"ts": "1721423955.425009", "text": "<https://www.figma.com/proto/WOJVdr3bbnBloZT9z6pY9K/Design-file-(Copy)?node-id=6926-41358&amp;t=1vz7Nsg0nLh7fisF-1&amp;scaling=scale-down&amp;content-scaling=fixed&amp;page-id=2201%3A74500&amp;starting-point-node-id=6592%3A97897&amp;show-proto-sidebar=1|https://www.figma.com/proto/WOJVdr3bbnBloZT9z6pY9K/Design-file-(Copy)?node-id=6926-[…]74500&amp;starting-point-node-id=6592%3A97897&amp;show-proto-sidebar=1>", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "kzyF6", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://www.figma.com/proto/WOJVdr3bbnBloZT9z6pY9K/Design-file-(Copy)?node-id=6926-41358&t=1vz7Nsg0nLh7fisF-1&scaling=scale-down&content-scaling=fixed&page-id=2201%3A74500&starting-point-node-id=6592%3A97897&show-proto-sidebar=1", "text": "https://www.figma.com/proto/WOJVdr3bbnBloZT9z6pY9K/Design-file-(Copy)?node-id=6926-[…]74500&starting-point-node-id=6592%3A97897&show-proto-sidebar=1"}]}]}]}, {"ts": "1721423947.685819", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> check out the new comp builder workflow. We still need to make revisions to it but here is the first draft. We can go over this during the next eng call and discuss what we could do to make it better. <PERSON><PERSON> is still working on redesigining the allocation page.\nFew things to consider:\nDo we need any setting regarding OTE and paymix in the comp builder?\nShould we include setting adjustment letters within comp builder or outside of it?\nWe should add include and exclude employees by job titles in the eligibility settings?\nWhat is the purpose of final approver in the comp builder? does it make sense to keep it?\nWe need to add cost of living adjustments?\nDo we need top down and bottom up approach?\nMove uploading promotions to settings>Upload\nDo we need currency convertor separately at org level and cycle level?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721423947.685819", "reply_count": 5, "edited": {"user": "U04DS2MBWP4", "ts": "1721427606.000000"}, "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "HNrlm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " check out the new comp builder workflow. We still need to make revisions to it but here is the first draft. We can go over this during the next eng call and discuss what we could do to make it better. <PERSON><PERSON> is still working on redesigining the allocation page.\nFew things to consider:\nDo we need any setting regarding OTE and paymix in the comp builder?\nShould we include setting adjustment letters within comp builder or outside of it?\nWe should add include and exclude employees by job titles in the eligibility settings?\nWhat is the purpose of final approver in the comp builder? does it make sense to keep it?\nWe need to add cost of living adjustments?\nDo we need top down and bottom up approach?\nMove uploading promotions to settings>Upload\nDo we need currency convertor separately at org level and cycle level?"}]}]}]}], "created_at": "2025-05-22T21:35:34.618257"}