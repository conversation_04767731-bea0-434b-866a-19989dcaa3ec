#!/usr/bin/env python
"""
Debug citation lookup issues by testing the exact vector IDs that are failing.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from apps.documents.models import EmbeddingMetadata, DocumentChunk
from apps.accounts.models import Tenant

def debug_citation_lookup():
    """Debug citation lookup for specific vector IDs."""
    print("🔍 Debugging Citation Lookup")
    print("=" * 50)
    
    # Get stride tenant
    tenant = Tenant.objects.get(slug="stride")
    print(f"Using tenant: {tenant.name} (slug: {tenant.slug})")
    
    # Test the problematic vector IDs from the search output
    test_vector_ids = [
        "834fa0ab-b849-4a09-9f15-52c017ea1597",
        "71fb5251-1858-433e-8b12-5a8d92514761", 
        "b135bf21-553e-4e4b-a338-916baf394b4b",
        "1bf7e0da-8e2e-4207-af3c-5caa637332c8",
        "2fb9390f-c57b-4237-90bc-e3cac4293f49"
    ]
    
    print(f"\n📊 Testing {len(test_vector_ids)} vector IDs...")
    
    for i, vector_id in enumerate(test_vector_ids):
        print(f"\n🔍 Test {i+1}: {vector_id}")
        
        # Test 1: Direct EmbeddingMetadata lookup
        try:
            embedding = EmbeddingMetadata.objects.filter(vector_id=vector_id).first()
            if embedding:
                print(f"   ✅ EmbeddingMetadata found: ID {embedding.id}")
                
                # Test 2: Check the chunk
                chunk = embedding.chunk
                if chunk:
                    print(f"   ✅ Chunk found: ID {chunk.id}")
                    print(f"   📄 Chunk tenant: {chunk.tenant.slug}")
                    print(f"   📄 Chunk text preview: {chunk.text[:100]}...")
                    
                    # Test 3: Check tenant match
                    if chunk.tenant == tenant:
                        print(f"   ✅ Tenant matches: {chunk.tenant.slug}")
                    else:
                        print(f"   ❌ Tenant mismatch: chunk={chunk.tenant.slug}, expected={tenant.slug}")
                        
                else:
                    print(f"   ❌ No chunk linked to embedding")
            else:
                print(f"   ❌ No EmbeddingMetadata found")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            
        # Test 4: Use the exact method from the service
        try:
            chunk = EmbeddingMetadata.get_chunk_by_vector_id(vector_id)
            if chunk:
                print(f"   ✅ get_chunk_by_vector_id() works: Chunk {chunk.id}")
                
                # Check tenant again
                if chunk.tenant == tenant:
                    print(f"   ✅ Tenant check passes")
                else:
                    print(f"   ❌ Tenant check fails: {chunk.tenant.slug} != {tenant.slug}")
            else:
                print(f"   ❌ get_chunk_by_vector_id() returned None")
        except Exception as e:
            print(f"   ❌ get_chunk_by_vector_id() error: {e}")
    
    # Test 5: Check overall data consistency
    print(f"\n📊 Overall Data Consistency Check:")
    
    total_embeddings = EmbeddingMetadata.objects.count()
    stride_chunks = DocumentChunk.objects.filter(tenant=tenant).count()
    stride_embeddings = EmbeddingMetadata.objects.filter(chunk__tenant=tenant).count()
    
    print(f"   Total EmbeddingMetadata records: {total_embeddings}")
    print(f"   Stride tenant chunks: {stride_chunks}")
    print(f"   Stride tenant embeddings: {stride_embeddings}")
    
    if stride_embeddings == stride_chunks:
        print(f"   ✅ All stride chunks have embeddings")
    else:
        print(f"   ⚠️  Mismatch: {stride_chunks} chunks vs {stride_embeddings} embeddings")
    
    # Test 6: Check if there are chunks from other tenants
    other_tenants = Tenant.objects.exclude(slug="stride")
    for other_tenant in other_tenants:
        other_chunks = DocumentChunk.objects.filter(tenant=other_tenant).count()
        if other_chunks > 0:
            print(f"   📊 {other_tenant.slug} tenant has {other_chunks} chunks")
    
    print(f"\n✅ Citation lookup debugging completed!")

if __name__ == "__main__":
    debug_citation_lookup()
