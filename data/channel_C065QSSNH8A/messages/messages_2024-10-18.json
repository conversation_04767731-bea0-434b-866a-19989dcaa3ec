{"date": "2024-10-18", "channel_id": "C065QSSNH8A", "message_count": 15, "messages": [{"ts": "1729272777.740039", "text": "so say i want to kick the whole thing back to a manager and say try again, is there a way to do that?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WGT8R", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "so say i want to kick the whole thing back to a manager and say try again, is there a way to do that?"}]}]}]}, {"ts": "1729272730.931899", "text": "can we 'un' approve a managers previously approved submissions?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729272730.931899", "reply_count": 2, "edited": {"user": "U07EJ2LP44S", "ts": "1729272748.000000"}, "blocks": [{"type": "rich_text", "block_id": "bMaIr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "can we 'un' approve a managers previously approved submissions?"}]}]}]}, {"ts": "1729272114.361129", "text": "loom invite sent <@U07M6QKHUC9>", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "SD59a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "loom invite sent "}, {"type": "user", "user_id": "U07M6QKHUC9"}]}]}]}, {"ts": "1729267475.674219", "text": "Running 5 mnts late", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PAGth", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Running 5 mnts late"}]}]}]}, {"ts": "1729265841.387449", "text": "<@U07M6QKHUC9> Demo ENV is upto date until yesterday.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729182988.035019", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "SMYAX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Demo ENV is upto date until yesterday."}]}]}]}, {"ts": "1729229510.488459", "text": "<@U07NBMXTL1E> <!here> Lets move the sales update to Monday's standup as we need to focus on QA testing during tomorrow's standup.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729229510.488459", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "URQ4e", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " "}, {"type": "broadcast", "range": "here"}, {"type": "text", "text": " Lets move the sales update to Monday's standup as we need to focus on QA testing during tomorrow's standup."}]}]}]}, {"ts": "1729206495.604509", "text": "<@U0690EB5JE5> <https://docs.google.com/spreadsheets/d/1j05ohorYpwG0Li6_nJNeuOm1UxMqSvX7bczHa-4ORsY/edit?gid=0#gid=0|here> are the first wave of results from my merit planning testing and impersonation.  <PERSON> is working on QA for\n• checking data is consistent and correct everywhere\n• paybands\n• cycle and org insights\n• reports\n• org view\n<@U07EJ2LP44S> I would encourage you to make your QA specific and detailed to make it useful for future. We want to go back to those specific issues for next set of QA testing for another customers. Goal is to build a running checklist of scenarios as part of our QA testing guide.  Try to break the system as much as you can :wink:", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729206495.604509", "reply_count": 6, "files": [{"id": "F07SR8P8AMP", "created": 1729183169, "timestamp": 1729183169, "name": "SDF QA Testing", "title": "SDF QA Testing", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1j05ohorYpwG0Li6_nJNeuOm1UxMqSvX7bczHa-4ORsY", "external_url": "https://docs.google.com/spreadsheets/d/1j05ohorYpwG0Li6_nJNeuOm1UxMqSvX7bczHa-4ORsY/edit?gid=0#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1j05ohorYpwG0Li6_nJNeuOm1UxMqSvX7bczHa-4ORsY/edit?gid=0#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSJ5xSEnPX9KXHOaNvOaAAZ9aWiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07SR8P8AMP/sdf_qa_testing", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "A+Sh9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1j05ohorYpwG0Li6_nJNeuOm1UxMqSvX7bczHa-4ORsY/edit?gid=0#gid=0", "text": "here"}, {"type": "text", "text": " are the first wave of results from my merit planning testing and impersonation.  <PERSON> is working on QA for\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "checking data is consistent and correct everywhere"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "paybands"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "cycle and org insights"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "reports"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "org view"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I would encourage you to make your QA specific and detailed to make it useful for future. We want to go back to those specific issues for next set of QA testing for another customers. Goal is to build a running checklist of scenarios as part of our QA testing guide.  Try to break the system as much as you can "}, {"type": "emoji", "name": "wink", "unicode": "1f609"}]}]}]}, {"ts": "1729205708.948089", "text": "Have a 2nd power outage of the day currently ", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Have a 2nd power outage of the day currently "}]}]}]}, {"ts": "1729199759.490529", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> On sdf-test i only see <PERSON><PERSON><PERSON> as a assigned manager ( i just did that for him to cross check a scenario between alayacare and sdf-test) if you are impersonating any manager then assign appropriate role to avoid running into unintended issues", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1729199759.490529", "reply_count": 17, "blocks": [{"type": "rich_text", "block_id": "Dn7n/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " On sdf-test i only see <PERSON><PERSON><PERSON> as a assigned manager ( i just did that for him to cross check a scenario between alayacare and sdf-test) if you are impersonating any manager then assign appropriate role to avoid running into unintended issues"}]}]}]}, {"ts": "1729194930.822049", "text": "<!here> hierarchy issue opened under <https://compiify.atlassian.net/browse/COM-3832>, <https://compiify.atlassian.net/browse/COM-3830> are still unresolved for org view. please log your comments in here if you observe it in sdf-test", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1729194930.822049", "reply_count": 10, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13893::75f7cfbe602148848fa3524380768656", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3832?atlOrigin=eyJpIjoiNmI3MGI1ZDI0NWZkNDIwZmFmNmY0YjIzZGQ5MzRjMGMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3832 <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> report to <PERSON>, while <PERSON> was incorrec…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13893::ae9013faf8d440549be3950001febdc2", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/39b4b364807f203c465b678307a3f359?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13893\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3832\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3832", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:13891::3a5c8a581ef3418f983f01b0cfe301fe", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3830?atlOrigin=eyJpIjoiMThhNGUyYzAzMDNjNDJmMDk0YWNjZGY5OTAxMTc4YjUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3830 Issue: Incorrect Access Level for Manager Anastasia in Alayacare En…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13891::3cad9f04310f4d2e89f080042ad34af7", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/39b4b364807f203c465b678307a3f359?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13891\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3830\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3830", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "PerYq", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " hierarchy issue opened under "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3832"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3830"}, {"type": "text", "text": " are still unresolved for org view. please log your comments in here if you observe it in sdf-test"}]}]}]}, {"ts": "1729192126.275599", "text": "CWA won't give us a reference until they've completed a cycle. Alayacare too, needs to complete the cycle", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qSZmj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "CWA won't give us a reference until they've completed a cycle. Alayacare too, needs to complete the cycle"}]}]}]}, {"ts": "1729192093.487829", "text": "We have <PERSON> at Nauto. <mailto:<EMAIL>|<EMAIL>>", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7GjFj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We have <PERSON> at Nauto. "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}]}]}]}, {"ts": "1729192071.378719", "text": "Not CWA yet", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "CWTa5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Not CWA yet"}]}]}]}, {"ts": "1729191944.217049", "text": "<@U07EJ2LP44S> Could you share the names / info on any referenecable customers we currently have? <PERSON><PERSON><PERSON> is thinking <PERSON><PERSON>, <PERSON>", "user": "U07NBMXTL1E", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GpQni", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Could you share the names / info on any referenecable customers we currently have? <PERSON><PERSON><PERSON> is thinking <PERSON><PERSON>, <PERSON>"}]}]}]}, {"ts": "1729190773.353569", "text": "<@U0690EB5JE5> In the demo env, whenever we try to create a new cycle during sales demo, it gets stuck in the loop for couple of minutes. Can we fix this pls?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729190773.353569", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "3bK1B", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " In the demo env, whenever we try to create a new cycle during sales demo, it gets stuck in the loop for couple of minutes. Can we fix this pls?"}]}]}]}], "created_at": "2025-05-22T21:35:34.646449"}