{"date": "2024-10-29", "channel_id": "C065QSSNH8A", "message_count": 30, "messages": [{"ts": "1730223799.948279", "text": "@here Deployment is complete.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730223024.442759", "subtype": "thread_broadcast", "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "V+8CT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "@here Deployment is complete."}]}]}]}, {"ts": "1730223117.790989", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> Could you please create a ticket for the issue that happened today with details what caused the issue? We will work on adding validation and handle cascading changes to avoid this in future.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730223117.790989", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "4xcQq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Could you please create a ticket for the issue that happened today with details what caused the issue? We will work on adding validation and handle cascading changes to avoid this in future."}]}]}]}, {"ts": "1730223046.109419", "text": "will update once deployment finishes. There shouldn't be any downtime.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+gH6T", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will update once deployment finishes. There shouldn't be any downtime."}]}]}]}, {"ts": "1730223024.442759", "text": "<!here> HRBP changes/bug fixes are being deployed to production.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730223024.442759", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "kE/WV", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " HRBP changes/bug fixes are being deployed to production."}]}]}]}, {"ts": "1730222446.329319", "text": "<@U04DKEFP1K8> can we pls fix that filtering and performance rating issue for the QA env as well?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730222446.329319", "reply_count": 1, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "qTMwN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can we pls fix that filtering and performance rating issue for the QA env as well?"}]}]}]}, {"ts": "1730222384.983409", "text": "ok. are we merging the hrbp as well?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1730222384.983409", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "5JNtM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok. are we merging the hrbp as well?"}]}]}]}, {"ts": "1730222364.065259", "text": "<@U07EJ2LP44S> bug fixes for alayacare are still pending merge fyi. <@U0690EB5JE5> and i need to do a quick sync before i merge them", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "tOgBJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " bug fixes for alayacare are still pending merge fyi. "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " and i need to do a quick sync before i merge them"}]}]}]}, {"ts": "1730221061.336549", "text": "these are the original values", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Bd7//", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "these are the original values"}]}]}]}, {"ts": "1730221048.722839", "text": "<@U06HN8XDC5A> here is the file", "user": "U04DKEFP1K8", "type": "message", "files": [{"id": "F07U07VEHGS", "created": 1730221043, "timestamp": 1730221043, "name": "performance_rating (1).csv", "title": "performance_rating (1).csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": true, "size": 314, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07U07VEHGS/performance_rating__1_.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07U07VEHGS/download/performance_rating__1_.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07U07VEHGS/performance_rating__1_.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07U07VEHGS-bde83a6d4d", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F07U07VEHGS/performance_rating__1_.csv/edit", "preview": "1,,1,(0) Too new to rate,1,f,,0\r\n2,,2,(1) Up or Out,1,f,,1\r\n3,,3,(2) Inconsistent or Misplaced,1,f,,2\r\n4,,4,(3) Acceptable Performer,1,f,,3\r\n5,,5,(4) Coachable,1,f,,4\r\n6,,6,(5) Solid Citizen,1,f,,5\r\n7,,7,(6) Key Contributor,1,f,,6\r\n8,,8,(7) Rising Star,1,f,,7\r\n9,,9,(8) Strong Player,1,f,,8\r\n10,,10,(9) Star,1,f,,9", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">(0) Too new to rate</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">f</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\">(1) Up or Out</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">f</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">1</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">(2) Inconsistent or Misplaced</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">f</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">2</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">(3) Acceptable Performer</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">f</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">3</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">(4) Coachable</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">f</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">4</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">(5) Solid Citizen</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">f</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">5</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">7</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">7</div><div class=\"cm-col\">(6) Key Contributor</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">f</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">6</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\">(7) Rising Star</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">f</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">7</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">9</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">9</div><div class=\"cm-col\">(8) Strong Player</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">f</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">8</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\">(9) Star</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\">f</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">9</div></div></div>\n</div>\n", "lines": 10, "lines_more": 0, "preview_is_truncated": false, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "d7kis", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " here is the file"}]}]}]}, {"ts": "1730221041.623429", "text": "<@U04DKEFP1K8> <@U06HN8XDC5A> <https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YPkip", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1730220181.971639", "text": "<@U06HN8XDC5A> Can you join here? <https://us06web.zoom.us/j/82756495754?pwd=zBKTUPl3S7GCsK9tOF3COS5m5zHd7U.1>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R07TKJP4DSB", "block_id": "JpXpC", "api_decoration_available": false, "call": {"v1": {"id": "R07TKJP4DSB", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1730220182, "active_participants": [], "all_participants": [], "display_id": "827-5649-5754", "join_url": "https://us06web.zoom.us/j/82756495754?pwd=zBKTUPl3S7GCsK9tOF3COS5m5zHd7U.1", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1730308776, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/82756495754?pwd=zBKTUPl3S7GCsK9tOF3COS5m5zHd7U.1", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "tVK7E", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " Can you join here? "}, {"type": "link", "url": "https://us06web.zoom.us/j/82756495754?pwd=zBKTUPl3S7GCsK9tOF3COS5m5zHd7U.1"}]}]}]}, {"ts": "1730217357.017039", "text": "<@U04DKEFP1K8> <@U07M6QKHUC9> <@U07EJ2LP44S> Audit trail is enabled in QA ENV. Please check it out and let us know ", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "0Vuk9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Audit trail is enabled in QA ENV. Please check it out and let us know "}]}]}]}, {"ts": "1730215063.901719", "text": "I am not sure if what we're building for Diversified will fit this use case or not", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1730215063.901719", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "w6oWM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am not sure if what we're building for Diversified will fit this use case or not"}]}]}]}, {"ts": "1730215049.035289", "text": "Would like to talk through <PERSON><PERSON><PERSON>'s bonus use case in todays call if possible (they have a Target Bonus for some employees and a Manager Incentive Plan for others they want to track separately)", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fXl6I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Would like to talk through <PERSON><PERSON><PERSON>'s bonus use case in todays call if possible (they have a Target Bonus for some employees and a Manager Incentive Plan for others they want to track separately)"}]}]}]}, {"ts": "1730199286.736439", "text": "<@U07EJ2LP44S> Did you try the fix suggested in <https://compiify.atlassian.net/browse/COM-3915|COM-3915>\nI'm looking into this issue but also wanted to make sure you're not blocked on this.", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1730199286.736439", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13998::9915187e2b94411ca6e47b668c7da7d1", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3915?atlOrigin=eyJpIjoiOTllYWQ0N2JiY2VmNGYxN2EyMjlhNzNkYTJmNzA3NjUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3915 Issue: IC category missing in CSV download after upload>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13998::adcd27931f1e44da9a066d19542936e4", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/35dd492af25f9dfa74f8be283d9a0bba?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13998\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3915\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3915", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "NhCfV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Did you try the fix suggested in "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3915", "text": "COM-3915"}, {"type": "text", "text": "\nI'm looking into this issue but also wanted to make sure you're not blocked on this."}]}]}]}, {"ts": "**********.288329", "text": "<@U07EJ2LP44S>\n• Synced Tithely data from HRIS and is available on <https://tithely.stridehr.io/> \n• Updated curanahealth as well and fixed the hierarchy issue there as well", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6uNs/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Synced Tithely data from HRIS and is available on "}, {"type": "link", "url": "https://tithely.stridehr.io/"}, {"type": "text", "text": " "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Updated curanahealth as well and fixed the hierarchy issue there as well"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "**********.332059", "text": "<@U07M6QKHUC9> <@U04DKEFP1K8> Is there anything I need to take care of in SDF during my day?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.332059", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "WCpXr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Is there anything I need to take care of in SDF during my day?"}]}]}]}, {"ts": "**********.899599", "text": "<@U04DKEFP1K8> Thanks for catching these data issues with the AlayaCare.\n<@U0690EB5JE5> can we pls get these fixed today?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.899599", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "LY7Bj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Thanks for catching these data issues with the AlayaCare.\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we pls get these fixed today?"}]}]}]}, {"ts": "1730163185.185309", "text": "<!here> are the data issue reported today ( all must fix asap)\n• <https://compiify.atlassian.net/browse/COM-3932>\n• <https://compiify.atlassian.net/browse/COM-3931>\n• <https://compiify.atlassian.net/browse/COM-3930>\n<!here> are the enhancement requested for better user exp ( all are nitpicks)\n<https://compiify.atlassian.net/browse/COM-3925>\n<https://compiify.atlassian.net/browse/COM-3926>\n<https://compiify.atlassian.net/browse/COM-3927>\n<https://compiify.atlassian.net/browse/COM-3928>\n<https://compiify.atlassian.net/browse/COM-3929>\n\n<!here> is hrbp validation video ( part 1) [watch it on 1.25x - i'll drink a redbull next time ;-)]\n<https://www.loom.com/share/c785e9b949d54bb6a29ca3af8f3974b0>\n\n<@U07M6QKHUC9> <@U07EJ2LP44S> sdf-test environment lumen flag has been removed and you both can view equity / validate equity data from previous cycle", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1730163185.185309", "reply_count": 14, "edited": {"user": "U04DKEFP1K8", "ts": "1730163206.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14015::951b17284bf24a8ab0d694f53ff0eb70", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3932?atlOrigin=eyJpIjoiMTg5N2VlZDY4OGY0NDY0Mzg3MWNmMGUwY2E2NzRjNzAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3932 Issue: HRBP Visibility Issue for Annette Hill's Managers>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14015::cb3b4672ee904b89b284558e6c0e339c", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14015\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3932\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3932", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:14014::2a658e00ba3d4b4facaf656ff6d191f6", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3931?atlOrigin=eyJpIjoiMDVmNDkwMThlMjc3NGJiOGFkNGZlOWNkOTAxNmU2NDQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3931 Inaccurate Budget Usage Display for Managers in Budget Use Report>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14014::7bd196c8101149d1a28b66a7ebd21568", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14014\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3931\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3931", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 3, "blocks": [{"type": "section", "block_id": "uf:ih:14013::c7583ded685f48d5bc8c97b8275c26eb", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3930?atlOrigin=eyJpIjoiNzhjZDg2YjcyYTBiNGJkN2JmMGVhMTYxN2Y3NjYzNTAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3930 Issue: Investigate Total Target Cash Calculation Discrepancy for ho…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14013::db440704b2224c30ace393f441b7d48f", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14013\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3930\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3930", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 4, "blocks": [{"type": "section", "block_id": "uf:ih:14008::ca0723ed12f043998da272617420c527", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3925?atlOrigin=eyJpIjoiZGY0MGExMTZkNjk2NGU3ZjkwNTI3M2EzYmI1OGM0YjQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3925 Employee Names Cut Off in Recommenders List>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14008::77850aeb49b24024b7c2a69d114f06a4", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14008\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3925\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3925", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 5, "blocks": [{"type": "section", "block_id": "uf:ih:14009::4bf87a1531fd41d1ba4e1669de52687d", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3926?atlOrigin=eyJpIjoiZGUyMGVlYWY0NDNkNDViZDg4NzM3ZTRhZmY4MTE2NWYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3926 Sort Department Names on X-Axis Alphabetically>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14009::864e4d2c71db44868545908036e756c3", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14009\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3926\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3926", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "pYsMt", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " are the data issue reported today ( all must fix asap)\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3932"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3931"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3930"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "broadcast", "range": "here"}, {"type": "text", "text": " are the enhancement requested for better user exp ( all are nitpicks)\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3925"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3926"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3927"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3928"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3929"}, {"type": "text", "text": "\n\n"}, {"type": "broadcast", "range": "here"}, {"type": "text", "text": " is hrbp validation video ( part 1) [watch it on 1.25x - i'll drink a redbull next time ;-)]\n"}, {"type": "link", "url": "https://www.loom.com/share/c785e9b949d54bb6a29ca3af8f3974b0"}, {"type": "text", "text": "\n\n"}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " sdf-test environment lumen flag has been removed and you both can view equity / validate equity data from previous cycle"}]}]}]}, {"ts": "1730148574.787189", "text": "She said that would be very last minute but I can ask if she knows it yet", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1730148574.787189", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "BYY2G", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She said that would be very last minute but I can ask if she knows it yet"}]}]}]}, {"ts": "1730148561.067819", "text": "i don't believe so", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IAcYE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i don't believe so"}]}]}]}, {"ts": "1730148540.831029", "text": "<@U07EJ2LP44S> have chery<PERSON> signed off on budget amount for salary and bonus ( at org level)?", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rq4V5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " have ch<PERSON><PERSON> signed off on budget amount for salary and bonus ( at org level)?"}]}]}]}, {"ts": "**********.108279", "text": "We need to deliver it to her account tomorrow, she has to train the HRBPs and needs time to review", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gQ7zN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We need to deliver it to her account tomorrow, she has to train the HRBPs and needs time to review"}]}]}]}, {"ts": "**********.872359", "text": "she did emphasize, HRBP should not see their own data! but i think we've covered that", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eL9BN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "she did emphasize, HRBP should not see their own data! but i think we've covered that"}]}]}]}, {"ts": "**********.801199", "text": "we impersonated as annette to make sure her view would still work for her", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "100", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "K4Dxg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we impersonated as annette to make sure her view would still work for her"}]}]}]}, {"ts": "**********.962689", "text": "and likes the flexibility", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.962689", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "KTnxc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "and likes the flexibility"}]}]}]}, {"ts": "1730145803.952049", "text": "she is good with the m3 suggestion", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1730145803.952049", "reply_count": 2, "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8", "U07M6QKHUC9", "U0690EB5JE5"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "TyUVr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "she is good with the m3 suggestion"}]}]}]}, {"ts": "1730145797.928549", "text": "ok i'm done with cheryl", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "partying_face", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Uc8rT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok i'm done with cheryl"}]}]}]}, {"ts": "1730144085.514639", "text": "approver will have entirre organization view as well and they can submit in one attempt all of them", "user": "U04DKEFP1K8", "type": "message", "edited": {"user": "U04DKEFP1K8", "ts": "1730144096.000000"}, "blocks": [{"type": "rich_text", "block_id": "dgHtb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "approver will have entirre organization view as well and they can submit in one attempt all of them"}]}]}]}, {"ts": "1730143979.005169", "text": "just to confirm, <@U04DKEFP1K8>, in the secnario where all the m3s are the lowest level, the approver will still have to go in and approve each team vs just their whole grou", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1BM8N", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "just to confirm, "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": ", in the secnario where all the m3s are the lowest level, the approver will still have to go in and approve each team vs just their whole grou"}]}]}]}], "created_at": "2025-05-22T21:35:34.659414"}