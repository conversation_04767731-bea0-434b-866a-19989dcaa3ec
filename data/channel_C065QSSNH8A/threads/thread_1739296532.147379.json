{"thread_ts": "1739296532.147379", "channel_id": "C065QSSNH8A", "reply_count": 12, "replies": [{"ts": "1739296676.229109", "text": "<@U07EJ2LP44S> We can have hourly rates uploaded but that be will just for display if the type is `Regular`. I can better answer or suggest how they want to treat their hourly employees w.r.t merit adjustments as we have different fields for hourly and regular employees.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739296532.147379", "blocks": [{"type": "rich_text", "block_id": "8PQ0F", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We can have hourly rates uploaded but that be will just for display if the type is "}, {"type": "text", "text": "Regular", "style": {"code": true}}, {"type": "text", "text": ". I can better answer or suggest how they want to treat their hourly employees w.r.t merit adjustments as we have different fields for hourly and regular employees."}]}]}]}, {"ts": "1739297250.886659", "text": "<@U07M6QKHUC9> here", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739296532.147379", "blocks": [{"type": "rich_text", "block_id": "iBTQg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " here"}]}]}]}, {"ts": "1739297293.365509", "text": "<@U07EJ2LP44S> Please let me know how exactly they want to use the product for hourly employees. May be this is a new requirement.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739296532.147379", "blocks": [{"type": "rich_text", "block_id": "GO87q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please let me know how exactly they want to use the product for hourly employees. May be this is a new requirement."}]}]}]}, {"ts": "1739297304.594119", "text": "*kapil*  [9:57 AM]\nI think that’s what we did when we first did the initial data upload. We kept all employees as regular employees but made their pay hourly.\n\n\n\n*<PERSON><PERSON><PERSON>*  [9:59 AM]\n<@U07M6QKHUC9> what you mean by made their pay hourly? Are your referring to employment type or compensation type?\n\n\n\n*kapil*  [10:06 AM]\nwe had an hourly rate associated for them. We kept the emp as FT employees under the time type (FT/PT) column. I don't remember what we did for the compensation type as it was back in Novemeber", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1739296532.147379", "blocks": [{"type": "rich_text", "block_id": "qg/co", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "kapil", "style": {"bold": true}}, {"type": "text", "text": "  [9:57 AM]\nI think that’s what we did when we first did the initial data upload. We kept all employees as regular employees but made their pay hourly.\n\n\n\n"}, {"type": "text", "text": "<PERSON><PERSON><PERSON>", "style": {"bold": true}}, {"type": "text", "text": "  [9:59 AM]\n"}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " what you mean by made their pay hourly? Are your referring to employment type or compensation type?\n\n\n\n"}, {"type": "text", "text": "kapil", "style": {"bold": true}}, {"type": "text", "text": "  [10:06 AM]\nwe had an hourly rate associated for them. We kept the emp as FT employees under the time type (FT/PT) column. I don't remember what we did for the compensation type as it was back in Novemeber"}]}]}]}, {"ts": "1739306668.959169", "text": "This is what <PERSON> said", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739296532.147379", "files": [{"id": "F08CN3KMT8W", "created": 1739306664, "timestamp": 1739306664, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 59754, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08CN3KMT8W/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08CN3KMT8W/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CN3KMT8W-c8504da8f0/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CN3KMT8W-c8504da8f0/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CN3KMT8W-c8504da8f0/image_360.png", "thumb_360_w": 360, "thumb_360_h": 38, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CN3KMT8W-c8504da8f0/image_480.png", "thumb_480_w": 480, "thumb_480_h": 50, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CN3KMT8W-c8504da8f0/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CN3KMT8W-c8504da8f0/image_720.png", "thumb_720_w": 720, "thumb_720_h": 75, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CN3KMT8W-c8504da8f0/image_800.png", "thumb_800_w": 800, "thumb_800_h": 84, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CN3KMT8W-c8504da8f0/image_960.png", "thumb_960_w": 960, "thumb_960_h": 100, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CN3KMT8W-c8504da8f0/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 107, "original_w": 1552, "original_h": 162, "thumb_tiny": "AwAFADC/sA7k855p3PrSmkoAOfWjn1oooAXHuaMe5paKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08CN3KMT8W/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08CN3KMT8W-655ba34b01", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "OdcKl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is what <PERSON> said"}]}]}]}, {"ts": "1739306681.057269", "text": "She's mostly opposed to them saying part time, b/c they aren't.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739296532.147379", "blocks": [{"type": "rich_text", "block_id": "68L37", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She's mostly opposed to them saying part time, b/c they aren't."}]}]}]}, {"ts": "1739306686.009889", "text": "They are hourly but not part time", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739296532.147379", "blocks": [{"type": "rich_text", "block_id": "KZceH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They are hourly but not part time"}]}]}]}, {"ts": "1739307028.594879", "text": "correct. That's what I remember", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1739296532.147379", "blocks": [{"type": "rich_text", "block_id": "dU/GG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "correct. That's what I remember"}]}]}]}, {"ts": "1739323748.268069", "text": "<@U07EJ2LP44S>\nWe have two fields \"Employment Type\" and \"Compensation Type\".  We can hide comp type on all the views and  can use \"Employment Type\" or \"Worker Type\" to display as they like as these fields don't have any impact on the functionality. However, Its also confusing to me from the message above when they say they would do adjustments on annualized numbers. Do they mean they want all the employees to be treated like Salaried employees? If its just about \"Compensation Type\" being displayed as `Part Time` we can address that quickly by changing it to `Hourly` in the UI. Let me know.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739296532.147379", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1739323827.000000"}, "blocks": [{"type": "rich_text", "block_id": "il/Gx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "\nWe have two fields \"Employment Type\" and \"Compensation Type\".  We can hide comp type on all the views and  can use \"Employment Type\" or \"Worker Type\" to display as they like as these fields don't have any impact on the functionality. However, Its also confusing to me from the message above when they say they would do adjustments on annualized numbers. Do they mean they want all the employees to be treated like Salaried employees? If its just about \"Compensation Type\" being displayed as "}, {"type": "text", "text": "Part Time", "style": {"code": true}}, {"type": "text", "text": " we can address that quickly by changing it to "}, {"type": "text", "text": "Hourly", "style": {"code": true}}, {"type": "text", "text": " in the UI. Let me know."}]}]}]}, {"ts": "1739373450.850859", "text": "<@U0690EB5JE5> She says just changing the language to 'Hourly' would be great. We just need to get rid of PT b/c they are not.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739296532.147379", "blocks": [{"type": "rich_text", "block_id": "ma6N3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " She says just changing the language to 'Hourly' would be great. We just need to get rid of PT b/c they are not."}]}]}]}, {"ts": "1739373802.992379", "text": "Ok will take care of it tomorrow ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739296532.147379", "blocks": [{"type": "rich_text", "block_id": "VNT5v", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok will take care of it tomorrow "}]}]}]}, {"ts": "1739442529.073429", "text": "<@U07EJ2LP44S> This is Done. We changed the wording `Part Time` to `Hourly` in table views and reports.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739296532.147379", "subtype": "thread_broadcast", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S", "U07M6QKHUC9"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "FGog2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is Done. We changed the wording "}, {"type": "text", "text": "Part Time", "style": {"code": true}}, {"type": "text", "text": " to "}, {"type": "text", "text": "Hourly", "style": {"code": true}}, {"type": "text", "text": " in table views and reports."}]}]}]}], "created_at": "2025-05-22T22:00:49.340463"}