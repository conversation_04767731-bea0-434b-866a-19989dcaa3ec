{"test_summary": {"start_time": "2025-05-26 04:58:39.557530", "end_time": "2025-05-26 04:58:42.881210", "total_duration": "0:00:03.323680", "documents_processed": 12, "documents_failed": 0}, "data_quality": {"total_documents": 12, "total_chunks": 186, "total_embeddings": 186, "documents_with_content": 12, "chunks_with_embeddings": 186, "documents_with_permalinks": 12, "quality_issues": []}, "data_integrity": {"postgresql_chunks": 186, "qdrant_vectors": 0, "matched_vectors": 186, "orphaned_chunks": [], "orphaned_vectors": [], "integrity_issues": []}, "search_functionality": {"test_queries": [], "successful_searches": 0, "failed_searches": 0, "search_issues": ["Search test error: Unknown mode: simple"], "performance_metrics": {"total_time": 0.0, "average_time": 0.0, "min_time": Infinity, "max_time": 0.0, "detailed_timings": []}}, "api_test_functionality": {"api_tests": [{"scenario": "Basic Search", "query": "budget adherence testing", "status": "failed", "error": "HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "response_time": 0.005836963653564453}, {"scenario": "Query Expansion Test", "query": "bug reports and issues", "status": "failed", "error": "HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "response_time": 0.004803180694580078}, {"scenario": "Multi-Step Reasoning Test", "query": "What are the main engineering challenges discussed in recent meetings?", "status": "failed", "error": "HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "response_time": 0.005205631256103516}, {"scenario": "Full RAG Features Test", "query": "Summarize customer feedback and manager recommendations from the last quarter", "status": "failed", "error": "HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "response_time": 0.004785060882568359}, {"scenario": "Low Relevance Threshold Test", "query": "testing updates and showstopper bugs", "status": "failed", "error": "HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "response_time": 0.005383014678955078}], "successful_api_calls": 0, "failed_api_calls": 5, "api_issues": ["Scenario 'Basic Search': HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "Scenario 'Query Expansion Test': HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "Scenario 'Multi-Step Reasoning Test': HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "Scenario 'Full RAG Features Test': HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "Scenario 'Low Relevance Threshold Test': HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}"], "rag_techniques_tested": {"hybrid_search": false, "query_expansion": false, "multi_step_reasoning": false, "context_aware": false, "citation_engine": false, "router_engine": false}, "performance_metrics": {"total_time": 0.026013851165771484, "average_time": 0.005202770233154297, "min_time": 0.004785060882568359, "max_time": 0.005836963653564453, "detailed_timings": [{"scenario": "Basic Search", "query": "budget adherence testing", "time_seconds": 0.005836963653564453, "time_formatted": "0.01s"}, {"scenario": "Query Expansion Test", "query": "bug reports and issues", "time_seconds": 0.004803180694580078, "time_formatted": "0.00s"}, {"scenario": "Multi-Step Reasoning Test", "query": "What are the main engineering challenges discussed in recent meetings?", "time_seconds": 0.005205631256103516, "time_formatted": "0.01s"}, {"scenario": "Full RAG Features Test", "query": "Summarize customer feedback and manager recommendations from the last quarter", "time_seconds": 0.004785060882568359, "time_formatted": "0.00s"}, {"scenario": "Low Relevance Threshold Test", "query": "testing updates and showstopper bugs", "time_seconds": 0.005383014678955078, "time_formatted": "0.01s"}]}}, "overall_status": "FAIL"}