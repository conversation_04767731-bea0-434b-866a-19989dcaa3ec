{"date": "2024-11-08", "channel_id": "C065QSSNH8A", "message_count": 22, "messages": [{"ts": "1731090512.504539", "text": "<@U04DKEFP1K8>", "user": "U06HN8XDC5A", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mRXN8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1731090508.188969", "text": "Which page ? merit view or other page", "user": "U06HN8XDC5A", "type": "message", "thread_ts": "1731090508.188969", "reply_count": 13, "blocks": [{"type": "rich_text", "block_id": "yynZm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Which page ? merit view or other page"}]}]}]}, {"ts": "1731090272.009149", "text": "<@U0690EB5JE5> <PERSON> reported slow performance, i took screen shot from aws monitoring ( for the backend and the database). These look okay at this time and overall utilization is fairly low.\nWill need to debug further if she reports any further performance issues.", "user": "U04DKEFP1K8", "type": "message", "files": [{"id": "F0806D32QH2", "created": 1731090221, "timestamp": 1731090221, "name": "Screenshot 2024-11-08 at 10.19.50 AM.png", "title": "Screenshot 2024-11-08 at 10.19.50 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 417977, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F0806D32QH2/screenshot_2024-11-08_at_10.19.50___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F0806D32QH2/download/screenshot_2024-11-08_at_10.19.50___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0806D32QH2-914eef7366/screenshot_2024-11-08_at_10.19.50___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0806D32QH2-914eef7366/screenshot_2024-11-08_at_10.19.50___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0806D32QH2-914eef7366/screenshot_2024-11-08_at_10.19.50___am_360.png", "thumb_360_w": 360, "thumb_360_h": 184, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0806D32QH2-914eef7366/screenshot_2024-11-08_at_10.19.50___am_480.png", "thumb_480_w": 480, "thumb_480_h": 245, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0806D32QH2-914eef7366/screenshot_2024-11-08_at_10.19.50___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0806D32QH2-914eef7366/screenshot_2024-11-08_at_10.19.50___am_720.png", "thumb_720_w": 720, "thumb_720_h": 367, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0806D32QH2-914eef7366/screenshot_2024-11-08_at_10.19.50___am_800.png", "thumb_800_w": 800, "thumb_800_h": 408, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0806D32QH2-914eef7366/screenshot_2024-11-08_at_10.19.50___am_960.png", "thumb_960_w": 960, "thumb_960_h": 490, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0806D32QH2-914eef7366/screenshot_2024-11-08_at_10.19.50___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 522, "original_w": 3294, "original_h": 1680, "thumb_tiny": "AwAYADDRAGaXH1pB1paAACjFGaM0AJj2NLijNFABmjNFFABmjPtRRQAZozRRQB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0806D32QH2/screenshot_2024-11-08_at_10.19.50___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F0806D32QH2-2bbf31870b", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F0803QXDKPV", "created": 1731090228, "timestamp": 1731090228, "name": "Screenshot 2024-11-08 at 10.20.42 AM.png", "title": "Screenshot 2024-11-08 at 10.20.42 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 438845, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F0803QXDKPV/screenshot_2024-11-08_at_10.20.42___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F0803QXDKPV/download/screenshot_2024-11-08_at_10.20.42___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0803QXDKPV-3da9188c56/screenshot_2024-11-08_at_10.20.42___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0803QXDKPV-3da9188c56/screenshot_2024-11-08_at_10.20.42___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0803QXDKPV-3da9188c56/screenshot_2024-11-08_at_10.20.42___am_360.png", "thumb_360_w": 360, "thumb_360_h": 187, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0803QXDKPV-3da9188c56/screenshot_2024-11-08_at_10.20.42___am_480.png", "thumb_480_w": 480, "thumb_480_h": 249, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0803QXDKPV-3da9188c56/screenshot_2024-11-08_at_10.20.42___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0803QXDKPV-3da9188c56/screenshot_2024-11-08_at_10.20.42___am_720.png", "thumb_720_w": 720, "thumb_720_h": 373, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0803QXDKPV-3da9188c56/screenshot_2024-11-08_at_10.20.42___am_800.png", "thumb_800_w": 800, "thumb_800_h": 414, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0803QXDKPV-3da9188c56/screenshot_2024-11-08_at_10.20.42___am_960.png", "thumb_960_w": 960, "thumb_960_h": 497, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0803QXDKPV-3da9188c56/screenshot_2024-11-08_at_10.20.42___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 530, "original_w": 3432, "original_h": 1778, "thumb_tiny": "AwAYADDQKrx8oP4UuB/dH5UppaAG4H90flRtH90flTqSgBNq/wB0flRhf7opaKADI9RRkeoqM0UASZHqKMj1FR0UASZHqKMj1qOigD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0803QXDKPV/screenshot_2024-11-08_at_10.20.42___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F0803QXDKPV-beee99b493", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "XuGLQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON> reported slow performance, i took screen shot from aws monitoring ( for the backend and the database). These look okay at this time and overall utilization is fairly low.\nWill need to debug further if she reports any further performance issues."}]}]}]}, {"ts": "**********.879109", "text": "Diversified Energy 2023 Bonus Worksheet: this is the information we'll need to upload into their account once we enable the bonus feature. This is last year's data they want to test on.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.879109", "reply_count": 2, "files": [{"id": "F080S1Q9TR6", "created": **********, "timestamp": **********, "name": "2023 Annual Bonus Worksheet.xlsx", "title": "2023 Annual Bonus Worksheet.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 285379, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F080S1Q9TR6/2023_annual_bonus_worksheet.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F080S1Q9TR6/download/2023_annual_bonus_worksheet.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F080S1Q9TR6-0adde6f3fe/2023_annual_bonus_worksheet_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F080S1Q9TR6-0adde6f3fe/2023_annual_bonus_worksheet_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F080S1Q9TR6/2023_annual_bonus_worksheet.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F080S1Q9TR6-ddf5637c12", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "JYqUe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified Energy 2023 Bonus Worksheet: this is the information we'll need to upload into their account once we enable the bonus feature. This is last year's data they want to test on."}]}]}]}, {"ts": "**********.360209", "text": "i did", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ksg7x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i did"}]}]}]}, {"ts": "**********.870869", "text": "<@U07EJ2LP44S> are you starting the meeting?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zqImO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " are you starting the meeting?"}]}]}]}, {"ts": "**********.521139", "text": "Iet's just keep it", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "SsfOL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Iet's just keep it"}]}]}]}, {"ts": "**********.946139", "text": "<@U07M6QKHUC9> <@U04DKEFP1K8> Should we move the meeting back this morning since <@U0690EB5JE5> isn't avail?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "r+NIJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Should we move the meeting back this morning since "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " isn't avail?"}]}]}]}, {"ts": "1731079069.576529", "text": "For DGK, I changed the canadian employees, still see this in the cycle builder. Do we need to delte the cycle and start over?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731079069.576529", "reply_count": 5, "files": [{"id": "F0805BEQWH2", "created": 1731079066, "timestamp": 1731079066, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 53030, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F0805BEQWH2/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F0805BEQWH2/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0805BEQWH2-56d185e25b/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0805BEQWH2-56d185e25b/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0805BEQWH2-56d185e25b/image_360.png", "thumb_360_w": 360, "thumb_360_h": 125, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0805BEQWH2-56d185e25b/image_480.png", "thumb_480_w": 480, "thumb_480_h": 167, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0805BEQWH2-56d185e25b/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0805BEQWH2-56d185e25b/image_720.png", "thumb_720_w": 720, "thumb_720_h": 250, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0805BEQWH2-56d185e25b/image_800.png", "thumb_800_w": 800, "thumb_800_h": 278, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0805BEQWH2-56d185e25b/image_960.png", "thumb_960_w": 960, "thumb_960_h": 333, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0805BEQWH2-56d185e25b/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 356, "original_w": 1388, "original_h": 482, "thumb_tiny": "AwAQADDSOPT9KTH0/Kl5paAE4A6UZpaTmgBc0ZpKOaAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0805BEQWH2/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F0805BEQWH2-86a2c6e380", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "PzHzy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For DGK, I changed the canadian employees, still see this in the cycle builder. Do we need to delte the cycle and start over?"}]}]}]}, {"ts": "1731072444.396419", "text": "<@U07M6QKHUC9> <@U04DKEFP1K8>\nI am traveling my native my native for some work early morning my tomorrow. Will skip the call to get some sleep. <@U06HN8XDC5A> is available on call.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "iDwTC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "\nI am traveling my native my native for some work early morning my tomorrow. Will skip the call to get some sleep. "}, {"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " is available on call."}]}]}]}, {"ts": "1731072171.477629", "text": "<@U07M6QKHUC9> demo ENV is not having these fixes. I will update demo on Monday.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DPDj+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " demo ENV is not having these fixes. I will update demo on Monday."}]}]}]}, {"ts": "1731071934.601519", "text": "<@U06HN8XDC5A> PRs merged today\n<https://github.com/Compiify/Yosemite/pull/1741>\n<https://github.com/Compiify/Yosemite/pull/1758>\n<https://github.com/Compiify/Yellowstone/pull/1775>\n<https://github.com/Compiify/Yellowstone/pull/1770>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "S9gy7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " PRs merged today\n"}, {"type": "link", "url": "https://github.com/Compiify/Yosemite/pull/1741"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://github.com/Compiify/Yosemite/pull/1758"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://github.com/Compiify/Yellowstone/pull/1775"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://github.com/Compiify/Yellowstone/pull/1770"}]}]}]}, {"ts": "1731071799.734319", "text": "*Fixes deployed to PROD today:*\n• Budget Fixes of Alaycare\n• Region and Country Filter in merit View\n• Date issue reported in DegenKolb\n• Some minor nitpick cosmetics\n<https://compiify.atlassian.net/browse/COM-3960>\n<https://compiify.atlassian.net/browse/COM-3958>\n<https://compiify.atlassian.net/browse/COM-3956>\n<https://compiify.atlassian.net/browse/COM-3922>,\n<https://compiify.atlassian.net/browse/COM-3928>,\n<https://compiify.atlassian.net/browse/COM-3927>,\n<https://compiify.atlassian.net/browse/COM-3952>,\n<https://compiify.atlassian.net/browse/COM-3929>,\n<https://compiify.atlassian.net/browse/COM-3926>,\n<https://compiify.atlassian.net/browse/COM-3794>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1731071945.000000"}, "blocks": [{"type": "rich_text", "block_id": "7twoa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Fixes deployed to PROD today:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Budget Fixes of Alaycare"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Region and Country Filter in merit View"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Date issue reported in DegenKolb"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Some minor nitpick cosmetics"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3960"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3958"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3956"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3922"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3928"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3927"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3952"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3929"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3926"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3794"}]}]}]}, {"ts": "1731070269.836149", "text": "<@U07EJ2LP44S> SDF issue. I have fixed the data. I am unable to reproduce the issue. I will continue to debug the issue. Please do not make any updates to this ENV and share the file with me. I will take care of updates to this ENV and verify during my day until we fix this.\n<@U07M6QKHUC9> Meanwhile we are working on a fix which we will have more control and can assure this won't happen again. Basically we are getting rid of the library we are using. ETA is Monday.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1731070269.836149", "reply_count": 2, "edited": {"user": "U0690EB5JE5", "ts": "1731070424.000000"}, "blocks": [{"type": "rich_text", "block_id": "C2/oF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " SDF issue. I have fixed the data. I am unable to reproduce the issue. I will continue to debug the issue. Please do not make any updates to this ENV and share the file with me. I will take care of updates to this ENV and verify during my day until we fix this.\n"}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Meanwhile we are working on a fix which we will have more control and can assure this won't happen again. Basically we are getting rid of the library we are using. ETA is Monday."}]}]}]}, {"ts": "1731061204.869459", "text": "<@U07M6QKHUC9> The foundational issue you were concerned with is because you are using rounded percents in the UI to calculate the value. In the loom of this ticket  you are using rounded value 5.76% to calculate where as the actual percent is 5.758087983. Please calculate both ways to confirm if the percents are rounded. This is the same confusion today in the UI I had explained once in the call. We can discuss more if you still feel that there is an issue.\n<https://compiify.atlassian.net/browse/COM-3958>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1731061319.000000"}, "blocks": [{"type": "rich_text", "block_id": "hQfK9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " The foundational issue you were concerned with is because you are using rounded percents in the UI to calculate the value. In the loom of this ticket  you are using rounded value 5.76% to calculate where as the actual percent is 5.758087983. Please calculate both ways to confirm if the percents are rounded. This is the same confusion today in the UI I had explained once in the call. We can discuss more if you still feel that there is an issue.\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3958"}]}]}]}, {"ts": "1731027935.320159", "text": "<@U0690EB5JE5> also regarding SDF, why does the data leakage is happening for <PERSON>? We keep fixing it and then it keeps coming back :pensive: \n\nI’m sorry to say this, but this kind of instability is really unacceptable. The data leakage is a big deal", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1731027935.320159", "reply_count": 2, "edited": {"user": "U07M6QKHUC9", "ts": "1731028016.000000"}, "blocks": [{"type": "rich_text", "block_id": "3zUq3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " also regarding SDF, why does the data leakage is happening for Nico? We keep fixing it and then it keeps coming back "}, {"type": "emoji", "name": "pensive", "unicode": "1f614"}, {"type": "text", "text": " "}, {"type": "text", "text": "\n\nI’m sorry to say this, but this kind of instability is really unacceptable. "}, {"type": "text", "text": "The data leakage is a big deal"}]}]}]}, {"ts": "1731027836.513749", "text": "We solved the dates issue in SDF and now it’s popping back in degenkolb", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1731027836.513749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "qiFyK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We solved the dates issue in SDF and now it’s popping back in degenkolb"}]}]}]}, {"ts": "**********.944149", "text": "Ok, sorry to ruin everyone's evening/morning, but we have some major issues in Degenkolb. <PERSON><PERSON><PERSON><PERSON> can share more live, but here's the overview of my experience. <https://www.loom.com/share/e579104eac44401cbd1892e849288fe1?sid=5d7a1149-5c65-426e-aff0-4e236fdaaf6c>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.944149", "reply_count": 15, "reactions": [{"name": "eyes", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}], "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "LazvK", "video_url": "https://www.loom.com/embed/e579104eac44401cbd1892e849288fe1?sid=5d7a1149-5c65-426e-aff0-4e236fdaaf6c&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/e579104eac44401cbd1892e849288fe1-50ca59f9009e9238-4x3.jpg", "alt_text": "Summary of <PERSON><PERSON>'s Account", "title": {"type": "plain_text", "text": "Summary of <PERSON><PERSON>'s Account", "emoji": true}, "title_url": "https://www.loom.com/share/e579104eac44401cbd1892e849288fe1", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 5 min  ", "emoji": true}}, {"type": "section", "block_id": "B3F6f", "text": {"type": "mrkdwn", "text": ":information_source: This video provides a detailed overview of the recent issues encountered with <PERSON><PERSON>'s account, including data wipes and cycle creation problems....", "verbatim": false}}, {"type": "actions", "block_id": "uiru+", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/e579104eac44401cbd1892e849288fe1?sid=5d7a1149-5c65-426e-aff0-4e236fdaaf6c"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"e579104eac44401cbd1892e849288fe1\",\"videoName\":\"Summary of <PERSON><PERSON>'s Account\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/e579104eac44401cbd1892e849288fe1?sid=5d7a1149-5c65-426e-aff0-4e236fdaaf6c", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "runOB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok, sorry to ruin everyone's evening/morning, but we have some major issues in Degenkolb. <PERSON><PERSON><PERSON><PERSON> can share more live, but here's the overview of my experience. "}, {"type": "link", "url": "https://www.loom.com/share/e579104eac44401cbd1892e849288fe1?sid=5d7a1149-5c65-426e-aff0-4e236fdaaf6c"}]}]}]}, {"ts": "**********.669499", "text": "ok, i'll let her know now", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Pb6Ab", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok, i'll let her know now"}]}]}]}, {"ts": "1731017155.242629", "text": "<@U07EJ2LP44S> you can send an update to chery<PERSON> stating that job levels for all individual contributors (IC) are now updated except the employee id listed below . Job level now equal numeric value taken from column K from <https://docs.google.com/spreadsheets/d/14YFrljYFaReoG5XxyXDh2DBl1tvMlTKY/edit?gid=**********#gid=**********>.\n\nFor below job titles  a matching job title was not found in pay bands file ( will need to update job level for these employees manually or provide these 2 stride team ) for bulk update\n\nEmployee ID      Employee Job title\n6113\tRevenue Accounting Manager\n922\t        Product Marketing Intern\n1618\tCustomer Success Operations Consultant\n1596\tExecutive Advisor\n876\t        Customer Enablement Intern\n866\t        Legal Intern\nB033\tQuality Analyst Developer\n1428\tExecutive Advisor\n600205\tBusiness Development Manager\n1408\tValue Engineering Consultant\nA001\tSenior Full Stack Developer\nB016\tQuality Analyst Developer\n298\t        Academic Fellow\n1000000\tStaff Developer - Contractor\n6099\tUS Home Care Consultant\n1027\tBusiness Development Manager\n158\t        Principal Technical Services Consultant\n1001\tProduct Manager - Home Care", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wgRqp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " you can send an update to chery<PERSON> stating that job levels for all individual contributors (IC) are now updated except the employee id listed below . Job level now equal numeric value taken from column K from "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/14YFrljYFaReoG5XxyXDh2DBl1tvMlTKY/edit?gid=**********#gid=**********"}, {"type": "text", "text": ".\n\nFor below job titles  a matching job title was not found in pay bands file ( will need to update job level for these employees manually or provide these 2 stride team ) for bulk update\n\nEmployee ID      Employee Job title\n6113\tRevenue Accounting Manager\n922\t        Product Marketing Intern\n1618\tCustomer Success Operations Consultant\n1596\tExecutive Advisor\n876\t        Customer Enablement Intern\n866\t        Legal Intern\nB033\tQuality Analyst Developer\n1428\tExecutive Advisor\n600205\tBusiness Development Manager\n1408\tValue Engineering Consultant\nA001\tSenior Full Stack Developer\nB016\tQuality Analyst Developer\n298\t        Academic Fellow\n1000000\tStaff Developer - Contractor\n6099\tUS Home Care Consultant\n1027\tBusiness Development Manager\n158\t        Principal Technical Services Consultant\n1001\tProduct Manager - Home Care"}]}]}]}, {"ts": "**********.115609", "text": "<!here> We are going to submit our company profile and and a self review of our capabilities to a comp analyst for including in their market research report. Check out this doc and pls let me know if we need to add any features and capabilities that I missed in this doc\n<https://docs.google.com/document/d/1WDA_giHZEi30ogJam1q9Gjp7lKsy-7WxxbHWGL-7quI/edit?tab=t.0>", "user": "U07M6QKHUC9", "type": "message", "files": [{"id": "F07VCSBBL11", "created": 1731016216, "timestamp": 1731016216, "name": "StrideHR Compensation Planning Platform", "title": "StrideHR Compensation Planning Platform", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 108678, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1WDA_giHZEi30ogJam1q9Gjp7lKsy-7WxxbHWGL-7quI", "external_url": "https://docs.google.com/document/d/1WDA_giHZEi30ogJam1q9Gjp7lKsy-7WxxbHWGL-7quI/edit?tab=t.0", "url_private": "https://docs.google.com/document/d/1WDA_giHZEi30ogJam1q9Gjp7lKsy-7WxxbHWGL-7quI/edit?tab=t.0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07VCSBBL11-312497283a/stridehr_compensation_planning_platform_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07VCSBBL11-312497283a/stridehr_compensation_planning_platform_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07VCSBBL11-312497283a/stridehr_compensation_planning_platform_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07VCSBBL11-312497283a/stridehr_compensation_planning_platform_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07VCSBBL11-312497283a/stridehr_compensation_planning_platform_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07VCSBBL11-312497283a/stridehr_compensation_planning_platform_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07VCSBBL11-312497283a/stridehr_compensation_planning_platform_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07VCSBBL11-312497283a/stridehr_compensation_planning_platform_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07VCSBBL11-312497283a/stridehr_compensation_planning_platform_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1035, "thumb_tiny": "AwAwACXTJxSbh7/lQTik3UALuHv+VAIPr+VG4UbhQAtFFFACHHek+WnHpTMfWgB3HtRx7UmB6GjA9DQA6iiigAJwKbn3pxGaTaKADd9KM/SjaKXA9KACiiigD//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07VCSBBL11/stridehr_compensation_planning_platform", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "JNJy4", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " We are going to submit our company profile and and a self review of our capabilities to a comp analyst for including in their market research report. Check out this doc and pls let me know if we need to add any features and capabilities that I missed in this doc\n"}, {"type": "link", "url": "https://docs.google.com/document/d/1WDA_giHZEi30ogJam1q9Gjp7lKsy-7WxxbHWGL-7quI/edit?tab=t.0"}]}]}]}, {"ts": "1731006200.018049", "text": "Where did we land on the bonus conversation? Has anything been built yet? I need to give <PERSON> an accurate expectation on when she can test.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731006200.018049", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "3Fu/J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Where did we land on the bonus conversation? Has anything been built yet? I need to give <PERSON> an accurate expectation on when she can test."}]}]}]}], "created_at": "2025-05-22T21:35:34.670266"}