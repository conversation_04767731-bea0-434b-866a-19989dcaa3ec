{"date": "2024-10-24", "channel_id": "C065QSSNH8A", "message_count": 16, "messages": [{"ts": "1729791479.094569", "text": "<@U04DKEFP1K8> I was discussing this scenario with <PERSON> when I was review the fix. This is expected behaviour.  I am not sure if we should change it. If they remove its difficult know if the user intended to remove inputs as well. Please advice.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729719113.977379", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "9db4M", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I was discussing this scenario with <PERSON> when I was review the fix. This is expected behaviour.  I am not sure if we should change it. If they remove its difficult know if the user intended to remove inputs as well. Please advice."}]}]}]}, {"ts": "1729785855.274649", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1729785850.309249", "text": "<@U07M6QKHUC9> <PERSON> has joined", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6pkTO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " <PERSON> has joined"}]}]}]}, {"ts": "1729785830.224539", "text": "<!here> Let's meet here on my zoom as <PERSON> is running late\n<https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8FWf7", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Let's meet here on my zoom as <PERSON> is running late\n"}, {"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1729784751.310639", "text": "We have the fix ready. We did testing and deployed. Fix is very minor and deploying to PROD. We will continue more regression tomorrow. Changes look good for this issue.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729719113.977379", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "j2i7h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We have the fix ready. We did testing and deployed. Fix is very minor and deploying to PROD. We will continue more regression tomorrow. Changes look good for this issue."}]}]}]}, {"ts": "1729767876.402439", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> Alaycare reports issue is resolved,", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DGH15", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Alaycare reports issue is resolved,"}]}]}]}, {"ts": "1729729369.568119", "text": "<!here> are requirements discussed with <PERSON> today\n1. HRBP usecase: \"So for example, we'll take <PERSON>, she supports <PERSON>. She should be able to go in and see everyone below <PERSON>. So obviously not <PERSON>, but everyone below <PERSON> and kind of like do a spot check.\"\n2. <PERSON> will discuss flagged employees with VP's so lets make sure flags are raised correctly\n3. HRBP will not submit on behalf of manager, manager themselves will submit \n4. Have a clear solution available for chery<PERSON> to update any adjustment after manager has submitted , she is 100% aware she will be updating some emp for sure and she does not want manager going back n again\n5. Manager will first manage their direct reports and then later filter by their manager and perform adjustment for certain emp. we will need this jira addressed before that <https://compiify.atlassian.net/browse/COM-3919>\n6. <PERSON> ideally wanted cycle insights to update live as change are being made and without waiting for manager to submit \n7. HRBP's , <PERSON>, <PERSON><PERSON> and <PERSON><PERSON> will be doing final validations\n8. She will using the reports to push the updated data back to their HRIS for payroll and comp letters\n", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1729729369.568119", "reply_count": 4, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14002::e8000253eaad47d2a57511fb5d43dabd", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3919?atlOrigin=eyJpIjoiNGY3NWUwMzdmMjFiNDgwNWJmOWM4MWRhOGNhNjVjZjYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3919 Add Filter for Manager Name in Recommenders Meeting Review>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14002::4a7d391c747d42ac92d340f3090e93ff", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14002\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3919\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3919", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "lkG2i", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " are requirements discussed with <PERSON> today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "HRBP usecase: \"So for example, we'll take <PERSON>, she supports <PERSON>. She should be able to go in and see everyone below <PERSON>. So obviously not <PERSON>, but everyone below <PERSON> and kind of like do a spot check.\""}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> will discuss flagged employees with VP's so lets make sure flags are raised correctly"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "HRBP will not submit on behalf of manager, manager themselves will submit "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Have a clear solution available for chery<PERSON> to update any adjustment after manager has submitted , she is 100% aware she will be updating some emp for sure and she does not want manager going back n again"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Manager will first manage their direct reports and then later filter by their manager and perform adjustment for certain emp. we will need this jira addressed before that "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3919"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> ideally wanted cycle insights to update live as change are being made and without waiting for manager to submit "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "HRBP's , <PERSON>, <PERSON><PERSON> and <PERSON><PERSON> will be doing final validations"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "She will using the reports to push the updated data back to their HRIS for payroll and comp letters"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1729723920.642529", "text": "Overall SDF test env looks pretty good. I still need to re-test few things but I think we are ready for them.:partying_face::fire:\nGood work <@U071FN2589Y> <@U06HN8XDC5A> <@U07MH77PUBV> <@U0690EB5JE5> <@U07EJ2LP44S> <@U04DKEFP1K8>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729723920.642529", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "QnM9Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Overall SDF test env looks pretty good. I still need to re-test few things but I think we are ready for them."}, {"type": "emoji", "name": "partying_face", "unicode": "1f973"}, {"type": "emoji", "name": "fire", "unicode": "1f525"}, {"type": "text", "text": "\nGood work "}, {"type": "user", "user_id": "U071FN2589Y"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1729722218.238899", "text": "This is an interesting part of the calibration process and highlights the flexibility Stride provides to its customers. Great job team!", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729719784.936449", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "aVHgB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is an interesting part of the calibration process and highlights the flexibility Stride provides to its customers. Great job team!"}]}]}]}, {"ts": "1729719843.289769", "text": "<!here> we have asked cainwatters team to pause adding any adjsutment for a day so we have make sure Fix for COM-3917 is developed and deployed by eod of Oct 24 IST", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8ku/D", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " we have asked cainwatters team to pause adding any adjsutment for a day so we have make sure Fix for COM-3917 is developed and deployed by eod of Oct 24 IST"}]}]}]}, {"ts": "1729719784.936449", "text": "<@U0690EB5JE5> additionally cainwatters team also explained a requirement which is already available in the product bu tneed to be thoroughly regressed to avoid any issue it is documented here <https://compiify.atlassian.net/browse/COM-3918>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1729719784.936449", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14001::3aaee74a1a024a1a8a0ca8b2f298ee59", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3918?atlOrigin=eyJpIjoiNmNhOWJkYmE2Mjc3NGM2N2E0ODc2ZGVkM2M0ODUwNmYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3918 Update Rating Scale and Merit Increase Calculation>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14001::753f1af92daf4416a6fc10a6cf3c7421", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14001\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3918\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3918", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "MNG+A", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " additionally cainwatters team also explained a requirement which is already available in the product bu tneed to be thoroughly regressed to avoid any issue it is documented here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3918"}]}]}]}, {"ts": "1729719113.977379", "text": "<@U0690EB5JE5> <PERSON> and I connected with cainwatters and now i am able to reproduce the issue. It is documented here <https://compiify.atlassian.net/browse/COM-3917>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1729719113.977379", "reply_count": 34, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14000::6f686758103a4bbf9bfe356755ec09fa", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3917?atlOrigin=eyJpIjoiMTRlM2Q4Y2UyZDkwNDE2MzkzNDEwMjUyYmIwZjQ0MzYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3917 Issue: New salary not updating after merit increase>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14000::2a602e761e774a29a5e37bfca1fa3c62", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14000\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3917\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3917", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "gYkfF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON> and I connected with cainwatters and now i am able to reproduce the issue. It is documented here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3917"}]}]}]}, {"ts": "1729712004.337849", "text": "<@U0690EB5JE5> a minor bug on data consistency. What did <PERSON> decide in terms of calculations on decimal points?\n<https://compiify.atlassian.net/browse/COM-3916>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13999::05ee579fd8154a41998bfd3271f0ca98", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3916?atlOrigin=eyJpIjoiZjcwNWMzOWQ5ZDZhNDY5ODgxNmQwNWJkYzdhMWI3N2IiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3916 Inconsistent Promotion Rate Calculation in People Insights Team>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13999::c34117c1bc9242b6989be91f209f05c6", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13999\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3916\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3916", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "H6j8s", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " a minor bug on data consistency. What did <PERSON> decide in terms of calculations on decimal points?\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3916"}]}]}]}, {"ts": "1729709674.011699", "text": "I dont' think it's on yet either, <@U04DKEFP1K8>?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729709674.011699", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "pg4u7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I dont' think it's on yet either, "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1729709538.438559", "text": "No I have not yet, I will reply back now asking if she would like access to the env or just the data download", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729709538.438559", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "4UGfP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No I have not yet, I will reply back now asking if she would like access to the env or just the data download"}]}]}]}, {"ts": "1729709222.909819", "text": "<@U07EJ2LP44S> could you provide an update on if we have shared env with Vercara?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "s+dxx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " could you provide an update on if we have shared env with <PERSON><PERSON><PERSON><PERSON>?"}]}]}]}], "created_at": "2025-05-22T21:35:34.662359"}