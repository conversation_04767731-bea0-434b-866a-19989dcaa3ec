{"date": "2024-02-01", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1706752612.441269", "text": "Priorities for Eng for the next day:\n• :repeat: Password reset flow (<https://compiify.atlassian.net/browse/COM-2240|COM-2240>) _(I know we're making progress on this and it's expected to be a multi-day effort_ :slightly_smiling_face: _)_\n• SDF: Additional columns for previous salary &amp; raise date (<https://compiify.atlassian.net/browse/COM-2232|COM-2232>)\n• Bulk Actions for adjustment letters (<https://compiify.atlassian.net/browse/COM-2252|COM-2252>)\n• Adjustment letter visibility / privacy (<https://compiify.atlassian.net/browse/COM-2257|COM-2257>)\n• Errors while approving/overriding on QA env (<https://compiify.atlassian.net/browse/COM-2261|COM-2261>)\n• Job level filter for Adjustment letters (<https://compiify.atlassian.net/browse/COM-2250|COM-2250>)\nThen, continue with <https://compiify.atlassian.net/browse/COM-2087|Wave 3> from the top of the list, especially the Reports-related items.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eMc3D", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Password reset flow ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2240", "text": "COM-2240"}, {"type": "text", "text": ") "}, {"type": "text", "text": "(I know we're making progress on this and it's expected to be a multi-day effort ", "style": {"italic": true}}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642", "style": {"italic": true}}, {"type": "text", "text": " )", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF: Additional columns for previous salary & raise date ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2232", "text": "COM-2232"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Bulk Actions for adjustment letters ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2252", "text": "COM-2252"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Adjustment letter visibility / privacy ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2257", "text": "COM-2257"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Errors while approving/overriding on QA env ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2261", "text": "COM-2261"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Job level filter for Adjustment letters ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2250", "text": "COM-2250"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nThen, continue with "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " from the top of the list, especially the Reports-related items."}]}]}]}, {"ts": "1706740686.027659", "text": "<@U0658EW4B8D> <@U065H3M6WJV> Here is 1st of 2 updates for Adjustment letter:\nMultiple bug fixes request under <https://compiify.atlassian.net/browse/COM-2145> have been deployed on qa environment.\n\nPending items i am working on: Additional template required for <PERSON>'s testing. ETA: end of the day. I will send an update once the remaining templated are uploaded on the env.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1706740686.027659", "reply_count": 3, "edited": {"user": "U04DKEFP1K8", "ts": "1706740699.000000"}, "reactions": [{"name": "gratitude-thank-you", "users": ["U065H3M6WJV", "U0658EW4B8D"], "count": 2}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12206::674baf90c08911ee90696bacf15c48de", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2145?atlOrigin=eyJpIjoiN2Y4Yjc4NDlmYjg3NGMyNjhkYzZlNTI2OTUxODM4OTIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2145 Neuroflow UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12206::674baf92c08911ee90696bacf15c48de", "elements": [{"type": "mrkdwn", "text": "Status: *In Progress*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12206::674baf91c08911ee90696bacf15c48de", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12206\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12206\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2145", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "v/Ou3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Here is 1st of 2 updates for Adjustment letter:\nMultiple bug fixes request under "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2145"}, {"type": "text", "text": " have been deployed on qa environment.\n\nPending items i am working on: Additional template required for <PERSON>'s testing. ETA: end of the day. I will send an update once the remaining templated are uploaded on the env."}]}]}]}], "created_at": "2025-05-22T21:35:34.585738"}