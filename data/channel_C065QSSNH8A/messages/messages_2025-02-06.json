{"date": "2025-02-06", "channel_id": "C065QSSNH8A", "message_count": 34, "messages": [{"ts": "1738866297.361909", "text": "<@U0690EB5JE5> <PERSON> cannot see full data (she does see some data) in her view right now. The filters aren't working, and when I impersonate I see nothing. I removed and readded her role and it did not help. This is <PERSON><PERSON><PERSON>, <PERSON> Super Admin. Same for <PERSON><PERSON>.", "user": "U07EJ2LP44S", "type": "message", "edited": {"user": "U07EJ2LP44S", "ts": "1738866383.000000"}, "blocks": [{"type": "rich_text", "block_id": "UXA/u", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON> cannot see full data (she does see some data) in her view right now. The filters aren't working, and when I impersonate I see nothing. I removed and readded her role and it did not help. This is <PERSON><PERSON><PERSON>, <PERSON> Super Admin. Same for <PERSON><PERSON>."}]}]}]}, {"ts": "1738863898.973629", "text": "<@U0690EB5JE5> In the calculated budget section of the cycle builder, the calclulated budget is reflecting INR but the budget is reflecting USD. i don't think this is causing any functional issues but FYI.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738863898.973629", "reply_count": 7, "files": [{"id": "F08C1T921B7", "created": 1738863894, "timestamp": 1738863894, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 54299, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08C1T921B7/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08C1T921B7/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_360.png", "thumb_360_w": 360, "thumb_360_h": 212, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_480.png", "thumb_480_w": 480, "thumb_480_h": 283, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_720.png", "thumb_720_w": 720, "thumb_720_h": 425, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_800.png", "thumb_800_w": 800, "thumb_800_h": 472, "original_w": 912, "original_h": 538, "thumb_tiny": "AwAcADDSPPcikx/tGlFLQA3HuaMf7TUtH4UAJj3NLkUH6UUAJj2oA56Uv4UZoAKBRmgUAFLRRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08C1T921B7/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08C1T921B7-d10ace4c6a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Jn6Hw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " In the calculated budget section of the cycle builder, the calclulated budget is reflecting INR but the budget is reflecting USD. i don't think this is causing any functional issues but FYI."}]}]}]}, {"ts": "1738858072.111179", "text": "ok lets do that", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8mBzF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok lets do that"}]}]}]}, {"ts": "1738857843.692319", "text": "I am available if you want to meet ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "JTlF2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am available if you want to meet "}]}]}]}, {"ts": "1738857833.708679", "text": "It’s ready", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fpK1t", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It’s"}, {"type": "text", "text": " ready"}]}]}]}, {"ts": "1738857818.297889", "text": "Yes, I can", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wjwGe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, I can"}]}]}]}, {"ts": "1738857790.586429", "text": "<@U0690EB5JE5> you were going to show the total rewards today?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "SZFpJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " you were going to show the total rewards today?"}]}]}]}, {"ts": "1738857716.918369", "text": "I am fine to skip", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738857716.918369", "reply_count": 10, "blocks": [{"type": "rich_text", "block_id": "ezU6h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am fine to skip"}]}]}]}, {"ts": "1738857684.937499", "text": "<@U07EJ2LP44S> <@U0690EB5JE5> do we need to meet after <PERSON>'s diversified call or should we skip the meeting?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vf123", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " do we need to meet after <PERSON>'s diversified call or should we skip the meeting?"}]}]}]}, {"ts": "1738857070.683319", "text": "I thought HRBP were able to impersonate their managers and I remember seeing it", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jQdVh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I thought HRBP were able to impersonate their managers and I remember seeing it"}]}]}]}, {"ts": "1738856032.912339", "text": "I don’t think so, let me check. Impersonation was allowed only for admins ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vUBiP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " think so, let me check. Impersonation was allowed only for admins "}]}]}]}, {"ts": "1738855968.816769", "text": "Ok I think I'm with you on this but she SWEARS it was possible before. I have a call with her at noon to talk anyway so we'll see what she says then", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0G/Dt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok I think I'm with you on this but she SWEARS it was possible before. I have a call with her at noon to talk anyway so we'll see what she says then"}]}]}]}, {"ts": "1738855939.091269", "text": "And export ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "R5Hd7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And export "}]}]}]}, {"ts": "1738855932.644119", "text": "Yes, no reports just table ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "e095v", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, no reports just table "}]}]}]}, {"ts": "1738855829.991279", "text": "It makes sense though that they wouldn't be able to - since we modeled HRBP after managers IIRC?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oOKxF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It makes sense though that they wouldn't be able to - since we modeled HRBP after managers IIRC?"}]}]}]}, {"ts": "1738855765.873809", "text": "<PERSON> said she is positive HRBPs could impersonate before", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EXzUA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> said she is positive HRBPs could impersonate before"}]}]}]}, {"ts": "1738855735.853249", "text": "Ok; I didn't test that ever so I don't know. Is that the same for reporting? Did HRBPs ever have access to reports?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "f/1Xc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok; I didn't test that ever so I don't know. Is that the same for reporting? Did HRBPs ever have access to reports?"}]}]}]}, {"ts": "1738855618.431929", "text": "Also Impersonation doesn’t makes sense VG HRBP as they can only see employees assigned.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0hNml", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also Impersonation "}, {"type": "text", "text": "doesn’t"}, {"type": "text", "text": " makes sense VG HRBP as they can only see employees assigned."}]}]}]}, {"ts": "1738855544.054639", "text": "I don’t think we ever supported that.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "C/NAL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " think we ever supported that."}]}]}]}, {"ts": "1738855460.501149", "text": "Whereas before they could go in and see what the manager saw", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LRyku", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Whereas before they could go in and see what the manager saw"}]}]}]}, {"ts": "1738855442.691999", "text": "I think they are saying the HRBP themselves isn't able to impersonate a manager they work with", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "smonC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think they are saying the HRBP themselves isn't able to impersonate a manager they work with"}]}]}]}, {"ts": "1738855219.655709", "text": "Impersonation of an HRBP in VG", "user": "U0690EB5JE5", "type": "message", "files": [{"id": "F08C3KSLFGT", "created": 1738855209, "timestamp": 1738855209, "name": "Screenshot 2025-02-06 at 8.50.04 PM.png", "title": "Screenshot 2025-02-06 at 8.50.04 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 259276, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08C3KSLFGT/screenshot_2025-02-06_at_8.50.04___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08C3KSLFGT/download/screenshot_2025-02-06_at_8.50.04___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 195, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 260, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 389, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 433, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 519, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 554, "original_w": 1916, "original_h": 1036, "thumb_tiny": "AwAZADDR69qPwoxjGKU0AGKSlo9KAE/Cl/CiloAQ0UoooASilpBQAUtFFAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08C3KSLFGT/screenshot_2025-02-06_at_8.50.04___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08C3KSLFGT-2b3c840fb8", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "KioDG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Impersonation of an HRBP in VG"}]}]}]}, {"ts": "1738855180.562519", "text": "do you have an example?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "dFomM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "do you have an example?"}]}]}]}, {"ts": "1738855174.910829", "text": "<@U07EJ2LP44S> HRBP I am able to impersonate, whats the issue exactly", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4OCsm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " HRBP I am able to impersonate, whats the issue exactly"}]}]}]}, {"ts": "1738855159.111399", "text": "Yes, We are on top of it. <@U06HN8XDC5A> FYI...", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "j48mC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, We are on top of it. "}, {"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " FYI..."}]}]}]}, {"ts": "1738855122.381909", "text": "Ok. That is highest priority for me since we have an active cycle, and one starting on Monday. We didn't train people to press enter because that wasn't the behavior before so there's a sense of urgency there.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738855122.381909", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "iKcT9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. That is highest priority for me since we have an active cycle, and one starting on Monday. We didn't train people to press enter because that wasn't the behavior before so there's a sense of urgency there."}]}]}]}, {"ts": "1738855063.102669", "text": "Work in progress ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "06eRS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Work in progress "}]}]}]}, {"ts": "1738855050.398429", "text": "Enter thing will address tomorrow ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jUIYy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Enter thing will address tomorrow "}]}]}]}, {"ts": "1738855032.112419", "text": "I just told her about the enter thing. How quickly can we address that?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "euGKJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I just told her about the enter thing. How quickly can we address that?"}]}]}]}, {"ts": "1738855015.964619", "text": "I have not pushed any changes today ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ArFQp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have not pushed any changes today "}]}]}]}, {"ts": "1738854989.377069", "text": "HRBP will check and get back ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "KllHz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "HRBP will check and get back "}]}]}]}, {"ts": "1738854976.656029", "text": "1. Merit not working. I think they have to press enter which is not obvious", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+yK8x", "elements": [{"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Merit not working. I think they have to press enter which is not obvious"}]}], "style": "ordered", "indent": 0, "offset": 0, "border": 0}]}]}, {"ts": "1738854851.150619", "text": "<@U0690EB5JE5> From <PERSON> at Valgenesis:Quick flag before our call\n[10:07 AM] <PERSON>\n\nHaving some comments from managers the comp planner isn't working right ( not updating the comp field when a% is added and merit not updating at the top\n[10:08 AM] <PERSON>\n\nAlso the HRBP's can't impersonate people the settings and Reporting view has gone ? but we didn't change anything", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "aeDI6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " From <PERSON> at Valgenesis:Quick flag before our call\n[10:07 AM] <PERSON>\n\nHaving some comments from managers the comp planner isn't working right ( not updating the comp field when a% is added and merit not updating at the top\n[10:08 AM] <PERSON>\n\nAlso the HRBP's can't impersonate people the settings and Reporting view has gone ? but we didn't change anything"}]}]}]}, {"ts": "1738852884.947509", "text": "I have an 11am meeting with <PERSON><PERSON><PERSON> (including <PERSON>) so I may not make it to the call. I don't know that it would go that long but just FYI.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5", "U07M6QKHUC9"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "DlsgJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have an 11am meeting with <PERSON><PERSON><PERSON> (including <PERSON>) so I may not make it to the call. I don't know that it would go that long but just FYI."}]}]}]}], "created_at": "2025-05-22T21:35:34.701741"}