{"date": "2024-06-20", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1718838957.047129", "text": "<@U065H3M6WJV> can you please review <https://compiify.atlassian.net/browse/COM-3340>. requirements added in this jira to allow granular performance rating in the product for CWA", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1718838957.047129", "reply_count": 2, "edited": {"user": "U04DKEFP1K8", "ts": "1718838991.000000"}, "reactions": [{"name": "muscle", "users": ["U04DS2MBWP4"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13401::e2c3e7c02e9111efab59e75b32d98389", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3340?atlOrigin=eyJpIjoiZTRlZTIyMTIzMjM0NGI0ZDgzYjQyZjE0OTY3YWQ3ODYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3340 Allow granular performance ratings for cainwatters environment>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13401::e2c40ed02e9111efab59e75b32d98389", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2a19465d953b8640b8f3558fd4f15da9?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13401::e2c3e7c12e9111efab59e75b32d98389", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13401\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13401\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3340", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "WXMxG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " can you please review "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3340"}, {"type": "text", "text": ". requirements added in this jira to allow granular performance rating in the product for CWA"}]}]}]}, {"ts": "1718827465.105159", "text": "I tried to create a new cycle today in `new-meritview` env and it's not working. :confused:\n\n<@U04DKEFP1K8> is there a known issue that would prevent new cycles being launched? Or can you help me figure out why the \"Mid-Year Cycle\" that is set to \"Active\" status cannot be viewed? (I can't get the budget allocation page to load either)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1718827465.105159", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "QpR8r", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I tried to create a new cycle today in "}, {"type": "text", "text": "new-meritview", "style": {"code": true}}, {"type": "text", "text": " env and it's not working. "}, {"type": "emoji", "name": "confused", "unicode": "1f615"}, {"type": "text", "text": "\n\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " is there a known issue that would prevent new cycles being launched? Or can you help me figure out why the \"Mid-Year Cycle\" that is set to \"Active\" status cannot be viewed? (I can't get the budget allocation page to load either)"}]}]}]}], "created_at": "2025-05-22T21:35:34.624483"}