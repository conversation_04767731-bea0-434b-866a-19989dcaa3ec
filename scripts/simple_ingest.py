#!/usr/bin/env python
"""
Simple direct ingestion script using LocalSlackSourceInterface.

This script bypasses the DocumentSource model and directly uses the
LocalSlackSourceInterface with the UnifiedLlamaIndexIngestionService.

Usage:
    cd multi_source_rag
    python ../scripts/simple_ingest.py
"""

import os
import sys
import django
from datetime import datetime

# Set up Django
sys.path.append('.')
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from apps.documents.services.llama_ingestion_service_unified import UnifiedLlamaIndexIngestionService
from apps.documents.interfaces.local_slack import LocalSlackSourceInterface
from apps.accounts.models import Tenant

def main():
    print("🚀 Simple Slack Data Ingestion")
    print("=" * 40)
    
    start_time = datetime.now()
    
    try:
        # 1. Get or create tenant
        tenant, created = Tenant.objects.get_or_create(
            slug="default",
            defaults={"name": "Default Tenant"}
        )
        if created:
            print("✅ Created default tenant")
        else:
            print("✅ Using existing tenant")
        
        # 2. Initialize LocalSlackSourceInterface
        print("📡 Initializing LocalSlackSourceInterface...")
        slack_interface = LocalSlackSourceInterface({
            "data_dir": "../data/",
            "channel": "C065QSSNH8A",
            "max_tokens": 500,
            "overlap_tokens": 50
        })
        
        # 3. Get data info
        info = slack_interface.get_staged_data_info()
        print(f"📊 Found {info.total_messages} messages in {len(info.available_dates)} files")
        
        # 4. Fetch documents (limit for testing)
        print("📄 Fetching documents...")
        documents = slack_interface.fetch_documents(limit=50, days_back=365)
        print(f"✅ Got {len(documents)} documents")
        
        if not documents:
            print("⚠️ No documents to ingest")
            return
        
        # 5. Initialize UnifiedLlamaIndexIngestionService
        print("🔧 Initializing ingestion service...")
        ingestion_service = UnifiedLlamaIndexIngestionService(tenant)
        
        # 6. Process documents directly
        print("🔄 Starting ingestion...")
        
        processed_count = 0
        failed_count = 0
        
        for i, document in enumerate(documents):
            try:
                print(f"Processing document {i+1}/{len(documents)}: {document['id']}")
                
                # Create a mock source for the ingestion service
                from apps.documents.models import DocumentSource
                mock_source = DocumentSource(
                    tenant=tenant,
                    name="Local Slack C065QSSNH8A",
                    source_type="slack",
                    config={
                        "data_dir": "../data/",
                        "channel": "C065QSSNH8A"
                    }
                )
                
                # Process single document
                raw_doc = ingestion_service._process_single_document(mock_source, document)
                processed_count += 1
                
                if (i + 1) % 10 == 0:
                    print(f"✅ Processed {i + 1} documents...")
                
            except Exception as e:
                print(f"❌ Failed to process document {document.get('id', 'unknown')}: {e}")
                failed_count += 1
                continue
        
        # 7. Show results
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n🎉 INGESTION COMPLETED!")
        print("=" * 30)
        print(f"📄 Documents processed: {processed_count}")
        print(f"❌ Documents failed: {failed_count}")
        print(f"⏱️ Duration: {duration.total_seconds():.1f} seconds")
        
        # Get stats from ingestion service
        stats = ingestion_service.get_processing_stats()
        if stats:
            print(f"🔗 Chunks created: {stats.get('chunks_created', 0)}")
            print(f"⚡ Vectors stored: {stats.get('vectors_stored', 0)}")
        
        print(f"\n✅ Slack data ingestion completed successfully!")
        
    except Exception as e:
        print(f"\n❌ INGESTION FAILED: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
