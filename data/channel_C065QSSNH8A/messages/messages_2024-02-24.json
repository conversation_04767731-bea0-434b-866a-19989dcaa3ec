{"date": "2024-02-24", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1708798040.777829", "text": "<@U065H3M6WJV>If the changes are approve before we confirm lisa and katya , we should ideally give a quick demo to one of them to explain how live budget update ,  review all, unlock all updates work. thoughts?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708798040.777829", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "ssC3k", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": "If the changes are approve before we confirm lisa and katya , we should ideally give a quick demo to one of them to explain how live budget update ,  review all, unlock all updates work. thoughts?"}]}]}]}, {"ts": "1708797327.401409", "text": "Sure <PERSON>. ", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708797327.401409", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "TDE/Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure <PERSON>. "}]}]}]}, {"ts": "1708797076.498829", "text": "I have an appointment starting at 12, is it possible for me to get the changes to sanity test by around 10:30 <@U04DKEFP1K8> ?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9UTCR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have an appointment starting at 12, is it possible for me to get the changes to sanity test by around 10:30 "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " ?"}]}]}]}, {"ts": "1708796867.654849", "text": "<@U04DS2MBWP4> these were the 4 critical issues, if those are addressed and no new blockers found, we’ll be good to release the changes", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nDXYw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " these were the 4 critical issues, if those are addressed and no new blockers found, we’ll be good to release the changes"}]}]}]}, {"ts": "1708796328.060359", "text": "ok. <@U065H3M6WJV> are you comfortable with releasing to <PERSON> with with these 4 fixes or do we need to fix others as well?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "H9A35", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok. "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " are you comfortable with releasing to <PERSON> with with these 4 fixes or do we need to fix others as well?"}]}]}]}, {"ts": "1708796167.994139", "text": "We have received fixes for 4 critical fixes <PERSON> had requested. I am starting to test them now, will be making them available for Rachel Testing in another hour or so.", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xF8sH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We have received fixes for 4 critical fixes <PERSON> had requested. I am starting to test them now, will be making them available for Rachel Testing in another hour or so."}]}]}]}, {"ts": "1708796114.656469", "text": "what should we tell <PERSON>?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "98fp9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "what should we tell <PERSON>?"}]}]}]}, {"ts": "1708796079.718839", "text": "<@U04DKEFP1K8> <@U065H3M6WJV> how are we doing on resolving the bugs?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9wONo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " how are we doing on resolving the bugs?"}]}]}]}, {"ts": "1708734330.297639", "text": "<@U04DKEFP1K8> From my initial testing of the realtime updates, I've found 8 more bugs and added them to <https://compiify.atlassian.net/browse/COM-1929|this JIRA>. I think at least 4 of them could be blockers for SDF to use the feature. Can you take a look and assess whether you think eng can solve them by tomorrow?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708734330.297639", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "cWbNp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " From my initial testing of the realtime updates, I've found 8 more bugs and added them to "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1929", "text": "this JIRA"}, {"type": "text", "text": ". I think at least 4 of them could be blockers for SDF to use the feature. Can you take a look and assess whether you think eng can solve them by tomorrow?"}]}]}]}], "created_at": "2025-05-22T21:35:34.579658"}