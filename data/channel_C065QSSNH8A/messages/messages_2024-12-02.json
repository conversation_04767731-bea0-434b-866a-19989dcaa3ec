{"date": "2024-12-02", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1733161292.385309", "text": "Bug (maybe) in Curana's comp builder: <https://compiify.atlassian.net/browse/COM-4010>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733161292.385309", "reply_count": 7, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14093::fffb7cada4064b9b9a8f9a2b3c292806", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4010?atlOrigin=eyJpIjoiOTNkYjg3M2RhMWE3NDcxZWE1MTJhOWJkMzBhNWMzOWQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4010 Missing Performance Option in Recommendation Guidelines for Curana>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14093::35c09d0d7a514483adc5bc1ffaee5a01", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14093\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4010\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4010", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Qbito", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bug (maybe) in <PERSON><PERSON><PERSON>'s comp builder: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4010"}]}]}]}, {"ts": "1733159674.355589", "text": "<@U07EJ2LP44S> please play around with Cura<PERSON> cycle and let me know potential blockers for your meeting.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MwUMh", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " please play around with <PERSON><PERSON><PERSON> cycle and let me know potential blockers for your meeting."}]}]}]}, {"ts": "1733159203.672949", "text": "<https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "M6D04", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3"}]}]}]}, {"ts": "1733153665.156469", "text": "<!here> can we please move our meeting to 8:30 AM I need to drop off my daughter to school", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733153665.156469", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "vztGB", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " can we please move our meeting to 8:30 AM I need to drop off my daughter to school"}]}]}]}], "created_at": "2025-05-22T21:35:34.683146"}