{"thread_ts": "1738090837.658679", "channel_id": "C065QSSNH8A", "reply_count": 12, "replies": [{"ts": "1738123714.312429", "text": "<@U07EJ2LP44S> Probably we should tell them we don't support this specific requirement of `Proration based on last raise date` for this cycle.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738090837.658679", "blocks": [{"type": "rich_text", "block_id": "TeQ/u", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Probably we should tell them we don't support this specific requirement of "}, {"type": "text", "text": "Proration based on last raise date", "style": {"code": true}}, {"type": "text", "text": " for this cycle."}]}]}]}, {"ts": "1738161348.533949", "text": "I understand we don’t support it, but I’m trying to think of a workaround", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738090837.658679", "blocks": [{"type": "rich_text", "block_id": "U3kwh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I understand we don’t support it, but I’m trying to think of a workaround"}]}]}]}, {"ts": "1738161417.011729", "text": "Changing hire date is not a good idea. It confuses the users about tenure ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738090837.658679", "blocks": [{"type": "rich_text", "block_id": "8UgKL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Changing hire date is not a good idea. It confuses the users about tenure "}]}]}]}, {"ts": "1738170312.703189", "text": "I think this is going to be the only available solution. Their adjustment letters won't have the correct information otherwise. I agree it's not ideal. I have been through every nuance of the system with them but they continue to have things come up with data that are difficult to get around.\n\nAlso they are doing a full Workday implementation right now, which is why their efforts are not on this cycle.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738090837.658679", "blocks": [{"type": "rich_text", "block_id": "Ki3/1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think this is going to be the only available solution. Their adjustment letters won't have the correct information otherwise. I agree it's not ideal. I have been through every nuance of the system with them but they continue to have things come up with data that are difficult to get around.\n\nAlso they are doing a full Workday implementation right now, which is why their efforts are not on this cycle."}]}]}]}, {"ts": "1738170491.219609", "text": "<@U07EJ2LP44S> Why does Adjustment letters will look wrong? They can always edit the adjustments to prorated value calculating offline.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738090837.658679", "blocks": [{"type": "rich_text", "block_id": "V8oZv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Why does Adjustment letters will look wrong? They can always edit the adjustments to prorated value calculating offline."}]}]}]}, {"ts": "1738170530.202359", "text": "based on last raise date. System is not going to restrict editing the adjustments as of today.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738090837.658679", "blocks": [{"type": "rich_text", "block_id": "qd2U2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "based on last raise date. System is not going to restrict editing the adjustments as of today."}]}]}]}, {"ts": "1738170549.175729", "text": "Not sure if I understood the usecase correctly", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738090837.658679", "edited": {"user": "U0690EB5JE5", "ts": "1738170562.000000"}, "blocks": [{"type": "rich_text", "block_id": "+6yGu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Not sure if I understood the usecase correctly"}]}]}]}, {"ts": "1738170849.874429", "text": "Do you mean manually changing the increase for every prorated employee? Outside of the manual effort of that, the main issue is when they export back to their system, it won't have the full merit increase reflected. There's a ton of room for error when they have to track multiple exports and manual changes.\n\nThe use case is that they only use proration based on last raise date. They will exclude complete on hire date, so that's not a major issue. But they won't be able to use proration in the way they have been for years now, so it will confuse everyone if they don't have a way to do that. And manual changes for ~100 people isn't a great solution either.\n\nUltimately we can give them a couple options and they can choose what to do. It's just a big cycle and every workaround to this issue is a challenge.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738090837.658679", "blocks": [{"type": "rich_text", "block_id": "C1md5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you mean manually changing the increase for every prorated employee? Outside of the manual effort of that, the main issue is when they export back to their system, it won't have the full merit increase reflected. There's a ton of room for error when they have to track multiple exports and manual changes.\n\nThe use case is that they only use proration based on last raise date. They will exclude complete on hire date, so that's not a major issue. But they won't be able to use proration in the way they have been for years now, so it will confuse everyone if they don't have a way to do that. And manual changes for ~100 people isn't a great solution either.\n\nUltimately we can give them a couple options and they can choose what to do. It's just a big cycle and every workaround to this issue is a challenge."}]}]}]}, {"ts": "1738170990.771149", "text": "ok. Lets hear from them.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738090837.658679", "blocks": [{"type": "rich_text", "block_id": "DDyc5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok. Lets hear from them."}]}]}]}, {"ts": "1738171303.696549", "text": "In the worst case as a last resort I will check the effort and impact of this change tomorrow. Please do not commit to customer until then.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738090837.658679", "blocks": [{"type": "rich_text", "block_id": "cayLG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In the worst case as a last resort I will check the effort and impact of this change tomorrow. Please do not commit to customer until then."}]}]}]}, {"ts": "1738172602.176509", "text": "I won't! I have a call with them this afternoon to try and get additional clarification", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738090837.658679", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "6TWdl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I won't! I have a call with them this afternoon to try and get additional clarification"}]}]}]}, {"ts": "1738186870.780019", "text": "Ok I think we're good here. I got on with <PERSON> (who I think doesn't really understand what proration is) and his payroll manager, and they are ok using our current proration. They will exclude anyone hired after 9/1 AND exclude anyone with a raise after 9/1. They will prorate from 1/1/24 for anyone hired before 9/1. That should all work fine. I think <PERSON> just got confused about what he was asking for.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738090837.658679", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nq2xt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok I think we're good here. I got on with <PERSON> (who I think doesn't really understand what proration is) and his payroll manager, and they are ok using our current proration. They will exclude anyone hired after 9/1 AND exclude anyone with a raise after 9/1. They will prorate from 1/1/24 for anyone hired before 9/1. That should all work fine. I think <PERSON> just got confused about what he was asking for."}]}]}]}], "created_at": "2025-05-22T22:00:49.347207"}