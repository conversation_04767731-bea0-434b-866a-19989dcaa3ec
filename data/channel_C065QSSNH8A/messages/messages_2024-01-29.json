{"date": "2024-01-29", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1706497447.757859", "text": "Priorities for Eng for the next day:\n• For Neuroflow: Ensure we are able to generate adjustment letters from formatted HTML with images (logo &amp; signature will be included, <https://docs.google.com/document/d/1I9JL5r4vtGi8UFMxAaeaXHzGOjsoSjQO/edit?usp=sharing&amp;ouid=107994932584597228039&amp;rtpof=true&amp;sd=true|see example>)\n• :repeat: SDF: Allow inputting new titles for promotions (<https://compiify.atlassian.net/browse/COM-2198|COM-2198>) - In development - *ETA 1/26*\n• :repeat: Fix adjustment letter variables (<https://compiify.atlassian.net/browse/COM-2224|COM-2224>)\n• :repeat: SDF / DA / Neuroflow: Date drift in cycle steps (<https://compiify.atlassian.net/browse/COM-2223|COM-2223>)\n• :repeat: Currency conversion issues (<https://compiify.atlassian.net/browse/COM-1919|COM-1919> and <https://compiify.atlassian.net/browse/COM-2142|COM-2142>)\n• :repeat: Enable comment (view/add) on \"Reviewed\" rows (<https://compiify.atlassian.net/browse/COM-2140|COM-2140>)\n• :repeat: Promotion job-title display issue (<https://compiify.atlassian.net/browse/COM-2212|COM-2212>)\n• :repeat: Export filename bug (<https://compiify.atlassian.net/browse/COM-2213|COM-2213>)\nAfter this continue with the rest of <https://compiify.atlassian.net/browse/COM-2087|Wave 3> from top to bottom.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706497447.757859", "reply_count": 6, "edited": {"user": "U065H3M6WJV", "ts": "1706497457.000000"}, "blocks": [{"type": "rich_text", "block_id": "xYjF5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For Neuroflow: Ensure we are able to generate adjustment letters from formatted HTML with images (logo & signature will be included, "}, {"type": "link", "url": "https://docs.google.com/document/d/1I9JL5r4vtGi8UFMxAaeaXHzGOjsoSjQO/edit?usp=sharing&ouid=107994932584597228039&rtpof=true&sd=true", "text": "see example"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " SDF: Allow inputting new titles for promotions ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2198", "text": "COM-2198"}, {"type": "text", "text": ") - In development - "}, {"type": "text", "text": "ETA 1/26", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Fix adjustment letter variables ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2224", "text": "COM-2224"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " SDF / DA / Neuroflow: Date drift in cycle steps ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2223", "text": "COM-2223"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Currency conversion issues ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1919", "text": "COM-1919"}, {"type": "text", "text": " and "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2142", "text": "COM-2142"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Enable comment (view/add) on \"Reviewed\" rows ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2140", "text": "COM-2140"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Promotion job-title display issue ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2212", "text": "COM-2212"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Export filename bug ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2213", "text": "COM-2213"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAfter this continue with the rest of "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " from top to bottom."}]}]}]}], "created_at": "2025-05-22T21:35:34.587300"}