{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2024-04", "message_count": 46, "messages": [{"ts": "1712252969.543469", "text": "<@U0690EB5JE5> What process do you want to use for removing tickets from the Eng Priorities board once they're done? (I've been moving the status to 'Done' once I can verify them in a test env, do you want to remove them entirely from the board at that point, or wait til they've been pushed to a production env?)", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "madWv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " What process do you want to use for removing tickets from the Eng Priorities board once they're done? (I've been moving the status to 'Done' once I can verify them in a test env, do you want to remove them entirely from the board at that point, or wait til they've been pushed to a production env?)"}]}]}]}, {"ts": "1712253036.887029", "text": "<@U065H3M6WJV> I think we can remove entirely. Also, We can setup the board to have only tickets closed in last week.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1712253036.887029", "reply_count": 3, "edited": {"user": "U0690EB5JE5", "ts": "1712253048.000000"}, "reactions": [{"name": "bulb", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "wpypR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I think we can remove entirely. Also, We can setup the board to have only tickets closed in last week."}]}]}]}, {"ts": "1712592615.482349", "text": "I'll need about 5 min break before the next call :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "enc3T", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'll need about 5 min break before the next call "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1712592640.951729", "text": "Let's meet at 9:15", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Jbd6P", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let's meet at 9:15"}]}]}]}, {"ts": "1712602325.375849", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> Any idea why I'm getting an \"Internal Error\" when I attempt a local login (admin@cfy) on <http://da.compiify.com|da.compiify.com> ?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1712602325.375849", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "r2ZSH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Any idea why I'm getting an \"Internal Error\" when I attempt a local login (admin@cfy) on "}, {"type": "link", "url": "http://da.compiify.com", "text": "da.compiify.com"}, {"type": "text", "text": " ?"}]}]}]}, {"ts": "1712619655.582949", "text": "<!here> neuroflow cloud resources will be permanently delete during this week.", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV", "U04DS2MBWP4"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "PkLsW", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " neuroflow cloud resources will be permanently delete during this week."}]}]}]}, {"ts": "1712682960.270729", "text": "We've received <https://drive.google.com/drive/u/0/folders/1Exiboz3bjOVWsqgoEaGpZnAfZiV6SweR|data from Practifi >for their fall cycle. <@U04DKEFP1K8> will you or <@U0690EB5JE5> be on point for this customer implementation?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1712682960.270729", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "bMNTh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We've received "}, {"type": "link", "url": "https://drive.google.com/drive/u/0/folders/1Exiboz3bjOVWsqgoEaGpZnAfZiV6SweR", "text": "data from Practifi "}, {"type": "text", "text": "for their fall cycle. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " will you or "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " be on point for this customer implementation?"}]}]}]}, {"ts": "1712780692.023749", "text": "<@U065H3M6WJV> <PERSON><PERSON><PERSON> and me are targeting to complete backend development scoping for Merit View 2.0  in this week. We will need updated frontend workflow by middle of next week.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1712780692.023749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "qaz6G", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> and me are targeting to complete backend development scoping for Merit View 2.0  in this week. We will need updated frontend workflow by middle of next week."}]}]}]}, {"ts": "1712813790.378669", "text": "hey <@U04DS2MBWP4> do you have otterpilot recording from the pay bands discussion on tuesday?", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "uGonr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "hey "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " do you have otterpilot recording from the pay bands discussion on tuesday?"}]}]}]}, {"ts": "1712872862.169409", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> Nauto uses Namely and according to <https://help.merge.dev/en/articles/5758382-integration-onboarding-characterization|this Merge page> it seems like this might be one of the simpler authentication types. Would there be a way for us to provide Nauto with the newer integration page and see if they can connect? (Is there any risk of it wiping out any of their current data/settings unintentionally?)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1712872862.169409", "reply_count": 7, "attachments": [{"image_url": "https://downloads.intercomcdn.com/i/o/379082/107841514fe57a3f0b2a855a/065e08fde79f8a1ee25eb6318655bdc9.jpg", "image_width": 1200, "image_height": 628, "image_bytes": 74161, "from_url": "https://help.merge.dev/en/articles/5758382-integration-onboarding-characterization", "id": 1, "original_url": "https://help.merge.dev/en/articles/5758382-integration-onboarding-characterization", "fallback": "Integration onboarding characterization | Merge Help Center", "text": "Helpful guidance on our offered integrations", "title": "Integration onboarding characterization | Merge Help Center", "title_link": "https://help.merge.dev/en/articles/5758382-integration-onboarding-characterization", "service_name": "help.merge.dev"}], "blocks": [{"type": "rich_text", "block_id": "Gk7uF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " <PERSON><PERSON> uses Namely and according to "}, {"type": "link", "url": "https://help.merge.dev/en/articles/5758382-integration-onboarding-characterization", "text": "this Merge page"}, {"type": "text", "text": " it seems like this might be one of the simpler authentication types. Would there be a way for us to provide Nauto with the newer integration page and see if they can connect? (Is there any risk of it wiping out any of their current data/settings unintentionally?)"}]}]}]}, {"ts": "1712876044.497979", "text": "Per the discussion earlier today, I pinged both our contacts at Rightway to see if they're able to get us connected to their ADP admin sooner. I got an autoreply saying <PERSON> is on maternity leave already, so it's a low likelihood but I did copy <PERSON><PERSON><PERSON> on her team as well.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1712876044.497979", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "QaG/n", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Per the discussion earlier today, I pinged both our contacts at Rightway to see if they're able to get us connected to their ADP admin sooner. I got an autoreply saying <PERSON> is on maternity leave already, so it's a low likelihood but I did copy <PERSON><PERSON><PERSON> on her team as well."}]}]}]}, {"ts": "1712883236.136299", "text": "<!here> all the UAT tasks have been linked under a parent jira <https://compiify.atlassian.net/browse/COM-2695>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12756::24660610f86711ee98bdadf1b74e41eb", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2695?atlOrigin=eyJpIjoiN2Q4ODJkNTA1NWFhNDY0MmE0Yzc3MDBiMzU1NjcyNjUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2695 Customer UAT task list>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12756::24662d21f86711ee98bdadf1b74e41eb", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12756::24662d20f86711ee98bdadf1b74e41eb", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12756\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12756\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2695", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "raDP5", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " all the UAT tasks have been linked under a parent jira "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2695"}]}]}]}, {"ts": "1712935678.644319", "text": "<@U0690EB5JE5> I think you were going to move the Friday's weekly demo meetings to a different day so that <PERSON><PERSON><PERSON><PERSON> can also join. Is it still happening today?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nI+ps", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I think you were going to move the Friday's weekly demo meetings to a different day so that <PERSON><PERSON><PERSON><PERSON> can also join. Is it still happening today?"}]}]}]}, {"ts": "1712935709.624899", "text": "<@U04DS2MBWP4> that was only last week's instance.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "k5Loi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " that was only last week's instance."}]}]}]}, {"ts": "1712935738.798839", "text": "ok", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1712935739.394989", "text": "I can move it to Monday if required.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "La9QA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can move it to Monday if required."}]}]}]}, {"ts": "1712935752.270409", "text": "I am good with Friday", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "wKPuM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am good with Friday"}]}]}]}, {"ts": "1712947086.319079", "text": "<@U04DKEFP1K8> <PERSON> has provided the updated performance ratings, the <https://docs.google.com/spreadsheets/d/1_Wb3KOYilNRKIbPm7_D92-gyzN1yoiC5/edit?usp=drive_link&amp;ouid=107994932584597228039&amp;rtpof=true&amp;sd=true|spreadsheet is here> in their folder. I also included a column with the \"numeric\" rating so that we can have a consistent order for sorting, but the expectation would be that the text version is what appears in a downloadable report.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1712947086.319079", "reply_count": 19, "edited": {"user": "U065H3M6WJV", "ts": "1712951052.000000"}, "reactions": [{"name": "partying_face", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "guRmF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " <PERSON> has provided the updated performance ratings, the "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1_Wb3KOYilNRKIbPm7_D92-gyzN1yoiC5/edit?usp=drive_link&ouid=107994932584597228039&rtpof=true&sd=true", "text": "spreadsheet is here"}, {"type": "text", "text": " in their folder. I also included a column with the \"numeric\" rating so that we can have a consistent order for sorting, but the expectation would be that the text version is what appears in a downloadable report."}]}]}]}, {"ts": "1712966713.989319", "text": "<@U065H3M6WJV> i have finished interviewing all the intern candidates, have added my feedback in Column L <https://docs.google.com/spreadsheets/d/1X0p8oP3KYBKIBtrPvfYpGKpogEloj7-oLKAOK5smPeY/edit#gid=787511326>. Let meet on monday or tuesday to finalize who we can send out an initial offer.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1712966713.989319", "reply_count": 2, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "IHvp2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " i have finished interviewing all the intern candidates, have added my feedback in Column L "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1X0p8oP3KYBKIBtrPvfYpGKpogEloj7-oLKAOK5smPeY/edit#gid=787511326"}, {"type": "text", "text": ". Let meet on monday or tuesday to finalize who we can send out an initial offer."}]}]}]}, {"ts": "1713220217.673779", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> Based on this morning's discussions, I've started the doc for <https://docs.google.com/document/d/1R6UqowpVM15BBLW0H3_9CPC89nuCHdMtdIlmhGLOJfs/edit#heading=h.je216lwhkaxu|Pay Bands requirements> here. I haven't yet completed the requirements for Search capabilities, but the new format for navigation &amp; visual for employee count are things we can already start implementing. Let me know if you have any questions.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713220217.673779", "reply_count": 1, "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "files": [{"id": "F06V3JEF4PJ", "created": 1713220221, "timestamp": 1713220221, "name": "Pay Bands v1 completion", "title": "Pay Bands v1 completion", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1R6UqowpVM15BBLW0H3_9CPC89nuCHdMtdIlmhGLOJfs", "external_url": "https://docs.google.com/document/d/1R6UqowpVM15BBLW0H3_9CPC89nuCHdMtdIlmhGLOJfs/edit#heading=h.je216lwhkaxu", "url_private": "https://docs.google.com/document/d/1R6UqowpVM15BBLW0H3_9CPC89nuCHdMtdIlmhGLOJfs/edit#heading=h.je216lwhkaxu", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSOc8MB+FADd2H5UNj/wCvSDJP3qADDf3h+VOpMH1owfWgBaKKKAEY47Z/GkU57AUrDJ6ZpMH0FADqKbg/5NL83tQAtFA96KAGtSbvf9KcxHQikGMen40AGT6/pRk+opePX9aOPX9aAFooFFAH/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06V3JEF4PJ/pay_bands_v1_completion", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ntrMS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Based on this morning's discussions, I've started the doc for "}, {"type": "link", "url": "https://docs.google.com/document/d/1R6UqowpVM15BBLW0H3_9CPC89nuCHdMtdIlmhGLOJfs/edit#heading=h.je216lwhkaxu", "text": "Pay Bands requirements"}, {"type": "text", "text": " here. I haven't yet completed the requirements for Search capabilities, but the new format for navigation & visual for employee count are things we can already start implementing. Let me know if you have any questions."}]}]}]}, {"ts": "1713309339.970349", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I finally had some time to step through the calculation formulas <PERSON> documented in COM-2524. I have a bunch of questions, maybe we can start with async review and cover it live if needed. My questions are here in the bold &amp; green text: <https://docs.google.com/document/d/1k84xWPsTYasi0njEgAlEdyVaVgmNuQRJF5Kq0Cihynw/edit|Compiify formulas>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713309339.970349", "reply_count": 2, "files": [{"id": "F06UFAGF93Q", "created": 1713309341, "timestamp": 1713309341, "name": "Compiify formulas [COM-2524]", "title": "Compiify formulas [COM-2524]", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 148367, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1k84xWPsTYasi0njEgAlEdyVaVgmNuQRJF5Kq0Cihynw", "external_url": "https://docs.google.com/document/d/1k84xWPsTYasi0njEgAlEdyVaVgmNuQRJF5Kq0Cihynw/edit", "url_private": "https://docs.google.com/document/d/1k84xWPsTYasi0njEgAlEdyVaVgmNuQRJF5Kq0Cihynw/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06UFAGF93Q-7c91731609/compiify_formulas__com-2524__1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTSOfek59TSt05pvy+9ADhx3paYCvvT6ACiiigBD0pvH92nHOOKTDetABx6UuPYUc+1HPtQAoooooAQ9OmaTH+z+tONJk0AJj/Z/WnUmTQM0ALRRRQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06UFAGF93Q/compiify_formulas__com-2524_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "QoH7f", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I finally had some time to step through the calculation formulas <PERSON> documented in COM-2524. I have a bunch of questions, maybe we can start with async review and cover it live if needed. My questions are here in the bold & green text: "}, {"type": "link", "url": "https://docs.google.com/document/d/1k84xWPsTYasi0njEgAlEdyVaVgmNuQRJF5Kq0Cihynw/edit", "text": "Compiify formulas"}]}]}]}, {"ts": "1713351457.340209", "text": "<@U065H3M6WJV> nauto-test ENV is ready for integration", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1712872862.169409", "subtype": "thread_broadcast", "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}, {"name": "birthday_party_parrot", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F06VDCY53NU", "created": 1713351452, "timestamp": 1713351452, "name": "Screenshot 2024-04-17 at 4.26.19 PM.png", "title": "Screenshot 2024-04-17 at 4.26.19 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 186663, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06VDCY53NU/screenshot_2024-04-17_at_4.26.19___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06VDCY53NU/download/screenshot_2024-04-17_at_4.26.19___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 194, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 259, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 388, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 432, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 518, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06VDCY53NU-1393d2f0d1/screenshot_2024-04-17_at_4.26.19___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 552, "original_w": 1917, "original_h": 1034, "thumb_tiny": "AwAZADBn2qf/AJ6H8hR9qn/vn9KhpaALcLXEylhMFAOOaJmuIVDGYMCccVJp/Mbf739KNR4iT/e/pQBV+1T/AN8/pR9qn/vn8hUVFACUtFFAFm2uRAhG0HJz1xSXNz56AbQuD65qvRQAUUUUAf/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06VDCY53NU/screenshot_2024-04-17_at_4.26.19___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06VDCY53NU-698eb6401b", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "F+3/I", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " nauto-test ENV is ready for integration"}]}]}]}, {"ts": "1713400021.744049", "text": "<@U04DKEFP1K8> I recorded the call with Delaney &amp; <PERSON> - got some more clarification on equity, but also expect <PERSON> will re-confirm details with us next week.\n• Sounds like they do follow a traditional vesting \"cliff\"\n• <PERSON> would like managers to see equity info during the cycle, but expects only exec level leaders to input values\n• They may want to include refresh equity in addition to promotion equity this cycle\n• No established budget for the equity components\n<https://us06web.zoom.us/rec/share/rKFupICa0VCglNqXu7WKIsq5ZbtE66vxCETLS30yt-K0d2fw95sMxyZNAsKXpdoC.8H26lMOLOl633aZk|Zoom recording> / Passcode: *q52i8lg$*\n_Note that there is customer data displayed during the call, so do not share with anyone who is not a full time Compiify employee_", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713400021.744049", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "HBmyx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I recorded the call with Delaney & Linda - got some more clarification on equity, but also expect <PERSON> will re-confirm details with us next week.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sounds like they do follow a traditional vesting \"cliff\""}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> would like managers to see equity info during the cycle, but expects only exec level leaders to input values"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "They may want to include refresh equity in addition to promotion equity this cycle"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "No established budget for the equity components"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "link", "url": "https://us06web.zoom.us/rec/share/rKFupICa0VCglNqXu7WKIsq5ZbtE66vxCETLS30yt-K0d2fw95sMxyZNAsKXpdoC.8H26lMOLOl633aZk", "text": "Zoom recording"}, {"type": "text", "text": " / Passcode: "}, {"type": "text", "text": "q52i8lg$", "style": {"bold": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Note that there is customer data displayed during the call, so do not share with anyone who is not a full time Compiify employee", "style": {"italic": true}}]}]}]}, {"ts": "1713413266.969799", "text": "<@U065H3M6WJV> <@U0690EB5JE5> are you both available at 815am pst / 845pm ist for eng recurring meeting?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1713413266.969799", "reply_count": 2, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "PcswJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " are you both available at 815am pst / 845pm ist for eng recurring meeting"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1713458418.632949", "text": "Recording link for today's call is now in the <https://docs.google.com/document/d/1sRICN3PCrHRh4Wgk2O1Qw5CF8tCYgBnwe5S_WBBgNQI/edit|Eng discussions doc>.", "user": "U065H3M6WJV", "type": "message", "edited": {"user": "U065H3M6WJV", "ts": "1713458424.000000"}, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "SU0L7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Recording link for today's call is now in the "}, {"type": "link", "url": "https://docs.google.com/document/d/1sRICN3PCrHRh4Wgk2O1Qw5CF8tCYgBnwe5S_WBBgNQI/edit", "text": "Eng discussions doc"}, {"type": "text", "text": "."}]}]}]}, {"ts": "**********.052059", "text": "<@U0690EB5JE5> We had an onboarding call today with Vercara and they are using Trinet for their HRIS. What do I need to request from them to enable an integration?\n\nI know we had requested an API key from SDF, but according to <https://help.merge.dev/en/articles/5708552-trinet-how-do-i-link-my-account|this article from Merge>, it seems like generating the client ID &amp; secret requires them to select an integration type within Trinet first. Do we know what selection they should make?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.052059", "reply_count": 2, "attachments": [{"image_url": "https://downloads.intercomcdn.com/i/o/379082/107841514fe57a3f0b2a855a/065e08fde79f8a1ee25eb6318655bdc9.jpg", "image_width": 1200, "image_height": 628, "image_bytes": 74161, "from_url": "https://help.merge.dev/en/articles/5708552-trinet-how-do-i-link-my-account", "id": 1, "original_url": "https://help.merge.dev/en/articles/5708552-trinet-how-do-i-link-my-account", "fallback": "TriNet - How do I link my account? | Merge Help Center", "text": "How to link your TriNet account to Merge", "title": "TriNet - How do I link my account? | Merge Help Center", "title_link": "https://help.merge.dev/en/articles/5708552-trinet-how-do-i-link-my-account", "service_name": "help.merge.dev"}], "blocks": [{"type": "rich_text", "block_id": "cx4/K", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We had an onboarding call today with Vercara and they are using Trinet for their HRIS. What do I need to request from them to enable an integration?\n\nI know we had requested an API key from SDF, but according to "}, {"type": "link", "url": "https://help.merge.dev/en/articles/5708552-trinet-how-do-i-link-my-account", "text": "this article from Merge"}, {"type": "text", "text": ", it seems like generating the client ID & secret requires them to select an integration type within Trinet first. Do we know what selection they should make?"}]}]}]}, {"ts": "**********.651849", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> For Vercara, they do not have an upcoming cycle that they are targeting yet, they just want to get their environment loaded ASAP so they can start understanding the platform and using the built-in reports.\n\n• They use Microsoft for SSO. We could try enabling Microsoft through Auth0, or could start them off with local login first if we need more time to finish testing Auth0 changes.\n• Their main HRIS is TriNet, so I assume we want to try using an integration for the initial data load. (relevant to the question above about getting credentials)\n• This is the customer who requested a customization to allow them to attach a PDF document of goals to every employee. Previously <https://compiify.slack.com/archives/C06GV6YM6VA/p1713197566999449?thread_ts=**********.104579&amp;cid=C06GV6YM6VA|Mahesh estimated> this is about 1 week of effort (of course we need to define the requirements first), so we will want to provide them a rough ETA for being able to support that -- the sooner, the better. \n• Our target for having data loaded is *Friday, April 26th* so that we can give them a walkthrough on Monday, April 29th with their data in their account. Please let me know if you foresee any issues hitting that timeline!\nThis is our first paying customer so let's make sure we give them an excellent experience!", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.651849", "reply_count": 11, "blocks": [{"type": "rich_text", "block_id": "2SGfF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " For Vercara, they do not have an upcoming cycle that they are targeting yet, they just want to get their environment loaded ASAP so they can start understanding the platform and using the built-in reports.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They use Microsoft for SSO. We could try enabling Microsoft through Auth0, or could start them off with local login first if we need more time to finish testing Auth0 changes."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Their main HRIS is TriNet, so I assume we want to try using an integration for the initial data load. (relevant to the question above about getting credentials)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "This is the customer who requested a customization to allow them to attach a PDF document of goals to every employee. Previously "}, {"type": "link", "url": "https://compiify.slack.com/archives/C06GV6YM6VA/p1713197566999449?thread_ts=**********.104579&cid=C06GV6YM6VA", "text": "<PERSON><PERSON><PERSON> estimated"}, {"type": "text", "text": " this is about 1 week of effort (of course we need to define the requirements first), so we will want to provide them a rough ETA for being able to support that -- the sooner, the better. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Our target for having data loaded is "}, {"type": "text", "text": "Friday, April 26th", "style": {"bold": true}}, {"type": "text", "text": " so that we can give them a walkthrough on Monday, April 29th with their data in their account. Please let me know if you foresee any issues hitting that timeline!"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nThis is our first paying customer so let's make sure we give them an excellent experience!"}]}]}]}, {"ts": "**********.088239", "text": "<@U04DKEFP1K8> here are all the assets for the all the pages of website for Gaurav to use:\n<https://drive.google.com/drive/folders/17a3-QKU3t1mF9NlPJ9InwTpuK4X2c2iU?usp=drive_link>\n\nHere are all the Figma designs for the website. Let me know if you don't have access to this Figma folder for Stride\n<https://www.figma.com/files/project/*********/Stride?fuid=1170850046975729676>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.088239", "reply_count": 2, "edited": {"user": "U04DS2MBWP4", "ts": "**********.000000"}, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "A98O4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " here are all the assets for the all the pages of website for Gaurav to use:\n"}, {"type": "link", "url": "https://drive.google.com/drive/folders/17a3-QKU3t1mF9NlPJ9InwTpuK4X2c2iU?usp=drive_link"}, {"type": "text", "text": "\n\nHere are all the Figma designs for the website. Let me know if you don't have access to this Figma folder for Stride\n"}, {"type": "link", "url": "https://www.figma.com/files/project/*********/Stride?fuid=1170850046975729676"}]}]}]}, {"ts": "1713540756.454589", "text": "Demo meeting link\n<https://us06web.zoom.us/j/88688933175?pwd=V0DgBg5Gw7jXjgh6raW36aOcbdcNbP.1>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Zs3JW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Demo meeting link\n"}, {"type": "link", "url": "https://us06web.zoom.us/j/88688933175?pwd=V0DgBg5Gw7jXjgh6raW36aOcbdcNbP.1"}]}]}]}, {"ts": "1713540759.191169", "text": "<!here>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mK6AI", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}]}]}]}, {"ts": "1713553797.605989", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I've started a <https://docs.google.com/document/d/1wmIr7jMwCUvudn5mTDIIlb6JbwiYQ9oQxFPNYLGhvv4/edit|PRD for the document customization> <PERSON><PERSON><PERSON><PERSON> has requested. The first major question where I need your input, is how to think about the tradeoffs between making this very specifically about a single \"goal\" document for every employee, vs. designing something a bit more generalized for \"employee documents.\"", "user": "U065H3M6WJV", "type": "message", "edited": {"user": "U065H3M6WJV", "ts": "1713553803.000000"}, "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "/90Wb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I've started a "}, {"type": "link", "url": "https://docs.google.com/document/d/1wmIr7jMwCUvudn5mTDIIlb6JbwiYQ9oQxFPNYLGhvv4/edit", "text": "PRD for the document customization"}, {"type": "text", "text": " <PERSON><PERSON><PERSON><PERSON> has requested. The first major question where I need your input, is how to think about the tradeoffs between making this very specifically about a single \"goal\" document for every employee, vs. designing something a bit more generalized for \"employee documents.\""}]}]}]}, {"ts": "1713726096.738279", "text": "<@U065H3M6WJV> I have merged all the jira's from different UAT as well as few other tasks like social login which are pending. Here is the link to the file <https://docs.google.com/spreadsheets/d/1NxksYgZAwiSREK0LDf6Ywmu5lsg62qLp/edit?usp=sharing&amp;ouid=113251126259695967737&amp;rtpof=true&amp;sd=true>\n\nCan you please review it ( especially the ones which are in to do state) and may be add a new column to define updated priority based on your analysis?\n\n<@U0690EB5JE5> Once <PERSON>'s reviews is complete, we can distribute them to the team ( at least critical and high priority ones).", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1713726096.738279", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "D/TGp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I have merged all the jira's from different UAT as well as few other tasks like social login which are pending. Here is the link to the file "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1NxksYgZAwiSREK0LDf6Ywmu5lsg62qLp/edit?usp=sharing&ouid=113251126259695967737&rtpof=true&sd=true"}, {"type": "text", "text": "\n\nCan you please review it ( especially the ones which are in to do state) and may be add a new column to define updated priority based on your analysis?\n\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Once <PERSON>'s reviews is complete, we can distribute them to the team ( at least critical and high priority ones)."}]}]}]}, {"ts": "1713820062.053239", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> The load time for HR Admin and Merit &gt; organization subtabs seem to have increased on the test environment. Do we know what's affecting that performance?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713820062.053239", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "pHAGx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " The load time for HR Admin and Merit > organization subtabs seem to have increased on the test environment. Do we know what's affecting that performance?"}]}]}]}, {"ts": "1713895994.454079", "text": "Quick update on <https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit|Merit 2.0 designs>: I dropped these in a doc just so it's easier review &amp; comment. Let me know if you have questions -- my hope was that we could get started on the new \"manager view\" sooner, while we iron out the remaining questions for the \"manager of managers\" view", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713895994.454079", "reply_count": 1, "files": [{"id": "F07033J43GE", "created": 1713895996, "timestamp": 1713895996, "name": "Merit 2.0 designs (in progress)", "title": "Merit 2.0 designs (in progress)", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10", "external_url": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit", "url_private": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSJ+v4Ckz9fyobGRnNIMepoAeDmimYH96l+X1oAdRQOlFACEA0mB6CnGkFACYHpR0FLRQAA5GaWgUUAITikyP8ilLAGkDA+tABuH+RS5+tJlfWgbQetADhRQDmigD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07033J43GE/merit_2", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "qEIqE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Quick update on "}, {"type": "link", "url": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit", "text": "Merit 2.0 designs"}, {"type": "text", "text": ": I dropped these in a doc just so it's easier review & comment. Let me know if you have questions -- my hope was that we could get started on the new \"manager view\" sooner, while we iron out the remaining questions for the \"manager of managers\" view"}]}]}]}, {"ts": "1713980571.005139", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I have a few items from PlayQ that we need to capture, what's the format we agreed to use now -- should I create an Epic for \"PlayQ UAT\" or capture this in an existing epic or ticket?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713980571.005139", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "NsPyc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I have a few items from PlayQ that we need to capture, what's the format we agreed to use now -- should I create an Epic for \"PlayQ UAT\" or capture this in an existing epic or ticket?"}]}]}]}, {"ts": "1713985059.638749", "text": "<@U0690EB5JE5> <@U065H3M6WJV> Did you both received an email from Payroll Services &lt;<mailto:<EMAIL>|<EMAIL>>&gt; with subject line: TriNet Integration Center - Compiify?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1713985059.638749", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "nGyoR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Did you both received an email from Payroll Services <"}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": "> with subject line: TriNet Integration Center - Compiify?"}]}]}]}, {"ts": "1713994244.339869", "text": "Our first live customer integration! <PERSON> from Nauto went through the process to connect with <PERSON><PERSON> during today's call. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713994244.339869", "reply_count": 9, "reactions": [{"name": "tada", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F070WLKKDUZ", "created": 1713994216, "timestamp": 1713994216, "name": "Screenshot 2024-04-24 at 2.10.33 PM.png", "title": "Screenshot 2024-04-24 at 2.10.33 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 472433, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F070WLKKDUZ/screenshot_2024-04-24_at_2.10.33___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F070WLKKDUZ/download/screenshot_2024-04-24_at_2.10.33___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 222, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 296, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 444, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 494, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 593, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 632, "original_w": 2258, "original_h": 1394, "thumb_tiny": "AwAdADCtS0UUAW7a3ikg3uGJzjipvskBBwrcepNFhxbf8CNTgkodwAPtQBj0UlLQAUUUUAaVh/x7/wDAjVhvun6VjbvlAIzj3IpQ4DAheR/tH/GgBlLRRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F070WLKKDUZ/screenshot_2024-04-24_at_2.10.33___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F070WLKKDUZ-b32dfe5053", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "yllpV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Our first live customer integration! <PERSON> from Nauto went through the process to connect with <PERSON><PERSON> during today's call. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1714008737.438029", "text": "This is a very big milestone for us. Our first integration going live. Congratulations everyone. :tada: :raised_hands: ", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1713994244.339869", "subtype": "thread_broadcast", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "OEn8+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is a very big milestone for us. Our first integration going live. Congratulations everyone. "}, {"type": "emoji", "name": "tada", "unicode": "1f389"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "raised_hands", "unicode": "1f64c"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1714008964.458409", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> We should plan to review customer implementation status / action items in addition to any project updates in the next Eng discussion meeting.\n\nSome things I know we have customers waiting on:\n• *Vercara* - TriNet integration option; we may need to move their walkthrough scheduled for 4/29 if their environment is unlikely to be ready by then. (My guess is they will not be able to provide the incremental data even if we get the initial sync by Friday)\n• *Practifi* - Data load. They uploaded CSV to the Google Drive 4/9, we have had 2 weeks and nothing shared back yet. \n• *PlayQ* - Google (social) login. If this is not likely to be ready by Friday morning (Pacific time zone), let's set up a username/password for <PERSON> for now. \n    ◦ Other items captured in <https://compiify.atlassian.net/browse/COM-2799|PlayQ UAT list>\n• *Nauto* - Equity data load; Namely sync. \n    ◦ Other items captured in <https://compiify.atlassian.net/browse/COM-2357|Nauto UAT list>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1714008964.458409", "reply_count": 45, "blocks": [{"type": "rich_text", "block_id": "xWzy0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We should plan to review customer implementation status / action items in addition to any project updates in the next Eng discussion meeting.\n\nSome things I know we have customers waiting on:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Vercar<PERSON>", "style": {"bold": true}}, {"type": "text", "text": " - TriNet integration option; we may need to move their walkthrough scheduled for 4/29 if their environment is unlikely to be ready by then. (My guess is they will not be able to provide the incremental data even if we get the initial sync by Friday)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON>", "style": {"bold": true}}, {"type": "text", "text": " - Data load. They uploaded CSV to the Google Drive 4/9, we have had 2 weeks and nothing shared back yet. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "PlayQ", "style": {"bold": true}}, {"type": "text", "text": " - Google (social) login. If this is not likely to be ready by Friday morning (Pacific time zone), let's set up a username/password for <PERSON> for now. "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Other items captured in "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2799", "text": "PlayQ UAT list"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON>", "style": {"bold": true}}, {"type": "text", "text": " - Equity data load; Namely sync. "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Other items captured in "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2357", "text": "Nauto UAT list"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}, {"ts": "1714028980.125589", "text": "<@U065H3M6WJV> <@U04DKEFP1K8> Can we discuss this today in the eng discussion?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1713309339.970349", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "X8ncf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can we discuss this today in the eng discussion?"}]}]}]}, {"ts": "1714072442.460179", "text": "Practifi UAT jira: <https://compiify.atlassian.net/browse/COM-2821>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1714072442.460179", "reply_count": 6, "reactions": [{"name": "thankyouty", "users": ["U065H3M6WJV"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12882::fb25d560033711ef94ec6f37b373e51a", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2821?atlOrigin=eyJpIjoiZDJmMzE1NzhiYjZiNDExODhhZDliODU2ZjJmY2FiNzUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2821 Practifi UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12882::fb25d562033711ef94ec6f37b373e51a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12882::fb25d561033711ef94ec6f37b373e51a", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12882\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12882\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2821", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "aTaxf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Practifi UAT jira: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2821"}]}]}]}, {"ts": "1714091417.544729", "text": "Vercara UAT <https://compiify.atlassian.net/browse/COM-2833>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1714091417.544729", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12894::28cb9be0036411ef929db3fba3ff5629", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2833?atlOrigin=eyJpIjoiYTliYTM1MzllNDYwNDcyNGFlOTRiZDc1Y2NjMTFkMzIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2833 Vercara UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12894::28cb9be2036411ef929db3fba3ff5629", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12894::28cb9be1036411ef929db3fba3ff5629", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12894\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12894\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2833", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "rXP9U", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Vercara UAT "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2833"}]}]}]}, {"ts": "1714094026.368249", "text": "<!here> are the shortlisted summer internship candidates\n1. <PERSON><PERSON> <https://drive.google.com/file/d/1zs_gR4ET4rxB7iKY8tEUAg3P74v_sSfq/view?usp=drive_link>\n2. <PERSON><PERSON><PERSON> B<PERSON> <https://drive.google.com/open?id=1-Frv9YyJVcfD9epTZPZRB7MbYUuFU10N>\n3. <PERSON><PERSON><PERSON> <https://drive.google.com/open?id=1kG1d28VpDmZ4oleFWVUVk-7NaLjWdG4->\nAlso <PERSON> was offered the internship but he declined ( after initially accepting it )", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "lMh5Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " are the shortlisted summer internship candidates\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>-G<PERSON>ri "}, {"type": "link", "url": "https://drive.google.com/file/d/1zs_gR4ET4rxB7iKY8tEUAg3P74v_sSfq/view?usp=drive_link"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> "}, {"type": "link", "url": "https://drive.google.com/open?id=1-Frv9YyJVcfD9epTZPZRB7MbYUuFU10N"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> "}, {"type": "link", "url": "https://drive.google.com/open?id=1kG1d28VpDmZ4oleFWVUVk-7NaLjWdG4-"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAlso <PERSON> was offered the internship but he declined ( after initially accepting it )"}]}]}]}, {"ts": "1714369599.808769", "text": "<!here> i am able to access tirnet's api access and secret keys from vercara", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1714369599.808769", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "RuSWs", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " i am able to access tirnet's api access and secret keys from vercara"}]}]}]}, {"ts": "1714395866.063899", "text": "<@U065H3M6WJV> short update\n\nBoth vercara and practific env are ready\n\n• <https://practifi.compiify.com/login>\n• <https://vercara.compiify.com/login>\nEng will populate data for practifi and test integration with api keys from trinet for vercara tomorrow", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1714395866.063899", "reply_count": 4, "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "attachments": [{"from_url": "https://practifi.compiify.com/login", "service_icon": "https://practifi.compiify.com/apple-touch-icon.png", "id": 1, "original_url": "https://practifi.compiify.com/login", "fallback": "Compiify", "text": "Web site created using create-react-app", "title": "Compiify", "title_link": "https://practifi.compiify.com/login", "service_name": "practifi.compiify.com"}, {"from_url": "https://vercara.compiify.com/login", "service_icon": "https://vercara.compiify.com/apple-touch-icon.png", "id": 2, "original_url": "https://vercara.compiify.com/login", "fallback": "Compiify", "text": "Web site created using create-react-app", "title": "Compiify", "title_link": "https://vercara.compiify.com/login", "service_name": "vercara.compiify.com"}], "blocks": [{"type": "rich_text", "block_id": "yWg9k", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " short update\n\nBoth vercara and practific env are ready\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://practifi.compiify.com/login"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://vercara.compiify.com/login"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nEng will populate data for practifi and test integration with api keys from trinet for vercara tomorrow"}]}]}]}, {"ts": "1714418406.258369", "text": "While testing reports &amp; employee edits, I found that the data set on <http://test.compiify.com|test.compiify.com> still had <PERSON><PERSON>'s name for the HR Business Partner and the entity name \"SDF\" for all employees.\n• The good news is I'm able to update those with Mu<PERSON><PERSON>'s bulk upload flow!\n• But, let's please make sure the data set we use when resetting the environment has these SDF-specific items removed. The HRBP should be \"Quan Nguyen\" and entity should be \"Payright\"\n<@U0690EB5JE5> <@U04DKEFP1K8>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1714418406.258369", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "vcRru", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "While testing reports & employee edits, I found that the data set on "}, {"type": "link", "url": "http://test.compiify.com", "text": "test.compiify.com"}, {"type": "text", "text": " still had Katya's name for the HR Business Partner and the entity name \"SDF\" for all employees.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The good news is I'm able to update those with <PERSON><PERSON><PERSON>'s bulk upload flow!"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "But, let's please make sure the data set we use when resetting the environment has these SDF-specific items removed. The HRBP should be \"<PERSON>uan <PERSON>\" and entity should be \"Payright\""}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}]}