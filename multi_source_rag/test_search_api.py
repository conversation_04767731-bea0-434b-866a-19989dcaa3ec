#!/usr/bin/env python
"""
Script to test the search API with different query types.
"""

import os
import sys
import django
import json
import logging
import time
import requests
from datetime import datetime

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk
from apps.search.services.rag_service import RAGService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def print_database_stats():
    """Print database statistics."""
    logger.info("Database Statistics:")
    logger.info(f"Raw Documents: {RawDocument.objects.count()}")
    logger.info(f"Document Chunks: {DocumentChunk.objects.count()}")
    logger.info(f"Document Sources: {DocumentSource.objects.count()}")

def test_search_api_direct():
    """Test the search API directly using the RAGService."""
    logger.info("Testing search API directly using RAGService...")

    # Get the default tenant
    tenant = Tenant.objects.get(slug="default")

    # Get or create a test user
    from django.contrib.auth.models import User
    user, created = User.objects.get_or_create(
        username="test_user",
        defaults={"email": "<EMAIL>", "is_staff": True}
    )

    # Create user profile if it doesn't exist
    from apps.accounts.models import UserProfile
    if not hasattr(user, "profile"):
        UserProfile.objects.create(user=user, tenant=tenant)

    # Create RAG service
    rag_service = RAGService(user=user, tenant_slug=tenant.slug)

    # Define test queries
    test_queries = [
        # Summarization queries
        "Summarize the key discussions about product features",
        "Summarize with details about customer feedback",
        "Provide a summary of technical issues discussed",

        # Time-framed discussion queries
        "What was discussed about the roadmap between January and March?",
        "What feedback did we receive from customers in the last month?",
        "What were the main technical challenges discussed recently?",

        # Feedback extraction queries
        "Extract all feedback about the user interface",
        "What feedback have we received about performance issues?",
        "Summarize customer complaints about reliability",

        # Issue report queries
        "What issues were reported by Amanda about Curana?",
        "List all critical bugs reported in the last month",
        "What are the most common issues reported by users?"
    ]

    # Test each query
    for query in test_queries:
        logger.info(f"\n\n===== Testing query: {query} =====")

        # Start timer
        start_time = time.time()

        try:
            # Execute search
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=10,
                use_hybrid_search=True,
                use_context_aware=True,
                output_format="text",
            )

            # Calculate processing time
            processing_time = time.time() - start_time

            # Log results
            logger.info(f"Query: {query}")
            logger.info(f"Answer: {search_result.generated_answer}")
            logger.info(f"Processing time: {processing_time:.2f}s")
            logger.info(f"Retrieved {len(retrieved_docs)} documents")

            # Log citations
            logger.info("Citations:")
            for i, citation in enumerate(search_result.citations.all().order_by('rank')):
                chunk = citation.document_chunk
                logger.info(f"  {i+1}. {chunk.document.title} - Relevance: {citation.relevance_score:.4f}")
                logger.info(f"     Content: {chunk.text[:100]}...")

        except Exception as e:
            logger.error(f"Error executing search: {str(e)}")

    logger.info("Search API testing completed.")

def test_search_api_http():
    """Test the search API via HTTP requests."""
    logger.info("Testing search API via HTTP requests...")

    # API endpoint
    api_url = "http://localhost:8000/api/search/"

    # Define test queries
    test_queries = [
        # Summarization queries
        "Summarize the key discussions about product features",
        "Summarize with details about customer feedback",
        "Provide a summary of technical issues discussed",

        # Time-framed discussion queries
        "What was discussed about the roadmap between January and March?",
        "What feedback did we receive from customers in the last month?",
        "What were the main technical challenges discussed recently?",

        # Feedback extraction queries
        "Extract all feedback about the user interface",
        "What feedback have we received about performance issues?",
        "Summarize customer complaints about reliability",

        # Issue report queries
        "What issues were reported by Amanda about Curana?",
        "List all critical bugs reported in the last month",
        "What are the most common issues reported by users?"
    ]

    # Test each query
    for query in test_queries:
        logger.info(f"\n\n===== Testing query: {query} =====")

        # Prepare request data
        request_data = {
            "query": query,
            "top_k": 10,
            "use_hybrid_search": True,
            "use_context_aware": True,
            "output_format": "text",
        }

        try:
            # Send request
            response = requests.post(api_url, json=request_data)

            # Check response
            if response.status_code == 200:
                result = response.json()
                logger.info(f"Query: {query}")
                logger.info(f"Answer: {result['data']['answer']}")
                logger.info(f"Processing time: {result['data']['metrics']['processing_time']}")

                # Log sources
                logger.info("Sources:")
                for i, source in enumerate(result['data']['sources']):
                    logger.info(f"  {i+1}. {source['metadata']['title']} - Relevance: {source['relevance']:.4f}")
                    logger.info(f"     Content: {source['text'][:100]}...")
            else:
                logger.error(f"Error: {response.status_code} - {response.text}")

        except Exception as e:
            logger.error(f"Error executing search: {str(e)}")

    logger.info("HTTP API testing completed.")

def main():
    """Main function to test the search API."""
    logger.info("Starting search API testing...")

    # Print database stats
    print_database_stats()

    # Test search API directly
    test_search_api_direct()

    # Test search API via HTTP (uncomment if server is running)
    # test_search_api_http()

    logger.info("Search API testing completed.")

if __name__ == "__main__":
    main()
