{"thread_ts": "1741116837.232479", "channel_id": "C065QSSNH8A", "reply_count": 9, "replies": [{"ts": "1741137659.825929", "text": "Yes this happens whenever a new employee added. ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741116837.232479", "blocks": [{"type": "rich_text", "block_id": "98il2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes this happens whenever a new employee added. "}]}]}]}, {"ts": "1741138471.882869", "text": "Wait, what? <PERSON><PERSON><PERSON><PERSON> stops working when we add a person?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741116837.232479", "blocks": [{"type": "rich_text", "block_id": "h6sSi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Wait, what? <PERSON><PERSON><PERSON><PERSON> stops working when we add a person?"}]}]}]}, {"ts": "1741138496.872869", "text": "only for that employee who is added", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741116837.232479", "blocks": [{"type": "rich_text", "block_id": "KfEKx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "only for that employee who is added"}]}]}]}, {"ts": "1741138517.075609", "text": "for rest it works fine.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741116837.232479", "blocks": [{"type": "rich_text", "block_id": "dipiy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "for rest it works fine."}]}]}]}, {"ts": "1741138535.080459", "text": "No, it wasn’t working  working for anybody", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741116837.232479", "blocks": [{"type": "rich_text", "block_id": "UpB8Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No, it "}, {"type": "text", "text": "wasn’t"}, {"type": "text", "text": " working  working for anybody"}]}]}]}, {"ts": "1741138561.030029", "text": "No salaries were changing in the merit table at all until I resaved ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741116837.232479", "blocks": [{"type": "rich_text", "block_id": "tDvRG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No salaries were changing in the merit table at all until I resaved "}]}]}]}, {"ts": "1741138578.509429", "text": "ok will take a look then", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741116837.232479", "blocks": [{"type": "rich_text", "block_id": "GkfFI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok will take a look then"}]}]}]}, {"ts": "1741138649.616989", "text": "<@U07MH77PUBV> PTAL", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741116837.232479", "blocks": [{"type": "rich_text", "block_id": "uhghc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " PTAL"}]}]}]}, {"ts": "1741140187.144669", "text": "<@U07EJ2LP44S> Org configuration was missing which I guess was causing the issue. I have updated the Org configurations for conversion rates. Lets see if it happens again. <@U07MH77PUBV> Just confirm add an international employee the currency conversion works.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741116837.232479", "blocks": [{"type": "rich_text", "block_id": "5Kdm/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Org configuration was missing which I guess was causing the issue. I have updated the Org configurations for conversion rates. Lets see if it happens again. "}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " Just confirm add an international employee the currency conversion works."}]}]}]}], "created_at": "2025-05-22T22:00:49.369414"}