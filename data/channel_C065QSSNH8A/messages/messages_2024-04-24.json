{"date": "2024-04-24", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1713980571.005139", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I have a few items from PlayQ that we need to capture, what's the format we agreed to use now -- should I create an Epic for \"PlayQ UAT\" or capture this in an existing epic or ticket?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713980571.005139", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "NsPyc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I have a few items from PlayQ that we need to capture, what's the format we agreed to use now -- should I create an Epic for \"PlayQ UAT\" or capture this in an existing epic or ticket?"}]}]}]}], "created_at": "2025-05-22T21:35:34.603215"}