# Generated by Django 4.2.10 on 2025-06-02 12:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("accounts", "0001_initial"),
        ("core", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("documents", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="crossplatformreference",
            name="source_chunk",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="outgoing_references",
                to="documents.documentchunk",
            ),
        ),
        migrations.AddField(
            model_name="crossplatformreference",
            name="target_chunk",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="incoming_references",
                to="documents.documentchunk",
            ),
        ),
        migrations.AddField(
            model_name="crossplatformreference",
            name="tenant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="accounts.tenant"
            ),
        ),
        migrations.AddField(
            model_name="apikey",
            name="tenant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="accounts.tenant"
            ),
        ),
        migrations.AddField(
            model_name="apikey",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="api_keys",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
