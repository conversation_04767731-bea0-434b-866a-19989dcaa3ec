{"date": "2024-11-05", "channel_id": "C065QSSNH8A", "message_count": 8, "messages": [{"ts": "1730821322.254079", "text": "<@U07M6QKHUC9> Can you please confirm if you are still seeing this issue? I don't see N/A in the drop down list of departments. Was there any data update done to <PERSON>ith<PERSON>?\n<https://compiify.atlassian.net/browse/COM-3949>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730821322.254079", "reply_count": 3, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14032::006a0272dce4469598eb1b5baff111aa", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3949?atlOrigin=eyJpIjoiYmM5ZTNhNTc1YWI3NDIzOWJkMTIwNjgzYTkzMjVkNjMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3949 Issue: Inconsistent data display when filtering by Department NA>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14032::019390b1e4814b6f955d5f536d38a216", "elements": [{"type": "mrkdwn", "text": "Status: *In Development*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/3ba050d9f25e3d5164f213ae816bb449?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14032\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3949\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3949", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "DAFE1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Can you please confirm if you are still seeing this issue? I don't see N/A in the drop down list of departments. Was there any data update done to <PERSON><PERSON><PERSON>?\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3949"}]}]}]}, {"ts": "1730820175.030079", "text": "<@U07EJ2LP44S> engineering support for this week", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "files": [{"id": "F07UVCU3JKX", "created": 1730820083, "timestamp": 1730820083, "name": "Screenshot 2024-11-05 at 2.22.10 PM.png", "title": "Screenshot 2024-11-05 at 2.22.10 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 55517, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07UVCU3JKX/screenshot_2024-11-05_at_2.22.10___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07UVCU3JKX/download/screenshot_2024-11-05_at_2.22.10___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UVCU3JKX-ad174baa83/screenshot_2024-11-05_at_2.22.10___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UVCU3JKX-ad174baa83/screenshot_2024-11-05_at_2.22.10___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UVCU3JKX-ad174baa83/screenshot_2024-11-05_at_2.22.10___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 201, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UVCU3JKX-ad174baa83/screenshot_2024-11-05_at_2.22.10___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 267, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UVCU3JKX-ad174baa83/screenshot_2024-11-05_at_2.22.10___pm_160.png", "original_w": 614, "original_h": 342, "thumb_tiny": "AwAaADDRYcDGfwpqlh/Cxz6kU5/u84/EVCNuR9z/AL5NAD8Nk/f/AEpwJ6FD9eKiO3cf9X19DTht3j7mc+hoAmooooAYzHHH6GowWz/H/wB9CnkZHNR7V3dB+VK4xctno/5ilBbcPv8AX1GKYVXeeB+VOCjeOB1oAnoqEqu77o/KpV4UUxH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07UVCU3JKX/screenshot_2024-11-05_at_2.22.10___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07UVCU3JKX-6425bfefc6", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "cxNpW", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " engineering support for this week"}]}]}]}, {"ts": "1730787374.588289", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> Agenda for today\n• Go through upcoming customers and cycle dates and requirements. I just discussed with <@U04DKEFP1K8> and I think there are quite a few new requirements to work on.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730787374.588289", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "OlhfK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Agenda for today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Go through upcoming customers and cycle dates and requirements. I just discussed with "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " and I think there are quite a few new requirements to work on."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1730755371.726909", "text": "It's always the murphy's law. When you most expected things to come up and had <PERSON><PERSON><PERSON>'s support, they didn't:grin:\n\nit also is because we did a lot of stress testing the past 2 weeks. phew!", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730755371.726909", "reply_count": 1, "reactions": [{"name": "sweat_smile", "users": ["U07MH77PUBV"], "count": 1}, {"name": "raised_hands", "users": ["U07MH77PUBV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "c3UMe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It's always the murphy's law. When you most expected things to come up and had <PERSON><PERSON><PERSON>'s support, they didn't"}, {"type": "emoji", "name": "grin", "unicode": "1f601"}, {"type": "text", "text": "\n\nit also is because we did a lot of stress testing the past 2 weeks. phew!"}]}]}]}, {"ts": "1730755023.843209", "text": "<!here> Logging off now\nGreat that we didn't face any issues so far today!", "user": "U07MH77PUBV", "type": "message", "edited": {"user": "U07MH77PUBV", "ts": "1730755041.000000"}, "reactions": [{"name": "heart", "users": ["U07EJ2LP44S", "U07M6QKHUC9"], "count": 2}, {"name": "thankyouty", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}, {"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}, {"name": "grinning", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "/cix4", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Logging off now\nGreat that we didn't face any issues so far today!"}]}]}]}, {"ts": "1730754098.687609", "text": "ah we forgot about it. Lets' keep it as it is for now and we can finalize it tomorrow", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rMNjR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ah we forgot about it. Lets' keep it as it is for now and we can finalize it tomorrow"}]}]}]}, {"ts": "1730751626.911819", "text": "We didn't decide on the meeting timing going forward, should I leave it for now?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "l473K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We didn't decide on the meeting timing going forward, should I leave it for now?"}]}]}]}, {"ts": "1730749885.387009", "text": "<@U07EJ2LP44S> I've closed the SDF  Jira tickets raised by me. all if the issues I reported on Friday are fixed.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MtkCw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I've closed the SDF  Jira tickets raised by me. all if the issues I reported on Friday are fixed."}]}]}]}], "created_at": "2025-05-22T21:35:34.674046"}