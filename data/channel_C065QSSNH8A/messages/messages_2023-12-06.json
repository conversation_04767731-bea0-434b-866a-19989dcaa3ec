{"date": "2023-12-06", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1701886062.108489", "text": "Online now <@U0658EW4B8D> <https://us06web.zoom.us/j/85136045601?pwd=fxFuBKVUaRaAxT4PcI1sUWUf1aHo4F.1>", "user": "U065H3M6WJV", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R068VQZ7N77", "block_id": "2VeAG", "api_decoration_available": false, "call": {"v1": {"id": "R068VQZ7N77", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1701886062, "active_participants": [], "all_participants": [], "display_id": "851-3604-5601", "join_url": "https://us06web.zoom.us/j/85136045601?pwd=fxFuBKVUaRaAxT4PcI1sUWUf1aHo4F.1", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1701976426, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/85136045601?pwd=fxFuBKVUaRaAxT4PcI1sUWUf1aHo4F.1", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "uQ0bf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Online now "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://us06web.zoom.us/j/85136045601?pwd=fxFuBKVUaRaAxT4PcI1sUWUf1aHo4F.1"}]}]}]}, {"ts": "1701885733.024919", "text": "<@U0658EW4B8D> <PERSON> is running a little late for the meeting. She'll ping when when is ready", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hJGL3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " <PERSON> is running a little late for the meeting. She'll ping when when is ready"}]}]}]}, {"ts": "1701823929.293409", "text": "<@U065H3M6WJV> slides 13, 14, 15 and 18 highlights the beta program and implementation process. We will refine them as we gain more clarity on how we want to run the beta program and implementation\n<https://docs.google.com/presentation/d/1kCCTPk19VwJOVvq1I972HJwvXGY1LTz4/edit#slide=id.p15>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F0696HJ3URF", "created": 1701823932, "timestamp": 1701823932, "name": "Discovery Questions.pptx", "title": "Discovery Questions.pptx", "mimetype": "application/vnd.openxmlformats-officedocument.presentationml.presentation", "filetype": "pptx", "pretty_type": "PowerPoint Presentation", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 19625, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1kCCTPk19VwJOVvq1I972HJwvXGY1LTz4", "external_url": "https://docs.google.com/presentation/d/1kCCTPk19VwJOVvq1I972HJwvXGY1LTz4/edit#slide=id.p15", "url_private": "https://docs.google.com/presentation/d/1kCCTPk19VwJOVvq1I972HJwvXGY1LTz4/edit#slide=id.p15", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_360.png", "thumb_360_w": 360, "thumb_360_h": 203, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_480.png", "thumb_480_w": 480, "thumb_480_h": 270, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_720.png", "thumb_720_w": 720, "thumb_720_h": 405, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_800.png", "thumb_800_w": 800, "thumb_800_h": 450, "original_w": 800, "original_h": 450, "thumb_tiny": "AwAbADCnRSqAfvHH4Zp21P8Anp/47XWWIFyM5UfU0FcdwfoaXamfvn/vmjbHj/Wf+O0gGUU/an/PQ/8AfNNYAdDn8MUwEpcE9qSigBcH0pdrehptFAClSOopKKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0696HJ3URF/discovery_questions.pptx", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "agM7d", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " slides 13, 14, 15 and 18 highlights the beta program and implementation process. We will refine them as we gain more clarity on how we want to run the beta program and implementation\n"}, {"type": "link", "url": "https://docs.google.com/presentation/d/1kCCTPk19VwJOVvq1I972HJwvXGY1LTz4/edit#slide=id.p15"}]}]}]}], "created_at": "2025-05-22T21:35:34.572448"}