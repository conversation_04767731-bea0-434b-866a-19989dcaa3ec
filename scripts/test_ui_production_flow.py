#!/usr/bin/env python3
"""
Production-ready UI test script for the complete search flow.
Tests the actual user journey from login to search results.
"""

import time
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException

def setup_driver():
    """Setup Chrome driver with appropriate options."""
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")

    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"❌ Failed to setup Chrome driver: {e}")
        return None

def test_login_flow(driver, base_url):
    """Test the login flow."""
    print("\n🔐 Testing Login Flow...")

    try:
        # Navigate to login page
        driver.get(f"{base_url}/accounts/login/")
        print(f"   📍 Navigated to: {driver.current_url}")

        wait = WebDriverWait(driver, 10)

        # Wait for login form
        username_field = wait.until(EC.presence_of_element_located((By.NAME, "username")))
        password_field = driver.find_element(By.NAME, "password")
        login_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")

        print("   ✅ Found login form elements")

        # Fill credentials
        username_field.clear()
        username_field.send_keys("testuser")
        password_field.clear()
        password_field.send_keys("testpass123")

        print("   ✅ Filled login credentials")

        # Submit form
        login_button.click()

        # Wait for redirect
        time.sleep(3)

        current_url = driver.current_url
        print(f"   📍 After login: {current_url}")

        # Check if login was successful
        if "/accounts/login/" in current_url:
            # Check for error messages
            page_source = driver.page_source
            if "Invalid username" in page_source or "error" in page_source.lower():
                print("   ❌ Login failed - invalid credentials")
                return False
            else:
                print("   ⚠️  Still on login page - checking for issues")
                return False
        else:
            print("   ✅ Login successful - redirected away from login page")
            return True

    except Exception as e:
        print(f"   ❌ Login flow failed: {e}")
        return False

def test_search_page_access(driver, base_url):
    """Test accessing the search page."""
    print("\n🔍 Testing Search Page Access...")

    try:
        # Navigate to search page
        driver.get(f"{base_url}/search/")
        print(f"   📍 Navigated to: {driver.current_url}")

        wait = WebDriverWait(driver, 10)

        # Wait for search form
        search_form = wait.until(EC.presence_of_element_located((By.ID, "searchForm")))
        query_input = driver.find_element(By.NAME, "query")
        submit_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")

        print("   ✅ Found search form elements")

        # Check for CSRF token
        csrf_token = driver.find_element(By.NAME, "csrfmiddlewaretoken")
        if csrf_token:
            print(f"   ✅ CSRF token present: {csrf_token.get_attribute('value')[:20]}...")
        else:
            print("   ❌ CSRF token missing")
            return False

        return True

    except Exception as e:
        print(f"   ❌ Search page access failed: {e}")
        return False

def test_search_query(driver, base_url, query="test query about code"):
    """Test submitting a search query."""
    print(f"\n🔎 Testing Search Query: '{query}'...")

    try:
        wait = WebDriverWait(driver, 10)

        # Find form elements
        query_input = driver.find_element(By.NAME, "query")
        submit_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")

        # Clear and enter query
        query_input.clear()
        query_input.send_keys(query)
        print("   ✅ Entered search query")

        # Submit form
        submit_button.click()
        print("   ✅ Submitted search form")

        # Wait for response
        time.sleep(10)  # Give time for search processing

        current_url = driver.current_url
        page_source = driver.page_source

        print(f"   📍 After search: {current_url}")

        # Check for CSRF error
        if "CSRF verification failed" in page_source:
            print("   ❌ CSRF verification failed")
            return False

        # Check for error messages
        if "error" in page_source.lower() and "error.html" not in current_url:
            print("   ⚠️  Error detected in page content")
            # Look for specific error messages
            if "An unexpected error occurred" in page_source:
                print("   Error: Unexpected error occurred")
            elif "ValueError" in page_source:
                print("   Error: ValueError in search processing")

        # Check for successful search results page
        if "search/query" in current_url or "search_results" in page_source:
            print("   ✅ Reached search results page")

            # Check for search results content
            if "No results found" in page_source or "0 results" in page_source:
                print("   ⚠️  Search completed but no results found")
                return True  # This is still a successful search, just no data
            elif "results" in page_source.lower() or "generated_answer" in page_source:
                print("   ✅ Search results displayed")
                return True
            else:
                print("   ✅ Search completed successfully")
                return True
        elif current_url.endswith("/search/"):
            print("   ⚠️  Redirected back to search page")
            # Check for messages
            if "alert" in page_source or "message" in page_source:
                print("   Checking for error messages...")
                if "Please enter a search query" in page_source:
                    print("   Error: Empty query detected")
                elif "error" in page_source.lower():
                    print("   Error: General error message found")
            return False
        else:
            print("   ❌ Did not reach search results page")
            print(f"   Page title: {driver.title}")
            return False

    except Exception as e:
        print(f"   ❌ Search query failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Production UI Flow Test")
    print("=" * 50)

    base_url = "http://localhost:8000"
    driver = setup_driver()

    if not driver:
        print("❌ Failed to setup browser driver")
        sys.exit(1)

    try:
        # Test 1: Login Flow
        login_success = test_login_flow(driver, base_url)

        if not login_success:
            print("\n❌ Login flow failed - cannot proceed with search tests")
            sys.exit(1)

        # Test 2: Search Page Access
        search_page_success = test_search_page_access(driver, base_url)

        if not search_page_success:
            print("\n❌ Search page access failed")
            sys.exit(1)

        # Test 3: Search Query
        search_query_success = test_search_query(driver, base_url, "slack messages about engineering")

        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Results Summary:")
        print(f"   Login Flow: {'✅ PASS' if login_success else '❌ FAIL'}")
        print(f"   Search Page Access: {'✅ PASS' if search_page_success else '❌ FAIL'}")
        print(f"   Search Query: {'✅ PASS' if search_query_success else '❌ FAIL'}")

        if login_success and search_page_success and search_query_success:
            print("\n🎉 All tests passed! UI is production ready.")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed. UI needs attention.")
            sys.exit(1)

    finally:
        driver.quit()

if __name__ == "__main__":
    main()
