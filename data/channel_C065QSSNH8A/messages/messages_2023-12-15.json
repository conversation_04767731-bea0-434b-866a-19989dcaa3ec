{"date": "2023-12-15", "channel_id": "C065QSSNH8A", "message_count": 18, "messages": [{"ts": "1702612703.129999", "text": "Also, related to our conversation earlier: <https://hbr.org/2021/10/research-cameras-on-or-off>\n\nA few snippets from this one piece of research published in the Journal of Applied Psychology:\n\n• \"Using the camera was positively correlated to daily feelings of fatigue; the number of hours that employees spent in virtual meetings were not. This indicates that keeping the camera consistently on during meetings is at the heart of the fatigue problem.\"\n• \"Even more interesting to us was our finding that fatigue reduced how engaged employees felt, as well as reducing their voice in meetings.\"\n•  \"To further complicate matters, when we examined our results along with the demographics of the employees, it also turned out that being on camera was more fatiguing for certain groups — specifically, women and employees newer to the organization.\"\nI always like bringing a research-focused and empirically-driven lens to these conversations and figured this would be interesting to do a deep dive on.", "user": "U0658EW4B8D", "type": "message", "thread_ts": "1702612703.129999", "reply_count": 4, "attachments": [{"from_url": "https://hbr.org/2021/10/research-cameras-on-or-off", "ts": 1635250533, "image_url": "https://hbr.org/resources/images/article_assets/2021/10/Oct21_26_5198264_1277308208.jpg", "image_width": 1200, "image_height": 675, "image_bytes": 169613, "service_icon": "https://hbr.org/resources/images/apple-touch-icon.png", "id": 1, "original_url": "https://hbr.org/2021/10/research-cameras-on-or-off", "fallback": "Harvard Business Review: Research: Cameras On or Off?", "text": "Managers looking to encourage engagement and inclusion in remote meetings have long encouraged team members to keep their cameras turned on. But researchers examining remote employees’ reactions to the constant video conference calls of the remote work era have found that keeping video on all day actually increases so-called “zoom fatigue.” That’s particularly true for women and new employees, groups that already may feel that they are under the microscope.", "title": "Research: Cameras On or Off?", "title_link": "https://hbr.org/2021/10/research-cameras-on-or-off", "service_name": "Harvard Business Review"}], "blocks": [{"type": "rich_text", "block_id": "NkCVl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also, related to our conversation earlier: "}, {"type": "link", "url": "https://hbr.org/2021/10/research-cameras-on-or-off"}, {"type": "text", "text": "\n\nA few snippets from this one piece of research published in the Journal of Applied Psychology:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "\"Using the camera was positively correlated to daily feelings of fatigue; the number of hours that employees spent in virtual meetings were not. This indicates that keeping the camera consistently on during meetings is at the heart of the fatigue problem.\""}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\"Even more interesting to us was our finding that fatigue reduced how engaged employees felt, as well as reducing their voice in meetings.\""}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": " \"To further complicate matters, when we examined our results along with the demographics of the employees, it also turned out that being on camera was more fatiguing for certain groups — specifically, women and employees newer to the organization.\""}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nI always like bringing a research-focused and empirically-driven lens to these conversations and figured this would be interesting to do a deep dive on."}]}]}]}, {"ts": "1702606093.224359", "text": "Hey <@U065H3M6WJV> we just got a second meeting set for the product advisory board. Our messaging is working and resonating with prospects.\nLet’s discuss the structure of the advisory board and how to position it to the prospect. I think it might be helpful for you to join the first few meetings. let me know if you want to join some of these meetings as you will be working very closely with these customers", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702606093.224359", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "tjbad", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " we just got a second meeting set for the product advisory board. Our messaging is working and resonating with prospects.\nLet’s discuss the structure of the advisory board and how to position it to the prospect. I think it might be helpful for you to join the first few meetings. let me know if you want to join some of these meetings as you will be working very closely with these customers"}]}]}]}, {"ts": "1702590338.812159", "text": "And here's the recording from today's session with \"<PERSON><PERSON>\" (thanks <@U0658EW4B8D> !)\n\n<https://us06web.zoom.us/rec/share/-ov8O4-RbNMwrEYBvpoOh1iBL2SVKtA-yeOjAVcVBk9Y4Lh_HoTwh4BXId4ATMS6.jl49z5gNUGUGCdDx>\nPasscode: *Szp+zh0=*", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D", "U04DS2MBWP4"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "U35Em", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And here's the recording from today's session with \"<PERSON><PERSON>\" (thanks "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " !)\n\n"}, {"type": "link", "url": "https://us06web.zoom.us/rec/share/-ov8O4-RbNMwrEYBvpoOh1iBL2SVKtA-yeOjAVcVBk9Y4Lh_HoTwh4BXId4ATMS6.jl49z5gNUGUGCdDx"}, {"type": "text", "text": "\nPasscode: "}, {"type": "text", "text": "Szp+zh0=", "style": {"bold": true}}]}]}]}, {"ts": "1702590150.322039", "text": "<@U04DKEFP1K8> I've added some more of the live issues to the <https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=1695437663|list in this sheet>. Can you help by updating the status of those that are already fixed, and enter any JIRA links for those that have tickets already?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702590150.322039", "reply_count": 1, "files": [{"id": "F068XFR96CD", "created": 1701905087, "timestamp": 1701905087, "name": "UAT test cases (draft)", "title": "UAT test cases (draft)", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU", "external_url": "https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSYkGlprdRTqACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F068XFR96CD/uat_test_cases__draft_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "x3g67", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I've added some more of the live issues to the "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=1695437663", "text": "list in this sheet"}, {"type": "text", "text": ". Can you help by updating the status of those that are already fixed, and enter any JIRA links for those that have tickets already?"}]}]}]}, {"ts": "1702585353.053019", "text": "that way we would be able to cover the whole product.", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1702585381.000000"}, "blocks": [{"type": "rich_text", "block_id": "Pz+cE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "that way we would be able to cover the whole product."}]}]}]}, {"ts": "1702585336.854679", "text": "I think lets do it all", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "p4E2x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think lets do it all"}]}]}]}, {"ts": "1702585147.558099", "text": "Do we need to walk through creating a cycle, or mainly focus on what an admin would do during a cycle / at end of cycle?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OFX2M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we need to walk through creating a cycle, or mainly focus on what an admin would do during a cycle / at end of cycle?"}]}]}]}, {"ts": "1702584974.404669", "text": "yep", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HC6zQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yep"}]}]}]}, {"ts": "1702584848.750019", "text": "So our user would be an HR admin like <PERSON><PERSON> or <PERSON>?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "J/1tw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So our user would be an HR admin like <PERSON><PERSON> or <PERSON>?"}]}]}]}, {"ts": "1702584776.373589", "text": "admin dashboard, settings", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "a1Btn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "admin dashboard, settings"}]}]}]}, {"ts": "1702584754.354699", "text": "admin view", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7NBPM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "admin view"}]}]}]}, {"ts": "1702584715.986249", "text": "Question for y'all: Is there any more from the Compiify tool that we should usability-test in tomorrow's session? Any use cases that we did not get to cover in the last 2 meetings?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GSsC2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Question for y'all: Is there any more from the Compiify tool that we should usability-test in tomorrow's session? Any use cases that we did not get to cover in the last 2 meetings?"}]}]}]}, {"ts": "1702584685.888169", "text": "I dunno what happened to my internet there :disappointed:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "D4caC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I dunno what happened to my internet there "}, {"type": "emoji", "name": "disappointed", "unicode": "1f61e"}]}]}]}, {"ts": "1702584659.864909", "text": "My Zoom froze...", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "r2FxM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "My Zoom froze..."}]}]}]}, {"ts": "1702584598.167129", "text": "If it was super important feel free to type it here!", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "aYdRq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If it was super important feel free to type it here!"}]}]}]}, {"ts": "1702584590.380809", "text": "Whoops I thought we were done then heard <PERSON> at the very end", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "d+BeZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Whoops I thought we were done then heard <PERSON> at the very end"}]}]}]}, {"ts": "1702580669.055109", "text": "sure", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "isGtt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "sure"}]}]}]}, {"ts": "1702580630.852039", "text": "<@U04DS2MBWP4> and <@U04DKEFP1K8> Are you able to join today's working session? I thought we could continue the exercise of seeing how the current Compiify tool works for a leader (<PERSON><PERSON> user) in the comp cycle.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ePWXm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Are you able to join today's working session? I thought we could continue the exercise of seeing how the current Compiify tool works for a leader (<PERSON><PERSON> user) in the comp cycle."}]}]}]}], "created_at": "2025-05-22T21:35:34.565144"}