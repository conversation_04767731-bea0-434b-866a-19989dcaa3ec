"""
Fallback answers for RAG system.

This module provides fallback answers for common topics when no relevant documents are found.
"""

import logging
import re
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

# Common topics that might be searched
COMMON_TOPICS = {
    "rag": [
        "retrieval augmented generation",
        "retrieval-augmented generation",
        "rag system",
        "rag architecture",
        "retrieval system",
    ],
    "vector_database": [
        "vector database",
        "vector db",
        "vector store",
        "vectorstore",
        "embeddings database",
        "qdrant",
        "pinecone",
        "weaviate",
        "milvus",
    ],
    "llm": [
        "language model",
        "large language model",
        "llm",
        "gpt",
        "transformer",
        "generative ai",
        "neural network",
        "ai model",
    ],
    "embedding": [
        "embedding",
        "embeddings",
        "vector embedding",
        "text embedding",
        "semantic embedding",
        "bert embedding",
        "sentence transformer",
    ],
    "hybrid_search": [
        "hybrid search",
        "bm25",
        "keyword search",
        "dense retrieval",
        "sparse retrieval",
        "lexical search",
        "semantic search",
    ],
    "chunking": [
        "chunking",
        "document chunking",
        "chunk size",
        "chunk overlap",
        "text splitting",
        "document splitting",
    ],
    "metadata": [
        "metadata",
        "document metadata",
        "filtering",
        "metadata filtering",
        "document properties",
    ],
    "performance": [
        "performance",
        "optimization",
        "latency",
        "throughput",
        "speed",
        "efficiency",
    ],
}

# Fallback answers for common topics
FALLBACK_ANSWERS = {
    "rag": """
Based on general knowledge about Retrieval Augmented Generation (RAG):

RAG is an architecture that combines retrieval systems with generative AI to produce more accurate and factual responses. The key components of a RAG system typically include:

1. A document ingestion pipeline that processes and indexes content
2. A vector database that stores document embeddings for semantic search
3. A retrieval system that finds relevant information based on queries
4. A generation component (typically an LLM) that produces responses using the retrieved context

RAG systems help address hallucination issues in LLMs by grounding their responses in retrieved documents.

If you're looking for specific information about our organization's RAG implementation, please provide more details about what you're trying to find.
""",

    "vector_database": """
Based on general knowledge about vector databases:

Vector databases are specialized data stores designed to efficiently index, store, and search vector embeddings. They typically support:

1. High-dimensional vector storage
2. Approximate nearest neighbor (ANN) search algorithms like HNSW or IVF
3. Filtering based on metadata
4. Horizontal scaling for large collections

Popular vector database solutions include Qdrant, Pinecone, Weaviate, Milvus, and Chroma.

For RAG systems, vector databases are typically used to store document embeddings and enable semantic search.

If you're looking for information about our specific vector database implementation, please provide more details about what you're trying to find.
""",

    "llm": """
Based on general knowledge about Large Language Models (LLMs):

Large Language Models are neural network-based systems trained on vast amounts of text data to understand and generate human language. Key aspects include:

1. Transformer architecture with attention mechanisms
2. Ability to understand context and generate coherent text
3. Pre-training on large corpora followed by fine-tuning
4. Token-based processing with context windows

In RAG systems, LLMs are typically used to generate responses based on retrieved context, combining their general language capabilities with specific retrieved information.

If you're looking for information about our specific LLM implementation, please provide more details about what you're trying to find.
""",

    "embedding": """
Based on general knowledge about embeddings:

Embeddings are dense vector representations of text that capture semantic meaning. Key aspects include:

1. High-dimensional vectors (typically 384-1536 dimensions)
2. Semantic similarity represented by vector proximity
3. Generated using specialized models like Sentence Transformers
4. Enables "meaning search" rather than keyword matching

In RAG systems, embeddings are used to convert both documents and queries into vectors for semantic matching.

If you're looking for information about our specific embedding implementation, please provide more details about what you're trying to find.
""",

    "hybrid_search": """
Based on general knowledge about hybrid search:

Hybrid search combines multiple retrieval methods to improve search quality:

1. Dense retrieval using vector embeddings for semantic understanding
2. Sparse retrieval using methods like BM25 for keyword matching
3. Reranking to improve result relevance
4. Score normalization and fusion techniques

This approach leverages the strengths of both semantic understanding and lexical matching.

If you're looking for information about our specific hybrid search implementation, please provide more details about what you're trying to find.
""",

    "chunking": """
Based on general knowledge about document chunking:

Chunking is the process of breaking documents into smaller pieces for efficient indexing and retrieval:

1. Typically splits documents into paragraphs, fixed token lengths, or semantic units
2. Balances chunk size (typically 256-1024 tokens) and overlap
3. Preserves context while enabling precise retrieval
4. May use hierarchical approaches (parent-child chunks)

Effective chunking strategies are crucial for RAG system performance.

If you're looking for information about our specific chunking implementation, please provide more details about what you're trying to find.
""",

    "metadata": """
Based on general knowledge about metadata in search systems:

Metadata provides additional context and filtering capabilities for document retrieval:

1. Includes attributes like source, author, date, tags, categories
2. Enables filtering to narrow search scope
3. Can be used for boosting or dampening search results
4. Helps with relevance determination

In RAG systems, metadata filtering is often combined with semantic search for precise retrieval.

If you're looking for information about our specific metadata implementation, please provide more details about what you're trying to find.
""",

    "performance": """
Based on general knowledge about RAG system performance optimization:

RAG performance optimization typically focuses on several areas:

1. Vector search efficiency (using optimized indices like HNSW)
2. Caching frequently accessed embeddings and results
3. Parallelizing retrieval operations
4. Optimizing chunk size and embedding dimension
5. Query preprocessing and expansion
6. Response generation optimization

These optimizations help reduce latency and improve throughput while maintaining result quality.

If you're looking for information about our specific performance optimizations, please provide more details about what you're trying to find.
"""
}


def identify_topic(query: str) -> Optional[str]:
    """
    Identify the topic of a query based on keyword matching.
    
    Args:
        query: Query string
        
    Returns:
        Optional[str]: Identified topic or None
    """
    # Normalize query
    query_lower = query.lower()
    
    # Check each topic
    for topic, keywords in COMMON_TOPICS.items():
        for keyword in keywords:
            if keyword.lower() in query_lower:
                return topic
                
    return None


def generate_fallback_answer(query: str) -> Optional[str]:
    """
    Generate a fallback answer for a query when no relevant documents are found.
    
    Args:
        query: Query string
        
    Returns:
        Optional[str]: Fallback answer or None
    """
    # Identify topic
    topic = identify_topic(query)
    
    # If topic identified, provide fallback answer
    if topic and topic in FALLBACK_ANSWERS:
        logger.info(f"Providing fallback answer for topic: {topic}")
        return FALLBACK_ANSWERS[topic]
        
    # No matching topic found
    return None


def get_enhanced_not_found_message(query: str) -> str:
    """
    Get an enhanced "not found" message that provides useful information.
    
    Args:
        query: Query string
        
    Returns:
        str: Enhanced not found message
    """
    # Try to get a fallback answer
    fallback_answer = generate_fallback_answer(query)
    
    if fallback_answer:
        return fallback_answer
    
    # If no fallback, provide a helpful message
    return """
I couldn't find specific information about your query in our knowledge base. 

This could be because:
1. The information hasn't been indexed yet
2. It might be available under different terminology
3. It might be in a collection that isn't currently accessible

You might try:
- Rephrasing your question with different keywords
- Being more specific about what you're looking for
- Asking about related topics that might contain the information

If you believe this information should be available, please contact the system administrator.
"""
