#!/usr/bin/env python
"""
Simple script to check database statistics.
"""

import os
import django

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from apps.documents.models import RawDocument, DocumentChunk, DocumentSource, EmbeddingMetadata

# Print database statistics
print("RAGSearch Database Statistics")
print("============================")
print(f"Raw Documents: {RawDocument.objects.count()}")
print(f"Document Chunks: {DocumentChunk.objects.count()}")
print(f"Document Sources: {DocumentSource.objects.count()}")
print(f"Embedding Metadata: {EmbeddingMetadata.objects.count()}")

# Get Slack sources
slack_sources = DocumentSource.objects.filter(source_type__in=["slack", "local_slack"])
print(f"\nSlack Sources: {slack_sources.count()}")
for source in slack_sources:
    docs = RawDocument.objects.filter(source=source)
    chunks = DocumentChunk.objects.filter(document__source=source)
    print(f"  - {source.name} ({source.source_type}): {docs.count()} documents, {chunks.count()} chunks")
    
    # Get document counts by content type
    content_types = docs.values_list('content_type', flat=True).distinct()
    for content_type in content_types:
        doc_count = docs.filter(content_type=content_type).count()
        chunk_count = chunks.filter(chunk_type=content_type).count()
        print(f"    - {content_type}: {doc_count} documents, {chunk_count} chunks")

# Get chunking strategies for Slack documents
if slack_sources.exists():
    print("\nChunking Strategies:")
    for source in slack_sources:
        docs = RawDocument.objects.filter(source=source)
        for doc in docs[:5]:  # Limit to 5 documents to avoid too much output
            try:
                metadata = doc.metadata
                if isinstance(metadata, str):
                    import json
                    metadata = json.loads(metadata)
                
                chunking_strategy = metadata.get('chunking_strategy', 'unknown')
                print(f"  - {doc.title}: {chunking_strategy}")
            except Exception as e:
                print(f"  - {doc.title}: Error parsing metadata - {str(e)}")
