{"date": "2023-12-02", "channel_id": "C065QSSNH8A", "message_count": 11, "messages": [{"ts": "1701490635.995679", "text": "<@U065H3M6WJV> let us know your thoughts if we should ask <PERSON> if there is a scope to start the planning cycle for managers after perf ratings are published ( asking since perf rating comes out around 2 feb and current request to open application is jan 22nd ) . I was also thinking that managers would need to view rating before they make any decisions. ( i will anyways plan development activities to accommodate scenario presented to us anyways)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1701490635.995679", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "qj0RV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " let us know your thoughts if we should ask <PERSON> if there is a scope to start the planning cycle for managers after perf ratings are published ( asking since perf rating comes out around 2 feb and current request to open application is jan 22nd ) . I was also thinking that managers would need to view rating before they make any decisions. ( i will anyways plan development activities to accommodate scenario presented to us anyways)"}]}]}]}, {"ts": "1701489890.551529", "text": "Hey <@U065H3M6WJV> please share angel<PERSON>’s feedback for OTE front end in the merit view", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1701489890.551529", "reply_count": 6, "edited": {"user": "U04DKEFP1K8", "ts": "1701489913.000000"}, "blocks": [{"type": "rich_text", "block_id": "EGTZZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " please share <PERSON><PERSON><PERSON>s feedback for OTE front end in the merit view"}]}]}]}, {"ts": "1701484244.859709", "text": "<https://docs.google.com/spreadsheets/d/1QxlCmvkmAZGWnTaepnC8xPTVvggze9Jm4hBG7l_lJCc/edit#gid=0|Rough Stellar Project Plan here>", "user": "U0658EW4B8D", "type": "message", "files": [{"id": "F067WN3PDLP", "created": 1701484248, "timestamp": 1701484248, "name": "Stellar Implementation Project Plan", "title": "Stellar Implementation Project Plan", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 103185, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1QxlCmvkmAZGWnTaepnC8xPTVvggze9Jm4hBG7l_lJCc", "external_url": "https://docs.google.com/spreadsheets/d/1QxlCmvkmAZGWnTaepnC8xPTVvggze9Jm4hBG7l_lJCc/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1QxlCmvkmAZGWnTaepnC8xPTVvggze9Jm4hBG7l_lJCc/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHRbdnjH40gL+q/lTj1pAKVwEy+cZX8qUbs8kY+lHelouAtFFFMAooooAMc0UUUAFFFFABRRRQAUUUUAFFFFAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F067WN3PDLP/stellar_implementation_project_plan", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "h0Txj", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/spreadsheets/d/1QxlCmvkmAZGWnTaepnC8xPTVvggze9Jm4hBG7l_lJCc/edit#gid=0", "text": "Rough Stellar Project Plan here"}]}]}]}, {"ts": "1701482835.042479", "text": "Looking at <PERSON><PERSON>'s timeline and their end of cycle is going to be TIGHT", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GeoRP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Looking at <PERSON><PERSON>'s timeline and their end of cycle is going to be TIGHT"}]}]}]}, {"ts": "1701477467.228979", "text": "Can start here and breakout if needed", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eB9U/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can start here and breakout if needed"}]}]}]}, {"ts": "1701476438.817279", "text": "Do you all feel the need to create a separate slack channel for each customer or are we good here?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701476438.817279", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "g6agJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you all feel the need to create a separate slack channel for each customer or are we good here?"}]}]}]}, {"ts": "1701476381.011609", "text": "Here is Stellar timeline\n<https://docs.google.com/spreadsheets/d/1OzDi5j444dTOGX1XgwfQqGxL4p0io2tpd2kfHXFpU6M/edit#gid=0>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "meow_thx", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F068AUMHDDZ", "created": 1701476383, "timestamp": 1701476383, "name": "SDF Comp Timeline - Compiify", "title": "SDF Comp Timeline - Compiify", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 125149, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1OzDi5j444dTOGX1XgwfQqGxL4p0io2tpd2kfHXFpU6M", "external_url": "https://docs.google.com/spreadsheets/d/1OzDi5j444dTOGX1XgwfQqGxL4p0io2tpd2kfHXFpU6M/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1OzDi5j444dTOGX1XgwfQqGxL4p0io2tpd2kfHXFpU6M/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHSJOeKTLDPTikZgGwSBSnoaVgEDMT04p9RA5I/xqUdKLAFFFFMBjsobB60rdD9KZIshcFTx9acQcGgBi1MOlRqPbH4VIOlABRRRQAUUUUAFFFFABRRRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F068AUMHDDZ/sdf_comp_timeline_-_compiify", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "4xSdH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is Stellar timeline\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1OzDi5j444dTOGX1XgwfQqGxL4p0io2tpd2kfHXFpU6M/edit#gid=0"}]}]}]}, {"ts": "1701467261.429009", "text": "Random: just added a <#C0683U8CHSA|> channel where I expect y’all to drop some photos of <PERSON>, <PERSON><PERSON>, and any other fuzzy friends. I went ahead and kicked it off with one of mine. :wink: ", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "it/GD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Random: just added a "}, {"type": "channel", "channel_id": "C0683U8CHSA"}, {"type": "text", "text": " channel where I expect y’all to drop some photos of <PERSON>, <PERSON><PERSON>, and any other fuzzy friends. I went ahead and kicked it off with one of mine. "}, {"type": "emoji", "name": "wink", "unicode": "1f609"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1701457836.523439", "text": "<!here> most requirements from the call with stellar seems attainable except we will not get performance ratings with the initial upload. Need to discuss with you all", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1701457836.523439", "reply_count": 3, "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4", "U065H3M6WJV"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "wbXhe", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " most requirements from the call with stellar seems attainable except we will not get performance ratings with the initial upload. Need to discuss with you all"}]}]}]}, {"ts": "1701457822.823259", "text": "<@U065H3M6WJV> we should figure out the release plan for new pay bands and total rewards portal", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YGOdU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " we should figure out the release plan for new pay bands and total rewards portal"}]}]}]}, {"ts": "1701457779.207529", "text": "Team- It was a good call. Most of their requirements are fairly simples and it looks like we can address them with the exception of pay gap analytics within comp band and total rewards portal", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "2PC+O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Team- It was a good call. Most of their requirements are fairly simples and it looks like we can address them with the exception of pay gap analytics within comp band and total rewards portal"}]}]}]}], "created_at": "2025-05-22T21:35:34.573404"}