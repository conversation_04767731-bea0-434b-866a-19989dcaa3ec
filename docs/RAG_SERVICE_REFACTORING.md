# RAG Service Refactoring

## Overview
This document describes the refactoring of the RAG service to use LlamaIndex capabilities.

## Changes Made
- Replaced custom RAG logic in `rag_service.py` with LlamaIndex implementation
- Removed dependency on `llama_index_rag_service_new.py` and deleted the file
- Removed dependency on `llama_index_rag_service.py` and deleted the file
- Updated `ingest_slack.py` to use the new RAGService implementation
- Implemented a more efficient and maintainable RAG pipeline using LlamaIndex's capabilities
- Added comprehensive error handling and performance monitoring
- Improved citation tracking and management
- Simplified the API by removing unused parameters
- Removed dead code and redundant methods

## Implementation Details
The new implementation:
1. Uses LlamaIndex's VectorStoreIndex for retrieval
2. Uses LlamaIndex's CitationQueryEngine for citation generation
3. Maintains compatibility with existing database models
4. Provides better error handling and fallback mechanisms
5. Includes performance monitoring and statistics

## Testing
To test the new implementation:
1. Run the Django shell: `python manage.py shell`
2. Import the RAG service: `from apps.search.services.rag_service import RAGService`
3. Create an instance: `rag = RAGService(user=user, tenant_slug='your-tenant')`
4. Test a search: `result, docs = rag.search('your query')`
5. Check the response and citations
