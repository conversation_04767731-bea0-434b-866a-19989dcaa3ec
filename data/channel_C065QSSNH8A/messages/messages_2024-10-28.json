{"date": "2024-10-28", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1730135779.712459", "text": "<@U04DKEFP1K8> Budgets are made visible now. <@U07M6QKHUC9> manager filter is also added in merit view (right now the change is only on <http://qa.stridehr.io|qa.stridehr.io>)", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730128887.743089", "subtype": "thread_broadcast", "reactions": [{"name": "partying_face", "users": ["U07M6QKHUC9"], "count": 1}, {"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Bc0fK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Budgets are made visible now. "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " manager filter is also added in merit view (right now the change is only on "}, {"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}, {"type": "text", "text": ")"}]}]}]}, {"ts": "1730135334.032219", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> <@U07M6QKHUC9> I am unavailable my evening tomorrow for meetings due to a personal work. I am available on slack for anything.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "e+s4C", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I am unavailable my evening tomorrow for meetings due to a personal work. I am available on slack for anything."}]}]}]}, {"ts": "1730128912.151039", "text": "Please let me know if I can go ahead and push the changes to prod.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3KaJ1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please let me know if I can go ahead and push the changes to prod."}]}]}]}, {"ts": "1730128887.743089", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S>\nChanges for HRBP are ready and deployed in <https://qa.stridehr.io> . Tested and regressed. There is some slowness for HRBP  login which we are optimizing as we speak and will deploy tomorrow.\nThank You <@U071FN2589Y> <@U06HN8XDC5A> for the turn around.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730128887.743089", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "+Ex9g", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "\nChanges for HRBP are ready and deployed in "}, {"type": "link", "url": "https://qa.stridehr.io"}, {"type": "text", "text": " . Tested and regressed. There is some slowness for HRBP  login which we are optimizing as we speak and will deploy tomorrow.\nThank You "}, {"type": "user", "user_id": "U071FN2589Y"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " for the turn around."}]}]}]}], "created_at": "2025-05-22T21:35:34.661307"}