{"date": "2025-02-13", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1739468020.144469", "text": "<@U0690EB5JE5> <@U07M6QKHUC9> <PERSON><PERSON><PERSON> wants to move forward with the separate budget cycle", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739468020.144469", "reply_count": 14, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "YfVqQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> wants to move forward with the separate budget cycle"}]}]}]}, {"ts": "1739467906.586919", "text": "<@U07EJ2LP44S> yes thats the plan, Zip file as per hierarchy.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "17MZW", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " yes thats the plan, Zip file as per hierarchy."}]}]}]}, {"ts": "1739467326.619579", "text": "Can we provide a zip file for their letters with the letters grouped by team, like we did for Degenkolb? They will be importing them into their HRIS (HiBob)", "user": "U07EJ2LP44S", "type": "message", "edited": {"user": "U07EJ2LP44S", "ts": "1739467429.000000"}, "blocks": [{"type": "rich_text", "block_id": "L6c3X", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can we provide a zip file for their letters with the letters grouped by team, like we did for Degenkolb? They will be importing them into their HRIS (HiBob)"}]}]}]}, {"ts": "1739466311.409719", "text": "For Valgenesis Letters, they would like the % change to show the merit increase change %, not the overall comp (including bonuses, for example)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739466311.409719", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "bdlci", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For Valgenesis Letters, they would like the % change to show the merit increase change %, not the overall comp (including bonuses, for example)"}]}]}]}, {"ts": "1739462439.581609", "text": "Yes we can ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rjqNn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes we can "}]}]}]}, {"ts": "1739462237.719569", "text": "<@U0690EB5JE5> if you’re available, we can still meet", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "F06Ve", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " if you’re available, we can still meet"}]}]}]}, {"ts": "1739456845.337299", "text": "I am likely going to miss the meeting today. I have an urgent care visit scheduled for my daughter at 11 and it includes an xray so I'm doubtful i'll be done in time", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5", "U07M6QKHUC9"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "lnz6z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am likely going to miss the meeting today. I have an urgent care visit scheduled for my daughter at 11 and it includes an xray so I'm doubtful i'll be done in time"}]}]}]}, {"ts": "1739442529.073429", "text": "<@U07EJ2LP44S> This is Done. We changed the wording `Part Time` to `Hourly` in table views and reports.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739296532.147379", "subtype": "thread_broadcast", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S", "U07M6QKHUC9"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "FGog2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is Done. We changed the wording "}, {"type": "text", "text": "Part Time", "style": {"code": true}}, {"type": "text", "text": " to "}, {"type": "text", "text": "Hourly", "style": {"code": true}}, {"type": "text", "text": " in table views and reports."}]}]}]}, {"ts": "1739392066.630219", "text": "Is this possible? Essentially the low recommendation would be the same across several ratings", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739392066.630219", "reply_count": 5, "files": [{"id": "F08D40URA3W", "created": 1739392062, "timestamp": 1739392062, "name": "IMG_4908.png", "title": "IMG_4908", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 163705, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08D40URA3W/img_4908.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08D40URA3W/download/img_4908.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_360.png", "thumb_360_w": 166, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_480.png", "thumb_480_w": 221, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_720.png", "thumb_720_w": 332, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_800.png", "thumb_800_w": 800, "thumb_800_h": 1734, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_960.png", "thumb_960_w": 443, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_1024.png", "thumb_1024_w": 472, "thumb_1024_h": 1024, "original_w": 1179, "original_h": 2556, "thumb_tiny": "AwAwABbMooooAKKKKANNdNjLYLP+dP8A7Li/vv8AnTV1KENkq/5Cn/2pB/ck/IUAJ/ZcX99/zFH9lw/33/MUv9qQf3JPyFH9qQf3JPyFAGRRRS7WH8J/KgBKKKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08D40URA3W/img_4908.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08D40URA3W-a5b858b06b", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Mh5DW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is this possible? Essentially the low recommendation would be the same across several ratings"}]}]}]}], "created_at": "2025-05-22T21:35:34.713596"}