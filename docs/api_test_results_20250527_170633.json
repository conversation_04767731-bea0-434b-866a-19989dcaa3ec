{"total_tests": 9, "successful_tests": 0, "failed_tests": 9, "test_details": [{"test_name": "Basic Search", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.01909470558166504}, {"test_name": "Query Expansion (HyDE)", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.010667085647583008}, {"test_name": "Multi-Step Reasoning", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.012582778930664062}, {"test_name": "Full RAG Features", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.011022090911865234}, {"test_name": "Low Relevance Threshold", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.012536048889160156}, {"test_name": "Output Format: text", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.01227426528930664}, {"test_name": "Output Format: json", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.011247873306274414}, {"test_name": "Output Format: markdown", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.011082172393798828}, {"test_name": "Output Format: table", "status": "failed", "error": "HTTP 401: {\"detail\":\"Invalid token.\"}", "execution_time": 0.02683115005493164}], "rag_techniques_verified": {"hybrid_search": false, "query_expansion": false, "multi_step_reasoning": false, "context_aware": false, "citation_engine": false, "router_engine": false}, "performance_metrics": {"total_time": 0.12733817100524902, "average_time": 0.014148685667249892, "min_time": 0.010667085647583008, "max_time": 0.02683115005493164}}