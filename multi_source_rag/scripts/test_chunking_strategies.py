#!/usr/bin/env python
"""
Test script for configurable chunking strategies.

This script demonstrates how different source types use different chunking strategies
and shows the impact of skip-chunking for pre-optimized sources like Slack.

Usage:
    python scripts/test_chunking_strategies.py
"""

import os
import django
import logging
from typing import Dict, Any

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from apps.core.utils.chunking_strategies import (
    ChunkingStrategy,
    SourceChunkingConfig,
    get_chunking_strategy_info,
    should_skip_chunking,
    get_optimal_chunk_size
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_chunking_strategies():
    """Test and demonstrate chunking strategies for different source types."""

    print("=" * 80)
    print("CONFIGURABLE CHUNKING STRATEGIES TEST")
    print("=" * 80)

    # Test different source types
    test_sources = [
        "slack",
        "local_slack",
        "github",
        "confluence",
        "pdf",
        "file",
        "discord",
        "unknown_source"
    ]

    print("\n📋 CHUNKING STRATEGY CONFIGURATION")
    print("-" * 50)

    for source_type in test_sources:
        info = get_chunking_strategy_info(source_type)
        skip = should_skip_chunking(source_type)
        chunk_size = get_optimal_chunk_size(source_type)

        print(f"\n🔧 Source Type: {source_type}")
        print(f"   Strategy: {info['strategy']}")
        print(f"   Skip Chunking: {'✅ YES' if skip else '❌ NO'}")
        print(f"   Chunk Size: {chunk_size} chars")
        print(f"   Description: {info['description']}")

        if info['params']:
            print(f"   Parameters:")
            for key, value in info['params'].items():
                if key != 'description':
                    print(f"     - {key}: {value}")


def test_slack_skip_chunking():
    """Demonstrate skip-chunking behavior for Slack sources."""

    print("\n" + "=" * 80)
    print("SLACK SKIP-CHUNKING DEMONSTRATION")
    print("=" * 80)

    slack_sources = ["slack", "local_slack"]

    for source_type in slack_sources:
        info = get_chunking_strategy_info(source_type)

        print(f"\n📱 {source_type.upper()} SOURCE")
        print("-" * 30)
        print(f"Strategy: {info['strategy']}")
        print(f"Skip Chunking: {info['skip_chunking']}")
        print(f"Direct Embedding: {info['direct_embedding']}")
        print(f"Preserve Metadata: {info['preserve_metadata']}")

        print("\n💡 Why skip chunking for Slack?")
        print("   • Messages are already strategically merged by LocalSlackInterface")
        print("   • Conversations are grouped by threads and time proximity")
        print("   • Monthly aggregation preserves context and reduces fragmentation")
        print("   • Pre-optimized documents don't need further chunking")


def test_strategy_comparison():
    """Compare different chunking strategies side by side."""

    print("\n" + "=" * 80)
    print("CHUNKING STRATEGY COMPARISON")
    print("=" * 80)

    strategies = [
        ("local_slack", "Pre-optimized Slack data"),
        ("github", "Code repositories"),
        ("confluence", "Documentation"),
        ("pdf", "PDF documents"),
        ("file", "Generic files")
    ]

    print(f"\n{'Source Type':<15} {'Strategy':<20} {'Skip?':<8} {'Size':<8} {'Overlap':<8}")
    print("-" * 70)

    for source_type, description in strategies:
        info = get_chunking_strategy_info(source_type)
        skip = "YES" if info['skip_chunking'] else "NO"
        size = info['chunk_size']
        overlap = info['chunk_overlap']

        print(f"{source_type:<15} {info['strategy']:<20} {skip:<8} {size:<8} {overlap:<8}")

    print("\n📊 Key Insights:")
    print("   • Slack sources skip chunking (pre-optimized)")
    print("   • Code uses file-based chunking (preserves structure)")
    print("   • Documents use semantic chunking (meaning-based)")
    print("   • Different chunk sizes optimize for content type")


def test_configuration_flexibility():
    """Demonstrate configuration flexibility."""

    print("\n" + "=" * 80)
    print("CONFIGURATION FLEXIBILITY")
    print("=" * 80)

    print("\n🔧 Available Chunking Strategies:")
    for strategy in ChunkingStrategy:
        params = SourceChunkingConfig.get_strategy_params(strategy)
        requires_chunking = params.get("requires_chunking", True)

        print(f"\n   {strategy.value}:")
        print(f"     Requires Chunking: {requires_chunking}")
        print(f"     Description: {params.get('description', 'N/A')}")

    print("\n📝 Source Type Mappings:")
    for source_type, strategy in SourceChunkingConfig.SOURCE_STRATEGIES.items():
        print(f"   {source_type:<15} → {strategy.value}")

    print("\n💡 Benefits of Configurable Chunking:")
    print("   • Source-aware processing optimizes for content type")
    print("   • Skip-chunking preserves pre-optimized documents")
    print("   • Flexible configuration allows easy customization")
    print("   • Better retrieval performance through appropriate chunking")


def main():
    """Main test function."""
    try:
        test_chunking_strategies()
        test_slack_skip_chunking()
        test_strategy_comparison()
        test_configuration_flexibility()

        print("\n" + "=" * 80)
        print("✅ CHUNKING STRATEGIES TEST COMPLETED SUCCESSFULLY")
        print("=" * 80)

    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        print(f"\n❌ Test failed: {str(e)}")


if __name__ == "__main__":
    main()
