#!/usr/bin/env python
"""
Test the UI search after fixing the embedding dimension issue.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from apps.search.services.rag_service import RAGService
from django.contrib.auth.models import User

def test_ui_search_fixed():
    """Test the UI search after fixing embedding dimensions."""
    print("🔍 Testing UI Search After Embedding Fix")
    print("=" * 60)
    
    # Get the user
    user = User.objects.get(email="<EMAIL>")
    print(f"Using user: {user.email}")
    
    # Create RAG service (this should trigger the same initialization as the UI)
    print(f"\n🔧 Creating RAG service (simulating UI initialization)...")
    try:
        rag_service = RAGService(user=user, tenant_slug="stride")
        print(f"✅ RAG service created successfully")
        print(f"   Service tenant: {rag_service.tenant_slug}")
        
        if rag_service.unified_service:
            print(f"   Unified service available: ✅")
        else:
            print(f"   Unified service available: ❌")
            return False
        
        # Test a search that was failing before
        print(f"\n🔍 Testing search that was failing in UI...")
        query = "What's the latest on Curana?"
        
        try:
            result, docs = rag_service.search(query, top_k=10)
            
            print(f"✅ Search completed successfully!")
            print(f"   Result ID: {result.id}")
            print(f"   Tenant: {result.search_query.tenant.slug}")
            print(f"   Citations: {result.citations.count()}")
            print(f"   Answer length: {len(result.generated_answer)} chars")
            
            if result.citations.count() > 0:
                print(f"   🎉 CITATIONS WORKING! Found {result.citations.count()} citations")
                return True
            else:
                print(f"   ❌ No citations found")
                return False
                
        except Exception as search_error:
            print(f"   ❌ Search failed: {search_error}")
            
            # Check if it's still the dimension error
            if "dimension error" in str(search_error).lower():
                print(f"   🚨 STILL DIMENSION MISMATCH ERROR!")
                print(f"   The fix didn't work - UI is still using wrong embedding model")
            
            return False
            
    except Exception as e:
        print(f"❌ RAG service creation failed: {e}")
        return False

if __name__ == "__main__":
    success = test_ui_search_fixed()
    if success:
        print(f"\n🎉 UI SEARCH IS FIXED! Citations are working!")
    else:
        print(f"\n❌ UI search is still broken - need more investigation")
