{"date": "2024-12-17", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1734452257.625669", "text": "<https://docs.google.com/document/d/1IEdPBEMQWs3VEA275DKcBt5QOWWHujR04eOVkAoYtv0/edit?tab=t.0>", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F085VSUPC1F", "created": 1734452260, "timestamp": 1734452260, "name": "Paybands Feedback", "title": "Paybands Feedback", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 60944, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1IEdPBEMQWs3VEA275DKcBt5QOWWHujR04eOVkAoYtv0", "external_url": "https://docs.google.com/document/d/1IEdPBEMQWs3VEA275DKcBt5QOWWHujR04eOVkAoYtv0/edit?tab=t.0", "url_private": "https://docs.google.com/document/d/1IEdPBEMQWs3VEA275DKcBt5QOWWHujR04eOVkAoYtv0/edit?tab=t.0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTRJwetOBzTWI6GlHSgBaKKKACiiigBDjPWgEDvQRmgKBQAuR60UUUAFFFFABRRRQAUUUUAFFFFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F085VSUPC1F/paybands_feedback", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "u7LdQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/document/d/1IEdPBEMQWs3VEA275DKcBt5QOWWHujR04eOVkAoYtv0/edit?tab=t.0"}]}]}]}, {"ts": "1734444207.364829", "text": "<@U07EJ2LP44S> Letters updated per email from customer. Also folder structure per recommenders hierarchy. Please spot check once before sharing with customer.\n<@U07M6QKHUC9> Again this took almost 2 days of an engineer's time. This is not sustainable in the long term. We should stop accepting customer specific template requirements and generalize with support for little customizations (self serve)", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734370134.049029", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1734444386.000000"}, "files": [{"id": "F085DFAV2PP", "created": 1734444063, "timestamp": 1734444063, "name": "employee_letters_nested_fixed.zip", "title": "employee_letters_nested_fixed.zip", "mimetype": "application/zip", "filetype": "zip", "pretty_type": "Zip", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 27137152, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F085DFAV2PP/employee_letters_nested_fixed.zip", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F085DFAV2PP/download/employee_letters_nested_fixed.zip", "media_display_type": "unknown", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F085DFAV2PP/employee_letters_nested_fixed.zip", "permalink_public": "https://slack-files.com/T04DM97F1UM-F085DFAV2PP-b446eaab85", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "S7qsH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Letters updated per email from customer. Also folder structure per recommenders hierarchy. Please spot check once before sharing with customer.\n"}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Again this took almost 2 days of an engineer's time. This is not sustainable in the long term. We should stop accepting customer specific template requirements and generalize with support for little customizations (self serve)"}]}]}]}, {"ts": "1734387813.542859", "text": "<@U0690EB5JE5> Also, can you please check your email for RE: Stride Letters, from Degenkolb. Id like to discuss if we can do what they are asking (different letters for different role types - exempt, non exempt, etc)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734387813.542859", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "x/OFk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Also, can you please check your email for RE: Stride Letters, from Degenkolb. Id like to discuss if we can do what they are asking (different letters for different role types - exempt, non exempt, etc)"}]}]}]}, {"ts": "1734387638.779779", "text": "<@U0690EB5JE5> <PERSON><PERSON><PERSON><PERSON> is asking for SFTP info. She says 'I received the username and SSH key, but not the host site or password.'", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734387638.779779", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "hVS2R", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON><PERSON> is asking for SFTP info. She says 'I received the username and SSH key, but not the host site or password.'"}]}]}]}, {"ts": "1734383629.136579", "text": "<@U07M6QKHUC9> we have a call to discuss paybands with tithely this week. do you want to cancel, since building out the full feature is no longer on the table?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734383629.136579", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Ly6rL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " we have a call to discuss paybands with tithely this week. do you want to cancel, since building out the full feature is no longer on the table?"}]}]}]}, {"ts": "1734375439.294449", "text": "<@U0690EB5JE5> Pls add your comments too if you have any", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734368908.191279", "subtype": "thread_broadcast", "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Laon5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Pls add your comments too if you have any"}]}]}]}], "created_at": "2025-05-22T21:35:34.677577"}