{"date": "2025-02-21", "channel_id": "C065QSSNH8A", "message_count": 15, "messages": [{"ts": "1740160092.101529", "text": "Valgenesis:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740160092.101529", "reply_count": 15, "files": [{"id": "F08EDM3DHRB", "created": 1740160084, "timestamp": 1740160084, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 569402, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08EDM3DHRB/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08EDM3DHRB/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_360.png", "thumb_360_w": 360, "thumb_360_h": 247, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_480.png", "thumb_480_w": 480, "thumb_480_h": 330, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_720.png", "thumb_720_w": 720, "thumb_720_h": 494, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_800.png", "thumb_800_w": 800, "thumb_800_h": 549, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_960.png", "thumb_960_w": 960, "thumb_960_h": 659, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 703, "original_w": 1806, "original_h": 1240, "thumb_tiny": "AwAgADDQ2e5p2D60EZPU0bfc0AGD60Y96McdTRj3NABj3NLijHuaKAEIyen60mPb9aGOD3pM/WgBcYHIpePSk6jnNKFFABgelLgelGMUUAf/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08EDM3DHRB/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08EDM3DHRB-673d50f774", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "GxYJn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis:"}]}]}]}, {"ts": "1740158605.843209", "text": "yes hopefully", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "aAga7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes hopefully"}]}]}]}, {"ts": "1*********.427929", "text": "Diversified is doing fine with bonus for the most part so hopefully we caught and fixed any bugs with that one already.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "f8bT7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified is doing fine with bonus for the most part so hopefully we caught and fixed any bugs with that one already."}]}]}]}, {"ts": "**********.117169", "text": "at least the main account. Bonus is extensively used this time unlike last year where we might expect some bugs.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Lc36R", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "at least the main account. Bonus is extensively used this time unlike last year where we might expect some bugs."}]}]}]}, {"ts": "**********.607869", "text": "If Valgenesis doing well then Curana would be in the same state.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "iPLGQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If Valgenesis doing well then Curana would be in the same state."}]}]}]}, {"ts": "**********.662949", "text": "Ok! I'm trying to anticipate what could blow up our days on Monday and Tuesday :see_no_evil:", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WjNqH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok! I'm trying to anticipate what could blow up our days on Monday and Tuesday "}, {"type": "emoji", "name": "see_no_evil", "unicode": "1f648"}]}]}]}, {"ts": "**********.869339", "text": "SSO works if role is assigned and manager role is assigned automatically when cycle is published. So we should be good there.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1740156844.000000"}, "blocks": [{"type": "rich_text", "block_id": "LF2sx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SSO works if role is assigned and manager role is assigned automatically when cycle is published. So we should be good there."}]}]}]}, {"ts": "**********.614159", "text": "Curana starts Monday with their main cycle, and Tuesday with their MIP cycle. They may add a few more recommenders to the MIP; will we need to do anything for SSO? Anything else we need to do for SSO in the main account? There are so many recommenders now.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zmiAz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Curana starts Monday with their main cycle, and Tuesday with their MIP cycle. They may add a few more recommenders to the MIP; will we need to do anything for SSO? Anything else we need to do for SSO in the main account? There are so many recommenders now."}]}]}]}, {"ts": "**********.295109", "text": "SHHHHHH MAHESH", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "smile", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "GY0LP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SHHHHHH MAHESH"}]}]}]}, {"ts": "**********.924829", "text": "Val<PERSON> seems to be pretty active but haven’t reported any issues. It feels too good to be true :smile: ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "UA+64", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Val<PERSON> seems to be pretty active but "}, {"type": "text", "text": "haven’t"}, {"type": "text", "text": " reported any issues. It feels too good to be true "}, {"type": "emoji", "name": "smile", "unicode": "1f604"}, {"type": "text", "text": " "}]}]}]}, {"ts": "**********.872589", "text": "Just FYI, I met with the ELT at Curana, and they decided NOT to use the market adjustment field after all. However, <PERSON><PERSON><PERSON> wants to use it (with the compa ratio feature) so it was still worth doing. :slightly_smiling_face:", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "u9eLq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just FYI, I met with the ELT at Curana, and they decided NOT to use the market adjustment field after all. However, <PERSON><PERSON><PERSON> wants to use it (with the compa ratio feature) so it was still worth doing. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1740152251.965019", "text": "Question: Should we generate letters for promotion cases as the increment adjustments are not upload?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "awOYj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Question: Should we generate letters for promotion cases as the increment adjustments are not upload?"}]}]}]}, {"ts": "1740152174.247849", "text": "<@U07EJ2LP44S> Please find adjustment letters for Val<PERSON> with current cycle state. Please let me know if any issues.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739980228.234569", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "xMptv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please find adjustment letters for <PERSON><PERSON> with current cycle state. Please let me know if any issues."}]}]}]}, {"ts": "1740110871.824349", "text": "<@U07EJ2LP44S> We will be wrapping up all the work by mid of next week (delayed by few days). Please follow up with Tithely for updated benefits data.\ncc: <@U07MH77PUBV>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739980263.587899", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "keV85", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We will be wrapping up all the work by mid of next week (delayed by few days). Please follow up with <PERSON><PERSON><PERSON> for updated benefits data.\ncc: "}, {"type": "user", "user_id": "U07MH77PUBV"}]}]}]}, {"ts": "1740110258.720519", "text": "<@U07EJ2LP44S> This is done. Please let me know if anything missed. There are few employees missing ratings.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740070952.514879", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "xZAir", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is done. Please let me know if anything missed. There are few employees missing ratings."}]}]}]}], "created_at": "2025-05-22T21:35:34.710462"}