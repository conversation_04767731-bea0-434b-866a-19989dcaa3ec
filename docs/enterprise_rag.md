# Enterprise RAG Implementation

This document describes the implementation of the Enterprise RAG system using LlamaIndex's advanced capabilities.

## Overview

The Enterprise RAG system is a significant improvement over the previous RAG implementation, leveraging LlamaIndex's advanced features for better retrieval, processing, and response generation. The system consists of three main components:

1. **EnterpriseRAGService**: Core service for handling RAG queries with advanced routing and citation.
2. **MultiModalQueryEngine**: Advanced query engine for complex queries using sub-questions and multiple indices.
3. **EnterpriseRetrieverEngine**: Advanced retrieval engine with hybrid search and post-processing capabilities.

## Components

### EnterpriseRAGService

The `EnterpriseRAGService` is the main entry point for RAG queries. It provides:

- **Intelligent Query Routing**: Routes queries to specialized query engines based on query content.
- **Automatic Citation**: Automatically generates citations for retrieved documents.
- **Multi-Step Reasoning**: Handles complex queries with multi-step reasoning.
- **Specialized Query Engines**: Provides specialized query engines for different data types (conversations, code, documents).

```python
from apps.search.services.enterprise_rag_service import EnterpriseRAGService

# Create Enterprise RAG Service
rag_service = EnterpriseRAGService(user=request.user, tenant_slug=tenant_slug)

# Execute search
search_result, retrieved_docs = rag_service.search(
    query_text="What are the key features of our product?",
    top_k=5,
    use_hybrid_search=True,
    use_context_aware=True,
    output_format="text",
)
```

### MultiModalQueryEngine

The `MultiModalQueryEngine` provides advanced query capabilities using multiple indices and sub-questions. It supports:

- **Sub-Question Query Engine**: Breaks down complex queries into simpler sub-questions.
- **Composable Graph Query Engine**: Handles structured data with graph-based queries.
- **Knowledge Graph Query Engine**: Processes entity relationships with knowledge graph queries.

```python
from apps.search.engines.multi_modal_engine import MultiModalQueryEngine

# Create Multi-Modal Query Engine
engine = MultiModalQueryEngine(tenant_slug=tenant_slug)

# Execute query
import asyncio
response = asyncio.run(engine.query(
    query_text="What are the key features of our product?",
    use_sub_questions=True,
    max_sub_questions=3,
))
```

### EnterpriseRetrieverEngine

The `EnterpriseRetrieverEngine` provides advanced retrieval capabilities with hybrid search and post-processing. It supports:

- **Hybrid Retrieval**: Combines vector search and keyword search for better results.
- **Post-Processing Pipeline**: Filters and reranks retrieved nodes for better relevance.
- **Intelligent Routing**: Routes queries to the most appropriate retrieval method.

```python
from apps.search.retrievers.enterprise_retrievers import EnterpriseRetrieverEngine

# Create Enterprise Retriever Engine
engine = EnterpriseRetrieverEngine(tenant_slug=tenant_slug)

# Execute retrieval
nodes = engine.retrieve(
    query_text="What are the key features of our product?",
    top_k=5,
    similarity_cutoff=0.7,
    use_hybrid=True,
)
```

## LlamaIndex Ingestion Service

The `LlamaIngestionService` provides advanced document ingestion capabilities using LlamaIndex's IngestionPipeline. It supports:

- **Specialized Pipelines**: Different pipelines for different content types (conversations, code, documents).
- **Advanced Node Parsing**: Intelligent chunking strategies for different content types.
- **Metadata Extraction**: Automatic extraction of metadata from documents.
- **Embedding Generation**: Automatic generation of embeddings for documents.

```python
from apps.documents.services.llama_ingestion_service import LlamaIngestionService

# Create LlamaIngestion Service
ingestion_service = LlamaIngestionService(tenant_slug=tenant_slug)

# Ingest documents
import asyncio
node_ids = asyncio.run(ingestion_service.ingest_documents(
    documents=documents,
    content_type="documents",
    job=processing_job,
))
```

## API Integration

The Enterprise RAG system is integrated with the existing API endpoints, replacing the previous RAG implementation. The main API endpoint is:

```
POST /api/search/
```

Request body:
```json
{
    "query": "What are the key features of our product?",
    "top_k": 5,
    "tenant_slug": "stride",
    "filter": {"source_type": "slack"},
    "use_hybrid_search": true,
    "use_context_aware": true,
    "use_query_expansion": false,
    "use_multi_step_reasoning": false,
    "output_format": "text",
    "min_relevance_score": 0.4
}
```

Response:
```json
{
    "status": "success",
    "data": {
        "query": "What are the key features of our product?",
        "answer": "Our product has the following key features: ...",
        "timestamp": "2023-01-01T00:00:00Z",
        "metrics": {
            "retriever_score": 0.85,
            "confidence_score": 0.9,
            "processing_time": "0.25s"
        },
        "sources": [
            {
                "id": 1,
                "text": "Our product has the following key features: ...",
                "relevance": 0.9,
                "metadata": {
                    "title": "Product Documentation",
                    "url": "https://example.com/docs",
                    "source": "web",
                    "created_at": "2023-01-01T00:00:00Z"
                }
            }
        ]
    }
}
```

## Testing

The Enterprise RAG system can be tested using the provided test script:

```bash
python scripts/test_enterprise_rag.py
```

This script tests all three components of the Enterprise RAG system with various queries and configurations.

## Future Improvements

1. **Evaluation Framework**: Implement an evaluation framework for continuous improvement.
2. **Agent-Based RAG**: Implement agent-based RAG for complex workflows.
3. **Async Processing Pipeline**: Implement an async processing pipeline for better performance.
4. **Caching and Performance Optimization**: Implement caching and performance optimization for faster responses.
