{"date": "2025-03-07", "channel_id": "C065QSSNH8A", "message_count": 14, "messages": [{"ts": "1741369142.896779", "text": "Yes I’ll have them do that. They also do need to update the 401(k) number, so is it better just to have that all together?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741369142.896779", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "JCvZh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes "}, {"type": "text", "text": "I’ll"}, {"type": "text", "text": " have them do that. They also do need to update the 401(k) number, so is it better just to have that all together?"}]}]}]}, {"ts": "1741365433.652329", "text": "<@U07EJ2LP44S> Is tithely sending us a file that just includes those salary changes?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741287529.741899", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "zQVRB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Is tithely sending us a file that just includes those salary changes?"}]}]}]}, {"ts": "1741361559.316839", "text": "Will add the employee. That issue is fixed.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5GZsA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will add the employee. That issue is fixed."}]}]}]}, {"ts": "1741361527.664849", "text": "Because her hire date is after the cutoff.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BsjPN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Because her hire date is after the cutoff."}]}]}]}, {"ts": "1741361503.455639", "text": "<@U0690EB5JE5> Another addition for Curana, and I wonder if this will have the same issue?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741361503.455639", "reply_count": 1, "files": [{"id": "F08GR1W38SF", "created": 1741361498, "timestamp": 1741361498, "name": "<PERSON><PERSON>d_Employees_Curana-Stride 01.23 (1) (1).xlsx", "title": "<PERSON><PERSON>d_Employees_Curana-Stride 01.23 (1) (1).xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 971968, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GR1W38SF/mariah_hanson_add_employees_curana-stride_01.23__1___1_.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GR1W38SF/download/mariah_hanson_add_employees_curana-stride_01.23__1___1_.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GR1W38SF-1f18d172a0/maria<PERSON>_hanson_add_employees_curana-stride_01.23__1___1__converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GR1W38SF-1f18d172a0/mariah_hanson_add_employees_curana-stride_01.23__1___1__thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GR1W38SF/mariah_hanson_add_employees_curana-stride_01.23__1___1_.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GR1W38SF-64d1b57c90", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "BF5VU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Another addition for <PERSON><PERSON><PERSON>, and I wonder if this will have the same issue?"}]}]}]}, {"ts": "1741344297.283169", "text": "I found the band Id based on the title and deleted. Done.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741292496.133329", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1741348705.000000"}, "blocks": [{"type": "rich_text", "block_id": "7M6uv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I found the band Id based on the title and deleted. Done."}]}]}]}, {"ts": "1741344252.124149", "text": "<@U07EJ2LP44S> This is taken care. Unlike other customers we didn't delete and create a new cycle after the data updates and there is an issue with uploads which doesn't carry title changes to cycle, However I have mitigated the issue should be good now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741293660.038459", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "F/wki", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is taken care. Unlike other customers we didn't delete and create a new cycle after the data updates and there is an issue with uploads which doesn't carry title changes to cycle, However I have mitigated the issue should be good now."}]}]}]}, {"ts": "1741344153.351829", "text": "<@U07M6QKHUC9> fix is deployed. Please note for this employee proration is 0.16 and prorated increase will be way lesser than the actual proposed value.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741220267.544949", "subtype": "thread_broadcast", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "VTs+8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " fix is deployed. Please note for this employee proration is 0.16 and prorated increase will be way lesser than the actual proposed value."}]}]}]}, {"ts": "1741320608.317919", "text": "Will take a look at the requests/issues my evening.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "iFAXu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take a look at the requests/issues my evening."}]}]}]}, {"ts": "1741293660.038459", "text": "<@U0690EB5JE5> This looks like a bug for Tithely:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741293660.038459", "reply_count": 2, "files": [{"id": "F08G4708N15", "created": 1741293656, "timestamp": 1741293656, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 150781, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08G4708N15/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08G4708N15/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_360.png", "thumb_360_w": 360, "thumb_360_h": 121, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_480.png", "thumb_480_w": 480, "thumb_480_h": 161, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_720.png", "thumb_720_w": 720, "thumb_720_h": 241, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_800.png", "thumb_800_w": 800, "thumb_800_h": 268, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_960.png", "thumb_960_w": 960, "thumb_960_h": 322, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G4708N15-ee7c6694a5/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 343, "original_w": 1498, "original_h": 502, "thumb_tiny": "AwAQADC+YwCeScnPNOwfWlNFACc+tGD/AHqU9KUdKAEwfWgA55NLRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08G4708N15/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08G4708N15-db638fb6e3", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "6dxeg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " This looks like a bug for <PERSON><PERSON><PERSON>:"}]}]}]}, {"ts": "1741292496.133329", "text": "<@U0690EB5JE5> A few updates to Tithely bands. One of the deletes doesn't have a band ID, so I'm not sure if that one will come out? It doesn't have one in the system at all.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741292496.133329", "reply_count": 1, "files": [{"id": "F08GN1LGWAG", "created": 1741292492, "timestamp": 1741292492, "name": "TithelyBandsUpdateMar6.csv", "title": "TithelyBandsUpdateMar6.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1346, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GN1LGWAG/tithelybandsupdatemar6.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GN1LGWAG/download/tithelybandsupdatemar6.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GN1LGWAG/tithelybandsupdatemar6.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GN1LGWAG-2b288c3b96", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GN1LGWAG/tithelybandsupdatemar6.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Band Id,Effective Start Date,Effective End Date,Compensation Grade,Location (Country),Location (Region),Team,Department,Job Family,Job Subfamily,Description,Tier,Job Category,Job Level,Job Title,Frequency,Salary Currency,Salary Min Amount,Salary Target Amount,Salary Max Amount,Variable Pay Currency,Variable Pay Target (%),Variable Pay Target Amount,Equity Currency,Equity Target Amount,Equity Target (unit),Equity Target (%),Min Equity (unit),Max Equity (unit),Equity Participation Rate (%),OTE Min Amount,OTE Target Amount,OTE Max Amount,Pay Mix\r\nU,84,01/01/2021,,,CA,,,Customer Experience,Customer Experience,,,0,IC,0,Education Specialist II & Ascend Bookkeeping,Annual,CAD,20080,25100,30120,,0,0,,0,0,0,0,0,0,0,0,0,\r\nD,,01/01/2021,,,CA,,,Engineering,Engineering,,,0,Mgr,0,Sr. Engineering Manager I,Annual ,CAD,122600,153200,183800,,0,0,,0,0,0,0,0,0,0,0,0,\r\nD,18,01/01/2021,,,US,,,Customer Experience,Customer Experience,,,0,IC,0,Education Specialist,Annual,USD,47520,59400,71280,,0,0,,0,0,0,0,0...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Band Id</div><div class=\"cm-col\">Effective Start Date</div><div class=\"cm-col\">Effective End Date</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Location (Country)</div><div class=\"cm-col\">Location (Region)</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">Job Subfamily</div><div class=\"cm-col\">Description</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Frequency</div><div class=\"cm-col\">Salary Currency</div><div class=\"cm-col\">Salary Min Amount</div><div class=\"cm-col\">Salary Target Amount</div><div class=\"cm-col\">Salary Max Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay Target (%)</div><div class=\"cm-col\">Variable Pay Target Amount</div><div class=\"cm-col\">Equity Currency</div><div class=\"cm-col\">Equity Target Amount</div><div class=\"cm-col\">Equity Target (unit)</div><div class=\"cm-col\">Equity Target (%)</div><div class=\"cm-col\">Min Equity (unit)</div><div class=\"cm-col\">Max Equity (unit)</div><div class=\"cm-col\">Equity Participation Rate (%)</div><div class=\"cm-col\">OTE Min Amount</div><div class=\"cm-col\">OTE Target Amount</div><div class=\"cm-col\">OTE Max Amount</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">84</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">CA</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Education Specialist II &amp; Ascend Bookkeeping</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">CAD</div><div class=\"cm-col cm-num\">20080</div><div class=\"cm-col cm-num\">25100</div><div class=\"cm-col cm-num\">30120</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col\"></div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">CA</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Mgr</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Sr. Engineering Manager I</div><div class=\"cm-col\">Annual </div><div class=\"cm-col\">CAD</div><div class=\"cm-col cm-num\">122600</div><div class=\"cm-col cm-num\">153200</div><div class=\"cm-col cm-num\">183800</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">18</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Education Specialist</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">47520</div><div class=\"cm-col cm-num\">59400</div><div class=\"cm-col cm-num\">71280</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">21</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Lifetime Customer Success Manager</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">49200</div><div class=\"cm-col cm-num\">61500</div><div class=\"cm-col cm-num\">73800</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">24</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Operations Specialist</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">48320</div><div class=\"cm-col cm-num\">60400</div><div class=\"cm-col cm-num\">72480</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 6, "lines_more": 2, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "tfCFl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " A few updates to Tithely bands. One of the deletes doesn't have a band ID, so I'm not sure if that one will come out? It doesn't have one in the system at all."}]}]}]}, {"ts": "1741287658.545099", "text": "<@U07EJ2LP44S> regarding <PERSON>'s latest question on allowing managers to make changes after salary increases have been accepted by them, how do you want to handle this?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741287658.545099", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "BzgHO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " regarding <PERSON>'s latest question on allowing managers to make changes after salary increases have been accepted by them, how do you want to handle this?"}]}]}]}, {"ts": "1741287529.741899", "text": "And also, they would like to exclude the change history items that don't include a salary change. So for example if there wa a title change and it didn't impact the salary. Could we either exclude these items ourselves, or could they send over a file that just includes those salary changes and we could use that data instead?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741287529.741899", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "OHylN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And also, they would like to exclude the change history items that don't include a salary change. So for example if there wa a title change and it didn't impact the salary. Could we either exclude these items ourselves, or could they send over a file that just includes those salary changes and we could use that data instead?"}]}]}]}, {"ts": "1741287432.768859", "text": "<@U0690EB5JE5> For total rewards, can we change the language in that area above the salaries:\n\nCurrent Salary = New Salary\nPrevious Salary (keep same)\nFirst Salary = Starting Salary", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741287432.768859", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "YtNNH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " For total rewards, can we change the language in that area above the salaries:\n\nCurrent Salary = New Salary\nPrevious Salary (keep same)\nFirst Salary = Starting Salary"}]}]}]}], "created_at": "2025-05-22T21:35:34.714786"}