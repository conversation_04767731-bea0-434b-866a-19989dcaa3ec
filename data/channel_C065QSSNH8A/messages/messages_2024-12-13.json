{"date": "2024-12-13", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1734112157.732929", "text": "<@U07EJ2LP44S> at 15Five, were you involved in headcount planning at all?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734112157.732929", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "C6KdA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " at 15Five, were you involved in headcount planning at all?"}]}]}]}, {"ts": "1734108639.933309", "text": "<@U0690EB5JE5> SSO for Diversified - certificate/URL in your inbox; can you review when you return?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734108639.933309", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "AqXAU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " SSO for Diversified - certificate/URL in your inbox; can you review when you return?"}]}]}]}, {"ts": "1734108461.511459", "text": "It is ready to close. They gave some feedback on people insights that I will write up, but otherwise she said she gave her feedback throughout the cycle, so nothing new. They also said the report exports are 'really ugly' LOL so I think we could do better there. We can do a total wrap up call and discuss a testimonial/case study, but we'll need to do that after the new year. They still have a ton going on with the backend work of the cycle, and they are starting open enrollment now.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734108461.511459", "reply_count": 5, "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "QTEIY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It is ready to close. They gave some feedback on people insights that I will write up, but otherwise she said she gave her feedback throughout the cycle, so nothing new. They also said the report exports are 'really ugly' LOL so I think we could do better there. We can do a total wrap up call and discuss a testimonial/case study, but we'll need to do that after the new year. They still have a ton going on with the backend work of the cycle, and they are starting open enrollment now."}]}]}]}, {"ts": "1734107125.549309", "text": "<@U07EJ2LP44S> do we have any updates from Alaya Care on the closing of their cycle? I'd also love to attend the feedback call with them. Any idea when <PERSON> is going to be able to go over that with us?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pOVDQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " do we have any updates from <PERSON><PERSON> Care on the closing of their cycle? I'd also love to attend the feedback call with them. Any idea when <PERSON> is going to be able to go over that with us?"}]}]}]}, {"ts": "1734052853.597209", "text": "Should we cancel the leadership meeting tomorrow? <PERSON><PERSON><PERSON> is out of the office.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734052853.597209", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "jbvv2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we cancel the leadership meeting tomorrow? <PERSON><PERSON><PERSON> is out of the office."}]}]}]}, {"ts": "**********.186369", "text": "<@U0690EB5JE5> Degenkolb is ready to start generating their letters. Do you need anything else to kick off this process? Ideally, the letters can be exported by team so they are easier to distribute (like we talked about before, just putting them into folders vs downloads in the account). We'll want to QA a few of them once we've generated.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.186369", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "7V4/N", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Degenkolb is ready to start generating their letters. Do you need anything else to kick off this process? Ideally, the letters can be exported by team so they are easier to distribute (like we talked about before, just putting them into folders vs downloads in the account). We'll want to QA a few of them once we've generated."}]}]}]}], "created_at": "2025-05-22T21:35:34.678375"}