{"date": "2025-01-07", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1736204830.191109", "text": "Last raise date not a column in org view or in cycle builder - eligibility rules (or in the full employee data export). Need to add it. <https://compiify.atlassian.net/browse/COM-4048>", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14131::e1022c4d6df2456eaf02bbd8a1395a26", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4048?atlOrigin=eyJpIjoiMThjMzk5NTQ4MjI1NDc4NDg5N2Q0NDk1NWMwMjBiMzkiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4048 Missing Last Raise Date in Valgenesis Eligibility Rules/Org View>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14131::1905b57f20b3423581d7450bf880937a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14131\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4048\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4048", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "VTfiz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Last raise date not a column in org view or in cycle builder - eligibility rules (or in the full employee data export). Need to add it. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4048"}]}]}]}, {"ts": "1736203157.146119", "text": "Valgenesis bug in cycle - total cash comp calculation (bonus related): <https://compiify.atlassian.net/browse/COM-4047>", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14130::998a88c7d3274376aa47ecfb63022457", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4047?atlOrigin=eyJpIjoiNjYzYTMzMmQ5NjQ5NDM3OWI1MDUwNzdkZGYyOTBhYTAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4047 Issue: 'New' Total Target Cash Calculation Error for Bonus Eligible…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14130::f67404fe9c07438f882e1c8fea05a43c", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14130\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4047\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4047", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Q097F", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis bug in cycle - total cash comp calculation (bonus related): "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4047"}]}]}]}], "created_at": "2025-05-22T21:35:34.696027"}