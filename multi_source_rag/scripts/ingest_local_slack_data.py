#!/usr/bin/env python
"""
Comprehensive Slack Data Ingestion Script using LocalSlackInterface

This script ingests Slack data from the data/ folder using the LocalSlackInterface
and the enhanced UnifiedIngestionService with configurable chunking strategies
and enhanced metadata.

Features:
- Uses LocalSlackInterface for optimized Slack data processing
- Applies enhanced metadata for better filtering capabilities
- Supports skip-chunking for pre-optimized Slack documents
- Comprehensive error handling and progress tracking
- Database cleaning and setup options
- Detailed logging and statistics

Usage:
    python scripts/ingest_local_slack_data.py [options]
    
Options:
    --clean-db          Clean database before ingestion
    --channel-id        Specific channel ID to process (default: C065QSSNH8A)
    --time-period       Time period for aggregation (monthly, weekly, daily, custom)
    --days-back         Number of days to look back (default: 365)
    --dry-run           Show what would be processed without actual ingestion
    --batch-size        Number of documents to process in each batch (default: 10)
    --include-threads   Include thread messages (default: True)
    --filter-bots       Filter out bot messages (default: True)
"""

import os
import sys
import django
import logging
import argparse
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from django.db import transaction
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk, EmbeddingMetadata
from apps.documents.interfaces.local_slack import LocalSlackSourceInterface
from apps.documents.services.llama_ingestion_service_unified import UnifiedIngestionService
from apps.core.utils.collection_manager import get_collection_name

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('slack_ingestion.log')
    ]
)
logger = logging.getLogger(__name__)


class SlackDataIngestionManager:
    """
    Manages the complete Slack data ingestion process using LocalSlackInterface.
    """
    
    def __init__(
        self,
        data_dir: str = "data",
        tenant_slug: str = "default",
        channel_id: str = "C065QSSNH8A"
    ):
        """
        Initialize the ingestion manager.
        
        Args:
            data_dir: Path to the data directory
            tenant_slug: Tenant slug for data isolation
            channel_id: Slack channel ID to process
        """
        self.data_dir = Path(data_dir)
        self.tenant_slug = tenant_slug
        self.channel_id = channel_id
        
        # Get or create tenant
        self.tenant, created = Tenant.objects.get_or_create(
            slug=tenant_slug,
            defaults={"name": f"Tenant {tenant_slug}"}
        )
        if created:
            logger.info(f"Created new tenant: {tenant_slug}")
        
        # Initialize statistics
        self.stats = {
            "start_time": datetime.now(),
            "documents_processed": 0,
            "documents_created": 0,
            "chunks_created": 0,
            "embeddings_created": 0,
            "errors": 0,
            "skipped": 0
        }
    
    def clean_database(self, confirm: bool = False) -> bool:
        """
        Clean existing data for the tenant and channel.
        
        Args:
            confirm: Whether to proceed without confirmation
            
        Returns:
            bool: True if cleaning was performed
        """
        if not confirm:
            response = input(f"⚠️  Clean all data for tenant '{self.tenant_slug}' and channel '{self.channel_id}'? (y/N): ")
            if response.lower() != 'y':
                logger.info("Database cleaning cancelled")
                return False
        
        logger.info("🧹 Cleaning database...")
        
        try:
            with transaction.atomic():
                # Get document source
                source = DocumentSource.objects.filter(
                    tenant=self.tenant,
                    source_type="local_slack"
                ).first()
                
                if source:
                    # Delete related data
                    chunks_deleted = DocumentChunk.objects.filter(
                        tenant=self.tenant,
                        document__source=source
                    ).count()
                    
                    docs_deleted = RawDocument.objects.filter(
                        tenant=self.tenant,
                        source=source
                    ).count()
                    
                    # Perform deletion
                    DocumentChunk.objects.filter(
                        tenant=self.tenant,
                        document__source=source
                    ).delete()
                    
                    RawDocument.objects.filter(
                        tenant=self.tenant,
                        source=source
                    ).delete()
                    
                    logger.info(f"✅ Cleaned {docs_deleted} documents and {chunks_deleted} chunks")
                else:
                    logger.info("No existing data found to clean")
                
                return True
                
        except Exception as e:
            logger.error(f"❌ Error cleaning database: {str(e)}")
            return False
    
    def setup_document_source(self) -> DocumentSource:
        """
        Create or get the document source for local Slack data.
        
        Returns:
            DocumentSource: The document source instance
        """
        source, created = DocumentSource.objects.get_or_create(
            tenant=self.tenant,
            name=f"Local Slack - {self.channel_id}",
            source_type="local_slack",
            defaults={
                "config": {
                    "channel_id": self.channel_id,
                    "data_dir": str(self.data_dir),
                    "time_period": "monthly",
                    "enable_summary": False,
                    "quality_threshold": 0.3
                }
            }
        )
        
        if created:
            logger.info(f"✅ Created document source: {source.name}")
        else:
            logger.info(f"📋 Using existing document source: {source.name}")
        
        return source
    
    def initialize_local_slack_interface(
        self,
        time_period: str = "monthly",
        include_threads: bool = True,
        filter_bots: bool = True
    ) -> LocalSlackSourceInterface:
        """
        Initialize the LocalSlackInterface with configuration.
        
        Args:
            time_period: Time period for aggregation
            include_threads: Whether to include thread messages
            filter_bots: Whether to filter bot messages
            
        Returns:
            LocalSlackSourceInterface: Configured interface
        """
        config = {
            "channel_id": self.channel_id,
            "data_dir": str(self.data_dir),
            "time_period": time_period,
            "enable_summary": False,
            "quality_threshold": 0.3,
            "max_gap_minutes": 30,
            "max_documents_per_channel": 10000
        }
        
        interface = LocalSlackSourceInterface(config)
        logger.info(f"🔧 Initialized LocalSlackInterface for channel {self.channel_id}")
        logger.info(f"   Time period: {time_period}")
        logger.info(f"   Include threads: {include_threads}")
        logger.info(f"   Filter bots: {filter_bots}")
        
        return interface
    
    def fetch_documents(
        self,
        interface: LocalSlackSourceInterface,
        days_back: int = 365,
        include_threads: bool = True,
        filter_bots: bool = True,
        time_period: str = "monthly"
    ) -> List[Dict[str, Any]]:
        """
        Fetch documents from the LocalSlackInterface.
        
        Args:
            interface: LocalSlackInterface instance
            days_back: Number of days to look back
            include_threads: Whether to include thread messages
            filter_bots: Whether to filter bot messages
            time_period: Time period for aggregation
            
        Returns:
            List[Dict[str, Any]]: List of documents
        """
        logger.info(f"📥 Fetching documents from LocalSlackInterface...")
        logger.info(f"   Days back: {days_back}")
        logger.info(f"   Time period: {time_period}")
        
        try:
            documents = interface.fetch_documents(
                channel_id=self.channel_id,
                days_back=days_back,
                include_threads=include_threads,
                filter_bots=filter_bots,
                time_period=time_period
            )
            
            logger.info(f"✅ Fetched {len(documents)} documents")
            
            # Log document summary
            if documents:
                logger.info("📊 Document Summary:")
                for i, doc in enumerate(documents[:5]):  # Show first 5
                    title = doc.get("title", "Untitled")
                    content_length = len(doc.get("content", ""))
                    metadata = doc.get("metadata", {})
                    message_count = metadata.get("message_count", 0)
                    logger.info(f"   {i+1}. {title} ({content_length} chars, {message_count} messages)")
                
                if len(documents) > 5:
                    logger.info(f"   ... and {len(documents) - 5} more documents")
            
            return documents
            
        except Exception as e:
            logger.error(f"❌ Error fetching documents: {str(e)}")
            self.stats["errors"] += 1
            return []
    
    def ingest_documents(
        self,
        documents: List[Dict[str, Any]],
        source: DocumentSource,
        batch_size: int = 10,
        dry_run: bool = False
    ) -> bool:
        """
        Ingest documents using the UnifiedIngestionService.
        
        Args:
            documents: List of documents to ingest
            source: DocumentSource instance
            batch_size: Number of documents to process in each batch
            dry_run: Whether to perform a dry run
            
        Returns:
            bool: True if ingestion was successful
        """
        if dry_run:
            logger.info(f"🔍 DRY RUN: Would ingest {len(documents)} documents")
            for i, doc in enumerate(documents):
                title = doc.get("title", "Untitled")
                content_length = len(doc.get("content", ""))
                logger.info(f"   {i+1}. {title} ({content_length} chars)")
            return True
        
        logger.info(f"🚀 Starting ingestion of {len(documents)} documents...")
        
        # Initialize ingestion service
        ingestion_service = UnifiedIngestionService(self.tenant_slug)
        
        # Process documents in batches
        total_batches = (len(documents) + batch_size - 1) // batch_size
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(documents))
            batch_docs = documents[start_idx:end_idx]
            
            logger.info(f"📦 Processing batch {batch_num + 1}/{total_batches} ({len(batch_docs)} documents)")
            
            try:
                with transaction.atomic():
                    for doc in batch_docs:
                        try:
                            # Process document through ingestion service
                            result = ingestion_service.process_document(doc, source)
                            
                            if result:
                                self.stats["documents_processed"] += 1
                                self.stats["documents_created"] += 1
                                
                                # Count chunks and embeddings created
                                if hasattr(result, 'chunks'):
                                    self.stats["chunks_created"] += len(result.chunks)
                                
                                logger.debug(f"✅ Processed: {doc.get('title', 'Untitled')}")
                            else:
                                self.stats["skipped"] += 1
                                logger.warning(f"⚠️  Skipped: {doc.get('title', 'Untitled')}")
                                
                        except Exception as e:
                            self.stats["errors"] += 1
                            logger.error(f"❌ Error processing document '{doc.get('title', 'Untitled')}': {str(e)}")
                            continue
                
                logger.info(f"✅ Completed batch {batch_num + 1}/{total_batches}")
                
            except Exception as e:
                logger.error(f"❌ Error processing batch {batch_num + 1}: {str(e)}")
                self.stats["errors"] += 1
                continue
        
        logger.info("🎉 Ingestion completed!")
        return True
    
    def print_statistics(self):
        """Print ingestion statistics."""
        end_time = datetime.now()
        duration = end_time - self.stats["start_time"]
        
        print("\n" + "=" * 60)
        print("📊 INGESTION STATISTICS")
        print("=" * 60)
        print(f"⏱️  Duration: {duration}")
        print(f"📄 Documents processed: {self.stats['documents_processed']}")
        print(f"📄 Documents created: {self.stats['documents_created']}")
        print(f"🧩 Chunks created: {self.stats['chunks_created']}")
        print(f"🔗 Embeddings created: {self.stats['embeddings_created']}")
        print(f"⚠️  Errors: {self.stats['errors']}")
        print(f"⏭️  Skipped: {self.stats['skipped']}")
        
        if self.stats["documents_processed"] > 0:
            avg_time = duration.total_seconds() / self.stats["documents_processed"]
            print(f"⚡ Average time per document: {avg_time:.2f}s")
        
        print("=" * 60)


def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(description="Ingest Slack data using LocalSlackInterface")
    
    parser.add_argument("--clean-db", action="store_true", help="Clean database before ingestion")
    parser.add_argument("--channel-id", default="C065QSSNH8A", help="Channel ID to process")
    parser.add_argument("--time-period", default="monthly", choices=["monthly", "weekly", "daily", "custom"], help="Time period for aggregation")
    parser.add_argument("--days-back", type=int, default=365, help="Number of days to look back")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be processed without actual ingestion")
    parser.add_argument("--batch-size", type=int, default=10, help="Number of documents to process in each batch")
    parser.add_argument("--include-threads", action="store_true", default=True, help="Include thread messages")
    parser.add_argument("--filter-bots", action="store_true", default=True, help="Filter out bot messages")
    parser.add_argument("--data-dir", default="data", help="Path to data directory")
    parser.add_argument("--tenant-slug", default="default", help="Tenant slug")
    
    args = parser.parse_args()
    
    # Initialize ingestion manager
    manager = SlackDataIngestionManager(
        data_dir=args.data_dir,
        tenant_slug=args.tenant_slug,
        channel_id=args.channel_id
    )
    
    try:
        # Clean database if requested
        if args.clean_db:
            if not manager.clean_database():
                logger.error("Database cleaning failed")
                return 1
        
        # Setup document source
        source = manager.setup_document_source()
        
        # Initialize LocalSlackInterface
        interface = manager.initialize_local_slack_interface(
            time_period=args.time_period,
            include_threads=args.include_threads,
            filter_bots=args.filter_bots
        )
        
        # Fetch documents
        documents = manager.fetch_documents(
            interface=interface,
            days_back=args.days_back,
            include_threads=args.include_threads,
            filter_bots=args.filter_bots,
            time_period=args.time_period
        )
        
        if not documents:
            logger.warning("No documents to process")
            return 0
        
        # Ingest documents
        success = manager.ingest_documents(
            documents=documents,
            source=source,
            batch_size=args.batch_size,
            dry_run=args.dry_run
        )
        
        # Print statistics
        manager.print_statistics()
        
        if success:
            logger.info("🎉 Slack data ingestion completed successfully!")
            return 0
        else:
            logger.error("❌ Slack data ingestion failed")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Fatal error: {str(e)}")
        manager.print_statistics()
        return 1


if __name__ == "__main__":
    exit(main())
