{"date": "2024-07-30", "channel_id": "C065QSSNH8A", "message_count": 13, "messages": [{"ts": "1722362406.369689", "text": "All intros to <PERSON> are done except Diversified Energy, DegenKolb, and Sonendo. We will do the intro at the time of implementation call with them.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "s1FTz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All intros to <PERSON> are done except Diversified Energy, DegenKolb, and Sonendo. We will do the intro at the time of implementation call with them."}]}]}]}, {"ts": "1722361155.618809", "text": "<!here> I have created an active cycle and option to edit employee details are now visible. Eng team has introdcued a new field to edit last in the UI which needs a validation. I am running that now. We can send a confirmation to Vercara once its done.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722361155.618809", "reply_count": 2, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "3+2HB", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I have created an active cycle and option to edit employee details are now visible. Eng team has introdcued a new field to edit last in the UI which needs a validation. I am running that now. We can send a confirmation to Vercara once its done."}]}]}]}, {"ts": "1722350824.756599", "text": "<@U04DS2MBWP4> will take a look in sometime.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "rUdtX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " will take a look in sometime."}]}]}]}, {"ts": "1722350793.358919", "text": "quick update- Comp builder design should be complete by Thursday. New Merit planner (with regrouping) ETA is Friday.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Y+Sbu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "quick update- Comp builder design should be complete by Thursday. New Merit planner (with regrouping) ETA is Friday."}]}]}]}, {"ts": "1722350641.388179", "text": "<@U0690EB5JE5> Looks like Vercara is experiencing the same issue of \"3 dots to edit emp data is not showing up\". Cc <@U07EJ2LP44S>", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "56f4f", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Looks like <PERSON><PERSON><PERSON><PERSON> is experiencing the same issue of \"3 dots to edit emp data is not showing up\". Cc "}, {"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1722349739.264919", "text": "<!here> Here is the agenda for today's eng meeting. Feel free to add other agenda items:\nPart-Time Product Manager (<PERSON>)\nCustomer Requirements (resume <PERSON><PERSON><PERSON>'s roadmap agenda)\nCustomer Implementation Roadmap\nComp Builder Requirements Doc", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "h0ZEQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Here is the agenda for today's eng meeting. Feel free to add other agenda items:\nPart-Time Product Manager (<PERSON>)\nCustomer Requirements (resume <PERSON><PERSON><PERSON>'s roadmap agenda)\nCustomer Implementation Roadmap\nComp Builder Requirements Doc"}]}]}]}, {"ts": "1722349539.493299", "text": "has renamed the channel from \"productleadership\" to \"1-productleadership\"", "user": "U04DS2MBWP4", "type": "message", "subtype": "channel_name"}, {"ts": "1722316433.915639", "text": "Sure :+1:", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8x92/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure "}, {"type": "emoji", "name": "+1", "unicode": "1f44d"}]}]}]}, {"ts": "1722316409.395159", "text": "<@U0690EB5JE5> For compliance purpose can you fill up the following template for atleast 2 of your reportees and send it back by wednesday", "user": "U04DKEFP1K8", "type": "message", "files": [{"id": "F07FAC36HSL", "created": 1722316401, "timestamp": 1722316401, "name": "Copy of Employee Performance Review.docx", "title": "Copy of Employee Performance Review.docx", "mimetype": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filetype": "docx", "pretty_type": "Word Document", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 17148, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07FAC36HSL/copy_of_employee_performance_review.docx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07FAC36HSL/download/copy_of_employee_performance_review.docx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FAC36HSL-b73319ac77/copy_of_employee_performance_review_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FAC36HSL-b73319ac77/copy_of_employee_performance_review_thumb_pdf.png", "thumb_pdf_w": 1210, "thumb_pdf_h": 935, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07FAC36HSL/copy_of_employee_performance_review.docx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07FAC36HSL-2afea67bd3", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "hjZQX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " For compliance purpose can you fill up the following template for atleast 2 of your reportees and send it back by wednesday"}]}]}]}, {"ts": "1722283551.030679", "text": "She was one of my favorite PMs, and we've stayed in touch a bit", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "HUu3v", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She was one of my favorite PMs, and we've stayed in touch a bit"}]}]}]}, {"ts": "1722283533.783259", "text": "I LOVE HER", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6xm4q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I LOVE HER"}]}]}]}, {"ts": "1722281690.289139", "text": "<@U07EJ2LP44S> Just got off the call with <PERSON>. She is interested in being an IC part-time product manager. We can discuss her fit during the eng call tomorrow. She was 15<PERSON><PERSON>'s first product manager and likes companies at our stage. May be we can give her  1 to 2 projects from the roadmap that <PERSON><PERSON><PERSON> created and see how it goes. What's your experience with her?\n\n<@U0690EB5JE5> <@U04DKEFP1K8> I will share the details during the eng call tomorrow.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Em+8n", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Just got off the call with <PERSON>. She is interested in being an IC part-time product manager. We can discuss her fit during the eng call tomorrow. She was 15Five's first product manager and likes companies at our stage. May be we can give her  1 to 2 projects from the roadmap that <PERSON><PERSON><PERSON> created and see how it goes. What's your experience with her?\n\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I will share the details during the eng call tomorrow."}]}]}]}, {"ts": "1722278769.901989", "text": "<@U0690EB5JE5> can we pls create login credentials for another advisor (<PERSON>) for testing environment\nemail <mailto:<EMAIL>|<EMAIL>>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722278769.901989", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "c5uUz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we pls create login credentials for another advisor (<PERSON>) for testing environment\nemail "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}]}]}]}], "created_at": "2025-05-22T21:35:34.635104"}