"""
Embedding Model Consistency Manager

This module ensures consistent embedding model usage across the entire RAG system.
It prevents the critical bug where different embedding models with different dimensions
are used for ingestion vs search, which breaks vector similarity search.

Key Features:
- Single source of truth for embedding model configuration
- Automatic dimension validation
- Consistent model initialization across all services
- Environment-based model selection with fallbacks
"""

import logging
import os
from typing import Op<PERSON>, Tuple, Any
from django.conf import settings

logger = logging.getLogger(__name__)

# Global embedding model configuration
_GLOBAL_EMBEDDING_MODEL = None
_GLOBAL_EMBEDDING_DIMENSIONS = None
_EMBEDDING_MODEL_INITIALIZED = False


class EmbeddingModelConfig:
    """Configuration class for embedding model consistency."""

    # Default model configuration (768 dimensions for BGE-base-en-v1.5)
    DEFAULT_MODEL = "BAAI/bge-base-en-v1.5"
    DEFAULT_DIMENSIONS = 768

    # Supported models with their dimensions
    SUPPORTED_MODELS = {
        "BAAI/bge-base-en-v1.5": 768,  # BGE Base English v1.5 (recommended)
        "BAAI/bge-small-en-v1.5": 384,  # BGE Small English v1.5 (faster)
        "BAAI/bge-large-en-v1.5": 1024,  # BGE Large English v1.5 (best quality)
        "sentence-transformers/all-MiniLM-L6-v2": 384,
        "sentence-transformers/all-mpnet-base-v2": 768,
        "text-embedding-ada-002": 1536,  # OpenAI
        "embedding-001": 768,  # Gemini
    }

    @classmethod
    def get_model_config(cls) -> Tuple[str, int]:
        """
        Get the configured embedding model and dimensions.

        Returns:
            Tuple[str, int]: (model_name, dimensions)
        """
        # Check environment variable first
        model_name = os.environ.get("EMBEDDING_MODEL_NAME")

        # Check Django settings
        if not model_name:
            model_name = getattr(settings, "EMBEDDING_MODEL_NAME", cls.DEFAULT_MODEL)

        # Normalize model name
        if model_name == "all-MiniLM-L6-v2":
            model_name = "sentence-transformers/all-MiniLM-L6-v2"

        # Get dimensions
        dimensions = cls.SUPPORTED_MODELS.get(model_name, cls.DEFAULT_DIMENSIONS)

        return model_name, dimensions

    @classmethod
    def is_gemini_preferred(cls) -> bool:
        """Check if Gemini embedding is preferred based on configuration."""
        gemini_api_key = os.environ.get("GEMINI_API_KEY") or getattr(settings, "GEMINI_API_KEY", "")
        use_gemini = os.environ.get("USE_GEMINI_EMBEDDING", "false").lower() == "true"

        return bool(gemini_api_key and use_gemini)


def get_consistent_embedding_model() -> Any:
    """
    Get the globally consistent embedding model.

    This function ensures that the same embedding model is used throughout
    the entire application, preventing dimension mismatches.

    Returns:
        Any: LlamaIndex embedding model instance
    """
    global _GLOBAL_EMBEDDING_MODEL, _GLOBAL_EMBEDDING_DIMENSIONS, _EMBEDDING_MODEL_INITIALIZED

    if _EMBEDDING_MODEL_INITIALIZED and _GLOBAL_EMBEDDING_MODEL is not None:
        return _GLOBAL_EMBEDDING_MODEL

    # CRITICAL FIX: Force the correct model to prevent dimension mismatches
    # Always use BAAI/bge-base-en-v1.5 (768d) to match the vector database
    FORCED_MODEL_NAME = "BAAI/bge-base-en-v1.5"
    FORCED_DIMENSIONS = 768

    try:
        # Use HuggingFace with forced correct model
        from llama_index.embeddings.huggingface import HuggingFaceEmbedding

        logger.info(f"Initializing HuggingFace embedding: {FORCED_MODEL_NAME} (this may take a moment to download)")

        _GLOBAL_EMBEDDING_MODEL = HuggingFaceEmbedding(
            model_name=FORCED_MODEL_NAME,
            embed_batch_size=32,
        )
        _GLOBAL_EMBEDDING_DIMENSIONS = FORCED_DIMENSIONS

        logger.info(f"✅ Initialized consistent HuggingFace embedding: {FORCED_MODEL_NAME} ({FORCED_DIMENSIONS}d)")
        _EMBEDDING_MODEL_INITIALIZED = True
        return _GLOBAL_EMBEDDING_MODEL

    except Exception as e:
        logger.error(f"Failed to initialize forced embedding model: {e}")
        raise RuntimeError(f"Cannot initialize embedding model: {e}")


def get_embedding_dimensions() -> int:
    """
    Get the dimensions of the globally consistent embedding model.

    Returns:
        int: Number of dimensions
    """
    global _GLOBAL_EMBEDDING_DIMENSIONS

    if _GLOBAL_EMBEDDING_DIMENSIONS is None:
        # Initialize the model to get dimensions
        get_consistent_embedding_model()

    return _GLOBAL_EMBEDDING_DIMENSIONS


def set_global_embedding_model(force_reinit: bool = False) -> None:
    """
    Set the global LlamaIndex Settings.embed_model to the consistent model.

    Args:
        force_reinit: Whether to force reinitialization of the model
    """
    global _EMBEDDING_MODEL_INITIALIZED

    if force_reinit:
        _EMBEDDING_MODEL_INITIALIZED = False

    from llama_index.core import Settings

    embedding_model = get_consistent_embedding_model()
    Settings.embed_model = embedding_model

    logger.info(f"Set global LlamaIndex embedding model: {type(embedding_model).__name__} ({get_embedding_dimensions()}d)")


def validate_embedding_consistency() -> bool:
    """
    Validate that the current embedding model is consistent across the system.

    Returns:
        bool: True if consistent, False otherwise
    """
    try:
        from llama_index.core import Settings

        # Get the global model
        global_model = get_consistent_embedding_model()
        global_dimensions = get_embedding_dimensions()

        # Check if Settings.embed_model matches
        if Settings.embed_model is None:
            logger.warning("Settings.embed_model is None")
            return False

        # Check if they're the same type
        if type(Settings.embed_model) != type(global_model):
            logger.error(f"Embedding model type mismatch: {type(Settings.embed_model)} vs {type(global_model)}")
            return False

        logger.info(f"Embedding model consistency validated: {type(global_model).__name__} ({global_dimensions}d)")
        return True

    except Exception as e:
        logger.error(f"Error validating embedding consistency: {e}")
        return False


def reset_embedding_model() -> None:
    """Reset the global embedding model (for testing purposes)."""
    global _GLOBAL_EMBEDDING_MODEL, _GLOBAL_EMBEDDING_DIMENSIONS, _EMBEDDING_MODEL_INITIALIZED

    _GLOBAL_EMBEDDING_MODEL = None
    _GLOBAL_EMBEDDING_DIMENSIONS = None
    _EMBEDDING_MODEL_INITIALIZED = False

    logger.info("Reset global embedding model")


def get_embedding_model_info() -> dict:
    """
    Get information about the current embedding model configuration.

    Returns:
        dict: Model information including name, type, and dimensions
    """
    try:
        model = get_consistent_embedding_model()
        dimensions = get_embedding_dimensions()
        model_name, _ = EmbeddingModelConfig.get_model_config()

        return {
            "model_type": type(model).__name__,
            "model_name": model_name,
            "dimensions": dimensions,
            "is_gemini_preferred": EmbeddingModelConfig.is_gemini_preferred(),
            "initialized": _EMBEDDING_MODEL_INITIALIZED,
        }
    except Exception as e:
        return {
            "error": str(e),
            "initialized": False,
        }
