#!/usr/bin/env python
"""
Test Citation Deduplication Fix

This script tests the fix for the duplicate citation constraint violation issue.
It verifies that:
1. Citations are properly deduplicated
2. No duplicate key constraint violations occur
3. Search results include proper citations

Author: AI Assistant
Date: 2025-01-30
"""

import os
import sys
import django
import logging

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.search.services.rag_service import RAGService
from apps.search.models import SearchResult, ResultCitation

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_citation_deduplication():
    """Test that citations are properly deduplicated."""
    print("🧪 TESTING CITATION DEDUPLICATION FIX")
    print("=" * 60)
    
    # Get tenant and user
    tenant = Tenant.objects.get(slug="test-tenant")
    user, _ = User.objects.get_or_create(
        username="test_user",
        defaults={"email": "<EMAIL>", "is_staff": True}
    )
    
    # Create RAG service
    rag_service = RAGService(user=user, tenant_slug=tenant.slug)
    
    # Test queries that might return duplicate chunks
    test_queries = [
        "budget adherence testing",
        "bug reports and issues",
        "manager recommendations",
        "testing updates and progress"
    ]
    
    total_searches = 0
    total_citations = 0
    errors_encountered = 0
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}: '{query}'")
        print("-" * 40)
        
        try:
            # Get initial counts
            initial_results = SearchResult.objects.count()
            initial_citations = ResultCitation.objects.count()
            
            # Perform search
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=10,
                min_relevance_score=0.15,
                output_format="text"
            )
            
            # Get final counts
            final_results = SearchResult.objects.count()
            final_citations = ResultCitation.objects.count()
            
            # Calculate new records
            new_results = final_results - initial_results
            new_citations = final_citations - initial_citations
            
            print(f"✅ Search completed successfully")
            print(f"📊 New search results: {new_results}")
            print(f"📋 New citations created: {new_citations}")
            print(f"📄 Retrieved documents: {len(retrieved_docs)}")
            
            # Verify no duplicate citations for this result
            result_citations = ResultCitation.objects.filter(result=search_result)
            unique_chunks = set(citation.document_chunk_id for citation in result_citations)
            
            if len(result_citations) == len(unique_chunks):
                print(f"✅ No duplicate citations detected")
            else:
                print(f"❌ Duplicate citations found: {len(result_citations)} citations for {len(unique_chunks)} unique chunks")
                errors_encountered += 1
            
            # Show citation details
            if result_citations.exists():
                print(f"📈 Citation details:")
                for citation in result_citations[:5]:  # Show first 5
                    print(f"   Rank {citation.rank}: Chunk {citation.document_chunk_id}, Score: {citation.relevance_score:.4f}")
            
            total_searches += 1
            total_citations += new_citations
            
        except Exception as e:
            print(f"❌ Search failed: {str(e)}")
            errors_encountered += 1
            logger.error(f"Search error for query '{query}': {e}")
    
    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Total searches completed: {total_searches}")
    print(f"📋 Total citations created: {total_citations}")
    print(f"❌ Errors encountered: {errors_encountered}")
    
    if errors_encountered == 0:
        print(f"🎉 ALL TESTS PASSED - Citation deduplication is working correctly!")
    else:
        print(f"⚠️  Some tests failed - Review errors above")
    
    return errors_encountered == 0


def test_database_constraint_integrity():
    """Test that the database constraint is properly enforced."""
    print(f"\n🔒 TESTING DATABASE CONSTRAINT INTEGRITY")
    print("=" * 60)
    
    # Check for any existing duplicate citations
    from django.db import connection
    
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT result_id, document_chunk_id, COUNT(*) as count
            FROM search_resultcitation 
            GROUP BY result_id, document_chunk_id 
            HAVING COUNT(*) > 1
        """)
        
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"❌ Found {len(duplicates)} duplicate citation pairs:")
            for result_id, chunk_id, count in duplicates[:10]:  # Show first 10
                print(f"   Result {result_id}, Chunk {chunk_id}: {count} citations")
            return False
        else:
            print(f"✅ No duplicate citations found in database")
            return True


def main():
    """Main test function."""
    print("🧪 CITATION DEDUPLICATION FIX TESTING")
    print("=" * 80)
    
    # Test 1: Citation deduplication
    test1_passed = test_citation_deduplication()
    
    # Test 2: Database constraint integrity
    test2_passed = test_database_constraint_integrity()
    
    # Final result
    print(f"\n🎯 FINAL RESULTS")
    print("=" * 80)
    
    if test1_passed and test2_passed:
        print(f"🎉 ALL TESTS PASSED!")
        print(f"✅ Citation deduplication fix is working correctly")
        print(f"✅ No duplicate key constraint violations")
        print(f"✅ Database integrity maintained")
    else:
        print(f"❌ SOME TESTS FAILED!")
        if not test1_passed:
            print(f"❌ Citation deduplication test failed")
        if not test2_passed:
            print(f"❌ Database constraint integrity test failed")


if __name__ == "__main__":
    main()
