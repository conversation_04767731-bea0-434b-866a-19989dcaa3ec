{"date": "2024-03-07", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1709759411.927119", "text": "<@U065H3M6WJV> <PERSON><PERSON><PERSON> had reported that letter generation on latest build is failing. I have verified that latest merge did broke the build and generation still works fine on previous build. Opened <https://compiify.atlassian.net/browse/COM-2467> and have captured stack trace for the failure.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1709759411.927119", "reply_count": 4, "reactions": [{"name": "thankyouty", "users": ["U065H3M6WJV", "U0690EB5JE5"], "count": 2}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12528::ec1a75f0dbfd11eebae1817f9de92744", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2467?atlOrigin=eyJpIjoiNGVmZDVjN2UwYmZiNGUyZmJkYWY0NDg4NTYxZDVjNmMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2467 Letter generation is broken on latest build with commit id (49fe03d…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12528::ec1a75f2dbfd11eebae1817f9de92744", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12528::ec1a75f1dbfd11eebae1817f9de92744", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12528\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12528\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2467", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "4WBa6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> had reported that letter generation on latest build is failing. I have verified that latest merge did broke the build and generation still works fine on previous build. Opened "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2467"}, {"type": "text", "text": " and have captured stack trace for the failure."}]}]}]}, {"ts": "**********.421369", "text": "Lets make me for now", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "XYEvT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Lets make me for now"}]}]}]}, {"ts": "**********.151409", "text": "<@U04DS2MBWP4> Who will take the ownership of AWS Parent account, please let me know", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "V6T7v", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Who will take the ownership of AWS Parent account, please let me know"}]}]}]}, {"ts": "**********.969609", "text": "<@U065H3M6WJV> Following issue is in review and pending deployment <https://compiify.atlassian.net/browse/COM-2456> Just wanted to make sure you and <PERSON><PERSON><PERSON> have a plan for the same", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.969609", "reply_count": 5, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12517::6627b5a0dbeb11eeb1cd7dfb83c6047f", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2456?atlOrigin=eyJpIjoiMjI4OGI3YzUwZThkNDQyYzk1ZmU0MzJjM2E4YTExZDQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2456 Cannot approve or overwrite merit recommendations as a Super Admin>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12517::6627b5a2dbeb11eeb1cd7dfb83c6047f", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/79ad6fec1eb23766c2c2c46cb17078ab?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12517::6627b5a1dbeb11eeb1cd7dfb83c6047f", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12517\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12517\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2456", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "mXkDs", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Following issue is in review and pending deployment "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2456"}, {"type": "text", "text": " Just wanted to make sure you and <PERSON><PERSON><PERSON> have a plan for the same"}]}]}]}], "created_at": "2025-05-22T21:35:34.611665"}