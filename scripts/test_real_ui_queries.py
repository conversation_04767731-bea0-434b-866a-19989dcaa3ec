#!/usr/bin/env python3
"""
Real UI Query Testing - Test the actual queries you mentioned
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def test_ui_query_direct(query, description):
    """Test a query directly through the search service."""
    
    print(f"\n🔍 Testing: {description}")
    print(f"📝 Query: '{query}'")
    print("-" * 60)
    
    try:
        from apps.search.services.rag_service import RAGService
        from django.contrib.auth.models import User
        
        user = User.objects.first()
        rag_service = RAGService(user=user, tenant_slug='stride')
        
        # Test with very low threshold to ensure we get results
        search_result, retrieved_docs = rag_service.search(
            query_text=query,
            top_k=10,
            min_relevance_score=0.1,  # Very low threshold
            use_hybrid_search=True,
            use_context_aware=True,
            use_query_expansion=False,
            use_multi_step_reasoning=False
        )
        
        citations_count = search_result.citations.count()
        response_length = len(search_result.generated_answer)
        
        print(f"✅ Search completed successfully")
        print(f"📊 Citations found: {citations_count}")
        print(f"📝 Response length: {response_length} characters")
        print(f"🎯 Confidence: {search_result.llm_confidence_score:.2f}")
        
        # Check response quality
        response = search_result.generated_answer
        
        # Check for document references
        has_doc_refs = '[Document' in response
        print(f"📄 Document references: {'❌ Found' if has_doc_refs else '✅ Clean'}")
        
        # Check for human dates
        import re
        date_pattern = r'\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}\b'
        human_dates = re.findall(date_pattern, response)
        print(f"📅 Human dates: {'✅ Found ' + str(len(human_dates)) if human_dates else '⚠️ None found'}")
        
        # Show sample response
        print(f"\n📝 Response preview:")
        print(response[:300] + "..." if len(response) > 300 else response)
        
        if citations_count > 0:
            print(f"\n📚 Sample citations:")
            for i, citation in enumerate(search_result.citations.all()[:3]):
                print(f"  {i+1}. Score: {citation.relevance_score:.3f}")
                print(f"     Text: {citation.document_chunk.text[:100]}...")
                if citation.document_chunk.document.permalink:
                    print(f"     Link: {citation.document_chunk.document.permalink}")
                print()
        else:
            print("\n❌ No citations found - this is the main UI issue!")
            
        return {
            'success': True,
            'citations_count': citations_count,
            'response_length': response_length,
            'has_doc_refs': has_doc_refs,
            'human_dates_count': len(human_dates),
            'response': response
        }
        
    except Exception as e:
        print(f"❌ Search failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

def test_browser_ui():
    """Test if the browser UI is accessible."""
    
    print("\n🌐 Testing Browser UI Access")
    print("-" * 60)
    
    try:
        response = requests.get("http://127.0.0.1:8000/search/", timeout=5)
        
        if response.status_code == 200:
            print("✅ Search page accessible")
            
            # Check if login is required
            if 'login' in response.url.lower():
                print("🔐 Login required - redirected to login page")
                return False
            else:
                print("✅ Search page loaded successfully")
                return True
        else:
            print(f"⚠️ Unexpected status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server")
        print("💡 Make sure to run: poetry run python manage.py runserver")
        return False
    except Exception as e:
        print(f"❌ Browser test failed: {e}")
        return False

def main():
    """Test all the queries you mentioned."""
    
    print("🚀 Real UI Query Testing")
    print("=" * 80)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test browser accessibility first
    browser_accessible = test_browser_ui()
    
    # Test the specific queries you mentioned
    test_queries = [
        ("list issues reported by Amanda", "List Issues Query - Your main concern"),
        ("summarize discussions on Tithley", "Summarize Query - No output issue"),
        ("whats latest on Curana?", "Latest Updates Query - Looks better"),
        ("What did Amanda say about the Curana API issue?", "Fact Lookup - Specific person/topic"),
        ("Show me the Slack discussion on migrating to Kafka", "Thread Recall - Conversation lookup"),
        ("What was the consensus on using Sentry vs Datadog?", "Opinion Tracking - Decision finding"),
        ("Any issues reported in Slack during the March 2024 release?", "Time-Based Query - Date filtering"),
        ("How do I restart the Kafka consumer?", "Procedural Query - How-to retrieval"),
        ("Any discussion of the 500 errors yesterday?", "Incident Query - Alert related"),
        ("What has Tim been working on lately?", "Person Scoped - Individual focus"),
    ]
    
    results = {}
    
    for query, description in test_queries:
        result = test_ui_query_direct(query, description)
        results[query] = result
    
    # Generate summary report
    print("\n📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 80)
    
    successful_queries = sum(1 for r in results.values() if r.get('success', False))
    total_queries = len(results)
    
    print(f"🎯 Success Rate: {successful_queries}/{total_queries} ({successful_queries/total_queries*100:.0f}%)")
    print(f"🌐 Browser Access: {'✅ Working' if browser_accessible else '❌ Failed'}")
    
    # Analyze citation issues
    queries_with_citations = sum(1 for r in results.values() if r.get('citations_count', 0) > 0)
    queries_without_citations = total_queries - queries_with_citations
    
    print(f"\n📚 Citation Analysis:")
    print(f"✅ Queries with citations: {queries_with_citations}")
    print(f"❌ Queries without citations: {queries_without_citations}")
    
    if queries_without_citations > 0:
        print(f"\n❌ MAIN ISSUE IDENTIFIED: {queries_without_citations} queries return no citations")
        print("💡 This explains why the UI shows no sources/citations")
        print("💡 Need to investigate search relevance thresholds or vector search logic")
    
    # Analyze response quality
    queries_with_doc_refs = sum(1 for r in results.values() if r.get('has_doc_refs', False))
    queries_with_human_dates = sum(1 for r in results.values() if r.get('human_dates_count', 0) > 0)
    
    print(f"\n🎨 Response Quality:")
    print(f"❌ Queries with document references: {queries_with_doc_refs} (should be 0)")
    print(f"✅ Queries with human dates: {queries_with_human_dates}")
    
    # Specific query analysis
    print(f"\n🔍 Specific Query Results:")
    for query, result in results.items():
        if result.get('success'):
            citations = result.get('citations_count', 0)
            status = "✅" if citations > 0 else "❌"
            print(f"{status} '{query[:50]}...': {citations} citations")
        else:
            print(f"❌ '{query[:50]}...': FAILED")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if queries_without_citations > 5:
        print("1. 🔧 CRITICAL: Fix search relevance thresholds - too restrictive")
        print("2. 🔍 Check vector search configuration and embedding matching")
        print("3. 📊 Verify Qdrant collection has correct data")
    
    if queries_with_doc_refs > 0:
        print("4. 🧹 Document reference cleaning needs improvement")
    
    if browser_accessible:
        print("5. 🌐 Test these queries in the actual browser UI")
        print("6. 🎯 Focus on fixing citation retrieval for production readiness")
    else:
        print("5. 🚀 Start the development server first")
    
    return results

if __name__ == "__main__":
    main()
