{"date": "2024-10-20", "channel_id": "C065QSSNH8A", "message_count": 14, "messages": [{"ts": "1729423616.321679", "text": "<@U04DKEFP1K8> I have the bonus budget fixes and some more bug fixes related to budgets accuracy and more.\n<https://github.com/Compiify/Yellowstone/pull/1251/files>\nThis needs to be tested thoroughly for accuracy and correctness before merging. I need a session from you on what to test and how to test as this needs a lot of calculations knowledge ( I don't have the in depth knowledge on the cycle builder). If you could also do a round of testing on this PR would help gain more confidence to merge it before your Monday starts.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729423616.321679", "reply_count": 5, "edited": {"user": "U0690EB5JE5", "ts": "1729424030.000000"}, "blocks": [{"type": "rich_text", "block_id": "DDveK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I have the bonus budget fixes and some more bug fixes related to budgets accuracy and more.\n"}, {"type": "link", "url": "https://github.com/Compiify/Yellowstone/pull/1251/files"}, {"type": "text", "text": "\nThis needs to be tested thoroughly for accuracy and correctness before merging. I need a session from you on what to test and how to test as this needs a lot of calculations knowledge ( I don't have the in depth knowledge on the cycle builder). If you could also do a round of testing on this PR would help gain more confidence to merge it before your Monday starts."}]}]}]}, {"ts": "1729384708.109719", "text": "fixing remote location 4 fixed the data consistency issue with eligible employees count.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729384708.109719", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "uVFtz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "fixing remote location 4 fixed the data consistency issue with eligible employees count."}]}]}]}, {"ts": "1729383653.150269", "text": "In the org view, they have a region remote location 4. I am not seeing that location under filters in the Merit planner so it looks like that location is missing", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729383653.150269", "reply_count": 2, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "oJyEo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In the org view, they have a region remote location 4. I am not seeing that location under filters in the Merit planner so it looks like that location is missing"}]}]}]}, {"ts": "1729381178.966959", "text": "<@U07M6QKHUC9> what did you mean \"also remote location 4 is not showing in their data\"", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5gwOc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " what did you mean \"also remote location 4 is not showing in their data\""}]}]}]}, {"ts": "1729373886.215469", "text": "<@U04DKEFP1K8> can you send me the local login credentials for SDF root user, their CEO and also for <PERSON>?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729373886.215469", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "qwRpm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you send me the local login credentials for SDF root user, their CEO and also for <PERSON>?"}]}]}]}, {"ts": "1729372489.912519", "text": "<@U0690EB5JE5> <https://compiify.atlassian.net/browse/COM-3863>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729372489.912519", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13924::fa82f5e355234833a648e2e201b745de", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3863?atlOrigin=eyJpIjoiOTUzZmY1MDJjNDU4NDljOGJhMjM5ZThiMmUxN2RlZTQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3863 Add Time Zone to Comments>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13924::0a87562f9d9d414fb204ee8a36af7a22", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13924\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3863\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3863", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "tqjaB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3863"}]}]}]}, {"ts": "1729372285.998959", "text": "<@U0690EB5JE5> some potential issues in cycle insights\n<https://compiify.atlassian.net/browse/COM-3862>.", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13923::2cb078d2e1f54da49e05c134ade6395b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3862?atlOrigin=eyJpIjoiYjA3NGM2MWRjYzhkNDM3M2IzNTk3YmIxYTU3NTI2MzQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3862 Budget Status Discrepancies>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13923::2a3f9fe1afd04cbc8ac3cebac9b0ac87", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13923\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3862\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3862", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "WqSUT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " some potential issues in cycle insights\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3862"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1729371821.572189", "text": "<@U0690EB5JE5> when we switch to the people insights, can we please make the default view to be cycle insights instead of org insights?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729371821.572189", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "yU6QA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " when we switch to the people insights, can we please make the default view to be cycle insights instead of org insights?"}]}]}]}, {"ts": "1729371447.374499", "text": "<@U0690EB5JE5> In the past, we used to see the budget allocation for each planning managers's org as well for their direct reports i.e. their team.\n\nAllocation page currently is only showing the budget for their direct team only.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729371447.374499", "reply_count": 3, "files": [{"id": "F07SNBTMPB4", "created": 1729371442, "timestamp": 1729371442, "name": "Screenshot 2024-10-19 at 1.57.18 PM.png", "title": "Screenshot 2024-10-19 at 1.57.18 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 407354, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07SNBTMPB4/screenshot_2024-10-19_at_1.57.18___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07SNBTMPB4/download/screenshot_2024-10-19_at_1.57.18___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SNBTMPB4-ff247027f7/screenshot_2024-10-19_at_1.57.18___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SNBTMPB4-ff247027f7/screenshot_2024-10-19_at_1.57.18___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SNBTMPB4-ff247027f7/screenshot_2024-10-19_at_1.57.18___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 156, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SNBTMPB4-ff247027f7/screenshot_2024-10-19_at_1.57.18___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 208, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SNBTMPB4-ff247027f7/screenshot_2024-10-19_at_1.57.18___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SNBTMPB4-ff247027f7/screenshot_2024-10-19_at_1.57.18___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 311, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SNBTMPB4-ff247027f7/screenshot_2024-10-19_at_1.57.18___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 346, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SNBTMPB4-ff247027f7/screenshot_2024-10-19_at_1.57.18___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 415, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SNBTMPB4-ff247027f7/screenshot_2024-10-19_at_1.57.18___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 443, "original_w": 3886, "original_h": 1680, "thumb_tiny": "AwAUADDSFLSDrS0AFFFFACUUUGgBaKKKACiiigAooooA/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07SNBTMPB4/screenshot_2024-10-19_at_1.57.18___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07SNBTMPB4-7c3ea38201", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "o4esj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " In the past, we used to see the budget allocation for each planning managers's org as well for their direct reports i.e. their team.\n\nAllocation page currently is only showing the budget for their direct team only."}]}]}]}, {"ts": "1729365127.200369", "text": "<@U0690EB5JE5> can you pls see if we are seeing similar issues in Alaya Care env?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729365127.200369", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "h1kMf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you pls see if we are seeing similar issues in Alaya Care env?"}]}]}]}, {"ts": "1729364957.831089", "text": "<@U0690EB5JE5> another critical data discrepency issue. It is high priority as well\n<https://compiify.atlassian.net/browse/COM-3861>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729364957.831089", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13922::b869524fe6914632b621acbd264a74bf", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3861?atlOrigin=eyJpIjoiODNiY2EzNWI5MjQxNDcwMzk2M2EwNTNiZjNhN2MwZWEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3861 Inconsistency in Total Budget Used Display>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13922::6def04a282154a1b80647d6d11f0ce21", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13922\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3861\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3861", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "V4DV1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " another critical data discrepency issue. It is high priority as well\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3861"}]}]}]}, {"ts": "1729364335.573059", "text": "<@U0690EB5JE5> it looks like filtering by manager in org view is no longer working.\n<https://www.loom.com/share/0f1e69861f3844498f982af4432efca0>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729364335.573059", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "Hk0Xe", "video_url": "https://www.loom.com/embed/0f1e69861f3844498f982af4432efca0?unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/0f1e69861f3844498f982af4432efca0-5235394214e83f9d-4x3.jpg", "alt_text": "Merit View | Stride - 19 October 2024", "title": {"type": "plain_text", "text": "Merit View | Stride - 19 October 2024", "emoji": true}, "title_url": "https://www.loom.com/share/0f1e69861f3844498f982af4432efca0", "author_name": "<PERSON><PERSON><PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 1 min  ", "emoji": true}}, {"type": "actions", "block_id": "H19JO", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/0f1e69861f3844498f982af4432efca0"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"0f1e69861f3844498f982af4432efca0\",\"videoName\":\"Merit View | Stride - 19 October 2024\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/0f1e69861f3844498f982af4432efca0", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "7hCfM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " it looks like filtering by manager in org view is no longer working.\n"}, {"type": "link", "url": "https://www.loom.com/share/0f1e69861f3844498f982af4432efca0"}]}]}]}, {"ts": "1729363817.180029", "text": "<@U0690EB5JE5> total eligible emp count in my tasks is still showing 130 vs 132. I think you are still working in fixing it. It looks like the discrepency is with the CEO's direct reports. Per cycle builder, he is recommencing 18 emp and in my tasks it showing 16 emp.", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1729364303.000000"}, "blocks": [{"type": "rich_text", "block_id": "j6tiZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " total eligible emp count in my tasks is still showing 130 vs 132. I think you are still working in fixing it. It looks like the discrepency is with the CEO's direct reports. Per cycle builder, he is recommencing 18 emp and in my tasks it showing 16 emp."}]}]}]}, {"ts": "1729363490.991039", "text": "<@U0690EB5JE5> hiding one time bonus, and removing check marks made it so much better and less confusing. It's looking good :fire:", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/sJwL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " hiding one time bonus, and removing check marks made it so much better and less confusing. It's looking good "}, {"type": "emoji", "name": "fire", "unicode": "1f525"}]}]}]}], "created_at": "2025-05-22T21:35:34.666057"}