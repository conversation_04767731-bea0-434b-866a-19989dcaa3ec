#!/usr/bin/env python
"""
Embedding Model Consistency Validation Script

This script validates that the embedding model consistency fix is working correctly
and that all parts of the RAG system are using the same embedding model with
consistent dimensions.

Usage:
    python scripts/validate_embedding_consistency.py
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'multi_source_rag.settings')
django.setup()

import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_embedding_consistency() -> Dict[str, Any]:
    """
    Test embedding model consistency across all components.
    
    Returns:
        Dict with test results
    """
    results = {
        "overall_status": "UNKNOWN",
        "tests": {},
        "errors": [],
        "warnings": [],
        "model_info": {}
    }
    
    try:
        print("🔍 Testing Embedding Model Consistency...")
        print("=" * 60)
        
        # Test 1: Consistency Manager
        print("\n1️⃣ Testing Embedding Consistency Manager...")
        try:
            from apps.core.utils.embedding_consistency import (
                get_consistent_embedding_model, 
                get_embedding_dimensions,
                get_embedding_model_info,
                validate_embedding_consistency,
                set_global_embedding_model
            )
            
            # Get model info
            model_info = get_embedding_model_info()
            results["model_info"] = model_info
            
            print(f"   ✅ Model Type: {model_info.get('model_type', 'Unknown')}")
            print(f"   ✅ Model Name: {model_info.get('model_name', 'Unknown')}")
            print(f"   ✅ Dimensions: {model_info.get('dimensions', 'Unknown')}")
            print(f"   ✅ Gemini Preferred: {model_info.get('is_gemini_preferred', False)}")
            
            # Test model initialization
            model = get_consistent_embedding_model()
            dimensions = get_embedding_dimensions()
            
            print(f"   ✅ Model initialized: {type(model).__name__}")
            print(f"   ✅ Dimensions: {dimensions}")
            
            results["tests"]["consistency_manager"] = "PASS"
            
        except Exception as e:
            error_msg = f"Consistency manager test failed: {str(e)}"
            results["errors"].append(error_msg)
            results["tests"]["consistency_manager"] = "FAIL"
            print(f"   ❌ {error_msg}")
        
        # Test 2: Global Settings
        print("\n2️⃣ Testing Global LlamaIndex Settings...")
        try:
            from llama_index.core import Settings
            
            # Set global embedding model
            set_global_embedding_model()
            
            # Validate consistency
            is_consistent = validate_embedding_consistency()
            
            if is_consistent:
                print(f"   ✅ Global Settings.embed_model: {type(Settings.embed_model).__name__}")
                print(f"   ✅ Consistency validation: PASSED")
                results["tests"]["global_settings"] = "PASS"
            else:
                error_msg = "Global settings consistency validation failed"
                results["errors"].append(error_msg)
                results["tests"]["global_settings"] = "FAIL"
                print(f"   ❌ {error_msg}")
                
        except Exception as e:
            error_msg = f"Global settings test failed: {str(e)}"
            results["errors"].append(error_msg)
            results["tests"]["global_settings"] = "FAIL"
            print(f"   ❌ {error_msg}")
        
        # Test 3: RAG Service
        print("\n3️⃣ Testing RAG Service Embedding Model...")
        try:
            from apps.search.services.unified_rag_service import UnifiedRAGService
            
            # Initialize RAG service (this should set consistent embedding)
            rag_service = UnifiedRAGService(tenant_slug='stride')
            
            # Check if the service initialized properly
            if hasattr(rag_service, 'vector_store_index'):
                print(f"   ✅ RAG Service initialized successfully")
                print(f"   ✅ Vector store index available")
                results["tests"]["rag_service"] = "PASS"
            else:
                warning_msg = "RAG service initialized but no vector store index found"
                results["warnings"].append(warning_msg)
                results["tests"]["rag_service"] = "PARTIAL"
                print(f"   ⚠️  {warning_msg}")
                
        except Exception as e:
            error_msg = f"RAG service test failed: {str(e)}"
            results["errors"].append(error_msg)
            results["tests"]["rag_service"] = "FAIL"
            print(f"   ❌ {error_msg}")
        
        # Test 4: Ingestion Service
        print("\n4️⃣ Testing Ingestion Service Embedding Model...")
        try:
            from apps.documents.services.llama_ingestion_service_unified import UnifiedLlamaIndexIngestionService
            from apps.accounts.models import Tenant
            
            # Get tenant
            tenant = Tenant.objects.get(slug='stride')
            
            # Initialize ingestion service
            ingestion_service = UnifiedLlamaIndexIngestionService(tenant=tenant)
            
            print(f"   ✅ Ingestion Service initialized successfully")
            results["tests"]["ingestion_service"] = "PASS"
                
        except Exception as e:
            error_msg = f"Ingestion service test failed: {str(e)}"
            results["errors"].append(error_msg)
            results["tests"]["ingestion_service"] = "FAIL"
            print(f"   ❌ {error_msg}")
        
        # Test 5: Content Embedding Function
        print("\n5️⃣ Testing Content Embedding Function...")
        try:
            from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
            
            # Test different content types - should all return the same model
            models = {}
            for content_type in ['slack', 'github', 'confluence', None]:
                model = get_embedding_model_for_content(content_type=content_type)
                models[content_type or 'default'] = type(model).__name__
            
            # Check if all models are the same type
            unique_types = set(models.values())
            if len(unique_types) == 1:
                print(f"   ✅ All content types use same model: {list(unique_types)[0]}")
                print(f"   ✅ Model types: {models}")
                results["tests"]["content_embedding"] = "PASS"
            else:
                error_msg = f"Inconsistent models for different content types: {models}"
                results["errors"].append(error_msg)
                results["tests"]["content_embedding"] = "FAIL"
                print(f"   ❌ {error_msg}")
                
        except Exception as e:
            error_msg = f"Content embedding test failed: {str(e)}"
            results["errors"].append(error_msg)
            results["tests"]["content_embedding"] = "FAIL"
            print(f"   ❌ {error_msg}")
        
        # Test 6: Vector Dimensions Consistency
        print("\n6️⃣ Testing Vector Dimensions Consistency...")
        try:
            from apps.core.utils.embedding_consistency import get_embedding_dimensions
            from llama_index.core import Settings
            
            # Get dimensions from different sources
            consistent_dims = get_embedding_dimensions()
            
            # Test actual embedding
            if Settings.embed_model:
                test_embedding = Settings.embed_model.get_text_embedding("test")
                actual_dims = len(test_embedding)
                
                if consistent_dims == actual_dims:
                    print(f"   ✅ Dimension consistency: {consistent_dims} == {actual_dims}")
                    results["tests"]["vector_dimensions"] = "PASS"
                else:
                    error_msg = f"Dimension mismatch: expected {consistent_dims}, got {actual_dims}"
                    results["errors"].append(error_msg)
                    results["tests"]["vector_dimensions"] = "FAIL"
                    print(f"   ❌ {error_msg}")
            else:
                warning_msg = "No global embedding model set for dimension testing"
                results["warnings"].append(warning_msg)
                results["tests"]["vector_dimensions"] = "PARTIAL"
                print(f"   ⚠️  {warning_msg}")
                
        except Exception as e:
            error_msg = f"Vector dimensions test failed: {str(e)}"
            results["errors"].append(error_msg)
            results["tests"]["vector_dimensions"] = "FAIL"
            print(f"   ❌ {error_msg}")
        
        # Calculate overall status
        test_results = list(results["tests"].values())
        if all(result == "PASS" for result in test_results):
            results["overall_status"] = "PASS"
        elif any(result == "FAIL" for result in test_results):
            results["overall_status"] = "FAIL"
        else:
            results["overall_status"] = "PARTIAL"
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 EMBEDDING CONSISTENCY TEST SUMMARY")
        print("=" * 60)
        
        for test_name, status in results["tests"].items():
            status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
            print(f"{status_icon} {test_name.replace('_', ' ').title()}: {status}")
        
        print(f"\n🎯 Overall Status: {results['overall_status']}")
        
        if results["errors"]:
            print(f"\n❌ Errors ({len(results['errors'])}):")
            for error in results["errors"]:
                print(f"   • {error}")
        
        if results["warnings"]:
            print(f"\n⚠️  Warnings ({len(results['warnings'])}):")
            for warning in results["warnings"]:
                print(f"   • {warning}")
        
        if results["overall_status"] == "PASS":
            print(f"\n🎉 EMBEDDING CONSISTENCY FIX SUCCESSFUL!")
            print(f"   All components are using consistent embedding model:")
            print(f"   Model: {results['model_info'].get('model_name', 'Unknown')}")
            print(f"   Type: {results['model_info'].get('model_type', 'Unknown')}")
            print(f"   Dimensions: {results['model_info'].get('dimensions', 'Unknown')}")
        else:
            print(f"\n🚨 EMBEDDING CONSISTENCY ISSUES DETECTED!")
            print(f"   Please review the errors above and fix the issues.")
        
        return results
        
    except Exception as e:
        results["overall_status"] = "ERROR"
        results["errors"].append(f"Test execution failed: {str(e)}")
        print(f"\n💥 Test execution failed: {str(e)}")
        return results


if __name__ == "__main__":
    test_results = test_embedding_consistency()
    
    # Exit with appropriate code
    if test_results["overall_status"] == "PASS":
        sys.exit(0)
    elif test_results["overall_status"] == "PARTIAL":
        sys.exit(1)
    else:
        sys.exit(2)
