{"thread_ts": "**********.990729", "channel_id": "C065QSSNH8A", "reply_count": 7, "replies": [{"ts": "1741140633.058909", "text": "<@U07M6QKHUC9> Will take care now. Please note for curana we two ENVs we need to check with them which one of them needs those changes.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.990729", "blocks": [{"type": "rich_text", "block_id": "9zDM1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Will take care now. Please note for curana we two ENVs we need to check with them which one of them needs those changes."}]}]}]}, {"ts": "**********.946929", "text": "for now I will apply those changes to Curana main account.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.990729", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "oHUpQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "for now I will apply those changes to Curana main account."}]}]}]}, {"ts": "**********.543519", "text": "<@U07M6QKHUC9> done.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.990729", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Jz7bU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " done."}]}]}]}, {"ts": "**********.531039", "text": "<@U0690EB5JE5> can you please confirm <PERSON> was marked in eligible for both cycles", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.990729", "blocks": [{"type": "rich_text", "block_id": "VOXC+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you please confirm <PERSON> was marked in eligible for both cycles"}]}]}]}, {"ts": "**********.167849", "text": "Also, they did state that <PERSON> should be added only for the merit cycle", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.990729", "blocks": [{"type": "rich_text", "block_id": "nZgyq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also, they did state that <PERSON> should be added only for the merit cycle"}]}]}]}, {"ts": "**********.468479", "text": "As I mentioned above. I added only to main account, merit cycle.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.990729", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "WNNyc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "As I mentioned above. I added only to main account, merit cycle."}]}]}]}, {"ts": "**********.337259", "text": "we are good here then.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.990729", "blocks": [{"type": "rich_text", "block_id": "Udb1U", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we are good here then."}]}]}]}], "created_at": "2025-05-22T22:00:49.368741"}