{"date": "2024-07-15", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1721064014.889669", "text": "Please confirm", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "K5TAD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please confirm"}]}]}]}, {"ts": "1721064003.023959", "text": "<@U04DS2MBWP4> Looks like PlayQ login issue is resolved", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721064003.023959", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "LoLOC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Looks like PlayQ login issue is resolved"}]}]}]}, {"ts": "1721056102.519369", "text": "Sounds like a plan. Thanks for the update.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0l4GW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sounds like a plan. Thanks for the update."}]}]}]}, {"ts": "1721039544.906619", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8>\n*Update on `In QA`* *<https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|tickets> .* \nThere were around 150 tickets. I targeted tickets from below categories and brought down the tickets to 44.\n\n• *Old feature tickets* - _These were mostly fresh implementations like meritview 2.0 and tested already by <PERSON> and we have bugs reported from those features. So closed._\n• *UI Cosmetics* - _verified and closed. Added screenshots in the tickets._\n• *HR Admin* - _closed as not valid anymore_\n• *Old Merit view* - _closed as not valid anymore_\nThe remaining tickets are around calculations and functionalities and would need couple of days to verify thoroughly.  I will be targeting to close them by Wednesday or Thursday. Some of them are equity related and will assign them to <@U04DKEFP1K8> as those will be covered with Nauto QA. We can discuss more on this in eng discussion if any further questions.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1721040319.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Zh+q6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Update on ", "style": {"bold": true}}, {"type": "text", "text": "In QA", "style": {"bold": true, "code": true}}, {"type": "text", "text": " ", "style": {"bold": true}}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "tickets", "style": {"bold": true}}, {"type": "text", "text": " . ", "style": {"bold": true}}, {"type": "text", "text": "\nThere were around 150 tickets. I targeted tickets from below categories and brought down the tickets to 44.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Old feature tickets", "style": {"bold": true}}, {"type": "text", "text": " - "}, {"type": "text", "text": "These were mostly fresh implementations like meritview 2.0 and tested already by <PERSON> and we have bugs reported from those features. So closed.", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "UI Cosmetics", "style": {"bold": true}}, {"type": "text", "text": " - "}, {"type": "text", "text": "verified and closed. Added screenshots in the tickets.", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "HR Admin", "style": {"bold": true}}, {"type": "text", "text": " - "}, {"type": "text", "text": "closed as not valid anymore", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Old Merit view", "style": {"bold": true}}, {"type": "text", "text": " -"}, {"type": "text", "text": " closed as not valid anymore", "style": {"italic": true}}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nThe remaining tickets are around calculations and functionalities and would need couple of days to verify thoroughly.  I will be targeting to close them by Wednesday or Thursday. Some of them are equity related and will assign them to "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " as those will be covered with Nauto QA. We can discuss more on this in eng discussion if any further questions."}]}]}]}], "created_at": "2025-05-22T21:35:34.619718"}