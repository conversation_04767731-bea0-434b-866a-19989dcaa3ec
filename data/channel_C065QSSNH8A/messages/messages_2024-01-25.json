{"date": "2024-01-25", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1706144171.946279", "text": "Priorities for Eng for the next day:\n• :repeat: SDF: Allow inputting new titles for promotions (<https://compiify.atlassian.net/browse/COM-2198|COM-2198>)\n• SDF: Export values for new XLM columns (<https://compiify.atlassian.net/browse/COM-2214|COM-2214>)\n• DA: Roll up Eric, <PERSON><PERSON><PERSON>, <PERSON><PERSON> under Emnet; Exclude <PERSON>'s and <PERSON>'s entire teams from the cycle (<https://compiify.atlassian.net/browse/COM-2221|COM-2221>)\n    ◦ _(I don't know if any Eng work is needed or just config changes from <PERSON><PERSON><PERSON><PERSON>, so putting it on the list just in case)_\n• Filter improvements (<https://compiify.atlassian.net/browse/COM-2139|COM-2139>, <https://compiify.atlassian.net/browse/COM-2220|COM-2220>)\n• Login email case sensitivity (<https://compiify.atlassian.net/browse/COM-2166|COM-2166>)\nAfter these we should have completed Wave 2 and can continue with <https://compiify.atlassian.net/browse/COM-2087|Wave 3>, starting from the top of the list.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706144171.946279", "reply_count": 1, "edited": {"user": "U065H3M6WJV", "ts": "1706146443.000000"}, "blocks": [{"type": "rich_text", "block_id": "LnHMy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " SDF: Allow inputting new titles for promotions ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2198", "text": "COM-2198"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF: Export values for new XLM columns ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2214", "text": "COM-2214"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "DA: <PERSON> up <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> under Emnet; Exclude <PERSON>'s and <PERSON>'s entire teams from the cycle ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2221", "text": "COM-2221"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "(I don't know if any Eng work is needed or just config changes from <PERSON><PERSON><PERSON><PERSON>, so putting it on the list just in case)", "style": {"italic": true}}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Filter improvements ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2139", "text": "COM-2139"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2220", "text": "COM-2220"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Login email case sensitivity ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2166", "text": "COM-2166"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAfter these we should have completed Wave 2 and can continue with "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": ", starting from the top of the list."}]}]}]}, {"ts": "**********.781229", "text": "<@U04DKEFP1K8> pls move this conversation to revenue leadership as we<PERSON> and chris are not in this channel", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.781229", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "X8osM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " pls move this conversation to revenue leadership as we<PERSON> and chris are not in this channel"}]}]}]}, {"ts": "**********.122089", "text": "<@U068MM2H2QG> <@U04DS2MBWP4> We had 3 critical issue related to email health yesterday. 1/3 has been resolved. Per the devops engineer other 2 are not critical as long as we are able to send email from the domain. Please retry on your end again sending emails. Also can one of you send a test email to <mailto:<EMAIL>|<EMAIL>>\n\nThese are remaining list of error ( which are non critical per devops engineer)", "user": "U04DKEFP1K8", "type": "message", "files": [{"id": "F06G4BYHP4Y", "created": **********, "timestamp": **********, "name": "Screenshot 2024-01-24 at 11.30.31 AM.png", "title": "Screenshot 2024-01-24 at 11.30.31 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 148782, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06G4BYHP4Y/screenshot_2024-01-24_at_11.30.31___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06G4BYHP4Y/download/screenshot_2024-01-24_at_11.30.31___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_360.png", "thumb_360_w": 360, "thumb_360_h": 57, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_480.png", "thumb_480_w": 480, "thumb_480_h": 76, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_720.png", "thumb_720_w": 720, "thumb_720_h": 114, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_800.png", "thumb_800_w": 800, "thumb_800_h": 126, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_960.png", "thumb_960_w": 960, "thumb_960_h": 151, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 161, "original_w": 3400, "original_h": 536, "thumb_tiny": "AwAHADDSwaMH1paKAEOcUc0tFACc0UtFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06G4BYHP4Y/screenshot_2024-01-24_at_11.30.31___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06G4BYHP4Y-09299e2168", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "wQkeg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U068MM2H2QG"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " We had 3 critical issue related to email health yesterday. 1/3 has been resolved. Per the devops engineer other 2 are not critical as long as we are able to send email from the domain. Please retry on your end again sending emails. Also can one of you send a test email to "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": "\n\nThese are remaining list of error ( which are non critical per devops engineer)"}]}]}]}], "created_at": "2025-05-22T21:35:34.588742"}