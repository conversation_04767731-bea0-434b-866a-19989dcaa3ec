{"date": "2023-12-16", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1702736906.071909", "text": "<!here> Here is parent jira which tracks all the items listed in <PERSON>'s UAT spreadsheet <https://compiify.atlassian.net/browse/COM-1905>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:11966::607455209c1f11ee9c6e455153c1a95a", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-1905?atlOrigin=eyJpIjoiMDU2Y2JjN2JiNWFiNDZjZDg0ODFiYWY4NTM4YWZhZmYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-1905 Dec 2023 Issues from Rachel's UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:11966::607455229c1f11ee9c6e455153c1a95a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:11966::607455219c1f11ee9c6e455153c1a95a", "elements": [{"type": "button", "action_id": "{\"issueId\":\"11966\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"11966\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-1905", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "HzDs9", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Here is parent jira which tracks all the items listed in <PERSON>'s UAT spreadsheet "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1905"}]}]}]}, {"ts": "1702736865.491889", "text": "<!here> is parent jira <https://compiify.atlassian.net/browse/COM-1893> to track Merit View enhancement listed in the google slides <https://docs.google.com/presentation/d/1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0/edit#slide=id.g263cddf32b6_0_137>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:11954::4857fff09c1f11ee8489dbb8a9985de5", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-1893?atlOrigin=eyJpIjoiNmM1YWRmOWFjY2I2NGNjMThjZTQ3MDJjNDI5OGZlYWEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-1893 Merit View Enhancements - Direct Reports Tab ( Dec 2023)>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:11954::485827009c1f11ee8489dbb8a9985de5", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:11954::4857fff19c1f11ee8489dbb8a9985de5", "elements": [{"type": "button", "action_id": "{\"issueId\":\"11954\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"11954\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-1893", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "nX93K", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " is parent jira "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1893"}, {"type": "text", "text": " to track Merit View enhancement listed in the google slides "}, {"type": "link", "url": "https://docs.google.com/presentation/d/1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0/edit#slide=id.g263cddf32b6_0_137"}]}]}]}, {"ts": "1702736780.259119", "text": "<!here>\n\nHere is the parent jira to track issues for Digital Asset implementation\n\n<https://compiify.atlassian.net/browse/COM-1859>", "user": "U04DKEFP1K8", "type": "message", "edited": {"user": "U04DKEFP1K8", "ts": "1702737903.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:11920::15684be09c1f11ee97825747be94a3ef", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-1859?atlOrigin=eyJpIjoiMmIzNDExMzc5Mjk1NDA4NmI5N2MyNDYxZGQzM2RhMzgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-1859 Digital Asset UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:11920::15684be29c1f11ee97825747be94a3ef", "elements": [{"type": "mrkdwn", "text": "Status: *In Progress*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:11920::15684be19c1f11ee97825747be94a3ef", "elements": [{"type": "button", "action_id": "{\"issueId\":\"11920\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"11920\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-1859", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "3WI0Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": "\n\nHere is the parent jira to track issues for Digital Asset implementation\n\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1859"}]}]}]}, {"ts": "1702669205.152899", "text": "Here's today's \"usability\" session for our CHRO admin:\n\n<https://us06web.zoom.us/rec/share/ubGGykcvLStxsJqEoE6nTuZWnWLxRRl0ByrjbNwtu0UJS-WxhEcBOEqcRO02wPug.cfJ3u2pmOQcdUpue>\nPasscode: *i#y^87Hu*", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4", "U04DKEFP1K8"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "svVuM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here's today's \"usability\" session for our CHRO admin:\n\n"}, {"type": "link", "url": "https://us06web.zoom.us/rec/share/ubGGykcvLStxsJqEoE6nTuZWnWLxRRl0ByrjbNwtu0UJS-WxhEcBOEqcRO02wPug.cfJ3u2pmOQcdUpue"}, {"type": "text", "text": "\nPasscode:"}, {"type": "text", "text": " i#y^87Hu", "style": {"bold": true}}]}]}]}], "created_at": "2025-05-22T21:35:34.564052"}