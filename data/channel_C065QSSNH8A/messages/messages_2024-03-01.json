{"date": "2024-03-01", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1709271898.533129", "text": "<@U04DKEFP1K8> Updated the <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities> list, with the DA blockers as the highest priority.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709271898.533129", "reply_count": 2, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "+pNck", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Updated the "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities"}, {"type": "text", "text": " list, with the DA blockers as the highest priority."}]}]}]}, {"ts": "1709250512.773069", "text": "<!here> <http://stridenow.ai|stridenow.ai>.       <http://stridenow.io|stridenow.io>.      <http://stridehere.com|stridehere.com>\n\nwhich one do we want to pick? do you have any strong preference or strong NO GO? If we are confident about using ai then we should do <http://stridenow.ai|stridenow.ai>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "thinking_face", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "+zMIY", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://stridenow.ai", "text": "stridenow.ai"}, {"type": "text", "text": ".       "}, {"type": "link", "url": "http://stridenow.io", "text": "stridenow.io"}, {"type": "text", "text": ".      "}, {"type": "link", "url": "http://stridehere.com", "text": "stridehere.com"}, {"type": "text", "text": "\n\nwhich one do we want to pick? do you have any strong preference or strong NO GO? If we are confident about using ai then we should do "}, {"type": "link", "url": "http://stridenow.ai", "text": "stridenow.ai"}]}]}]}, {"ts": "1709250260.053769", "text": "<@U04DKEFP1K8> can you send over the two stride domains we purchased through aws?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1709250260.053769", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "Mj7w6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you send over the two stride domains we purchased through aws?"}]}]}]}, {"ts": "1709240451.521939", "text": "<@U04DKEFP1K8> For <PERSON>'s issues today - do we have an internal \"audit trail\" view (or DB entries) that we can access to figure out what happened with <PERSON>?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709240451.521939", "reply_count": 41, "blocks": [{"type": "rich_text", "block_id": "bVWX2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For <PERSON>'s issues today - do we have an internal \"audit trail\" view (or DB entries) that we can access to figure out what happened with <PERSON>?"}]}]}]}, {"ts": "1709237074.259649", "text": "Quick update on DA: We're going to reset their environment and have <PERSON> start with a fresh cycle.\n\nSome of the issues he encountered so far:\n• Proration didn't match what they'd set up in HiBob - most likely, they miscommunicated their expectation because they aren't using proration at all. \n• The hierarchy made merit planning harder than it needed to be - we're going to roll up teams under leaders, more similar to SDF\n• Currency conversions were not completely matching between Compiify and HiBob -- we can solve this with a higher precision conversion rate\n• <PERSON> hit a persistent bug where employees could not be \"Reviewed\" or \"Approved\", either in impersonation or in Super Admin mode, and we couldn't resolve this without a code push\nWe're aiming to have this reset for him by tomorrow morning. :cat-roomba:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709237074.259649", "reply_count": 8, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "DCeVf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Quick update on DA: We're going to reset their environment and have <PERSON> start with a fresh cycle.\n\nSome of the issues he encountered so far:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Proration didn't match what they'd set up in HiBob - most likely, they miscommunicated their expectation because they aren't using proration at all. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The hierarchy made merit planning harder than it needed to be - we're going to roll up teams under leaders, more similar to SDF"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Currency conversions were not completely matching between Compiify and HiBob -- we can solve this with a higher precision conversion rate"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> hit a persistent bug where employees could not be \"Reviewed\" or \"Approved\", either in impersonation or in Super Admin mode, and we couldn't resolve this without a code push"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nWe're aiming to have this reset for him by tomorrow morning. "}, {"type": "emoji", "name": "cat-roomba"}]}]}]}], "created_at": "2025-05-22T21:35:34.614012"}