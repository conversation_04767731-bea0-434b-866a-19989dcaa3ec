{"thread_ts": "1741287658.545099", "channel_id": "C065QSSNH8A", "reply_count": 3, "replies": [{"ts": "1741287987.314609", "text": "<@U07EJ2LP44S> when I am impersonating as <PERSON>, I am able to make changes to the salary for <PERSON>, so not sure why <PERSON> is not able to make changes for <PERSON>.\n\n<@U0690EB5JE5> FYI", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741287658.545099", "blocks": [{"type": "rich_text", "block_id": "i3XLp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " when I am impersonating as <PERSON>, I am able to make changes to the salary for <PERSON>, so not sure why <PERSON> is not able to make changes for <PERSON>.\n\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " FYI"}]}]}]}, {"ts": "1741292073.859059", "text": "You cannot make the change if you are viewing the team she accepted. She might be able to make the change if she views the entire organization, but it is not working the same for both situations. EG if you click on 'MICHELLE LAUDICK's team' you cannot make the change. If you look at 'my organization' you can.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741287658.545099", "blocks": [{"type": "rich_text", "block_id": "kHPYl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "You cannot make the change if you are viewing the team she accepted. She might be able to make the change if she views the entire organization, but it is not working the same for both situations. EG if you click on 'MICHELLE LAUDICK's team' you cannot make the change. If you look at 'my organization' you can."}]}]}]}, {"ts": "1741292388.498419", "text": "Ah okk", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741287658.545099", "blocks": [{"type": "rich_text", "block_id": "uzjWT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ah okk"}]}]}]}], "created_at": "2025-05-22T22:00:49.366859"}