{"date": "2024-02-21", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1708478498.880109", "text": "<@U04DKEFP1K8> Eng priorities for next day are <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|on the board> including the couple of cleanup tickets for password reset. And can you have a look at the items in <https://compiify.atlassian.net/browse/COM-2087|Wave 3> that have been \"in development\" for some time now?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fvuzG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "on the board"}, {"type": "text", "text": " including the couple of cleanup tickets for password reset. And can you have a look at the items in "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " that have been \"in development\" for some time now?"}]}]}]}, {"ts": "1708466878.483039", "text": "Here's a fun issue :laughing:\n\nSince I have support@compiify set up as an \"alias\", it uses my Gmail profile picture when something comes \"from\" that address. Which is weird if you get an email from support for a password reset, for example:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708466878.483039", "reply_count": 7, "files": [{"id": "F06LG1E0ZKJ", "created": 1708466746, "timestamp": 1708466746, "name": "Screenshot 2024-02-20 at 2.04.57 PM.png", "title": "Screenshot 2024-02-20 at 2.04.57 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 25018, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06LG1E0ZKJ/screenshot_2024-02-20_at_2.04.57___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06LG1E0ZKJ/download/screenshot_2024-02-20_at_2.04.57___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LG1E0ZKJ-eec1038bec/screenshot_2024-02-20_at_2.04.57___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LG1E0ZKJ-eec1038bec/screenshot_2024-02-20_at_2.04.57___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LG1E0ZKJ-eec1038bec/screenshot_2024-02-20_at_2.04.57___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 194, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LG1E0ZKJ-eec1038bec/screenshot_2024-02-20_at_2.04.57___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 259, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LG1E0ZKJ-eec1038bec/screenshot_2024-02-20_at_2.04.57___pm_160.png", "original_w": 505, "original_h": 272, "thumb_tiny": "AwAZADDRLLkrnnFNyv8AfNP2jOcDNHPoPzoAbwejml2/7TUuW9B+dLQAm3/aNKKKKACk5zS0UAJzQM96WigAooooA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06LG1E0ZKJ/screenshot_2024-02-20_at_2.04.57___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06LG1E0ZKJ-7adfd39053", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "AORXh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here's a fun issue "}, {"type": "emoji", "name": "laughing", "unicode": "1f606"}, {"type": "text", "text": "\n\nSince I have support@compiify set up as an \"alias\", it uses my Gmail profile picture when something comes \"from\" that address. Which is weird if you get an email from support for a password reset, for example:"}]}]}]}, {"ts": "1708455410.571989", "text": "<@U04DKEFP1K8> - question from <PERSON>; I think the answer is 'Yes', <PERSON><PERSON> can see any of the \"Reviewed\" data that <PERSON> inputs, correct?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708455410.571989", "reply_count": 9, "files": [{"id": "F06L4NCF9G9", "created": 1708455408, "timestamp": 1708455408, "name": "Screenshot 2024-02-20 at 10.56.07 AM.png", "title": "Screenshot 2024-02-20 at 10.56.07 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 11970, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06L4NCF9G9/screenshot_2024-02-20_at_10.56.07___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06L4NCF9G9/download/screenshot_2024-02-20_at_10.56.07___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06L4NCF9G9-a02b016f30/screenshot_2024-02-20_at_10.56.07___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06L4NCF9G9-a02b016f30/screenshot_2024-02-20_at_10.56.07___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06L4NCF9G9-a02b016f30/screenshot_2024-02-20_at_10.56.07___am_360.png", "thumb_360_w": 360, "thumb_360_h": 111, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06L4NCF9G9-a02b016f30/screenshot_2024-02-20_at_10.56.07___am_480.png", "thumb_480_w": 480, "thumb_480_h": 147, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06L4NCF9G9-a02b016f30/screenshot_2024-02-20_at_10.56.07___am_160.png", "original_w": 518, "original_h": 159, "thumb_tiny": "AwAOADDQyN3JP507KjuPzoAwSaWgBNy+oo3D1FLRQAm4eopcg9xRRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06L4NCF9G9/screenshot_2024-02-20_at_10.56.07___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06L4NCF9G9-ee01b005dc", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Kop2q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " - question from <PERSON>; I think the answer is 'Yes', <PERSON><PERSON> can see any of the \"Reviewed\" data that <PERSON> inputs, correct?"}]}]}]}], "created_at": "2025-05-22T21:35:34.581111"}