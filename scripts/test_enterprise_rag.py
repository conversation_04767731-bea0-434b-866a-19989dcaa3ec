#!/usr/bin/env python
"""
Test script for the Enterprise RAG Service.

This script tests the Enterprise RAG Service with various queries and configurations.
"""

import os
import sys
import django
import logging
import time
from typing import Dict, Any, List, Optional

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "multi_source_rag.config.settings.local")
django.setup()

# Import Django models and services
from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.search.services.enterprise_rag_service import EnterpriseRAGService
from apps.search.engines.multi_modal_engine import MultiModalQueryEngine
from apps.search.retrievers.enterprise_retrievers import EnterpriseRetrieverEngine

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def test_enterprise_rag_service():
    """Test the Enterprise RAG Service."""
    logger.info("Testing Enterprise RAG Service...")
    
    # Get a user and tenant
    try:
        user = User.objects.first()
        tenant = Tenant.objects.first()
        
        if not user or not tenant:
            logger.error("No user or tenant found. Please create a user and tenant first.")
            return
        
        logger.info(f"Using user: {user.username} and tenant: {tenant.name}")
        
        # Create Enterprise RAG Service
        rag_service = EnterpriseRAGService(user=user, tenant_slug=tenant.slug)
        
        # Test queries
        test_queries = [
            "What are the key features of our product?",
            "How do I set up a new project?",
            "What is the latest update on the roadmap?",
            "Who is responsible for the frontend development?",
            "What are the best practices for code reviews?",
        ]
        
        for query in test_queries:
            logger.info(f"Testing query: {query}")
            
            # Start timer
            start_time = time.time()
            
            # Execute search
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=5,
                use_hybrid_search=True,
                use_context_aware=True,
                output_format="text",
            )
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Log results
            logger.info(f"Query: {query}")
            logger.info(f"Answer: {search_result.generated_answer[:100]}...")
            logger.info(f"Processing time: {processing_time:.2f}s")
            logger.info(f"Retrieved {len(retrieved_docs)} documents")
            logger.info(f"Average retriever score: {search_result.retriever_score_avg:.4f}")
            logger.info("-" * 80)
            
    except Exception as e:
        logger.error(f"Error testing Enterprise RAG Service: {str(e)}", exc_info=True)


def test_multi_modal_engine():
    """Test the Multi-Modal Query Engine."""
    logger.info("Testing Multi-Modal Query Engine...")
    
    # Get a tenant
    try:
        tenant = Tenant.objects.first()
        
        if not tenant:
            logger.error("No tenant found. Please create a tenant first.")
            return
        
        logger.info(f"Using tenant: {tenant.name}")
        
        # Create Multi-Modal Query Engine
        engine = MultiModalQueryEngine(tenant_slug=tenant.slug)
        
        # Test queries
        test_queries = [
            "What are the key features of our product?",
            "How do I set up a new project?",
            "What is the latest update on the roadmap?",
        ]
        
        for query in test_queries:
            logger.info(f"Testing query: {query}")
            
            # Start timer
            start_time = time.time()
            
            # Execute query
            try:
                # Use async query with await
                import asyncio
                response = asyncio.run(engine.query(
                    query_text=query,
                    use_sub_questions=True,
                    max_sub_questions=3,
                ))
                
                # Calculate processing time
                processing_time = time.time() - start_time
                
                # Log results
                logger.info(f"Query: {query}")
                logger.info(f"Response: {response.response[:100]}...")
                logger.info(f"Processing time: {processing_time:.2f}s")
                logger.info("-" * 80)
            except Exception as e:
                logger.error(f"Error executing query: {str(e)}", exc_info=True)
            
    except Exception as e:
        logger.error(f"Error testing Multi-Modal Query Engine: {str(e)}", exc_info=True)


def test_enterprise_retriever_engine():
    """Test the Enterprise Retriever Engine."""
    logger.info("Testing Enterprise Retriever Engine...")
    
    # Get a tenant
    try:
        tenant = Tenant.objects.first()
        
        if not tenant:
            logger.error("No tenant found. Please create a tenant first.")
            return
        
        logger.info(f"Using tenant: {tenant.name}")
        
        # Create Enterprise Retriever Engine
        engine = EnterpriseRetrieverEngine(tenant_slug=tenant.slug)
        
        # Test queries
        test_queries = [
            "What are the key features of our product?",
            "How do I set up a new project?",
            "What is the latest update on the roadmap?",
        ]
        
        for query in test_queries:
            logger.info(f"Testing query: {query}")
            
            # Start timer
            start_time = time.time()
            
            # Execute retrieval
            nodes = engine.retrieve(
                query_text=query,
                top_k=5,
                similarity_cutoff=0.7,
                use_hybrid=True,
            )
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Log results
            logger.info(f"Query: {query}")
            logger.info(f"Retrieved {len(nodes)} nodes")
            logger.info(f"Processing time: {processing_time:.2f}s")
            
            # Log node details
            for i, node in enumerate(nodes[:3]):  # Show top 3 nodes
                logger.info(f"Node {i+1}: {node.node.text[:100]}... (Score: {node.score:.4f})")
            
            logger.info("-" * 80)
            
    except Exception as e:
        logger.error(f"Error testing Enterprise Retriever Engine: {str(e)}", exc_info=True)


if __name__ == "__main__":
    logger.info("Starting Enterprise RAG tests...")
    
    # Test Enterprise RAG Service
    test_enterprise_rag_service()
    
    # Test Multi-Modal Query Engine
    test_multi_modal_engine()
    
    # Test Enterprise Retriever Engine
    test_enterprise_retriever_engine()
    
    logger.info("Enterprise RAG tests completed.")
