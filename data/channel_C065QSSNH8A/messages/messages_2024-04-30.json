{"date": "2024-04-30", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1714418406.258369", "text": "While testing reports &amp; employee edits, I found that the data set on <http://test.compiify.com|test.compiify.com> still had <PERSON><PERSON>'s name for the HR Business Partner and the entity name \"SDF\" for all employees.\n• The good news is I'm able to update those with Mu<PERSON><PERSON>'s bulk upload flow!\n• But, let's please make sure the data set we use when resetting the environment has these SDF-specific items removed. The HRBP should be \"Quan Nguyen\" and entity should be \"Payright\"\n<@U0690EB5JE5> <@U04DKEFP1K8>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1714418406.258369", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "vcRru", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "While testing reports & employee edits, I found that the data set on "}, {"type": "link", "url": "http://test.compiify.com", "text": "test.compiify.com"}, {"type": "text", "text": " still had Katya's name for the HR Business Partner and the entity name \"SDF\" for all employees.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The good news is I'm able to update those with <PERSON><PERSON><PERSON>'s bulk upload flow!"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "But, let's please make sure the data set we use when resetting the environment has these SDF-specific items removed. The HRBP should be \"<PERSON>uan <PERSON>\" and entity should be \"Payright\""}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}], "created_at": "2025-05-22T21:35:34.601903"}