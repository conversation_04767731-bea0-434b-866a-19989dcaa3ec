{"thread_ts": "1734015167.151239", "channel_id": "C065QSSNH8A", "reply_count": 4, "replies": [{"ts": "1734016459.603669", "text": "<@U0690EB5JE5> will it be too much of a work if we only do it in Merit and org view, and do not touch people insights?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734015167.151239", "blocks": [{"type": "rich_text", "block_id": "4IrZ2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " will it be too much of a work if we only do it in Merit and org view, and do not touch people insights?"}]}]}]}, {"ts": "1734018956.665419", "text": "Yes we can just limit it to org view and merit view. <@U07M6QKHUC9> then why we need to enable recommendations by compa ratio when they don’t use it? Cc: <@U07MH77PUBV> ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734015167.151239", "edited": {"user": "U0690EB5JE5", "ts": "1734018975.000000"}, "blocks": [{"type": "rich_text", "block_id": "nRxFv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes we can just limit it to org view and merit view. "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " then why we need to enable recommendations by compa ratio when "}, {"type": "text", "text": "they "}, {"type": "text", "text": "don’t use it? Cc: "}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1734019206.908189", "text": "Initially, they said compa ratio in the last call. But looks like they actually want the range penetration.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734015167.151239", "blocks": [{"type": "rich_text", "block_id": "FFx/d", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Initially, they said compa ratio in the last call. But looks like they actually want the range penetration."}]}]}]}, {"ts": "1734019675.303089", "text": "<@U07MH77PUBV> do not work on the ticket I assigned today related to enabling recommendations by performance and compa ratio ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734015167.151239", "reactions": [{"name": "+1", "users": ["U07MH77PUBV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "qkJQO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " do not work on the ticket I assigned today related to enabling recommendations by performance and compa ratio "}]}]}]}], "created_at": "2025-05-22T22:00:49.315795"}