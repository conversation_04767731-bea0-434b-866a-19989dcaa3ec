#!/usr/bin/env python
"""
Final comprehensive production readiness test for the RAG Search system.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from apps.search.services.rag_service import RAGService
from apps.search.models import SearchResult
from django.contrib.auth.models import User
import time

def final_production_test():
    """Run final comprehensive production test."""
    print("🚀 FINAL PRODUCTION READINESS TEST")
    print("=" * 80)
    print("Testing the complete RAG Search system for production deployment")
    print("=" * 80)
    
    # Get the user
    user = User.objects.get(email="<EMAIL>")
    print(f"✅ User authenticated: {user.email}")
    
    # Test different query types that represent real-world usage
    test_scenarios = [
        {
            "name": "Issue Tracking",
            "query": "List all issues reported by <PERSON>",
            "expected_citations": ">= 5",
            "expected_content": ["amanda", "issue", "report"]
        },
        {
            "name": "Technical Troubleshooting", 
            "query": "What problems did <PERSON> mention about the system?",
            "expected_citations": ">= 5",
            "expected_content": ["rachel", "problem", "system"]
        },
        {
            "name": "Project Status",
            "query": "What's the latest on Curana project?",
            "expected_citations": ">= 3",
            "expected_content": ["curana", "project"]
        },
        {
            "name": "General Discussion",
            "query": "What is discussed in the engineering channel?",
            "expected_citations": ">= 5",
            "expected_content": ["engineering", "discuss"]
        },
        {
            "name": "Bug Reports",
            "query": "Show me all bug reports from the team",
            "expected_citations": ">= 5",
            "expected_content": ["bug", "report"]
        }
    ]
    
    # Initialize RAG service
    print(f"\n🔧 Initializing RAG Service...")
    start_time = time.time()
    rag_service = RAGService(user=user, tenant_slug="stride")
    init_time = time.time() - start_time
    print(f"✅ RAG Service initialized in {init_time:.2f}s")
    
    # Track results
    total_tests = len(test_scenarios)
    passed_tests = 0
    total_citations = 0
    total_search_time = 0
    
    print(f"\n🧪 Running {total_tests} Production Test Scenarios")
    print("=" * 60)
    
    for i, scenario in enumerate(test_scenarios):
        print(f"\n📋 Scenario {i+1}: {scenario['name']}")
        print(f"🔍 Query: '{scenario['query']}'")
        
        try:
            # Perform search
            search_start = time.time()
            result, docs = rag_service.search(scenario['query'], top_k=15)
            search_time = time.time() - search_start
            total_search_time += search_time
            
            # Check citations
            citations = result.citations.all()
            citation_count = citations.count()
            total_citations += citation_count
            
            # Validate results
            print(f"   ⏱️  Search time: {search_time:.2f}s")
            print(f"   📊 Citations: {citation_count}")
            print(f"   📄 Answer length: {len(result.generated_answer)} chars")
            print(f"   🏢 Tenant: {result.search_query.tenant.slug}")
            
            # Check citation quality
            if citation_count >= 5:
                print(f"   ✅ Citations: EXCELLENT ({citation_count} citations)")
            elif citation_count >= 3:
                print(f"   ✅ Citations: GOOD ({citation_count} citations)")
            elif citation_count >= 1:
                print(f"   ⚠️  Citations: MINIMAL ({citation_count} citations)")
            else:
                print(f"   ❌ Citations: FAILED (0 citations)")
                continue
            
            # Check content relevance
            answer_lower = result.generated_answer.lower()
            content_matches = sum(1 for term in scenario['expected_content'] 
                                if term.lower() in answer_lower)
            
            if content_matches >= len(scenario['expected_content']) * 0.7:
                print(f"   ✅ Content relevance: HIGH ({content_matches}/{len(scenario['expected_content'])} terms)")
            elif content_matches >= 1:
                print(f"   ⚠️  Content relevance: MEDIUM ({content_matches}/{len(scenario['expected_content'])} terms)")
            else:
                print(f"   ❌ Content relevance: LOW ({content_matches}/{len(scenario['expected_content'])} terms)")
                continue
            
            # Check citation permalinks
            citations_with_links = sum(1 for c in citations 
                                     if c.document_chunk.document and c.document_chunk.document.permalink)
            
            if citations_with_links == citation_count:
                print(f"   ✅ Permalinks: ALL CITATIONS ({citations_with_links}/{citation_count})")
            elif citations_with_links >= citation_count * 0.8:
                print(f"   ✅ Permalinks: MOST CITATIONS ({citations_with_links}/{citation_count})")
            else:
                print(f"   ⚠️  Permalinks: SOME MISSING ({citations_with_links}/{citation_count})")
            
            # Test passed
            print(f"   🎉 SCENARIO PASSED")
            passed_tests += 1
            
        except Exception as e:
            print(f"   ❌ SCENARIO FAILED: {e}")
    
    # Final summary
    print(f"\n📊 FINAL PRODUCTION TEST RESULTS")
    print("=" * 80)
    print(f"✅ Tests passed: {passed_tests}/{total_tests} ({(passed_tests/total_tests)*100:.1f}%)")
    print(f"📈 Total citations: {total_citations}")
    print(f"⚡ Average search time: {total_search_time/total_tests:.2f}s")
    print(f"🎯 Average citations per test: {total_citations/total_tests:.1f}")
    
    # Production readiness assessment
    if passed_tests == total_tests:
        print(f"\n🎉 PRODUCTION READY!")
        print(f"✅ All scenarios passed")
        print(f"✅ Citations working perfectly")
        print(f"✅ Search performance acceptable")
        print(f"✅ Content quality high")
        return True
    elif passed_tests >= total_tests * 0.8:
        print(f"\n⚠️  MOSTLY PRODUCTION READY")
        print(f"✅ {passed_tests}/{total_tests} scenarios passed")
        print(f"⚠️  Minor issues to address")
        return False
    else:
        print(f"\n❌ NOT PRODUCTION READY")
        print(f"❌ Only {passed_tests}/{total_tests} scenarios passed")
        print(f"❌ Major issues need fixing")
        return False

if __name__ == "__main__":
    success = final_production_test()
    if success:
        print(f"\n🚀 SYSTEM IS PRODUCTION READY FOR DEPLOYMENT!")
    else:
        print(f"\n🔧 SYSTEM NEEDS ADDITIONAL WORK BEFORE PRODUCTION")
