# RAGSearch API Documentation

This document provides comprehensive documentation for the RAGSearch API.

## Authentication

All API endpoints require authentication using one of the following methods:

### Token Authentication

```
Authorization: Token <api-token>
```

To generate an API token:

1. Go to the admin interface at `/admin/`
2. Navigate to API Keys
3. Create a new API key for the desired user and tenant

### Session Authentication

Browser-based requests can use session authentication if the user is already logged in.

## API Endpoints

### Search

#### Search Query

```
POST /api/search/
```

Performs a RAG search and returns results with citations.

**Request Body:**

```json
{
  "query": "What is RAG?",
  "top_k": 20,
  "use_hybrid_search": true,
  "min_relevance_score": 0.4,
  "use_context_aware": true,
  "use_query_expansion": false,
  "conversation_id": "optional-conversation-id"
}
```

**Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| query | string | Yes | The search query text |
| top_k | integer | No | Number of results to return (default: 10) |
| use_hybrid_search | boolean | No | Whether to use hybrid search (default: false) |
| min_relevance_score | float | No | Minimum relevance score for results (default: 0.3) |
| use_context_aware | boolean | No | Whether to use context-aware retrieval (default: false) |
| use_query_expansion | boolean | No | Whether to use query expansion (default: false) |
| conversation_id | string | No | ID of the conversation for context |

**Response:**

```json
{
  "id": "search-result-id",
  "query": "What is RAG?",
  "answer": "RAG (Retrieval-Augmented Generation) is a technique that enhances large language models by retrieving relevant information from external sources before generating a response...",
  "citations": [
    {
      "id": "citation-id",
      "document_id": "document-id",
      "document_title": "Introduction to RAG",
      "text": "RAG (Retrieval-Augmented Generation) combines retrieval systems with text generation...",
      "relevance_score": 0.92,
      "permalink": "https://example.com/documents/123",
      "source_name": "Documentation",
      "author_name": "John Doe",
      "created_at": "2023-05-15T14:30:00Z"
    }
  ],
  "metadata": {
    "retrieval_time_ms": 120,
    "generation_time_ms": 450,
    "total_time_ms": 570,
    "retriever_score_avg": 0.85,
    "llm_confidence_score": 0.92
  }
}
```

### Conversations

#### Create Conversation

```
POST /api/conversations/
```

Creates a new conversation.

**Request Body:**

```json
{
  "title": "RAG Discussion"
}
```

**Response:**

```json
{
  "id": "conversation-id",
  "title": "RAG Discussion",
  "created_at": "2023-06-10T09:15:00Z",
  "updated_at": "2023-06-10T09:15:00Z",
  "message_count": 0
}
```

#### List Conversations

```
GET /api/conversations/
```

Lists all conversations for the authenticated user.

**Response:**

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": "conversation-id-1",
      "title": "RAG Discussion",
      "created_at": "2023-06-10T09:15:00Z",
      "updated_at": "2023-06-10T10:30:00Z",
      "message_count": 5
    },
    {
      "id": "conversation-id-2",
      "title": "Vector Search Questions",
      "created_at": "2023-06-09T14:20:00Z",
      "updated_at": "2023-06-09T15:45:00Z",
      "message_count": 8
    }
  ]
}
```

#### Get Conversation

```
GET /api/conversations/{id}/
```

Retrieves a specific conversation.

**Response:**

```json
{
  "id": "conversation-id",
  "title": "RAG Discussion",
  "created_at": "2023-06-10T09:15:00Z",
  "updated_at": "2023-06-10T10:30:00Z",
  "message_count": 5,
  "messages": [
    {
      "id": "message-id-1",
      "content": "What is RAG?",
      "is_user": true,
      "created_at": "2023-06-10T09:15:30Z"
    },
    {
      "id": "message-id-2",
      "content": "RAG (Retrieval-Augmented Generation) is a technique that enhances large language models...",
      "is_user": false,
      "created_at": "2023-06-10T09:15:45Z",
      "search_result_id": "search-result-id-1"
    }
  ]
}
```

#### Add Message to Conversation

```
POST /api/conversations/{id}/messages/
```

Adds a new message to a conversation and performs a search if it's a user message.

**Request Body:**

```json
{
  "content": "How does RAG compare to fine-tuning?",
  "is_user": true
}
```

**Response:**

```json
{
  "id": "message-id",
  "content": "How does RAG compare to fine-tuning?",
  "is_user": true,
  "created_at": "2023-06-10T10:30:00Z",
  "conversation_id": "conversation-id"
}
```

### Documents

#### List Document Sources

```
GET /api/document-sources/
```

Lists all document sources for the authenticated user's tenant.

**Response:**

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": "source-id-1",
      "name": "Slack Workspace",
      "source_type": "local_slack",
      "last_synced": "2023-06-01T12:00:00Z",
      "is_active": true,
      "document_count": 1250
    },
    {
      "id": "source-id-2",
      "name": "GitHub Repository",
      "source_type": "github",
      "last_synced": "2023-06-05T14:30:00Z",
      "is_active": true,
      "document_count": 350
    }
  ]
}
```

#### Create Document Source

```
POST /api/document-sources/
```

Creates a new document source.

**Request Body:**

```json
{
  "name": "Slack Workspace",
  "source_type": "local_slack",
  "config": {
    "data_dir": "data/slack/",
    "time_period": "monthly",
    "enable_semantic_cross_refs": true,
    "use_embeddings": true
  }
}
```

**Response:**

```json
{
  "id": "source-id",
  "name": "Slack Workspace",
  "source_type": "local_slack",
  "last_synced": null,
  "is_active": true,
  "document_count": 0
}
```

#### Start Document Ingestion

```
POST /api/document-sources/{id}/ingest/
```

Starts a document ingestion job for the specified source.

**Response:**

```json
{
  "job_id": "job-id",
  "source_id": "source-id",
  "status": "pending",
  "started_at": "2023-06-10T11:00:00Z"
}
```

### Feedback

#### Submit Feedback

```
POST /api/search-results/{id}/feedback/
```

Submits feedback for a search result.

**Request Body:**

```json
{
  "is_helpful": true,
  "comment": "This answer was very accurate and provided good context."
}
```

**Response:**

```json
{
  "id": "feedback-id",
  "result_id": "search-result-id",
  "is_helpful": true,
  "comment": "This answer was very accurate and provided good context.",
  "submitted_at": "2023-06-10T15:45:00Z"
}
```

## Error Responses

All API endpoints return standard HTTP status codes:

- 200: Success
- 400: Bad Request (invalid parameters)
- 401: Unauthorized (invalid or missing authentication)
- 403: Forbidden (insufficient permissions)
- 404: Not Found (resource doesn't exist)
- 500: Internal Server Error

Error responses include a JSON body with details:

```json
{
  "error": "error_code",
  "message": "Human-readable error message",
  "details": {
    "field_name": ["Field-specific error message"]
  }
}
```

## Rate Limiting

API requests are rate-limited based on the API key or user account:

- 100 requests per minute for standard users
- 1000 requests per minute for premium users

Rate limit headers are included in all responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## Pagination

List endpoints support pagination with the following query parameters:

- `page`: Page number (default: 1)
- `page_size`: Number of items per page (default: 20, max: 100)

Paginated responses include navigation links:

```json
{
  "count": 125,
  "next": "https://api.example.com/api/conversations/?page=2",
  "previous": null,
  "results": [...]
}
```
