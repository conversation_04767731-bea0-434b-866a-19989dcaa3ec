{"date": "2024-03-11", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1710110901.701429", "text": "<@U065H3M6WJV> Here is a list of tasks that require to be automated for customer implementation\n<https://compiify.atlassian.net/browse/COM-2484> ( Note: This is a WIP as i continue to populate each of the jira's)", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}, {"name": "thankyouty", "users": ["U065H3M6WJV"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12545::4c6f2240df3011eebd20710c412f167b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2484?atlOrigin=eyJpIjoiMjQ3NjQ3MGU2NmZmNDhhYTg4YjYxNjk1N2M3MmU3M2QiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2484 End to End Customer Implementation Automation>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12545::4c6f2242df3011eebd20710c412f167b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12545::4c6f2241df3011eebd20710c412f167b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12545\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12545\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2484", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "q7Jf+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Here is a list of tasks that require to be automated for customer implementation\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2484"}, {"type": "text", "text": " ( Note: This is a WIP as i continue to populate each of the jira's)"}]}]}]}], "created_at": "2025-05-22T21:35:34.610712"}