{"thread_ts": "1738089916.651459", "channel_id": "C065QSSNH8A", "reply_count": 4, "replies": [{"ts": "1738123728.664039", "text": "Will take a look.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738089916.651459", "blocks": [{"type": "rich_text", "block_id": "vTXq6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take a look."}]}]}]}, {"ts": "1738151031.553279", "text": "<@U07EJ2LP44S> Looks like all of the employees being deleted are managers.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738089916.651459", "files": [{"id": "F08AQBHBUAZ", "created": 1738151026, "timestamp": 1738151026, "name": "Screenshot 2025-01-29 at 5.13.11 PM.png", "title": "Screenshot 2025-01-29 at 5.13.11 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 144934, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08AQBHBUAZ/screenshot_2025-01-29_at_5.13.11___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08AQBHBUAZ/download/screenshot_2025-01-29_at_5.13.11___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQBHBUAZ-33ffc6a8d7/screenshot_2025-01-29_at_5.13.11___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQBHBUAZ-33ffc6a8d7/screenshot_2025-01-29_at_5.13.11___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQBHBUAZ-33ffc6a8d7/screenshot_2025-01-29_at_5.13.11___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 329, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQBHBUAZ-33ffc6a8d7/screenshot_2025-01-29_at_5.13.11___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 438, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQBHBUAZ-33ffc6a8d7/screenshot_2025-01-29_at_5.13.11___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQBHBUAZ-33ffc6a8d7/screenshot_2025-01-29_at_5.13.11___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 657, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQBHBUAZ-33ffc6a8d7/screenshot_2025-01-29_at_5.13.11___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 730, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AQBHBUAZ-33ffc6a8d7/screenshot_2025-01-29_at_5.13.11___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 877, "original_w": 966, "original_h": 882, "thumb_tiny": "AwArADDSO3PJ/WkOKXPsaQ9aADijijmj60AAA7GlFJken60oxQAHPbFI3WlP1pG60AH4il5pPz/Oj8aAF+agZ70mPelFAAcZ5H6UjdadTG60AL/npRxSZ4H0pw6UAJx70opcD0oxQB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AQBHBUAZ/screenshot_2025-01-29_at_5.13.11___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08AQBHBUAZ-4f08847a05", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "uo534", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Looks like all of the employees being deleted are managers."}]}]}]}, {"ts": "1738151135.566679", "text": "Please also pass status = `Inactive` to delete employees in the file. We will fix it to remove the need to send the status as well as the update type is already available. Deletion should work with the file below post fixing the validation errors", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738089916.651459", "edited": {"user": "U0690EB5JE5", "ts": "1738151180.000000"}, "files": [{"id": "F08AVN89NVA", "created": 1738151133, "timestamp": 1738151133, "name": "CuranaDeletesbyHireDate.csv", "title": "CuranaDeletesbyHireDate.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": true, "size": 275, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08AVN89NVA/curanadeletesbyhiredate.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08AVN89NVA/download/curanadeletesbyhiredate.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AVN89NVA/curanadeletesbyhiredate.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08AVN89NVA-6dd3329754", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AVN89NVA/curanadeletesbyhiredate.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,Employment Status\r\nD,4412,Inactive\r\nD,4347,Inactive\r\nD,4587,Inactive\r\nD,4851,Inactive\r\nD,4497,Inactive\r\nD,4483,Inactive\r\nD,4709,Inactive\r\nD,2867,Inactive\r\nD,197,Inactive\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">Employment Status</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4412</div><div class=\"cm-col\">Inactive</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4347</div><div class=\"cm-col\">Inactive</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4587</div><div class=\"cm-col\">Inactive</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4851</div><div class=\"cm-col\">Inactive</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4497</div><div class=\"cm-col\">Inactive</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4483</div><div class=\"cm-col\">Inactive</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4709</div><div class=\"cm-col\">Inactive</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">2867</div><div class=\"cm-col\">Inactive</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">197</div><div class=\"cm-col\">Inactive</div></div></div>\n</div>\n", "lines": 14, "lines_more": 4, "preview_is_truncated": false, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Fexaa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please also pass status = "}, {"type": "text", "text": "Inactive", "style": {"code": true}}, {"type": "text", "text": " to delete employees in the file. We will fix it to remove the need to send the status as well as the update type is already available. Deletion should work with the file below post fixing the validation errors"}]}]}]}, {"ts": "1738151247.735089", "text": "you can add them to excluded list instead of deleting in the cycle to not mess with hierarchy.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738089916.651459", "blocks": [{"type": "rich_text", "block_id": "dx43T", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "you can add them to excluded list instead of deleting in the cycle to not mess with hierarchy."}]}]}]}], "created_at": "2025-05-22T22:00:49.347504"}