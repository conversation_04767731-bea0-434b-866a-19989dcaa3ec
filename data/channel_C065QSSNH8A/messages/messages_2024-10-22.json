{"date": "2024-10-22", "channel_id": "C065QSSNH8A", "message_count": 22, "messages": [{"ts": "**********.013819", "text": "<@U0690EB5JE5> perf rating upload is blocked for curanahealth <https://compiify.atlassian.net/browse/COM-3887> this can also affect other customers. please check", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13948::47e2afb88c474e0e8554091b8e3e79de", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3887?atlOrigin=eyJpIjoiNTJlZTE5YWYzMGViNGNjZjhlM2VjYWNkNjUyMmI2N2YiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3887 Unable to upload performance rating>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13948::dc74f2c4f43a445faccaacce2f6956a0", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13948\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3887\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3887", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "ZEKWi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " perf rating upload is blocked for curanahealth "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3887"}, {"type": "text", "text": " this can also affect other customers. please check"}]}]}]}, {"ts": "**********.366969", "text": "or maybe not double booked, just running over with a customer", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.366969", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "vMmtb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "or maybe not double booked, just running over with a customer"}]}]}]}, {"ts": "**********.214199", "text": "<PERSON><PERSON><PERSON><PERSON> and I did a brief touch base but since <PERSON><PERSON><PERSON> wasn't there and you were double booked we left to get some work done", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "u759c", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON> and I did a brief touch base but since <PERSON><PERSON><PERSON> wasn't there and you were double booked we left to get some work done"}]}]}]}, {"ts": "1729613468.336839", "text": "<@U07EJ2LP44S> are we meeting for standup? or is it cancelled?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9Kj60", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " are we meeting for standup? or is it cancelled?"}]}]}]}, {"ts": "1729611330.718579", "text": "<@U04DKEFP1K8> UPDATE:\n• HRBP changes are being worked on ETA hopefully tomorrow\n• Bonus budget issues are fixed except for the one Ka<PERSON>l <https://compiify.atlassian.net/browse/COM-3881|reported> . Same PRs\n• There are some other data discrepancy issues are fixed in <https://stride-hr.slack.com/archives/C053GSJ96RM/p1729606640798039|<PERSON><PERSON><PERSON>'s PR>. Please check PR description for issues fixed.\nI Couldn't test these fixes as i got the PRs just 30 mnts ago. Please go ahead and merge if fixes look good. Else I will take care tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729611330.718579", "reply_count": 7, "edited": {"user": "U0690EB5JE5", "ts": "1729612070.000000"}, "attachments": [{"from_url": "https://stride-hr.slack.com/archives/C053GSJ96RM/p1729606640798039", "ts": "1729606640.798039", "author_id": "U06HN8XDC5A", "channel_id": "C053GSJ96RM", "channel_team": "T04DM97F1UM", "is_msg_unfurl": true, "is_thread_root_unfurl": true, "message_blocks": [{"team": "T04DM97F1UM", "channel": "C053GSJ96RM", "ts": "1729606640.798039", "message": {"blocks": [{"type": "rich_text", "block_id": "8PAvE", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://github.com/Compiify/Yellowstone/pull/1715"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://github.com/Compiify/Yosemite/pull/1722"}, {"type": "text", "text": "\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Please review and ,merge"}]}]}]}}], "private_channel_prompt": true, "id": 1, "original_url": "https://stride-hr.slack.com/archives/C053GSJ96RM/p1729606640798039", "fallback": "[October 22nd, 2024 7:17 AM] mukesh: <https://github.com/Compiify/Yellowstone/pull/1715>,\n<https://github.com/Compiify/Yosemite/pull/1722>\n<@U0690EB5JE5> Please review and ,merge", "text": "<https://github.com/Compiify/Yellowstone/pull/1715>,\n<https://github.com/Compiify/Yosemite/pull/1722>\n<@U0690EB5JE5> Please review and ,merge", "author_name": "<PERSON><PERSON><PERSON>", "author_link": "https://stride-hr.slack.com/team/U06HN8XDC5A", "author_icon": "https://avatars.slack-edge.com/2024-02-12/6607493921911_af9e6603797795ca83de_48.jpg", "author_subname": "<PERSON><PERSON><PERSON>", "mrkdwn_in": ["text"], "footer": "Thread in Slack Conversation"}], "blocks": [{"type": "rich_text", "block_id": "nz2B5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " UPDATE:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "HRBP changes are being worked on ETA hopefully tomorrow"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Bonus budget issues are fixed except for the one <PERSON><PERSON><PERSON> "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3881", "text": "reported"}, {"type": "text", "text": " "}, {"type": "text", "text": ". Same PRs"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "There are some other data discrepancy issues are fixed in "}, {"type": "link", "url": "https://stride-hr.slack.com/archives/C053GSJ96RM/p1729606640798039", "text": "Mukes<PERSON>'s PR"}, {"type": "text", "text": ". Please check PR description for issues fixed."}]}], "style": "bullet", "indent": 0, "offset": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "I Couldn't test these fixes as i got the PRs just 30 mnts ago. Please go ahead and merge if fixes look good. Else I will take care tomorrow."}]}]}]}, {"ts": "1729574154.479959", "text": "<@U07M6QKHUC9> Sounds great. We can decide in today's leadership call.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729574154.479959", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "e2aZT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Sounds great. We can decide in today's leadership call."}]}]}]}, {"ts": "1729574032.958139", "text": "I’m not sure if we have any confidential discussions here. I am fine with adding engineers to this channel and renaming this channel to product engineering.\n\nOr we can create a new channel and use that going forward for all product and engineering discussions", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "YKSl9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I’m not sure if we have any confidential discussions here. I am fine with adding engineers to this channel and renaming this channel to product engineering.\n\nOr we can create a new channel and use that going forward for all product and engineering discussions"}]}]}]}, {"ts": "1729569635.472179", "text": "<!here> I feel there is a gap between me and engineers as they are not exposed to this channel. Yes somethings can be hidden or moved to <#C068KL59RJP|> channel. I think most of the issues discussions happen here I feel they should have exposer to make them feel part of what we go through.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1729569646.000000"}, "blocks": [{"type": "rich_text", "block_id": "T3AOQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I feel there is a gap between me and engineers as they are not exposed to this channel. Yes somethings can be hidden or moved to "}, {"type": "channel", "channel_id": "C068KL59RJP"}, {"type": "text", "text": " channel. I think most of the issues discussions happen here I feel they should have exposer to make them feel part of what we go through."}]}]}]}, {"ts": "1729562808.773389", "text": "<!here> Gentle Reminder! next weekend will be a long weekend in India. Oct 31st and Nov 1st.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "100", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Z5yar", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Gentle Reminder! next weekend will be a long weekend in India. Oct 31st and Nov 1st."}]}]}]}, {"ts": "1729560242.797399", "text": "<@U07M6QKHUC9> Just heads up the phase 1 from <PERSON>’s PRD planned to be delivered this week is delayed by a week due on going issues.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729560242.797399", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1729560273.000000"}, "blocks": [{"type": "rich_text", "block_id": "FMDz2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Just heads up the phase 1 "}, {"type": "text", "text": "from <PERSON>’s PRD "}, {"type": "text", "text": "planned to be delivered this week is delayed by a week due on going issues"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1729560167.300469", "text": "Thank you <PERSON><PERSON><PERSON><PERSON> will prioritise today ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gFP7Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you <PERSON><PERSON><PERSON><PERSON> will prioritise today "}]}]}]}, {"ts": "1729557212.484809", "text": "<@U0690EB5JE5> Bonus issue reported yesterday have been partially fixed ( still seeing some calculation errors, i have reopened them)\n• <https://compiify.atlassian.net/browse/COM-3873>\n• <https://compiify.atlassian.net/browse/COM-3874>", "user": "U04DKEFP1K8", "type": "message", "edited": {"user": "U04DKEFP1K8", "ts": "1729557224.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13934::40753a17c9da41869cd5675ecc7cdfe1", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3873?atlOrigin=eyJpIjoiNDc3MjRlYmNhYjNjNGRjZjg2ODQxMDQ0ZTQzODliZGQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3873 Bonus Budget Inaccuracy Issue>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13934::a97b6196fd1b4b92a0b7cc34853583f6", "elements": [{"type": "mrkdwn", "text": "Status: *In Development*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/712020:7d833b3a-e9d6-423f-93b8-d525378819e9/af9dff77-7442-4e64-9882-eb63aaa8f5a1/48?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13934\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3873\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3873", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:13935::57a204d9f77a4bcc8214d0c1a90792a7", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3874?atlOrigin=eyJpIjoiYzE4YzEzNmMxMmY5NDI2YWJmZmNkMTVlYjljZjRlZWEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3874 Issue: Incorrect Bonus Budget Calculation for Justin Rice>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13935::1af3418a78e54a63b620517287db27b1", "elements": [{"type": "mrkdwn", "text": "Status: *In Development*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/712020:7d833b3a-e9d6-423f-93b8-d525378819e9/af9dff77-7442-4e64-9882-eb63aaa8f5a1/48?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13935\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3874\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3874", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "rekui", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Bonus issue reported yesterday have been partially fixed ( still seeing some calculation errors, i have reopened them)\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3873"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3874"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1729554178.814399", "text": "Yes, for sure. Will recheck once this is fixed.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QG+Oo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, for sure. Will recheck once this is fixed."}]}]}]}, {"ts": "1729554026.170489", "text": "<@U07EJ2LP44S> Also, I am noticing similar discrepency issues in Alaya Care as well. So this issue is independent of the emp count discrepency in SDF env. <@U0690EB5JE5> FYA", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3PU5t", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Also, I am noticing similar discrepency issues in Alaya Care as well. So this issue is independent of the emp count discrepency in SDF env. "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " FYA"}]}]}]}, {"ts": "1729553962.611829", "text": "<@U07EJ2LP44S> after <@U0690EB5JE5> has fixed the emp count for org insights in SDF env, can you double check if the charts data in org view is matching with org insights. I just randomly checked the data for emp performance by position in range  and there is discrepency between org view and org insights.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729553962.611829", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "sWa7Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " after "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " has fixed the emp count for org insights in SDF env, can you double check if the charts data in org view is matching with org insights. I just randomly checked the data for emp performance by position in range  and there is discrepency between org view and org insights."}]}]}]}, {"ts": "**********.622069", "text": "<@U0690EB5JE5> HRBP issue and enhancements from Alayacare\n• <https://compiify.atlassian.net/browse/COM-3882>\n• <https://compiify.atlassian.net/browse/COM-3883>\n• <https://compiify.atlassian.net/browse/COM-3884>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.622069", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13943::99bcb3e3a4ab4db7af2c806f16d0d8ba", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3882?atlOrigin=eyJpIjoiZjBlMzllZDdiMjVkNDg5YWJlMzMwZDE2YTZjNThkM2MiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3882 Enhancement to Impersonation for HR Partner Account Management>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13943::cb366b2aee2d47c59545d7620f91fb10", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13943\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3882\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3882", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:13944::9c67100fa00c489892d55f464c9037af", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3883?atlOrigin=eyJpIjoiZWE2MzM3MGMwNzJkNDk4ODg5YzFmMjA2MjNkNzI2ZTMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3883 Issue: Allow removal of HR business partner from specific managers>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13944::24c9be1c43de488b8e4ac7a11b986406", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13944\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3883\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3883", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 3, "blocks": [{"type": "section", "block_id": "uf:ih:13945::f3648e93e0e64859b28a2682967b8ac7", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3884?atlOrigin=eyJpIjoiYmU4OGNjOTg3Y2EwNDU2OWJiNzM5ODNkNDljYjU3MGEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3884 HR Admin Role Cannot Switch to Manager View>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13945::2fc33a1ab622484e9b9b33274b0b9bdc", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13945\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3884\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3884", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "xSK9e", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " HRBP issue and enhancements from Alayacare\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3882"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3883"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3884"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1729546448.111359", "text": "<@U0690EB5JE5> Just FYI, I have started to add the jira tickets for AlayaCare based on QA env. Here is an interesting one:\n<https://www.loom.com/share/c855918647d14e1aa692de11ca722072>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729546448.111359", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "/WBwY", "video_url": "https://www.loom.com/embed/c855918647d14e1aa692de11ca722072?unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/c855918647d14e1aa692de11ca722072-27f98d7055437575-4x3.jpg", "alt_text": "AlayaCare QA Site Investigation 🕵️♂️", "title": {"type": "plain_text", "text": "AlayaCare QA Site Investigation 🕵️‍♂️", "emoji": true}, "title_url": "https://www.loom.com/share/c855918647d14e1aa692de11ca722072", "author_name": "<PERSON><PERSON><PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 38 sec  ", "emoji": true}}, {"type": "section", "block_id": "tprnR", "text": {"type": "mrkdwn", "text": ":information_source: I'm currently at the AlayaCare QA site investigating an issue with the budget, salary, and promotion increase percentage for country MG. Despite...", "verbatim": false}}, {"type": "actions", "block_id": "kXqvl", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/c855918647d14e1aa692de11ca722072"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"c855918647d14e1aa692de11ca722072\",\"videoName\":\"AlayaCare QA Site Investigation 🕵️♂️\",\"sendWatchLaterReminderWeekdaysOnly\":true,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/c855918647d14e1aa692de11ca722072", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "k/Gjq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Just FYI, I have started to add the jira tickets for AlayaCare based on QA env. Here is an interesting one:\n"}, {"type": "link", "url": "https://www.loom.com/share/c855918647d14e1aa692de11ca722072"}]}]}]}, {"ts": "1729542800.157349", "text": "i might get interrupted by the heating repair guy", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ymR0I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i might get interrupted by the heating repair guy"}]}]}]}, {"ts": "1729542786.475789", "text": "yes i can do that", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729542786.475789", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "MXtik", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes i can do that"}]}]}]}, {"ts": "1729542748.248539", "text": "<@U07EJ2LP44S> do you have 15 mins to walk me thru the data and use cases of AlayaCare so I can get started with its testing?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "m0871", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " do you have 15 mins to walk me thru the data and use cases of AlayaCare so I can get started with its testing?"}]}]}]}, {"ts": "1729538536.383879", "text": "Speaking of, found a bug this morning in their data; just put this in at highest priority <@U04DKEFP1K8> <https://compiify.atlassian.net/browse/COM-3879>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729538536.383879", "reply_count": 1, "edited": {"user": "U07EJ2LP44S", "ts": "1729538560.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13940::6c12b366a4de4f7eb3d388dbd3ea7e51", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3879?atlOrigin=eyJpIjoiMWM2MTVlNjA4ZGRlNDg0MTljOWQyZjlhNDlmM2FlODMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3879 Employee Count Discrepancy in PeopleInsights>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13940::fd898438a1fb4b678f6f0a0e066aadbe", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13940\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3879\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3879", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "n9MiB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Speaking of, found a bug this morning in their data; just put this in at highest priority "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3879"}]}]}]}, {"ts": "1729538111.252979", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> Great job with the demo call. It looks we are on our way to win their confidence back.:tada:", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729538111.252979", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Sv6XE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Great job with the demo call. It looks we are on our way to win their confidence back."}, {"type": "emoji", "name": "tada", "unicode": "1f389"}]}]}]}], "created_at": "2025-05-22T21:35:34.663964"}