{"date": "2024-01-01", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1704057157.231289", "text": "Phew, this is making me real nervous. ", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1704057157.231289", "reply_count": 6, "reactions": [{"name": "aaaaaa", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "eLFY1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Phew, this is making me real nervous. "}]}]}]}, {"ts": "1704057096.314619", "text": "Things I still haven't been able to test thoroughly:\n• Budget adherence when submitting changes\n• Reports / charts\n• Adjustment letters (I haven't really started looking at these yet)", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Y21Ip", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Things I still haven't been able to test thoroughly:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Budget adherence when submitting changes"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Reports / charts"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Adjustment letters (I haven't really started looking at these yet)"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1704057016.345659", "text": "Today's update: I did some more testing on SDF's instance, to see if I could complete a manager's entire set of recommendations &amp; submission.\n\nI managed to get it into a state where neither the manager nor the admin can update a few employees, so the cycle cannot be completed. :upside_down_face:\n\nI logged the bugs I found along the way, including the ones at the root of this ^ blocking scenario. Updated bug counts are below.\n\n• <https://compiify.atlassian.net/browse/COM-2084|COM-2084: Showstoppers> : 4 issues\n• <https://compiify.atlassian.net/browse/COM-2085|COM-2085: Wave 1> : 24 issues\n• <https://compiify.atlassian.net/browse/COM-2086|COM-2086: Wave 2> : 26 issues\n• <https://compiify.atlassian.net/browse/COM-2087|COM-2087: Wave 3> : 24 issues\n• <https://compiify.atlassian.net/browse/COM-2088|COM-2088: Wave 4> : 29 issues\n", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WU001", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Today's update: I did some more testing on SDF's instance, to see if I could complete a manager's entire set of recommendations & submission.\n\nI managed to get it into a state where neither the manager nor the admin can update a few employees, so the cycle cannot be completed. "}, {"type": "emoji", "name": "upside_down_face", "unicode": "1f643"}, {"type": "text", "text": "\n\nI logged the bugs I found along the way, including the ones at the root of this ^ blocking scenario. Updated bug counts are below.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2084", "text": "COM-2084: Showstoppers"}, {"type": "text", "text": " : 4 issues"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2085", "text": "COM-2085: Wave 1"}, {"type": "text", "text": " : 24 issues"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2086", "text": "COM-2086: Wave 2"}, {"type": "text", "text": " : 26 issues"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "COM-2087: Wave 3"}, {"type": "text", "text": " : 24 issues"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2088", "text": "COM-2088: Wave 4"}, {"type": "text", "text": " : 29 issues"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}], "created_at": "2025-05-22T21:35:34.595277"}