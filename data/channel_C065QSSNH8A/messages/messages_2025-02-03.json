{"date": "2025-02-03", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1738587870.092229", "text": "<@U07EJ2LP44S> This is fixed.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738342580.211129", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "hG5Os", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is fixed."}]}]}]}, {"ts": "1738587857.326209", "text": "<@U07EJ2LP44S> I have an <https://github.com/Compiify/Yellowstone/pull/2086|API> ready to distribute the delta based on the current distribution. I am just waiting for <PERSON>'s confirmation and validate with you.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1738587981.000000"}, "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}, {"name": "tada", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "0l5Fe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have an "}, {"type": "link", "url": "https://github.com/Compiify/Yellowstone/pull/2086", "text": "API"}, {"type": "text", "text": " ready to distribute the delta based on the current distribution. I am just waiting for <PERSON>'s confirmation and validate with you."}]}]}]}], "created_at": "2025-05-22T21:35:34.702966"}