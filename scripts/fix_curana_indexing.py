#!/usr/bin/env python3
"""
Fix the curana chunks indexing issue by re-indexing them in Qdrant.
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.documents.models import DocumentChunk
from apps.accounts.models import Tenant
from apps.core.utils.llama_index_vectorstore import get_vector_store
from apps.core.utils.collection_manager import get_collection_name
from llama_index.core.schema import TextNode
from llama_index.core import Document
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_curana_indexing():
    """Fix the curana chunks indexing issue."""
    print("🔧 Fixing Curana Chunks Indexing Issue")
    print("=" * 50)

    # Initialize LlamaIndex components first
    print("🔧 Initializing LlamaIndex components...")
    from apps.core.utils.llama_index_embeddings import initialize_embedding_models
    from apps.core.utils.embedding_consistency import set_global_embedding_model

    initialize_embedding_models()
    set_global_embedding_model()
    print("✅ LlamaIndex components initialized")

    # Get stride tenant
    tenant = Tenant.objects.get(slug='stride')

    # Get all curana chunks
    curana_chunks = DocumentChunk.objects.filter(
        tenant=tenant,
        text__icontains='curana'
    )

    print(f"📊 Found {curana_chunks.count()} curana chunks to re-index")

    if curana_chunks.count() == 0:
        print("❌ No curana chunks found!")
        return

    # Get vector store with proper embedding model
    collection_name = get_collection_name(tenant.slug, intent="conversation")

    # Create vector store with embedding model
    from llama_index.core import Settings
    from llama_index.vector_stores.qdrant import QdrantVectorStore
    from apps.core.utils.collection_manager import get_qdrant_client

    client = get_qdrant_client()
    vector_store = QdrantVectorStore(
        client=client,
        collection_name=collection_name
    )

    # Set the embedding model on the vector store
    vector_store._embed_model = Settings.embed_model

    print(f"🔗 Using vector collection: {collection_name}")
    print(f"🔗 Using embedding model: {type(Settings.embed_model).__name__}")

    # Convert chunks to LlamaIndex nodes and add to vector store
    nodes = []
    for chunk in curana_chunks:
        # Create TextNode with the same node_id as stored in metadata
        node_id = chunk.metadata.get('node_id')
        if not node_id:
            print(f"⚠️  Chunk {chunk.id} has no node_id, skipping")
            continue

        node = TextNode(
            text=chunk.text,
            id_=node_id,  # Use the same node_id
            metadata={
                **chunk.metadata,
                "chunk_id": chunk.id,
                "document_id": str(chunk.document.id),
                "tenant_id": str(tenant.id),
            }
        )
        nodes.append(node)

        if len(nodes) % 10 == 0:
            print(f"   📝 Prepared {len(nodes)} nodes...")

    print(f"📝 Prepared {len(nodes)} nodes for indexing")

    if len(nodes) == 0:
        print("❌ No valid nodes to index!")
        return

    # Add nodes to vector store using VectorStoreIndex
    print("🔄 Adding nodes to vector store...")
    try:
        from llama_index.core import VectorStoreIndex

        # Create index from vector store
        index = VectorStoreIndex.from_vector_store(vector_store)

        # Insert nodes into the index
        index.insert_nodes(nodes)
        print(f"✅ Successfully indexed {len(nodes)} curana chunks!")
    except Exception as e:
        print(f"❌ Error indexing chunks: {e}")
        import traceback
        traceback.print_exc()
        return

    # Test the fix
    print("\n🧪 Testing the fix...")
    from apps.search.services.unified_rag_service import UnifiedRAGService
    from apps.accounts.models import User

    user = User.objects.filter(email='<EMAIL>').first()
    if not user:
        print("❌ Test user not found")
        return

    # Initialize RAG service
    rag_service = UnifiedRAGService('stride', user)

    # Test search
    response = rag_service.citation_engine.query("curana")
    source_nodes = response.source_nodes

    print(f"🔍 Test search found {len(source_nodes)} nodes")

    # Check if any curana chunks are found
    curana_node_ids = [chunk.metadata.get('node_id') for chunk in curana_chunks]
    found_curana = 0

    for node in source_nodes:
        if node.node_id in curana_node_ids:
            found_curana += 1
            print(f"   ✅ Found curana chunk: {node.node_id} (score: {getattr(node, 'score', 'N/A')})")

    if found_curana > 0:
        print(f"🎉 SUCCESS! Found {found_curana} curana chunks in search results!")
        print("✅ Curana indexing issue has been fixed!")
    else:
        print("❌ Still no curana chunks found in search results")
        print("🔍 Top results:")
        for i, node in enumerate(source_nodes[:3]):
            print(f"   {i+1}. {node.node_id} (score: {getattr(node, 'score', 'N/A')})")

if __name__ == "__main__":
    fix_curana_indexing()
