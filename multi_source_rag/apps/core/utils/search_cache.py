"""
Search result caching utilities for performance optimization.
"""

import hashlib
import json
import logging
from typing import Any, Dict, List, Optional, Tuple

from django.core.cache import cache
from django.conf import settings
from langchain.schema import Document

logger = logging.getLogger(__name__)

# Cache configuration
SEARCH_CACHE_TIMEOUT = getattr(settings, 'SEARCH_CACHE_TIMEOUT', 3600)  # 1 hour default
SEARCH_CACHE_PREFIX = 'search_cache:'
MAX_CACHE_KEY_LENGTH = 250


def _generate_cache_key(
    query: str,
    collection_name: str,
    metadata_filter: Optional[Dict[str, Any]] = None,
    k: int = 5,
    content_type: Optional[str] = None,
    tenant_slug: Optional[str] = None,
) -> str:
    """
    Generate a cache key for search parameters.
    
    Args:
        query: Search query
        collection_name: Vector collection name
        metadata_filter: Metadata filter parameters
        k: Number of results
        content_type: Content type for embedding selection
        tenant_slug: Tenant identifier
        
    Returns:
        str: Cache key
    """
    # Create a dictionary of all parameters
    cache_params = {
        'query': query.lower().strip(),  # Normalize query
        'collection': collection_name,
        'k': k,
        'content_type': content_type,
        'tenant': tenant_slug,
        'filter': metadata_filter or {}
    }
    
    # Convert to JSON string for consistent hashing
    cache_string = json.dumps(cache_params, sort_keys=True)
    
    # Generate hash
    cache_hash = hashlib.md5(cache_string.encode('utf-8')).hexdigest()
    
    # Create cache key with prefix
    cache_key = f"{SEARCH_CACHE_PREFIX}{cache_hash}"
    
    # Ensure key length is within limits
    if len(cache_key) > MAX_CACHE_KEY_LENGTH:
        cache_key = f"{SEARCH_CACHE_PREFIX}{cache_hash[:MAX_CACHE_KEY_LENGTH-len(SEARCH_CACHE_PREFIX)]}"
    
    return cache_key


def _serialize_search_results(results: List[Tuple[Document, float]]) -> List[Dict[str, Any]]:
    """
    Serialize search results for caching.
    
    Args:
        results: List of (Document, score) tuples
        
    Returns:
        List[Dict]: Serialized results
    """
    serialized = []
    for doc, score in results:
        serialized.append({
            'content': doc.page_content,
            'metadata': doc.metadata,
            'score': score
        })
    return serialized


def _deserialize_search_results(serialized: List[Dict[str, Any]]) -> List[Tuple[Document, float]]:
    """
    Deserialize cached search results.
    
    Args:
        serialized: Serialized search results
        
    Returns:
        List[Tuple[Document, float]]: Deserialized results
    """
    results = []
    for item in serialized:
        doc = Document(
            page_content=item['content'],
            metadata=item['metadata']
        )
        results.append((doc, item['score']))
    return results


def get_cached_search_results(
    query: str,
    collection_name: str,
    metadata_filter: Optional[Dict[str, Any]] = None,
    k: int = 5,
    content_type: Optional[str] = None,
    tenant_slug: Optional[str] = None,
) -> Optional[List[Tuple[Document, float]]]:
    """
    Get cached search results if available.
    
    Args:
        query: Search query
        collection_name: Vector collection name
        metadata_filter: Metadata filter parameters
        k: Number of results
        content_type: Content type for embedding selection
        tenant_slug: Tenant identifier
        
    Returns:
        Optional[List[Tuple[Document, float]]]: Cached results or None
    """
    try:
        cache_key = _generate_cache_key(
            query=query,
            collection_name=collection_name,
            metadata_filter=metadata_filter,
            k=k,
            content_type=content_type,
            tenant_slug=tenant_slug
        )
        
        cached_data = cache.get(cache_key)
        if cached_data:
            logger.info(f"Cache hit for search query: {query[:50]}...")
            return _deserialize_search_results(cached_data)
        
        logger.debug(f"Cache miss for search query: {query[:50]}...")
        return None
        
    except Exception as e:
        logger.warning(f"Error retrieving from cache: {str(e)}")
        return None


def cache_search_results(
    query: str,
    collection_name: str,
    results: List[Tuple[Document, float]],
    metadata_filter: Optional[Dict[str, Any]] = None,
    k: int = 5,
    content_type: Optional[str] = None,
    tenant_slug: Optional[str] = None,
    timeout: Optional[int] = None,
) -> bool:
    """
    Cache search results for future use.
    
    Args:
        query: Search query
        collection_name: Vector collection name
        results: Search results to cache
        metadata_filter: Metadata filter parameters
        k: Number of results
        content_type: Content type for embedding selection
        tenant_slug: Tenant identifier
        timeout: Cache timeout in seconds (uses default if None)
        
    Returns:
        bool: True if caching succeeded, False otherwise
    """
    try:
        cache_key = _generate_cache_key(
            query=query,
            collection_name=collection_name,
            metadata_filter=metadata_filter,
            k=k,
            content_type=content_type,
            tenant_slug=tenant_slug
        )
        
        serialized_results = _serialize_search_results(results)
        
        cache_timeout = timeout or SEARCH_CACHE_TIMEOUT
        cache.set(cache_key, serialized_results, cache_timeout)
        
        logger.debug(f"Cached search results for query: {query[:50]}...")
        return True
        
    except Exception as e:
        logger.warning(f"Error caching search results: {str(e)}")
        return False


def invalidate_search_cache(
    tenant_slug: Optional[str] = None,
    collection_name: Optional[str] = None
) -> int:
    """
    Invalidate search cache entries.
    
    Args:
        tenant_slug: Invalidate cache for specific tenant (None for all)
        collection_name: Invalidate cache for specific collection (None for all)
        
    Returns:
        int: Number of cache entries invalidated
    """
    try:
        # Note: Django's cache framework doesn't support pattern-based deletion
        # This is a simplified implementation that would need to be enhanced
        # for production use with Redis or Memcached pattern support
        
        if hasattr(cache, 'delete_pattern'):
            # Redis cache backend supports pattern deletion
            pattern = SEARCH_CACHE_PREFIX + '*'
            if tenant_slug:
                pattern += f'*{tenant_slug}*'
            if collection_name:
                pattern += f'*{collection_name}*'
            
            deleted_count = cache.delete_pattern(pattern)
            logger.info(f"Invalidated {deleted_count} cache entries with pattern: {pattern}")
            return deleted_count
        else:
            # Fallback: clear entire cache (not ideal for production)
            logger.warning("Cache backend doesn't support pattern deletion, clearing all cache")
            cache.clear()
            return 1  # Return 1 to indicate cache was cleared
            
    except Exception as e:
        logger.error(f"Error invalidating search cache: {str(e)}")
        return 0


def get_cache_stats() -> Dict[str, Any]:
    """
    Get cache statistics if available.
    
    Returns:
        Dict[str, Any]: Cache statistics
    """
    try:
        if hasattr(cache, 'get_stats'):
            return cache.get_stats()
        else:
            return {
                'cache_backend': str(type(cache)),
                'pattern_support': hasattr(cache, 'delete_pattern'),
                'timeout': SEARCH_CACHE_TIMEOUT
            }
    except Exception as e:
        logger.error(f"Error getting cache stats: {str(e)}")
        return {'error': str(e)}
