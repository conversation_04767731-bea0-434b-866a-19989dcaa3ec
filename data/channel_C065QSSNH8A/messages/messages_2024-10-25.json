{"date": "2024-10-25", "channel_id": "C065QSSNH8A", "message_count": 13, "messages": [{"ts": "1729871925.695849", "text": "we can use the zoom link for today's standup\n<https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "U5rKh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we can use the zoom link for today's standup\n"}, {"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1729871577.336979", "text": "+ update on Degen Kolb status", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729862398.370119", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "16B7I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "+ update on Degen Kolb status"}]}]}]}, {"ts": "1729871465.165529", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> we can meet over my zoom", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6+TdY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " we can meet over my zoom"}]}]}]}, {"ts": "1729864623.878039", "text": "Hi team, I am really feeling unwell today. Will need to take the day off calls, but I will still try and finalize some of the data that needs to be finished and do some final testing for SDF. We'll call it a semi sick day from bed.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729864623.878039", "reply_count": 2, "edited": {"user": "U07EJ2LP44S", "ts": "1729870439.000000"}, "blocks": [{"type": "rich_text", "block_id": "HsrMb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hi team, I am really feeling unwell today. Will need to take the day off calls, but I will still try and finalize some of the data that needs to be finished and do some final testing for SDF. We'll call it a semi sick day from bed."}]}]}]}, {"ts": "1729862398.370119", "text": "Agenda for today:\n• HRBP requirements review. Work is almost done with current understanding. Have questions around budgets and counts\n• SDF  ENV readiness for Monday's demo", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729862398.370119", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Wfqw4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "HRBP requirements review. Work is almost done with current understanding. Have questions around budgets and counts"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF  ENV readiness for Monday's demo"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1729861170.225999", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> The issue was due to partially filled cycle. Currently when data changes are uploaded, system tries to update open cycles. Currently the partially filled cycle is not handled gracefully.  Deleting the cycle will fix the problem. However we have put minor fix to handle this. We have a backlog ticket to not update all open cycles but only Active cycles.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729798430.190029", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1729861205.000000"}, "files": [{"id": "F07TWJWBJQH", "created": 1729861006, "timestamp": 1729861006, "name": "Screenshot 2024-10-25 at 11.54.47 AM.png", "title": "Screenshot 2024-10-25 at 11.54.47 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 95978, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07TWJWBJQH/screenshot_2024-10-25_at_11.54.47___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07TWJWBJQH/download/screenshot_2024-10-25_at_11.54.47___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TWJWBJQH-edcc77c3c2/screenshot_2024-10-25_at_11.54.47___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TWJWBJQH-edcc77c3c2/screenshot_2024-10-25_at_11.54.47___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TWJWBJQH-edcc77c3c2/screenshot_2024-10-25_at_11.54.47___am_360.png", "thumb_360_w": 360, "thumb_360_h": 100, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TWJWBJQH-edcc77c3c2/screenshot_2024-10-25_at_11.54.47___am_480.png", "thumb_480_w": 480, "thumb_480_h": 134, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TWJWBJQH-edcc77c3c2/screenshot_2024-10-25_at_11.54.47___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TWJWBJQH-edcc77c3c2/screenshot_2024-10-25_at_11.54.47___am_720.png", "thumb_720_w": 720, "thumb_720_h": 201, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TWJWBJQH-edcc77c3c2/screenshot_2024-10-25_at_11.54.47___am_800.png", "thumb_800_w": 800, "thumb_800_h": 223, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TWJWBJQH-edcc77c3c2/screenshot_2024-10-25_at_11.54.47___am_960.png", "thumb_960_w": 960, "thumb_960_h": 268, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07TWJWBJQH-edcc77c3c2/screenshot_2024-10-25_at_11.54.47___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 285, "original_w": 1912, "original_h": 533, "thumb_tiny": "AwANADDRxmlAFA6UtACcelHHpS0UAJj2pp356Lj60+igD//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07TWJWBJQH/screenshot_2024-10-25_at_11.54.47___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07TWJWBJQH-264dd93270", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "v/lSJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " The issue was due to partially filled cycle. Currently when data changes are uploaded, system tries to update open cycles. Currently the partially filled cycle is not handled gracefully.  Deleting the cycle will fix the problem. However we have put minor fix to handle this. We have a backlog ticket to not update all open cycles but only Active cycles."}]}]}]}, {"ts": "1729812511.925129", "text": "<@U0690EB5JE5> restested SDF for key issues. I think we are good here after <PERSON> does her re-testing.", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "9ndFN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " restested SDF for key issues. I think we are good here after <PERSON> does her re-testing."}]}]}]}, {"ts": "1729812042.038759", "text": "<@U07EJ2LP44S> Can you please update the status of SDF tickets raised by you in Jira?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nBWNU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Can you please update the status of SDF tickets raised by you in Jira?"}]}]}]}, {"ts": "1729810495.342539", "text": "<@U0690EB5JE5> added a minor UX bug for SDF <https://compiify.atlassian.net/browse/COM-3922>", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14005::62f5abf7acbc4a08aac00f6a61481ef0", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3922?atlOrigin=eyJpIjoiM2U1YjgwOGU1OWI4NGNkZWI0OTQ3MTkyZjNlMGIwODMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3922 UX Bug with Pagination Display>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14005::45d05de681e8449e8251306a31ebe983", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14005\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3922\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3922", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "s9sjk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " added a minor UX bug for SDF "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3922"}]}]}]}, {"ts": "1729809708.444379", "text": "<@U04DKEFP1K8> can you pls add me and mahesh to lasoft channel?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729809708.444379", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "dpmOJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you pls add me and mahesh to lasoft channel?"}]}]}]}, {"ts": "1729806833.290299", "text": "<@U0690EB5JE5> i am observing <https://compiify.atlassian.net/browse/COM-3888> now on people insights ( this is totally disrupting application access if use click on people insights).\ncc: <@U07EJ2LP44S>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1729806833.290299", "reply_count": 10, "edited": {"user": "U04DKEFP1K8", "ts": "1729807533.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13949::ab5d5cd01d1a46c09f2a1f5f4ab8665c", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3888?atlOrigin=eyJpIjoiZTQ0MjA5MmUwMzFlNDJkYmJiYWQzNzVlN2Q4NGIwOTAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3888 GC triggered in alayacare production environment on fetching budget…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13949::f23ed59810944c7caa84fad5e7e3196a", "elements": [{"type": "mrkdwn", "text": "Status: *In Development*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13949\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3888\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3888", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "mIv7I", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " i am observing "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3888"}, {"type": "text", "text": " now on people insights ( this is totally disrupting application access if use click on people insights).\ncc: "}, {"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1729798430.190029", "text": "<@U0690EB5JE5> <https://compiify.atlassian.net/browse/COM-3887> is blocker for adding new rating or updating existing ratings. <@U07EJ2LP44S> is totally blocked", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1729798430.190029", "reply_count": 6, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13948::7421b9d736ed4351afdc9c3df2977472", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3887?atlOrigin=eyJpIjoiZDViZGQ4N2M2NzRlNGNiZTk0ZTUyYjNlMTZiY2EzYTYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3887 Unable to upload performance rating>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13948::3e7ca8576a1d4f01abf34172113a333a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/35dd492af25f9dfa74f8be283d9a0bba?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13948\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3887\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3887", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "9oDMK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3887"}, {"type": "text", "text": " is blocker for adding new rating or updating existing ratings. "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " is totally blocked"}]}]}]}, {"ts": "1729796340.605359", "text": "<@U07EJ2LP44S> please check this loom <https://www.loom.com/share/150e11b35d5b47dd9002b899ffe153cc> ( no issues here but just more of fyi if we need to share with Alyssa)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1729796340.605359", "reply_count": 13, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "qc0oL", "video_url": "https://www.loom.com/embed/150e11b35d5b47dd9002b899ffe153cc?unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/150e11b35d5b47dd9002b899ffe153cc-f48dd4819da4011b-4x3.jpg", "alt_text": "Understanding Bonus Calculation for Employees", "title": {"type": "plain_text", "text": "Understanding Bonus Calculation for Employees", "emoji": true}, "title_url": "https://www.loom.com/share/150e11b35d5b47dd9002b899ffe153cc", "author_name": "<PERSON><PERSON><PERSON><PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 1 min  ", "emoji": true}}, {"type": "section", "block_id": "KjhQ6", "text": {"type": "mrkdwn", "text": ":information_source: <PERSON>, in this video, I discuss the importance of having a proper bonus program for accurate percentage calculations related to employee...", "verbatim": false}}, {"type": "actions", "block_id": "edsLk", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/150e11b35d5b47dd9002b899ffe153cc"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"150e11b35d5b47dd9002b899ffe153cc\",\"videoName\":\"Understanding Bonus Calculation for Employees\",\"sendWatchLaterReminderWeekdaysOnly\":true,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/150e11b35d5b47dd9002b899ffe153cc", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "dW8eo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " please check this loom "}, {"type": "link", "url": "https://www.loom.com/share/150e11b35d5b47dd9002b899ffe153cc"}, {"type": "text", "text": " ( no issues here but just more of fyi if we need to share with <PERSON><PERSON>)"}]}]}]}], "created_at": "2025-05-22T21:35:34.661614"}