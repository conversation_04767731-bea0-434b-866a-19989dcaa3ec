#!/usr/bin/env python3
"""
Test script to verify response quality improvements.

This script tests the improved RAG service to ensure:
1. Responses are more comprehensive and detailed
2. Citations are working correctly
3. Response quality has improved significantly
"""

import os
import sys
import django
import time

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth.models import User
from apps.search.services.rag_service import RAGService


def test_response_quality():
    """Test the improved response quality."""
    print("🧪 Testing Response Quality Improvements")
    print("=" * 50)
    
    try:
        # Get user
        user = User.objects.get(email='<EMAIL>')
        print(f"✅ Found user: {user.email}")
        
        # Create service
        service = RAGService(tenant_slug='test-tenant', user=user)
        print(f"✅ Created RAG service for tenant: test-tenant")
        
        # Test queries
        test_queries = [
            "list issues reported on Curana",
            "what are the main problems with the authentication system",
            "explain the deployment process for the application",
            "what are the recent bug reports and their status"
        ]
        
        print(f"\n📝 Testing {len(test_queries)} queries...")
        
        results = []
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔍 Query {i}: {query}")
            print("-" * 40)
            
            start_time = time.time()
            
            # Execute search with improved settings
            result, docs = service.search(
                query_text=query,
                top_k=20,
                use_context_aware=True,  # Use enhanced prompts
                use_hybrid_search=True,
                min_relevance_score=0.1
            )
            
            processing_time = time.time() - start_time
            
            # Analyze results
            response_length = len(result.generated_answer)
            citation_count = len(docs)
            avg_relevance = sum(doc[1] for doc in docs) / len(docs) if docs else 0
            
            print(f"⏱️  Processing Time: {processing_time:.2f}s")
            print(f"📄 Response Length: {response_length} characters")
            print(f"🔗 Citations: {citation_count}")
            print(f"📊 Avg Relevance: {avg_relevance:.3f}")
            print(f"📝 Response Preview: {result.generated_answer[:200]}...")
            
            results.append({
                'query': query,
                'response_length': response_length,
                'citation_count': citation_count,
                'avg_relevance': avg_relevance,
                'processing_time': processing_time,
                'response': result.generated_answer
            })
            
            # Quality assessment
            if response_length > 200 and citation_count > 0:
                print("✅ Quality: GOOD (Detailed response with citations)")
            elif response_length > 100:
                print("⚠️  Quality: FAIR (Moderate detail)")
            else:
                print("❌ Quality: POOR (Too brief)")
        
        # Summary
        print(f"\n📊 SUMMARY RESULTS")
        print("=" * 50)
        
        total_queries = len(results)
        avg_response_length = sum(r['response_length'] for r in results) / total_queries
        avg_citations = sum(r['citation_count'] for r in results) / total_queries
        avg_processing_time = sum(r['processing_time'] for r in results) / total_queries
        avg_relevance_score = sum(r['avg_relevance'] for r in results) / total_queries
        
        print(f"📈 Average Response Length: {avg_response_length:.0f} characters")
        print(f"🔗 Average Citations: {avg_citations:.1f}")
        print(f"⏱️  Average Processing Time: {avg_processing_time:.2f}s")
        print(f"📊 Average Relevance Score: {avg_relevance_score:.3f}")
        
        # Quality assessment
        good_quality = sum(1 for r in results if r['response_length'] > 200 and r['citation_count'] > 0)
        quality_percentage = (good_quality / total_queries) * 100
        
        print(f"\n🎯 QUALITY ASSESSMENT")
        print(f"✅ Good Quality Responses: {good_quality}/{total_queries} ({quality_percentage:.0f}%)")
        
        if quality_percentage >= 75:
            print("🎉 EXCELLENT: Response quality improvements are working well!")
        elif quality_percentage >= 50:
            print("👍 GOOD: Response quality has improved significantly")
        else:
            print("⚠️  NEEDS WORK: Response quality still needs improvement")
        
        # Expected improvements verification
        print(f"\n🔍 IMPROVEMENT VERIFICATION")
        print("Expected improvements from changes:")
        print("- ✅ 140% more context (5 → 12 documents)")
        print("- ✅ Better synthesis (simple_summarize → tree_summarize)")
        print("- ✅ Quality filtering (SimilarityPostprocessor)")
        
        if avg_response_length > 300:
            print("✅ Response length indicates comprehensive answers")
        else:
            print("⚠️  Response length could be more comprehensive")
            
        if avg_citations > 3:
            print("✅ Good citation coverage")
        else:
            print("⚠️  Citation coverage could be improved")
        
        return results
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """Main test function."""
    print("🚀 Response Quality Test Suite")
    print("Testing improvements made on June 2, 2025")
    print("Changes: 5→12 docs, simple_summarize→tree_summarize, quality filtering")
    print()
    
    results = test_response_quality()
    
    if results:
        print(f"\n✅ Test completed successfully!")
        print(f"📊 Tested {len(results)} queries with improved RAG service")
    else:
        print(f"\n❌ Test failed!")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
