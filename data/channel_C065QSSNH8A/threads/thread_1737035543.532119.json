{"thread_ts": "**********.532119", "channel_id": "C065QSSNH8A", "reply_count": 33, "replies": [{"ts": "1737035721.168089", "text": "No you had reset the ratings", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "7M27S", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No you had reset the ratings"}]}]}]}, {"ts": "1737036190.421919", "text": "Does doing that wipe the previous data? So if there WAS a matching name/# that didn't change, we'd still have to reupload it all again?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "fhM8Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Does doing that wipe the previous data? So if there WAS a matching name/# that didn't change, we'd still have to reupload it all again?"}]}]}]}, {"ts": "1737036225.787279", "text": "For example if 'meets expectations' is in the old rating structure and the new one, it wouldn't apply the new ratings to the name from before?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "12/d7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For example if 'meets expectations' is in the old rating structure and the new one, it wouldn't apply the new ratings to the name from before?"}]}]}]}, {"ts": "1737036415.972949", "text": "Since the ratings were deleted and they were of type range not fixed values, values were reset", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "1wjrK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Since the ratings were deleted and they were of type range not fixed values, values were reset"}]}]}]}, {"ts": "1737036499.492769", "text": "Ok - second part of my question is, shouldn't it still be able to let you SET a budget? That step is before you set up any ratings decisions. It should still be able to sum the salaries and allow a budget to be set.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "paIum", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok - second part of my question is, shouldn't it still be able to let you SET a budget? That step is before you set up any ratings decisions. It should still be able to sum the salaries and allow a budget to be set."}]}]}]}, {"ts": "1737036520.490779", "text": "I could understand it not being able to calculate a variance, but not that it can't even sum the salaries for a high level budget.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "XtXVb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I could understand it not being able to calculate a variance, but not that it can't even sum the salaries for a high level budget."}]}]}]}, {"ts": "1737036541.103009", "text": "At that step we may not even choose to use performance metrics", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "g1CN3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "At that step we may not even choose to use performance metrics"}]}]}]}, {"ts": "1737036564.948619", "text": "It does, only calculated will be 0", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "Eck6g", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It does, only calculated will be 0"}]}]}]}, {"ts": "1737036593.166259", "text": "", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "files": [{"id": "F088S5D1AFQ", "created": 1737036590, "timestamp": 1737036590, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 66659, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F088S5D1AFQ/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F088S5D1AFQ/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F088S5D1AFQ-a448edc9e3/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F088S5D1AFQ-a448edc9e3/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F088S5D1AFQ-a448edc9e3/image_360.png", "thumb_360_w": 360, "thumb_360_h": 204, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F088S5D1AFQ-a448edc9e3/image_480.png", "thumb_480_w": 480, "thumb_480_h": 272, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F088S5D1AFQ-a448edc9e3/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F088S5D1AFQ-a448edc9e3/image_720.png", "thumb_720_w": 720, "thumb_720_h": 408, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F088S5D1AFQ-a448edc9e3/image_800.png", "thumb_800_w": 800, "thumb_800_h": 453, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F088S5D1AFQ-a448edc9e3/image_960.png", "thumb_960_w": 960, "thumb_960_h": 544, "original_w": 988, "original_h": 560, "thumb_tiny": "AwAbADDS5x1pDnPU0fl+dKKADB9aMH1oPWk79vzoAdRSGloAjaRFOGdQfQ0edFjPmLj60PBG7bmXJ+tJ9mixjZ+pp6C1HqwcZVgR7UuDmkRFjXagwKdSGGKKKKAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F088S5D1AFQ/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F088S5D1AFQ-83be12af7e", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}]}, {"ts": "1737036605.343939", "text": "If not will check and get back, I don’t remember the expected behaviour exactly. It’s always confusing ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "3SCAo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If not will check and get back, I "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " remember the expected behaviour exactly. "}, {"type": "text", "text": "It’s"}, {"type": "text", "text": " always confusing "}]}]}]}, {"ts": "1737036617.702849", "text": "Here. This should not be at all impacted by performance. It's only in the step AFTER this we designate if we're using performance metrics or not.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "qFGM/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here. This should not be at all impacted by performance. It's only in the step AFTER this we designate if we're using performance metrics or not."}]}]}]}, {"ts": "1737036641.388849", "text": "I still think this part at least is a bug", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "hTF5U", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I still think this part at least is a bug"}]}]}]}, {"ts": "1737036652.951789", "text": "Ok. Will take a look ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "XBtb9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. Will take a look "}]}]}]}, {"ts": "1737036662.865719", "text": "And the allocations page is all 0 except for the top line which shoul also not depend on performance ratings", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "wSKoJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And the allocations page is all 0 except for the top line which shoul also not depend on performance ratings"}]}]}]}, {"ts": "**********.580749", "text": "Ok thank you", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "aO5Qy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok thank you"}]}]}]}, {"ts": "**********.599859", "text": "I have 3 calls today to get going on all these accounts so I want to make sure i know whats going on", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "YXAXh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have 3 calls today to get going on all these accounts so I want to make sure i know whats going on"}]}]}]}, {"ts": "**********.483579", "text": "Sure. Will check. ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "21G+K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure. Will check. "}]}]}]}, {"ts": "**********.418899", "text": "OK I FiGURED THIS OUT", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "uGr5a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OK I FiGURED THIS OUT"}]}]}]}, {"ts": "**********.818739", "text": "What’s the issue?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "cfZQL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "What’s"}, {"type": "text", "text": " the issue"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1737037500.948409", "text": "not a bug, or maybe just a weird one. the data refresh from curana came over with no salaries. so the fact that they are empty would indeed make sense.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "F/w+L", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "not a bug, or maybe just a weird one. the data refresh from curana came over with no salaries. so the fact that they are empty would indeed make sense."}]}]}]}, {"ts": "1737037514.491439", "text": "what doesn't make sense is that i published the cycle and it didn't change any of the cycle data", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "cSf9s", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "what doesn't make sense is that i published the cycle and it didn't change any of the cycle data"}]}]}]}, {"ts": "1737037523.565389", "text": "inside the cycle all those people still have salaries", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "Q4i8i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "inside the cycle all those people still have salaries"}]}]}]}, {"ts": "1737037539.984549", "text": "they still have compa ratios and paybands and salaries", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "BpEDk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "they still have compa ratios and paybands and salaries"}]}]}]}, {"ts": "1737037550.334769", "text": "even though the data update wiped them all out, it didn't touch the active cycle", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "NTodk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "even though the data update wiped them all out, it didn't touch the active cycle"}]}]}]}, {"ts": "1737037556.207139", "text": "even after publishing", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "dKOSF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "even after publishing"}]}]}]}, {"ts": "1737037627.740349", "text": "That’s interesting. System was never tested for this scenario. I guess non zero value will overwrite.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "l9NRS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That’s"}, {"type": "text", "text": " interesting. System was never tested for this scenario. I guess non zero value will overwrite."}]}]}]}, {"ts": "1737037669.674499", "text": "<PERSON><PERSON>, and they all have budgets like they did before", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "q2CPZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON>, and they all have budgets like they did before"}]}]}]}, {"ts": "1737037688.457079", "text": "So i think none of those changes 'took' for the cycle, or maby not on the front end", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "YFmXe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So i think none of those changes 'took' for the cycle, or maby not on the front end"}]}]}]}, {"ts": "1737037721.722049", "text": "That could maybe be why the market adjustment filter is not functional? If the systems sees no compa ratio on the back end even if it shows in fe?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "MqEaN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That could maybe be why the market adjustment filter is not functional? If the systems sees no compa ratio on the back end even if it shows in fe?"}]}]}]}, {"ts": "1737037739.328149", "text": "Yes", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "c53D7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes"}]}]}]}, {"ts": "1737037809.365969", "text": "Will you be uploading salaries? That should fix the issue. We will push a fix to handle this edge case tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "GHLxE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will you be uploading salaries? That should fix the issue. We will push a fix to handle this edge case tomorrow."}]}]}]}, {"ts": "1737037909.278889", "text": "Yes I will be, salaries and bonuses and I'll put in some fake performance data", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "NfZsb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes I will be, salaries and bonuses and I'll put in some fake performance data"}]}]}]}, {"ts": "1737037932.456839", "text": ":raised_hands: ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.532119", "blocks": [{"type": "rich_text", "block_id": "3Wg9w", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "raised_hands", "unicode": "1f64c"}, {"type": "text", "text": " "}]}]}]}], "created_at": "2025-05-22T22:00:49.332345"}