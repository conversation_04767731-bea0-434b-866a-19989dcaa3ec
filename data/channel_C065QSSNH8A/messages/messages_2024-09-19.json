{"date": "2024-09-19", "channel_id": "C065QSSNH8A", "message_count": 8, "messages": [{"ts": "1726769942.130109", "text": "Report issue is deeper than I thought and will take time. Will have closure on this tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1726768530.004359", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "WsQJ3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Report issue is deeper than I thought and will take time. Will have closure on this tomorrow."}]}]}]}, {"ts": "1726768530.004359", "text": "<@U07EJ2LP44S> *UPDATE on the issues:*\nThere is some database query breaking the server when you click on compensation cycle reports. Please do not click on reports until we fix the issue.\n*For cycle edit:* We have disabled audit trail for now and this solved the issue and I was able to complete the edit cycle flow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1726768530.004359", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "qffHR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "text", "text": "UPDATE on the issues:", "style": {"bold": true}}, {"type": "text", "text": "\nThere is some database query breaking the server when you click on compensation cycle reports. Please do not click on reports until we fix the issue.\n"}, {"type": "text", "text": "For cycle edit:", "style": {"bold": true}}, {"type": "text", "text": " We have disabled audit trail for now and this solved the issue and I was able to complete the edit cycle flow."}]}]}]}, {"ts": "1726763752.630749", "text": "<PERSON><PERSON><PERSON> still hanging here:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726763752.630749", "reply_count": 4, "files": [{"id": "F07NV8ALAL8", "created": 1726763747, "timestamp": 1726763747, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 335103, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NV8ALAL8/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NV8ALAL8/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_360.png", "thumb_360_w": 360, "thumb_360_h": 220, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_480.png", "thumb_480_w": 480, "thumb_480_h": 293, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_720.png", "thumb_720_w": 720, "thumb_720_h": 439, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_800.png", "thumb_800_w": 800, "thumb_800_h": 488, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_960.png", "thumb_960_w": 960, "thumb_960_h": 586, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NV8ALAL8-6234eb5961/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 625, "original_w": 3038, "original_h": 1854, "thumb_tiny": "AwAdADCa4ZlcAEjjoDUfmOP42/On3I/eD6VFQAvmN/eP50eY398/nTaKAH+Y/wDfb86TzH/vN+dJRQBLc/6wfSoqluDmQfQVEeRQAlFABHU5paAEpaKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NV8ALAL8/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NV8ALAL8-62055d10a4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "rdtBt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> still hanging here:"}]}]}]}, {"ts": "1726761305.360049", "text": "<@U0690EB5JE5> demo env is not loading for me", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RczdC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " demo env is not loading for me"}]}]}]}, {"ts": "1726761197.607629", "text": "<@U07EJ2LP44S> can you pls send me two good looking two adjustments letters created from Stride? Need to send this to a prospect", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726761197.607629", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "8h7WZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can you pls send me two good looking two adjustments letters created from Stride? Need to send this to a prospect"}]}]}]}, {"ts": "**********.171119", "text": "<https://docs.google.com/spreadsheets/d/1LB8R2sohO0uddIo664l2rbY9Wrx4mv-nLnp6bxocJ24/edit?usp=sharing>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.171119", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "9p24Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/spreadsheets/d/1LB8R2sohO0uddIo664l2rbY9Wrx4mv-nLnp6bxocJ24/edit?usp=sharing"}]}]}]}, {"ts": "**********.502009", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> Still having trouble with the demo account. Right now it's hanging up on trying to download a report. Also a 'something went wrong' error on Org view. It eventually loaded.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.502009", "reply_count": 6, "edited": {"user": "U07EJ2LP44S", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "T8kf/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Still having trouble with the demo account. Right now it's hanging up on trying to download a report. Also a 'something went wrong' error on Org view. It eventually loaded."}]}]}]}, {"ts": "**********.885749", "text": "Mayank will be demonstrating HRBP changes today’s call", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "doZrV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Mayank will be demonstrating HRBP changes today’s call"}]}]}]}], "created_at": "2025-05-22T21:35:34.637837"}