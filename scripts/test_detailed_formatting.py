#!/usr/bin/env python3
"""
Test script to verify the improved detailed formatting is working.
"""

import os
import sys
import django
import time

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth.models import User
from apps.search.services.rag_service import RAGService
from apps.core.utils.query_classifier import classify_query
from apps.core.utils.prompt_templates import get_prompt_template


def test_prompt_template():
    """Test that the correct prompt template is being used."""
    print("🧪 Testing Prompt Template Selection")
    print("=" * 50)
    
    query = "list issues reported on Curana"
    
    # Test classification
    classification = classify_query(query)
    print(f"Query: {query}")
    print(f"Classification: {classification['type']} (confidence: {classification['confidence']:.2f})")
    
    # Test template selection
    template = get_prompt_template(query=query, query_type=classification['type'])
    
    # Check if it's using the LIST_ISSUES_TEMPLATE
    if "CRITICAL FORMATTING REQUIREMENTS" in template:
        print("✅ Using enhanced LIST_ISSUES_TEMPLATE")
    else:
        print("❌ Not using enhanced template")
    
    # Show template preview
    print(f"\n📝 Template Preview:")
    print(template[:500] + "...")
    
    return classification['type']


def test_detailed_search():
    """Test the search with improved formatting."""
    print("\n🔍 Testing Detailed Search Response")
    print("=" * 50)
    
    try:
        # Get user
        user = User.objects.get(email='<EMAIL>')
        print(f"✅ Found user: {user.email}")
        
        # Create service
        service = RAGService(tenant_slug='test-tenant', user=user)
        print(f"✅ Created RAG service")
        
        # Test query
        query = "list issues reported on Curana"
        print(f"\n🔍 Query: {query}")
        
        start_time = time.time()
        
        # Execute search with enhanced prompts
        result, docs = service.search(
            query_text=query,
            top_k=15,  # Use more documents for better context
            use_context_aware=True,  # This uses the enhanced prompt templates
            use_hybrid_search=True,
            min_relevance_score=0.1
        )
        
        processing_time = time.time() - start_time
        
        print(f"\n📊 Results:")
        print(f"⏱️  Processing Time: {processing_time:.2f}s")
        print(f"📄 Response Length: {len(result.generated_answer)} characters")
        print(f"🔗 Citations: {len(docs)}")
        
        print(f"\n📝 Generated Response:")
        print("-" * 50)
        print(result.generated_answer)
        print("-" * 50)
        
        # Analyze response quality
        response = result.generated_answer
        
        # Check for detailed formatting indicators
        quality_indicators = {
            "Has bullet points": "•" in response or "-" in response,
            "Has dates": any(month in response for month in ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]),
            "Has detailed descriptions": len(response) > 300,
            "Has structured format": "Issues" in response and ":" in response,
            "Has specific names": "Curana" in response,
            "Has citations": "[" in response and "]" in response
        }
        
        print(f"\n🎯 Quality Analysis:")
        for indicator, present in quality_indicators.items():
            status = "✅" if present else "❌"
            print(f"{status} {indicator}")
        
        # Overall assessment
        passed_checks = sum(quality_indicators.values())
        total_checks = len(quality_indicators)
        quality_score = (passed_checks / total_checks) * 100
        
        print(f"\n📈 Overall Quality Score: {quality_score:.0f}% ({passed_checks}/{total_checks})")
        
        if quality_score >= 80:
            print("🎉 EXCELLENT: Detailed formatting is working well!")
        elif quality_score >= 60:
            print("👍 GOOD: Most formatting requirements are met")
        else:
            print("⚠️  NEEDS IMPROVEMENT: Formatting requirements not fully met")
        
        return result
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """Main test function."""
    print("🚀 Detailed Formatting Test Suite")
    print("Testing enhanced prompt templates for comprehensive responses")
    print()
    
    # Test 1: Prompt template selection
    query_type = test_prompt_template()
    
    # Test 2: Actual search with detailed formatting
    result = test_detailed_search()
    
    if result:
        print(f"\n✅ Test completed successfully!")
        print(f"📊 The enhanced prompt templates are working")
        print(f"🎯 Query was classified as: {query_type}")
        print(f"📝 Response should now include detailed formatting with:")
        print(f"   - Specific dates in human-readable format")
        print(f"   - Detailed descriptions for each issue")
        print(f"   - Names of people and systems")
        print(f"   - Structured bullet point format")
        print(f"   - Comprehensive context and details")
    else:
        print(f"\n❌ Test failed!")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
