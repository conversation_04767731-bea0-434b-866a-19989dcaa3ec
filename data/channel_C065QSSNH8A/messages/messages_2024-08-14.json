{"date": "2024-08-14", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1723657870.847719", "text": "Does anyone know if Alayacare was sent info on the Bamboo integration?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723657870.847719", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "weHaH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Does anyone know if Alayacare was sent info on the Bamboo integration?"}]}]}]}, {"ts": "1723655132.606129", "text": "<@U04DS2MBWP4> Can you cancel the rightway call that was scheduled for tomorrow?", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "done", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "fT7Qd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Can you cancel the rightway call that was scheduled for tomorrow?"}]}]}]}, {"ts": "**********.600149", "text": "<@U07EJ2LP44S> Here are some questions we need clarity on to effectively manage compensation adjustments for hourly employees:, ( some of answers are obvious but lets get confirmation in any case)\n\n\t•\tWhen preparing the compensation budget, should we convert hourly wages to an annualized salary?\n\t•\tIt would be helpful to have an example of how the salary budget is prepared when the employee population includes hourly workers.\n\t•\tHow should hours per week be accounted for in this process?\n\t•\tShould salary adjustments for hourly employees be based on their hourly rate, or should we use the annualized figures?\n        •\tIf the annualized approach is preferred, could you provide a sample conversion formula, if one has been used?\n• Also please confirm hourly wages uses how many decimal digits", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.600149", "reply_count": 3, "edited": {"user": "U04DKEFP1K8", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "B06Jx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Here are some questions we need clarity on to effectively manage compensation adjustments for hourly employees:, ( some of answers are obvious but lets get confirmation in any case)\n\n\t•\tWhen preparing the compensation budget, should we convert hourly wages to an annualized salary?\n\t•\tIt would be helpful to have an example of how the salary budget is prepared when the employee population includes hourly workers.\n\t•\tHow should hours per week be accounted for in this process?\n\t•\tShould salary adjustments for hourly employees be based on their hourly rate, or should we use the annualized figures?\n        •\tIf the annualized approach is preferred, could you provide a sample conversion formula, if one has been used?\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also please confirm hourly wages uses how many decimal digits"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "**********.650789", "text": "<!here> just wrapped up CWA meeting, will start the eng meeting at 905am PT", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "N/7rd", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " just wrapped up CWA meeting, will start the eng meeting at 905am PT"}]}]}]}, {"ts": "**********.416179", "text": "Please record CWA meeting <!here> ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.416179", "reply_count": 1, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "AHHe2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please record CWA meeting "}, {"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}]}]}]}, {"ts": "**********.292099", "text": "<!here> <https://div-energy.stridehr.io/> has been updated to have diversified energy data. Need to enable google login account for all of us. <@U07EJ2LP44S> you can use the local credential which you had created with api.", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "attachments": [{"from_url": "https://div-energy.stridehr.io/", "service_icon": "https://div-energy.stridehr.io/apple-touch-icon.png", "id": 1, "original_url": "https://div-energy.stridehr.io/", "fallback": "Stride", "text": "Web site created using create-react-app", "title": "Stride", "title_link": "https://div-energy.stridehr.io/", "service_name": "div-energy.stridehr.io"}], "blocks": [{"type": "rich_text", "block_id": "y1ei2", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://div-energy.stridehr.io/"}, {"type": "text", "text": " has been updated to have diversified energy data. Need to enable google login account for all of us. "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " you can use the local credential which you had created with api."}]}]}]}, {"ts": "**********.756699", "text": "Anything else I should add?\n---\nHi <PERSON> and <PERSON>,\n\nJust a reminder, I scheduled a call for tomorrow; can you confirm if you are available to join?\n\nWe have reviewed the data and are missing some elements, but we should have a first draft in the application to show you tomorrow. Additionally, we'd like to gather some additional information about your upcoming cycle, including dates. We'd like to cover:\n\n• first data review \n• cycle timeline, including start date and approval date \n• specific cycle components -eg are hourly employees included, exclusions by hire date etc \n• paycor authentication so we can begin the integration process\n\nLooking forward to connecting!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.756699", "reply_count": 8, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "cflG9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Anything else I should add?\n---\nHi <PERSON> and <PERSON>,\n\nJust a reminder, I scheduled a call for tomorrow; can you confirm if you are available to join?\n\nWe have reviewed the data and are missing some elements, but we should have a first draft in the application to show you tomorrow. Additionally, we'd like to gather some additional information about your upcoming cycle, including dates. We'd like to cover:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "first data review "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "cycle timeline, including start date and approval date "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "specific cycle components -eg are hourly employees included, exclusions by hire date etc "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "paycor authentication so we can begin the integration process"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n\nLooking forward to connecting!"}]}]}]}, {"ts": "1723582970.812139", "text": "<https://us06web.zoom.us/j/86735736875?pwd=b6ZOakFcr5WWbeFjSI2as7hasbPGra.1>", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R07H5TV9KG9", "block_id": "JqU8r", "api_decoration_available": false, "call": {"v1": {"id": "R07H5TV9KG9", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1723582971, "active_participants": [], "all_participants": [], "display_id": "867-3573-6875", "join_url": "https://us06web.zoom.us/j/86735736875?pwd=b6ZOakFcr5WWbeFjSI2as7hasbPGra.1", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1723735848, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/86735736875?pwd=b6ZOakFcr5WWbeFjSI2as7hasbPGra.1", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "l/5rb", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://us06web.zoom.us/j/86735736875?pwd=b6ZOakFcr5WWbeFjSI2as7hasbPGra.1"}]}]}]}, {"ts": "1723582959.778789", "text": "2pm meeting <@U04DS2MBWP4>", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3dJ8I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "2pm meeting "}, {"type": "user", "user_id": "U04DS2MBWP4"}]}]}]}], "created_at": "2025-05-22T21:35:34.629367"}