{"thread_ts": "**********.648899", "channel_id": "C065QSSNH8A", "reply_count": 15, "replies": [{"ts": "1737049313.539039", "text": "<@U07EJ2LP44S> I see that you are working with customer and not going try fix anything. Please ping me the file you uploaded. I will take a look.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "TYMw/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I see that you are working with customer and not going try fix anything. Please ping me the file you uploaded. I will take a look."}]}]}]}, {"ts": "**********.872879", "text": "The issue at this point is <PERSON> IS in the account, but not showing up at all in the org view.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "eCpn5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The issue at this point is <PERSON> IS in the account, but not showing up at all in the org view."}]}]}]}, {"ts": "**********.358189", "text": "I can find him in the list but no data", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "r2sBJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can find him in the list but no data"}]}]}]}, {"ts": "**********.359349", "text": "I did not change any of his info when I added the root user, so not sure if that's related", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "uLNa2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I did not change any of his info when I added the root user, so not sure if that's related"}]}]}]}, {"ts": "**********.605989", "text": "Ok let me try now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "nwz70", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok let me try now."}]}]}]}, {"ts": "1737050616.868639", "text": "will take a look during my day.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "yxoM8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will take a look during my day."}]}]}]}, {"ts": "1737050908.872829", "text": "Also not sure if you caught the part that if you republish a cycle, it wipes out the column settings. It should dfeinitely not to that", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "T+9YM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also not sure if you caught the part that if you republish a cycle, it wipes out the column settings. It should dfeinitely not to that"}]}]}]}, {"ts": "1737051065.467309", "text": "Yes I did, I specifically discussed this scenario yesterday. <@U06HN8XDC5A> it’s still happening, please take a look on highest priority ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "YUibt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes I did, I specifically discussed this scenario yesterday. "}, {"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " "}, {"type": "text", "text": "it’s"}, {"type": "text", "text": " still happening, please take a look on highest priority "}]}]}]}, {"ts": "1737051504.419029", "text": "Also I can't upload data and get it to 'take' into the current cycle. I think we're going ot have to delete it and start over with a new test cycle. There are just too many changing variables", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "v0Q04", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also I can't upload data and get it to 'take' into the current cycle. I think we're going ot have to delete it and start over with a new test cycle. There are just too many changing variables"}]}]}]}, {"ts": "1737051673.443849", "text": "I just captured their cycle settings, can this please be deleted asap", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "bFPiJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I just captured their cycle settings, can this please be deleted asap"}]}]}]}, {"ts": "1737079111.714179", "text": "Yes will delete the cycle", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "f4UTL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes will delete the cycle"}]}]}]}, {"ts": "1737079130.473429", "text": "It’s better to create a new cycle", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "bTz4F", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It’s"}, {"type": "text", "text": " better to create"}, {"type": "text", "text": " "}, {"type": "text", "text": "a new cycle"}]}]}]}, {"ts": "1737080635.779779", "text": "<@U07EJ2LP44S> Just reconfirmed publishing cycle again won't wipe column settings. Is there a possibility <PERSON> would have clicked clear all unknowingly? There is no other way the column settings would have gotten wiped out.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.648899", "edited": {"user": "U0690EB5JE5", "ts": "1737081106.000000"}, "blocks": [{"type": "rich_text", "block_id": "AnHQT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Just reconfirmed publishing cycle again won't wipe column settings. Is there a possibility <PERSON> would have clicked clear all unknowingly? There is no other way the column settings would have gotten wiped out."}]}]}]}, {"ts": "1737121174.826919", "text": "Oh, I am 100% sure it happened. I clicked publish and immediately went back in, and it wiped it out. It was me that did it,", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "5f+e4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Oh, I am 100% sure it happened. I clicked publish and immediately went back in, and it wiped it out. It was me that did it,"}]}]}]}, {"ts": "1737121257.903439", "text": "There is no such implementation in the code. I republished multiple times my morning. I will check anyway once more.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.648899", "blocks": [{"type": "rich_text", "block_id": "wmdqe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There is no such implementation in the code. I republished multiple times my morning. I will check anyway once more."}]}]}]}], "created_at": "2025-05-22T22:00:49.331691"}