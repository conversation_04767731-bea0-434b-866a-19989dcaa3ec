{"date": "2023-12-14", "channel_id": "C065QSSNH8A", "message_count": 32, "messages": [{"ts": "1702573400.570549", "text": "<@U0658EW4B8D>  We have started writing blogs. <https://docs.google.com/document/d/1Vw1Rmk-kduGM07R6KF38QSKHCwHfvAfokjyvjBTA56c/edit#heading=h.kamnxapw2bvz|Here> is a link to the first blog. Can you please go through this and add your comments/suggestions to this doc? We will be writing about 3 blogs per month. Also let's discuss the blog/content strategy to make sure we are writing blogs on topics that are most meaningful to customers.\n<@U065H3M6WJV> I would love to get <PERSON>'s take on the the topics that we should be writing blogs on. I might join the call for the first 10 mins just for that", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702573400.570549", "reply_count": 15, "reactions": [{"name": "+1", "users": ["U0658EW4B8D"], "count": 1}], "files": [{"id": "F06A0FC2BLN", "created": 1702573402, "timestamp": 1702573402, "name": "What is Compensation Management?", "title": "What is Compensation Management?", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 154369, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1Vw1Rmk-kduGM07R6KF38QSKHCwHfvAfokjyvjBTA56c", "external_url": "https://docs.google.com/document/d/1Vw1Rmk-kduGM07R6KF38QSKHCwHfvAfokjyvjBTA56c/edit#heading=h.kamnxapw2bvz", "url_private": "https://docs.google.com/document/d/1Vw1Rmk-kduGM07R6KF38QSKHCwHfvAfokjyvjBTA56c/edit#heading=h.kamnxapw2bvz", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1035, "thumb_tiny": "AwAwACXTJxSbhQx9aTj3oAdRSA47GjPsaAFooooARqT8aVqb+NAC59zRn3NHNKM5oAWiiigBCcUmTTiM0mKAEBpQfajFGBQAtFFFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06A0FC2BLN/what_is_compensation_management_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "2jCCw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": "  We have started writing blogs. "}, {"type": "link", "url": "https://docs.google.com/document/d/1Vw1Rmk-kduGM07R6KF38QSKHCwHfvAfokjyvjBTA56c/edit#heading=h.kamnxapw2bvz", "text": "Here"}, {"type": "text", "text": " is a link to the first blog. Can you please go through this and add your comments/suggestions to this doc? We will be writing about 3 blogs per month. Also let's discuss the blog/content strategy to make sure we are writing blogs on topics that are most meaningful to customers.\n"}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I would love to get <PERSON>'s take on the the topics that we should be writing blogs on. I might join the call for the first 10 mins just for that"}]}]}]}, {"ts": "1702573224.319629", "text": "What's the best way to keep a checklist of the customer-specific items? Maybe a separate \"UAT\" file for each one? (I don't think these should be shared directly with the customer though)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702573224.319629", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Nwl0i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "What's the best way to keep a checklist of the customer-specific items? Maybe a separate \"UAT\" file for each one? (I don't think these should be shared directly with the customer though)"}]}]}]}, {"ts": "1702571342.490109", "text": "<@U065H3M6WJV> <@U04DKEFP1K8> I wiil be signing the contract with Merge today. I just put a time on your cal today to discuss the implementation plan. <@U04DKEFP1K8> Can <PERSON><PERSON><PERSON> join the call as well?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702571342.490109", "reply_count": 2, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "SaPAz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I wiil be signing the contract with Me<PERSON> today. I just put a time on your cal today to discuss the implementation plan. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can <PERSON><PERSON><PERSON> join the call as well?"}]}]}]}, {"ts": "1702522520.227789", "text": "<@U065H3M6WJV> I have added <PERSON> and <PERSON> to all advisory meetings during the week of Jan 2nd. What I would love to do is have each of our advisors role play as a customer while <PERSON> will role play as the sales rep to do the customer discovery and go through the sales pitch with them. It will be about 30- 45 min pitch and then we will leave last 15 mins for advisors to give feedback to <PERSON> on what parts of sales pitch and customer discovery resonated with them, so <PERSON> can fine tune his pitch accordingly. Plus it's a great way for <PERSON> to gain familiarity with our target buyers.\n\nCan you please run this by the advisors to make sure they are good with this role play exercise?", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1702522560.000000"}, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "W/Z0P", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I have added <PERSON> and <PERSON> to all advisory meetings during the week of Jan 2nd. What I would love to do is have each of our advisors role play as a customer while <PERSON> will role play as the sales rep to do the customer discovery and go through the sales pitch with them. It will be about 30- 45 min pitch and then we will leave last 15 mins for advisors to give feedback to <PERSON> on what parts of sales pitch and customer discovery resonated with them, so <PERSON> can fine tune his pitch accordingly. Plus it's a great way for <PERSON> to gain familiarity with our target buyers.\n\nCan you please run this by the advisors to make sure they are good with this role play exercise?"}]}]}]}, {"ts": "1702517079.270589", "text": "Team-  I have reorganized the Drive.\n<@U04DKEFP1K8> pls move technical folder to engineering. I am unable to move it.\n<https://drive.google.com/drive/u/0/folders/1P2E5KZS-vfovWHp3L_fLdZqmek1Q8guI>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702517079.270589", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "qKh4h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Team-  I have reorganized the Drive.\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " pls move technical folder to engineering. I am unable to move it.\n"}, {"type": "link", "url": "https://drive.google.com/drive/u/0/folders/1P2E5KZS-vfovWHp3L_fLdZqmek1Q8guI"}]}]}]}, {"ts": "1702516954.943529", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> Here are <https://docs.google.com/presentation/d/1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0/edit#slide=id.p|my recommendations> for how we should adjust the behavior of recommended values &amp; updated calculations as managers are editing in Merit View. We can decide later whether to keep, revise, or remove the \"Reviewed\" state based on feedback from advisors.\n\nPlease let me know if you have any questions or have any trouble understanding these requirements. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702516954.943529", "reply_count": 4, "files": [{"id": "F069W8S9GEA", "created": 1702516957, "timestamp": 1702516957, "name": "Merit View changes, Dec 2023", "title": "Merit View changes, Dec 2023", "mimetype": "application/vnd.google-apps.presentation", "filetype": "gpres", "pretty_type": "Google Slides", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0", "external_url": "https://docs.google.com/presentation/d/1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0/edit#slide=id.p", "url_private": "https://docs.google.com/presentation/d/1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0/edit#slide=id.p", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_360.png", "thumb_360_w": 360, "thumb_360_h": 203, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_480.png", "thumb_480_w": 480, "thumb_480_h": 270, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_720.png", "thumb_720_w": 720, "thumb_720_h": 405, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_800.png", "thumb_800_w": 800, "thumb_800_h": 450, "thumb_tiny": "AwAbADDTooqPMvPC/nQBJRTCZAeAp/GgmTnAU/jQA+io8yZ+6MfWpKACiiigBCAeozQAB0AFLRQAUUUUAf/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F069W8S9GEA/merit_view_changes__dec_2023", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "a0L7j", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Here are "}, {"type": "link", "url": "https://docs.google.com/presentation/d/1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0/edit#slide=id.p", "text": "my recommendations"}, {"type": "text", "text": " for how we should adjust the behavior of recommended values & updated calculations as managers are editing in Merit View. We can decide later whether to keep, revise, or remove the \"Reviewed\" state based on feedback from advisors.\n\nPlease let me know if you have any questions or have any trouble understanding these requirements. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1702513907.482249", "text": "yes", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "IB8ze", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes"}]}]}]}, {"ts": "1702512114.375339", "text": "<@U0658EW4B8D> if there is nothing prefilled, are those fields then 'blank' rather than 'zero'?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NHfms", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " if there is nothing prefilled, are those fields then 'blank' rather than 'zero'?"}]}]}]}, {"ts": "1702512078.651579", "text": "Yes to everything and worth noting that you can manually load values as well, if you do not want to do modeled prefill as you see through the config", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4GnYj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes to everything and worth noting that you can manually load values as well, if you do not want to do modeled prefill as you see through the config"}]}]}]}, {"ts": "1702511356.825889", "text": "ChartHop's <https://www.youtube.com/watch?v=dV3C4eIQ-6w|marketing video >had a screenshot of their config options for guidelines &amp; prefill as well.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702511356.825889", "reply_count": 1, "files": [{"id": "F069VV1M3AA", "created": 1702511310, "timestamp": 1702511310, "name": "ChartHop_Planning_Configuration_Guidelines.png", "title": "ChartHop_Planning_Configuration_Guidelines.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 788020, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F069VV1M3AA/charthop_planning_configuration_guidelines.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F069VV1M3AA/download/charthop_planning_configuration_guidelines.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_360.png", "thumb_360_w": 360, "thumb_360_h": 212, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_480.png", "thumb_480_w": 480, "thumb_480_h": 283, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_720.png", "thumb_720_w": 720, "thumb_720_h": 425, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_800.png", "thumb_800_w": 800, "thumb_800_h": 472, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_960.png", "thumb_960_w": 960, "thumb_960_h": 566, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 604, "original_w": 2224, "original_h": 1312, "thumb_tiny": "AwAcADCbTuYGz/fP8hVvaB2qppp/cN/vn+lXKb3ATGegH5Uu36flRS0gEx7D8qY8SkbucgHvUlI/3G+lMClppxA3++atl6o2fyxED1/pU5JoYE276flS7z7VBk0ZNICffSO/yN9KhyaRidp+lAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F069VV1M3AA/charthop_planning_configuration_guidelines.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F069VV1M3AA-4394189595", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "attachments": [{"from_url": "https://www.youtube.com/watch?v=dV3C4eIQ-6w", "service_icon": "https://a.slack-edge.com/80588/img/unfurl_icons/youtube.png", "thumb_url": "https://i.ytimg.com/vi/dV3C4eIQ-6w/hqdefault.jpg", "thumb_width": 480, "thumb_height": 360, "video_html": "<iframe width=\"400\" height=\"225\" src=\"https://www.youtube.com/embed/dV3C4eIQ-6w?feature=oembed&autoplay=1&iv_load_policy=3\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" allowfullscreen title=\"Easy Way to Streamline Compensation Reviews with ChartHop\"></iframe>", "video_html_width": 400, "video_html_height": 225, "id": 1, "original_url": "https://www.youtube.com/watch?v=dV3C4eIQ-6w", "fallback": "YouTube Video: Easy Way to Streamline Compensation Reviews with ChartHop", "title": "Easy Way to Streamline Compensation Reviews with ChartHop", "title_link": "https://www.youtube.com/watch?v=dV3C4eIQ-6w", "author_name": "ChartHop", "author_link": "https://www.youtube.com/@ChartHop", "service_name": "YouTube", "service_url": "https://www.youtube.com/"}], "blocks": [{"type": "rich_text", "block_id": "cxnRK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ChartHop's "}, {"type": "link", "url": "https://www.youtube.com/watch?v=dV3C4eIQ-6w", "text": "marketing video "}, {"type": "text", "text": "had a screenshot of their config options for guidelines & prefill as well."}]}]}]}, {"ts": "1702507934.156229", "text": "I do think they need to be able to see multiple rows and a good amount of data. You can see <PERSON><PERSON> has a way for the user to resize the budget area so that they can fit more rows on the screen, for example.", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "yP45Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I do think they need to be able to see multiple rows and a good amount of data. You can see <PERSON><PERSON> has a way for the user to resize the budget area so that they can fit more rows on the screen, for example."}]}]}]}, {"ts": "1702507880.385299", "text": "I think the ease of use is directly proportional to amount of data they can see to make it easier to make the decisions without having to scroll and multiple clicks.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3q7A1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think the ease of use is directly proportional to amount of data they can see to make it easier to make the decisions without having to scroll and multiple clicks."}]}]}]}, {"ts": "1702507784.627869", "text": "Although, I think ease-of-use is more important than compactness of data.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NYD4j", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Although, I think ease-of-use is more important than compactness of data."}]}]}]}, {"ts": "1702507739.733679", "text": "Their format adds a lot of height, since it is there for each row.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9iQnz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Their format adds a lot of height, since it is there for each row."}]}]}]}, {"ts": "1702507725.758089", "text": "yes", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IB8ze", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes"}]}]}]}, {"ts": "1702507716.678799", "text": "If the HR owner doesn't opt for \"prefill\", perhaps the \"recommendations\" still show above the field.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sN+Dd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If the HR owner doesn't opt for \"prefill\", perhaps the \"recommendations\" still show above the field."}]}]}]}, {"ts": "1702507701.270769", "text": "Yep, that's why I think their \"prefill\" setting is \"real\" values", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VhEjZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yep, that's why I think their \"prefill\" setting is \"real\" values"}]}]}]}, {"ts": "1702507682.460809", "text": "this view shows prefilled with the recommended values as both the recommended and the filled numbers are same", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LczXe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "this view shows prefilled with the recommended values as both the recommended and the filled numbers are same"}]}]}]}, {"ts": "1702507604.259159", "text": "probably. I think we can minimize this confusion with a tooltip and/or popup and and just show the real values for the MVP purposes. Once we have actual customers start using it, lets take it again then", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702507604.259159", "reply_count": 2, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ulL6s", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "probably. I think we can minimize this confusion with a tooltip and/or popup and and just show the real values for the MVP purposes. Once we have actual customers start using it, lets take it again then"}]}]}]}, {"ts": "1702507468.425839", "text": "Interesting! In that case (guessing from the screenshots above) it seems like they are prefilled as \"real\" values and not _just_ suggestions.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PcBMf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Interesting! In that case (guessing from the screenshots above) it seems like they are prefilled as \"real\" values and not "}, {"type": "text", "text": "just ", "style": {"italic": true}}, {"type": "text", "text": "suggestions."}]}]}]}, {"ts": "1702507342.575709", "text": "This configurations page has as option to prefill recommendations\n<https://support.pave.com/hc/en-us/articles/6739129295895-Recommendation-Logic>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F069ZDADMV3", "created": 1702507314, "timestamp": 1702507314, "name": "Screenshot 2023-12-13 at 2.41.27 PM.png", "title": "Screenshot 2023-12-13 at 2.41.27 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 928718, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F069ZDADMV3/screenshot_2023-12-13_at_2.41.27___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F069ZDADMV3/download/screenshot_2023-12-13_at_2.41.27___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 214, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 285, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 427, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 475, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 570, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 608, "original_w": 3782, "original_h": 2244, "thumb_tiny": "AwAcADDTxQelFB6UAICaWkFLQAUUUUAFB6UUhoAWim8+po59TQA6imgn1oIDHnr6jrQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F069ZDADMV3/screenshot_2023-12-13_at_2.41.27___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F069ZDADMV3-a7da591c65", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "1oz7V", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This configurations page has as option to prefill recommendations\n"}, {"type": "link", "url": "https://support.pave.com/hc/en-us/articles/6739129295895-Recommendation-Logic"}]}]}]}, {"ts": "1702505435.216609", "text": "Another, probably older design, shows the concept of an \"empty\" field with an extra highlight. This would be different from entering $0.00 intentionally as the value, I think.", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F069Z98189K", "created": 1702505404, "timestamp": 1702505404, "name": "Pave_screenshot_older.png", "title": "Pave_screenshot_older.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 1513768, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F069Z98189K/pave_screenshot_older.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F069Z98189K/download/pave_screenshot_older.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_360.png", "thumb_360_w": 360, "thumb_360_h": 234, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_480.png", "thumb_480_w": 480, "thumb_480_h": 311, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_720.png", "thumb_720_w": 720, "thumb_720_h": 467, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_800.png", "thumb_800_w": 800, "thumb_800_h": 519, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_960.png", "thumb_960_w": 960, "thumb_960_h": 623, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 664, "original_w": 2910, "original_h": 1888, "thumb_tiny": "AwAfADCeyKm2Unjk9qsgJ/smq+nH/RF57mrW4etN7gJhP9mj5P8AZoDe9LkUgGkIOcD8qMIey/lS7hS5HrQBRsoY5LVTIqk5PX61Y+yW/wDzyX8qj07/AI9F+pq3Te4iD7LBj/Vr+VJ9kt/+ea1P2oFIZD9kt/8AnmtH2S3/AOeS1PRQB//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F069Z98189K/pave_screenshot_older.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F069Z98189K-f9bd8f8145", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "/9x/t", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Another, probably older design, shows the concept of an \"empty\" field with an extra highlight. This would be different from entering $0.00 intentionally as the value, I think."}]}]}]}, {"ts": "1702505211.270969", "text": "From this screen, you can see they have a recommendation above the field -- less clear whether it was also prefilled, but if so, it seems that it has been applied against the budget too :thinking_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702505211.270969", "reply_count": 2, "files": [{"id": "F069Z8QJ2NR", "mode": "tombstone"}], "blocks": [{"type": "rich_text", "block_id": "O0vFE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "From this screen, you can see they have a recommendation above the field -- less clear whether it was also prefilled, but if so, it seems that it has been applied against the budget too "}, {"type": "emoji", "name": "thinking_face", "unicode": "1f914"}]}]}]}, {"ts": "1702504798.130029", "text": "I think pave does that", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "g26/5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think pave does that"}]}]}]}, {"ts": "1702504775.501879", "text": "I don't know if any competitor actually pre-fills values. My assumption is that they would only show company recommended ranges, at most. But let me see if we can find any examples in practice..", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0Di6O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I don't know if any competitor actually pre-fills values. My assumption is that they would only show company recommended ranges, at most. But let me see if we can find any examples in practice.."}]}]}]}, {"ts": "1702504651.783109", "text": "I am wondering how our competitors are solving for this", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VBVK1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am wondering how our competitors are solving for this"}]}]}]}, {"ts": "1702504619.187999", "text": "could we see how the lighter shade of the purple color would look like the dark purple looks like the real values. The grey color option doesn’t look bad at all especially if the managers will see a pop-up on the first login that your HR has already pre-populated the recommended options for you to consider.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702504619.187999", "reply_count": 3, "reactions": [{"name": "thinking_face", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TTYm+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "could we see how the lighter shade of the purple color would look like the dark purple looks like the real values. The grey color option doesn’t look bad at all especially if the managers will see a pop-up on the first login that your HR has already pre-populated the recommended options for you to consider."}]}]}]}, {"ts": "1702503428.913209", "text": "Here is another option I tried, in case the gray looked too much like an 'inactive' state. The purple color would ideally be a lighter shade; this was just what was available in the browser page. So again, just think of this as \"illustration\" and not entirely accurate. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F06A2296DGA", "created": 1702503385, "timestamp": 1702503385, "name": "Recommendations_Prefill_Purple.png", "title": "Recommendations_Prefill_Purple.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 846094, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06A2296DGA/recommendations_prefill_purple.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06A2296DGA/download/recommendations_prefill_purple.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_360.png", "thumb_360_w": 360, "thumb_360_h": 233, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_480.png", "thumb_480_w": 480, "thumb_480_h": 311, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_720.png", "thumb_720_w": 720, "thumb_720_h": 466, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_800.png", "thumb_800_w": 800, "thumb_800_h": 518, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_960.png", "thumb_960_w": 960, "thumb_960_h": 622, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 663, "original_w": 3732, "original_h": 2418, "thumb_tiny": "AwAfADDS9aAB2pBSjpQAfjS0mKOfagAJxz1oBzyRRRQAUCigUAFLiiigBKKDS0Af/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06A2296DGA/recommendations_prefill_purple.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06A2296DGA-fd9d02f948", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06A229KKHQ", "created": 1702503390, "timestamp": 1702503390, "name": "Recommendations_Prefill_Purple_WithEdits.png", "title": "Recommendations_Prefill_Purple_WithEdits.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 843536, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06A229KKHQ/recommendations_prefill_purple_withedits.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06A229KKHQ/download/recommendations_prefill_purple_withedits.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_360.png", "thumb_360_w": 360, "thumb_360_h": 233, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_480.png", "thumb_480_w": 480, "thumb_480_h": 310, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_720.png", "thumb_720_w": 720, "thumb_720_h": 466, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_800.png", "thumb_800_w": 800, "thumb_800_h": 517, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_960.png", "thumb_960_w": 960, "thumb_960_h": 621, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 662, "original_w": 3746, "original_h": 2422, "thumb_tiny": "AwAfADDS65oAHakHelHSgA/GlpMUc+1AATgZ6/SgHPJFFFABigUUCgAxS4oooASiiloA/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06A229KKHQ/recommendations_prefill_purple_withedits.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06A229KKHQ-7b1f68278c", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "8U3Fv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is another option I tried, in case the gray looked too much like an 'inactive' state. The purple color would ideally be a lighter shade; this was just what was available in the browser page. So again, just think of this as \"illustration\" and not entirely accurate. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1702503252.586979", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> After our working session, I tried making some \"illustrative mockups\" with the current Compiify, just working with what I could adjust in the browser itself.\n\nHere's an example of what it could look like if recommended values were prefilled, but not automatically applied to the budget until the manager actually edits them. (See if you can tell the difference?)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702503252.586979", "reply_count": 1, "files": [{"id": "F06AQQTKP9N", "created": 1702503242, "timestamp": 1702503242, "name": "Recommendations_Prefill_Gray.png", "title": "Recommendations_Prefill_Gray.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 847266, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06AQQTKP9N/recommendations_prefill_gray.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06AQQTKP9N/download/recommendations_prefill_gray.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_360.png", "thumb_360_w": 360, "thumb_360_h": 235, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_480.png", "thumb_480_w": 480, "thumb_480_h": 313, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_720.png", "thumb_720_w": 720, "thumb_720_h": 470, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_800.png", "thumb_800_w": 800, "thumb_800_h": 522, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_960.png", "thumb_960_w": 960, "thumb_960_h": 626, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 668, "original_w": 3736, "original_h": 2438, "thumb_tiny": "AwAfADDS65oxSClHSgA/GlpMUc+1AATgZ6/SgHPJFFFABigUUCgAxS4oooASlpKWgD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06AQQTKP9N/recommendations_prefill_gray.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06AQQTKP9N-f79ef17629", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06AQQU14QG", "created": 1702503246, "timestamp": 1702503246, "name": "Recommendations_Prefill_Gray_WithEdits.png", "title": "Recommendations_Prefill_Gray_WithEdits.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 835066, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06AQQU14QG/recommendations_prefill_gray_withedits.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06AQQU14QG/download/recommendations_prefill_gray_withedits.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_360.png", "thumb_360_w": 360, "thumb_360_h": 232, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_480.png", "thumb_480_w": 480, "thumb_480_h": 309, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_720.png", "thumb_720_w": 720, "thumb_720_h": 464, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_800.png", "thumb_800_w": 800, "thumb_800_h": 515, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_960.png", "thumb_960_w": 960, "thumb_960_h": 618, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 659, "original_w": 3728, "original_h": 2400, "thumb_tiny": "AwAeADDS9aAB2pBSjp1oAPxpfxpKPxFAB79aAc8kUUUAFAooFAC0UUUAIaWkNLQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06AQQU14QG/recommendations_prefill_gray_withedits.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06AQQU14QG-eeeba13790", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "wacyq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " After our working session, I tried making some \"illustrative mockups\" with the current Compiify, just working with what I could adjust in the browser itself.\n\nHere's an example of what it could look like if recommended values were prefilled, but not automatically applied to the budget until the manager actually edits them. (See if you can tell the difference?)"}]}]}]}, {"ts": "1702501207.127109", "text": "Ah okk", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "uzjWT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ah okk"}]}]}]}, {"ts": "1702501168.903989", "text": "Unfortunately <PERSON> had a client meeting that ran way over so we didn't meet today - trying to reschedule for Friday", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "k5Fwt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Unfortunately <PERSON> had a client meeting that ran way over so we didn't meet today - trying to reschedule for Friday"}]}]}]}, {"ts": "1702498372.512989", "text": "Here's the recording of our \"usability session\" with <PERSON> :slightly_smiling_face:\n\n<https://us06web.zoom.us/rec/share/8XD3BwWDIo88YYFwWeIzn4pm7q9cCbh_-axjL_iPeXa_Ofp996VOhi_dvj4-L9wN.bXP-fxuc8d9qz6jh>\nPasscode: *y!x#1*D6*", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TIUgv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here's the recording of our \"usability session\" with <PERSON> "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": "\n\n"}, {"type": "link", "url": "https://us06web.zoom.us/rec/share/8XD3BwWDIo88YYFwWeIzn4pm7q9cCbh_-axjL_iPeXa_Ofp996VOhi_dvj4-L9wN.bXP-fxuc8d9qz6jh"}, {"type": "text", "text": "\nPasscode: "}, {"type": "text", "text": "y!x#1*D6", "style": {"bold": true}}]}]}]}], "created_at": "2025-05-22T21:35:34.566102"}