#!/usr/bin/env python3
"""
Test script to reproduce and verify the CSRF issue fix.
"""

import requests
import time
from bs4 import BeautifulSoup
import sys

def test_csrf_issue():
    """Test the CSRF issue with the search endpoint."""
    base_url = "http://localhost:8000"

    # Create a session to maintain cookies
    session = requests.Session()

    print("🔍 Testing CSRF issue reproduction...")

    # Step 1: Login first to get authenticated session
    try:
        print("\n1. Logging in to get authenticated session...")
        login_page_response = session.get(f"{base_url}/accounts/login/")
        print(f"   Login page status: {login_page_response.status_code}")

        if login_page_response.status_code != 200:
            print(f"❌ Failed to access login page: {login_page_response.status_code}")
            return False

        # Parse HTML to extract CSRF token for login
        soup = BeautifulSoup(login_page_response.content, 'html.parser')
        csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
        if not csrf_input:
            print("   ❌ No CSRF token found in login form")
            return False

        login_csrf_token = csrf_input.get('value')
        print(f"   ✅ Found login CSRF token: {login_csrf_token[:20]}...")

        # Submit login form
        login_data = {
            'username': 'testuser',
            'password': 'testpass123',
            'csrfmiddlewaretoken': login_csrf_token
        }

        login_response = session.post(f"{base_url}/accounts/login/", data=login_data, allow_redirects=False)
        print(f"   Login response status: {login_response.status_code}")

        if login_response.status_code == 302:
            print("   ✅ Successfully logged in")
        else:
            print(f"   ❌ Login failed: {login_response.status_code}")
            print(f"   Response content: {login_response.text[:500]}...")
            return False

    except Exception as e:
        print(f"❌ Error during login: {e}")
        return False

    # Step 2: Try to access the search page to get CSRF token
    try:
        print("\n2. Getting search page to extract CSRF token...")
        search_page_response = session.get(f"{base_url}/search/")
        print(f"   Search page status: {search_page_response.status_code}")

        if search_page_response.status_code != 200:
            print(f"❌ Failed to access search page: {search_page_response.status_code}")
            return False

        # Parse HTML to extract CSRF token
        soup = BeautifulSoup(search_page_response.content, 'html.parser')
        csrf_token = None

        # Look for CSRF token in form
        csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
        if csrf_input:
            csrf_token = csrf_input.get('value')
            print(f"   ✅ Found CSRF token: {csrf_token[:20]}...")
        else:
            print("   ❌ No CSRF token found in form")
            return False

    except Exception as e:
        print(f"❌ Error accessing search page: {e}")
        return False

    # Step 3: Test POST without CSRF token (should fail)
    print("\n3. Testing POST without CSRF token (should fail)...")
    try:
        response = session.post(
            f"{base_url}/search/query/",
            data={"query": "test query"},
            allow_redirects=False
        )
        print(f"   Status without CSRF: {response.status_code}")
        if response.status_code == 403:
            print("   ✅ Correctly rejected request without CSRF token")
        else:
            print(f"   ⚠️  Unexpected status code: {response.status_code}")

    except Exception as e:
        print(f"   ❌ Error in POST without CSRF: {e}")

    # Step 4: Test POST with CSRF token (should work)
    print("\n4. Testing POST with CSRF token (should work)...")
    try:
        response = session.post(
            f"{base_url}/search/query/",
            data={
                "query": "test query about code",
                "csrfmiddlewaretoken": csrf_token
            },
            allow_redirects=False
        )
        print(f"   Status with CSRF: {response.status_code}")

        if response.status_code == 200:
            print("   ✅ Successfully processed request with CSRF token")
            return True
        elif response.status_code == 302:
            print("   ✅ Redirected (likely successful, check redirect location)")
            print(f"   Redirect location: {response.headers.get('Location', 'Not specified')}")
            return True
        else:
            print(f"   ❌ Unexpected status code: {response.status_code}")
            print(f"   Response content: {response.text[:500]}...")
            return False

    except Exception as e:
        print(f"   ❌ Error in POST with CSRF: {e}")
        return False

def test_with_headless_browser():
    """Test using selenium for more realistic browser behavior."""
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.chrome.options import Options

        print("\n🌐 Testing with headless browser...")

        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")

        driver = webdriver.Chrome(options=chrome_options)

        try:
            # Step 1: Login first
            driver.get("http://localhost:8000/accounts/login/")
            print("   ✅ Loaded login page")

            wait = WebDriverWait(driver, 10)
            username_field = wait.until(EC.presence_of_element_located((By.NAME, "username")))
            password_field = driver.find_element(By.NAME, "password")
            login_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")

            # Fill login form
            username_field.send_keys("testuser")
            password_field.send_keys("testpass123")
            print("   ✅ Filled login credentials")

            # Submit login
            login_button.click()
            print("   ✅ Submitted login form")

            # Wait for redirect after login
            time.sleep(2)

            # Step 2: Navigate to search page
            driver.get("http://localhost:8000/search/")
            print("   ✅ Loaded search page")

            # Wait for form to be present
            wait = WebDriverWait(driver, 10)
            search_form = wait.until(EC.presence_of_element_located((By.ID, "searchForm")))
            print("   ✅ Found search form")

            # Find query input and submit button
            query_input = driver.find_element(By.NAME, "query")
            submit_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")

            # Enter test query
            query_input.send_keys("test query about code")
            print("   ✅ Entered test query")

            # Submit form
            submit_button.click()
            print("   ✅ Submitted form")

            # Wait for response (either success page or error)
            time.sleep(5)

            current_url = driver.current_url
            page_source = driver.page_source

            print(f"   Current URL: {current_url}")

            # Check if we got a CSRF error
            if "CSRF verification failed" in page_source:
                print("   ❌ CSRF verification failed")
                return False
            elif "search/query" in current_url or "results" in page_source.lower() or "search_results" in page_source:
                print("   ✅ Successfully submitted search query")
                return True
            else:
                print("   ⚠️  Unexpected page content")
                print(f"   Page title: {driver.title}")
                return False

        finally:
            driver.quit()

    except ImportError:
        print("   ⚠️  Selenium not available, skipping browser test")
        return None
    except Exception as e:
        print(f"   ❌ Browser test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 CSRF Issue Test Script")
    print("=" * 50)

    # Test with requests
    requests_success = test_csrf_issue()

    # Test with browser
    browser_success = test_with_headless_browser()

    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Requests test: {'✅ PASS' if requests_success else '❌ FAIL'}")
    if browser_success is not None:
        print(f"   Browser test: {'✅ PASS' if browser_success else '❌ FAIL'}")
    else:
        print("   Browser test: ⚠️  SKIPPED (selenium not available)")

    if requests_success and (browser_success is None or browser_success):
        print("\n🎉 All tests passed! CSRF issue is resolved.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. CSRF issue needs attention.")
        sys.exit(1)
