{"date": "2025-01-29", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1738166889.007509", "text": "Ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PfDzz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok"}]}]}]}, {"ts": "1738166593.755499", "text": "<@U07M6QKHUC9> Adjustment letter set up is tested locally for demo. But I am seeing some issue in ENV and its taking time. Should be definitely ready tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738166593.755499", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1738166611.000000"}, "blocks": [{"type": "rich_text", "block_id": "V1QbL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Adjustment letter set up is tested locally for demo. But I am seeing some issue in ENV and its taking time. Should be definitely ready tomorrow."}]}]}]}, {"ts": "1738165831.896369", "text": "<@U0690EB5JE5> I need to remove all variable from employees in test.stridhr. I tried but it's failing. Help?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "reply_count": 24, "edited": {"user": "U07EJ2LP44S", "ts": "1738165858.000000"}, "blocks": [{"type": "rich_text", "block_id": "Y90eO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I need to remove all variable from employees in test.stridhr. I tried but it's failing. Help?"}]}]}]}, {"ts": "1738129745.954719", "text": "Resharing .VG Recommenders login enabled.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737996549.021619", "subtype": "thread_broadcast", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Lj5E+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Resharing .VG Recommenders login enabled."}]}]}]}, {"ts": "1738125805.101719", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> Can we stop, DGOC and Alayacare ENVs?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738125805.101719", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "ST24z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Can we stop, DGOC and Alayacare ENVs?"}]}]}]}, {"ts": "1738090837.658679", "text": "Check your emails for what <PERSON><PERSON><PERSON> is trying to do, and we can see if there's a workaround for what they want.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738090837.658679", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "DOmH+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Check your emails for what <PERSON><PERSON><PERSON> is trying to do, and we can see if there's a workaround for what they want."}]}]}]}, {"ts": "1738089916.651459", "text": "<@U0690EB5JE5> Can't get these deletes out (I did try just 2 columns but it's kicking back errors). We are trying to delete anyone hired after 8/31/24.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738089916.651459", "reply_count": 4, "files": [{"id": "F08A8PW3PT9", "created": 1738089849, "timestamp": 1738089849, "name": "CuranaDeletesbyHireDate.csv", "title": "CuranaDeletesbyHireDate.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1177, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08A8PW3PT9/curanadeletesbyhiredate.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08A8PW3PT9/download/curanadeletesbyhiredate.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08A8PW3PT9/curanadeletesbyhiredate.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08A8PW3PT9-57f9107eda", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08A8PW3PT9/curanadeletesbyhiredate.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *\r\nD,4412,STEPHA<PERSON><PERSON>,ST THOMAS,STEPHA<PERSON><PERSON> ST THOMAS,<EMAIL>,F,10/2/24\r\nD,4347,TIA<PERSON>,M<PERSON><PERSON><PERSON>D,TIANA MORELAND,<EMAIL>,F,9/11/24\r\nD,4587,PATRICIA,RUSHMAN,PATRICIA RUSHMAN,<EMAIL>,F,12/3/24\r\nD,4851,SARA<PERSON>,INGISON,SARAH INGISON,<EMAIL>,F,11/5/24\r\nD,4497,TIMOTHY,PURI,TIMOTHY PURI,<EMAIL>,M,9/30/24\r\nD,4483,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<EMAIL>,<PERSON>,10/9/24\r\nD,4709,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> MEDITZ,<EMAIL>,<PERSON>,1/6/25\r\nD,2867,<PERSON><PERSON><PERSON>,<PERSON>BOTT,MEGAN ABBOTT,<EMAIL>,F,12/3/24\r\nD,197,ANGELA,THOMPS<PERSON>,ANGELA THOMPS<PERSON>,<EMAIL>,F,10/16/24\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4412</div><div class=\"cm-col\">STEPHANIE</div><div class=\"cm-col\">ST THOMAS</div><div class=\"cm-col\">STEPHANIE ST THOMAS</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">10/2/24</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4347</div><div class=\"cm-col\">TIANA</div><div class=\"cm-col\">MORELAND</div><div class=\"cm-col\">TIANA MORELAND</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">9/11/24</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4587</div><div class=\"cm-col\">PATRICIA</div><div class=\"cm-col\">RUSHMAN</div><div class=\"cm-col\">PATRICIA RUSHMAN</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">12/3/24</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4851</div><div class=\"cm-col\">SARAH</div><div class=\"cm-col\">INGISON</div><div class=\"cm-col\">SARAH INGISON</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">11/5/24</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4497</div><div class=\"cm-col\">TIMOTHY</div><div class=\"cm-col\">PURI</div><div class=\"cm-col\">TIMOTHY PURI</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">9/30/24</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4483</div><div class=\"cm-col\">KEVIN</div><div class=\"cm-col\">FINKBINER</div><div class=\"cm-col\">KEVIN FINKBINER</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">10/9/24</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">4709</div><div class=\"cm-col\">DAVID</div><div class=\"cm-col\">MEDITZ</div><div class=\"cm-col\">DAVID MEDITZ</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">1/6/25</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">2867</div><div class=\"cm-col\">MEGAN</div><div class=\"cm-col\">ABBOTT</div><div class=\"cm-col\">MEGAN ABBOTT</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">12/3/24</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">197</div><div class=\"cm-col\">ANGELA</div><div class=\"cm-col\">THOMPSON</div><div class=\"cm-col\">ANGELA THOMPSON</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">10/16/24</div></div></div>\n</div>\n", "lines": 14, "lines_more": 4, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "dWLKg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can't get these deletes out (I did try just 2 columns but it's kicking back errors). We are trying to delete anyone hired after 8/31/24."}]}]}]}], "created_at": "2025-05-22T21:35:34.704018"}