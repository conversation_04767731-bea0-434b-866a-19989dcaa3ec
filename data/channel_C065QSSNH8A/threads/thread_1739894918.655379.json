{"thread_ts": "**********.655379", "channel_id": "C065QSSNH8A", "reply_count": 6, "replies": [{"ts": "1739895492.139769", "text": "This shows what is being uploaded. If name is upload name will show up.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.655379", "blocks": [{"type": "rich_text", "block_id": "UolRb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This shows what is being uploaded. If name is upload name will show up."}]}]}]}, {"ts": "1739895562.728889", "text": "If I change rating in the org view it still shows the number though. Can I not make org view changes to the performance rating and have it show the name?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.655379", "blocks": [{"type": "rich_text", "block_id": "opPV8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If I change rating in the org view it still shows the number though. Can I not make org view changes to the performance rating and have it show the name?"}]}]}]}, {"ts": "**********.881119", "text": "Or do I have to have something uploaded to the account first before I can set the rating in the org view?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.655379", "blocks": [{"type": "rich_text", "block_id": "TW/Nu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Or do I have to have something uploaded to the account first before I can set the rating in the org view?"}]}]}]}, {"ts": "**********.584269", "text": "yes, settings.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.655379", "blocks": [{"type": "rich_text", "block_id": "pv/6i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes, settings."}]}]}]}, {"ts": "**********.339849", "text": "Settings are done", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.655379", "blocks": [{"type": "rich_text", "block_id": "Ow+OU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Settings are done"}]}]}]}, {"ts": "**********.563569", "text": "Posted the file to upload to stridedemo in a new post so I can fix this for the curana demo on friday", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.655379", "blocks": [{"type": "rich_text", "block_id": "9HJby", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Posted the file to upload to stridedemo in a new post so I can fix this for the curana demo on friday"}]}]}]}], "created_at": "2025-05-22T22:00:49.362678"}