{"date": "2024-10-15", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1729002665.666039", "text": "<@U0690EB5JE5> The JustWorks authentication for Tithely is completed. Should I put in a ticket when customers complete so you know to go get the data?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729002665.666039", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "xNI2Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " The JustWorks authentication for <PERSON><PERSON><PERSON> is completed. Should I put in a ticket when customers complete so you know to go get the data?"}]}]}]}, {"ts": "**********.299489", "text": "<@U07M6QKHUC9> <@U04DKEFP1K8> <@U07EJ2LP44S> There are two features which were not fully completed.\n\n• *Audit Log* - We have fixed the issue that the change was causing in cycle creation. We will test it for a larger customer like Alayacare/curana health and enable it by early next week\n• *Integrations Sync issue -* The work was done but the approach was bit expensive. We are working on alternative cheaper approach and should be done by EOW.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "lbZpR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " There are two features which "}, {"type": "text", "text": "were not fully completed.\n"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>t Log", "style": {"bold": true, "italic": false}}, {"type": "text", "text": " - We have fixed the issue that the change was causing in cycle creation. We will test it for a larger customer like Alayacare/curana health and enable it by early next week"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Integrations Sync issue - ", "style": {"bold": true, "italic": false}}, {"type": "text", "text": "The work was done but the approach was bit expensive. We are working on alternative cheaper approach and should be done by EOW."}]}], "style": "bullet", "indent": 0, "offset": 0, "border": 0}]}]}, {"ts": "**********.127109", "text": "<@U04DKEFP1K8> I have assigned this back to you as discussed in last leadership stand up. Please assign back once we have discussed with customer\n<https://compiify.atlassian.net/browse/COM-3733>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.127109", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "uilHv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I have assigned this back to you as discussed in last leadership stand up. Please assign back once we have discussed with customer\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3733"}]}]}]}, {"ts": "1728941565.103259", "text": "item to add to tomorrow leadership standup:\n• tool tip text for merit planning view\n• engineering work for phase 1 design improvements", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1728941565.103259", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "SQtrF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "item to add to tomorrow leadership standup:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "tool tip text for merit planning view"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "engineering work for phase 1 design improvements"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.648131"}