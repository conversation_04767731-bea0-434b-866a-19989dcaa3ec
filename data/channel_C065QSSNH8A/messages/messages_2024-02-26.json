{"date": "2024-02-26", "channel_id": "C065QSSNH8A", "message_count": 10, "messages": [{"ts": "1708902391.535019", "text": "saurab<PERSON>.jain has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R06LWR81PDF", "block_id": "+6Y0W", "api_decoration_available": false, "call": {"v1": {"id": "R06LWR81PDF", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1708902391, "active_participants": [], "all_participants": [{"slack_id": "U04DKEFP1K8"}, {"slack_id": "U065H3M6WJV"}], "display_id": "869-4550-4247", "join_url": "https://us06web.zoom.us/j/86945504247?pwd=jxckfGIuU4UI0w3zzgjhqHjKdLV5Ta.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEXzE0OTU3YTNkOTA5YjRkMzNhOTJmYzA0ZjY4ZTExNDE3JnVzcz1haG43VGQ0YkxNY0hzVE5peVFBLVQzMWdSaVdsQjhVWnVnbHBvck5NdDRXNHY3MmNZMUxGaElITndTaV92bEJEVk1IanNxb2ZNSG0yUmlXU1lTTkRCdmd6V2Y4M1RJRWZjX3NrS19BLkh0bnRybDA1NDhqUVpiNVc%3D&action=join&confno=86945504247&pwd=jxckfGIuU4UI0w3zzgjhqHjKdLV5Ta.1", "name": "Zoom meeting started by <PERSON><PERSON><PERSON><PERSON>", "created_by": "U05185RFCNT", "date_end": 1708903951, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "zInqM", "text": {"type": "mrkdwn", "text": "Meeting passcode: jxckfGIuU4UI0w3zzgjhqHjKdLV5Ta.1", "verbatim": false}}]}, {"ts": "1708888529.061099", "text": "Latest update: sdf-test cycle is reset, I'll start testing the \"clean slate\" case now.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708888529.061099", "reply_count": 80, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "L1RIu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Latest update: sdf-test cycle is reset, I'll start testing the \"clean slate\" case now."}]}]}]}, {"ts": "1708968848.503889", "text": "Update: I have received fixed for 3 out of 4 fixes from the above list. I am planning to complete my review and merge them at noon", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708968848.503889", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "CGqBw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Update: I have received fixed for 3 out of 4 fixes from the above list. I am planning to complete my review and merge them at noon"}]}]}]}, {"ts": "1708927076.089239", "text": "Here is jira list which engg will target for today\n1. <https://compiify.atlassian.net/browse/COM-2395>\n2. <https://compiify.atlassian.net/browse/COM-2398>\n3. <https://compiify.atlassian.net/browse/COM-2399>\n4. <https://compiify.atlassian.net/browse/COM-2406>", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV", "U04DS2MBWP4"], "count": 2}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12459::fd423d70d46b11eea7653f2df266b93b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2398?atlOrigin=eyJpIjoiYzc2MTIzMDM4Y2IxNGQ1ZGFjZjM5NjllZTkzYzViMDEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2398 Realtime updates: Cycle reports use last \"Reviewed\" value despite n…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12459::fd423d76d46b11eea7653f2df266b93b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/347c906ac625df3f0780bf0833593920?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "Vinod.e"}, {"type": "mrkdwn", "text": "Assignee: *Vinod.e*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12459::fd423d71d46b11eea7653f2df266b93b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12459\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12459\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2398", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:12460::fd423d72d46b11eea7653f2df266b93b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2399?atlOrigin=eyJpIjoiNmQ1YTc1ZDFkMzJlNGI5NjhjNzEwNWMxNzUxOWU5NTUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2399 Realtime updates: Export from Merit view includes older values if m…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12460::fd423d77d46b11eea7653f2df266b93b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/347c906ac625df3f0780bf0833593920?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "Vinod.e"}, {"type": "mrkdwn", "text": "Assignee: *Vinod.e*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12460::fd423d73d46b11eea7653f2df266b93b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12460\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12460\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2399", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 3, "blocks": [{"type": "section", "block_id": "uf:ih:12467::fd423d74d46b11eea7653f2df266b93b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2406?atlOrigin=eyJpIjoiZjkzOTk2NTkxNTUxNGMxOTk3MmRjZTJiZWNhOTkxMWIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2406 Realtime updates: Export behavior is inconsistent for Reviewed/Pend…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12467::fd423d78d46b11eea7653f2df266b93b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/347c906ac625df3f0780bf0833593920?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "Vinod.e"}, {"type": "mrkdwn", "text": "Assignee: *Vinod.e*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12467::fd423d75d46b11eea7653f2df266b93b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12467\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12467\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2406", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Pa6de", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is jira list which engg will target for today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2395"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2398"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2399"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2406"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1708924961.181259", "text": "<@U04DKEFP1K8> Can you update here what we're committing to fix by tomorrow morning? I communicated to <PERSON> that the $0.00 inputs not overwriting values in Export would be fixed; please update me on what else we expect to be able to clear from the known issues overnight. :pray:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "T+Y2U", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can you update here what we're committing to fix by tomorrow morning? I communicated to <PERSON> that the $0.00 inputs not overwriting values in Export would be fixed; please update me on what else we expect to be able to clear from the known issues overnight. "}, {"type": "emoji", "name": "pray", "unicode": "1f64f"}]}]}]}, {"ts": "1708913783.204469", "text": "<@U04DS2MBWP4> <PERSON> should be able to view her previous update ( i impersonated her and can see the updates too). Looks like she did refresh and was able to see . i have asked her to confirm", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06LFLCUWQM", "created": 1708913758, "timestamp": 1708913758, "name": "Screenshot 2024-02-25 at 6.14.52 PM.png", "title": "Screenshot 2024-02-25 at 6.14.52 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 601573, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06LFLCUWQM/screenshot_2024-02-25_at_6.14.52___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06LFLCUWQM/download/screenshot_2024-02-25_at_6.14.52___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 190, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 253, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 380, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 422, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 506, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 540, "original_w": 3428, "original_h": 1808, "thumb_tiny": "AwAZADDR47ilyP8AIo70UAHH+RS0lFAC0h6jjNFFAB3opO9BoAWlptFACntRSUtAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06LFLCUWQM/screenshot_2024-02-25_at_6.14.52___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06LFLCUWQM-e7e94d7289", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "E4xiF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " <PERSON> should be able to view her previous update ( i impersonated her and can see the updates too). Looks like she did refresh and was able to see . i have asked her to confirm"}]}]}]}, {"ts": "1708907721.512349", "text": "<@U065H3M6WJV> Comments are not lost. I have recovered them from a db dump. Will share a file shortly. We can restore them as well", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708907721.512349", "reply_count": 16, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "9sRbR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Comments are not lost. I have recovered them from a db dump. Will share a file shortly. We can restore them as well"}]}]}]}, {"ts": "1708906772.791909", "text": "Sounds good", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZZ2v3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sounds good"}]}]}]}, {"ts": "1708906626.052269", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> SDF production data is restored\n\n• In the process of resetting, we _lost any prior comments_ that might have been entered. But any other promotion &amp; salary inputs were preserved.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708906626.052269", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "6PgTL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " SDF production data is restored\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In the process of resetting, we "}, {"type": "text", "text": "lost any prior comments", "style": {"italic": true}}, {"type": "text", "text": " that might have been entered. But any other promotion & salary inputs were preserved."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1708903925.007299", "text": "Cycle recreated, re-inputting data now.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "G/z9X", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle recreated, re-inputting data now."}]}]}]}], "created_at": "2025-05-22T21:35:34.577976"}