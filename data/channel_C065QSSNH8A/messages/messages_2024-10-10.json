{"date": "2024-10-10", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1728584492.513449", "text": "That should be fine", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "RKYCB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That should be fine"}]}]}]}, {"ts": "1728584450.562219", "text": "<@U07EJ2LP44S> since chery<PERSON> is done with her demo for alayacare exec's, i will turn off alayacare-test environment ( same goes for nauto-test as well)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Pcwfc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " since chery<PERSON> is done with her demo for alayacare exec's, i will turn off alayacare-test environment ( same goes for nauto-test as well)"}]}]}]}, {"ts": "1728580379.887049", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> Who is the owner of workflow which is creating these jira tickets", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1728580379.887049", "reply_count": 8, "files": [{"id": "F07R1BQPST0", "created": 1728534054, "timestamp": 1728534054, "name": "Screenshot 2024-10-10 at 9.50.43 AM.png", "title": "Screenshot 2024-10-10 at 9.50.43 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 1051719, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07R1BQPST0/screenshot_2024-10-10_at_9.50.43___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07R1BQPST0/download/screenshot_2024-10-10_at_9.50.43___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R1BQPST0-26aff83b79/screenshot_2024-10-10_at_9.50.43___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R1BQPST0-26aff83b79/screenshot_2024-10-10_at_9.50.43___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R1BQPST0-26aff83b79/screenshot_2024-10-10_at_9.50.43___am_360.png", "thumb_360_w": 360, "thumb_360_h": 211, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R1BQPST0-26aff83b79/screenshot_2024-10-10_at_9.50.43___am_480.png", "thumb_480_w": 480, "thumb_480_h": 281, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R1BQPST0-26aff83b79/screenshot_2024-10-10_at_9.50.43___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R1BQPST0-26aff83b79/screenshot_2024-10-10_at_9.50.43___am_720.png", "thumb_720_w": 720, "thumb_720_h": 422, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R1BQPST0-26aff83b79/screenshot_2024-10-10_at_9.50.43___am_800.png", "thumb_800_w": 800, "thumb_800_h": 469, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R1BQPST0-26aff83b79/screenshot_2024-10-10_at_9.50.43___am_960.png", "thumb_960_w": 960, "thumb_960_h": 562, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R1BQPST0-26aff83b79/screenshot_2024-10-10_at_9.50.43___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 600, "original_w": 2820, "original_h": 1652, "thumb_tiny": "AwAcADDS/iptO/iptACilpM0UAOFB6UlHagBP4qSnd6bQACloFLQAUHpRQelAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07R1BQPST0/screenshot_2024-10-10_at_9.50.43___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07R1BQPST0-596f25c691", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "RmNTY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Who is the owner of workflow which is creating these jira tickets"}]}]}]}, {"ts": "1728579534.812579", "text": "I will look into this issue however to avoid in future.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pVQhh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will look into this issue however to avoid in future."}]}]}]}, {"ts": "1728579504.692669", "text": "<@U07M6QKHUC9> Logout and login, The save error should go off. Tried and tested with <@U07EJ2LP44S>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1728579504.692669", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "aKLDS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Logout and login, The save error should go off. Tried and tested with "}, {"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}], "created_at": "2025-05-22T21:35:34.648973"}