#!/usr/bin/env python
"""
Script to test performance improvements for RAG search system.
"""

import os
import sys
import time
import django
from django.db import connection
from django.test.utils import override_settings

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth.models import User
from apps.search.models import SearchResult
from apps.search.services.rag_service import RAGService
from apps.core.utils.search_cache import get_cache_stats, invalidate_search_cache
from apps.documents.models import DocumentContent


def reset_query_count():
    """Reset Django's query count for testing."""
    connection.queries_log.clear()


def get_query_count():
    """Get the number of database queries executed."""
    return len(connection.queries_log)


def test_citation_loading_performance():
    """Test citation loading performance with and without select_related."""
    print("\n" + "="*60)
    print("TESTING CITATION LOADING PERFORMANCE")
    print("="*60)
    
    # Get a search result with citations
    search_result = SearchResult.objects.filter(citations__isnull=False).first()
    
    if not search_result:
        print("❌ No search results with citations found. Skipping test.")
        return
    
    print(f"Testing with SearchResult ID: {search_result.id}")
    
    # Test 1: Without optimization (N+1 queries)
    print("\n1. Testing WITHOUT select_related (N+1 queries):")
    reset_query_count()
    start_time = time.time()
    
    citations = search_result.citations.all().order_by("rank")
    citation_data = []
    for citation in citations:
        # This will trigger additional queries
        data = {
            'text': citation.document_chunk.text if citation.document_chunk else '',
            'document_title': (
                citation.document_chunk.document.title 
                if citation.document_chunk and citation.document_chunk.document 
                else 'Unknown'
            ),
            'source_type': (
                citation.document_chunk.document.source.source_type
                if citation.document_chunk and citation.document_chunk.document and citation.document_chunk.document.source
                else None
            ),
        }
        citation_data.append(data)
    
    end_time = time.time()
    queries_without_optimization = get_query_count()
    time_without_optimization = end_time - start_time
    
    print(f"   ⏱️  Time: {time_without_optimization:.4f}s")
    print(f"   🗄️  Database queries: {queries_without_optimization}")
    print(f"   📄 Citations processed: {len(citation_data)}")
    
    # Test 2: With optimization (select_related)
    print("\n2. Testing WITH select_related (optimized):")
    reset_query_count()
    start_time = time.time()
    
    citations = search_result.citations.select_related(
        'document_chunk__document__source'
    ).order_by("rank")
    citation_data_optimized = []
    for citation in citations:
        # This should not trigger additional queries
        data = {
            'text': citation.document_chunk.text if citation.document_chunk else '',
            'document_title': (
                citation.document_chunk.document.title 
                if citation.document_chunk and citation.document_chunk.document 
                else 'Unknown'
            ),
            'source_type': (
                citation.document_chunk.document.source.source_type
                if citation.document_chunk and citation.document_chunk.document and citation.document_chunk.document.source
                else None
            ),
        }
        citation_data_optimized.append(data)
    
    end_time = time.time()
    queries_with_optimization = get_query_count()
    time_with_optimization = end_time - start_time
    
    print(f"   ⏱️  Time: {time_with_optimization:.4f}s")
    print(f"   🗄️  Database queries: {queries_with_optimization}")
    print(f"   📄 Citations processed: {len(citation_data_optimized)}")
    
    # Calculate improvements
    if queries_without_optimization > 0:
        query_reduction = ((queries_without_optimization - queries_with_optimization) / queries_without_optimization) * 100
        print(f"\n✅ IMPROVEMENT:")
        print(f"   📉 Query reduction: {query_reduction:.1f}% ({queries_without_optimization} → {queries_with_optimization})")
        
        if time_without_optimization > 0:
            time_improvement = ((time_without_optimization - time_with_optimization) / time_without_optimization) * 100
            print(f"   ⚡ Time improvement: {time_improvement:.1f}%")


def test_search_caching_performance():
    """Test search result caching performance."""
    print("\n" + "="*60)
    print("TESTING SEARCH CACHING PERFORMANCE")
    print("="*60)
    
    # Get a user for testing
    user = User.objects.first()
    if not user:
        print("❌ No users found. Skipping test.")
        return
    
    # Clear cache first
    invalidate_search_cache()
    
    # Test query
    test_query = "test performance query"
    
    print(f"Testing with query: '{test_query}'")
    print(f"Using user: {user.username}")
    
    # Test 1: First search (no cache)
    print("\n1. First search (cache miss):")
    start_time = time.time()
    
    try:
        rag_service = RAGService(user=user, tenant_slug=tenant='test-tenant', user=user)
        result1, docs1 = rag_service.search(test_query, top_k=5)
        
        end_time = time.time()
        first_search_time = end_time - start_time
        
        print(f"   ⏱️  Time: {first_search_time:.4f}s")
        print(f"   📄 Results: {len(docs1)} documents")
        print(f"   🆔 Result ID: {result1.id}")
        
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return
    
    # Test 2: Second search (should use cache if implemented)
    print("\n2. Second search (potential cache hit):")
    start_time = time.time()
    
    try:
        rag_service2 = RAGService(user=user, tenant_slug=tenant='test-tenant', user=user)
        result2, docs2 = rag_service2.search(test_query, top_k=5)
        
        end_time = time.time()
        second_search_time = end_time - start_time
        
        print(f"   ⏱️  Time: {second_search_time:.4f}s")
        print(f"   📄 Results: {len(docs2)} documents")
        print(f"   🆔 Result ID: {result2.id}")
        
        # Calculate improvement
        if first_search_time > 0:
            time_improvement = ((first_search_time - second_search_time) / first_search_time) * 100
            print(f"\n✅ IMPROVEMENT:")
            print(f"   ⚡ Time improvement: {time_improvement:.1f}%")
            
            if time_improvement > 10:
                print("   🎯 Significant improvement detected - caching may be working!")
            else:
                print("   ℹ️  Minimal improvement - caching may not be active or query is too fast to measure")
        
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    # Show cache stats
    print("\n3. Cache statistics:")
    cache_stats = get_cache_stats()
    for key, value in cache_stats.items():
        print(f"   {key}: {value}")


def test_document_content_optimization():
    """Test document content loading optimization."""
    print("\n" + "="*60)
    print("TESTING DOCUMENT CONTENT OPTIMIZATION")
    print("="*60)
    
    # Test 1: Count documents by size
    total_docs = DocumentContent.objects.count()
    large_docs = DocumentContent.objects.filter(content_size__gt=10000).count()
    medium_docs = DocumentContent.objects.filter(content_size__gt=1000, content_size__lte=10000).count()
    small_docs = DocumentContent.objects.filter(content_size__lte=1000).count()
    docs_with_summary = DocumentContent.objects.filter(content_summary__isnull=False).exclude(content_summary='').count()
    
    print(f"📊 Document Content Statistics:")
    print(f"   Total documents: {total_docs}")
    print(f"   Large documents (>10KB): {large_docs}")
    print(f"   Medium documents (1-10KB): {medium_docs}")
    print(f"   Small documents (≤1KB): {small_docs}")
    print(f"   Documents with summaries: {docs_with_summary}")
    
    if total_docs == 0:
        print("❌ No document content found. Skipping optimization test.")
        return
    
    # Test 2: Preview loading vs full content loading
    print(f"\n🔍 Testing content loading performance:")
    
    # Test preview loading
    reset_query_count()
    start_time = time.time()
    
    preview_docs = DocumentContent.objects.get_content_preview_only()[:10]
    preview_data = []
    for doc in preview_docs:
        preview_data.append({
            'id': doc.id,
            'size': doc.content_size,
            'summary': doc.content_summary[:100] if doc.content_summary else None
        })
    
    end_time = time.time()
    preview_time = end_time - start_time
    preview_queries = get_query_count()
    
    print(f"   📋 Preview loading: {preview_time:.4f}s, {preview_queries} queries, {len(preview_data)} docs")
    
    # Test full content loading
    reset_query_count()
    start_time = time.time()
    
    full_docs = DocumentContent.objects.all()[:10]
    full_data = []
    for doc in full_docs:
        full_data.append({
            'id': doc.id,
            'size': len(doc.content) if doc.content else 0,
            'content_preview': doc.content[:100] if doc.content else None
        })
    
    end_time = time.time()
    full_time = end_time - start_time
    full_queries = get_query_count()
    
    print(f"   📄 Full loading: {full_time:.4f}s, {full_queries} queries, {len(full_data)} docs")
    
    if full_time > 0:
        time_improvement = ((full_time - preview_time) / full_time) * 100
        print(f"\n✅ Preview loading is {time_improvement:.1f}% faster than full content loading")


def main():
    """Run all performance tests."""
    print("🚀 RAG SEARCH PERFORMANCE TESTING")
    print("=" * 60)
    
    # Enable query logging for testing
    with override_settings(DEBUG=True):
        test_citation_loading_performance()
        test_search_caching_performance()
        test_document_content_optimization()
    
    print("\n" + "="*60)
    print("✅ PERFORMANCE TESTING COMPLETE")
    print("="*60)


if __name__ == "__main__":
    main()
