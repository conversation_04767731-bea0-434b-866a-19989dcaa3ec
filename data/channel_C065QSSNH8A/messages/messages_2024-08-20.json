{"date": "2024-08-20", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1724168001.005549", "text": "<@U04DKEFP1K8> I have fixed both the issues in my PR. Please try again and let me know.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724105860.668249", "subtype": "thread_broadcast", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "0MpeT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I have fixed both the issues in my PR. Please try again and let me know."}]}]}]}, {"ts": "1724165439.670789", "text": "<@U07EJ2LP44S> I am going to fix the integration page error. We need to look at cycle page error. This could be due to missing data.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724078094.367339", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "0UsqH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am going to fix the integration page error. We need to look at cycle page error. This could be due to missing data."}]}]}]}, {"ts": "1724139543.665269", "text": "<!here> I synced with <@U04DKEFP1K8> and listed all the requirements in my <https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=1270401060#gid=1270401060|roadmap sheet>(`Latest Prioroties` sheet)\nI have put rough estimates and also assigned priority. We will review/update this in the eng call and add any missing requirements and finalize the priorities for next 2 to 3 weeks as beyond that or even before that the list would change a bit :slightly_smiling_face: .\n\nAlso I have created place holder tickets for all the requirements listed. <@U07EJ2LP44S> We will add details to the tickets as we go. Please feel free to edit tickets with correct/more details.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724140002.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F06MMTVHJCA", "created": 1709446944, "timestamp": 1709446944, "name": "Project Plan - March to May", "title": "Roadmap - <PERSON><PERSON><PERSON>", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk", "external_url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSJOelJlvSnUUANy3pSjPelooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06MMTVHJCA/project_plan_-_march_to_may", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "taLwz", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I synced with "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " and listed all the requirements in my "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=1270401060#gid=1270401060", "text": "roadmap sheet"}, {"type": "text", "text": "("}, {"type": "text", "text": "Latest Prioroties", "style": {"code": true}}, {"type": "text", "text": " sheet)\nI have put rough estimates and also assigned priority. We will review/update this in the eng call and add any missing requirements and finalize the priorities for next 2 to 3 weeks as beyond that or even before that the list would change a bit "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " .\n\nAlso I have created place holder tickets for all the requirements listed. "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We will add details to the tickets as we go. Please feel free to edit tickets with correct/more details."}]}]}]}, {"ts": "1724107788.050499", "text": "<@U04DS2MBWP4> Lowering the prioirty to review people insights for nauto ( <PERSON> mentioned in the channel they are not planning to use them in this cycle, will focus on other tasks)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724107788.050499", "reply_count": 3, "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Bma69", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Lowering the prioirty to review people insights for nauto ( <PERSON> mentioned in the channel they are not planning to use them in this cycle, will focus on other tasks)"}]}]}]}, {"ts": "1724105860.668249", "text": "Hey <@U07EJ2LP44S> I have updated valgenesis environment with latest resynced data. There are 2 errors currently which engineering needs to resolve before the environment can be handed off to <PERSON>.\n• <https://compiify.atlassian.net/browse/COM-3517>\n• <https://compiify.atlassian.net/browse/COM-3518>\nI will discuss these further with <PERSON><PERSON><PERSON> tonight.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724105860.668249", "reply_count": 4, "edited": {"user": "U04DKEFP1K8", "ts": "1724105893.000000"}, "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}, {"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13578::ed1705105e7811efa0abf34f6a52929b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3517?atlOrigin=eyJpIjoiMjlmM2VjOWYwZjEyNDkyYmJiNTgxZjEwOWIyMzQ4ZmMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3517 Unable to access Settings Page on Valgenesis environment>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13578::ed172c205e7811efa0abf34f6a52929b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13578::ed1705115e7811efa0abf34f6a52929b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13578\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13578\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3517", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:13579::ed1705125e7811efa0abf34f6a52929b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3518?atlOrigin=eyJpIjoiM2VkMTNiZDQzMWE3NGFjMmE2MWQ3YTUwN2UyMTI0MWQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3518 Unable to collect annual salary data via hibob integration>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13579::ed172c215e7811efa0abf34f6a52929b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13579::ed1705135e7811efa0abf34f6a52929b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13579\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13579\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3518", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "YQ43g", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have updated valgenesis environment with latest resynced data. There are 2 errors currently which engineering needs to resolve before the environment can be handed off to <PERSON>.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3517"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3518"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nI will discuss these further with <PERSON><PERSON><PERSON> tonight."}]}]}]}, {"ts": "1724092948.055539", "text": "More updates:\n• Alerts includes all the employees and equity as well\n• There few sections intentionally designed to have all the employees and I agree its difficult explain this and should change it\n• Mayank will share the list of sections have submitted vs not submitted filter.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724092268.637889", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1724092990.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "84LJB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "More updates:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Alerts includes all the employees and equity as well"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "There few sections intentionally designed to have all the employees and I agree its difficult explain this and should change it"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Mayank will share the list of sections have submitted vs not submitted filter."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724092268.637889", "text": "<@U04DS2MBWP4> Reg. filters not showing up in insights. for filters there was fix pushed to test ENV today. but not in production ENV.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724092268.637889", "reply_count": 6, "edited": {"user": "U0690EB5JE5", "ts": "1724092291.000000"}, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "a2Ar/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Reg. filters not showing up in insights. for filters there was fix pushed to test ENV today. but not in production ENV."}]}]}]}], "created_at": "2025-05-22T21:35:34.627518"}