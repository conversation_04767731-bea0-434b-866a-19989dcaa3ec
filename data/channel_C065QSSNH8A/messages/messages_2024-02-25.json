{"date": "2024-02-25", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1708832240.589889", "text": "Here is the summary of issue triaged / fixed during the day\n1. <https://compiify.atlassian.net/browse/COM-2394|COM-2394> - This was a blocker since in certain cases new lives updates were not getting saved. This is fixed and deployed\n2. One australian contractor adjustment were not getting updated in budget correctly ( since the band was not uploaded for australia) Not a product issue. this is not a blocker currently.\n3. COM-2397 HR Admin page shows incorrect budget overview for manager with non us employees ( root caused and its a minor fix). this is not a blocker currently.\n\n<PERSON> reported  <https://compiify.atlassian.net/browse/COM-2395> but it is not a blocker currently\n\n<PERSON> will add further updates and next set of blocking issue", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708832240.589889", "reply_count": 25, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12455::2eeea580d38f11ee977e6b82976486d9", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2394?atlOrigin=eyJpIjoiMmUwMjEwOWNjNDY0NGU5ZTkxYTkyNDhjZThiZjQ1MDIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2394 New updates are reverted from salary and promotion ...will add more…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12455::2eeea584d38f11ee977e6b82976486d9", "elements": [{"type": "mrkdwn", "text": "Status: *Done*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12455::2eeea581d38f11ee977e6b82976486d9", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12455\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12455\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2394", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:12456::2eeea582d38f11ee977e6b82976486d9", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2395?atlOrigin=eyJpIjoiNTJkNGE0ODM4YmFiNGQ0MWEyNDBlZmExN2MwYmM2ODgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2395 Realtime updates: \"Unlock all\" reverts $0.00 merit increases to bla…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12456::2eeea585d38f11ee977e6b82976486d9", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/393c72388fdc9e69b9048f7ada27eceb?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "yaroslav.v"}, {"type": "mrkdwn", "text": "Assignee: *yaroslav.v*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12456::2eeea583d38f11ee977e6b82976486d9", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12456\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12456\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2395", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "kx1VX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the summary of issue triaged / fixed during the day\n1. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2394", "text": "COM-2394"}, {"type": "text", "text": " - This was a blocker since in certain cases new lives updates were not getting saved. This is fixed and deployed\n2. One australian contractor adjustment were not getting updated in budget correctly ( since the band was not uploaded for australia) Not a product issue. this is not a blocker currently.\n3. "}, {"type": "text", "text": "COM-2397 HR", "style": {"unlink": true}}, {"type": "text", "text": " Admin page shows incorrect budget overview for manager with non us employees ( root caused and its a minor fix). this is not a blocker currently.\n\n<PERSON> reported  "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2395"}, {"type": "text", "text": " but it is not a blocker currently\n\n<PERSON> will add further updates and next set of blocking issue"}]}]}]}, {"ts": "1708820089.908809", "text": "Thanks <@U065H3M6WJV> <@U04DKEFP1K8> That's good to hear. Looking forward to the outcome of it.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/xTZw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " That's good to hear. Looking forward to the outcome of it."}]}]}]}, {"ts": "1708819159.698029", "text": "<@U04DS2MBWP4> After the latest sync with <@U04DKEFP1K8> , I feel that we have a better understanding of the root causes of the miscalculation, so we might be able to resolve those today. Still a couple hours away from a new build that can be re-tested.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YSVnP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " After the latest sync with "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " , I feel that we have a better understanding of the root causes of the miscalculation, so we might be able to resolve those today. Still a couple hours away from a new build that can be re-tested."}]}]}]}, {"ts": "1708801246.160899", "text": "Hey <@U04DS2MBWP4> <@U04DKEFP1K8> the first pass isn't positive - I keep finding cases where the amounts in the rows don't match the amount tallied in the budget. It's quite likely that this is due to using a data set that already had some values input, but that's exactly what we'll be dealing with SDF as well. :confused:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708801246.160899", "reply_count": 18, "reactions": [{"name": "face_with_rolling_eyes", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "KpPPs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " the first pass isn't positive - I keep finding cases where the amounts in the rows don't match the amount tallied in the budget. It's quite likely that this is due to using a data set that already had some values input, but that's exactly what we'll be dealing with SDF as well. "}, {"type": "emoji", "name": "confused", "unicode": "1f615"}]}]}]}], "created_at": "2025-05-22T21:35:34.579019"}