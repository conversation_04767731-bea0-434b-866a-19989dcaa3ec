#!/usr/bin/env python3
"""
Fix citation mapping between vector IDs and chunks.
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.documents.models import EmbeddingMetadata, DocumentChunk
import requests

def check_vector_id_mapping():
    """Check the mapping between vector IDs and chunks."""
    print("🔍 Checking Vector ID Mapping")
    print("=" * 50)
    
    # Check EmbeddingMetadata records
    embeddings = EmbeddingMetadata.objects.all()
    print(f"📊 Total EmbeddingMetadata records: {embeddings.count()}")
    
    # Show sample vector IDs
    print(f"\n📄 Sample vector IDs from EmbeddingMetadata:")
    for i, embedding in enumerate(embeddings[:5], 1):
        print(f"  {i}. Vector ID: {embedding.vector_id}")
        print(f"     Chunk ID: {embedding.chunk.id}")
        print(f"     Document: {embedding.chunk.document.title[:50]}...")
    
    # Check what vector IDs are actually in Qdrant
    print(f"\n🔍 Checking Qdrant collection...")
    try:
        # Get collection info
        response = requests.get("http://localhost:6333/collections/tenant_stride_default")
        if response.status_code == 200:
            collection_info = response.json()
            points_count = collection_info['result']['points_count']
            print(f"📊 Points in Qdrant collection: {points_count}")
            
            # Get some sample points
            scroll_response = requests.post(
                "http://localhost:6333/collections/tenant_stride_default/points/scroll",
                json={"limit": 5, "with_payload": True}
            )
            
            if scroll_response.status_code == 200:
                scroll_data = scroll_response.json()
                points = scroll_data['result']['points']
                
                print(f"\n📄 Sample vector IDs from Qdrant:")
                for i, point in enumerate(points, 1):
                    vector_id = point['id']
                    print(f"  {i}. Vector ID: {vector_id}")
                    
                    # Check if this ID exists in EmbeddingMetadata
                    embedding = EmbeddingMetadata.objects.filter(vector_id=vector_id).first()
                    if embedding:
                        print(f"     ✅ Found in EmbeddingMetadata (Chunk ID: {embedding.chunk.id})")
                    else:
                        print(f"     ❌ NOT found in EmbeddingMetadata")
            else:
                print(f"❌ Failed to scroll Qdrant points: {scroll_response.status_code}")
        else:
            print(f"❌ Failed to get Qdrant collection info: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking Qdrant: {str(e)}")

def test_specific_vector_ids():
    """Test the specific vector IDs that failed in the search."""
    print(f"\n🔍 Testing Specific Failed Vector IDs")
    print("=" * 50)
    
    failed_ids = [
        "c36bb4fa-00e9-4798-b052-cc20dc2b763e",
        "cb5e2f9e-c20d-4d80-884b-29055a9aefda", 
        "706b31c8-67a5-4bdd-b364-b64dc0dce2df",
        "b682eb0b-a168-4e20-8e14-06ceaff7bc2e",
        "5a3df087-7ebe-4540-9ccc-736202b54775",
        "7d54f5e6-b189-4c2d-b5eb-c0645f3ab7ad"
    ]
    
    for vector_id in failed_ids:
        print(f"\nTesting vector ID: {vector_id}")
        
        # Check in EmbeddingMetadata
        embedding = EmbeddingMetadata.objects.filter(vector_id=vector_id).first()
        if embedding:
            print(f"  ✅ Found in EmbeddingMetadata")
            print(f"     Chunk ID: {embedding.chunk.id}")
            print(f"     Document: {embedding.chunk.document.title[:50]}...")
        else:
            print(f"  ❌ NOT found in EmbeddingMetadata")
            
            # Check if there are similar IDs
            similar = EmbeddingMetadata.objects.filter(vector_id__startswith=vector_id[:8])
            if similar.exists():
                print(f"     🔍 Found {similar.count()} similar IDs starting with {vector_id[:8]}")
                for sim in similar[:3]:
                    print(f"       - {sim.vector_id}")

def fix_vector_id_format():
    """Check if vector IDs have format issues."""
    print(f"\n🔧 Checking Vector ID Format Issues")
    print("=" * 50)
    
    # Check for different vector ID formats
    embeddings = EmbeddingMetadata.objects.all()
    
    formats = {}
    for embedding in embeddings:
        vid = embedding.vector_id
        if len(vid) == 36 and vid.count('-') == 4:
            formats['uuid'] = formats.get('uuid', 0) + 1
        elif vid.isdigit():
            formats['integer'] = formats.get('integer', 0) + 1
        else:
            formats['other'] = formats.get('other', 0) + 1
    
    print(f"📊 Vector ID formats found:")
    for format_type, count in formats.items():
        print(f"  {format_type}: {count}")
    
    # Show sample of each format
    print(f"\n📄 Sample vector IDs by format:")
    for embedding in embeddings[:10]:
        vid = embedding.vector_id
        if len(vid) == 36 and vid.count('-') == 4:
            print(f"  UUID: {vid}")
        elif vid.isdigit():
            print(f"  Integer: {vid}")
        else:
            print(f"  Other: {vid}")

if __name__ == "__main__":
    check_vector_id_mapping()
    test_specific_vector_ids()
    fix_vector_id_format()
