{"thread_ts": "1736886734.837449", "channel_id": "C065QSSNH8A", "reply_count": 9, "replies": [{"ts": "1736887086.999969", "text": "Ah ok. I think we just have to wait until the existing customers are implemented before making a call on whether we want to build the French Canadian language or not. If all of our other customers are renewing, then we might consider building the French Canadian language. \n\nI don’t think we are gonna be building anything on the paybands for now but we can get her feedback on pre-cycle budget planning and the analytics during tomorrow’s call", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736886734.837449", "edited": {"user": "U07M6QKHUC9", "ts": "1736887116.000000"}, "blocks": [{"type": "rich_text", "block_id": "ZP5KC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ah ok. I think we just have to wait until the existing customer"}, {"type": "text", "text": "s"}, {"type": "text", "text": " are implemented before making a call on whether we want to build the French Canadian language or not. If all of our other customers are renewing, then we might consider building the French Canadian language. \n\nI don’t think we are gonna be building anything on the paybands for now but we can get her feedback on pre-cycle budget planning and the analytics during tomorrow’s call"}]}]}]}, {"ts": "1736887540.447669", "text": "Ok, I'll let you handle that with her, but if you don't build it I'm 100% sure she'll start looking for another tool. And she'll do that early. She's locked in at $5750 but we did commit to her to build it for cycle 2. She has an opt out though so if you don't build it I think she'll exercise that.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736886734.837449", "blocks": [{"type": "rich_text", "block_id": "gsIx2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok, I'll let you handle that with her, but if you don't build it I'm 100% sure she'll start looking for another tool. And she'll do that early. She's locked in at $5750 but we did commit to her to build it for cycle 2. She has an opt out though so if you don't build it I think she'll exercise that."}]}]}]}, {"ts": "1736887655.835049", "text": "Yes! Let’s try to find out during tomorrow’s call. What are her expectations for the language support. If it is not too much of a heavy lifting, then we should be able to build it even if it’s just <PERSON><PERSON><PERSON>.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736886734.837449", "blocks": [{"type": "rich_text", "block_id": "sus65", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes! Let’s try to find out during tomorrow’s call. What are her expectations for the language support. If it is not too much of a heavy lifting, then we should be able to build it even if it’s just <PERSON><PERSON><PERSON>."}]}]}]}, {"ts": "1736888092.340749", "text": "She already did explain; it can't be a translate button, it needs to be natively written into the app.  So basically a fully French (Canadian specifically) version of the app. <@U0690EB5JE5> can you guesstimate effort for that?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736886734.837449", "blocks": [{"type": "rich_text", "block_id": "7blk+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She already did explain; it can't be a translate button, it needs to be natively written into the app.  So basically a fully French (Canadian specifically) version of the app. "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you guesstimate effort for that?"}]}]}]}, {"ts": "1736888210.026209", "text": "I am wondering if he just make make the change for comp planner, and the reports, will it suffice her needs or does it have to be throughout the product?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736886734.837449", "blocks": [{"type": "rich_text", "block_id": "DRIbI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am wondering if he just make make the change for comp planner, and the reports, will it suffice her needs or does it have to be throughout the product?"}]}]}]}, {"ts": "1736888428.338579", "text": "I am guessing it's for anything a recommender has access to - if that view doesn't have reporting, we wouldn't need reporting in French. I think anyone that lives in the affected provinces have to have everything in their language, and for Alayacare I think that is only recommenders (though we should confirm that).", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736886734.837449", "blocks": [{"type": "rich_text", "block_id": "O+Gyx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am guessing it's for anything a recommender has access to - if that view doesn't have reporting, we wouldn't need reporting in French. I think anyone that lives in the affected provinces have to have everything in their language, and for Alayacare I think that is only recommenders (though we should confirm that)."}]}]}]}, {"ts": "1736888464.897459", "text": "Yep, let’s get into more details with her tomorrow", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736886734.837449", "blocks": [{"type": "rich_text", "block_id": "Sm3nu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yep, let’s get into more details with her tomorrow"}]}]}]}, {"ts": "1737023940.435109", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> Estimate for supporting `French Canadian language` will be a month effort. We need to change every place wherever the text is across the product. This also needs someone's help to accurately translate the text across. We can use google translator for engineering work meanwhile.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736886734.837449", "blocks": [{"type": "rich_text", "block_id": "1F+2h", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Estimate for supporting "}, {"type": "text", "text": "French Canadian language", "style": {"code": true}}, {"type": "text", "text": " will be a month effort. We need to change every place wherever the text is across the product. This also needs someone's help to accurately translate the text across. We can use google translator for engineering work meanwhile."}]}]}]}, {"ts": "1737044489.715819", "text": "OK, thank you for looking into it. I am waiting for alaya care to confirm if they would be okay with $25k pricing for the 3rd year", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736886734.837449", "blocks": [{"type": "rich_text", "block_id": "vf/HS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OK, thank you for looking into it. I am waiting for alaya care to confirm if they would be okay with $25k pricing for the 3rd year"}]}]}]}], "created_at": "2025-05-22T22:00:49.333597"}