{"date": "2024-07-22", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1721670518.130919", "text": "Actions items from July 22 eng call:\n\n<@U04DKEFP1K8>\nGet nauto env up and running including <PERSON><PERSON><PERSON>'s login\nSchedule meeting with Delany\nTest sorting functionality\nRevalidate fixed bugs\nTest Vercara's environment for bonus-related issues\nTest org view\nProvide feedback on compensation cycle builder by Wednesday\nSend message to vercara for OT employee sample data\n\n<@U04DS2MBWP4>\nSend note to Playq\nWork with <PERSON>oya on merit planning view categorization\n\n<@U0690EB5JE5>\nThink about and define scope for audit log feature)", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721670518.130919", "reply_count": 9, "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "I8+Xs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Actions items from July 22 eng call:\n\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "\nGet nauto env up and running including <PERSON><PERSON><PERSON>'s login\nSchedule meeting with Delany\nTest sorting functionality\nRevalidate fixed bugs\nTest Vercara's environment for bonus-related issues\nTest org view\nProvide feedback on compensation cycle builder by Wednesday\nSend message to vercara for OT employee sample data\n\n"}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": "\nSend note to Playq\nWork with <PERSON><PERSON> on merit planning view categorization\n\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "\nThink about and define scope for audit log feature)"}]}]}]}, {"ts": "1721669883.323299", "text": "<@U04DKEFP1K8> can you remove rachel from the eng calls&gt;", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "done", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "XdqMy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you remove rachel from the eng calls>"}]}]}]}, {"ts": "1721658942.583789", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> How do we find out how active CWA is in the prod environment and where are they clicking? Is it thru heap and clarity?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721658942.583789", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "c2/3t", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " How do we find out how active CWA is in the prod environment and where are they clicking? Is it thru heap and clarity?"}]}]}]}, {"ts": "1721650569.704989", "text": "<@U04DKEFP1K8> the issue is fixed and recreated the db. Please upload the data and let me know if you still see issue.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721579645.007949", "subtype": "thread_broadcast", "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4", "U04DKEFP1K8"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "St+Wy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " the issue is fixed and recreated the db. Please upload the data and let me know if you still see issue."}]}]}]}, {"ts": "1721639788.500669", "text": "<@U04DKEFP1K8> <@U04DS2MBWP4> I have created a new sheet (*`RoadMap-August`* ) and added list of road map items based on what I know.\n<https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=286802099#gid=286802099>\nPlease do take a look and add if anything is missed. We should review this in one of the eng discussions for prioritization.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721639788.500669", "reply_count": 3, "edited": {"user": "U0690EB5JE5", "ts": "1721640095.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06MMTVHJCA", "created": 1709446944, "timestamp": 1709446944, "name": "Project Plan - March to May", "title": "Roadmap - <PERSON><PERSON><PERSON>", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk", "external_url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSJOelJlvSnUUANy3pSjPelooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06MMTVHJCA/project_plan_-_march_to_may", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "iHi0E", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I have created a new sheet ("}, {"type": "text", "text": "RoadMap-August", "style": {"bold": true, "code": true}}, {"type": "text", "text": " ) and added list of road map items based on what I know.\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=286802099#gid=286802099"}, {"type": "text", "text": "\nPlease do take a look and add if anything is missed. We should review this in one of the eng discussions for prioritization."}]}]}]}, {"ts": "1721637668.003519", "text": "<@U04DS2MBWP4> The design looks great. However my two cents, I feel that we should also review our current cycle builder capabilities and address them at once. Like work on a cycle builder v2.\n\nAlso I think we should prioritize, UX improvements for *Merit View* and *Pay bands* over cycle builder as those would be frequently visited vs Cycle builder for better ROI and impact.\nThere is a lot of scope of improvement in both these areas.\nAnd we also need to work UI design for customer facing *Audit Log* for both Merit planning edits and employee edits*.* This will be very critical feature for a lot of reasons.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721423947.685819", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1721639068.000000"}, "blocks": [{"type": "rich_text", "block_id": "/GW7F", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " The design looks great. However my two cents, I feel that we should also review our current cycle builder capabilities and address them at once. Like work on a cycle builder v2.\n\nAlso I think we should prioritize, UX improvements for "}, {"type": "text", "text": "Merit View", "style": {"bold": true}}, {"type": "text", "text": " and "}, {"type": "text", "text": "Pay bands", "style": {"bold": true}}, {"type": "text", "text": " over cycle builder as those would be frequently visited vs Cycle builder for better ROI and impact.\nThere is a lot of scope of improvement in both these areas.\nAnd we also need to work UI design for customer facing "}, {"type": "text", "text": "<PERSON>t Log", "style": {"bold": true}}, {"type": "text", "text": " for both Merit planning edits and employee edits"}, {"type": "text", "text": ".", "style": {"bold": true}}, {"type": "text", "text": " This will be very critical feature for a lot of reasons."}]}]}]}], "created_at": "2025-05-22T21:35:34.617753"}