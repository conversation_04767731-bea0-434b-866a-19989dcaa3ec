#!/usr/bin/env python3
"""
Test search functionality directly using Django models.
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.unified_rag_service import UnifiedRAGService
from apps.accounts.models import Tenant, User
from apps.core.utils.query_classifier import classify_query

def test_query_classification():
    """Test the enhanced query classification."""
    print("🔍 Testing Query Classification")
    print("=" * 50)
    
    test_queries = [
        "List issues reported by <PERSON>",
        "Summarize issues reported about curana", 
        "What problems has <PERSON> mentioned?",
        "Show me all bugs found in the system",
        "Overview of problems with integrations",
        "How to implement a RAG system?",
        "What is a vector database?",
    ]
    
    for query in test_queries:
        classification = classify_query(query)
        print(f"Query: '{query}'")
        print(f"  Type: {classification['type']} (confidence: {classification['confidence']:.2f})")
        if classification['secondary_type']:
            print(f"  Secondary: {classification['secondary_type']}")
        print()

def test_search_service():
    """Test the search service directly."""
    print("🔍 Testing Search Service")
    print("=" * 50)
    
    # Get tenant and user
    tenant = Tenant.objects.first()
    user = User.objects.first()
    
    if not tenant or not user:
        print("❌ No tenant or user found. Please ensure data is loaded.")
        return
    
    print(f"Using tenant: {tenant.name}")
    print(f"Using user: {user.email}")
    print()
    
    # Initialize service
    service = UnifiedRAGService(tenant.slug, user)
    
    # Test queries
    test_queries = [
        "List issues reported by Amanda",
        "Summarize issues reported about curana",
    ]
    
    for query in test_queries:
        print(f"Testing query: '{query}'")
        print("-" * 40)
        
        try:
            # Perform search
            search_result, retrieved_docs = service.search(
                query_text=query,
                top_k=10,
                min_relevance_score=0.1
            )
            
            print(f"✅ Search completed")
            print(f"📊 Retrieved documents: {len(retrieved_docs)}")
            print(f"📝 Answer length: {len(search_result.generated_answer)} characters")
            print(f"⭐ Average score: {search_result.retriever_score_avg:.3f}")
            
            # Check citations
            citations = search_result.citations.all()
            print(f"🔗 Citations: {citations.count()}")
            
            for i, citation in enumerate(citations[:3], 1):
                print(f"  Citation {i}: {citation.document_chunk.document.title[:50]}...")
                print(f"    Score: {citation.relevance_score:.3f}")
                if citation.document_chunk.document.permalink:
                    print(f"    Link: {citation.document_chunk.document.permalink}")
                if citation.document_chunk.profile:
                    print(f"    Profile: {citation.document_chunk.profile.display_name}")
            
            # Show answer preview
            answer_preview = search_result.generated_answer[:300]
            print(f"📄 Answer preview: {answer_preview}...")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        print("\n" + "="*50 + "\n")

def test_profile_attribution():
    """Test profile attribution for Amanda."""
    print("🔍 Testing Profile Attribution")
    print("=" * 50)
    
    from apps.documents.models import DocumentChunk
    from apps.accounts.models import UserPlatformProfile
    
    # Check Amanda's profile
    amanda_profile = UserPlatformProfile.objects.filter(
        display_name='amanda',
        platform='slack'
    ).first()
    
    if amanda_profile:
        print(f"✅ Amanda profile found: {amanda_profile}")
        
        # Check chunks attributed to Amanda
        amanda_chunks = DocumentChunk.objects.filter(profile=amanda_profile)
        print(f"📊 Chunks attributed to Amanda: {amanda_chunks.count()}")
        
        # Show sample chunks
        for i, chunk in enumerate(amanda_chunks[:3], 1):
            print(f"\nChunk {i}:")
            print(f"  Document: {chunk.document.title}")
            print(f"  Text preview: {chunk.text[:150]}...")
            if chunk.document.permalink:
                print(f"  Permalink: {chunk.document.permalink}")
    else:
        print("❌ Amanda profile not found")

if __name__ == "__main__":
    test_query_classification()
    test_profile_attribution()
    test_search_service()
