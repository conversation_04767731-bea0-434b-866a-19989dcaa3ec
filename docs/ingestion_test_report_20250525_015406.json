{"test_summary": {"start_time": "2025-05-25 01:53:17.891726", "end_time": "2025-05-25 01:53:23.585181", "total_duration": "0:00:05.693455", "documents_processed": 12, "documents_failed": 0}, "data_quality": {"total_documents": 12, "total_chunks": 526, "total_embeddings": 526, "documents_with_content": 12, "chunks_with_embeddings": 526, "documents_with_permalinks": 12, "quality_issues": []}, "data_integrity": {"postgresql_chunks": 526, "qdrant_vectors": 0, "matched_vectors": 526, "orphaned_chunks": [], "orphaned_vectors": [], "integrity_issues": []}, "search_functionality": {"test_queries": [{"query": "budget adherence testing", "status": "error", "error": "RAGService.search() got an unexpected keyword argument 'query'"}, {"query": "bug reports and issues", "status": "error", "error": "RAGService.search() got an unexpected keyword argument 'query'"}, {"query": "manager recommendations", "status": "error", "error": "RAGService.search() got an unexpected keyword argument 'query'"}, {"query": "testing updates", "status": "error", "error": "RAGService.search() got an unexpected keyword argument 'query'"}, {"query": "showstopper bugs", "status": "error", "error": "RAGService.search() got an unexpected keyword argument 'query'"}], "successful_searches": 0, "failed_searches": 5, "search_issues": ["Query 'budget adherence testing' failed: RAGService.search() got an unexpected keyword argument 'query'", "Query 'bug reports and issues' failed: RAGService.search() got an unexpected keyword argument 'query'", "Query 'manager recommendations' failed: RAGService.search() got an unexpected keyword argument 'query'", "Query 'testing updates' failed: RAGService.search() got an unexpected keyword argument 'query'", "Query 'showstopper bugs' failed: RAGService.search() got an unexpected keyword argument 'query'"]}, "overall_status": "FAIL"}