{"date": "2023-12-05", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1701794738.292899", "text": "<@U04DKEFP1K8> Is <PERSON><PERSON><PERSON> gonna be able to join the Merge call for sake of visibility?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5XNtv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Is <PERSON><PERSON><PERSON> gonna be able to join the Merge call for sake of visibility?"}]}]}]}, {"ts": "1701793803.372499", "text": "<!here> I have set the total rewards implemenation planning for SDF on 12/11 at 10 am pst. <@U0658EW4B8D> would you be able to attend it?", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "4TSuj", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I have set the total rewards implemenation planning for SDF on 12/11 at 10 am pst. "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " would you be able to attend it?"}]}]}]}, {"ts": "1701743441.872889", "text": "I think it's fine since <PERSON><PERSON><PERSON><PERSON> is joining it.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Pb3xN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think it's fine since <PERSON><PERSON><PERSON><PERSON> is joining it."}]}]}]}, {"ts": "1701742170.438499", "text": "Do you need me to join the Merge call tomorrow <@U04DS2MBWP4>? I noticed it overlaps our design sync.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "drfWt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you need me to join the Merge call tomorrow "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": "? I noticed it overlaps our design sync."}]}]}]}, {"ts": "1701724535.282289", "text": "Crap! I just realized the Thursday call with Digital Asset is a day when <@U04DKEFP1K8> is out. :picard-facepalm:\n\n_Edit: Moved to Friday, since I think <PERSON><PERSON><PERSON><PERSON> will be able to join then._ :+1: ", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701724535.282289", "reply_count": 9, "edited": {"user": "U065H3M6WJV", "ts": "1701742142.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "euzBr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Crap! I just realized the Thursday call with Digital Asset is a day when "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " is out. "}, {"type": "emoji", "name": "picard-facepalm"}, {"type": "text", "text": "\n\n"}, {"type": "text", "text": "Edit: Moved to Friday, since I think <PERSON><PERSON><PERSON><PERSON> will be able to join then. ", "style": {"italic": true}}, {"type": "emoji", "name": "+1", "unicode": "1f44d", "style": {"italic": true}}, {"type": "text", "text": " ", "style": {"italic": true}}]}]}]}, {"ts": "1701717940.019399", "text": "We've just received the customer data from *Digital Asset* and I've moved the files to the <https://drive.google.com/drive/folders/1VqpbnBFyZo-Dp4xbQBsNq9FmaG_1gYOR|Google Drive folder>.\n\nI'll look for a time for our next sync up, ideally when <PERSON><PERSON><PERSON><PERSON> and <PERSON> are also available to join.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701717940.019399", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "sYsDf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We've just received the customer data from "}, {"type": "text", "text": "Digital Asset", "style": {"bold": true}}, {"type": "text", "text": " and I've moved the files to the "}, {"type": "link", "url": "https://drive.google.com/drive/folders/1VqpbnBFyZo-Dp4xbQBsNq9FmaG_1gYOR", "text": "Google Drive folder"}, {"type": "text", "text": ".\n\nI'll look for a time for our next sync up, ideally when <PERSON><PERSON><PERSON><PERSON> and <PERSON> are also available to join."}]}]}]}, {"ts": "1701715087.643869", "text": "Do you mind sending me his scheduling link?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701715087.643869", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "DMKUf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you mind sending me his scheduling link?"}]}]}]}], "created_at": "2025-05-22T21:35:34.572817"}