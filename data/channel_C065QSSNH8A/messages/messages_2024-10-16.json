{"date": "2024-10-16", "channel_id": "C065QSSNH8A", "message_count": 10, "messages": [{"ts": "1729102554.680689", "text": "ok", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1729101345.968239", "text": "<@U07EJ2LP44S> I still see the issue in stridedemo env. I suspect something to do with infra. Can we wait until tomorrow. Please do not create a new cycle so that I can reproduce the error.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729101345.968239", "reply_count": 2, "reactions": [{"name": "white_check_mark", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "0mQHr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I still see the issue in stridedemo env. I suspect something to do with infra. Can we wait until tomorrow. Please do not create a new cycle so that I can reproduce the error."}]}]}]}, {"ts": "1729094285.210969", "text": "Can we turn the PlayQ env back on for a couple weeks", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729094285.210969", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "O5bvM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can we turn the PlayQ env back on for a couple weeks"}]}]}]}, {"ts": "1729092132.045309", "text": "Agenda for today:\ntesting progress\nalignment on phase 1 scope  for UX\nSDF demo- making sure we are good to go\nupdate on marketing", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WEr3l", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today:\ntesting progress\nalignment on phase 1 scope  for UX\nSDF demo- making sure we are good to go\nupdate on marketing"}]}]}]}, {"ts": "1729091762.855339", "text": "we have two demos today", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "JaaKL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we have two demos today"}]}]}]}, {"ts": "1729091200.333109", "text": "adding users to stridedemo failing:", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07S59U1NQ2", "created": 1729091195, "timestamp": 1729091195, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 321369, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07S59U1NQ2/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07S59U1NQ2/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07S59U1NQ2-613391ce50/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07S59U1NQ2-613391ce50/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07S59U1NQ2-613391ce50/image_360.png", "thumb_360_w": 360, "thumb_360_h": 236, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07S59U1NQ2-613391ce50/image_480.png", "thumb_480_w": 480, "thumb_480_h": 314, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07S59U1NQ2-613391ce50/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07S59U1NQ2-613391ce50/image_720.png", "thumb_720_w": 720, "thumb_720_h": 472, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07S59U1NQ2-613391ce50/image_800.png", "thumb_800_w": 800, "thumb_800_h": 524, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07S59U1NQ2-613391ce50/image_960.png", "thumb_960_w": 960, "thumb_960_h": 629, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07S59U1NQ2-613391ce50/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 671, "original_w": 2268, "original_h": 1486, "thumb_tiny": "AwAfADCNo2JPzJ1PVxTfJb+/H/32K03aOLG/aM037Rb/AN9KAM3yW/vx/wDfYo8pv+ekf/fYrS+0W/8AfSj7Rb/30oAzfKb/AJ6R/wDfYoEJz/rI/wDvsVpfaLf++lH2i3/vpQBFfLuKfMB161TMQ7yx/r/hVy+6x/j/AEqoxxQA3yl/57R/r/hR5S/89o/1/wAKaSSfvUmW9TQA/wApf+e0f6/4Uoij7zLn2z/hUeW9TRlvU0Af/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07S59U1NQ2/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07S59U1NQ2-ddf7ce44ca", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Yq4fk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "adding users to stridedemo failing:"}]}]}]}, {"ts": "1729087973.217709", "text": "cycle builder breaking in stridedemo after adding components", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729087973.217709", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "ZtRfo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "cycle builder breaking in stridedemo after adding components"}]}]}]}, {"ts": "1729077699.746269", "text": "<@U07M6QKHUC9> I went through <https://docs.google.com/document/d/1HhiWpLDJ0MqigAF2Ag-67C_lck2D9Zn5mc93g-E4N_M/edit|phase 1>. Are you expecting everything in the phase 1 to be done for Nov cycles or just renaming/reordering and tool tip is the scope? Changes like stories 1.2 and 1.3, the scope is more than just cosmetics and the effort would more than a week if we have to complete everything in phase 1. And Not sure if its a good idea to take up such functional changes at this juncture. We can align on the scope if we get time today in the meeting else tomorrow's meeting.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729077699.746269", "reply_count": 3, "edited": {"user": "U0690EB5JE5", "ts": "1729077934.000000"}, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "lWK+W", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I went through "}, {"type": "link", "url": "https://docs.google.com/document/d/1HhiWpLDJ0MqigAF2Ag-67C_lck2D9Zn5mc93g-E4N_M/edit", "text": "phase 1"}, {"type": "text", "text": ". Are you expecting everything in the phase 1 to be done for Nov cycles or just renaming/reordering and tool tip is the scope? Changes like stories 1.2 and 1.3, the scope is more than just cosmetics and the effort would more than a week if we have to complete everything in phase 1. And Not sure if its a good idea to take up such functional changes at this juncture. We can align on the scope if we get time today in the meeting else tomorrow's meeting."}]}]}]}, {"ts": "1729037690.795789", "text": "<!here> In tomorrow's standup lets discuss testing progress in 1st 2 week of october ( i am kinda stuck in a loop)", "user": "U04DKEFP1K8", "type": "message", "edited": {"user": "U04DKEFP1K8", "ts": "1729037708.000000"}, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8IQ4m", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " In tomorrow's standup lets discuss testing progress in 1st 2 week of october ( i am kinda stuck in a loop)"}]}]}]}, {"ts": "1729029364.042819", "text": "After talking with <PERSON><PERSON><PERSON> - wanted to share this overview of the work we do on paybands/leveling with customers to see if we can focus on this as an area to get more efficient. I recorded 2 videos - one was the overview on <PERSON><PERSON><PERSON><PERSON><PERSON>'s process, and the other was the overview on Alayacare (polar opposites as far as having a buttoned up HR process). For discussion in tomorrows call!\n\nDegenkolb: <https://www.loom.com/share/73eb33ffe9de4c5e84558a277c78d848?sid=db568e21-fb8e-4b15-a5b5-098489ac7781>\nAlayacare:<https://www.loom.com/share/083e51e3a55d41fc955596904e21fbc0?sid=01c5b4b5-4d75-4c5e-948b-e680661dafe1>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729029364.042819", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "vWOUG", "video_url": "https://www.loom.com/embed/73eb33ffe9de4c5e84558a277c78d848?sid=db568e21-fb8e-4b15-a5b5-098489ac7781&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/73eb33ffe9de4c5e84558a277c78d848-7c80d6f7dc55081c-4x3.jpg", "alt_text": "Streamlining Data Processing for Efficiency 💡", "title": {"type": "plain_text", "text": "Streamlining Data Processing for Efficiency 💡", "emoji": true}, "title_url": "https://www.loom.com/share/73eb33ffe9de4c5e84558a277c78d848", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 4 min  ", "emoji": true}}, {"type": "section", "block_id": "oBq5Z", "text": {"type": "mrkdwn", "text": ":information_source: After analyzing a case study on Degenkolb, I delved into the process of mapping pay bands to levels, highlighting the manual efforts involved in the...", "verbatim": false}}, {"type": "actions", "block_id": "KCmdG", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/73eb33ffe9de4c5e84558a277c78d848?sid=db568e21-fb8e-4b15-a5b5-098489ac7781"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"73eb33ffe9de4c5e84558a277c78d848\",\"videoName\":\"Streamlining Data Processing for Efficiency 💡\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/73eb33ffe9de4c5e84558a277c78d848?sid=db568e21-fb8e-4b15-a5b5-098489ac7781", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}, {"id": 2, "blocks": [{"type": "video", "block_id": "ZsxhF", "video_url": "https://www.loom.com/embed/083e51e3a55d41fc955596904e21fbc0?sid=01c5b4b5-4d75-4c5e-948b-e680661dafe1&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/083e51e3a55d41fc955596904e21fbc0-9b5a2fef0a7a4a2b-4x3.jpg", "alt_text": "Pay Band Mapping Overview 👩💼", "title": {"type": "plain_text", "text": "Pay Band Mapping Overview 👩‍💼", "emoji": true}, "title_url": "https://www.loom.com/share/083e51e3a55d41fc955596904e21fbc0", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 4 min  ", "emoji": true}}, {"type": "section", "block_id": "BXNBo", "text": {"type": "mrkdwn", "text": ":information_source: In this video, I provide an overview of the pay band mapping process with AlayaCare. We worked on uploading bans, matching titles, and creating new...", "verbatim": false}}, {"type": "actions", "block_id": "HNt2x", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/083e51e3a55d41fc955596904e21fbc0?sid=01c5b4b5-4d75-4c5e-948b-e680661dafe1"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"083e51e3a55d41fc955596904e21fbc0\",\"videoName\":\"Pay Band Mapping Overview 👩💼\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/083e51e3a55d41fc955596904e21fbc0?sid=01c5b4b5-4d75-4c5e-948b-e680661dafe1", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "3rks/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "After talking with <PERSON><PERSON><PERSON> - wanted to share this overview of the work we do on paybands/leveling with customers to see if we can focus on this as an area to get more efficient. I recorded 2 videos - one was the overview on Degenkolb's process, and the other was the overview on Alayacare (polar opposites as far as having a buttoned up HR process). For discussion in tomorrows call!\n\nDegenkolb: "}, {"type": "link", "url": "https://www.loom.com/share/73eb33ffe9de4c5e84558a277c78d848?sid=db568e21-fb8e-4b15-a5b5-098489ac7781"}, {"type": "text", "text": "\nAlayacare:"}, {"type": "link", "url": "https://www.loom.com/share/083e51e3a55d41fc955596904e21fbc0?sid=01c5b4b5-4d75-4c5e-948b-e680661dafe1"}]}]}]}], "created_at": "2025-05-22T21:35:34.647673"}