#!/usr/bin/env python3
"""
Comprehensive Quality & Correctness Validation for 768d Embedding Upgrade
Tests technical implementation, search accuracy, and response quality
"""

import os
import sys
import django
from datetime import datetime
import json

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))

# Setup Django with 768d model
os.environ["EMBEDDING_MODEL_NAME"] = "BAAI/bge-base-en-v1.5"
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def validate_technical_implementation():
    """Validate that 768d embeddings are properly implemented."""
    
    print("🔧 TECHNICAL IMPLEMENTATION VALIDATION")
    print("=" * 60)
    
    from apps.core.utils.embedding_consistency import get_embedding_model_info, get_consistent_embedding_model
    from apps.documents.models import EmbeddingMetadata, DocumentChunk, RawDocument
    
    results = {}
    
    # 1. Check configuration
    model_info = get_embedding_model_info()
    print(f"📊 Embedding Model: {model_info['model_name']}")
    print(f"📐 Dimensions: {model_info['dimensions']}")
    
    config_correct = (
        model_info['model_name'] == 'BAAI/bge-base-en-v1.5' and 
        model_info['dimensions'] == 768
    )
    print(f"✅ Configuration: {'CORRECT' if config_correct else 'INCORRECT'}")
    results['config_correct'] = config_correct
    
    # 2. Test embedding generation
    try:
        embedding_model = get_consistent_embedding_model()
        test_embedding = embedding_model.get_text_embedding("Test document for validation")
        embedding_correct = len(test_embedding) == 768
        print(f"✅ Embedding Generation: {'CORRECT' if embedding_correct else 'INCORRECT'} ({len(test_embedding)}d)")
        results['embedding_generation'] = embedding_correct
    except Exception as e:
        print(f"❌ Embedding Generation: FAILED - {e}")
        results['embedding_generation'] = False
    
    # 3. Check data integrity
    doc_count = RawDocument.objects.count()
    chunk_count = DocumentChunk.objects.count()
    embedding_count = EmbeddingMetadata.objects.count()
    
    data_integrity = doc_count > 0 and chunk_count > 0 and embedding_count > 0
    print(f"📊 Data Counts: {doc_count} docs, {chunk_count} chunks, {embedding_count} embeddings")
    print(f"✅ Data Integrity: {'CORRECT' if data_integrity else 'INCORRECT'}")
    results['data_integrity'] = data_integrity
    
    # 4. Check embedding metadata
    if embedding_count > 0:
        sample_embedding = EmbeddingMetadata.objects.first()
        metadata_correct = (
            sample_embedding.vector_dimensions == 768 and
            sample_embedding.model_name == 'BAAI/bge-base-en-v1.5'
        )
        print(f"✅ Embedding Metadata: {'CORRECT' if metadata_correct else 'INCORRECT'}")
        print(f"   Sample: {sample_embedding.vector_dimensions}d, {sample_embedding.model_name}")
        results['metadata_correct'] = metadata_correct
    else:
        results['metadata_correct'] = False
    
    # 5. Check vector store
    try:
        import qdrant_client
        client = qdrant_client.QdrantClient(host="localhost", port=6333)
        collections = client.get_collections()
        
        vector_store_ok = False
        for collection in collections.collections:
            if 'stride' in collection.name:
                info = client.get_collection(collection.name)
                vector_store_ok = True
                print(f"✅ Vector Store: CORRECT ({collection.name})")
                break
        
        if not vector_store_ok:
            print(f"❌ Vector Store: NO COLLECTIONS FOUND")
        
        results['vector_store'] = vector_store_ok
        
    except Exception as e:
        print(f"❌ Vector Store: ERROR - {e}")
        results['vector_store'] = False
    
    return results

def validate_search_accuracy():
    """Validate search accuracy with known correct answers."""
    
    print("\n🎯 SEARCH ACCURACY VALIDATION")
    print("=" * 60)
    
    from apps.search.services.rag_service import RAGService
    from django.contrib.auth.models import User
    
    # Test cases with known correct answers from the data
    test_cases = [
        {
            "query": "What did Amanda say about the proration date picker bug?",
            "expected_elements": ["Amanda", "proration", "date picker", "bug", "COM-4002"],
            "expected_citations": True,
            "description": "Specific person + specific bug report"
        },
        {
            "query": "Who is assigned to ticket COM-4036?",
            "expected_elements": ["Mahesh", "COM-4036", "assigned"],
            "expected_citations": True,
            "description": "Ticket assignment lookup"
        },
        {
            "query": "What is Kapil's role?",
            "expected_elements": ["Kapil", "CEO", "Founder"],
            "expected_citations": True,
            "description": "Person role identification"
        },
        {
            "query": "What was the issue with Position In Band for Alayacare?",
            "expected_elements": ["Position In Band", "Alayacare", "hourly employees"],
            "expected_citations": True,
            "description": "Customer-specific issue"
        },
        {
            "query": "Who reported the proration bug and who is fixing it?",
            "expected_elements": ["Amanda", "reported", "Mahesh", "fixing", "proration"],
            "expected_citations": True,
            "description": "Multi-hop reasoning query"
        }
    ]
    
    user = User.objects.first()
    rag_service = RAGService(user=user, tenant_slug='stride')
    
    results = {}
    total_accuracy = 0
    
    for i, test_case in enumerate(test_cases):
        query = test_case["query"]
        expected_elements = test_case["expected_elements"]
        expected_citations = test_case["expected_citations"]
        description = test_case["description"]
        
        print(f"\n🔍 Test {i+1}: {description}")
        print(f"📝 Query: '{query}'")
        
        try:
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=15,
                min_relevance_score=0.15,
                use_hybrid_search=True,
                use_context_aware=True
            )
            
            # Check citations
            citations_count = search_result.citations.count()
            has_citations = citations_count > 0
            citations_correct = has_citations == expected_citations
            
            # Check response content
            response = search_result.generated_answer.lower()
            elements_found = []
            for element in expected_elements:
                if element.lower() in response:
                    elements_found.append(element)
            
            element_accuracy = len(elements_found) / len(expected_elements)
            
            # Overall accuracy for this test
            test_accuracy = (
                (citations_correct * 0.4) +  # 40% weight for citations
                (element_accuracy * 0.6)     # 60% weight for content accuracy
            ) * 100
            
            total_accuracy += test_accuracy
            
            print(f"   📊 Citations: {citations_count} ({'✅' if citations_correct else '❌'})")
            print(f"   🎯 Elements: {len(elements_found)}/{len(expected_elements)} ({element_accuracy:.1%})")
            print(f"   📈 Accuracy: {test_accuracy:.0f}%")
            
            if element_accuracy < 0.5:
                print(f"   ⚠️  Missing: {[e for e in expected_elements if e not in elements_found]}")
            
            results[query] = {
                'citations': citations_count,
                'citations_correct': citations_correct,
                'elements_found': len(elements_found),
                'elements_expected': len(expected_elements),
                'element_accuracy': element_accuracy,
                'test_accuracy': test_accuracy,
                'response_length': len(search_result.generated_answer)
            }
            
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            results[query] = {'error': str(e), 'test_accuracy': 0}
    
    avg_accuracy = total_accuracy / len(test_cases)
    print(f"\n📊 OVERALL SEARCH ACCURACY: {avg_accuracy:.0f}%")
    
    return results, avg_accuracy

def validate_response_quality():
    """Validate response quality, formatting, and professionalism."""
    
    print("\n📝 RESPONSE QUALITY VALIDATION")
    print("=" * 60)
    
    from apps.search.services.rag_service import RAGService
    from django.contrib.auth.models import User
    import re
    
    user = User.objects.first()
    rag_service = RAGService(user=user, tenant_slug='stride')
    
    # Quality test queries
    quality_tests = [
        "What did Amanda say about the proration date picker bug?",
        "Show me discussions about DegenKolb report changes",
        "What tasks is Amanda responsible for?"
    ]
    
    quality_results = {}
    total_quality_score = 0
    
    for query in quality_tests:
        print(f"\n🔍 Testing: '{query[:50]}...'")
        
        try:
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=15,
                min_relevance_score=0.15,
                use_hybrid_search=True,
                use_context_aware=True
            )
            
            response = search_result.generated_answer
            citations_count = search_result.citations.count()
            
            # Quality checks
            quality_checks = {}
            
            # 1. No document references (should be clean)
            has_doc_refs = '[Document' in response or 'document_' in response
            quality_checks['clean_response'] = not has_doc_refs
            
            # 2. Human-readable dates
            date_pattern = r'\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b'
            human_dates = re.findall(date_pattern, response)
            quality_checks['human_dates'] = len(human_dates) > 0
            
            # 3. Professional formatting (no excessive bold, proper structure)
            excessive_bold = response.count('**') > 10
            quality_checks['professional_formatting'] = not excessive_bold
            
            # 4. Appropriate length (not too short, not too long)
            response_length = len(response)
            appropriate_length = 100 <= response_length <= 2000
            quality_checks['appropriate_length'] = appropriate_length
            
            # 5. Has citations
            quality_checks['has_citations'] = citations_count > 0
            
            # 6. Coherent and relevant
            query_words = query.lower().split()
            response_lower = response.lower()
            relevant_words = sum(1 for word in query_words if word in response_lower and len(word) > 3)
            quality_checks['relevance'] = relevant_words >= 2
            
            # 7. No error messages or fallbacks
            error_indicators = ['sorry', 'cannot find', 'no information', 'unable to', 'error']
            has_errors = any(indicator in response.lower() for indicator in error_indicators)
            quality_checks['no_errors'] = not has_errors
            
            # Calculate quality score
            quality_score = (sum(quality_checks.values()) / len(quality_checks)) * 100
            total_quality_score += quality_score
            
            print(f"   📊 Citations: {citations_count}")
            print(f"   📝 Length: {response_length} chars")
            print(f"   🎯 Quality Score: {quality_score:.0f}%")
            
            # Show quality breakdown
            for check, passed in quality_checks.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {check.replace('_', ' ').title()}")
            
            quality_results[query] = {
                'quality_score': quality_score,
                'quality_checks': quality_checks,
                'citations': citations_count,
                'response_length': response_length,
                'response_preview': response[:200] + "..." if len(response) > 200 else response
            }
            
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            quality_results[query] = {'error': str(e), 'quality_score': 0}
    
    avg_quality = total_quality_score / len(quality_tests)
    print(f"\n📊 OVERALL RESPONSE QUALITY: {avg_quality:.0f}%")
    
    return quality_results, avg_quality

def validate_performance_improvement():
    """Compare 768d performance against expected improvements."""
    
    print("\n📈 PERFORMANCE IMPROVEMENT VALIDATION")
    print("=" * 60)
    
    # Expected improvements from 384d baseline
    baseline_metrics = {
        'citation_success_rate': 52,  # % of queries with citations
        'average_quality': 66,        # % average quality score
        'factual_accuracy': 74,       # % accuracy for factual queries
    }
    
    expected_improvements = {
        'citation_success_rate': 65,  # Expected with 768d
        'average_quality': 75,        # Expected with 768d
        'factual_accuracy': 85,       # Expected with 768d
    }
    
    print("📊 BASELINE (384d) vs EXPECTED (768d) vs ACTUAL (768d)")
    print("-" * 60)
    
    # This would be populated by running the actual tests above
    # For now, showing the framework
    
    improvement_results = {
        'baseline_metrics': baseline_metrics,
        'expected_improvements': expected_improvements,
        'validation_needed': True
    }
    
    return improvement_results

def main():
    """Run comprehensive validation suite."""
    
    print("🚀 COMPREHENSIVE 768D EMBEDDING VALIDATION")
    print("=" * 80)
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all validation tests
    technical_results = validate_technical_implementation()
    search_results, search_accuracy = validate_search_accuracy()
    quality_results, response_quality = validate_response_quality()
    performance_results = validate_performance_improvement()
    
    # Generate comprehensive report
    print("\n📊 COMPREHENSIVE VALIDATION RESULTS")
    print("=" * 80)
    
    # Technical validation
    technical_score = (sum(technical_results.values()) / len(technical_results)) * 100
    print(f"🔧 Technical Implementation: {technical_score:.0f}%")
    
    # Search accuracy
    print(f"🎯 Search Accuracy: {search_accuracy:.0f}%")
    
    # Response quality
    print(f"📝 Response Quality: {response_quality:.0f}%")
    
    # Overall assessment
    overall_score = (technical_score + search_accuracy + response_quality) / 3
    print(f"\n🏆 OVERALL VALIDATION SCORE: {overall_score:.0f}%")
    
    # Detailed assessment
    if overall_score >= 85:
        print("\n🎉 EXCELLENT: 768d embedding upgrade is highly successful!")
        print("✅ Technical implementation is correct")
        print("✅ Search accuracy significantly improved")
        print("✅ Response quality is professional")
        print("✅ Ready for production deployment")
    elif overall_score >= 70:
        print("\n✅ GOOD: 768d embedding upgrade is successful with minor areas for improvement")
        print("💡 Monitor performance and fine-tune as needed")
    else:
        print("\n⚠️  NEEDS ATTENTION: Some validation issues found")
        print("💡 Review technical implementation and search configuration")
    
    # Specific recommendations
    print(f"\n💡 SPECIFIC FINDINGS:")
    
    if technical_score < 100:
        print("1. 🔧 Technical implementation needs attention")
        failed_checks = [k for k, v in technical_results.items() if not v]
        print(f"   Failed checks: {failed_checks}")
    
    if search_accuracy < 80:
        print("2. 🎯 Search accuracy could be improved")
        print("   Consider adjusting search thresholds or prompt templates")
    
    if response_quality < 80:
        print("3. 📝 Response quality needs improvement")
        print("   Review prompt templates and response formatting")
    
    # Save detailed results
    results_file = f"validation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    detailed_results = {
        'timestamp': datetime.now().isoformat(),
        'technical_results': technical_results,
        'search_results': {k: v for k, v in search_results.items() if 'error' not in v},
        'quality_results': {k: v for k, v in quality_results.items() if 'error' not in v},
        'performance_results': performance_results,
        'scores': {
            'technical_score': technical_score,
            'search_accuracy': search_accuracy,
            'response_quality': response_quality,
            'overall_score': overall_score
        }
    }
    
    with open(results_file, 'w') as f:
        json.dump(detailed_results, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    
    return detailed_results

if __name__ == "__main__":
    main()
