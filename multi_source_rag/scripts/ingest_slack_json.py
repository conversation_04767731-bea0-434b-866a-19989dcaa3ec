#!/usr/bin/env python
"""
Slack JSON Data Ingestion Script

This script ingests Slack data from JSON files in the data/slack/ folder
using the UnifiedIngestionService with enhanced metadata.

The script processes JSON files that contain Slack messages and creates
documents with enhanced metadata for better search and filtering.

Usage:
    python scripts/ingest_slack_json.py
"""

import os
import sys
import django
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from django.db import transaction
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource
from apps.documents.services.llama_ingestion_service_unified import UnifiedLlamaIndexIngestionService
from apps.core.utils.slack_metadata_enhancer import enhance_slack_metadata

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def parse_slack_messages(messages: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Parse Slack messages and create enhanced metadata.

    Args:
        messages: List of Slack message dictionaries

    Returns:
        Dict with parsed message data and metadata
    """
    if not messages:
        return {}

    # Sort messages by timestamp
    sorted_messages = sorted(messages, key=lambda x: float(x.get('ts', 0)))

    # Extract basic information
    participants = set()
    reactions = []
    has_code = False
    has_threads = False
    thread_count = 0

    for msg in sorted_messages:
        # Extract user
        user = msg.get('user', 'unknown')
        if user and user != 'unknown':
            participants.add(user)

        # Check for code blocks
        text = msg.get('text', '')
        if '```' in text or '`' in text:
            has_code = True

        # Check for threads
        if msg.get('thread_ts'):
            has_threads = True
            if msg.get('thread_ts') == msg.get('ts'):
                thread_count += 1

        # Extract reactions
        if 'reactions' in msg:
            for reaction in msg['reactions']:
                reactions.append({
                    'name': reaction.get('name', ''),
                    'count': reaction.get('count', 0)
                })

    # Calculate time range
    start_time = None
    end_time = None
    if sorted_messages:
        start_ts = float(sorted_messages[0].get('ts', 0))
        end_ts = float(sorted_messages[-1].get('ts', 0))
        start_time = datetime.fromtimestamp(start_ts).isoformat()
        end_time = datetime.fromtimestamp(end_ts).isoformat()

    # Create content by joining messages
    content_parts = []
    for msg in sorted_messages:
        user = msg.get('user', 'unknown')
        text = msg.get('text', '')
        ts = msg.get('ts', '')

        if text:
            # Convert timestamp to readable format
            try:
                dt = datetime.fromtimestamp(float(ts))
                time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
            except:
                time_str = ts

            content_parts.append(f"[{time_str}] {user}: {text}")

    content = "\n".join(content_parts)

    # Calculate quality score
    quality_score = min(1.0, (
        len(participants) * 0.2 +
        len(reactions) * 0.1 +
        (1.0 if has_threads else 0.0) * 0.3 +
        min(len(sorted_messages) / 20.0, 1.0) * 0.4
    ))

    return {
        'content': content,
        'metadata': {
            'message_count': len(sorted_messages),
            'participants': list(participants),
            'start_time': start_time,
            'end_time': end_time,
            'reactions': reactions,
            'has_code': has_code,
            'has_threads': has_threads,
            'thread_count': thread_count,
            'quality_score': quality_score,
            'technical_terms': extract_technical_terms(content)
        }
    }


def extract_technical_terms(text: str) -> List[str]:
    """Extract technical terms from text."""
    technical_keywords = [
        'api', 'database', 'server', 'client', 'endpoint', 'authentication',
        'authorization', 'deployment', 'docker', 'kubernetes', 'aws', 'gcp',
        'azure', 'microservice', 'frontend', 'backend', 'javascript', 'python',
        'java', 'react', 'vue', 'angular', 'node', 'express', 'django',
        'flask', 'spring', 'mongodb', 'postgresql', 'mysql', 'redis',
        'elasticsearch', 'kafka', 'rabbitmq', 'nginx', 'apache', 'ssl',
        'https', 'oauth', 'jwt', 'rest', 'graphql', 'websocket', 'grpc'
    ]

    text_lower = text.lower()
    found_terms = []

    for term in technical_keywords:
        if term in text_lower:
            found_terms.append(term)

    return list(set(found_terms))


def process_slack_file(file_path: Path) -> List[Dict[str, Any]]:
    """
    Process a single Slack JSON file and create documents.

    Args:
        file_path: Path to the Slack JSON file

    Returns:
        List of document dictionaries
    """
    logger.info(f"Processing file: {file_path.name}")

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Extract messages
        messages = data.get('messages', [])
        if not messages:
            logger.warning(f"No messages found in {file_path.name}")
            return []

        logger.info(f"Found {len(messages)} messages in {file_path.name}")

        # Parse messages and create document
        parsed_data = parse_slack_messages(messages)

        if not parsed_data:
            logger.warning(f"Failed to parse messages from {file_path.name}")
            return []

        # Create document
        doc_id = f"slack_{file_path.stem}"
        title = f"Slack Messages - {file_path.stem}"

        document = {
            'id': doc_id,
            'title': title,
            'content': parsed_data['content'],
            'content_type': 'text/slack',
            'metadata': {
                'source_file': file_path.name,
                'channel': file_path.stem.split('_')[0] if '_' in file_path.stem else 'unknown',
                **parsed_data['metadata']
            }
        }

        # Enhance metadata using SlackMetadataEnhancer
        enhanced_document = enhance_slack_metadata(document, "local_slack")

        logger.info(f"Created document: {title}")
        logger.info(f"  Content length: {len(enhanced_document['content'])} chars")
        logger.info(f"  Messages: {enhanced_document['metadata'].get('message_count', 0)}")
        logger.info(f"  Participants: {len(enhanced_document['metadata'].get('participants', []))}")
        logger.info(f"  Quality score: {enhanced_document['metadata'].get('quality_score', 0):.2f}")

        return [enhanced_document]

    except Exception as e:
        logger.error(f"Error processing {file_path.name}: {str(e)}")
        return []


def main():
    """Main ingestion function."""
    print("🚀 Starting Slack JSON Data Ingestion")
    print("=" * 50)

    # Configuration
    data_dir = Path("data/slack")
    tenant_slug = "default"

    if not data_dir.exists():
        print(f"❌ Data directory not found: {data_dir}")
        return 1

    try:
        # 1. Get or create tenant
        print("📋 Setting up tenant...")
        tenant, created = Tenant.objects.get_or_create(
            slug=tenant_slug,
            defaults={"name": f"Tenant {tenant_slug}"}
        )
        if created:
            print(f"✅ Created new tenant: {tenant_slug}")
        else:
            print(f"📋 Using existing tenant: {tenant_slug}")

        # 2. Create or get document source
        print("📁 Setting up document source...")
        source, created = DocumentSource.objects.get_or_create(
            tenant=tenant,
            name="Slack JSON Files",
            source_type="local_slack",
            defaults={
                "config": {
                    "data_dir": str(data_dir),
                    "file_format": "json"
                }
            }
        )
        if created:
            print(f"✅ Created document source: {source.name}")
        else:
            print(f"📋 Using existing document source: {source.name}")

        # 3. Find JSON files
        print("🔍 Finding JSON files...")
        json_files = list(data_dir.glob("*.json"))

        if not json_files:
            print(f"❌ No JSON files found in {data_dir}")
            return 1

        print(f"📄 Found {len(json_files)} JSON files:")
        for f in json_files:
            print(f"   {f.name}")

        # 4. Process files and create documents
        print("\n📥 Processing files...")
        all_documents = []

        for json_file in json_files:
            documents = process_slack_file(json_file)
            all_documents.extend(documents)

        if not all_documents:
            print("❌ No documents created from JSON files")
            return 1

        print(f"✅ Created {len(all_documents)} documents")

        # 5. Initialize ingestion service
        print("\n🔄 Initializing ingestion service...")
        ingestion_service = UnifiedLlamaIndexIngestionService(tenant_slug)
        print("✅ UnifiedLlamaIndexIngestionService initialized")

        # 6. Ingest documents
        print(f"\n🚀 Ingesting {len(all_documents)} documents...")
        processed = 0
        errors = 0

        for i, doc in enumerate(all_documents):
            try:
                print(f"   Processing {i+1}/{len(all_documents)}: {doc.get('title', 'Untitled')}")

                with transaction.atomic():
                    result = ingestion_service._process_single_document(source, doc)

                    if result:
                        processed += 1
                        print(f"   ✅ Processed successfully")
                    else:
                        print(f"   ⚠️  Skipped (no result)")

            except Exception as e:
                errors += 1
                print(f"   ❌ Error: {str(e)}")
                continue

        # 7. Print final statistics
        print("\n" + "=" * 50)
        print("📊 INGESTION COMPLETED")
        print("=" * 50)
        print(f"📄 Documents created: {len(all_documents)}")
        print(f"✅ Documents processed: {processed}")
        print(f"❌ Errors: {errors}")
        print(f"📈 Success rate: {(processed/len(all_documents)*100):.1f}%")

        if processed > 0:
            print(f"\n🎉 Successfully ingested {processed} Slack documents!")
            print("💡 You can now search this data using the RAG system")
            print("🔍 Try searching for technical discussions, specific users, or topics")
        else:
            print("\n❌ No documents were successfully processed")
            return 1

        return 0

    except Exception as e:
        print(f"\n❌ Fatal error: {str(e)}")
        logger.exception("Fatal error during ingestion")
        return 1


if __name__ == "__main__":
    exit(main())
