{"date": "2024-11-06", "channel_id": "C065QSSNH8A", "message_count": 32, "messages": [{"ts": "1730904039.051659", "text": "Than you for seeing my pain :rolling_on_the_floor_laughing:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1730903630.574069", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "xr0/c", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Than you for seeing my pain "}, {"type": "emoji", "name": "rolling_on_the_floor_laughing", "unicode": "1f923"}]}]}]}, {"ts": "1730903630.574069", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> The +test plan for users in a secondary environment did not work. It says there is no matching user. Do I need to upload new users with the +test email address before I can do this?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1730903630.574069", "reply_count": 11, "blocks": [{"type": "rich_text", "block_id": "PEQhf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " The +test plan for users in a secondary environment did not work. It says there is no matching user. Do I need to upload new users with the +test email address before I can do this?"}]}]}]}, {"ts": "1730895665.382459", "text": "Bonus requirements we can have it tomorrow as we have a working session today.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "tjBbX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bonus requirements we can have it tomorrow as we have a working session today."}]}]}]}, {"ts": "1730895624.128399", "text": "*Agenda for today's call:*\n• Budget issues reported yesterday\n• <https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=499510446#gid=499510446|Customer requirements / priorities> for upcoming cycles", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "R6s7n", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today's call:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Budget issues reported yesterday"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=499510446#gid=499510446", "text": "Customer requirements / priorities"}, {"type": "text", "text": " for upcoming cycles"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1730895368.693179", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> <@U04DKEFP1K8> Priority list for upcoming cycles after Nov are added here in this sheet (*Jan 25 Cycle Priorities).* We can go over this today. I will working on estimations and ETA tomorrow.\n<https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=499510446#gid=499510446>", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "IhxrT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Priority list for upcoming cycles after Nov are added here in this sheet ("}, {"type": "text", "text": "Jan 25 Cycle Priorities). ", "style": {"bold": true}}, {"type": "text", "text": "We can go over this today. I will working on estimations and ETA tomorrow.\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=499510446#gid=499510446"}]}]}]}, {"ts": "1730869102.051109", "text": "<@U0690EB5JE5> here is the file with currencies of international employees", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730869102.051109", "reply_count": 4, "files": [{"id": "F07UYSLDAER", "created": 1730869084, "timestamp": 1730869084, "name": "Stride Template (1).xlsx", "title": "Stride Template (1).xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 177398, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07UYSLDAER/stride_template__1_.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07UYSLDAER/download/stride_template__1_.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UYSLDAER-bb9b237759/stride_template__1__converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UYSLDAER-bb9b237759/stride_template__1__thumb_pdf.png", "thumb_pdf_w": 935, "thumb_pdf_h": 1210, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07UYSLDAER/stride_template__1_.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07UYSLDAER-09f4e8fa5f", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "3jGtc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " here is the file with currencies of international employees"}]}]}]}, {"ts": "1730868836.670719", "text": "first file I showed you", "user": "U07M6QKHUC9", "type": "message", "files": [{"id": "F080AFYTMGQ", "created": 1730868829, "timestamp": 1730868829, "name": "CompensationDataTemplate (5).csv", "title": "CompensationDataTemplate (5).csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": true, "size": 10056, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F080AFYTMGQ/compensationdatatemplate__5_.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F080AFYTMGQ/download/compensationdatatemplate__5_.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F080AFYTMGQ/compensationdatatemplate__5_.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F080AFYTMGQ-81b1601010", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F080AFYTMGQ/compensationdatatemplate__5_.csv/edit", "preview": "Employee Id,Employee Name (Read Only),Annual Salary Currency,Annual Salary (Currency),Annual Salary-Other (Currency),Variable Pay Currency,Variable Pay (%),Variable Pay (Currency),Target Bonus Currency,Target Bonus (%),Target Bonus (Currency),Last Raise Date,Previous Year Salary,Annual Salary OTE (Currency),Pay Mix,Hourly Rate,\"Update Type (NC,A,D,U)\"\r\n234,<PERSON>,USD,91416,0,,0,0,USD,0,0,,0,0,0,0,NC\r\n326,<PERSON>,USD,125000,0,,0,0,USD,0,0,,0,0,0,0,NC\r\n167,<PERSON>,USD,90537,0,,0,0,USD,0,0,,0,0,0,0,NC\r\n172,<PERSON>,USD,95000,0,,0,0,USD,0,0,,0,0,0,0,NC\r\n57,<PERSON>,USD,135000,0,,0,0,USD,0,0,,0,0,0,0,<PERSON>\r\n13,<PERSON>,USD,46945.6,0,,0,0,USD,0,0,,0,0,0,0,NC\r\n332,<PERSON>,USD,43908.8,0,,0,0,USD,0,0,,0,0,0,0,NC\r\n232,<PERSON><PERSON>,<PERSON>,51251.2,0,,0,0,USD,0,0,,0,0,0,0,NC\r\n134,Terese Tye,USD,141364.93,0,,0,0,USD,0,0,,0,0,0,0,NC\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Employee Id</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary (Currency)</div><div class=\"cm-col\">Annual Salary-Other (Currency)</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay (Currency)</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus (Currency)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE (Currency)</div><div class=\"cm-col\">Pay Mix</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Update Type (NC,A,D,U)</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">234</div><div class=\"cm-col\">David Wickstrom</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">91416</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">NC</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">326</div><div class=\"cm-col\">Nick White</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">125000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">NC</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">167</div><div class=\"cm-col\">Bradley White</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">90537</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">NC</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">172</div><div class=\"cm-col\">Cory Walz</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">95000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">NC</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">57</div><div class=\"cm-col\">Cory Wadstrom</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">135000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">NC</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">13</div><div class=\"cm-col\">Tyler Viers</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">46945.6</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">NC</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">332</div><div class=\"cm-col\">Tori Vailes</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">43908.8</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">NC</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">232</div><div class=\"cm-col\">Daniel Vailes</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">51251.2</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">NC</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">134</div><div class=\"cm-col\">Terese Tye</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">141364.93</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">NC</div></div></div>\n</div>\n", "lines": 171, "lines_more": 161, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "fSiQ8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "first file I showed you"}]}]}]}, {"ts": "1730867950.177839", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1730867946.333329", "text": "<@U07M6QKHUC9> Talking to <PERSON><PERSON>  wrapping up in 2 mnts.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "JtV2H", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Talking to <PERSON><PERSON>  wrapping up in 2 mnts."}]}]}]}, {"ts": "1730867886.111799", "text": "<@U0690EB5JE5> Can you join the call\n<https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730867886.111799", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Gmjab", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you join the call\n"}, {"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1730856640.354149", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> Can I find this recording on drive?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730831784.540979", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "kP1qq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Can I find this recording on drive"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1730856290.917559", "text": "<@U07M6QKHUC9> demo ENV is not updated with latest fixes. Alayacare, they had edited them manually and wouldn’t match the budgets calculated. I will confirm that later.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730837354.684819", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "1OlNT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " demo ENV is not updated with latest fixes. Alayacare, they had edited them manually and "}, {"type": "text", "text": "wouldn’t"}, {"type": "text", "text": " match the budgets calculated. I will confirm that later."}]}]}]}, {"ts": "1730855591.507569", "text": "<@U07M6QKHUC9> I will go through each ticket and reply on the ticket.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "aabYM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I will go through each ticket and reply on the ticket."}]}]}]}, {"ts": "1730853010.377209", "text": "<https://compiify.atlassian.net/browse/COM-3966>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14049::7e4ff3d56ed54d20bfeca6188d95e3d8", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3966?atlOrigin=eyJpIjoiNWI2Y2MxNmVlNTMyNDE5YmJhYzMxOWJiZjY0NjVhM2YiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3966 Update Terminology on Data Upload Screen>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14049::1d0d6d849dc34846b23381bb067c95a2", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14049\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3966\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3966", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "uFqK+", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3966"}]}]}]}, {"ts": "1730852086.105629", "text": "<@U0690EB5JE5> Can we meet at 9 pm or 9:30 pm to go over the data upload issues I am experiencing for Tithely?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730852086.105629", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "vqxxr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can we meet at 9 pm or 9:30 pm to go over the data upload issues I am experiencing for Tithely?"}]}]}]}, {"ts": "1730850612.061149", "text": "<https://compiify.atlassian.net/browse/COM-3965>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14048::51532a37f4a143c5a985677a419482b0", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3965?atlOrigin=eyJpIjoiMTkwMzNiZGJkYjkzNDQ5NmI3MGM3OTM4MDA5MTU4ZWMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3965 Error Message Not Showing Location in Comp Data Upload>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14048::7946c50bb891410bb9b32fc1019a363d", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14048\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3965\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3965", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "IMbyF", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3965"}]}]}]}, {"ts": "1730845562.052619", "text": "<@U0690EB5JE5> <https://compiify.atlassian.net/browse/COM-3964>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730845562.052619", "reply_count": 3, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14047::3111e75beba5476485d9e1d65fe1fb03", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3964?atlOrigin=eyJpIjoiYTcxMDQ1MzEwMmM1NDMzN2I0OTg0OTgxZGY4NzJiNTgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3964 Improve Login Options for Customers>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14047::4aa8eafe65e74f17b90f97c1e42a68e5", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14047\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3964\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3964", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "l6R6c", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3964"}]}]}]}, {"ts": "1730839967.825179", "text": "<https://compiify.atlassian.net/browse/COM-3963>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14046::6767bb33ff204109a2aa43696b80d216", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3963?atlOrigin=eyJpIjoiNDFkMTA0Zjk4ZDhkNDAxOGI2MTJmODMyMzFiOWYxOTMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3963 Issue: Discrepancy in Employee Data Display in Merit Planning View>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14046::00e0baa29c6f40a48c537954d461b3e2", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14046\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3963\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3963", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "13Riz", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3963"}]}]}]}, {"ts": "1730839435.271619", "text": "<@U0690EB5JE5> unable to filter by location in merit planning view\n<https://compiify.atlassian.net/browse/COM-3962>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14045::437394d121d04fa68ba3c1b234900ed3", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3962?atlOrigin=eyJpIjoiOTNkNjM1ZDFkMmFjNDkyYTg2OTgyYWI3NjEyNDVkMGQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3962 Unable to Filter by Location in Merit Planning View>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14045::a4e4290e542c402d8b03ccf89ffd53a7", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14045\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3962\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3962", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "7Q30d", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " unable to filter by location in merit planning view\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3962"}]}]}]}, {"ts": "1730839180.335479", "text": "<@U0690EB5JE5> created a jira to add eligibility status in emp data export\n<https://compiify.atlassian.net/browse/COM-3961>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730839180.335479", "reply_count": 2, "edited": {"user": "U07M6QKHUC9", "ts": "1730839193.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14044::7195a6e45d7a4e669350d3084a53e73b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3961?atlOrigin=eyJpIjoiOGIxOWE4Mjk4MDNkNDliYWExYjVmMjRhZDY1YjRjY2EiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3961 Add Column for Eligibility Status in Employee Data Export Report>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14044::b0e23e9751134e78b4d5f96d641e4e99", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14044\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3961\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3961", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "wnn7U", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " created a jira to add eligibility status in emp data export\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3961"}]}]}]}, {"ts": "1730837967.748699", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> did we do the QA testing on comp builder calculations for AlayaCare and if those calculations were matching the reports?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5FYHm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " did we do the QA testing on comp builder calculations for AlayaCare and if those calculations were matching the reports?"}]}]}]}, {"ts": "1730837819.810939", "text": "yes looking into frontend state issue", "user": "U06HN8XDC5A", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "bjtfg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes looking into frontend state issue"}]}]}]}, {"ts": "1730837788.134109", "text": "<@U06HN8XDC5A> is this something you are able to fix?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730837788.134109", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "XZIRZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " is this something you are able to fix?"}]}]}]}, {"ts": "1730837742.442659", "text": "<@U0690EB5JE5> <@U07EJ2LP44S> some major calculation issues in AlayaCare\n<https://compiify.atlassian.net/browse/COM-3960>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730837742.442659", "reply_count": 13, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14043::e356b88de1314fe4b2dea277f3fd5114", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3960?atlOrigin=eyJpIjoiZDE0NTUzODg1MDZmNDRhNGIxM2NjNjJlM2U2OGRmYWUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3960 Incorrect Total Salaries Calculation Issue>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14043::355f6b4fa09241c392f5825dfbadcb1a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14043\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3960\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3960", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "NYtny", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " some major calculation issues in AlayaCare\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3960"}]}]}]}, {"ts": "1730837354.684819", "text": "Also all of the above issues that I encountered in demo UAT are likely present in AlayaCare as well. <@U07EJ2LP44S> FYA", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730837354.684819", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "XI78t", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also all of the above issues that I encountered in demo UAT are likely present in AlayaCare as well. "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " FYA"}]}]}]}, {"ts": "1730837052.002759", "text": "I am not seeing similar calculations errors in SDF test env, how is that possible?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730837052.002759", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "l31q6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am not seeing similar calculations errors in SDF test env, how is that possible?"}]}]}]}, {"ts": "1730836871.953749", "text": "<@U0690EB5JE5> there are budget calculations errors in AlayaCare Prod env as well. They needs to be fixed asap\n<https://compiify.atlassian.net/browse/COM-3959>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730836871.953749", "reply_count": 5, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14042::7862608c29aa4b9380a18dff819c5f77", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3959?atlOrigin=eyJpIjoiMjU2ZDU4YWI4YTA5NDJiNWFkYTExYTc2MzI5ZmEwYmYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3959 Inconsistency in Salary Sum Calculation in AlayaCare Prod Environme…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14042::9975eff9eb094f2b910f627136338ecc", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14042\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3959\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3959", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "TWTCh", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " there are budget calculations errors in AlayaCare Prod env as well. They needs to be fixed asap\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3959"}]}]}]}, {"ts": "1730836391.746249", "text": "<@U0690EB5JE5> reporting bonus budget calculation errors in demo UAT\n<https://compiify.atlassian.net/browse/COM-3958>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14041::6f38830f386d457db2177838d7400513", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3958?atlOrigin=eyJpIjoiZGY5MThkMzQxMGFmNDg3NGE2NTQ4ODNkODhlZWNlNjYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3958 Calculation Errors in Bonus Planning>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14041::bfd2dbb748ab4622a2620d1cdd115570", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14041\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3958\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3958", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "EtbQU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " reporting bonus budget calculation errors in demo UAT\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3958"}]}]}]}, {"ts": "1730835525.277919", "text": "<@U0690EB5JE5> added a bug for calculation inconsistency in rounding off during currency conversion\n<https://compiify.atlassian.net/browse/COM-3956>", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1730835539.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14039::af2ffc55266c45bc858fc13c56e769ac", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3956?atlOrigin=eyJpIjoiNWU5NGRkYWI5N2MwNDIxNmI4YTZiMDc0MjJjM2FiNTgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3956 Inconsistent Rounding Issue in Currency Conversion>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14039::381bf351686a4ccc8ad686a6b1ff3ead", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14039\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3956\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3956", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Cds1a", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " added a bug for calculation inconsistency in rounding off during currency conversion\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3956"}]}]}]}, {"ts": "1730831929.734719", "text": "<@U07EJ2LP44S> T<PERSON><PERSON> has requested to re-send login credentials for <PERSON>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WPmqX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> has requested to re-send login credentials for <PERSON>"}]}]}]}, {"ts": "1730831879.786709", "text": "<@U04DKEFP1K8> tithely has completed the SSO instructions and they are in your inbox.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730831879.786709", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "DMKGS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " tithely has completed the SSO instructions and they are in your inbox."}]}]}]}, {"ts": "1730831784.540979", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> Great call with C<PERSON>na. I think we should discuss among outselves how we want to handle their budget's set up and allocation workflow? It feels like there is a scope for improvement on how we do the budget set up today, and this is a great opportunity to do that", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730831784.540979", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "S5Knh", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Great call with <PERSON><PERSON><PERSON>. I think we should discuss among outselves how we want to handle their budget's set up and allocation workflow? It feels like there is a scope for improvement on how we do the budget set up today, and this is a great opportunity to do that"}]}]}]}], "created_at": "2025-05-22T21:35:34.671987"}