#!/usr/bin/env python3
"""
Check user tenant configuration and fix if needed.
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.accounts.models import User, Tenant
from apps.documents.models import DocumentChunk, EmbeddingMetadata

def check_user_tenant():
    """Check user tenant configuration."""
    print("🔍 Checking User Tenant Configuration")
    print("=" * 50)

    # Check user
    user = User.objects.filter(email='<EMAIL>').first()
    if not user:
        print("❌ User not found")
        return

    print(f"✅ User found: {user.email}")
    print(f"   User ID: {user.id}")
    print(f"   Username: {user.username}")

    # Check user's tenant
    if hasattr(user, 'tenant'):
        print(f"   User tenant: {user.tenant.slug}")
    else:
        print("   ❌ User has no tenant attribute")

    # Check all tenants
    print(f"\n🏢 Available tenants:")
    tenants = Tenant.objects.all()
    for tenant in tenants:
        print(f"   - {tenant.slug} (ID: {tenant.id})")

        # Count chunks in each tenant
        chunk_count = DocumentChunk.objects.filter(tenant=tenant).count()
        curana_count = DocumentChunk.objects.filter(tenant=tenant, text__icontains='curana').count()
        print(f"     Chunks: {chunk_count}, Curana chunks: {curana_count}")

    # Check EmbeddingMetadata
    print(f"\n📊 EmbeddingMetadata status:")
    total_embeddings = EmbeddingMetadata.objects.count()
    print(f"   Total embedding metadata records: {total_embeddings}")

    # Check if problematic node IDs exist in EmbeddingMetadata
    problematic_node_ids = [
        'b682eb0b-a168-4e20-8e14-06ceaff7bc2e',
        '5a3df087-7ebe-4540-9ccc-736202b54775',
        '7d54f5e6-b189-4c2d-b5eb-c0645f3ab7ad'
    ]

    print(f"\n🔍 Checking problematic node IDs in EmbeddingMetadata:")
    for node_id in problematic_node_ids:
        embedding = EmbeddingMetadata.objects.filter(vector_id=node_id).first()
        if embedding:
            print(f"   ✅ {node_id} -> Chunk {embedding.chunk.id} (tenant: {embedding.chunk.tenant.slug})")
        else:
            print(f"   ❌ {node_id} not found in EmbeddingMetadata")

    return user, tenants

def fix_user_tenant():
    """Fix user tenant if needed."""
    print(f"\n🔧 Checking if user tenant needs fixing...")

    user = User.objects.filter(email='<EMAIL>').first()
    stride_tenant = Tenant.objects.filter(slug='stride').first()

    if not user or not stride_tenant:
        print("❌ User or stride tenant not found")
        return

    # Check if user has a profile
    if hasattr(user, 'profile'):
        profile = user.profile
        print(f"   Current profile tenant: {profile.tenant.slug}")

        if profile.tenant == stride_tenant:
            print("✅ User profile is already in stride tenant")
        else:
            print("🔧 Setting user profile to stride tenant...")
            profile.tenant = stride_tenant
            profile.save()
            print("✅ User profile tenant updated to stride")
    else:
        print("🔧 Creating user profile with stride tenant...")
        from apps.accounts.models import UserProfile
        UserProfile.objects.create(user=user, tenant=stride_tenant)
        print("✅ User profile created with stride tenant")

    return True

if __name__ == "__main__":
    user, tenants = check_user_tenant()
    fix_user_tenant()
