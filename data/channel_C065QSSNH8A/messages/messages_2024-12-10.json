{"date": "2024-12-10", "channel_id": "C065QSSNH8A", "message_count": 11, "messages": [{"ts": "1733850416.561889", "text": "Diviersified bonus award field to be made uneditable request: <https://compiify.atlassian.net/browse/COM-4025>", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14108::1eb73cc5381b42eb85dcb434e1efbecb", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4025?atlOrigin=eyJpIjoiM2ExNjYyNjI0NWNlNDYxZjgyZDA2N2E1ODMwNTc5ZTciLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4025 Request to Make Bonus Award Section Non-Editable>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14108::e1de495789f84b65be4151b6886cd56e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14108\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4025\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4025", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "VaglR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diviersified bonus award field to be made uneditable request: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4025"}]}]}]}, {"ts": "1733850017.664969", "text": "Tithely UATs combined and new tickets created: <https://compiify.atlassian.net/browse/COM-3946>", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}, {"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14029::75cc7554f3e54ff78b2a7e03d1835e35", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3946?atlOrigin=eyJpIjoiOGI2MzZkOTU5MjViNDhjZDhlZGQ2NWQ4NDlmMWE2YTMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3946 Tithely UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14029::7e4f561343cf4ff18b34301a540df6ab", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14029\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3946\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3946", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "R9OWT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Tithely UATs combined and new tickets created: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3946"}]}]}]}, {"ts": "1733846836.871939", "text": "<@U0690EB5JE5> <@U07EJ2LP44S> <https://docs.google.com/document/d/103EIDBRmuDFa83boLIshQnPc2E87ze1s-lBbBb36qX0/edit?tab=t.0|Total Rewards PRD for review>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733846836.871939", "reply_count": 2, "edited": {"user": "U07M6QKHUC9", "ts": "1733848392.000000"}, "files": [{"id": "F0855GY4PSL", "created": 1733848394, "timestamp": 1733848394, "name": "Total Rewards PRD_Dec 9 2024", "title": "Total Rewards PRD_Dec 9 2024", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 182762, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "103EIDBRmuDFa83boLIshQnPc2E87ze1s-lBbBb36qX0", "external_url": "https://docs.google.com/document/d/103EIDBRmuDFa83boLIshQnPc2E87ze1s-lBbBb36qX0/edit?tab=t.0", "url_private": "https://docs.google.com/document/d/103EIDBRmuDFa83boLIshQnPc2E87ze1s-lBbBb36qX0/edit?tab=t.0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTSPXqRSYP98/pQ2D1/lQF44JoAUcd80uRSbfc0bfegBaKKKAGt196Bk9xQ2PWkC980AO5/vUc+tJt9x+VKAPWgBaKKKAGt1oGPWlbpTB1oAfketHFG4UZoAWiiigD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0855GY4PSL/total_rewards_prd_dec_9_2024", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Is/m+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://docs.google.com/document/d/103EIDBRmuDFa83boLIshQnPc2E87ze1s-lBbBb36qX0/edit?tab=t.0", "text": "Total Rewards PRD for review"}]}]}]}, {"ts": "1733792807.347489", "text": "Are we already agreed on completing every item on the prd lists? I’m not clear on what we are going to definitely achieve and the timeline. I can cross check these requirements with <PERSON>‘s list, but I’d like to get clarity on what we’re actually doing.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "iZwZ/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Are we already agreed on completing every item on the prd lists? I’m not clear on what we are going to definitely achieve and the timeline. I can cross check these requirements with <PERSON>‘s list, but I’d like to get clarity on what we’re actually doing."}]}]}]}, {"ts": "1733791313.113279", "text": "<@U07EJ2LP44S> can you list which of the items in Diversified's are not covered by <PERSON>'s Phase 1- Epic 1 &amp; 2 PRDs so <PERSON><PERSON><PERSON> has clarity on what additional work needs to be done. We should create tickets for those items.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733498510.956169", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "ck+oT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can you list which of the items in Diversified's are not covered by <PERSON>'s Phase 1- Epic 1 & 2 PRDs so <PERSON><PERSON><PERSON> has clarity on what additional work needs to be done. We should create tickets for those items."}]}]}]}, {"ts": "1733782850.273249", "text": "I will ask curana, they are incredibly busy right now. <PERSON> might have more time, I’m not sure.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xObIn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will ask curana, they are incredibly busy right now. <PERSON> might have more time, I’m not sure."}]}]}]}, {"ts": "1733782415.524489", "text": "<@U07EJ2LP44S> can we pls set up a discovery call with <PERSON><PERSON><PERSON> and Curana to understand how their process of creating pay bands manually? We need to understand their process to guide our PRD. I will also have <PERSON> join those calls as she would be helping in on this projects on as-needed basis", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Y5ls2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can we pls set up a discovery call with <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> to understand how their process of creating pay bands manually? We need to understand their process to guide our PRD. I will also have <PERSON> join those calls as she would be helping in on this projects on as-needed basis"}]}]}]}, {"ts": "1733781419.875869", "text": "Feature request for curana: <https://compiify.atlassian.net/browse/COM-4022> I think it would be good to go over both Di<PERSON>ified and <PERSON><PERSON><PERSON>'s feedback in the call tomorrow.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14105::44f918d2389d40b590078772f270a4d3", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4022?atlOrigin=eyJpIjoiNzhjNTAwY2Q2NGY5NGU2OWE0NjFiNDJlNTE4M2JhNWYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4022 Feature Request: Market Adjustment Field Default Setting>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14105::269f3dcfdcc848e1a26a68deca258aa6", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14105\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4022\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4022", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "liSJC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Feature request for curana: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4022"}, {"type": "text", "text": " I think it would be good to go over both Di<PERSON><PERSON> and <PERSON><PERSON><PERSON>'s feedback in the call tomorrow."}]}]}]}, {"ts": "1733780598.741879", "text": "<@U0690EB5JE5> Unless you have other agenda items for Tomorrow's standup, we can go over the PRD for total rewards module. First draft should be ready by tomorrow", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733780598.741879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "lQkdX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Unless you have other agenda items for Tomorrow's standup, we can go over the PRD for total rewards module. First draft should be ready by tomorrow"}]}]}]}, {"ts": "1733775569.361389", "text": "<@U0690EB5JE5> assuming the current number of active environments we have, do you have an idea of how much our AWS costs will go down by after we have implemented tenancy in Jan. Need this info to determine how much in AWS credits we need for 2025? they are either use it or loose it.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733775569.361389", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "bD1iS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " assuming the current number of active environments we have, do you have an idea of how much our AWS costs will go down by after we have implemented tenancy in Jan. Need this info to determine how much in AWS credits we need for 2025? they are either use it or loose it."}]}]}]}, {"ts": "1733774437.057339", "text": "Add ability to filter by bonus eligible: <https://compiify.atlassian.net/browse/COM-4021>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14104::e96ead8d4f9e4d00ad8847843cb30562", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4021?atlOrigin=eyJpIjoiMTY2MzJlODQxY2E3NDEyNDgxZDMxNGM3ZWE4ZDVmZDMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4021 Add Filter for Bonus Eligible Employees>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14104::0eec75519e08430aa9df54769c1bc429", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14104\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4021\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4021", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "mIzSJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Add ability to filter by bonus eligible: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4021"}]}]}]}], "created_at": "2025-05-22T21:35:34.679590"}