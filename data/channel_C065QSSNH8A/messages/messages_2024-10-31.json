{"date": "2024-10-31", "channel_id": "C065QSSNH8A", "message_count": 18, "messages": [{"ts": "1730395798.350469", "text": "<@U0690EB5JE5> Starting Monday, we really need someone from engineering to be available to provide support EVERYDAY for the days we have active cycles running for customers until 2 pm pst", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1730432112.000000"}, "blocks": [{"type": "rich_text", "block_id": "XKvFV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Starting Monday, we really "}, {"type": "text", "text": "need "}, {"type": "text", "text": "someone from engineering to be available to provide support EVERYDAY"}, {"type": "text", "text": " for the days we have active cycles running for customers"}, {"type": "text", "text": " until 2 pm pst"}]}]}]}, {"ts": "1730395609.342529", "text": "I am not sure if I feel comfortable in creating a new cycle for Alaya Care.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730395609.342529", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Xr42e", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am not sure if I feel comfortable in creating a new cycle for Alaya Care."}]}]}]}, {"ts": "1730395568.985459", "text": "<@U0690EB5JE5> can we have someone to fix these issues immediately? <@U04DKEFP1K8> is going to send the loom videos", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Dqqak", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we have someone to fix these issues immediately? "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " is going to send the loom videos"}]}]}]}, {"ts": "1730395567.881259", "text": "<@U04DKEFP1K8> do we have an idea what's going wrong here? we are needing to do this same thing for alayacare - delete a cycle and launch a new one", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1730395567.881259", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "m3036", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " do we have an idea what's going wrong here? we are needing to do this same thing for alayacare - delete a cycle and launch a new one"}]}]}]}, {"ts": "1730395293.831489", "text": "How do you propose we tell them that? We can't push them off any longer", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DLlTH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "How do you propose we tell them that? We can't push them off any longer"}]}]}]}, {"ts": "1730395255.457319", "text": "<@U07EJ2LP44S> we are not ready to handover SDF test env to the customer. There are too many bugs and breakages after creating the new cycle", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "geuCT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " we are not ready to handover SDF test env to the customer. There are too many bugs and breakages after creating the new cycle"}]}]}]}, {"ts": "1730393702.047449", "text": "Just lost internet ", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "rjbfh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just lost internet "}]}]}]}, {"ts": "1730382194.779829", "text": "<@U04DKEFP1K8> fixes went in today's deployment\n• Count issue on cycle builder eligibility page\n• Republishing the cycle fixed the hourly rate prorated salary rounding issue. Looks like it was backfill issue\n• There were budget calculation issues and allocate page issues which are fixed. The percentages on the merit task page would have slight changes now vs yesterday as the earlier numbers were incorrect\n• Cycle creation issue\nI won't be able to join the call today. Available on slack for any urgent issue.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1730387239.000000"}, "blocks": [{"type": "rich_text", "block_id": "mKiqp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "text", "text": "fixes"}, {"type": "text", "text": " went in today's deployment\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Count issue on cycle builder eligibility page"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Republishing the cycle fixed the hourly rate prorated salary rounding issue. Looks like it was backfill issue"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "There were budget calculation issues and allocate page issues which are fixed. The percentages on the merit task page would have slight changes now vs yesterday as the earlier numbers were incorrect"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle creation issue"}]}], "style": "bullet", "indent": 0, "offset": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nI won't be able to join the call today. Available on slack for any urgent issue."}]}]}]}, {"ts": "1730382019.196179", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> now <http://qa.stridehr.io|qa.stridehr.io> is replica of <http://alayacare.stridehr.io|alayacare.stridehr.io>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730344990.381399", "subtype": "thread_broadcast", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Rm1U3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " now "}, {"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}, {"type": "text", "text": " is replica of "}, {"type": "link", "url": "http://alayacare.stridehr.io", "text": "alayacare.stridehr.io"}]}]}]}, {"ts": "1730380780.122599", "text": "<@U07EJ2LP44S> Deployment is complete. Please note I had to publish cycle to fix the budget issues.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730344990.381399", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "HhoDv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Deployment is complete. Please note I had to publish cycle to fix the budget issues."}]}]}]}, {"ts": "1730379337.048489", "text": "<@U07EJ2LP44S> Deployment started. Will update here once complete.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730344990.381399", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "DOVdh", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Deployment started. Will update here once complete."}]}]}]}, {"ts": "1730348742.947699", "text": "Thanks <@U04DKEFP1K8> and <@U0690EB5JE5> ", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4uT0k", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " and "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1730344990.381399", "text": "<@U0690EB5JE5> <PERSON> and I have committed to <PERSON> that allocate page fix will be deployed before her working day starts in est. Can we deploy the fix in production and work on COM-3941 in parallel? Is it doable?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1730344990.381399", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "jKL5A", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON> and I have committed to <PERSON> that allocate page fix will be deployed before her working day starts in est. Can we deploy the fix in production and work on COM-3941 in parallel? Is it doable?"}]}]}]}, {"ts": "1730335815.203459", "text": "<@U0690EB5JE5> While i was able to test allocate page related updates successfully , i am unable to create a new cycle on the branch you have shared. have raised <https://compiify.atlassian.net/browse/COM-3941>\nNote: Alayacare team wants to delete currently active cycle and create a new one so need to identify if this is an issue on private build or it exists on production build as well. Check loom for details.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1730335815.203459", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14024::bb6605443e1a4ae797bd28e9c96ff8b1", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3941?atlOrigin=eyJpIjoiNTk5NGE4NzAzZWZjNDA5ZThmYWRkYzc2YzI0NmVmZGIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3941 Issue: Unable to create a new cycle after closing an active cycle>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14024::e319ed10b8af4d59b9f0ee93575bce22", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14024\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3941\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3941", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "pUHwk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " While i was able to test allocate page related updates successfully , i am unable to create a new cycle on the branch you have shared. have raised "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3941"}, {"type": "text", "text": "\nNote: Alayacare team wants to delete currently active cycle and create a new one so need to identify if this is an issue on private build or it exists on production build as well. Check loom for details."}]}]}]}, {"ts": "1730324851.035149", "text": "Notes from alayacare meeting:\n1. <PERSON><PERSON> allocate page fix ( testing on the private build looks positive)\n2. New issue reported <https://compiify.atlassian.net/browse/COM-3939> ( nice to have)\n3. New issue reported <https://compiify.atlassian.net/browse/COM-3940> ( must fix)\n 4. <PERSON> will request us tomorrow sometime to delete current cycle ( prepped for testing) and activate the next cycle", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1730324851.035149", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14022::4cbf328e93e44047af855791e23cc484", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3939?atlOrigin=eyJpIjoiODQwZjZmOTU5NWYwNDcyY2FjNGQ5Nzg3NDFmNTVhY2EiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3939 Display Employee Salary in Local Currency on View Details Page>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14022::9dac6f6f56f14e65ad5c1e034c57588c", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14022\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3939\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3939", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:14023::b3764bc5be1044de841d3bfd09d62f96", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3940?atlOrigin=eyJpIjoiMjczZDMzMmU3YmQ1NDVlODk4ODM1Y2E4YmFkMWViNmQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3940 Inconsistent Flagging Issue for Proration Factor>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14023::01e7c645ef8040839f20a1695ce2bf90", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14023\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3940\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3940", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "8aR9d", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Notes from alayacare meeting:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Push allocate page fix ( testing on the private build looks positive)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "New issue reported "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3939"}, {"type": "text", "text": " ( nice to have)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "New issue reported "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3940"}, {"type": "text", "text": " ( must fix)"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": " 4. <PERSON> will request us tomorrow sometime to delete current cycle ( prepped for testing) and activate the next cycle"}]}]}]}, {"ts": "**********.357869", "text": "<@U07EJ2LP44S> i have raised <https://compiify.atlassian.net/browse/COM-3938> for the issue related to uploading curanahealth comp data with <PERSON><PERSON><PERSON>. There is a workaround that can be applied which is present in the loom but i would let <PERSON><PERSON><PERSON> and team triage the issue before applying it", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.357869", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14021::500aea9ee94045c0a8b8dff570a589ef", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3938?atlOrigin=eyJpIjoiMzAzY2E0MDI1YTA0NDY4M2FmNmY4NDczZWEyYjY4NjEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3938 Issue: Error uploading limited number of employees' salary data in …>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14021::64303273cb464bbfb8817b9bf43dcff4", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14021\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3938\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3938", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "ExZS3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " i have raised "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3938"}, {"type": "text", "text": " for the issue related to uploading curanahealth comp data with <PERSON><PERSON><PERSON>. There is a workaround that can be applied which is present in the loom but i would let <PERSON><PERSON><PERSON> and team triage the issue before applying it"}]}]}]}, {"ts": "**********.169699", "text": "<@U04DKEFP1K8> can you pls also share the loom for incorrect cycle start and end dates in the comp cycle builder?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.169699", "reply_count": 1, "edited": {"user": "U07M6QKHUC9", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "WeKOR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you pls also share the loom for incorrect cycle start and end dates in the comp cycle builder?"}]}]}]}, {"ts": "**********.158319", "text": "<@U0690EB5JE5> here are the jira's\n<https://compiify.atlassian.net/browse/COM-3943>\n<https://compiify.atlassian.net/browse/COM-3942>\n<https://compiify.atlassian.net/browse/COM-3944>", "user": "U04DKEFP1K8", "type": "message", "edited": {"user": "U04DKEFP1K8", "ts": "1730398458.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14026::84d5a50930ee4107ad4249e03b7f8a10", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3943?atlOrigin=eyJpIjoiMmQwODEwOTAwZWM0NGM2ZDkyNzgyZWVjMTI1ZTYwNzYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3943 Issue: Unable to access plants in new cycle on SDF test>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14026::f44174de3ee04d5f96a6cc47d19a83f4", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14026\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3943\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3943", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:14025::98c3a3dc23a34327bd015ea35db354df", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3942?atlOrigin=eyJpIjoiMzk0OGFlNGFlZmQwNDdhZmIxYWI1MjU5MzhkNzkxZTciLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3942 Discrepancy in Budget Calculation for Managers in SDF Test Environm…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14025::2e4f11d7117d4bef84488a59e06f598d", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14025\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3942\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3942", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 3, "blocks": [{"type": "section", "block_id": "uf:ih:14027::dfab15e6ed4c440f9b32914e21ee6635", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3944?atlOrigin=eyJpIjoiYmI1MDA4NGViNmIyNDAyZTgxN2NiNWNmMjdiOTIwYWIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3944 Incorrect Start Date Displayed on Compensation Cycle Page>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14027::aedf39e53eec43beae735bff414b923a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14027\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3944\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3944", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "lXqne", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " here are the jira's\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3943"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3942"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3944"}]}]}]}], "created_at": "2025-05-22T21:35:34.656809"}