{"date": "2024-01-27", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1706322925.876529", "text": "<@U0658EW4B8D> Whenever you have a chance to respond, a general question about adjustment letters:\n• Do you anticipate _any_ customer who wants different templates/language for a \"merit increase\" vs a \"market adjustment\"? (Would they ever tell an employee their raise is a \"market adjustment, or split that out separately?)\n• Would _any_ customer need to communicate both a \"bonus award\" _and_ \"one time bonus\" for the same cycle? To the same employee?\n• Would any customer need to specify the difference in \"refresh equity\" and \"promotion equity\" for the same employee?\n• Are OTE communications generally different from non-OTE? For example, do they (almost) always need to communicate \"new\" salary &amp; variable amount (or OTE) for any comp adjustment?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706322925.876529", "reply_count": 1, "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ffQDl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " Whenever you have a chance to respond, a general question about adjustment letters:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you anticipate "}, {"type": "text", "text": "any", "style": {"italic": true}}, {"type": "text", "text": " customer who wants different templates/language for a \"merit increase\" vs a \"market adjustment\"? (Would they ever tell an employee their raise is a \"market adjustment, or split that out separately?)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Would "}, {"type": "text", "text": "any ", "style": {"italic": true}}, {"type": "text", "text": "customer need to communicate both a \"bonus award\" "}, {"type": "text", "text": "and", "style": {"italic": true}}, {"type": "text", "text": " \"one time bonus\" for the same cycle? To the same employee?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Would any customer need to specify the difference in \"refresh equity\" and \"promotion equity\" for the same employee?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Are OTE communications generally different from non-OTE? For example, do they (almost) always need to communicate \"new\" salary & variable amount (or OTE) for any comp adjustment?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "**********.477929", "text": "Jotting this thought down before I forget: I've noticed a few times lately where the current product isn't displaying well on smaller screens.\n• During Wonolo demo, I had it configured with 6 budget types and the HR Admin view couldn't adequately show the different departments\n• <PERSON> was trying to view her account on her laptop, and the collapsed navigation panel had the \"&gt;&gt;\" icon overlapping with the Total Rewards icon, so it wasn't clear how to open it up again\nWe'll (eventually?) need a better plan for making these pages flexible for smaller screens.", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F06FYHEBC92", "created": **********, "timestamp": **********, "name": "Screenshot 2024-01-26 at 4.36.33 PM.png", "title": "Screenshot 2024-01-26 at 4.36.33 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 592752, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06FYHEBC92/screenshot_2024-01-26_at_4.36.33___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06FYHEBC92/download/screenshot_2024-01-26_at_4.36.33___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 223, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 297, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 446, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 495, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 594, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 634, "original_w": 3022, "original_h": 1870, "thumb_tiny": "AwAdADDRI9MCkx64pR+PWlyfQ0AIAO/WjC0uT6UZPpQAmFoCg0uT6UZPpQAdqBnvR2o5oAKOaCSMUmTigB1FJzQCc0Af/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06FYHEBC92/screenshot_2024-01-26_at_4.36.33___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06FYHEBC92-24012a6eef", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "bfrp0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Jotting this thought down before I forget: I've noticed a few times lately where the current product isn't displaying well on smaller screens.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "During Wonolo demo, I had it configured with 6 budget types and the HR Admin view couldn't adequately show the different departments"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> was trying to view her account on her laptop, and the collapsed navigation panel had the \">>\" icon overlapping with the Total Rewards icon, so it wasn't clear how to open it up again"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nWe'll (eventually?) need a better plan for making these pages flexible for smaller screens."}]}]}]}, {"ts": "**********.377289", "text": "<@U04DS2MBWP4> Any topics for <PERSON> specifically today? (I have my call with her at 1:30)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.377289", "reply_count": 9, "edited": {"user": "U065H3M6WJV", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "+sS5Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Any topics for <PERSON> specifically today? (I have my call with her at 1:30)"}]}]}]}], "created_at": "2025-05-22T21:35:34.587691"}