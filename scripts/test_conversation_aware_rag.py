#!/usr/bin/env python3
"""
Test Script for Conversation-Aware RAG Features

This script tests the enhanced conversation-aware RAG implementation
using real Slack data and validates the improvements.
"""

import os
import sys
import django
import logging
import time
from datetime import datetime
from typing import Dict, List, Any

# Add the project root to Python path
sys.path.insert(0, '/Users/<USER>/Desktop/RAGSearch')
sys.path.insert(0, '/Users/<USER>/Desktop/RAGSearch/multi_source_rag')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'multi_source_rag.settings')
django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk
from apps.documents.services.ingestion_service import IngestionService
from apps.search.services.rag_service import RAGService
from apps.documents.config.conversation_config import get_conversation_config, set_conversation_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ConversationAwareRAGTester:
    """Test suite for conversation-aware RAG features."""

    def __init__(self):
        """Initialize the tester."""
        self.tenant = None
        self.user = None
        self.source = None
        self.test_results = {}

    def setup_test_environment(self):
        """Set up test environment."""
        logger.info("🔧 Setting up test environment...")

        # Get or create tenant
        self.tenant, created = Tenant.objects.get_or_create(
            slug='test-conversation-rag',
            defaults={
                'name': 'Test Conversation RAG',
                'is_active': True
            }
        )

        if created:
            logger.info(f"Created new tenant: {self.tenant.name}")
        else:
            logger.info(f"Using existing tenant: {self.tenant.name}")

        # Get or create user
        self.user, created = User.objects.get_or_create(
            username='test_conversation_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )

        if created:
            logger.info(f"Created new user: {self.user.username}")
        else:
            logger.info(f"Using existing user: {self.user.username}")

    def create_test_document_source(self):
        """Create document source for testing."""
        logger.info("📄 Creating test document source...")

        # Configuration for conversation-aware processing
        config = {
            "data_dir": "/Users/<USER>/Desktop/RAGSearch/data/",
            "channel": "C065QSSNH8A",
            "time_period": "monthly",
            "custom_days": 365,  # Load more data (1 year)
            "use_conversation_aware": True,  # Enable conversation-aware processing
            "conversation_gap_minutes": 60,  # Increase gap to group more messages
            "min_conversation_messages": 1,  # Reduce minimum to allow single messages
            "enable_semantic_cross_refs": True,
            "quality_threshold": 0.1,
            "max_documents_per_channel": 100,  # Limit for testing
            "include_threads": True,
            "filter_bots": True,
        }

        # Create or update document source
        self.source, created = DocumentSource.objects.get_or_create(
            tenant=self.tenant,
            name="Test Slack Conversation Data",
            source_type="local_slack",
            defaults={
                'config': config,
                'is_active': True
            }
        )

        if not created:
            self.source.config = config
            self.source.save()

        logger.info(f"Document source ready: {self.source.name}")
        return self.source

    def test_conversation_aware_ingestion(self):
        """Test conversation-aware document ingestion."""
        logger.info("📥 Testing conversation-aware ingestion...")

        # Configure conversation-aware settings
        set_conversation_config(
            self.tenant.slug,
            'ingestion',
            use_conversation_pipeline=True,
            auto_detect_conversations=True,
            batch_size=25
        )

        # Configure parsing settings for conversation detection
        set_conversation_config(
            self.tenant.slug,
            'parsing',
            conversation_gap_minutes=60,
            min_conversation_messages=1
        )

        # Create ingestion service
        ingestion_service = IngestionService(tenant=self.tenant, user=self.user)

        # Clean existing data for clean test
        logger.info("Cleaning existing documents...")
        RawDocument.objects.filter(source=self.source).delete()

        # Process source with conversation-aware features
        start_time = time.time()
        try:
            processed, failed = ingestion_service.process_source(
                source=self.source,
                batch_size=25,
                limit=50  # Limit for testing
            )

            processing_time = time.time() - start_time

            # Get processing statistics
            stats = ingestion_service.get_processing_stats()

            self.test_results['ingestion'] = {
                'processed': processed,
                'failed': failed,
                'processing_time': processing_time,
                'stats': stats,
                'success': processed > 0 and failed == 0
            }

            logger.info(f"✅ Ingestion completed: {processed} processed, {failed} failed in {processing_time:.2f}s")

        except Exception as e:
            logger.error(f"❌ Ingestion failed: {str(e)}")
            self.test_results['ingestion'] = {
                'processed': 0,
                'failed': 1,
                'error': str(e),
                'success': False
            }

    def test_conversation_aware_search(self):
        """Test conversation-aware search functionality."""
        logger.info("🔍 Testing conversation-aware search...")

        # Configure conversation-aware query engine
        set_conversation_config(
            self.tenant.slug,
            'query_engine',
            enable_conversation_context=True,
            similarity_top_k=10,
            boost_recent_conversations=True
        )

        # Create RAG service
        rag_service = RAGService(user=self.user, tenant_slug=self.tenant.slug)

        # Test queries that should benefit from conversation awareness
        test_queries = [
            "What discussions happened about engineering issues?",
            "Who participated in product engineering conversations?",
            "What questions were asked in recent threads?",
            "Show me conversations with high engagement",
            "What topics were discussed by the team?"
        ]

        search_results = []

        for query in test_queries:
            logger.info(f"Testing query: {query}")

            try:
                start_time = time.time()

                # Execute search with conversation-aware features
                search_result, retrieved_docs = rag_service.search(
                    query_text=query,
                    top_k=5,
                    use_context_aware=True,
                    output_format="text"
                )

                query_time = time.time() - start_time

                # Analyze results for conversation-aware features
                conversation_features = self._analyze_conversation_features(retrieved_docs)

                result = {
                    'query': query,
                    'response': search_result.generated_answer if hasattr(search_result, 'generated_answer') else str(search_result),
                    'num_results': len(retrieved_docs),
                    'query_time': query_time,
                    'conversation_features': conversation_features,
                    'success': len(retrieved_docs) > 0
                }

                search_results.append(result)

                logger.info(f"✅ Query completed: {len(retrieved_docs)} results in {query_time:.2f}s")

            except Exception as e:
                logger.error(f"❌ Query failed: {str(e)}")
                search_results.append({
                    'query': query,
                    'error': str(e),
                    'success': False
                })

        self.test_results['search'] = {
            'queries_tested': len(test_queries),
            'successful_queries': sum(1 for r in search_results if r.get('success', False)),
            'results': search_results,
            'average_query_time': sum(r.get('query_time', 0) for r in search_results) / len(search_results),
            'success': all(r.get('success', False) for r in search_results)
        }

    def _analyze_conversation_features(self, retrieved_docs: List[Any]) -> Dict[str, Any]:
        """Analyze retrieved documents for conversation-aware features."""
        features = {
            'has_conversation_metadata': 0,
            'conversation_types': set(),
            'participant_counts': [],
            'engagement_scores': [],
            'thread_conversations': 0
        }

        for doc_tuple in retrieved_docs:
            if len(doc_tuple) >= 2:
                doc = doc_tuple[0]  # Document chunk

                if hasattr(doc, 'metadata'):
                    metadata = doc.metadata

                    # Check for conversation-aware metadata
                    if any(key.startswith('conversation_') for key in metadata.keys()):
                        features['has_conversation_metadata'] += 1

                    # Extract conversation type
                    conv_type = metadata.get('conversation_type')
                    if conv_type:
                        features['conversation_types'].add(conv_type)

                    # Extract participant count
                    participant_count = metadata.get('participant_count')
                    if participant_count:
                        features['participant_counts'].append(participant_count)

                    # Extract engagement score
                    engagement_score = metadata.get('engagement_score')
                    if engagement_score:
                        features['engagement_scores'].append(engagement_score)

                    # Check for thread conversations
                    if metadata.get('has_threads'):
                        features['thread_conversations'] += 1

        # Convert sets to lists for JSON serialization
        features['conversation_types'] = list(features['conversation_types'])

        return features

    def test_configuration_management(self):
        """Test configuration management for conversation features."""
        logger.info("⚙️ Testing configuration management...")

        try:
            # Test getting default configuration
            parsing_config = get_conversation_config(self.tenant.slug, 'parsing')
            query_config = get_conversation_config(self.tenant.slug, 'query_engine')
            ingestion_config = get_conversation_config(self.tenant.slug, 'ingestion')

            # Test setting custom configuration
            set_conversation_config(
                self.tenant.slug,
                'parsing',
                chunk_size=1024,
                max_conversation_gap_minutes=45
            )

            # Verify configuration was updated
            updated_config = get_conversation_config(self.tenant.slug, 'parsing')

            self.test_results['configuration'] = {
                'default_configs_loaded': True,
                'custom_config_set': True,
                'config_updated': updated_config.chunk_size == 1024,
                'success': True
            }

            logger.info("✅ Configuration management test passed")

        except Exception as e:
            logger.error(f"❌ Configuration test failed: {str(e)}")
            self.test_results['configuration'] = {
                'error': str(e),
                'success': False
            }

    def run_all_tests(self):
        """Run all conversation-aware RAG tests."""
        logger.info("🚀 Starting conversation-aware RAG tests...")

        try:
            # Setup
            self.setup_test_environment()
            self.create_test_document_source()

            # Run tests
            self.test_configuration_management()
            self.test_conversation_aware_ingestion()
            self.test_conversation_aware_search()

            # Generate report
            self.generate_test_report()

        except Exception as e:
            logger.error(f"❌ Test suite failed: {str(e)}")
            raise

    def generate_test_report(self):
        """Generate comprehensive test report."""
        logger.info("📊 Generating test report...")

        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() if result.get('success', False))

        print("\n" + "="*80)
        print("CONVERSATION-AWARE RAG TEST REPORT")
        print("="*80)
        print(f"Test Environment: {self.tenant.name}")
        print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total Tests: {total_tests}")
        print(f"Successful Tests: {successful_tests}")
        print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        print("\n" + "-"*80)

        # Configuration Test Results
        if 'configuration' in self.test_results:
            config_result = self.test_results['configuration']
            status = "✅ PASSED" if config_result.get('success') else "❌ FAILED"
            print(f"Configuration Management: {status}")
            if not config_result.get('success'):
                print(f"  Error: {config_result.get('error', 'Unknown error')}")

        # Ingestion Test Results
        if 'ingestion' in self.test_results:
            ingestion_result = self.test_results['ingestion']
            status = "✅ PASSED" if ingestion_result.get('success') else "❌ FAILED"
            print(f"Conversation-Aware Ingestion: {status}")
            print(f"  Documents Processed: {ingestion_result.get('processed', 0)}")
            print(f"  Documents Failed: {ingestion_result.get('failed', 0)}")
            print(f"  Processing Time: {ingestion_result.get('processing_time', 0):.2f}s")
            if not ingestion_result.get('success'):
                print(f"  Error: {ingestion_result.get('error', 'Unknown error')}")

        # Search Test Results
        if 'search' in self.test_results:
            search_result = self.test_results['search']
            status = "✅ PASSED" if search_result.get('success') else "❌ FAILED"
            print(f"Conversation-Aware Search: {status}")
            print(f"  Queries Tested: {search_result.get('queries_tested', 0)}")
            print(f"  Successful Queries: {search_result.get('successful_queries', 0)}")
            print(f"  Average Query Time: {search_result.get('average_query_time', 0):.2f}s")

            # Show sample results
            if search_result.get('results'):
                print("\n  Sample Query Results:")
                for result in search_result['results'][:2]:  # Show first 2 results
                    if result.get('success'):
                        print(f"    Query: {result['query']}")
                        print(f"    Results: {result['num_results']}")
                        features = result.get('conversation_features', {})
                        print(f"    Conversation Features: {features.get('has_conversation_metadata', 0)} docs with metadata")

        print("\n" + "="*80)

        if successful_tests == total_tests:
            print("🎉 ALL TESTS PASSED! Conversation-aware RAG is working correctly.")
        else:
            print(f"⚠️  {total_tests - successful_tests} test(s) failed. Check logs for details.")

        print("="*80)


def main():
    """Main test execution function."""
    tester = ConversationAwareRAGTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
