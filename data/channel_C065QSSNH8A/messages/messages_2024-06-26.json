{"date": "2024-06-26", "channel_id": "C065QSSNH8A", "message_count": 11, "messages": [{"ts": "1719420699.987309", "text": "<@U065H3M6WJV> this is <@U071FN2589Y>’s doc on People insights calculation logic. Its not that structured. Please review and comment\n<https://docs.google.com/document/d/1RTbo30sJVdfbgDZxj8YyM5oOtj6-dfrIZe4htDzsrOw/edit?addon_store#heading=h.jw8oqqz90dqt>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1719420699.987309", "reply_count": 5, "files": [{"id": "F079WKSC1L2", "created": 1719420704, "timestamp": 1719420704, "name": "Data Validation", "title": "Data Validation", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 40613, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1RTbo30sJVdfbgDZxj8YyM5oOtj6-dfrIZe4htDzsrOw", "external_url": "https://docs.google.com/document/d/1RTbo30sJVdfbgDZxj8YyM5oOtj6-dfrIZe4htDzsrOw/edit?addon_store#heading=h.jw8oqqz90dqt", "url_private": "https://docs.google.com/document/d/1RTbo30sJVdfbgDZxj8YyM5oOtj6-dfrIZe4htDzsrOw/edit?addon_store#heading=h.jw8oqqz90dqt", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1035, "thumb_tiny": "AwAwACXTopCM0bR6UAGfY0tFFABRRRQAjfdNC/h+FKelIB70ALRRRQAUUUUAFAGKKKACiiigAooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F079WKSC1L2/data_validation", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "F/VSm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " this is "}, {"type": "user", "user_id": "U071FN2589Y"}, {"type": "text", "text": "’s doc on People insights calculation logic. Its not that structured. Please review and comment\n"}, {"type": "link", "url": "https://docs.google.com/document/d/1RTbo30sJVdfbgDZxj8YyM5oOtj6-dfrIZe4htDzsrOw/edit?addon_store#heading=h.jw8oqqz90dqt"}]}]}]}, {"ts": "1719419888.530169", "text": "<!here> FYI... There were quite a few fixes and performance improvements pushed to `<https://new-meritview.compiify.com/>` I am still validating some of the fixes pushed. Will do more testing tomorrow and be ready to merge changes to customer test ENVs.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "MgqUw", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " FYI... There were quite a few fixes and performance improvements pushed to "}, {"type": "link", "url": "https://new-meritview.compiify.com/", "style": {"code": true}}, {"type": "text", "text": " I am still validating some of the fixes pushed. Will do more testing tomorrow and be ready to merge changes to customer test ENVs."}]}]}]}, {"ts": "1719417451.508599", "text": "<@U0690EB5JE5> which country is AR? I did not see any employees in the org view under this country. will show in the eng call what I mean", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719417451.508599", "reply_count": 4, "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Y6WWX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " which country is AR? I did not see any employees in the org view under this country. will show in the eng call what I mean"}]}]}]}, {"ts": "1719417405.233059", "text": "Now everything is hardcoded, as we are working on supporting customer configurable perf ratings and those will be dynamic.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VXuM/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Now everything is hardcoded, as we are working on supporting customer configurable perf ratings and those will be dynamic."}]}]}]}, {"ts": "1719417313.711229", "text": "<@U065H3M6WJV> <@U04DKEFP1K8> How do we take care of pre-filling guidelines as in case of dynamic perf rating", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1719417313.711229", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "1719418548.000000"}, "files": [{"id": "F079W8VJVK4", "mode": "tombstone"}], "blocks": [{"type": "rich_text", "block_id": "1plVb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " How do we take care of pre-filling guidelines as in case of dynamic perf rating"}]}]}]}, {"ts": "1719415152.854569", "text": "Thats what I am finding out. We will have to do most of this live.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "HU2pY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thats what I am finding out. We will have to do most of this live."}]}]}]}, {"ts": "1719415073.284869", "text": "<@U04DS2MBWP4> I would recommend to explore and go over this in a meeting as there would be back and forth.  ", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1719415084.000000"}, "blocks": [{"type": "rich_text", "block_id": "cB8DA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I would recommend to explore and go over this in a meeting as there would be back and forth.  "}]}]}]}, {"ts": "1719414917.062759", "text": "<@U065H3M6WJV> <@U0690EB5JE5> when I view direct reports in merit view, I get a list of 17 people. When I apply manager filter in the org view to see my direct reports, I get a list of only 10 people. Any idea why this discrepancy?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719414917.062759", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "P9c/D", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " when I view direct reports in merit view, I get a list of 17 people. When I apply manager filter in the org view to see my direct reports, I get a list of only 10 people. Any idea why this discrepancy?"}]}]}]}, {"ts": "1719414545.984939", "text": "also is there an option to see the hierarchy in the merit planning view? I can't seem to easily find it even though I am logged in as CEO", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "byMG8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "also is there an option to see the hierarchy in the merit planning view? I can't seem to easily find it even though I am logged in as CEO"}]}]}]}, {"ts": "1719414440.742369", "text": "<@U04DKEFP1K8> where can I find the raw data set that is used for the new merit view?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719414440.742369", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "M8tON", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " where can I find the raw data set that is used for the new merit view?"}]}]}]}, {"ts": "1719413211.192139", "text": "<@U04DKEFP1K8> I am not seeing the dry run invites for CWA and Nauto as we discussed in yesterday's eng call. I thought you said you were sending the invites during the meeting itself.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719413211.192139", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "iH2MF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I am not seeing the dry run invites for CWA and <PERSON><PERSON> as we discussed in yesterday's eng call. I thought you said you were sending the invites during the meeting itself."}]}]}]}], "created_at": "2025-05-22T21:35:34.623390"}