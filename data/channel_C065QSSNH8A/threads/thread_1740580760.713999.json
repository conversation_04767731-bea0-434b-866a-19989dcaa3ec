{"thread_ts": "**********.713999", "channel_id": "C065QSSNH8A", "reply_count": 5, "replies": [{"ts": "**********.170459", "text": "<@U07EJ2LP44S> Looks like user was able to login and We haven't made any changes that would impact login. Could you please ask user to clear cookies and try with the actual url `<https://curanahealthmip.stridehr.io/>` `<https://curanahealth.stridehr.io/>`", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.713999", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "FjsH9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Looks like user was able to login and We haven't made any changes that would impact login. Could you please ask user to clear cookies and try with the actual url "}, {"type": "link", "url": "https://curanahealthmip.stridehr.io/", "style": {"code": true}}, {"type": "text", "text": " "}, {"type": "link", "url": "https://curanahealth.stridehr.io/", "style": {"code": true}}]}]}]}, {"ts": "**********.155489", "text": "Those are the URLs they are using", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.713999", "blocks": [{"type": "rich_text", "block_id": "nAlTU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Those are the URLs they are using"}]}]}]}, {"ts": "**********.441239", "text": "When did the user login?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.713999", "blocks": [{"type": "rich_text", "block_id": "HKTgp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "When did the user login?"}]}]}]}, {"ts": "**********.404799", "text": "Meaning is it resolved?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.713999", "blocks": [{"type": "rich_text", "block_id": "G1bZb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Meaning is it resolved?"}]}]}]}, {"ts": "1740585159.774559", "text": "No, in the email it is mentioned there were successful logins and also our logs show successful login 18hours ago. Can you confirm this with user? There is nothing different for this user compared to others. Only possible fix is to clear the cookies and try.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.713999", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "xog5K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No, in the email it is mentioned there were successful logins and also our logs show successful login 18hours ago. Can you confirm this with user? There is nothing different for this user compared to others. Only possible fix is to clear the cookies and try."}]}]}]}], "created_at": "2025-05-22T22:00:49.358281"}