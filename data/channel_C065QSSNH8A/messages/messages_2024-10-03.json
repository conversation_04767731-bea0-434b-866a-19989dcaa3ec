{"date": "2024-10-03", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1727979891.610209", "text": "<PERSON><PERSON><PERSON> is pulling out the big guns. <@U07M6QKHUC9> Context is, <PERSON><PERSON><PERSON> asked if we could implement different recommendations by department. So for example, a rating of 4 is 5% in one department, but 4% in another. I told her for the next cycle yes. She responded with this:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727979891.610209", "reply_count": 29, "files": [{"id": "F07QCK23SQ4", "created": 1727979887, "timestamp": 1727979887, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 92106, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07QCK23SQ4/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07QCK23SQ4/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QCK23SQ4-3c9cea0509/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QCK23SQ4-3c9cea0509/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QCK23SQ4-3c9cea0509/image_360.png", "thumb_360_w": 360, "thumb_360_h": 91, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QCK23SQ4-3c9cea0509/image_480.png", "thumb_480_w": 480, "thumb_480_h": 121, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QCK23SQ4-3c9cea0509/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QCK23SQ4-3c9cea0509/image_720.png", "thumb_720_w": 720, "thumb_720_h": 182, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QCK23SQ4-3c9cea0509/image_800.png", "thumb_800_w": 800, "thumb_800_h": 202, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QCK23SQ4-3c9cea0509/image_960.png", "thumb_960_w": 960, "thumb_960_h": 242, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QCK23SQ4-3c9cea0509/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 258, "original_w": 1870, "original_h": 472, "thumb_tiny": "AwAMADDQ5z0NOy3pTdozmnjpQAmW9BRlvQUtFADfm9P1pRn0/WlooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07QCK23SQ4/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07QCK23SQ4-e6a2c75671", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "1EK3x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> is pulling out the big guns. "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Context is, <PERSON><PERSON><PERSON> asked if we could implement different recommendations by department. So for example, a rating of 4 is 5% in one department, but 4% in another. I told her for the next cycle yes. She responded with this:"}]}]}]}, {"ts": "1727976393.363619", "text": "Instead of trying to share <PERSON><PERSON><PERSON><PERSON>?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YUW2E", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Instead of trying to share <PERSON><PERSON><PERSON><PERSON>?"}]}]}]}, {"ts": "1727976384.579069", "text": "Did we decide I could actually just upgrade my Loom /", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727976384.579069", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "t794p", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Did we decide I could actually just upgrade my Loom /"}]}]}]}, {"ts": "1727976140.214399", "text": "<@U07NBMXTL1E> cycle builder is now clickable so you can move through different sections of cycle builder easily. This will be good for the demos,\n\n<@U0690EB5JE5> is pushing it to demo env now.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727970700.172859", "subtype": "thread_broadcast", "edited": {"user": "U07M6QKHUC9", "ts": "1727976151.000000"}, "reactions": [{"name": "white_check_mark", "users": ["U07NBMXTL1E"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "woE00", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " cycle builder is now clickable so you can move through different sections of cycle builder easily. This will be good for the demos,\n\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " is pushing it to demo env now."}]}]}]}, {"ts": "1727970746.773469", "text": "<@U07M6QKHUC9> Also we have fixed org edit pop up UX issue i.e. moving currency symbol outside of the input.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "tada", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "O7yVv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Also we have fixed org edit pop up UX issue i.e. moving currency symbol outside of the input."}]}]}]}, {"ts": "1727970700.172859", "text": "<@U04DKEFP1K8> Following changes are in <http://test.stridehr.io|test.stridehr.io>\n• Promotion flow - Alayacare\n• Cycle clickability and some minor enhancements on allocate page - <@U07M6QKHUC9>much awaited . It took a while\n• Cainwatters bug fixes reported yesterday\n• some of the Org view fixes reported by <PERSON>\n• Cycle closure\n    ◦ Update employee after cycle closure\n    ◦ remove validate step in cycle closure step\n    ◦ Remaining will be merged by early next week\n", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727970700.172859", "reply_count": 3, "reactions": [{"name": "tada", "users": ["U07M6QKHUC9"], "count": 1}, {"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "VBmzE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Following changes are in "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Promotion flow - Alayacare"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle clickability and some minor enhancements on allocate page - "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": "much awaited . It took a while"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Cainwatters bug fixes reported yesterday"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "some of the Org view fixes reported by <PERSON>"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle closure"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Update employee after cycle closure"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "remove validate step in cycle closure step"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Remaining will be merged by early next week"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1727968529.910449", "text": "CainWatters Update: Just got off a call with <PERSON><PERSON> and <PERSON>. They will not have budgets in time to run the cycle with all managers. Instead, they are going to do it the way they have done previously - HR will make all the recommendations in the system (like they used to do with spreadsheets). Then in their next cycle, they will include everyone. Our only users in the system will be <PERSON><PERSON><PERSON> and <PERSON><PERSON>.\n\n<@U04DKEFP1K8> They have a paybands refresh to do, and will send us that and the ratings scale mapping on Mon/Tues. Then they want to set up the cycle and start inputting performance ratings etc.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "vNyk5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "CainWatters Update: Just got off a call with <PERSON><PERSON> and <PERSON>. They will not have budgets in time to run the cycle with all managers. Instead, they are going to do it the way they have done previously - HR will make all the recommendations in the system (like they used to do with spreadsheets). Then in their next cycle, they will include everyone. Our only users in the system will be <PERSON><PERSON><PERSON> and <PERSON><PERSON>.\n\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " They have a paybands refresh to do, and will send us that and the ratings scale mapping on Mon/Tues. Then they want to set up the cycle and start inputting performance ratings etc."}]}]}]}, {"ts": "1727967016.035699", "text": "I did the Loom-Jira thing. I couldn't find a standard SDF epic so I put it under demo issues. <https://compiify.atlassian.net/browse/COM-3675>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727967016.035699", "reply_count": 3, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13736::d4dd4c6f80de4b099bbb927356661013", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3675?atlOrigin=eyJpIjoiM2UyNjJjNDNmNGQyNGViZDllNDVhMDE4OTAyMmY2OTAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3675 Issue: Performance rating toggle in Merit view not toggling correct…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13736::c082a7cd74ad4b0e9b7655d9edcfefe8", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13736\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3675\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3675", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "JhYaB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I did the Loom-Jira thing. I couldn't find a standard SDF epic so I put it under demo issues. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3675"}]}]}]}, {"ts": "1727950176.326329", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> Alayacare synced till date. Looks like they have added 5 new employees. Also I have set the based currency as CAD and updated currency config. Please let me know if you see any issues.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1727966472.000000"}, "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "sTqUf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Alayacare synced till"}, {"type": "text", "text": " date. "}, {"type": "text", "text": "Looks like they have added 5 new employees. Also I have set the based currency as CAD and updated currency config. Please let me know if you see any issues."}]}]}]}], "created_at": "2025-05-22T21:35:34.652367"}