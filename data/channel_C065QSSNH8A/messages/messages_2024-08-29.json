{"date": "2024-08-29", "channel_id": "C065QSSNH8A", "message_count": 11, "messages": [{"ts": "1724952509.172769", "text": "<@U04DKEFP1K8> Following enhancements merged till test ENV today.\n• Edit and View Performance rating in Org View\n• Region column in Org View", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724952509.172769", "reply_count": 2, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "pxBfH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Following enhancements merged till test ENV today.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Edit and View Performance rating in Org View"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Region column in Org View"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724934950.365199", "text": "Proposed structure for daily meeting: <https://docs.google.com/document/d/1wttp2pB94E4lEF5ZB57HtxZGdw7LI3_jpbcM-l1uVGM/edit?usp=sharing>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1724934950.365199", "reply_count": 7, "files": [{"id": "F07K03Y178D", "created": 1724934951, "timestamp": 1724934951, "name": "Leadership Meeting", "title": "Leadership Meeting", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 58586, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1wttp2pB94E4lEF5ZB57HtxZGdw7LI3_jpbcM-l1uVGM", "external_url": "https://docs.google.com/document/d/1wttp2pB94E4lEF5ZB57HtxZGdw7LI3_jpbcM-l1uVGM/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1wttp2pB94E4lEF5ZB57HtxZGdw7LI3_jpbcM-l1uVGM/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K03Y178D-676a5980fe/leadership_meeting_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTSJwaTP1/KhqMe1ABn6/lSjPvSfhSj6UALRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQB//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07K03Y178D/leadership_meeting", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "yvx/z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Proposed structure for daily meeting: "}, {"type": "link", "url": "https://docs.google.com/document/d/1wttp2pB94E4lEF5ZB57HtxZGdw7LI3_jpbcM-l1uVGM/edit?usp=sharing"}]}]}]}, {"ts": "1724934444.977019", "text": "<@U07EJ2LP44S> this issue is fixed. Please let me know if any other issues.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724853795.976939", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "EPbE5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " this issue is fixed. Please let me know if any other issues."}]}]}]}, {"ts": "1724934314.734769", "text": "Trying to update Diversified's Data - adding an additional (fake) employee. Screen gives me the confirmation of changes to two lines, then this:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1724934314.734769", "reply_count": 13, "files": [{"id": "F07K2QZGBH9", "created": 1724934311, "timestamp": 1724934311, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 186079, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07K2QZGBH9/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07K2QZGBH9/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_360.png", "thumb_360_w": 360, "thumb_360_h": 260, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_480.png", "thumb_480_w": 480, "thumb_480_h": 346, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_720.png", "thumb_720_w": 720, "thumb_720_h": 520, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_800.png", "thumb_800_w": 800, "thumb_800_h": 577, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_960.png", "thumb_960_w": 960, "thumb_960_h": 693, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07K2QZGBH9-ddda71b8e8/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 739, "original_w": 1704, "original_h": 1230, "thumb_tiny": "AwAiADCzgUDGOKXFCj5hQAZ5xQTjrVjav90flSbRn7ox9KAIKAc9KsbV/uj8qrkcmgBD+NKDz0opOaAJPOb0FHmnPQUyigB/nN/dpmcnpQelJj6/nQAtFFFABRRRQAUUUUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07K2QZGBH9/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07K2QZGBH9-dab2abba98", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "8uRgI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Trying to update Diversified's Data - adding an additional (fake) employee. Screen gives me the confirmation of changes to two lines, then this:"}]}]}]}, {"ts": "1724908236.067389", "text": "<@U07EJ2LP44S>  Please label every jira you create as `Prioritized` those will appear on this <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng Priorities board> for me to triage everyday. Since I am the default assignee, I will add the label in case missed.\ncc: <@U04DKEFP1K8>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724908255.000000"}, "reactions": [{"name": "white_check_mark", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "e3B4q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "  Please label every jira you create as "}, {"type": "text", "text": "Prioritized", "style": {"code": true}}, {"type": "text", "text": " those will appear on this "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng Priorities board"}, {"type": "text", "text": " for me to triage everyday. Since I am the default assignee, I will add the label in case missed.\ncc: "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1724889672.591629", "text": "She was talking more about communication, support problem and giving them talking points", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "uAKQR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She was talking more about communication, support problem and giving them talking points"}]}]}]}, {"ts": "1724889333.336329", "text": "Yes I think she would be great to interview!\n\n<@U04DS2MBWP4> when you say that <PERSON> confirmed that \"managers struggle consistently with the problems we are trying to solve\" - which problems were you discussing when she spoke about this? Manager problems with compensation in general (i.e. difficult to make decisions), or the specific communication support problem?", "user": "U07HCJ07H7G", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Arpvf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes I think she would be great to interview!\n\n"}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " when you say that <PERSON> confirmed that \"managers struggle consistently with the problems we are trying to solve\" - which problems were you discussing when she spoke about this? Manager problems with compensation in general (i.e. difficult to make decisions), or the specific communication support problem?"}]}]}]}, {"ts": "1724882548.981719", "text": "<@U07HCJ07H7G> <@U07EJ2LP44S> <PERSON> confirmed that other vendors are not working on this manager enablement functionality and managers struggle consistently struggle with the problems we are trying to solve. She thinks it would be a differentiator for us but she also said it's a hard problem to solve as every company will have a different comp philosophy and comp structure. Let me know if you want to interview <PERSON> for this. She is director of total rewards at Drata.", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1724883992.000000"}, "blocks": [{"type": "rich_text", "block_id": "a18uB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07HCJ07H7G"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " <PERSON> confirmed that other vendors are not working on this manager enablement functionality and managers struggle consistently struggle with the problems we are trying to solve. She thinks it would be a differentiator for us but she also said it's a hard problem to solve as every company will have a different comp philosophy and comp structure. Let me know if you want to interview <PERSON> for this. She is director of total rewards at Drata."}]}]}]}, {"ts": "1724882401.765189", "text": "<!here> here is the feedback from <PERSON> (our product advisor) based on her review of test environment\n<https://docs.google.com/document/d/19v8dloA_TMg8zlGEl-eoWemCqH7Z-NpviSxbdSn2ujk/edit>", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F07KNEQ2B4Y", "created": 1724882407, "timestamp": 1724882407, "name": "Stride Test Site feedback from Ang - August 2024", "title": "Stride Test Site feedback from Ang - August 2024", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 128534, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "19v8dloA_TMg8zlGEl-eoWemCqH7Z-NpviSxbdSn2ujk", "external_url": "https://docs.google.com/document/d/19v8dloA_TMg8zlGEl-eoWemCqH7Z-NpviSxbdSn2ujk/edit", "url_private": "https://docs.google.com/document/d/19v8dloA_TMg8zlGEl-eoWemCqH7Z-NpviSxbdSn2ujk/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07KNEQ2B4Y-6aff8328d2/stride_test_site_feedback_from_ang_-_august_2024_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1035, "thumb_tiny": "AwAwACXSJxS5pGGabtoAfRSDPvS59qACiiigBrjOOKAWpWx3pARQAvNLSZFICPagB1FFFADXOMcUgx3H608gHrRgUANwv+TS8etLRQAUUUUAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07KNEQ2B4Y/stride_test_site_feedback_from_ang_-_august_2024", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "yaodh", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " here is the feedback from <PERSON> (our product advisor) based on her review of test environment\n"}, {"type": "link", "url": "https://docs.google.com/document/d/19v8dloA_TMg8zlGEl-eoWemCqH7Z-NpviSxbdSn2ujk/edit"}]}]}]}, {"ts": "1724881162.766139", "text": "<!here> common question to all: \"*proration is commonly applied to merit-based salary increases to reflect partial-year work\"*, it’s generally not applied to market adjustments or Cost of living adjustment (COLA) increases. Do you guys 100% agree with this statement?", "user": "U04DKEFP1K8", "type": "message", "edited": {"user": "U04DKEFP1K8", "ts": "1724881186.000000"}, "blocks": [{"type": "rich_text", "block_id": "TQ1eg", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " common question to all: \""}, {"type": "text", "text": "proration is commonly applied to merit-based salary increases to reflect partial-year work\"", "style": {"bold": true}}, {"type": "text", "text": ", it’s generally not applied to market adjustments or Cost of living adjustment (COLA) increases. Do you guys 100% agree with this statement?"}]}]}]}, {"ts": "1724871849.663419", "text": "Action items from today's fireflies recording:\n\n<PERSON>\nTake ownership of restructuring leadership meetings (02:39)\nCreate a new schedule for daily leadership meetings with specific focus areas (09:43)\nValidate SDF issues and go through their feedback document (57:00)\nContinue with manager enablement interviews (57:06)\n\n<PERSON><PERSON>h\nUpdate prioritization spreadsheet with recently created Jira tickets (56:42)\nDemo audit log feature (19:21)\nDemo hourly rate feature (41:13)\n\nSa<PERSON><PERSON>h\nProvide context on SDF feedback to <PERSON> (59:08)\n\nKa<PERSON>l\nMake a decision on hiring sales representatives by early next week (59:19)", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9ZDAA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Action items from today's fireflies recording:\n\n<PERSON>\nTake ownership of restructuring leadership meetings (02:39)\nCreate a new schedule for daily leadership meetings with specific focus areas (09:43)\nValidate SDF issues and go through their feedback document (57:00)\nContinue with manager enablement interviews (57:06)\n\n<PERSON><PERSON>h\nUpdate prioritization spreadsheet with recently created Jira tickets (56:42)\nDemo audit log feature (19:21)\nDemo hourly rate feature (41:13)\n\nSa<PERSON><PERSON>h\nProvide context on SDF feedback to <PERSON> (59:08)\n\nKa<PERSON>l\nMake a decision on hiring sales representatives by early next week (59:19)"}]}]}]}], "created_at": "2025-05-22T21:35:34.643497"}