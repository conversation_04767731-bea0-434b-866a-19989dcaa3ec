{"date": "2024-02-05", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1707154841.785869", "text": "<@U04DKEFP1K8> Is there any issue if I close the \"CFY Sprint 15\" that's currently open in JIRA? It seems that this is older / no longer relevant, but please let me know if there would be any problem.\n\nI'm hoping to come up with a better way for us to track backlog & immediate priorities, and I think a Kanban board, or maybe just another version of a Scrum board like this might help. Also curious if <@U0690EB5JE5> has suggestions on how to keep track of daily priorities for an eng team when things are moving rapidly. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1707154841.785869", "reply_count": 6, "edited": {"user": "U065H3M6WJV", "ts": "1707154918.000000"}, "blocks": [{"type": "rich_text", "block_id": "dPsn+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Is there any issue if I close the \"CFY Sprint 15\" that's currently open in JIRA? It seems that this is older / no longer relevant, but please let me know if there would be any problem.\n\nI'm hoping to come up with a better way for us to track backlog & immediate priorities, and I think a Kanban board, or maybe just another version of a Scrum board like this might help. Also curious if "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " has suggestions on how to keep track of daily priorities for an eng team when things are moving rapidly. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}], "created_at": "2025-05-22T21:35:34.584853"}