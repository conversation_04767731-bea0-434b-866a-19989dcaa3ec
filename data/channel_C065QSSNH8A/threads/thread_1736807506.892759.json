{"thread_ts": "1736807506.892759", "channel_id": "C065QSSNH8A", "reply_count": 3, "replies": [{"ts": "1736807733.984519", "text": "I think <PERSON><PERSON><PERSON> said that all of these changes are getting pushed together so I just want to make sure everything is working before we push. I think it’s bonus changes (unless that’s already done everywhere), column configurator, UX design, and HRBP.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736807506.892759", "blocks": [{"type": "rich_text", "block_id": "FU7dL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think <PERSON><PERSON><PERSON> said that all of these changes are getting pushed together so I just want to make sure everything is working before we push. I think it’s bonus changes (unless "}, {"type": "text", "text": "that’s"}, {"type": "text", "text": " already done everywhere), column configurator, UX design, and HRBP."}]}]}]}, {"ts": "1736807741.957979", "text": "But yes, I’d like to get it all out ASAP", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736807506.892759", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "R82rW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "But yes, I’d like to get it all out ASAP"}]}]}]}, {"ts": "1736816663.443379", "text": "<@U07M6QKHUC9> We will keep testing and fixing but will not hold the the latest build anymore. I will be updating all the ENVs Tuesday. We will definitely might see some issues when we hand it over to customer and I want to seem them sooner than later. Yes, will fix all the issues reported so far by Wednesday.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736807506.892759", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "M20fp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " We will keep testing and fixing but will not hold the the latest build anymore. I will be updating all the ENVs Tuesday. We will definitely might see some issues when we hand it over to customer and I want to seem them sooner than later. Yes, will fix all the issues reported so far by Wednesday."}]}]}]}], "created_at": "2025-05-22T22:00:49.334324"}