{"date": "2024-02-06", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1707181078.853429", "text": "<@U04DKEFP1K8> Eng priorities for next day are updated on the <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities board>\n• Highest priority is *Password Reset flow*, since we're about to open up to ~10 employees at SDF with local login\n• If all tickets on this board are done, or someone is done and all others are assigned, they can continue working on Wave 3 tickets. \n• Let me know if this format works for you &amp; team, or we can adjust if there are concerns!", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1707181078.853429", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "pAUuV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are updated on the "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities board"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Highest priority is"}, {"type": "text", "text": " Password Reset flow", "style": {"bold": true}}, {"type": "text", "text": ", since we're about to open up to ~10 employees at SDF with local login"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "If all tickets on this board are done, or someone is done and all others are assigned, they can continue working on Wave 3 tickets. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me know if this format works for you & team, or we can adjust if there are concerns!"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1707161273.457169", "text": "Hey all: what's the intended meaning of the \"Current Equity\" column?\n• Is it supposed to be the sum of the \"Vested Equity\" and \"Unvested Equity\" columns, or some calculation?\n• If it is just the sum of Vested &amp; Unvested, do we need all 3 of these columns?\nThis is just a rough mock as I'm playing around with the data, and how we would be able to show % vested in addition to total granted equity:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1707161273.457169", "reply_count": 3, "files": [{"id": "F06HYT68472", "created": 1707161267, "timestamp": 1707161267, "name": "Screenshot 2024-02-05 at 11.25.05 AM.png", "title": "Screenshot 2024-02-05 at 11.25.05 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 110177, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06HYT68472/screenshot_2024-02-05_at_11.25.05___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06HYT68472/download/screenshot_2024-02-05_at_11.25.05___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_360.png", "thumb_360_w": 360, "thumb_360_h": 71, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_480.png", "thumb_480_w": 480, "thumb_480_h": 95, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_720.png", "thumb_720_w": 720, "thumb_720_h": 142, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_800.png", "thumb_800_w": 800, "thumb_800_h": 158, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_960.png", "thumb_960_w": 960, "thumb_960_h": 190, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 202, "original_w": 2348, "original_h": 464, "thumb_tiny": "AwAJADDS/Gj8aWigBPxo/GlooASilooA/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06HYT68472/screenshot_2024-02-05_at_11.25.05___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06HYT68472-c4ee29c046", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "KBgbt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey all: what's the intended meaning of the \"Current Equity\" column?\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is it supposed to be the sum of the \"Vested Equity\" and \"Unvested Equity\" columns, or some calculation?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "If it is just the sum of Vested & Unvested, do we need all 3 of these columns?"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nThis is just a rough mock as I'm playing around with the data, and how we would be able to show % vested in addition to total granted equity:"}]}]}]}], "created_at": "2025-05-22T21:35:34.584530"}