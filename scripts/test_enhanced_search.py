#!/usr/bin/env python3
"""
Test the enhanced search functionality with structured responses and citations.
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def test_search_api(query, description):
    """Test search API with a specific query."""
    print(f"\n🔍 Testing: {description}")
    print(f"Query: '{query}'")
    print("=" * 80)
    
    # Make API request
    url = "http://localhost:8000/search/query/"
    data = {
        'query': query,
        'csrfmiddlewaretoken': 'test'  # For testing
    }
    
    try:
        # Get CSRF token first
        session = requests.Session()
        csrf_response = session.get("http://localhost:8000/search/")
        
        if csrf_response.status_code == 200:
            # Extract CSRF token from response
            csrf_token = None
            for line in csrf_response.text.split('\n'):
                if 'csrfmiddlewaretoken' in line and 'value=' in line:
                    start = line.find('value="') + 7
                    end = line.find('"', start)
                    csrf_token = line[start:end]
                    break
            
            if csrf_token:
                data['csrfmiddlewaretoken'] = csrf_token
                
                response = session.post(url, data=data)
                
                if response.status_code == 200:
                    # Parse the response to extract the answer and citations
                    html_content = response.text
                    
                    # Extract answer from the response HTML
                    answer_start = html_content.find('<div class="response-container"')
                    if answer_start != -1:
                        answer_start = html_content.find('>', answer_start) + 1
                        answer_end = html_content.find('</div>', answer_start)
                        answer = html_content[answer_start:answer_end].strip()
                        
                        print(f"✅ Answer:")
                        print(answer)
                        print()
                    
                    # Count citations
                    citation_count = html_content.count('class="source-item"')
                    print(f"📊 Citations found: {citation_count}")
                    
                    # Check for "View Source" links
                    view_source_count = html_content.count('View Source')
                    print(f"🔗 View Source links: {view_source_count}")
                    
                    # Check for profile attribution
                    if 'amanda' in query.lower():
                        amanda_mentions = html_content.count('amanda')
                        print(f"👤 Amanda mentions: {amanda_mentions}")
                    
                    return True
                else:
                    print(f"❌ HTTP Error: {response.status_code}")
                    print(response.text[:500])
                    return False
            else:
                print("❌ Could not extract CSRF token")
                return False
        else:
            print(f"❌ Could not get CSRF token: {csrf_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Run enhanced search tests."""
    print("🚀 Testing Enhanced Search Functionality")
    print("=" * 80)
    
    # Test queries that should trigger specific formatting
    test_queries = [
        ("List issues reported by Amanda", "List Issues Query - Should use LIST_ISSUES_TEMPLATE"),
        ("Summarize issues reported about curana", "Summarize Issues Query - Should use SUMMARIZE_ISSUES_TEMPLATE"),
        ("What problems has Amanda mentioned?", "General Issues Query - Should detect Amanda"),
        ("Show me all bugs found in the system", "List Issues Query - General"),
        ("Overview of problems with integrations", "Summary Query - General"),
    ]
    
    success_count = 0
    total_count = len(test_queries)
    
    for query, description in test_queries:
        success = test_search_api(query, description)
        if success:
            success_count += 1
        
        print("\n" + "="*80)
    
    print(f"\n📊 Test Results: {success_count}/{total_count} successful")
    
    if success_count == total_count:
        print("🎉 All tests passed! Enhanced search is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
