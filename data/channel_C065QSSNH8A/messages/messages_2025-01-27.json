{"date": "2025-01-27", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1737997522.972009", "text": "Ok. Will be using <http://qa.stridehr.io|qa.stridehr.io> for next few days. Will let you know if not needed.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737997522.972009", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "ttj1+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. Will be using "}, {"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}, {"type": "text", "text": " for next few days. Will let you know if not needed."}]}]}]}, {"ts": "1737997452.217909", "text": "Don't use stridedemo please. The others are fine", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Un4+l", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Don't use stridedemo please. The others are fine"}]}]}]}, {"ts": "1737997434.066599", "text": "Also Enable SSO for Diversified; that was sent over in email this morning.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737997434.066599", "reply_count": 4, "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Kjhxh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also Enable SSO for Diversified; that was sent over in email this morning."}]}]}]}, {"ts": "1737996708.166389", "text": "We may need it until end of this week.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7sbMY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We may need it until end of this week."}]}]}]}, {"ts": "1737996694.226449", "text": "<@U07EJ2LP44S> Can we use any of \"stridedemo\",  \"qa\" or \"test\" for three days? We need to share an ENV with security agency team for pen test. So checking if we can use any existing one.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nCkhT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Can we use any of \"stridedemo\",  \"qa\" or \"test\" for three days? We need to share an ENV with security agency team for pen test. So checking if we can use any existing one."}]}]}]}, {"ts": "1737996549.021619", "text": "<@U07EJ2LP44S> There are few things still pending from engineering end\n• enable SSO for VG recommenders. I will do this in a day along with Diven\n• Compa Ratio matix range correction in cycle builder per <PERSON><PERSON><PERSON>'s requirement\nBoth will be done tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737996549.021619", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "8kE4Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " There are few things still pending from engineering end\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "enable SSO for VG recommenders. I will do this in a day along with <PERSON><PERSON>"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Compa Ratio matix range correction in cycle builder per <PERSON><PERSON><PERSON>'s requirement"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nBoth will be done tomorrow."}]}]}]}, {"ts": "1737990865.104889", "text": "<@U07EJ2LP44S> I am available until 11am PST for any help as mentioned earlier.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "vi3wy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am available until 11am PST for any help as mentioned earlier."}]}]}]}, {"ts": "1737959347.558319", "text": "<@U07EJ2LP44S> update on T<PERSON><PERSON> sync:\nHave made the updates to ENV. Also deleted all existing cycles. `Delta` sheet in the link below will explain what changed for each employee. We can discuss if any questions.\n<https://docs.google.com/spreadsheets/d/13dOmV-y6bBxdMgHnE2IoZ3oOAPQIHWnK9Fuo54owwG4/edit?usp=sharing>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737959347.558319", "reply_count": 21, "edited": {"user": "U0690EB5JE5", "ts": "1737960186.000000"}, "blocks": [{"type": "rich_text", "block_id": "e/Hxh", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " update on Tithley sync:\nHave made the updates to ENV. Also deleted all existing cycles. "}, {"type": "text", "text": "Delta", "style": {"code": true}}, {"type": "text", "text": " sheet in the link below will explain what changed for each employee. We can discuss if any questions.\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/13dOmV-y6bBxdMgHnE2IoZ3oOAPQIHWnK9Fuo54owwG4/edit?usp=sharing"}]}]}]}, {"ts": "1737953411.708969", "text": "<@U07EJ2LP44S> Curana ENV is reset.\n• Pulled data from the dump shared above\n• And filtered employees who are eligible for cycle and also made sure to include managers even if they are not eligible to keep the hierarchy. Filtered the employee list from sheet above `2025 Revieiws.xlsx` . I observed around 4 employees ('6847', '3939', '4193', '3782') from the eligible list are missing or inactive in dump shared by customer.\n• Cycle is not created yet. Lets create once they confirm data looks good\n• Uploaded bands as well\nPlease review and let me know if any issues.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737652485.009819", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1737953549.000000"}, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "0cGOl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Curana ENV is reset.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Pulled data from the dump shared above"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "And filtered employees who are eligible for cycle and also made sure to include managers even if they are not eligible to keep the hierarchy. Filtered the employee list from sheet above "}, {"type": "text", "text": "2025 Revieiws.xlsx", "style": {"code": true}}, {"type": "text", "text": " . I observed around 4 employees ('6847', '3939', '4193', '3782') from the eligible list are missing or inactive in dump shared by customer."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle is not created yet. Lets create once they confirm data looks good"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Uploaded bands as well"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nPlease review and let me know if any issues."}]}]}]}], "created_at": "2025-05-22T21:35:34.704552"}