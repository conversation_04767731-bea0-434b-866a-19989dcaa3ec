{"date": "2024-04-16", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1713220217.673779", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> Based on this morning's discussions, I've started the doc for <https://docs.google.com/document/d/1R6UqowpVM15BBLW0H3_9CPC89nuCHdMtdIlmhGLOJfs/edit#heading=h.je216lwhkaxu|Pay Bands requirements> here. I haven't yet completed the requirements for Search capabilities, but the new format for navigation &amp; visual for employee count are things we can already start implementing. Let me know if you have any questions.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713220217.673779", "reply_count": 1, "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "files": [{"id": "F06V3JEF4PJ", "created": 1713220221, "timestamp": 1713220221, "name": "Pay Bands v1 completion", "title": "Pay Bands v1 completion", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1R6UqowpVM15BBLW0H3_9CPC89nuCHdMtdIlmhGLOJfs", "external_url": "https://docs.google.com/document/d/1R6UqowpVM15BBLW0H3_9CPC89nuCHdMtdIlmhGLOJfs/edit#heading=h.je216lwhkaxu", "url_private": "https://docs.google.com/document/d/1R6UqowpVM15BBLW0H3_9CPC89nuCHdMtdIlmhGLOJfs/edit#heading=h.je216lwhkaxu", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06V3JEF4PJ-440a962f61/pay_bands_v1_completion_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSOc8MB+FADd2H5UNj/wCvSDJP3qADDf3h+VOpMH1owfWgBaKKKAEY47Z/GkU57AUrDJ6ZpMH0FADqKbg/5NL83tQAtFA96KAGtSbvf9KcxHQikGMen40AGT6/pRk+opePX9aOPX9aAFooFFAH/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06V3JEF4PJ/pay_bands_v1_completion", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ntrMS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Based on this morning's discussions, I've started the doc for "}, {"type": "link", "url": "https://docs.google.com/document/d/1R6UqowpVM15BBLW0H3_9CPC89nuCHdMtdIlmhGLOJfs/edit#heading=h.je216lwhkaxu", "text": "Pay Bands requirements"}, {"type": "text", "text": " here. I haven't yet completed the requirements for Search capabilities, but the new format for navigation & visual for employee count are things we can already start implementing. Let me know if you have any questions."}]}]}]}], "created_at": "2025-05-22T21:35:34.604838"}