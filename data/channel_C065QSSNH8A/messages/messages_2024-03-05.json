{"date": "2024-03-05", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1709590860.801069", "text": "Here is the document which explain how to connect to production db <https://docs.google.com/document/d/1TPOQshkYQ6spka0RwC4j1B_-3u7RAxuwjhsnpVGb9MI/edit>", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "VwnmO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the document which explain how to connect to production db "}, {"type": "link", "url": "https://docs.google.com/document/d/1TPOQshkYQ6spka0RwC4j1B_-3u7RAxuwjhsnpVGb9MI/edit"}]}]}]}, {"ts": "1709590799.173499", "text": "Yes it is easier to sort it out in database table, also she should really use the custom job title if she is sure she has not provided us the band she wats to use", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8g8RO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes it is easier to sort it out in database table, also she should really use the custom job title if she is sure she has not provided us the band she wats to use"}]}]}]}, {"ts": "1709590624.240619", "text": "I have been looking mostly at the CSV output so sometimes forget to look at how it shows in the actual Merit view / how to figure out it's a nonstandard title :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vXZt3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have been looking mostly at the CSV output so sometimes forget to look at how it shows in the actual Merit view / how to figure out it's a nonstandard title "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1709590586.840839", "text": "Ahhhh sorry, I missed that one too. Thank you for explaining <@U04DKEFP1K8>.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Oy71+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ahhhh sorry, I missed that one too. Thank you for explaining "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1709590552.979249", "text": "for the promoted employees <PERSON><PERSON> has created a custom job title whenever she did not found a title which she wants to assisgn and that is creating the issues", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oIN13", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "for the promoted employees <PERSON><PERSON> has created a custom job title whenever she did not found a title which she wants to assisgn and that is creating the issues"}]}]}]}, {"ts": "1709590464.384139", "text": "The issue with <PERSON> zhu is the same as one with <PERSON><PERSON> where <PERSON><PERSON> has not selected the next band provided per salary but created a new job title\n\nIf she would have selected pre-existing next level band per the salary range , system would have calculated <PERSON>'s new comp ratio correctly\n\n$154980 / $147500 ( 1.05)\n\nCurrently she is using a promoted job title \"Sr Growth Marketing Manager\" with a Salary Target of $113400 CAD  and hence system is reporting $154980 / $113400 = 1.3666", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8x4qR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The issue with <PERSON> zhu is the same as one with <PERSON><PERSON> where <PERSON><PERSON> has not selected the next band provided per salary but created a new job title\n\nIf she would have selected pre-existing next level band per the salary range , system would have calculated <PERSON>'s new comp ratio correctly\n\n$154980 / $147500 ( 1.05)\n\nCurrently she is using a promoted job title \"Sr Growth Marketing Manager\" with a Salary Target of $113400 CAD  and hence system is reporting $154980 / $113400 = 1.3666"}]}]}]}, {"ts": "1709588011.507719", "text": "Let me get back on this at 2 pm( wrapping up lunch)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Vx+NH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me get back on this at 2 pm( wrapping up lunch)"}]}]}]}, {"ts": "1709587935.415969", "text": "Hey <@U04DKEFP1K8> I'm trying to figure out why the \"new\" (after cycle) salary band &amp; compa ratio for \"Blair Zhu\" seems to be wrong in the downloadable \"Post Merit Roster\" report, because I think I reported something incorrectly to <PERSON> as a result.\n\nAre promotions not necessarily reflected in this report before things are finalized? Or any other reason why the \"New_Salary_Max\" is lower than \"Old_Salary_Max\", and the New_Compensation_Ratio is ~1.36 when it should be 1.05?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1ysCw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I'm trying to figure out why the \"new\" (after cycle) salary band & compa ratio for \"Blair Zhu\" seems to be wrong in the downloadable \"Post Merit Roster\" report, because I think I reported something incorrectly to <PERSON> as a result.\n\nAre promotions not necessarily reflected in this report before things are finalized? Or any other reason why the \"New_Salary_Max\" is lower than \"Old_Salary_Max\", and the New_Compensation_Ratio is ~1.36 when it should be 1.05?"}]}]}]}, {"ts": "1709586014.774699", "text": "<@U065H3M6WJV> here is analysis of why <PERSON><PERSON> is unable to view correct band for <PERSON><PERSON>\nThis is the current and next band details available for <PERSON><PERSON>\nInstead of selecting the next band which is available for <PERSON><PERSON> , she is adding a custom job title for <PERSON><PERSON>. When we add a custom job title we do not know wheat is going to be new range for it so we default to existing range itself.\nIf she selects the L4 level she has provided in her salary bands she will she the right range", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1709586014.774699", "reply_count": 1, "files": [{"id": "F06N7T6RDR7", "created": 1709585882, "timestamp": 1709585882, "name": "Screenshot 2024-03-04 at 12.55.43 PM.png", "title": "Screenshot 2024-03-04 at 12.55.43 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 61346, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06N7T6RDR7/screenshot_2024-03-04_at_12.55.43___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06N7T6RDR7/download/screenshot_2024-03-04_at_12.55.43___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 20, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 26, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 39, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 44, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 52, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06N7T6RDR7-ca88c30e16/screenshot_2024-03-04_at_12.55.43___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 56, "original_w": 2900, "original_h": 158, "thumb_tiny": "AwACADDSP9aQdaU/1pB1NAA3am55pzdqZ3oAe3SkP3FpW6Uh+4tZz/QZ/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06N7T6RDR7/screenshot_2024-03-04_at_12.55.43___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06N7T6RDR7-71f3763cb4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ACnfg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " here is analysis of why <PERSON><PERSON> is unable to view correct band for <PERSON><PERSON>\nThis is the current and next band details available for <PERSON><PERSON>\nInstead of selecting the next band which is available for <PERSON><PERSON> , she is adding a custom job title for <PERSON><PERSON>. When we add a custom job title we do not know wheat is going to be new range for it so we default to existing range itself.\nIf she selects the L4 level she has provided in her salary bands she will she the right range"}]}]}]}], "created_at": "2025-05-22T21:35:34.612568"}