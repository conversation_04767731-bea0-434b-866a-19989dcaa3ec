{"date": "2023-12-21", "channel_id": "C065QSSNH8A", "message_count": 19, "messages": [{"ts": "1703136218.854179", "text": "In my testing the biggest issue I've experienced that is more of a usability thing than anything (and perhaps my lack of understanding on the tool) is that it is very difficult for an admin, like <PERSON><PERSON><PERSON>, to see a single manager and their view. <PERSON><PERSON> doesn't work on the Organization page (logged in Ji<PERSON>) and Manager view / Direct Reports really doesn't address the issue at all. We can iterate the tool but long term I think this is a problem we need to solve. Additionally, from what I can tell, <PERSON><PERSON><PERSON> will be unable to modify budget for anyone but her own hierarchy which is incorrect IMO. I will do more testing in the AM but this is my biggest thought from what I reviewed tonight", "user": "U0658EW4B8D", "type": "message", "thread_ts": "1703136218.854179", "reply_count": 2, "reactions": [{"name": "face_with_monocle", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "aXeEe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In my testing the biggest issue I've experienced that is more of a usability thing than anything (and perhaps my lack of understanding on the tool) is that it is very difficult for an admin, like <PERSON><PERSON><PERSON>, to see a single manager and their view. <PERSON><PERSON> doesn't work on the Organization page (logged in Ji<PERSON>) and Manager view / Direct Reports really doesn't address the issue at all. We can iterate the tool but long term I think this is a problem we need to solve. Additionally, from what I can tell, <PERSON><PERSON><PERSON> will be unable to modify budget for anyone but her own hierarchy which is incorrect IMO. I will do more testing in the AM but this is my biggest thought from what I reviewed tonight"}]}]}]}, {"ts": "1703130514.951529", "text": "<@U04DKEFP1K8> ^", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qZ4AH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " ^"}]}]}]}, {"ts": "1703130510.676649", "text": "<PERSON><PERSON><PERSON> has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R06BGNLFA8Z", "block_id": "8L1+Y", "api_decoration_available": false, "call": {"v1": {"id": "R06BGNLFA8Z", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1703130510, "active_participants": [], "all_participants": [{"slack_id": "U065H3M6WJV"}, {"slack_id": "U04DKEFP1K8"}, {"slack_id": "U04DS2MBWP4"}], "display_id": "813-1205-5458", "join_url": "https://us06web.zoom.us/j/81312055458?pwd=G3PG2TefdqhPq9w2TX6roantyDc5Bq.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEXzUwZmRmZjM4NTNkNjQzOGQ4YTI3OTk1YmE2ODliZjE3JnVzcz1xajRKMGl5aG9BNUE0QlRDZUhSZjFiYzVVMGhXT3JDSHJsdEV3OXAyM3pwN3lkaUZqRnYxR3p3LXZnUU5VbDVpa194YjZoNHpVY09Rd3Bib001MFlZMW9oT205VjZEMllVVTBUcFl3LnFGc21fOVlKLUh4bGFINUY%3D&action=join&confno=81312055458&pwd=G3PG2TefdqhPq9w2TX6roantyDc5Bq.1", "name": "Zoom meeting started by <PERSON>", "created_by": "U05185RFCNT", "date_end": 1703132910, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "m+hvO", "text": {"type": "mrkdwn", "text": "Meeting passcode: G3PG2TefdqhPq9w2TX6roantyDc5Bq.1", "verbatim": false}}]}, {"ts": "1703130302.091569", "text": "<!here> guys i am available now. Can we jump on a call", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "tDhLN", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " guys i am available now. Can we jump on a call"}]}]}]}, {"ts": "1703129055.408379", "text": "Looks like we had a partial cycle setup so I just \"launched\" it. May not get us the perfect setup but we can at least test <!here>", "user": "U0658EW4B8D", "type": "message", "thread_ts": "1703129055.408379", "reply_count": 4, "reactions": [{"name": "wow", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "VxrQw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Looks like we had a partial cycle setup so I just \"launched\" it. May not get us the perfect setup but we can at least test "}, {"type": "broadcast", "range": "here"}]}]}]}, {"ts": "1703123126.071739", "text": "<@U04DKEFP1K8> pls call me when you are up.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1703123126.071739", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "qRTqQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " pls call me when you are up."}]}]}]}, {"ts": "1703122752.191789", "text": "This is a big bummer. <@U04DKEFP1K8> this is your TOP priority", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wvAvc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is a big bummer. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " this is your TOP priority"}]}]}]}, {"ts": "1703122684.522409", "text": "Update on DA UAT: I cannot successfully create a new cycle; I keep hitting a blocking error on the \"Bonus Planning\" step. :disappointed:\n\nI logged the ~10 issues that I saw in my testing in the <https://compiify.atlassian.net/browse/COM-1859|COM-1859> ticket for engineering.", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F06B0QMP0PP", "created": 1703122574, "timestamp": 1703122574, "name": "Screenshot 2023-12-20 at 5.35.22 PM.png", "title": "Screenshot 2023-12-20 at 5.35.22 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 347282, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06B0QMP0PP/screenshot_2023-12-20_at_5.35.22___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06B0QMP0PP/download/screenshot_2023-12-20_at_5.35.22___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 250, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 333, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 500, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 556, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 667, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 711, "original_w": 3834, "original_h": 2663, "thumb_tiny": "AwAhADDRwDRgA0o5FLQAnFHHtS0UAJgelGKWigAFFFFABRRRQAUUUUAAooFFABRRRQAUUUUAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06B0QMP0PP/screenshot_2023-12-20_at_5.35.22___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06B0QMP0PP-56a38f8c96", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:11920::965c81a09fa111eea9aa2f40127b488a", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-1859?atlOrigin=eyJpIjoiZGIwMWNmNzE1YzZhNGU5M2E4ODQ1YjJiMWNmOTE2ZGMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-1859 Digital Asset UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:11920::965c81a29fa111eea9aa2f40127b488a", "elements": [{"type": "mrkdwn", "text": "Status: *In Progress*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:11920::965c81a19fa111eea9aa2f40127b488a", "elements": [{"type": "button", "action_id": "{\"issueId\":\"11920\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"11920\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-1859", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "t9ZlS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Update on DA UAT: I cannot successfully create a new cycle; I keep hitting a blocking error on the \"Bonus Planning\" step. "}, {"type": "emoji", "name": "disappointed", "unicode": "1f61e"}, {"type": "text", "text": "\n\nI logged the ~10 issues that I saw in my testing in the "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1859", "text": "COM-1859"}, {"type": "text", "text": " ticket for engineering."}]}]}]}, {"ts": "1703116950.470439", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> For tomorrow's product demos with Neuroflow and Meati:\n1. The <http://qa.compiify.com|qa.compiify.com> environment is giving an internal error when I attempt to log in as <mailto:<EMAIL>|<EMAIL>>. \n2. I can log in to <http://staging.compiify.com|staging.compiify.com> with <mailto:<EMAIL>|<EMAIL>> and a dummy password, but \n    ◦ Can't demo Google login there\n    ◦ Can't create a new comp cycle (currently hitting an \"Unexpected Application Error\")\n3. I can log in to <http://dev-app.compiify.com|dev-app.compiify.com> using Google login for <mailto:<EMAIL>|<EMAIL>>. This one has an open cycle with a few recommendations already filled in.\n    ◦ Can we expect this to be stable tomorrow for 2 different demos?\n    ◦ Does anything need to be different in the Merit recommendations so that the demo shows what we want to highlight?\n    ◦ Is it okay if we do not demo a real \"cycle creation\"? (You can probably walk through some of the steps, but cannot create a new cycle without closing the old one, and that might not work smoothly.)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1703116950.470439", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "wyxVv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For tomorrow's product demos with Neuroflow and Meati:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The "}, {"type": "link", "url": "http://qa.compiify.com", "text": "qa.compiify.com"}, {"type": "text", "text": " environment is giving an internal error when I attempt to log in as "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ". "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "I can log in to "}, {"type": "link", "url": "http://staging.compiify.com", "text": "staging.compiify.com"}, {"type": "text", "text": " with "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " and a dummy password, but "}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can't demo Google login there"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Can't create a new comp cycle (currently hitting an \"Unexpected Application Error\")"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can log in to "}, {"type": "link", "url": "http://dev-app.compiify.com", "text": "dev-app.compiify.com"}, {"type": "text", "text": " using Google login for "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ". This one has an open cycle with a few recommendations already filled in."}]}], "style": "ordered", "indent": 0, "offset": 2, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can we expect this to be stable tomorrow for 2 different demos?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Does anything need to be different in the Merit recommendations so that the demo shows what we want to highlight?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Is it okay if we do not demo a real \"cycle creation\"? (You can probably walk through some of the steps, but cannot create a new cycle without closing the old one, and that might not work smoothly.)"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}, {"ts": "**********.212759", "text": "<@U04DKEFP1K8> do we have google analytics account for the website?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.212759", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "sxcse", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " do we have google analytics account for the website?"}]}]}]}, {"ts": "**********.599339", "text": "<@U0658EW4B8D> if we have access to Radford and Options Impact thru customer, how long would it take to create salary bands for them if they don't have salary bands to begin with?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.599339", "reply_count": 16, "blocks": [{"type": "rich_text", "block_id": "Q5GLe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " if we have access to Radford and Options Impact thru customer, how long would it take to create salary bands for them if they don't have salary bands to begin with?"}]}]}]}, {"ts": "**********.568209", "text": "<@U065H3M6WJV> In Meati's call today, they said there is no tool available for companies of their size (100 to 400 emp). A tool which is easier to implement and affordable. This could be a white space we need", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.568209", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "OKJPO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " In <PERSON><PERSON>'s call today, they said there is no tool available for companies of their size (100 to 400 emp). A tool which is easier to implement and affordable. This could be a white space we need"}]}]}]}, {"ts": "1703108014.287159", "text": "BTW <@U04DS2MBWP4> if we want to try Zoom's AI auto-transcript/analysis as an alternative to Otter I think you have to enable a setting as the admin", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1703108014.287159", "reply_count": 19, "files": [{"id": "F06B2PYD335", "created": 1703108010, "timestamp": 1703108010, "name": "Screenshot 2023-12-20 at 1.32.13 PM.png", "title": "Screenshot 2023-12-20 at 1.32.13 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 430544, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06B2PYD335/screenshot_2023-12-20_at_1.32.13___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06B2PYD335/download/screenshot_2023-12-20_at_1.32.13___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 186, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 248, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 372, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 413, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 496, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 529, "original_w": 3210, "original_h": 1658, "thumb_tiny": "AwAYADDSxSD6U6k5oATj0pQAe1HPtS/WgAooooAKbg+tOooATn2ox60tFABRRRQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06B2PYD335/screenshot_2023-12-20_at_1.32.13___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06B2PYD335-aad1b45a26", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "e1khM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "BTW "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " if we want to try Zoom's AI auto-transcript/analysis as an alternative to O<PERSON> I think you have to enable a setting as the admin"}]}]}]}, {"ts": "1703107312.034799", "text": "For today's call with <PERSON>, I decided to use the time getting his <https://www.figma.com/file/2caUVa1XZJ7cuxSJ9HHK9v/Annual-patterns-in-Comp%2FHR?type=whiteboard&amp;node-id=0-1&amp;t=qh8OQNazM5yz6xkh-0|map of the comp \"calendar\"> from his previous role(s), because I'm noticing how our customers &amp; advisors are trying to juggle comp management with other time-sensitive priorities.\n\nIt also helps us understand how often different members of the org will be interacting with our tools depending on which \"jobs\" we are helping them get done; I captured those at a high level in the categories of <PERSON>'s <https://docs.google.com/presentation/d/1i1ULo4tA3vVK0h--k1MV2c_J7kmZwGhJpR5AX8JBEPM/edit#slide=id.g2a42f70c363_0_116|HR landscape here> (with his input).", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1703107312.034799", "reply_count": 4, "edited": {"user": "U065H3M6WJV", "ts": "1703107322.000000"}, "blocks": [{"type": "rich_text", "block_id": "/HTuA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For today's call with <PERSON>, I decided to use the time getting his "}, {"type": "link", "url": "https://www.figma.com/file/2caUVa1XZJ7cuxSJ9HHK9v/Annual-patterns-in-Comp%2FHR?type=whiteboard&node-id=0-1&t=qh8OQNazM5yz6xkh-0", "text": "map of the comp \"calendar\""}, {"type": "text", "text": " from his previous role(s), because I'm noticing how our customers & advisors are trying to juggle comp management with other time-sensitive priorities.\n\nIt also helps us understand how often different members of the org will be interacting with our tools depending on which \"jobs\" we are helping them get done; I captured those at a high level in the categories of <PERSON>'s "}, {"type": "link", "url": "https://docs.google.com/presentation/d/1i1ULo4tA3vVK0h--k1MV2c_J7kmZwGhJpR5AX8JBEPM/edit#slide=id.g2a42f70c363_0_116", "text": "HR landscape here"}, {"type": "text", "text": " (with his input)."}]}]}]}, {"ts": "1703098726.078779", "text": "next meeting 11:10", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "f41VS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "next meeting 11:10"}]}]}]}, {"ts": "1703176601.797219", "text": "I think that security/compliance call can be pushed. I don't think there is any rush or time sensitive deadline we have for security/compliance", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1703176601.797219", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "xzhAC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think that security/compliance call can be pushed. I don't think there is any rush or time sensitive deadline we have for security/compliance"}]}]}]}, {"ts": "1703176452.716649", "text": "Adding some subtasks in Jira this AM", "user": "U0658EW4B8D", "type": "message", "edited": {"user": "U0658EW4B8D", "ts": "1703176475.000000"}, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}, {"name": "meow_thx", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "dP37K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Adding some subtasks in Jira this AM"}]}]}]}, {"ts": "1703176277.489689", "text": "Moved our working session by 30min to allow for the compliance call y'all have, and I'll use those 30 min to sanity check the environments before we're on a group call", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hRtxY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Moved our working session by 30min to allow for the compliance call y'all have, and I'll use those 30 min to sanity check the environments before we're on a group call"}]}]}]}, {"ts": "1703171268.542169", "text": "<!here> <http://staging.compiify.com|staging.compiify.com> is updated with latest build. <@U065H3M6WJV> <@U04DS2MBWP4> please check and confirm the environment looks good. <@U065H3M6WJV> google auth is enabled as well on the environment ( email: <mailto:<EMAIL>|<EMAIL>>)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "SEX/p", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://staging.compiify.com", "text": "staging.compiify.com"}, {"type": "text", "text": " is updated with latest build. "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " please check and confirm the environment looks good. "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " google auth is enabled as well on the environment ( email: "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ")"}]}]}]}], "created_at": "2025-05-22T21:35:34.558096"}