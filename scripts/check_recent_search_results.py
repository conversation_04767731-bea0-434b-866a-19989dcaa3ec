#!/usr/bin/env python
"""
Check recent search results and their citations.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from apps.search.models import SearchResult, ResultCitation
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

def check_recent_search_results():
    """Check recent search results and their citations."""
    print("🔍 Checking Recent Search Results")
    print("=" * 50)

    # Get recent search results (last 10)
    recent_results = SearchResult.objects.order_by('-timestamp')[:10]

    print(f"📊 Found {recent_results.count()} recent search results")

    for i, result in enumerate(recent_results):
        print(f"\n🔍 Result {i+1}: ID {result.id}")
        print(f"   Query: {result.search_query.query_text}")
        print(f"   User: {result.user.email if result.user else 'No user'}")
        print(f"   Tenant: {result.search_query.tenant.slug}")
        print(f"   Created: {result.timestamp}")
        print(f"   Answer length: {len(result.generated_answer)} chars")

        # Check citations
        citations = result.citations.all()
        print(f"   Citations: {citations.count()}")

        if citations.exists():
            print(f"   📖 Citation details:")
            for j, citation in enumerate(citations[:3]):
                print(f"      {j+1}. Chunk {citation.document_chunk.id} (score: {citation.relevance_score:.3f})")
                if citation.document_chunk.document:
                    doc = citation.document_chunk.document
                    print(f"         Document: {doc.title[:50]}...")
                    if doc.permalink:
                        print(f"         Permalink: {doc.permalink}")
                    else:
                        print(f"         ❌ No permalink")
                else:
                    print(f"         ❌ No document linked")
        else:
            print(f"   ❌ No citations")

        # Show answer preview
        print(f"   📄 Answer preview: {result.generated_answer[:100]}...")

    # Check overall citation statistics
    print(f"\n📊 Overall Citation Statistics:")
    total_results = SearchResult.objects.count()
    results_with_citations = SearchResult.objects.filter(citations__isnull=False).distinct().count()
    total_citations = ResultCitation.objects.count()

    print(f"   Total search results: {total_results}")
    print(f"   Results with citations: {results_with_citations}")
    print(f"   Total citations: {total_citations}")
    print(f"   Average citations per result: {total_citations / total_results if total_results > 0 else 0:.2f}")

    # Check recent citations (based on result timestamp)
    recent_citations = ResultCitation.objects.filter(
        result__timestamp__gte=timezone.now() - timedelta(hours=1)
    ).count()
    print(f"   Citations created in last hour: {recent_citations}")

if __name__ == "__main__":
    check_recent_search_results()
