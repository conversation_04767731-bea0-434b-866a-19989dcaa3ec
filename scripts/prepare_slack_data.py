#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to prepare Slack data for ingestion by consolidating message files.
"""

import os
import json
import glob
from pathlib import Path

def consolidate_slack_messages():
    """Consolidate Slack message files into a single JSON file per month."""

    # Source directory with structured data
    source_dir = Path("../data/channel_C065QSSNH8A/messages")

    # Target directory for consolidated data
    target_dir = Path("../data/consolidated")
    target_dir.mkdir(exist_ok=True)

    print(f"📁 Source directory: {source_dir}")
    print(f"📁 Target directory: {target_dir}")

    if not source_dir.exists():
        print(f"❌ Source directory does not exist: {source_dir}")
        return

    # Get all message files
    message_files = list(source_dir.glob("messages_*.json"))
    print(f"📄 Found {len(message_files)} message files")

    # Group files by month
    monthly_messages = {}

    for message_file in message_files:
        print(f"📖 Processing: {message_file.name}")

        try:
            with open(message_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Extract date from filename (messages_YYYY-MM-DD.json)
            date_str = message_file.stem.replace("messages_", "")
            month_key = date_str[:7]  # YYYY-MM

            # Get messages from the data
            messages = data.get('messages', [])
            print(f"  📝 Found {len(messages)} messages for {date_str}")

            # Add to monthly collection
            if month_key not in monthly_messages:
                monthly_messages[month_key] = []

            monthly_messages[month_key].extend(messages)

        except Exception as e:
            print(f"❌ Error processing {message_file}: {e}")

    # Create consolidated monthly files
    total_messages = 0
    for month, messages in monthly_messages.items():
        # Sort messages by timestamp
        messages.sort(key=lambda x: x.get('ts', '0'))

        # Create output file
        output_file = target_dir / f"C065QSSNH8A_{month}.json"

        # Create consolidated data structure
        consolidated_data = {
            "channel_id": "C065QSSNH8A",
            "channel_name": "1-productengineering",
            "month": month,
            "message_count": len(messages),
            "messages": messages
        }

        # Write consolidated file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(consolidated_data, f, indent=2, ensure_ascii=False)

        print(f"✅ Created {output_file} with {len(messages)} messages")
        total_messages += len(messages)

    print(f"\n🎉 Consolidation complete!")
    print(f"📊 Total messages processed: {total_messages}")
    print(f"📁 Monthly files created: {len(monthly_messages)}")
    print(f"📂 Output directory: {target_dir}")

    return target_dir

if __name__ == "__main__":
    consolidate_slack_messages()
