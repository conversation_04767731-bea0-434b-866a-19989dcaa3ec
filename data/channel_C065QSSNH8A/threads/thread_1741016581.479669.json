{"thread_ts": "1741016581.479669", "channel_id": "C065QSSNH8A", "reply_count": 6, "replies": [{"ts": "1741022054.250949", "text": "<@U07MH77PUBV> Please work on this tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741016581.479669", "blocks": [{"type": "rich_text", "block_id": "F7aZe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " Please work on this tomorrow."}]}]}]}, {"ts": "1741078931.309479", "text": "<@U07EJ2LP44S> Please confirm if this looks good", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1741016581.479669", "files": [{"id": "F08FL5369C7", "created": 1741078926, "timestamp": 1741078926, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07MH77PUBV", "user_team": "T04DM97F1UM", "editable": false, "size": 183308, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FL5369C7/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FL5369C7/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FL5369C7-486d179266/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FL5369C7-486d179266/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FL5369C7-486d179266/image_360.png", "thumb_360_w": 360, "thumb_360_h": 198, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FL5369C7-486d179266/image_480.png", "thumb_480_w": 480, "thumb_480_h": 264, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FL5369C7-486d179266/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FL5369C7-486d179266/image_720.png", "thumb_720_w": 720, "thumb_720_h": 395, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FL5369C7-486d179266/image_800.png", "thumb_800_w": 800, "thumb_800_h": 439, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FL5369C7-486d179266/image_960.png", "thumb_960_w": 960, "thumb_960_h": 527, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FL5369C7-486d179266/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 562, "original_w": 1866, "original_h": 1025, "thumb_tiny": "AwAaADDRxwOh+tBOMUo6DNI30oAXI9aUc9DQOlFABTWXJB3Ee1OooASkNLSHtQAppaKSgBaSiigD/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FL5369C7/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FL5369C7-0b09f74417", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "B8pI6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please confirm if this looks good"}]}]}]}, {"ts": "1741101820.787109", "text": "Can you confirm if the current salary will be the amount after the increases of the current cycle? In this example, they are the same so.\n\nAlso, can we confirm that the total rewards will be correct  before the cycle is closed? Meaning, do we have to close the cycle for the current salary to update to what the increase was given?\n\nSince these are replacing letters, we should not have to close the cycle before they are correct. But I’m not sure how that will function. ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741016581.479669", "blocks": [{"type": "rich_text", "block_id": "Spcx9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can you confirm if the current salary will be the amount after the increases of the current cycle? In this example, they are the same so.\n\nAlso, can we confirm that the total rewards will be correct  before the cycle is closed? Meaning, do we have to close the cycle for the current salary to update to what the increase was given?\n\nSince these are replacing letters, we should not have to close the cycle before they are correct. But I’m not sure how that will function. "}]}]}]}, {"ts": "1741101935.574809", "text": "<@U07EJ2LP44S> We have to update the history from backend once they confirm cycle is complete. Cycle closure doesn't update the history. In the example above there was not raise from previous cycle.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741016581.479669", "blocks": [{"type": "rich_text", "block_id": "4+YGa", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We have to update the history from backend once they confirm cycle is complete. Cycle closure doesn't update the history. In the example above there was not raise from previous cycle."}]}]}]}, {"ts": "1741102023.486659", "text": "Ok, that's fine, so long as we can 'trigger' the total rewards to include their increase from this cycle.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741016581.479669", "blocks": [{"type": "rich_text", "block_id": "fzAFs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok, that's fine, so long as we can 'trigger' the total rewards to include their increase from this cycle."}]}]}]}, {"ts": "1741105231.623439", "text": "Oh and just to be super clear, this looks good to me.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741016581.479669", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "LBie0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Oh and just to be super clear, this looks good to me."}]}]}]}], "created_at": "2025-05-22T22:00:49.354037"}