{"date": "2024-07-25", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1721928514.113479", "text": "Feedback from Alayacare- They might need HR business partners role in addition to super admin. where HR business partner will only have access to the data for their corresponding departments. I know we have a HR business partner role in user roles but not sure if we ever worked on it", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721928514.113479", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "B0L5D", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Feedback from Alayacare- They might need HR business partners role in addition to super admin. where HR business partner will only have access to the data for their corresponding departments. I know we have a HR business partner role in user roles but not sure if we ever worked on it"}]}]}]}, {"ts": "1721927862.563089", "text": "<@U04DS2MBWP4> kellie's credentials have been updated in the doc", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "7QdbH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " kellie's credentials have been updated in the doc"}]}]}]}, {"ts": "1721927853.491149", "text": "No functional categorization just show what changed and who changed. One for employee and one for merit planning. image is missing the author of the change. Image is to just convey the gist of it. This way audit will be simple to implement as well. Let me know your thoughts on this.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1721928101.000000"}, "blocks": [{"type": "rich_text", "block_id": "zA92O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No functional categorization just show what changed and who changed. One for employee and one for merit planning. image is missing the author of the change. Image is to just convey the gist of it. This way audit will be simple to implement as well. Let me know your thoughts on this."}]}]}]}, {"ts": "1721927828.926909", "text": "<@U04DS2MBWP4>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8n/+W", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}]}]}]}, {"ts": "1721927821.811799", "text": "for Audit log this was what My thinking.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1721928966.000000"}, "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F07DNGWR5MM", "created": 1721927819, "timestamp": 1721927819, "name": "Screenshot 2024-07-25 at 10.46.31 PM.png", "title": "Screenshot 2024-07-25 at 10.46.31 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 155723, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07DNGWR5MM/screenshot_2024-07-25_at_10.46.31___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07DNGWR5MM/download/screenshot_2024-07-25_at_10.46.31___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07DNGWR5MM-dd9c184421/screenshot_2024-07-25_at_10.46.31___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07DNGWR5MM-dd9c184421/screenshot_2024-07-25_at_10.46.31___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07DNGWR5MM-dd9c184421/screenshot_2024-07-25_at_10.46.31___pm_360.png", "thumb_360_w": 297, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07DNGWR5MM-dd9c184421/screenshot_2024-07-25_at_10.46.31___pm_480.png", "thumb_480_w": 396, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07DNGWR5MM-dd9c184421/screenshot_2024-07-25_at_10.46.31___pm_160.png", "original_w": 573, "original_h": 694, "thumb_tiny": "AwAwACfR7/xUu72P5Ud+lHc8UALmk3ex/KlpB9KAFooooAb/ABdKO/Iowc0YNAC9O1IP92lxikwaAHUUUUAJ/F3o796WigApAfY0tFABRTA7F9pQgf3qf+NAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07DNGWR5MM/screenshot_2024-07-25_at_10.46.31___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07DNGWR5MM-72e9ca3d60", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "EIrJR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "for Audit log this was what My thinking."}]}]}]}, {"ts": "1721864089.584679", "text": "<!here> update on valgenesis qa, running into some unexpected error ( these may go away if i re-upload the data but i will have engg team confirm that first )\n<@U0690EB5JE5> i will discuss these in standup <https://docs.google.com/document/d/1QAmFOUJ6UKSD_JL3WUxoqNCSq6r3ILmDzKjxqGSSy4k/edit>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1721864089.584679", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "AF9pc", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " update on valgenesis qa, running into some unexpected error ( these may go away if i re-upload the data but i will have engg team confirm that first )\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " i will discuss these in standup "}, {"type": "link", "url": "https://docs.google.com/document/d/1QAmFOUJ6UKSD_JL3WUxoqNCSq6r3ILmDzKjxqGSSy4k/edit"}]}]}]}], "created_at": "2025-05-22T21:35:34.616825"}