"""
Gemini LLM integration for the Multi-Source RAG system.

This module provides integration with Google's Gemini API for LlamaIndex:
- Gemini LLM integration
- Gemini embedding models
- LLM management with fallback to Ollama
- Response generation utilities
"""

import logging
import os
from typing import Any, Dict, List, Optional, Union

from django.conf import settings

logger = logging.getLogger(__name__)

# Gemini API configuration
GEMINI_API_KEY = getattr(settings, "GEMINI_API_KEY", "")
GEMINI_MODEL = getattr(settings, "GEMINI_MODEL", "gemini-1.5-flash")
GEMINI_EMBEDDING_MODEL = getattr(settings, "GEMINI_EMBEDDING_MODEL", "embedding-001")

# Fallback to Ollama configuration
OLLAMA_API_HOST = getattr(settings, "OLLAMA_API_HOST", "http://localhost:11434")
OLLAMA_MODEL_NAME = getattr(settings, "OLLAMA_MODEL_NAME", "llama3")


def initialize_gemini_llms() -> None:
    """Initialize and register Gemini LLMs with the registry."""
    from apps.core.utils.llama_index_setup import llama_index_registry

    # Check if Gemini API key is available
    if not GEMINI_API_KEY:
        logger.warning("GEMINI_API_KEY not found, skipping Gemini LLM initialization")
        return

    try:
        # Set the API key for Google AI
        os.environ["GOOGLE_API_KEY"] = GEMINI_API_KEY

        # Import Gemini LLM from LlamaIndex
        from llama_index.llms.gemini import Gemini

        # Initialize Gemini LLM
        gemini_llm = Gemini(
            model=GEMINI_MODEL,
            api_key=GEMINI_API_KEY,
            temperature=0.1,
            max_tokens=8192,  # Increased for longer responses
        )

        # Register with the registry
        llama_index_registry.register_llm("gemini", gemini_llm)
        logger.info(f"Initialized Gemini LLM with model: {GEMINI_MODEL}")

    except ImportError as e:
        logger.error(f"Failed to import Gemini LLM (install llama-index-llms-gemini): {str(e)}")
    except Exception as e:
        logger.error(f"Failed to initialize Gemini LLM: {str(e)}")


def initialize_gemini_embeddings() -> None:
    """Initialize and register Gemini embedding models with the registry."""
    from apps.core.utils.llama_index_setup import llama_index_registry

    # Check if Gemini API key is available
    if not GEMINI_API_KEY:
        logger.warning("GEMINI_API_KEY not found, skipping Gemini embedding initialization")
        return

    try:
        # Set the API key for Google AI
        os.environ["GOOGLE_API_KEY"] = GEMINI_API_KEY

        # Import Gemini embedding from LlamaIndex
        from llama_index.embeddings.gemini import GeminiEmbedding

        # Initialize Gemini embedding model
        gemini_embedding = GeminiEmbedding(
            model_name=GEMINI_EMBEDDING_MODEL,
            api_key=GEMINI_API_KEY,
        )

        # Register with the registry
        llama_index_registry.register_embedding("gemini", gemini_embedding)
        logger.info(f"Initialized Gemini embedding with model: {GEMINI_EMBEDDING_MODEL}")

    except ImportError as e:
        logger.error(f"Failed to import Gemini embedding (install llama-index-embeddings-gemini): {str(e)}")
    except Exception as e:
        logger.error(f"Failed to initialize Gemini embedding: {str(e)}")


def get_gemini_llm(
    model_name: Optional[str] = None,
    temperature: float = 0.1,
    max_tokens: int = 8192,
) -> Any:
    """
    Get a Gemini LLM instance with fallback to Ollama.

    Args:
        model_name: Name of the Gemini model to use
        temperature: Temperature for generation
        max_tokens: Maximum tokens to generate

    Returns:
        LLM instance (Gemini or Ollama fallback)
    """
    from apps.core.utils.llama_index_setup import llama_index_registry

    # Try to get Gemini LLM first
    if GEMINI_API_KEY:
        try:
            # Set the API key for Google AI
            os.environ["GOOGLE_API_KEY"] = GEMINI_API_KEY

            from llama_index.llms.gemini import Gemini

            model_name = model_name or GEMINI_MODEL
            gemini_llm = Gemini(
                model=model_name,
                api_key=GEMINI_API_KEY,
                temperature=temperature,
                max_tokens=max_tokens,
            )

            logger.info(f"Using Gemini LLM with model: {model_name}")
            return gemini_llm

        except ImportError as e:
            logger.warning(f"Gemini LLM not available (install llama-index-llms-gemini): {str(e)}")
        except Exception as e:
            logger.warning(f"Failed to create Gemini LLM: {str(e)}")

    # Fallback to Ollama
    logger.info("Falling back to Ollama LLM")
    try:
        from llama_index.llms.ollama import Ollama

        ollama_llm = Ollama(
            model=OLLAMA_MODEL_NAME,
            base_url=OLLAMA_API_HOST,
            request_timeout=120.0,
            temperature=temperature,
            context_window=4096,
            additional_kwargs={
                "top_p": 0.95,
            },
        )
        return ollama_llm

    except Exception as e:
        logger.error(f"Failed to create fallback Ollama LLM: {str(e)}")
        # Return registry default as last resort
        return llama_index_registry.get_llm()


def get_gemini_embedding(model_name: Optional[str] = None) -> Any:
    """
    Get a Gemini embedding model with fallback to HuggingFace.

    Args:
        model_name: Name of the Gemini embedding model to use

    Returns:
        Embedding model instance (Gemini or HuggingFace fallback)
    """
    from apps.core.utils.llama_index_setup import llama_index_registry

    # Try to get Gemini embedding first
    if GEMINI_API_KEY:
        try:
            # Set the API key for Google AI
            os.environ["GOOGLE_API_KEY"] = GEMINI_API_KEY

            from llama_index.embeddings.gemini import GeminiEmbedding

            model_name = model_name or GEMINI_EMBEDDING_MODEL
            gemini_embedding = GeminiEmbedding(
                model_name=model_name,
                api_key=GEMINI_API_KEY,
            )

            logger.info(f"Using Gemini embedding with model: {model_name}")
            return gemini_embedding

        except ImportError as e:
            logger.warning(f"Gemini embedding not available (install llama-index-embeddings-gemini): {str(e)}")
        except Exception as e:
            logger.warning(f"Failed to create Gemini embedding: {str(e)}")

    # Fallback to HuggingFace
    logger.info("Falling back to HuggingFace embedding")
    try:
        from llama_index.embeddings.huggingface import HuggingFaceEmbedding

        default_model_name = getattr(
            settings, "EMBEDDING_MODEL_NAME", "BAAI/bge-base-en-v1.5"
        )

        hf_embedding = HuggingFaceEmbedding(
            model_name=default_model_name,
            embed_batch_size=32,
        )
        return hf_embedding

    except Exception as e:
        logger.error(f"Failed to create fallback HuggingFace embedding: {str(e)}")
        # Return registry default as last resort
        return llama_index_registry.get_embedding()


def is_gemini_available() -> bool:
    """
    Check if Gemini API is available and properly configured.

    Returns:
        bool: True if Gemini is available, False otherwise
    """
    if not GEMINI_API_KEY:
        return False

    try:
        from llama_index.llms.gemini import Gemini
        from llama_index.embeddings.gemini import GeminiEmbedding
        return True
    except ImportError:
        return False


def get_llm_status() -> Dict[str, Any]:
    """
    Get the status of available LLM providers.

    Returns:
        dict: Status of LLM providers
    """
    status = {
        "gemini": {
            "available": is_gemini_available(),
            "api_key_configured": bool(GEMINI_API_KEY),
            "model": GEMINI_MODEL,
            "embedding_model": GEMINI_EMBEDDING_MODEL,
        },
        "ollama": {
            "available": True,  # Assume Ollama is always available as fallback
            "host": OLLAMA_API_HOST,
            "model": OLLAMA_MODEL_NAME,
        }
    }

    return status
