{"thread_ts": "1737480351.728199", "channel_id": "C065QSSNH8A", "reply_count": 13, "replies": [{"ts": "1737480470.377599", "text": "also, the target bonus calculated % on prorated employees is off. it looks like it might be adding the modifier to the total", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "j67ob", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "also, the target bonus calculated % on prorated employees is off. it looks like it might be adding the modifier to the total"}]}]}]}, {"ts": "1737480749.171669", "text": "Example: Employee <PERSON> - he has a salary of 55000, a target bonus of 5500 (10%), but the system is showing a target bonus percent of 19.23 (even though the $ is the right target bonus). It is also not applying the proration anywhere.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "4xwLy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Example: Employee <PERSON> - he has a salary of 55000, a target bonus of 5500 (10%), but the system is showing a target bonus percent of 19.23 (even though the $ is the right target bonus). It is also not applying the proration anywhere."}]}]}]}, {"ts": "1737510575.728939", "text": "<@U07MH77PUBV> Lets discuss this today.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "tkSs4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " Lets discuss this today."}]}]}]}, {"ts": "1737530568.160819", "text": "• Proration Modifier was being applied but the column \"Prorated Bonus Award\" was missing with all the recent overlapping changes from column config. That's added now\n• The target bonus percent was calculated based on proratedCurrentSalary. Maybe this issue could not be encountered yet because proration was not used perhaps. To have consistency, this percent is also based on currentSalary now.\nSo the effect of proration shows at the end only in the Prorated Bonus Award column", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "BB/EQ", "elements": [{"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Proration Modifier was being applied but the column \"Prorated Bonus Award\" was missing with all the recent overlapping changes from column config. That's added now"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The target bonus percent was calculated based on proratedCurrentSalary. Maybe this issue could not be encountered yet because proration was not used perhaps. To have consistency, this percent is also based on currentSalary now."}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nSo the effect of proration shows at the end only in the Prorated Bonus Award column"}]}]}]}, {"ts": "1737530645.941389", "text": "<@U07MH77PUBV> May be explain this taking the same employee example for clarity", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "/6Li2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " May be explain this taking the same employee example for clarity"}]}]}]}, {"ts": "1737530657.438959", "text": "Sure", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "3dmZ7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure"}]}]}]}, {"ts": "1737531365.424319", "text": "For the employee <PERSON>:\n`Current Salary: 55,000`\n`Target Bonus: 5500; 10%`\n`Modified Target Bonus: 4400 (having modifier 80%)`\n\nBelow values will be calculated based on `Modified Target Bonus`\n`Target Company Performance: 1760; 40%` \n`Target Individual Performance: 2640; 60%`\n\nBelow values will be based on above target values\n`Earned Company Performance: 1760; 100%` \n`Earned Individual Performance: 2164.80; 82%` \n\nSum of earned values:\n`Bonus Award: 3924.81; 71.36%`\n\nProration Modifier 0.52 will be applied now:\n`Prorated Bonus Award: 2040.90` ", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "RP4fJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For the employee <PERSON>:\n"}, {"type": "text", "text": "Current Salary: 55,000", "style": {"code": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Target Bonus: 5500; 10%", "style": {"code": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Modified Target Bonus: 4400 (having modifier 80%)", "style": {"code": true}}, {"type": "text", "text": "\n\nBelow values will be calculated based on "}, {"type": "text", "text": "Modified Target Bonus", "style": {"code": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Target Company Performance: 1760; 40% ", "style": {"code": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Target Individual Performance: 2640; 60%", "style": {"code": true}}, {"type": "text", "text": "\n\nBelow values will be based on above target values\n"}, {"type": "text", "text": "Earned Company Performance: 1760; 100% ", "style": {"code": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Earned Individual Performance: 2164.80; 82% ", "style": {"code": true}}, {"type": "text", "text": "\n\nSum of earned values:\n"}, {"type": "text", "text": "Bonus Award: 3924.81; 71.36%", "style": {"code": true}}, {"type": "text", "text": "\n\nProration Modifier 0.52 will be applied now:\n"}, {"type": "text", "text": "Prorated Bonus Award: 2040.90 ", "style": {"code": true}}]}]}]}, {"ts": "1737531418.638189", "text": "Thank You <@U07MH77PUBV> :raised_hands:", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "HF/xH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank You "}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "raised_hands", "unicode": "1f64c"}]}]}]}, {"ts": "1737561013.869069", "text": "Thank you! I do not see 'Prorated Bonus Award' in the column configurator yet, though.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "fw9bT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you! I do not see 'Prorated Bonus Award' in the column configurator yet, though."}]}]}]}, {"ts": "1737561036.952829", "text": "^ <@U0690EB5JE5> <@U07MH77PUBV>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "xJ5a+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "^ "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07MH77PUBV"}]}]}]}, {"ts": "1737561149.200909", "text": "taking a look.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "9TbCC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "taking a look."}]}]}]}, {"ts": "1737561244.891059", "text": "Looks like deployment issue. Triggered deployment give it 10mnts", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "K+ZPi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Looks like deployment issue. Triggered deployment give it 10mnts"}]}]}]}, {"ts": "1737561920.330619", "text": "<@U07EJ2LP44S> yes this was deployment issue. Column is available now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737480351.728199", "blocks": [{"type": "rich_text", "block_id": "CBV6/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " yes this was deployment issue. Column is available now."}]}]}]}], "created_at": "2025-05-22T22:00:49.352184"}