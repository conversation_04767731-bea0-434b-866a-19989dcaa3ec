"""
Views for the search functionality.
"""

import json
import logging
import traceback

import requests
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.views.decorators.http import require_POST

from .models import Conversation, Message, SearchQuery, SearchResult

logger = logging.getLogger(__name__)


@login_required
def search_view(request):
    """View for the search page."""
    from django.middleware.csrf import get_token

    # Get recent conversations if any
    recent_conversations = Conversation.objects.filter(user=request.user).order_by(
        "-updated_at"
    )[:5]

    # Explicitly get CS<PERSON><PERSON> token and add to context
    csrf_token = get_token(request)

    context = {
        "recent_conversations": recent_conversations,
        "csrf_token": csrf_token,
    }

    return render(request, "search/search_form.html", context)


@login_required
@require_POST
def search_query(request):
    """Handle search query submission."""
    query = request.POST.get("query", "")
    if not query:
        messages.error(request, "Please enter a search query.")
        return redirect("search:search")

    # Get source filters
    source_filters = request.POST.getlist("source_filter")

    # Get advanced search options
    use_hybrid_search = request.POST.get('use_hybrid_search') == 'true'
    use_context_aware = request.POST.get('use_context_aware') == 'true'
    use_query_expansion = request.POST.get('use_query_expansion') == 'true'
    use_multi_step_reasoning = request.POST.get('use_multi_step_reasoning') == 'true'

    # Get the tenant slug from the user's profile
    tenant_slug = (
        request.user.profile.tenant.slug
        if hasattr(request.user, "profile") and request.user.profile.tenant
        else "stride"
    )

    try:
        # Create RAG service using the new consolidated RAGService
        from apps.search.services.rag_service import RAGService
        rag_service = RAGService(
            user=request.user,
            tenant_slug=tenant_slug
        )

        # Create metadata filter based on source filters
        metadata_filter = None
        if source_filters:
            metadata_filter = {"source_type": {"$in": source_filters}}

        # FIXED: Optimized relevance thresholds for production-ready search
        min_relevance_score = 0.15  # LOWERED: Default threshold for better recall

        # Use even lower thresholds for specific query types that need comprehensive results
        query_lower = query.lower()
        if any(word in query_lower for word in ['list', 'show', 'find', 'what', 'who', 'when', 'how']):
            min_relevance_score = 0.10  # Very low threshold for factual/list queries
        elif 'curana' in query_lower and 'summarize' in query_lower:
            min_relevance_score = 0.08  # Ultra-low threshold for summarizing specific conversations
        elif 'customer feedback' in query_lower or 'customer feedbacks' in query_lower:
            min_relevance_score = 0.08  # Ultra-low threshold for customer feedback queries
        elif any(word in query_lower for word in ['curana', 'summarize', 'latest', 'recent', 'discussion']):
            min_relevance_score = 0.12  # Lower threshold for topic-specific or temporal queries

        # Perform search using RAGService interface with user-selected options
        search_result, retrieved_docs = rag_service.search(
            query_text=query,
            top_k=20,
            metadata_filter=metadata_filter,
            min_relevance_score=min_relevance_score,  # Use dynamic threshold
            use_hybrid_search=use_hybrid_search,
            use_context_aware=use_context_aware,
            use_query_expansion=use_query_expansion,
            use_multi_step_reasoning=use_multi_step_reasoning
        )

        # Create or get conversation
        conversation_id = request.POST.get("conversation_id")
        if conversation_id:
            conversation = get_object_or_404(
                Conversation, id=conversation_id, user=request.user
            )
        else:
            # Get the tenant ID from the user's profile
            tenant_id = (
                request.user.profile.tenant_id
                if hasattr(request.user, "profile") and request.user.profile.tenant
                else None
            )

            # If tenant_id is None, try to get the default tenant
            if tenant_id is None:
                from apps.accounts.models import Tenant

                default_tenant = Tenant.objects.filter(slug="stride").first()
                if default_tenant:
                    tenant_id = default_tenant.id

            conversation = Conversation.objects.create(
                user=request.user,
                title=query[:50] + ("..." if len(query) > 50 else ""),
                tenant_id=tenant_id,
                is_active=True,
            )

        # Add user message to conversation
        Message.objects.create(conversation=conversation, content=query, is_user=True)

        # Add assistant message to conversation
        Message.objects.create(
            conversation=conversation,
            content=search_result.generated_answer,
            is_user=False,
            search_result=search_result,
        )

        # Prepare context for template with optimized citation loading
        citations = search_result.citations.select_related(
            'document_chunk__document__source'
        ).order_by("rank")

        # Get recent conversations for the sidebar
        recent_conversations = (
            Conversation.objects.filter(user=request.user)
            .exclude(id=conversation.id)
            .order_by("-updated_at")[:5]
        )

        context = {
            "query": query,
            "response": search_result.generated_answer,
            "search_result": search_result,
            "citations": citations,
            "conversation": conversation,
            "recent_conversations": recent_conversations,
        }

        return render(request, "search/search_results.html", context)

    except ValueError as e:
        # Handle specific errors from the RAG service
        logger.error(f"ValueError in search_query: {str(e)}")
        return render(request, "search/error.html", {"error_message": str(e)})
    except Exception as e:
        # Handle unexpected errors
        error_message = f"An unexpected error occurred: {str(e)}"
        logger.error(f"{error_message}\n{traceback.format_exc()}")
        return render(request, "search/error.html", {"error_message": error_message})


@login_required
def conversation_list(request):
    """View for listing conversations."""
    conversations = Conversation.objects.filter(user=request.user).order_by(
        "-created_at"
    )
    return render(
        request, "search/conversation_list.html", {"conversations": conversations}
    )


@login_required
def conversation_detail(request, conversation_id):
    """View for conversation details."""
    conversation = get_object_or_404(
        Conversation, id=conversation_id, user=request.user
    )
    messages_list = conversation.messages.all().order_by("created_at")

    # Get recent conversations for the sidebar
    recent_conversations = (
        Conversation.objects.filter(user=request.user)
        .exclude(id=conversation_id)
        .order_by("-updated_at")[:5]
    )

    return render(
        request,
        "search/conversation_detail.html",
        {
            "conversation": conversation,
            "messages": messages_list,
            "recent_conversations": recent_conversations,
        },
    )


@login_required
@require_POST
def add_message(request, conversation_id):
    """Add a message to a conversation."""
    conversation = get_object_or_404(
        Conversation, id=conversation_id, user=request.user
    )
    content = request.POST.get("content", "")

    if not content:
        return JsonResponse(
            {"status": "error", "message": "Message content is required"}
        )

    # Create user message
    Message.objects.create(conversation=conversation, content=content, is_user=True)

    # Get source filters
    source_filters = request.POST.getlist("source_filter")

    # Get the tenant slug from the user's profile
    tenant_slug = (
        request.user.profile.tenant.slug
        if hasattr(request.user, "profile") and request.user.profile.tenant
        else "stride"
    )

    try:
        # Create RAG service using the new consolidated RAGService
        from apps.search.services.rag_service import RAGService
        rag_service = RAGService(
            user=request.user,
            tenant_slug=tenant_slug
        )

        # Create metadata filter based on source filters
        metadata_filter = None
        if source_filters:
            metadata_filter = {"source_type": {"$in": source_filters}}

        # FIXED: Optimized relevance thresholds for production-ready search
        min_relevance_score = 0.15  # LOWERED: Default threshold for better recall

        # Use even lower thresholds for specific query types that need comprehensive results
        content_lower = content.lower()
        if any(word in content_lower for word in ['list', 'show', 'find', 'what', 'who', 'when', 'how']):
            min_relevance_score = 0.10  # Very low threshold for factual/list queries
        elif 'curana' in content_lower and 'summarize' in content_lower:
            min_relevance_score = 0.08  # Ultra-low threshold for summarizing specific conversations
        elif 'customer feedback' in content_lower or 'customer feedbacks' in content_lower:
            min_relevance_score = 0.08  # Ultra-low threshold for customer feedback queries
        elif any(word in content_lower for word in ['curana', 'summarize', 'latest', 'recent', 'discussion']):
            min_relevance_score = 0.12  # Lower threshold for topic-specific or temporal queries

        # Enhance query for temporal queries (latest, recent, etc.)
        enhanced_query = content
        top_k = 20  # Default value
        if any(word in content.lower() for word in ['latest', 'recent', 'new', 'current', 'today', 'yesterday']):
            # For temporal queries, lower the relevance threshold and increase results
            min_relevance_score = max(0.1, min_relevance_score - 0.2)
            top_k = 30
            # Add temporal context to the query
            enhanced_query = f"{content} (focus on recent information and latest updates)"

        # Perform search using RAGService interface
        search_result, retrieved_docs = rag_service.search(
            query_text=enhanced_query,
            top_k=top_k,
            metadata_filter=metadata_filter,
            min_relevance_score=min_relevance_score,  # Use dynamic threshold
            use_hybrid_search=True,
            use_context_aware=True,
            use_query_expansion=False,
            use_multi_step_reasoning=False
        )

        # Create assistant message
        assistant_message = Message.objects.create(
            conversation=conversation,
            content=search_result.generated_answer,
            is_user=False,
            search_result=search_result,
        )

        # Optimize citation loading with select_related to avoid N+1 queries
        citations = search_result.citations.select_related(
            'document_chunk__document__source'
        ).order_by("rank")

        return JsonResponse(
            {
                "status": "success",
                "message": assistant_message.content,
                "citations": [
                    {
                        "text": citation.document_chunk.text,
                        "document_title": (
                            citation.document_chunk.document.title
                            if citation.document_chunk.document
                            else "Unknown"
                        ),
                        "document_url": (
                            citation.document_chunk.document.permalink
                            if citation.document_chunk.document
                            else None
                        ),
                        "relevance_score": citation.relevance_score,
                        "rank": citation.rank,
                        "source_type": (
                            citation.document_chunk.document.source.source_type
                            if citation.document_chunk.document and citation.document_chunk.document.source
                            else None
                        ),
                    }
                    for citation in citations
                ],
            }
        )
    except ValueError as e:
        # Handle specific errors from the RAG service
        logger.error(f"ValueError in add_message: {str(e)}")
        return JsonResponse(
            {"status": "error", "message": str(e), "error_type": "llm_error"}
        )
    except Exception as e:
        # Handle unexpected errors
        error_message = f"An unexpected error occurred: {str(e)}"
        logger.error(f"{error_message}\n{traceback.format_exc()}")
        return JsonResponse(
            {"status": "error", "message": error_message, "error_type": "system_error"}
        )
