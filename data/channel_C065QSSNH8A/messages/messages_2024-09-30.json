{"date": "2024-09-30", "channel_id": "C065QSSNH8A", "message_count": 15, "messages": [{"ts": "1727719810.731689", "text": "I think we also need to give ourselves a hand for the SDF call; I thought it went pretty darn well! Especially if she wants to invite others into the environment to see it. <@U07M6QKHUC9> <@U04DKEFP1K8>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727719810.731689", "reply_count": 1, "reactions": [{"name": "grinning", "users": ["U04DKEFP1K8"], "count": 1}, {"name": "100", "users": ["U07M6QKHUC9"], "count": 1}, {"name": "moneybag", "users": ["U07M6QKHUC9"], "count": 1}, {"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "GKLsM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think we also need to give ourselves a hand for the SDF call; I thought it went pretty darn well! Especially if she wants to invite others into the environment to see it. "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1727719089.536929", "text": "<@U07NBMXTL1E> are you done with demo environment for the day?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1727719089.536929", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "OauHr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " are you done with demo environment for the day?"}]}]}]}, {"ts": "1727717230.491289", "text": "<@U07NBMXTL1E> Huge kudos to you for doing an impressive demo for FordDirect. Your attention to details and the overall talk track is really good. Great job ramping up so quickly and becoming self sufficient with the demo.\n<@U07EJ2LP44S> Great work in helping <PERSON> with getting up to speed on the demo.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727717230.491289", "reply_count": 1, "reactions": [{"name": "tada", "users": ["U04DKEFP1K8", "U07EJ2LP44S", "U0690EB5JE5"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "+9XR2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " Huge kudos to you for doing an impressive demo for FordDirect. Your attention to details and the overall talk track is really good. Great job ramping up so quickly and becoming self sufficient with the demo.\n"}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Great work in helping <PERSON> with getting up to speed on the demo."}]}]}]}, {"ts": "1727716585.102029", "text": "<!here> these are the latest integration supported by merge <https://docs.merge.dev/integrations/hris/overview/> and during any of our demo calls, we should confidently confirm our support for these integrations.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1727716585.102029", "reply_count": 2, "attachments": [{"from_url": "https://docs.merge.dev/integrations/hris/overview/", "service_icon": "https://docs.merge.dev/icons/icon-48x48.png?v=01743f138ae8249f0cb7bed86fae3c90", "thumb_url": "https://docs.merge.dev/img/open-graph/merge-docs.jpg", "thumb_width": 2400, "thumb_height": 1256, "id": 1, "original_url": "https://docs.merge.dev/integrations/hris/overview/", "fallback": "<PERSON><PERSON>", "text": "Learn how to add Merge to your product.", "title": "<PERSON><PERSON>", "title_link": "https://docs.merge.dev/integrations/hris/overview/", "service_name": "docs.merge.dev"}], "blocks": [{"type": "rich_text", "block_id": "FNxgX", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " these are the latest integration supported by merge "}, {"type": "link", "url": "https://docs.merge.dev/integrations/hris/overview/"}, {"type": "text", "text": " and during any of our demo calls, we should confidently confirm our support for these integrations."}]}]}]}, {"ts": "1727716269.595459", "text": "<@U04DKEFP1K8> in the SDF test env, not sure why the flag was showing for promotions use case when there was no promotion recommendations. Is that a bug? If so, we should fix it before handing over the test env to them?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727716269.595459", "reply_count": 1, "edited": {"user": "U07M6QKHUC9", "ts": "1727716287.000000"}, "blocks": [{"type": "rich_text", "block_id": "KzHpG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " in the SDF test env, not sure why the flag was showing for promotions use case when there was no promotion recommendations. Is that a bug? If so, we should fix it before handing over the test env to them?"}]}]}]}, {"ts": "1727712404.228039", "text": "<@U0690EB5JE5> one of our prospects is using Success Factors. Do you anticipate any issues in pulling the data from them? I think Merge does support SuccessFactors", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727712404.228039", "reply_count": 5, "edited": {"user": "U07M6QKHUC9", "ts": "1727712412.000000"}, "blocks": [{"type": "rich_text", "block_id": "W0bOu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " one of our prospects is using Success Factors. Do you anticipate any issues in pulling the data from them? I think <PERSON><PERSON> does support SuccessFactors"}]}]}]}, {"ts": "1727706081.241129", "text": "Ok cool", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZAFgG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok cool"}]}]}]}, {"ts": "**********.654799", "text": "<PERSON> helped me set it up weeks ago", "user": "U07NBMXTL1E", "type": "message", "blocks": [{"type": "rich_text", "block_id": "UJcHQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> helped me set it up weeks ago"}]}]}]}, {"ts": "**********.158669", "text": "I have access to the test account for now, so should be good for the demo in 40 minutes", "user": "U07NBMXTL1E", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OGwrf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have access to the test account for now, so should be good for the demo in 40 minutes"}]}]}]}, {"ts": "**********.292709", "text": "I will see what I can do", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "l0N2S", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will see what I can do"}]}]}]}, {"ts": "**********.654939", "text": "<@U07M6QKHUC9> I am outside ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "J/m73", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I am outside "}]}]}]}, {"ts": "**********.641289", "text": "can you pls add <PERSON> to <http://test.stridehr.io|test.stridehr.io>. account", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gTsuI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "can you pls add <PERSON> to "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": ". account"}]}]}]}, {"ts": "**********.103799", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> it looks like demo account is still not fixed. we have a demo in 45 mins", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NExDE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " it looks like demo account is still not fixed. we have a demo in 45 mins"}]}]}]}, {"ts": "**********.166099", "text": "<@U04DKEFP1K8> will sync up with you on the issues once I am back in case I miss meeting. ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7tUtR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " will sync up with you on the issues once I am back in case I miss meeting. "}]}]}]}, {"ts": "**********.224439", "text": "I have an event to attend. I might join half an hour late or miss the meeting ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.224439", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "phqcf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have an event to attend"}, {"type": "text", "text": "."}, {"type": "text", "text": " I might join half an hour late or miss the meeting "}]}]}]}], "created_at": "2025-05-22T21:35:34.654338"}