{"thread_ts": "1736365016.067139", "channel_id": "C065QSSNH8A", "reply_count": 21, "replies": [{"ts": "1736397159.658939", "text": "<@U06HN8XDC5A> Can you please look into this? <@U07MH77PUBV> <PERSON><PERSON> can help here.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "4uiWB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " Can you please look into this? "}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " <PERSON><PERSON> can help here."}]}]}]}, {"ts": "1736397204.310159", "text": "I guess those column are missing in PRD hence in new UI.  <@U06HN8XDC5A> Please do take care", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "Ktdj7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I guess those column are missing in PRD hence in new UI.  "}, {"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " Please do take care"}]}]}]}, {"ts": "1736435831.652659", "text": "<@U07EJ2LP44S> this should be fixed in <http://qa.stridehr.io|qa.stridehr.io>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "1Qm0A", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " this should be fixed in "}, {"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}]}]}]}, {"ts": "1736441421.135389", "text": "I do not see the columns yet; I just freshly logged in and it still only shows 2 bonus items.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "/ypay", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I do not see the columns yet; I just freshly logged in and it still only shows 2 bonus items."}]}]}]}, {"ts": "1736483412.410289", "text": "<@U07EJ2LP44S> we need to add bonus and update cycle configuration to see those columns. Org view doesn't show those columns which is per our decision in one of the discussions.\n<@U07MH77PUBV> Please confirm confirm this in your QA.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "reactions": [{"name": "eyes", "users": ["U07MH77PUBV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "joqFE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " we need to add bonus and update cycle configuration to see those columns. Org view doesn't show those columns which is per our decision in one of the discussions.\n"}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " Please confirm confirm this in your QA."}]}]}]}, {"ts": "1736523374.755759", "text": "Bonus is already in the column configurator list though; just not the new fields.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "LFBrv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bonus is already in the column configurator list though; just not the new fields."}]}]}]}, {"ts": "1736523458.400249", "text": "I didn’t update the cycle config", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "7IRHC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I "}, {"type": "text", "text": "didn’t"}, {"type": "text", "text": " update the cycle config"}]}]}]}, {"ts": "1736523508.301899", "text": "The fields in comp planner appear based on toggle ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "cv7Zd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The fields in comp planner appear based on toggle "}]}]}]}, {"ts": "1736523630.410349", "text": "Ok - I tried but I can't get past budget and guidelines in cycle edit.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736365016.067139", "files": [{"id": "F087QN362G7", "created": 1736523623, "timestamp": 1736523623, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 44198, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F087QN362G7/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F087QN362G7/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F087QN362G7-85137568df/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F087QN362G7-85137568df/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F087QN362G7-85137568df/image_360.png", "thumb_360_w": 360, "thumb_360_h": 205, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F087QN362G7-85137568df/image_480.png", "thumb_480_w": 480, "thumb_480_h": 274, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F087QN362G7-85137568df/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F087QN362G7-85137568df/image_720.png", "thumb_720_w": 720, "thumb_720_h": 411, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F087QN362G7-85137568df/image_800.png", "thumb_800_w": 800, "thumb_800_h": 456, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F087QN362G7-85137568df/image_960.png", "thumb_960_w": 960, "thumb_960_h": 547, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F087QN362G7-85137568df/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 584, "original_w": 1522, "original_h": 868, "thumb_tiny": "AwAbADDTooooAKKaXA6jFAcHoM0AOooooAKKKKAGPEkhBYc0RxJGSVHWn0U7sAooopAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F087QN362G7/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F087QN362G7-45237902de", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "aj<PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok - I tried but I can't get past budget and guidelines in cycle edit."}]}]}]}, {"ts": "1736524202.193539", "text": "We are still testing and fixing. Monday will make sure everything is in good shape", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "edited": {"user": "U0690EB5JE5", "ts": "1736524214.000000"}, "blocks": [{"type": "rich_text", "block_id": "WAcF5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We are "}, {"type": "text", "text": "still "}, {"type": "text", "text": "testing and fixing. Monday will make sure everything is in good shape"}]}]}]}, {"ts": "1736524231.108679", "text": "<@U07MH77PUBV> please take a look", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "yfNt/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " please take a look"}]}]}]}, {"ts": "1736524286.602639", "text": "Ok thanks. Does that include HRBP changes too? I didn't see the ability to test that in there yet.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "C62ES", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok thanks. Does that include HRBP changes too? I didn't see the ability to test that in there yet."}]}]}]}, {"ts": "1736524367.833129", "text": "Yes, will make sure everything is setup correctly on Monday ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "hnylK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, will make sure everything is setup correctly on Monday "}]}]}]}, {"ts": "1736525188.143499", "text": "<@U07EJ2LP44S> I will be updating all the ENVs on Monday with latest build with configurator feature. You can directly test in respective ENVs", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "edited": {"user": "U0690EB5JE5", "ts": "1736525197.000000"}, "blocks": [{"type": "rich_text", "block_id": "TkkNr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I will "}, {"type": "text", "text": "be "}, {"type": "text", "text": "updating all the ENVs on Monday with latest build with configurator feature. You can directly test in respective ENVs"}]}]}]}, {"ts": "1736525414.701459", "text": "<@U07EJ2LP44S> Which step do you see this error?", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "Doa4/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Which step do you see this error?"}]}]}]}, {"ts": "1736525545.929759", "text": "<@U07MH77PUBV> go ahead and add yourself to <http://qa.strider.io|qa.strider.io>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "/qVIf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " go ahead and add yourself to "}, {"type": "link", "url": "http://qa.strider.io", "text": "qa.strider.io"}]}]}]}, {"ts": "1736525573.834169", "text": "Swagger end point is <http://qa-api.stridehr.io|qa-api.stridehr.io>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "fphVd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Swagger end point is "}, {"type": "link", "url": "http://qa-api.stridehr.io", "text": "qa-api.stridehr.io"}]}]}]}, {"ts": "1736526861.963889", "text": "This is known issue and was fixed and merged our evening today.\nNot sure if there's been a deployment in past 2-3 hours.", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "9zSCU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is known issue and was fixed and merged our evening today.\nNot sure if there's been a deployment in past 2-3 hours."}]}]}]}, {"ts": "1736527074.674019", "text": "Oh ok. I think I didn’t deploy", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "HKkWI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Oh ok. I think I "}, {"type": "text", "text": "didn’t"}, {"type": "text", "text": " deploy"}]}]}]}, {"ts": "1736527212.515319", "text": "There was another issue further in the cycle but I didn't observe that by changing the db.\nMaybe data issue, maybe real. This will be fixed on Monday", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1736365016.067139", "reactions": [{"name": "thankyouty", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "fP2wK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There was another issue further in the cycle but I didn't observe that by changing the db.\nMaybe data issue, maybe real. This will be fixed on Monday"}]}]}]}, {"ts": "1736784169.914529", "text": "<@U07EJ2LP44S> Please take a look and let me know. We are still doing last mile sanity and minor fixes. But you should be able to test now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736365016.067139", "blocks": [{"type": "rich_text", "block_id": "wTzxY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please take a look and let me know. We are still doing last mile sanity and minor fixes. But you should be able to test now."}]}]}]}], "created_at": "2025-05-22T22:00:49.335522"}