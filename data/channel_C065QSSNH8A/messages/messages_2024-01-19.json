{"date": "2024-01-19", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1705609542.184059", "text": "<@U04DKEFP1K8> SDF rescheduled for tomorrow. Please make sure we get all those XLM updates in by then!", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705609542.184059", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "qeRng", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " SDF rescheduled for tomorrow. Please make sure we get all those XLM updates in by then!"}]}]}]}, {"ts": "1705608548.925809", "text": "T minus 2 min :joy:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GmaDc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "T minus 2 min "}, {"type": "emoji", "name": "joy", "unicode": "1f602"}]}]}]}, {"ts": "1705608339.171119", "text": "Same! :sweat_smile:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "dqyHW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Same! "}, {"type": "emoji", "name": "sweat_smile", "unicode": "1f605"}]}]}]}, {"ts": "1705608331.064269", "text": "Oh kk no worries wanted to make sure I had the right link due to the last minute change!", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DEyKb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Oh kk no worries wanted to make sure I had the right link due to the last minute change!"}]}]}]}, {"ts": "1705608308.049779", "text": "<PERSON> and <PERSON><PERSON><PERSON> are still in another call, we'll start when <PERSON> is free to kick off his Zoom :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "73ypD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> and <PERSON><PERSON><PERSON> are still in another call, we'll start when <PERSON> is free to kick off his Zoom "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}], "created_at": "2025-05-22T21:35:34.590807"}