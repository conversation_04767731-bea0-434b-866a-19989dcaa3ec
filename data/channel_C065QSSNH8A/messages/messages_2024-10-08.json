{"date": "2024-10-08", "channel_id": "C065QSSNH8A", "message_count": 11, "messages": [{"ts": "1728410646.547159", "text": "moving to the channel <@U0690EB5JE5>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1728409960.945199", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "PKI9I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "moving to the channel "}, {"type": "user", "user_id": "U0690EB5JE5"}]}]}]}, {"ts": "1728410411.305919", "text": "<@U07EJ2LP44S> can we pls create a Jira ticker for this issue?\nIn the merit planning view, the selection of new columns to display them in the merit table do not persist after refresh.\n\nWe had a similar issue with the org view but it was fixed", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1728410411.305919", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "h2kz9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can we pls create a Jira ticker for this issue?\nIn the merit planning view, the selection of new columns to display them in the merit table do not persist after refresh.\n\nWe had a similar issue with the org view but it was fixed"}]}]}]}, {"ts": "1728409960.945199", "text": "<@U07EJ2LP44S> can we pls add some variable pay employees (just two are enough) to the demo data? Currently variable pay is now showing in the demo env", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1728409960.945199", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "qrtna", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can we pls add some variable pay employees (just two are enough) to the demo data? Currently variable pay is now showing in the demo env"}]}]}]}, {"ts": "1728408437.716599", "text": "Have to go pick sick kid up from school; be offline for a bit.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "uxCk/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Have to go pick sick kid up from school; be offline for a bit."}]}]}]}, {"ts": "1728405879.116509", "text": "<!here>\nSales Update: <PERSON> and I just had a call with FullSpeedAutomotive. 2200 emp. We are competing with Payscale. So far, the conversation is positive and next step is to show them a detailed demo.  It could be a large deal for us $50K:moneybag:  <@U0690EB5JE5> <@U04DKEFP1K8> we will have to win their confidence that we can support a large org like them.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1728405879.116509", "reply_count": 4, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "IafUO", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": "\nSales Update: <PERSON> and I just had a call with FullSpeedAutomotive. 2200 emp. We are competing with Payscale. So far, the conversation is positive and next step is to show them a detailed demo.  It could be a large deal for us $50K"}, {"type": "emoji", "name": "moneybag", "unicode": "1f4b0"}, {"type": "text", "text": "  "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " we will have to win their confidence that we can support a large org like them."}]}]}]}, {"ts": "1728403548.691749", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> Issues fixed today until PROD\n<https://compiify.atlassian.net/browse/COM-3718>\n<https://compiify.atlassian.net/browse/COM-3672>\n<https://compiify.atlassian.net/browse/COM-3712>\n<https://compiify.atlassian.net/browse/COM-3713>\n<https://compiify.atlassian.net/browse/COM-3719>", "user": "U0690EB5JE5", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13779::c31b7761620e411db9764217417d04b9", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3718?atlOrigin=eyJpIjoiMzA2Mzk3OTliYTM1NDRiZWJjNzNiNWYwNTU4NGU1YTgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3718 Bug: Unable to Save Employee Details in Cain Waters Production Acco…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13779::260c4e58f5de4528a9f94dd68a7c23d9", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13779\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3718\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3718", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:13733::693e966654bb41c89971b9860e4a8161", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3672?atlOrigin=eyJpIjoiYmVjNmQ0YWQxN2NjNGRlYzg1YmU3ZjRiZGFhMzNhN2QiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3672 column headers do not align with the cell content>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13733::a3a380fe048c4798843a0b953fec00aa", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/082e10750208b91e38660f50da682c58?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13733\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3672\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3672", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 3, "blocks": [{"type": "section", "block_id": "uf:ih:13773::c6f4c0bb9b724e24bfabb58759713bda", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3712?atlOrigin=eyJpIjoiZTIwMDgyYjU4MWY4NGI4NGFjYjA5ZDlhYzNlZjQ0NGUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3712 Issue: Displaying additional zeros when entering decimal digits in …>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13773::cfe5f7a2d75b4cef82b6304357bc7799", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/082e10750208b91e38660f50da682c58?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13773\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3712\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3712", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 4, "blocks": [{"type": "section", "block_id": "uf:ih:13774::41f5d8b69fc64a7cac8fd4042a88e053", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3713?atlOrigin=eyJpIjoiNTkxYTQ5MWExM2FmNDlhZmI2NjljYjc1MDU5OTg1YzMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3713 Add prompt message for changing exchange rate in localization tab>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13774::6cdbb1c166034a0fa8941dba32612351", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/082e10750208b91e38660f50da682c58?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13774\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3713\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3713", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 5, "blocks": [{"type": "section", "block_id": "uf:ih:13780::102169b7603b490f87225a31725839ed", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3719?atlOrigin=eyJpIjoiZjE4Yjk0YjczNDRmNDBlMWE5MDA1ZmJiZDM0NmIzZWEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3719 Issue: Allow selection of today's date in CycleBuilder for all date…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13780::b7e60265e6904fa1b41512c3b089894c", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/082e10750208b91e38660f50da682c58?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13780\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3719\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3719", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Gmv7P", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Issues fixed today until PROD\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3718"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3672"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3712"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3713"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3719"}]}]}]}, {"ts": "1728401652.975099", "text": "<@U07EJ2LP44S> Regarding Sonendo, if there are any specific requirements that requires engineering work, we should know them sooner so engineering has enough time to build it. If we are aiming to configure their cycle in June/July, ideally we should know in Q1 if they have any complex requirements from a product perspective IMO.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1728401652.975099", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "CPqJn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Regarding Sonendo, if there are any specific requirements that requires engineering work, we should know them sooner so engineering has enough time to build it. If we are aiming to configure their cycle in June/July, ideally we should know in Q1 if they have any complex requirements from a product perspective IMO."}]}]}]}, {"ts": "1728400893.501319", "text": "<@U07EJ2LP44S> will do it half an hour ", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "HcTrs", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " will do it half an hour "}]}]}]}, {"ts": "1728400753.801009", "text": "Meeting not needed for me; <@U0690EB5JE5> if we could get the curana users back in asap that would be good, we have a call with them in 2 hours.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lj2Ds", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Meeting not needed for me; "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " if we could get the curana users back in asap that would be good, we have a call with them in 2 hours."}]}]}]}, {"ts": "1728400686.658089", "text": "<!here> I have a sales meeting at 9. Does anyone have any agenda items? or we can give everyone some time back.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1728400686.658089", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "sk17z", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I have a sales meeting at 9. Does anyone have any agenda items? or we can give everyone some time back."}]}]}]}, {"ts": "1728384024.882049", "text": "<@U04DKEFP1K8> Known issues are fixed in alayacare and cwa ENVs", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "KfBIF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Known issues are fixed in alayacare and cwa ENVs"}]}]}]}], "created_at": "2025-05-22T21:35:34.649739"}