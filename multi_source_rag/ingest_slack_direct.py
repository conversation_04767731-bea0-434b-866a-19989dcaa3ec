#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to directly ingest Slack data from local files.
"""

import os
import sys
import django
import json
import logging
import hashlib
from datetime import datetime
from collections import defaultdict

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from django.db import transaction
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk, EmbeddingMetadata, DocumentContent
from apps.core.utils.vectorstore import get_qdrant_client

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_database():
    """Clean the database by removing all documents, chunks, and related data."""
    logger.info("Starting database cleaning process...")

    # Delete all embedding metadata
    embedding_count = EmbeddingMetadata.objects.count()
    EmbeddingMetadata.objects.all().delete()
    logger.info(f"Deleted {embedding_count} embedding metadata records")

    # Delete all document chunks
    chunk_count = DocumentChunk.objects.count()
    DocumentChunk.objects.all().delete()
    logger.info(f"Deleted {chunk_count} document chunks")

    # Delete all document content
    content_count = DocumentContent.objects.count()
    DocumentContent.objects.all().delete()
    logger.info(f"Deleted {content_count} document content records")

    # Delete all raw documents
    doc_count = RawDocument.objects.count()
    RawDocument.objects.all().delete()
    logger.info(f"Deleted {doc_count} raw documents")

    # Delete all document sources
    source_count = DocumentSource.objects.count()
    DocumentSource.objects.all().delete()
    logger.info(f"Deleted {source_count} document sources")

    # Clean Qdrant vector database
    try:
        # Get Qdrant client
        client = get_qdrant_client()

        # Get all tenants
        tenants = Tenant.objects.all()

        # Delete collections for each tenant
        for tenant in tenants:
            collection_name = f"{tenant.slug}_chunks"
            try:
                if client.collection_exists(collection_name):
                    client.delete_collection(collection_name)
                    logger.info(f"Deleted vector collection: {collection_name}")
            except Exception as e:
                logger.error(f"Error deleting vector collection {collection_name}: {str(e)}")
    except Exception as e:
        logger.error(f"Error cleaning vector database: {str(e)}")

    logger.info("Database cleaning completed successfully")

def create_document_source(tenant):
    """Create a document source for Slack data."""
    try:
        # Define the configuration for the Slack source
        config = {
            "data_dir": "../data/",
            "time_period": "monthly",
            "enable_semantic_cross_refs": True,
        }

        # Create the document source
        source = DocumentSource.objects.create(
            tenant=tenant,
            name="Local Slack Data",
            source_type="slack",
            config=config,
            is_active=True
        )

        logger.info(f"Created document source: {source.name} ({source.source_type})")
        return source

    except Exception as e:
        logger.error(f"Error creating document source: {str(e)}")
        return None

def ingest_slack_data_direct(tenant, source):
    """Ingest Slack data directly from local files."""
    try:
        # Path to the data directory
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data")
        
        # Path to the channel directory
        channel_dir = os.path.join(data_dir, "channel_C065QSSNH8A")
        
        # Path to the messages directory
        messages_dir = os.path.join(channel_dir, "messages")
        
        # Path to the threads directory
        threads_dir = os.path.join(channel_dir, "threads")
        
        if not os.path.exists(messages_dir):
            logger.error(f"Messages directory not found: {messages_dir}")
            return 0, 0
            
        # Get list of message files
        message_files = [f for f in os.listdir(messages_dir) if f.endswith(".json")]
        logger.info(f"Found {len(message_files)} message files")
        
        # Process message files by month
        processed = 0
        failed = 0
        
        # Group files by month
        monthly_files = defaultdict(list)
        for filename in message_files:
            if filename.startswith("messages_"):
                # Extract date from filename (format: messages_YYYY-MM-DD.json)
                date_str = filename.replace("messages_", "").replace(".json", "")
                if len(date_str) >= 7:  # At least YYYY-MM
                    month_key = date_str[:7]  # YYYY-MM
                    monthly_files[month_key].append(filename)
        
        logger.info(f"Grouped messages into {len(monthly_files)} months")
        
        # Process each month
        for month, files in monthly_files.items():
            try:
                # Create a document for this month
                doc_title = f"Slack Messages - {month}"
                
                # Generate content hash
                content_hash = hashlib.md5(month.encode()).hexdigest()
                
                # Create raw document
                doc = RawDocument.objects.create(
                    tenant=tenant,
                    title=doc_title,
                    source=source,
                    content_hash=content_hash,
                    content_type="slack_message",
                    external_id=f"slack-{month}",
                    metadata={
                        "channel": "C065QSSNH8A",
                        "month": month,
                        "file_count": len(files)
                    }
                )
                
                # Collect all messages for this month
                all_messages = []
                for filename in files:
                    try:
                        with open(os.path.join(messages_dir, filename), 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            
                            # Handle different JSON formats
                            if isinstance(data, list):
                                messages = data
                            else:
                                messages = data.get("messages", [])
                                
                            all_messages.extend(messages)
                    except Exception as e:
                        logger.error(f"Error processing file {filename}: {str(e)}")
                        failed += 1
                
                # Sort messages by timestamp
                all_messages.sort(key=lambda x: x.get("ts", "0"))
                
                # Create content from messages
                content_parts = []
                for msg in all_messages:
                    if msg.get("type") == "message" and msg.get("text"):
                        user = msg.get("user", "unknown")
                        ts = msg.get("ts", "")
                        text = msg.get("text", "")
                        
                        # Convert timestamp to datetime
                        try:
                            dt = datetime.fromtimestamp(float(ts))
                            time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                        except:
                            time_str = ts
                            
                        content_parts.append(f"[{time_str}] {user}: {text}")
                
                # Join content parts
                content = "\n\n".join(content_parts)
                
                # Create document content
                DocumentContent.objects.create(
                    document=doc,
                    content=content,
                    content_format="slack"
                )
                
                # Create chunks (one per message)
                for i, msg in enumerate(all_messages):
                    if msg.get("type") == "message" and msg.get("text"):
                        user = msg.get("user", "unknown")
                        ts = msg.get("ts", "")
                        text = msg.get("text", "")
                        
                        # Convert timestamp to datetime
                        try:
                            dt = datetime.fromtimestamp(float(ts))
                            time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                        except:
                            time_str = ts
                            
                        chunk_text = f"[{time_str}] {user}: {text}"
                        
                        # Create chunk
                        DocumentChunk.objects.create(
                            tenant=tenant,
                            document=doc,
                            text=chunk_text,
                            chunk_type="slack_message",
                            thread_id=msg.get("thread_ts", ts),
                            chunk_index=i,
                            metadata={
                                "user": user,
                                "timestamp": ts,
                                "time_str": time_str,
                                "has_thread": "thread_ts" in msg,
                                "channel": "C065QSSNH8A",
                                "month": month
                            }
                        )
                
                processed += 1
                logger.info(f"Processed document: {doc_title} with {len(all_messages)} messages")
                
            except Exception as e:
                logger.error(f"Error processing month {month}: {str(e)}")
                failed += 1
        
        return processed, failed
        
    except Exception as e:
        logger.error(f"Error ingesting data: {str(e)}")
        return 0, 0

def print_database_stats():
    """Print database statistics."""
    logger.info("Database Statistics:")
    logger.info(f"Raw Documents: {RawDocument.objects.count()}")
    logger.info(f"Document Chunks: {DocumentChunk.objects.count()}")
    logger.info(f"Document Sources: {DocumentSource.objects.count()}")
    logger.info(f"Embedding Metadata: {EmbeddingMetadata.objects.count()}")
    logger.info(f"Document Content: {DocumentContent.objects.count()}")

def main():
    """Main function to ingest Slack data directly."""
    logger.info("Starting direct Slack data ingestion...")

    # Clean the database first
    clean_database()

    # Get or create tenant
    tenant, created = Tenant.objects.get_or_create(
        slug="default",
        defaults={"name": "Default Tenant"}
    )

    # Print initial database stats
    logger.info("Initial database stats:")
    print_database_stats()

    # Create document source
    source = create_document_source(tenant)
    if not source:
        logger.error("Failed to create document source. Exiting.")
        return

    # Ingest Slack data directly
    logger.info("Ingesting Slack data directly...")
    processed, failed = ingest_slack_data_direct(tenant, source)

    # Print final database stats
    logger.info("Final database stats after ingestion:")
    print_database_stats()

    logger.info(f"Slack data ingestion completed: {processed} processed, {failed} failed")

if __name__ == "__main__":
    main()
