{"date": "2024-02-07", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1707328262.664119", "text": "<@U04DS2MBWP4> Any questions for <PERSON> today? If not, I'll probably spend time on comp cycle builder and/or adjustment letters.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1707328262.664119", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "GGRpm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Any questions for <PERSON> today? If not, I'll probably spend time on comp cycle builder and/or adjustment letters."}]}]}]}, {"ts": "1707272300.383209", "text": "<@U04DKEFP1K8> Eng priorities for next day are updated on the <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities board>\n• Highest priority continues to be *Password Reset flow*, since we're about to open up to ~10 employees at SDF with local login\n• I had to reopen at least one ticket - comments provided for what's missing\n• Pulled in a couple new things that will be annoying for SDF users and/or distracting in demos\n• If all tickets on this board are done, or someone is done and all others are assigned, they can continue working on <https://compiify.atlassian.net/browse/COM-2087|Wave 3> tickets.\n", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Zs9R+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are updated on the "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities board"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Highest priority continues to be"}, {"type": "text", "text": " Password Reset flow", "style": {"bold": true}}, {"type": "text", "text": ", since we're about to open up to ~10 employees at SDF with local login"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "I had to reopen at least one ticket - comments provided for what's missing"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Pulled in a couple new things that will be annoying for SDF users and/or distracting in demos"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "If all tickets on this board are done, or someone is done and all others are assigned, they can continue working on "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " tickets."}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}], "created_at": "2025-05-22T21:35:34.584278"}