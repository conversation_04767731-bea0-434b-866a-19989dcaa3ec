{"date": "2024-11-20", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1732124448.983509", "text": "FYI, <PERSON><PERSON> at CainWatters says all is well with the cycle!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732124448.983509", "reply_count": 1, "reactions": [{"name": "tada", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}, {"name": "partying_face", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "4vbH5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "FYI, <PERSON><PERSON> at CainWatters says all is well with the cycle!"}]}]}]}, {"ts": "1732123405.588499", "text": "Here's the bug/edge case for changing titles in org view: <https://compiify.atlassian.net/browse/COM-3997>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732123405.588499", "reply_count": 10, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14080::768f6a2f04294c7aabe906e1f2df3916", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3997?atlOrigin=eyJpIjoiOTc1YmZjZjU2YjI4NGMyNGFlYWYxNzI3Zjk1YzUxOGMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3997 Issue: Incorrect Level Mapping for Titles When Changed in Org View>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14080::51e86f0d32c74a889e044d375fe91ee1", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14080\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3997\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3997", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "u9/Bv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here's the bug/edge case for changing titles in org view: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3997"}]}]}]}, {"ts": "1732121530.423699", "text": "Diversified Energy - if AC is populated they are bonus eligible and need to be in the cycle.", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F0823NJ1MED", "created": 1732121513, "timestamp": 1732121513, "name": "Headcount Report.xlsx", "title": "Headcount Report.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 347009, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F0823NJ1MED/headcount_report.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F0823NJ1MED/download/headcount_report.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F0823NJ1MED-c0b5b26aa9/headcount_report_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F0823NJ1MED-c0b5b26aa9/headcount_report_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0823NJ1MED/headcount_report.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F0823NJ1MED-da2d95e150", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "+Mvm8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified Energy - if AC is populated they are bonus eligible and need to be in the cycle."}]}]}]}, {"ts": "1732119260.121089", "text": "<@U0690EB5JE5> can we pls set up and upload this performance ratings for Tithely?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1732119260.121089", "reply_count": 4, "files": [{"id": "F081A8UDE87", "created": 1732119229, "timestamp": 1732119229, "name": "PerformanceRatingHistory.xlsx", "title": "PerformanceRatingHistory.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 16999, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F081A8UDE87/performanceratinghistory.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F081A8UDE87/download/performanceratinghistory.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F081A8UDE87-4dcd031d7d/performanceratinghistory_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F081A8UDE87-4dcd031d7d/performanceratinghistory_thumb_pdf.png", "thumb_pdf_w": 935, "thumb_pdf_h": 1210, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F081A8UDE87/performanceratinghistory.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F081A8UDE87-141271cc00", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Hxt1c", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we pls set up and upload this performance ratings for Tithely?"}]}]}]}, {"ts": "1732077057.941229", "text": "Let’s disable it", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "AMxn1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let’s disable it"}]}]}]}, {"ts": "1732076376.829599", "text": "<@U07M6QKHUC9> Whats up with Vercara? should we keep their test instance active?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fOUkd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Whats up with <PERSON><PERSON><PERSON><PERSON>? should we keep their test instance active?"}]}]}]}, {"ts": "1732053851.546759", "text": "Fireflies did not catch my Valgenesis call, but the zoom summary for the HRBP is this:\n\n\nHRBP Allocation and Manager <PERSON>\nAmanda and <PERSON><PERSON> discussed the allocation of HRBPs (Human Resource Business Partners) based on employee locations. They agreed that a manager's HRBP would be based on their location, and their team's HRBPs would be different. The HRBPs would only have access to employees in their region, not across different regions. They also clarified that a manager would only see data for their assigned employees, and their rights would be similar to a manager's. The HRBPs could make recommendations on behalf of the manager for their assigned employees.\n\nSo my question here, is if we assigned the HRBP to the employee, say all employees in India go to HRBP 1, but their managers are in US/UK or wherever (which they said will happen), what view will the HRBP have and will they be able to make changes to the employees recommendations, even though they may not be mapped to the manager for that employee.\n\nWe have a call with them on Thursday immediately after the leadership call where we can get final clarifications as needed.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732053851.546759", "reply_count": 2, "edited": {"user": "U07EJ2LP44S", "ts": "1732054060.000000"}, "blocks": [{"type": "rich_text", "block_id": "BrKu5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Fireflies did not catch my Valgenesis call, but the zoom summary for the HRBP is this:\n\n\nHRBP Allocation and Manager <PERSON>\nAmanda and <PERSON><PERSON> discussed the allocation of HRBPs (Human Resource Business Partners) based on employee locations. They agreed that a manager's HRBP would be based on their location, and their team's HRBPs would be different. The HRBPs would only have access to employees in their region, not across different regions. They also clarified that a manager would only see data for their assigned employees, and their rights would be similar to a manager's. The HRBPs could make recommendations on behalf of the manager for their assigned employees.\n\nSo my question here, is if we assigned the HRBP to the employee, say all employees in India go to HRBP 1, but their managers are in US/UK or wherever (which they said will happen), what view will the HRBP have and will they be able to make changes to the employees recommendations, even though they may not be mapped to the manager for that employee.\n\nWe have a call with them on Thursday immediately after the leadership call where we can get final clarifications as needed."}]}]}]}, {"ts": "**********.785139", "text": "<@U0690EB5JE5> <https://compiify.atlassian.net/browse/COM-3996>\nadjustment letters is breaking the demo account", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.785139", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14079::c0a89a51458d4e768fab256b392fd78b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3996?atlOrigin=eyJpIjoiNjk2OWU3YWJmZjRjNDlkMGJkOTY5YzE4OWI5NzhmMzQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3996 Performance Issue with Adjustment Letter Creation>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14079::71021f37532040c086726a5b833d5325", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14079\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3996\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3996", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "g/HX2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3996"}, {"type": "text", "text": "\nadjustment letters is breaking the demo account"}]}]}]}, {"ts": "**********.232279", "text": "<@U0690EB5JE5> I have captured what Alayacare wants to see in payband/compa ratios as it relates to PT employees here: <https://compiify.atlassian.net/browse/COM-3995> / <https://loom.com/share/0766441da86c4ccdb802ff7406b9b3aa?src=composer>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.232279", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "7wHOB", "video_url": "https://www.loom.com/embed/0766441da86c4ccdb802ff7406b9b3aa?src=composer&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/0766441da86c4ccdb802ff7406b9b3aa-29497152d0362e4e-4x3.jpg", "alt_text": "Understanding Alliant Care's Pay Band Adjustment", "title": {"type": "plain_text", "text": "Understanding Alliant Care's Pay Band Adjustment", "emoji": true}, "title_url": "https://www.loom.com/share/0766441da86c4ccdb802ff7406b9b3aa", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 2 min  ", "emoji": true}}, {"type": "section", "block_id": "8k948", "text": {"type": "mrkdwn", "text": ":information_source: In this video, I explain the discrepancy in <PERSON><PERSON>'s pay calculation at Alliant Care. By adjusting her weekly hours to match the Australian...", "verbatim": false}}, {"type": "actions", "block_id": "A+8Sp", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/0766441da86c4ccdb802ff7406b9b3aa?src=composer"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"0766441da86c4ccdb802ff7406b9b3aa\",\"videoName\":\"Understanding Alliant Care's Pay Band Adjustment\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://loom.com/share/0766441da86c4ccdb802ff7406b9b3aa?src=composer", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "Vm476", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I have captured what Alayacare wants to see in payband/compa ratios as it relates to PT employees here: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3995"}, {"type": "text", "text": " / "}, {"type": "link", "url": "https://loom.com/share/0766441da86c4ccdb802ff7406b9b3aa?src=composer"}]}]}]}], "created_at": "2025-05-22T21:35:34.687216"}