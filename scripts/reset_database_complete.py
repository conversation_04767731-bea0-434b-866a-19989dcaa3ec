#!/usr/bin/env python
"""
Complete Database Reset and Setup Script

This script performs a complete database reset:
1. Drops and recreates the PostgreSQL database
2. Deletes all migration files
3. Creates fresh migrations
4. Applies migrations
5. Creates superuser and tenant
6. Ingests fresh data

Usage:
    python scripts/reset_database_complete.py
"""

import os
import sys
import subprocess
import shutil
import glob
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DatabaseResetter:
    """Complete database reset and setup."""
    
    def __init__(self):
        """Initialize the database resetter."""
        self.project_root = Path(__file__).parent.parent
        self.django_root = self.project_root / "multi_source_rag"
        
        # Database configuration (adjust as needed)
        self.db_config = {
            "name": "ragsearch_db",
            "user": "postgres",
            "password": "postgres",
            "host": "localhost",
            "port": "5432"
        }
        
        logger.info(f"Project root: {self.project_root}")
        logger.info(f"Django root: {self.django_root}")

    def run_command(self, command, cwd=None, check=True):
        """Run a shell command and return the result."""
        if cwd is None:
            cwd = self.django_root
            
        logger.info(f"Running: {command}")
        logger.info(f"Working directory: {cwd}")
        
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                check=check
            )
            
            if result.stdout:
                logger.info(f"Output: {result.stdout}")
            if result.stderr:
                logger.warning(f"Stderr: {result.stderr}")
                
            return result
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Command failed: {e}")
            logger.error(f"Stdout: {e.stdout}")
            logger.error(f"Stderr: {e.stderr}")
            if check:
                raise
            return e

    def drop_and_create_database(self):
        """Drop and recreate the PostgreSQL database."""
        logger.info("🗑️  Dropping and recreating PostgreSQL database...")
        
        db_name = self.db_config["name"]
        db_user = self.db_config["user"]
        
        # Drop database (ignore errors if it doesn't exist)
        drop_cmd = f'psql -U {db_user} -h localhost -c "DROP DATABASE IF EXISTS {db_name};"'
        self.run_command(drop_cmd, check=False)
        
        # Create database
        create_cmd = f'psql -U {db_user} -h localhost -c "CREATE DATABASE {db_name};"'
        self.run_command(create_cmd)
        
        logger.info("✅ Database recreated successfully")

    def delete_migration_files(self):
        """Delete all migration files except __init__.py."""
        logger.info("🧹 Deleting migration files...")
        
        apps_dir = self.django_root / "apps"
        migration_count = 0
        
        for app_dir in apps_dir.iterdir():
            if app_dir.is_dir():
                migrations_dir = app_dir / "migrations"
                if migrations_dir.exists():
                    # Delete all .py files except __init__.py
                    for migration_file in migrations_dir.glob("*.py"):
                        if migration_file.name != "__init__.py":
                            migration_file.unlink()
                            migration_count += 1
                            logger.info(f"   Deleted: {migration_file}")
                    
                    # Delete __pycache__ directories
                    pycache_dir = migrations_dir / "__pycache__"
                    if pycache_dir.exists():
                        shutil.rmtree(pycache_dir)
                        logger.info(f"   Deleted: {pycache_dir}")
        
        logger.info(f"✅ Deleted {migration_count} migration files")

    def create_fresh_migrations(self):
        """Create fresh migrations for all apps."""
        logger.info("📝 Creating fresh migrations...")
        
        # Make migrations for all apps
        result = self.run_command("python manage.py makemigrations")
        
        if result.returncode == 0:
            logger.info("✅ Fresh migrations created successfully")
        else:
            logger.error("❌ Failed to create migrations")
            raise RuntimeError("Migration creation failed")

    def apply_migrations(self):
        """Apply all migrations."""
        logger.info("🔄 Applying migrations...")
        
        result = self.run_command("python manage.py migrate")
        
        if result.returncode == 0:
            logger.info("✅ Migrations applied successfully")
        else:
            logger.error("❌ Failed to apply migrations")
            raise RuntimeError("Migration application failed")

    def create_superuser_and_tenant(self):
        """Create superuser and tenant."""
        logger.info("👤 Creating superuser and tenant...")
        
        # Create superuser script
        superuser_script = '''
from django.contrib.auth.models import User
from apps.accounts.models import Tenant, UserProfile

# Create superuser
if not User.objects.filter(username="mahesh").exists():
    user = User.objects.create_superuser(
        username="mahesh",
        email="<EMAIL>",
        password="admin123"
    )
    print(f"Created superuser: {user.username}")
else:
    user = User.objects.get(username="mahesh")
    print(f"Superuser already exists: {user.username}")

# Create tenant
if not Tenant.objects.filter(slug="stride").exists():
    tenant = Tenant.objects.create(
        name="Stride Technologies",
        slug="stride",
        description="Main tenant for testing"
    )
    print(f"Created tenant: {tenant.name}")
else:
    tenant = Tenant.objects.get(slug="stride")
    print(f"Tenant already exists: {tenant.name}")

# Create user profile
if not hasattr(user, "profile") or user.profile is None:
    profile = UserProfile.objects.create(
        user=user,
        tenant=tenant
    )
    print(f"Created user profile for {user.username}")
else:
    print(f"User profile already exists for {user.username}")

print("✅ Superuser and tenant setup complete")
'''
        
        # Write script to temporary file
        script_file = self.django_root / "temp_setup.py"
        with open(script_file, "w") as f:
            f.write(superuser_script)
        
        try:
            # Run the script
            result = self.run_command(f"python manage.py shell < temp_setup.py")
            
            if result.returncode == 0:
                logger.info("✅ Superuser and tenant created successfully")
            else:
                logger.error("❌ Failed to create superuser and tenant")
                raise RuntimeError("Superuser/tenant creation failed")
                
        finally:
            # Clean up temporary file
            if script_file.exists():
                script_file.unlink()

    def ingest_sample_data(self):
        """Ingest sample Slack data."""
        logger.info("📥 Ingesting sample data...")
        
        # Create ingestion script
        ingestion_script = '''
from apps.documents.services.llama_ingestion_service_unified import UnifiedLlamaIndexIngestionService
import json

try:
    # Initialize ingestion service
    service = UnifiedLlamaIndexIngestionService(tenant_slug="stride")
    
    # Slack configuration
    slack_config = {
        "token": "*********************************************************",
        "channels": [
            {"id": "C065QSSNH8A", "name": "1-productengineering"},
            {"id": "C07M2CAS79S", "name": "engineering-issues"}
        ],
        "max_messages": 50,  # Limit for fresh setup
        "days_back": 14
    }
    
    print(f"Starting ingestion from {len(slack_config['channels'])} Slack channels...")
    
    # Perform ingestion
    result = service.ingest_slack_data(
        token=slack_config["token"],
        channels=slack_config["channels"],
        max_messages=slack_config["max_messages"],
        days_back=slack_config["days_back"]
    )
    
    print(f"✅ Ingestion completed:")
    print(f"   Documents: {result.get('documents_created', 0)}")
    print(f"   Chunks: {result.get('chunks_created', 0)}")
    print(f"   Embeddings: {result.get('embeddings_created', 0)}")
    
except Exception as e:
    print(f"❌ Ingestion failed: {str(e)}")
    import traceback
    traceback.print_exc()
'''
        
        # Write script to temporary file
        script_file = self.django_root / "temp_ingest.py"
        with open(script_file, "w") as f:
            f.write(ingestion_script)
        
        try:
            # Run the script
            result = self.run_command(f"python manage.py shell < temp_ingest.py")
            
            if result.returncode == 0:
                logger.info("✅ Data ingestion completed successfully")
            else:
                logger.error("❌ Data ingestion failed")
                logger.warning("Continuing anyway - you can ingest data manually later")
                
        finally:
            # Clean up temporary file
            if script_file.exists():
                script_file.unlink()

    def verify_setup(self):
        """Verify the setup is working."""
        logger.info("🔍 Verifying setup...")
        
        verification_script = '''
from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.documents.models import Document, DocumentChunk
from apps.search.models import SearchQuery

# Check user and tenant
user = User.objects.filter(username="mahesh").first()
tenant = Tenant.objects.filter(slug="stride").first()

print(f"User: {user}")
print(f"Tenant: {tenant}")

# Check data
doc_count = Document.objects.filter(tenant=tenant).count() if tenant else 0
chunk_count = DocumentChunk.objects.filter(tenant=tenant).count() if tenant else 0

print(f"Documents: {doc_count}")
print(f"Chunks: {chunk_count}")

if user and tenant and doc_count > 0:
    print("✅ Setup verification successful!")
else:
    print("⚠️  Setup verification incomplete - some data may be missing")
'''
        
        # Write script to temporary file
        script_file = self.django_root / "temp_verify.py"
        with open(script_file, "w") as f:
            f.write(verification_script)
        
        try:
            # Run the script
            result = self.run_command(f"python manage.py shell < temp_verify.py")
            
            if result.returncode == 0:
                logger.info("✅ Setup verification completed")
            else:
                logger.error("❌ Setup verification failed")
                
        finally:
            # Clean up temporary file
            if script_file.exists():
                script_file.unlink()

    def reset_complete_database(self):
        """Perform complete database reset."""
        logger.info("🚀 Starting complete database reset...")
        logger.info("=" * 80)
        
        try:
            # Step 1: Drop and recreate database
            self.drop_and_create_database()
            
            # Step 2: Delete migration files
            self.delete_migration_files()
            
            # Step 3: Create fresh migrations
            self.create_fresh_migrations()
            
            # Step 4: Apply migrations
            self.apply_migrations()
            
            # Step 5: Create superuser and tenant
            self.create_superuser_and_tenant()
            
            # Step 6: Ingest sample data
            self.ingest_sample_data()
            
            # Step 7: Verify setup
            self.verify_setup()
            
            logger.info("\n" + "=" * 80)
            logger.info("🎉 Complete database reset successful!")
            logger.info("🧪 Ready for RAG service testing!")
            logger.info("=" * 80)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Database reset failed: {str(e)}")
            return False


def main():
    """Main execution function."""
    try:
        resetter = DatabaseResetter()
        success = resetter.reset_complete_database()
        
        if success:
            logger.info("\n🎉 Database reset completed successfully!")
            logger.info("You can now test the consolidated RAG service.")
            return 0
        else:
            logger.error("\n❌ Database reset failed!")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Script execution failed: {str(e)}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit(main())
