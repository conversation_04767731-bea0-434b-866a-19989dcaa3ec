{"date": "2025-01-16", "channel_id": "C065QSSNH8A", "message_count": 21, "messages": [{"ts": "1737050420.933449", "text": "Ok, we will take a look. Thats strange, Some changes were made to filters recently w.r.t column configurator which could have caused regression. should fixable by tomorrow.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Wxpbd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok, we will take a look. Thats strange, Some changes were made to filters recently w.r.t column configurator which could have caused regression. should fixable by tomorrow."}]}]}]}, {"ts": "1737050351.365759", "text": "The biggest issue is that when we were in testing - the export of the merit table did not match the data in the table. Apparently htat is still the case", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737050351.365759", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "LGa5c", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The biggest issue is that when we were in testing - the export of the merit table did not match the data in the table. Apparently htat is still the case"}]}]}]}, {"ts": "1737050326.002459", "text": "FWIW she does go back and forth between panic and being fine, but yes, we need to get her data right.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XMwe9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "FWIW she does go back and forth between panic and being fine, but yes, we need to get her data right."}]}]}]}, {"ts": "1737049756.196029", "text": "<@U07EJ2LP44S> It would help if you can keep me updated on whenever you have calls with customer and what the call about.I can do some sanity for whatever is expected for the call. Typically implementations are always painful with specially integrations sync. I see that <PERSON> is losing confidence.\nPlease share summary of what are the data updates needs to be done and cycle stuff needs to be taken care for all customers. I will do the updates during my day and do some sanity to make your life easy.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1737049793.000000"}, "blocks": [{"type": "rich_text", "block_id": "B8ilA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " It would help if you can keep me updated on whenever you have calls with customer and what the call about.I can do some sanity for whatever is expected for the call. Typically implementations are always painful with specially integrations sync. I see that <PERSON> is losing confidence.\nPlease share summary of what are the data updates needs to be done and cycle stuff needs to be taken care for all customers. I will do the updates during my day and do some sanity to make your life easy."}]}]}]}, {"ts": "1737047980.786159", "text": "<@U07M6QKHUC9> <PERSON> swears hire date was there, at the furthest right on the table, and i DO remember that. Was it removed in this change? Can we please add it back for her?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737047980.786159", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "v+VI+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " <PERSON> swears hire date was there, at the furthest right on the table, and i DO remember that. Was it removed in this change? Can we please add it back for her?"}]}]}]}, {"ts": "1737047527.335619", "text": "<@U0690EB5JE5> can you pls provide an update on the employment history for <PERSON>ith<PERSON>?\nAlso are we going to be able to individual employee benefits into the rewards letter?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737047527.335619", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "JBw4Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you pls provide an update on the employment history for Tith<PERSON>?\nAlso are we going to be able to individual employee benefits into the rewards letter?"}]}]}]}, {"ts": "1737045110.570649", "text": "Will check in an hour ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "esuHz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will check in an hour "}]}]}]}, {"ts": "**********.648899", "text": "<@U0690EB5JE5> Something is going wrong with Diversified's Data. I removed the non-bonus employees manually. In that update, <PERSON> - the CEO - was included as an employee to keep/update with his target bonus. File completed fine.\n\nNow I have added a root user dummy employee, so that I can make <PERSON> report to them. However, <PERSON> is not in the account anywhere. He's listed as a reporting manager, but he's not in the org list or in the reporting download. He IS in the data template download.\n\nUltimately I need to change him from the root employee to reporting to the new root employee, but he's not in the front end org view list at all. Help!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.648899", "reply_count": 15, "blocks": [{"type": "rich_text", "block_id": "PFyT7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Something is going wrong with Diversified's Data. I removed the non-bonus employees manually. In that update, <PERSON> - the CEO - was included as an employee to keep/update with his target bonus. File completed fine.\n\nNow I have added a root user dummy employee, so that I can make <PERSON> report to them. However, <PERSON> is not in the account anywhere. He's listed as a reporting manager, but he's not in the org list or in the reporting download. He IS in the data template download.\n\nUltimately I need to change him from the root employee to reporting to the new root employee, but he's not in the front end org view list at all. Help!"}]}]}]}, {"ts": "**********.479409", "text": "<https://docs.google.com/document/d/1-ziSXYmQUHtzy0t4yHnqXjfHyAdEKsZSpCCDuJSSVj4/edit?tab=t.0>\n<@U0690EB5JE5> <@U07EJ2LP44S> <@U07M6QKHUC9>\nEmail notification templates for different scenario", "user": "U06HN8XDC5A", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "p6TIx", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/document/d/1-ziSXYmQUHtzy0t4yHnqXjfHyAdEKsZSpCCDuJSSVj4/edit?tab=t.0"}, {"type": "text", "text": "\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": "\nEmail notification templates for different scenario"}]}]}]}, {"ts": "1737037278.178419", "text": "I guess data sync has caused some issue. Re upload should fix the problem. I will look into the integration code.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "cL0zS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I guess data sync has caused some issue. Re upload should"}, {"type": "text", "text": " "}, {"type": "text", "text": "fix the problem. I will look into the integration code."}]}]}]}, {"ts": "1737037184.481569", "text": "<@U0690EB5JE5> Will make a ticket now, but no bands are being matched to employees in org view in Curana. The band and compa ratio DOES show in merit view and in reporting, but it shows no band for anyone in org view", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737037184.481569", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "XW49B", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Will make a ticket now, but no bands are being matched to employees in org view in Curana. The band and compa ratio DOES show in merit view and in reporting, but it shows no band for anyone in org view"}]}]}]}, {"ts": "1737036397.248179", "text": "<@U0690EB5JE5> Experiencing this same bug in Diversified - the role assigning. <https://compiify.atlassian.net/browse/COM-4063>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737036397.248179", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14146::7acf9002b5cf43da8c919584b2b182cb", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4063?atlOrigin=eyJpIjoiNTBkMjIwMDg2OGZlNDQxMWI0ZDY0NzI1Nzg2ZjZhNjEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4063 Issue with Role Assigning in Curana>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14146::61cb8d3b67834e6f9fb1ef8a861cc06f", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14146\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4063\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4063", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "yEfkv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Experiencing this same bug in Diversified - the role assigning. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4063"}]}]}]}, {"ts": "1737035575.488719", "text": "The market adjustment filter is also not working - I am able to add a market adjustment no matter what the compa ratio is", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737035575.488719", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "A35v7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The market adjustment filter is also not working - I am able to add a market adjustment no matter what the compa ratio is"}]}]}]}, {"ts": "1737035543.532119", "text": "Oh, b/c they got wiped in the data refresh?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737035543.532119", "reply_count": 33, "blocks": [{"type": "rich_text", "block_id": "jQR2x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Oh, b/c they got wiped in the data refresh?"}]}]}]}, {"ts": "1737035370.564259", "text": "<@U07EJ2LP44S> that’s because there are no ratings assigned ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "p6876", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "text", "text": "that’s"}, {"type": "text", "text": " because there are no ratings assigned "}]}]}]}, {"ts": "1737035311.358999", "text": "<@U0690EB5JE5> bug in Curana cycle builder, system unable to calculate budgets. <https://compiify.atlassian.net/browse/COM-4066>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737035311.358999", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14149::4b8f9a4408904bf5a00155f379e0451c", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4066?atlOrigin=eyJpIjoiNGJlYjJmZDg2NTA3NGMxYjhkZGRlYWJkZmMyYTkzOTYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4066 Issue: Budget Calculation Error in Comp Builder>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14149::c193a206f25843299f4a0fe6d6dae0b4", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14149\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4066\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4066", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "p9wW1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " bug in Curana cycle builder, system unable to calculate budgets. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4066"}]}]}]}, {"ts": "1737024831.302629", "text": "<@U07EJ2LP44S> Can you confirm if all good with HRBP experience for Valgenesis?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737024831.302629", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "CNxds", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Can you confirm if all good with HRBP experience for Valgenesis?"}]}]}]}, {"ts": "1737023493.479249", "text": "<@U07EJ2LP44S> I re-synced VG and no new updates, I remember we refreshed couple of weeks ago.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737023493.479249", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "z7yVl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I re-synced VG and no new updates, I remember we refreshed couple of weeks ago."}]}]}]}, {"ts": "1737015269.635749", "text": "Thank You <@U07M6QKHUC9>. <@U07EJ2LP44S> Please do share any updates or anything required from engineering end.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "dvfly", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank You "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": ". "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please do share any updates or anything required from engineering end."}]}]}]}, {"ts": "1737015170.483369", "text": "Canceling tomorrow’s standup as <PERSON><PERSON><PERSON> is unable to make it. ", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DSGN/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Canceling tomorrow’s standup as <PERSON><PERSON><PERSON> is unable to make it. "}]}]}]}, {"ts": "1737010593.439139", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> Please let us know if any feedback. The change is already pushed though.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736939150.202069", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "SaCRJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Please let us know if any feedback. The change is already pushed though."}]}]}]}], "created_at": "2025-05-22T21:35:34.691107"}