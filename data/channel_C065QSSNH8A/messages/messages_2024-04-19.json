{"date": "2024-04-19", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1713540759.191169", "text": "<!here>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mK6AI", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}]}]}]}, {"ts": "1713540756.454589", "text": "Demo meeting link\n<https://us06web.zoom.us/j/88688933175?pwd=V0DgBg5Gw7jXjgh6raW36aOcbdcNbP.1>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Zs3JW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Demo meeting link\n"}, {"type": "link", "url": "https://us06web.zoom.us/j/88688933175?pwd=V0DgBg5Gw7jXjgh6raW36aOcbdcNbP.1"}]}]}]}, {"ts": "1713518815.088239", "text": "<@U04DKEFP1K8> here are all the assets for the all the pages of website for Gaurav to use:\n<https://drive.google.com/drive/folders/17a3-QKU3t1mF9NlPJ9InwTpuK4X2c2iU?usp=drive_link>\n\nHere are all the Figma designs for the website. Let me know if you don't have access to this Figma folder for Stride\n<https://www.figma.com/files/project/200626551/Stride?fuid=1170850046975729676>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1713518815.088239", "reply_count": 2, "edited": {"user": "U04DS2MBWP4", "ts": "1713519726.000000"}, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "A98O4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " here are all the assets for the all the pages of website for Gaurav to use:\n"}, {"type": "link", "url": "https://drive.google.com/drive/folders/17a3-QKU3t1mF9NlPJ9InwTpuK4X2c2iU?usp=drive_link"}, {"type": "text", "text": "\n\nHere are all the Figma designs for the website. Let me know if you don't have access to this Figma folder for Stride\n"}, {"type": "link", "url": "https://www.figma.com/files/project/200626551/Stride?fuid=1170850046975729676"}]}]}]}, {"ts": "**********.651849", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> For Vercara, they do not have an upcoming cycle that they are targeting yet, they just want to get their environment loaded ASAP so they can start understanding the platform and using the built-in reports.\n\n• They use Microsoft for SSO. We could try enabling Microsoft through Auth0, or could start them off with local login first if we need more time to finish testing Auth0 changes.\n• Their main HRIS is TriNet, so I assume we want to try using an integration for the initial data load. (relevant to the question above about getting credentials)\n• This is the customer who requested a customization to allow them to attach a PDF document of goals to every employee. Previously <https://compiify.slack.com/archives/C06GV6YM6VA/p1713197566999449?thread_ts=**********.104579&amp;cid=C06GV6YM6VA|Mahesh estimated> this is about 1 week of effort (of course we need to define the requirements first), so we will want to provide them a rough ETA for being able to support that -- the sooner, the better. \n• Our target for having data loaded is *Friday, April 26th* so that we can give them a walkthrough on Monday, April 29th with their data in their account. Please let me know if you foresee any issues hitting that timeline!\nThis is our first paying customer so let's make sure we give them an excellent experience!", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.651849", "reply_count": 11, "blocks": [{"type": "rich_text", "block_id": "2SGfF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " For Vercara, they do not have an upcoming cycle that they are targeting yet, they just want to get their environment loaded ASAP so they can start understanding the platform and using the built-in reports.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They use Microsoft for SSO. We could try enabling Microsoft through Auth0, or could start them off with local login first if we need more time to finish testing Auth0 changes."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Their main HRIS is TriNet, so I assume we want to try using an integration for the initial data load. (relevant to the question above about getting credentials)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "This is the customer who requested a customization to allow them to attach a PDF document of goals to every employee. Previously "}, {"type": "link", "url": "https://compiify.slack.com/archives/C06GV6YM6VA/p1713197566999449?thread_ts=**********.104579&cid=C06GV6YM6VA", "text": "<PERSON><PERSON><PERSON> estimated"}, {"type": "text", "text": " this is about 1 week of effort (of course we need to define the requirements first), so we will want to provide them a rough ETA for being able to support that -- the sooner, the better. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Our target for having data loaded is "}, {"type": "text", "text": "Friday, April 26th", "style": {"bold": true}}, {"type": "text", "text": " so that we can give them a walkthrough on Monday, April 29th with their data in their account. Please let me know if you foresee any issues hitting that timeline!"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nThis is our first paying customer so let's make sure we give them an excellent experience!"}]}]}]}, {"ts": "**********.052059", "text": "<@U0690EB5JE5> We had an onboarding call today with Vercara and they are using Trinet for their HRIS. What do I need to request from them to enable an integration?\n\nI know we had requested an API key from SDF, but according to <https://help.merge.dev/en/articles/5708552-trinet-how-do-i-link-my-account|this article from Merge>, it seems like generating the client ID &amp; secret requires them to select an integration type within Trinet first. Do we know what selection they should make?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.052059", "reply_count": 2, "attachments": [{"image_url": "https://downloads.intercomcdn.com/i/o/379082/107841514fe57a3f0b2a855a/065e08fde79f8a1ee25eb6318655bdc9.jpg", "image_width": 1200, "image_height": 628, "image_bytes": 74161, "from_url": "https://help.merge.dev/en/articles/5708552-trinet-how-do-i-link-my-account", "id": 1, "original_url": "https://help.merge.dev/en/articles/5708552-trinet-how-do-i-link-my-account", "fallback": "TriNet - How do I link my account? | Merge Help Center", "text": "How to link your TriNet account to Merge", "title": "TriNet - How do I link my account? | Merge Help Center", "title_link": "https://help.merge.dev/en/articles/5708552-trinet-how-do-i-link-my-account", "service_name": "help.merge.dev"}], "blocks": [{"type": "rich_text", "block_id": "cx4/K", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We had an onboarding call today with Vercara and they are using Trinet for their HRIS. What do I need to request from them to enable an integration?\n\nI know we had requested an API key from SDF, but according to "}, {"type": "link", "url": "https://help.merge.dev/en/articles/5708552-trinet-how-do-i-link-my-account", "text": "this article from Merge"}, {"type": "text", "text": ", it seems like generating the client ID & secret requires them to select an integration type within Trinet first. Do we know what selection they should make?"}]}]}]}], "created_at": "2025-05-22T21:35:34.603741"}