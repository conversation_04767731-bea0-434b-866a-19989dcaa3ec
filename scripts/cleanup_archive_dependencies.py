#!/usr/bin/env python
"""
Archive Dependencies Cleanup Script

This script removes all dependencies on the archived RAG services to make it safe
to delete the archive folder. It:

1. Removes old cached service functions from service_cache.py
2. Updates scripts to use the new RAGService
3. Updates documentation references
4. Verifies no remaining dependencies

Usage:
    cd multi_source_rag
    python ../scripts/cleanup_archive_dependencies.py
"""

import os
import sys
import re
from pathlib import Path

def print_banner():
    """Print cleanup banner."""
    print("🧹 ARCHIVE DEPENDENCIES CLEANUP")
    print("=" * 50)

def cleanup_service_cache():
    """Remove old cached service functions."""
    print("🔧 Cleaning up service cache...")
    
    service_cache_path = Path("apps/core/utils/service_cache.py")
    
    if not service_cache_path.exists():
        print("❌ Service cache file not found")
        return False
    
    # Read the file
    with open(service_cache_path, 'r') as f:
        content = f.read()
    
    # Remove the old functions
    lines = content.split('\n')
    new_lines = []
    skip_until_next_function = False
    
    for line in lines:
        # Start skipping at old function definitions
        if line.startswith('@lru_cache') and ('unified_rag' in lines[lines.index(line) + 1] or 'enhanced_rag' in lines[lines.index(line) + 1]):
            skip_until_next_function = True
            continue
        
        # Stop skipping at next function or class definition
        if skip_until_next_function and (line.startswith('def ') or line.startswith('class ') or line.startswith('@')):
            if 'get_cached_rag_service' in line or 'clear_service_cache' in line:
                skip_until_next_function = False
            else:
                continue
        
        if not skip_until_next_function:
            new_lines.append(line)
    
    # Write back the cleaned content
    with open(service_cache_path, 'w') as f:
        f.write('\n'.join(new_lines))
    
    print("✅ Service cache cleaned up")
    return True

def update_scripts():
    """Update scripts to use new RAGService."""
    print("📝 Updating scripts...")
    
    scripts_dir = Path("../scripts")
    if not scripts_dir.exists():
        scripts_dir = Path("scripts")
    
    updated_count = 0
    
    for script_file in scripts_dir.glob("*.py"):
        if script_file.name == "cleanup_archive_dependencies.py":
            continue
            
        try:
            with open(script_file, 'r') as f:
                content = f.read()
            
            original_content = content
            
            # Replace imports
            content = re.sub(
                r'from apps\.search\.services\.unified_rag_service import UnifiedRAGService',
                'from apps.search.services.rag_service import RAGService',
                content
            )
            
            content = re.sub(
                r'from apps\.search\.services\.enhanced_rag_service import EnhancedRAGService',
                'from apps.search.services.rag_service import RAGService',
                content
            )
            
            # Replace class usage
            content = re.sub(r'UnifiedRAGService\(', 'RAGService(user=user, tenant_slug=', content)
            content = re.sub(r'EnhancedRAGService\(', 'RAGService(user=user, tenant_slug=', content)
            
            if content != original_content:
                with open(script_file, 'w') as f:
                    f.write(content)
                updated_count += 1
                print(f"   ✅ Updated {script_file.name}")
                
        except Exception as e:
            print(f"   ⚠️  Error updating {script_file.name}: {e}")
    
    print(f"✅ Updated {updated_count} scripts")
    return True

def update_documentation():
    """Update documentation references."""
    print("📚 Updating documentation...")
    
    docs_dir = Path("docs")
    if not docs_dir.exists():
        print("⚠️  Docs directory not found")
        return True
    
    updated_count = 0
    
    for doc_file in docs_dir.glob("*.md"):
        try:
            with open(doc_file, 'r') as f:
                content = f.read()
            
            original_content = content
            
            # Replace service references in code examples
            content = re.sub(
                r'from apps\.search\.services\.unified_rag_service import UnifiedRAGService',
                'from apps.search.services.rag_service import RAGService',
                content
            )
            
            content = re.sub(
                r'from apps\.search\.services\.enhanced_rag_service import EnhancedRAGService',
                'from apps.search.services.rag_service import RAGService',
                content
            )
            
            # Replace class usage in examples
            content = re.sub(r'UnifiedRAGService\(', 'RAGService(user=user, tenant_slug=', content)
            content = re.sub(r'EnhancedRAGService\(', 'RAGService(user=user, tenant_slug=', content)
            
            if content != original_content:
                with open(doc_file, 'w') as f:
                    f.write(content)
                updated_count += 1
                print(f"   ✅ Updated {doc_file.name}")
                
        except Exception as e:
            print(f"   ⚠️  Error updating {doc_file.name}: {e}")
    
    print(f"✅ Updated {updated_count} documentation files")
    return True

def verify_no_dependencies():
    """Verify no remaining dependencies on archived services."""
    print("🔍 Verifying no remaining dependencies...")
    
    # Search for references to archived services
    search_patterns = [
        r'unified_rag_service',
        r'enhanced_rag_service',
        r'UnifiedRAGService',
        r'EnhancedRAGService',
        r'apps\.search\.services\.archive'
    ]
    
    found_references = []
    
    # Search in Python files
    for py_file in Path(".").rglob("*.py"):
        if "archive" in str(py_file):
            continue  # Skip archive files themselves
        if "cleanup_archive_dependencies.py" in str(py_file):
            continue  # Skip this script
            
        try:
            with open(py_file, 'r') as f:
                content = f.read()
            
            for pattern in search_patterns:
                if re.search(pattern, content):
                    found_references.append((str(py_file), pattern))
                    
        except Exception:
            continue
    
    # Search in documentation
    for md_file in Path("docs").rglob("*.md"):
        try:
            with open(md_file, 'r') as f:
                content = f.read()
            
            for pattern in search_patterns:
                if re.search(pattern, content):
                    found_references.append((str(md_file), pattern))
                    
        except Exception:
            continue
    
    if found_references:
        print("❌ Found remaining dependencies:")
        for file_path, pattern in found_references:
            print(f"   {file_path}: {pattern}")
        return False
    else:
        print("✅ No remaining dependencies found")
        return True

def main():
    """Main cleanup function."""
    print_banner()
    
    steps = [
        ("Service Cache Cleanup", cleanup_service_cache),
        ("Scripts Update", update_scripts),
        ("Documentation Update", update_documentation),
        ("Dependency Verification", verify_no_dependencies),
    ]
    
    results = {}
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        results[step_name] = step_func()
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 CLEANUP SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for step_name, passed_step in results.items():
        status = "✅ COMPLETED" if passed_step else "❌ FAILED"
        print(f"{step_name}: {status}")
        if passed_step:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} steps completed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 CLEANUP SUCCESSFUL!")
        print("✅ Archive folder can now be safely deleted")
        print("\nTo delete the archive:")
        print("   rm -rf apps/search/services/archive/")
        return 0
    else:
        print("\n❌ CLEANUP INCOMPLETE!")
        print("⚠️  Some dependencies still exist - review failed steps")
        return 1

if __name__ == "__main__":
    exit(main())
