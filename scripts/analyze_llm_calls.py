#!/usr/bin/env python
"""
LLM Call Analysis Script

This script analyzes why we still have 22 LLM calls instead of the expected 5.
It examines the CitationQueryEngine and response synthesis process.

Author: AI Assistant
Date: 2025-01-30
"""

import os
import sys
import django

# Set up Django
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def analyze_citation_query_engine():
    """Analyze CitationQueryEngine LLM call pattern."""
    print("🔍 Analyzing CitationQueryEngine LLM Call Pattern")
    print("=" * 60)
    
    print("\n📋 CitationQueryEngine Process Breakdown:")
    print("1. Vector Search: 1 call (fast - Qdrant)")
    print("2. Response Synthesis: Multiple LLM calls")
    print("3. Citation Generation: Additional LLM calls")
    
    print("\n🔍 Response Synthesis Analysis:")
    print("Current mode: 'compact' (should be fewer calls)")
    print("Expected: 1-3 LLM calls for synthesis")
    print("Actual: ~20+ LLM calls")
    
    print("\n❓ Why 22 LLM calls instead of 5?")
    print("\nPossible causes:")
    print("1. 📄 Document Chunking:")
    print("   - Retrieved 10 documents (similarity_top_k=10)")
    print("   - Each document may be processed separately")
    print("   - 10 documents × 2 LLM calls = 20 calls")
    
    print("\n2. 🔄 Iterative Processing:")
    print("   - Compact mode may still iterate through chunks")
    print("   - Each chunk requires LLM processing")
    print("   - Large documents = more chunks = more calls")
    
    print("\n3. 📝 Citation Processing:")
    print("   - Citation generation per document")
    print("   - Citation formatting LLM calls")
    print("   - Citation validation calls")
    
    print("\n4. 🏗️ Response Building:")
    print("   - Initial response generation")
    print("   - Response refinement")
    print("   - Final formatting")

def analyze_compact_mode():
    """Analyze compact response mode behavior."""
    print("\n🔍 Compact Response Mode Analysis:")
    print("=" * 40)
    
    print("Expected behavior:")
    print("- Concatenate all retrieved text")
    print("- Make 1-2 LLM calls for synthesis")
    print("- Minimal iterative processing")
    
    print("\nActual behavior (based on 22 calls):")
    print("- Still processing documents individually")
    print("- Making LLM calls per document/chunk")
    print("- Not truly 'compact' processing")

def analyze_similarity_top_k():
    """Analyze impact of similarity_top_k parameter."""
    print("\n📊 Similarity Top-K Analysis:")
    print("=" * 35)
    
    print("Current setting: similarity_top_k=10")
    print("Impact: Retrieves 10 documents")
    print("LLM calls pattern: ~2 calls per document = 20 calls")
    print("Additional calls: 2 for final synthesis = 22 total")
    
    print("\n💡 Optimization opportunity:")
    print("- Reduce similarity_top_k from 10 to 3-5")
    print("- Expected reduction: 22 calls → 8-12 calls")
    print("- Performance improvement: ~50% fewer LLM calls")

def analyze_citation_chunk_size():
    """Analyze citation chunk size impact."""
    print("\n📏 Citation Chunk Size Analysis:")
    print("=" * 35)
    
    print("Current setting: citation_chunk_size=256")
    print("Impact: Smaller chunks = more chunks = more LLM calls")
    print("Each document may be split into multiple chunks")
    
    print("\n💡 Optimization opportunity:")
    print("- Increase citation_chunk_size to 512 or 1024")
    print("- Fewer chunks = fewer LLM calls")
    print("- Trade-off: Larger context per call")

def provide_optimization_recommendations():
    """Provide specific recommendations to reduce LLM calls."""
    print("\n🎯 LLM Call Reduction Recommendations:")
    print("=" * 45)
    
    print("\n1. 🔢 Reduce similarity_top_k:")
    print("   Current: 10 → Recommended: 3-5")
    print("   Expected reduction: 50% fewer LLM calls")
    
    print("\n2. 📏 Increase citation_chunk_size:")
    print("   Current: 256 → Recommended: 512-1024")
    print("   Expected reduction: 30% fewer LLM calls")
    
    print("\n3. 🎛️ Use 'simple' response mode:")
    print("   Current: 'compact' → Recommended: 'simple'")
    print("   Expected reduction: 40% fewer LLM calls")
    
    print("\n4. 🚫 Disable citation engine:")
    print("   Use simple RetrieverQueryEngine instead")
    print("   Expected reduction: 80% fewer LLM calls")
    
    print("\n5. 📦 Batch processing:")
    print("   Process multiple documents in single LLM call")
    print("   Expected reduction: 60% fewer LLM calls")

def calculate_expected_improvements():
    """Calculate expected performance improvements."""
    print("\n📈 Expected Performance Improvements:")
    print("=" * 40)
    
    current_calls = 22
    current_time_per_call = 20  # seconds (Ollama)
    current_total_time = current_calls * current_time_per_call
    
    print(f"Current performance:")
    print(f"- LLM calls: {current_calls}")
    print(f"- Time per call: {current_time_per_call}s")
    print(f"- Total LLM time: {current_total_time}s ({current_total_time/60:.1f} minutes)")
    
    # Scenario 1: Reduce similarity_top_k to 5
    scenario1_calls = 12
    scenario1_time = scenario1_calls * current_time_per_call
    scenario1_improvement = ((current_total_time - scenario1_time) / current_total_time) * 100
    
    print(f"\nScenario 1 (similarity_top_k=5):")
    print(f"- LLM calls: {scenario1_calls}")
    print(f"- Total time: {scenario1_time}s ({scenario1_time/60:.1f} minutes)")
    print(f"- Improvement: {scenario1_improvement:.1f}%")
    
    # Scenario 2: Use simple response mode
    scenario2_calls = 5
    scenario2_time = scenario2_calls * current_time_per_call
    scenario2_improvement = ((current_total_time - scenario2_time) / current_total_time) * 100
    
    print(f"\nScenario 2 (simple mode + top_k=5):")
    print(f"- LLM calls: {scenario2_calls}")
    print(f"- Total time: {scenario2_time}s ({scenario2_time/60:.1f} minutes)")
    print(f"- Improvement: {scenario2_improvement:.1f}%")
    
    # Scenario 3: With Gemini
    gemini_time_per_call = 2  # seconds (Gemini Flash)
    scenario3_time = scenario2_calls * gemini_time_per_call
    scenario3_improvement = ((current_total_time - scenario3_time) / current_total_time) * 100
    
    print(f"\nScenario 3 (Gemini + optimizations):")
    print(f"- LLM calls: {scenario2_calls}")
    print(f"- Time per call: {gemini_time_per_call}s")
    print(f"- Total time: {scenario3_time}s")
    print(f"- Improvement: {scenario3_improvement:.1f}%")

def main():
    """Run LLM call analysis."""
    print("🔍 LLM Call Analysis - Why 22 Calls?")
    print("=" * 50)
    
    analyze_citation_query_engine()
    analyze_compact_mode()
    analyze_similarity_top_k()
    analyze_citation_chunk_size()
    provide_optimization_recommendations()
    calculate_expected_improvements()
    
    print("\n" + "=" * 50)
    print("🎯 CONCLUSION")
    print("=" * 50)
    
    print("\n❓ Why 22 LLM calls?")
    print("1. CitationQueryEngine processes 10 documents individually")
    print("2. Each document requires ~2 LLM calls (synthesis + citation)")
    print("3. Additional calls for final response assembly")
    
    print("\n💡 Quick wins to reduce calls:")
    print("1. similarity_top_k: 10 → 5 (50% reduction)")
    print("2. response_mode: 'compact' → 'simple' (40% reduction)")
    print("3. Switch to Gemini (90% time reduction)")
    
    print("\n🎯 Target: 5 LLM calls, <10 seconds total")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
