#!/usr/bin/env python3
"""
Comprehensive UI Test Suite - Production Readiness Validation
Tests the complete user experience from UX designer perspective.
"""

import os
import sys
import django
import requests
from datetime import datetime
import json

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def test_query_types():
    """Test different types of queries to validate UI consistency."""

    print("🧪 Testing Different Query Types")
    print("=" * 60)

    test_queries = [
        {
            "query": "list issues reported by <PERSON>",
            "type": "list_issues",
            "expected_format": "structured list with dates and details"
        },
        {
            "query": "whats latest on curana?",
            "type": "latest_updates",
            "expected_format": "chronological updates with dates"
        },
        {
            "query": "explain the database schema",
            "type": "explanation",
            "expected_format": "detailed explanation with structure"
        },
        {
            "query": "how to setup authentication?",
            "type": "how_to",
            "expected_format": "step-by-step instructions"
        },
        {
            "query": "compare different deployment strategies",
            "type": "analysis",
            "expected_format": "comparative analysis with pros/cons"
        }
    ]

    results = {}

    for test_case in test_queries:
        print(f"\n🔍 Testing: '{test_case['query']}'")
        print(f"📋 Type: {test_case['type']}")
        print(f"📝 Expected: {test_case['expected_format']}")

        # Test the template filter directly
        try:
            from apps.search.templatetags.search_extras import markdown_to_html

            # Simulate a response that might come from the LLM
            mock_response = generate_mock_response(test_case['type'])

            # Process through our template filter
            processed_html = markdown_to_html(mock_response)

            # Analyze the output
            analysis = analyze_html_output(processed_html, test_case['type'])

            results[test_case['query']] = {
                'type': test_case['type'],
                'analysis': analysis,
                'html_length': len(processed_html),
                'has_structure': check_html_structure(processed_html)
            }

            print(f"✅ Processed successfully")
            print(f"📊 Analysis: {analysis['summary']}")

        except Exception as e:
            print(f"❌ Error processing: {e}")
            results[test_case['query']] = {'error': str(e)}

    return results

def generate_mock_response(query_type):
    """Generate mock responses based on query type to test formatting."""

    responses = {
        "list_issues": """## Issues Reported by Amanda [Document 2921]

### Critical Issues
- **2024-11-06**: Login system timeout issues affecting user experience [Document 2922]
- **2024-10-15**: Database connection pool exhaustion during peak hours [Document 2923]
- **2024-09-28**: Email notification delays in production environment [Document 2924]

### Other Issues
- **2024-08-12**: UI responsiveness problems on mobile devices [Document 2925]
- **2024-07-20**: Search functionality returning incomplete results [Document 2926]

### Summary
Total issues found: 5
Most recent issue: November 6, 2024 - Login system timeout""",

        "latest_updates": """## Latest Updates on Curana [Document 2921]

### Most Recent Updates
- **2024-11-06**: Amanda reported new employee additions to the system [Document 2922]
- **2024-10-28**: Tori provided feedback on total rewards integration [Document 2923]

### Timeline of Events
1. **November 6, 2024**: System capacity increased for new hires - Amanda [Document 2924]
2. **October 28, 2024**: Rewards module testing completed - Tori [Document 2925]

### Current Status
System is ready for Q4 rollout as of November 6, 2024 [Document 2926]""",

        "explanation": """## Database Schema Overview [Document 2921]

### Core Tables
The database consists of several **key tables** that handle different aspects:

- **Users Table**: Stores user authentication and profile data [Document 2922]
- **Documents Table**: Contains all document metadata and content [Document 2923]
- **Sources Table**: Manages different data source configurations [Document 2924]

### Relationships
The schema uses *foreign key relationships* to maintain data integrity:

1. **One-to-Many**: Users can have multiple documents
2. **Many-to-Many**: Documents can belong to multiple sources

This is a **production-ready** schema with proper indexing.""",

        "how_to": """## Setting Up Authentication [Document 2921]

### Prerequisites
- Python 3.11+ installed [Document 2922]
- Django 4.2+ configured [Document 2923]

### Step-by-Step Instructions

1. **Install Dependencies**
   ```bash
   poetry install
   ```

2. **Configure Settings**
   - Update `AUTHENTICATION_BACKENDS` in settings [Document 2924]
   - Set up OAuth providers [Document 2925]

3. **Run Migrations**
   ```bash
   python manage.py migrate
   ```

### Verification
Test the setup by accessing `/accounts/login/` [Document 2926]""",

        "analysis": """## Deployment Strategy Comparison [Document 2921]

### Docker vs Traditional Deployment

#### Docker Deployment [Document 2922]
**Pros:**
- Consistent environments across dev/staging/prod
- Easy scaling with container orchestration
- Simplified dependency management

**Cons:**
- Additional overhead for small applications
- Learning curve for team members

#### Traditional Deployment [Document 2923]
**Pros:**
- Direct control over server configuration
- Lower resource overhead
- Familiar to most developers

**Cons:**
- Environment inconsistencies
- Complex dependency management

### Recommendation
For our use case, **Docker deployment** is recommended due to the multi-service architecture [Document 2924]."""
    }

    return responses.get(query_type, responses["explanation"])

def analyze_html_output(html, query_type):
    """Analyze the HTML output for quality and structure."""

    analysis = {
        'has_headers': '<h' in html,
        'has_lists': '<ul>' in html or '<ol>' in html,
        'has_styling_classes': 'response-heading' in html,
        'has_document_refs': '[Document' in html,
        'has_dates': any(month in html for month in ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']),
        'has_code_blocks': '<code>' in html or '<pre>' in html,
        'has_bold_text': '<strong>' in html,
        'has_italic_text': '<em>' in html,
        'structure_preserved': check_markdown_structure(html)
    }

    # Calculate quality score
    positive_points = sum([
        analysis['has_headers'],
        analysis['has_styling_classes'],
        analysis['has_dates'],
        analysis['structure_preserved'],
        not analysis['has_document_refs']  # Good if no document refs
    ])

    total_points = 5
    quality_score = (positive_points / total_points) * 100

    analysis['quality_score'] = quality_score
    analysis['summary'] = f"Quality: {quality_score:.0f}% - " + (
        "Excellent" if quality_score >= 80 else
        "Good" if quality_score >= 60 else
        "Needs Improvement"
    )

    return analysis

def check_html_structure(html):
    """Check if HTML has proper structure."""
    return {
        'has_proper_headers': '<h2 class="response-heading">' in html,
        'has_proper_lists': '<ul class="response-list">' in html,
        'has_paragraphs': '<p>' in html,
        'well_formatted': len(html.split('\n')) > 3
    }

def check_markdown_structure(html):
    """Check if markdown structure is preserved in HTML."""
    structure_indicators = [
        '<h2>' in html,  # Headers converted
        '<ul>' in html or '<ol>' in html,  # Lists converted
        '<p>' in html,   # Paragraphs created
        not html.count('<h2>') > html.count('</h2>'),  # Proper closing tags
    ]
    return sum(structure_indicators) >= 3

def test_conversation_detail_consistency():
    """Test conversation detail page consistency with main search UI."""

    print("\n🔍 Testing Conversation Detail Page Consistency")
    print("=" * 60)

    # Check if conversation detail template uses same styling
    conversation_template_path = "/Users/<USER>/Desktop/RAGSearch/multi_source_rag/apps/search/templates/search/conversation_detail.html"
    search_results_template_path = "/Users/<USER>/Desktop/RAGSearch/multi_source_rag/apps/search/templates/search/search_results.html"

    issues = []

    try:
        with open(conversation_template_path, 'r') as f:
            conv_content = f.read()

        with open(search_results_template_path, 'r') as f:
            search_content = f.read()

        # Check for consistency issues
        if 'search_results.css' not in conv_content:
            issues.append("❌ Conversation detail doesn't use search_results.css")

        if 'markdown_to_html' not in conv_content:
            issues.append("❌ Conversation detail doesn't use markdown_to_html filter")

        if 'response-container' not in conv_content:
            issues.append("❌ Conversation detail doesn't use response-container class")

        # Check for inline styles (bad practice)
        if '<style>' in conv_content:
            issues.append("⚠️  Conversation detail has inline styles instead of external CSS")

        if not issues:
            print("✅ Templates are consistent")
        else:
            for issue in issues:
                print(issue)

        return len(issues) == 0

    except FileNotFoundError as e:
        print(f"❌ Template file not found: {e}")
        return False

def test_header_message_clarity():
    """Test header message clarity and meaningfulness."""

    print("\n📝 Testing Header Message Clarity")
    print("=" * 60)

    # Test different scenarios
    test_cases = [
        {
            "context": "search results page",
            "current_header": "Answer",
            "issues": ["Too generic", "Doesn't indicate content type"]
        },
        {
            "context": "conversation detail",
            "current_header": "Assistant",
            "issues": ["Not descriptive", "Doesn't show response type"]
        }
    ]

    recommendations = []

    for case in test_cases:
        print(f"📍 Context: {case['context']}")
        print(f"🔤 Current: '{case['current_header']}'")
        print(f"❌ Issues: {', '.join(case['issues'])}")

        # Generate better header suggestions
        if "search results" in case['context']:
            suggestions = [
                "Search Results",
                "AI Response",
                "Answer & Analysis",
                "Intelligent Summary"
            ]
        else:
            suggestions = [
                "AI Assistant Response",
                "Detailed Answer",
                "Analysis & Insights"
            ]

        print(f"✅ Suggestions: {', '.join(suggestions)}")
        recommendations.extend(suggestions)
        print()

    return recommendations

def test_fixes_applied():
    """Test that all the fixes have been applied correctly."""

    print("\n🔧 Testing Applied Fixes")
    print("=" * 60)

    fixes_status = {}

    # Test 1: Conversation detail uses search_results.css
    conv_template_path = "/Users/<USER>/Desktop/RAGSearch/multi_source_rag/apps/search/templates/search/conversation_detail.html"
    try:
        with open(conv_template_path, 'r') as f:
            conv_content = f.read()

        fixes_status['css_consistency'] = 'search_results.css' in conv_content
        fixes_status['markdown_filter'] = 'markdown_to_html' in conv_content
        fixes_status['response_container'] = 'response-container' in conv_content

        print(f"✅ CSS Consistency: {'FIXED' if fixes_status['css_consistency'] else 'NEEDS FIX'}")
        print(f"✅ Markdown Filter: {'FIXED' if fixes_status['markdown_filter'] else 'NEEDS FIX'}")
        print(f"✅ Response Container: {'FIXED' if fixes_status['response_container'] else 'NEEDS FIX'}")

    except FileNotFoundError:
        print("❌ Conversation template not found")
        fixes_status['template_found'] = False

    # Test 2: Header messages improved
    search_results_path = "/Users/<USER>/Desktop/RAGSearch/multi_source_rag/apps/search/templates/search/search_results.html"
    try:
        with open(search_results_path, 'r') as f:
            search_content = f.read()

        fixes_status['header_improved'] = 'AI Response & Analysis' in search_content
        print(f"✅ Header Message: {'IMPROVED' if fixes_status['header_improved'] else 'NEEDS FIX'}")

    except FileNotFoundError:
        print("❌ Search results template not found")
        fixes_status['search_template_found'] = False

    return fixes_status

def main():
    """Main comprehensive test function."""

    print("🚀 Comprehensive UI Test Suite - Production Readiness")
    print("=" * 80)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Run all tests
    test_results = {}

    # Test 1: Query Types and Formatting
    test_results['query_formatting'] = test_query_types()

    # Test 2: Template Consistency (Updated)
    test_results['template_consistency'] = test_conversation_detail_consistency()

    # Test 3: Header Message Clarity
    test_results['header_clarity'] = test_header_message_clarity()

    # Test 4: Applied Fixes
    test_results['fixes_applied'] = test_fixes_applied()

    # Generate comprehensive report
    print("\n📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 80)

    # Query formatting results
    print("\n🎯 Query Formatting Quality:")
    for query, result in test_results['query_formatting'].items():
        if 'error' not in result:
            score = result['analysis']['quality_score']
            status = "✅" if score >= 80 else "⚠️" if score >= 60 else "❌"
            print(f"{status} {query}: {score:.0f}% quality")
        else:
            print(f"❌ {query}: Error - {result['error']}")

    # Template consistency
    print(f"\n🎨 Template Consistency: {'✅ PASS' if test_results['template_consistency'] else '❌ FAIL'}")

    # Applied fixes
    fixes = test_results['fixes_applied']
    fixes_passed = sum([
        fixes.get('css_consistency', False),
        fixes.get('markdown_filter', False),
        fixes.get('response_container', False),
        fixes.get('header_improved', False)
    ])
    print(f"\n🔧 Applied Fixes: {fixes_passed}/4 completed")

    # Overall assessment
    formatting_scores = [r['analysis']['quality_score'] for r in test_results['query_formatting'].values() if 'analysis' in r]
    avg_quality = sum(formatting_scores) / len(formatting_scores) if formatting_scores else 0

    print(f"\n🎯 Overall UI Quality Score: {avg_quality:.0f}%")

    if avg_quality >= 80 and test_results['template_consistency'] and fixes_passed >= 3:
        print("\n🎉 UI IS PRODUCTION READY!")
        print("✅ All tests passed with high quality scores")
        print("✅ Major fixes have been applied")
        print("\n🌐 Ready for browser testing:")
        print("1. Open http://127.0.0.1:8000/search/")
        print("2. Test query: 'list issues reported by Amanda'")
        print("3. Test query: 'whats latest on curana?'")
        print("4. Check conversation detail page consistency")
    else:
        print("\n⚠️  UI NEEDS IMPROVEMENTS")
        print("💡 Review the issues above and implement fixes")

    return test_results

if __name__ == "__main__":
    main()
