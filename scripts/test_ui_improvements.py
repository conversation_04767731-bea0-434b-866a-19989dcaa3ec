#!/usr/bin/env python3
"""
Comprehensive test script to validate UI improvements from a UX perspective.
Tests the complete user experience including formatting, readability, and navigation.
"""

import os
import sys
import django
import requests
from datetime import datetime

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def test_template_filter_improvements():
    """Test the enhanced template filter with all improvements."""
    
    print("🧪 Testing Enhanced Template Filter")
    print("=" * 50)
    
    from django.template import Template, Context
    
    # Test content with all the issues we're fixing
    test_content = """## Latest Updates on Curana [Document 2921]

### Most Recent Updates
- **2024-11-06**: <PERSON> reported new employee additions [Document 2922]
- **2024-02-26**: <PERSON> provided feedback on total rewards [Document 2923]

### Key People [Document 2924]
- **Amanda**: Product manager
- **Ma<PERSON>h**: Engineering lead [Document 2925]

This is a **bold** statement with *italic* text and a date: 2024-03-15."""

    template_string = """
    {% load search_extras %}
    <div class="response-container">
        {{ content|markdown_to_html }}
    </div>
    """
    
    try:
        template = Template(template_string)
        context = Context({'content': test_content})
        rendered = template.render(context)
        
        print("✅ Template rendered successfully!")
        
        # Check for improvements
        improvements = [
            ('[Document', 'Document references removed'),
            ('November 6, 2024', 'Date humanization working'),
            ('February 26, 2024', 'Multiple dates humanized'),
            ('March 15, 2024', 'Inline dates humanized'),
            ('<h2 class="response-heading">', 'Headers have styling classes'),
            ('<ul class="response-list">', 'Lists have styling classes'),
            ('<strong>', 'Bold text preserved'),
            ('<em>', 'Italic text preserved'),
        ]
        
        print("\n🔍 Improvement Checks:")
        print("-" * 30)
        all_passed = True
        
        for check, description in improvements:
            if check == '[Document':
                # This should NOT be found (improvement = removal)
                if check not in rendered:
                    print(f"✅ {description}: Success")
                else:
                    print(f"❌ {description}: Failed - still found document references")
                    all_passed = False
            else:
                # These should be found
                if check in rendered:
                    print(f"✅ {description}: Success")
                else:
                    print(f"❌ {description}: Failed - not found")
                    all_passed = False
        
        print(f"\n📝 Rendered HTML Preview:")
        print("-" * 30)
        # Show a clean preview
        preview = rendered.replace('\n    ', '\n').strip()
        print(preview[:500] + "..." if len(preview) > 500 else preview)
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Template rendering failed: {e}")
        return False

def test_date_humanization():
    """Test date humanization specifically."""
    
    print("\n📅 Testing Date Humanization")
    print("=" * 50)
    
    from apps.search.templatetags.search_extras import humanize_dates
    
    test_cases = [
        ("2024-11-06", "November 6, 2024"),
        ("2024-02-26", "February 26, 2024"),
        ("2024-03-15", "March 15, 2024"),
        ("On 2024-01-01 we started", "On January 1, 2024 we started"),
        ("Multiple dates: 2024-12-25 and 2024-07-04", "Multiple dates: December 25, 2024 and July 4, 2024"),
    ]
    
    all_passed = True
    for input_text, expected in test_cases:
        result = humanize_dates(input_text)
        if expected in result:
            print(f"✅ '{input_text}' -> '{result}'")
        else:
            print(f"❌ '{input_text}' -> '{result}' (expected: '{expected}')")
            all_passed = False
    
    return all_passed

def test_document_reference_cleaning():
    """Test document reference removal."""
    
    print("\n🧹 Testing Document Reference Cleaning")
    print("=" * 50)
    
    from apps.search.templatetags.search_extras import clean_document_references
    
    test_cases = [
        ("This is text [Document 2921]", "This is text"),
        ("Multiple [Document 123] references [Document 456]", "Multiple references"),
        ("Text with Document 789.", "Text with."),
        ("Clean text without references", "Clean text without references"),
        ("[Document 999] at the start", "at the start"),
    ]
    
    all_passed = True
    for input_text, expected in test_cases:
        result = clean_document_references(input_text)
        if result.strip() == expected.strip():
            print(f"✅ '{input_text}' -> '{result}'")
        else:
            print(f"❌ '{input_text}' -> '{result}' (expected: '{expected}')")
            all_passed = False
    
    return all_passed

def test_css_improvements():
    """Test CSS improvements for professional appearance."""
    
    print("\n🎨 Testing CSS Improvements")
    print("=" * 50)
    
    css_file = "/Users/<USER>/Desktop/RAGSearch/multi_source_rag/static/css/search_results.css"
    
    try:
        with open(css_file, 'r') as f:
            css_content = f.read()
        
        improvements = [
            ('font-weight: 500', 'Headers use medium weight (not bold)'),
            ('border-bottom: 1px solid', 'Subtle header underlines'),
            ('line-height: 1.3', 'Proper line height for headers'),
            ('.response-heading', 'Header styling classes exist'),
            ('.response-list', 'List styling classes exist'),
            ('.citation-number', 'Citation styling exists'),
        ]
        
        all_passed = True
        for check, description in improvements:
            if check in css_content:
                print(f"✅ {description}: Found")
            else:
                print(f"❌ {description}: Missing")
                all_passed = False
        
        return all_passed
        
    except FileNotFoundError:
        print(f"❌ CSS file not found: {css_file}")
        return False

def test_server_accessibility():
    """Test if the server is running and accessible."""
    
    print("\n🌐 Testing Server Accessibility")
    print("=" * 50)
    
    try:
        response = requests.get("http://127.0.0.1:8000/search/", timeout=5)
        
        if response.status_code == 200:
            print("✅ Server is accessible")
            return True
        elif response.status_code == 302:
            print("✅ Server is accessible (redirected to login)")
            return True
        else:
            print(f"⚠️  Server returned status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server")
        print("💡 Make sure to run: poetry run python manage.py runserver")
        return False
    except Exception as e:
        print(f"❌ Server test failed: {e}")
        return False

def main():
    """Main test function with comprehensive UX validation."""
    
    print("🚀 UI Improvements Validation Suite")
    print("=" * 60)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all tests
    tests = [
        ("Template Filter Improvements", test_template_filter_improvements),
        ("Date Humanization", test_date_humanization),
        ("Document Reference Cleaning", test_document_reference_cleaning),
        ("CSS Improvements", test_css_improvements),
        ("Server Accessibility", test_server_accessibility),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ UI improvements are working correctly")
        print("✅ Ready for manual testing in browser")
        print("\n🌐 Next Steps:")
        print("1. Open http://127.0.0.1:8000/search/ in your browser")
        print("2. Search for: 'whats latest on curana?'")
        print("3. Verify the professional formatting and readability")
    else:
        print(f"\n⚠️  {total - passed} tests failed")
        print("💡 Review the failed tests above and fix the issues")

if __name__ == "__main__":
    main()
