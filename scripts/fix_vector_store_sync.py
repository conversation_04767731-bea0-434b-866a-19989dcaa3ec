#!/usr/bin/env python
"""
Production-grade script to clean all data and run fresh ingestion.
Ensures complete data integrity across all systems.
"""

import os
import sys
import django
from typing import List, Dict, Any
import logging

# Setup Django
sys.path.insert(0, '.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

from apps.documents.models import DocumentChunk, EmbeddingMetadata, RawDocument
from apps.search.models import SearchQuery, SearchResult, ResultCitation
from apps.accounts.models import Tenant
from apps.core.utils.collection_manager import get_qdrant_client, get_collection_name
from apps.documents.services.llama_ingestion_service_unified import LlamaIngestionServiceUnified

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def clean_all_data(tenant_slug: str, confirm: bool = False) -> None:
    """
    Rebuild vector store to match database state.
    
    Args:
        tenant_slug: Tenant slug to rebuild for
        dry_run: If True, only analyze without making changes
    """
    logger.info(f"🔧 Starting vector store rebuild for tenant: {tenant_slug}")
    
    # Get tenant
    try:
        tenant = Tenant.objects.get(slug=tenant_slug)
    except Tenant.DoesNotExist:
        logger.error(f"❌ Tenant {tenant_slug} not found")
        return
    
    # Get database chunks with embeddings
    chunks_with_embeddings = DocumentChunk.objects.filter(
        tenant=tenant,
        embedding__isnull=False
    ).select_related('embedding', 'document').order_by('id')
    
    logger.info(f"📊 Found {chunks_with_embeddings.count()} chunks with embeddings in database")
    
    if dry_run:
        logger.info("🔍 DRY RUN - Analyzing data without making changes")
        
        # Check vector store state
        client = get_qdrant_client()
        collection_name = get_collection_name(tenant_slug, intent='default')
        
        try:
            collection_info = client.get_collection(collection_name)
            vector_points = collection_info.points_count
            logger.info(f"📊 Vector store has {vector_points} points")
            logger.info(f"📊 Database has {chunks_with_embeddings.count()} chunks")
            logger.info(f"📊 Difference: {vector_points - chunks_with_embeddings.count()} points")
            
            # Sample a few chunks to check consistency
            sample_chunks = chunks_with_embeddings[:5]
            logger.info(f"🔍 Checking sample chunks for consistency:")
            
            for chunk in sample_chunks:
                vector_id = chunk.embedding.vector_id
                
                # Check if this vector_id exists in vector store
                try:
                    points = client.retrieve(
                        collection_name=collection_name,
                        ids=[vector_id],
                        with_payload=True
                    )
                    
                    if points:
                        point = points[0]
                        payload = point.payload or {}
                        text_in_vs = payload.get('text', payload.get('_node_content', ''))
                        
                        logger.info(f"   ✅ Chunk {chunk.id} -> Vector {vector_id}: Found in VS")
                        logger.info(f"      DB text: {chunk.text[:50]}...")
                        logger.info(f"      VS text: {str(text_in_vs)[:50]}...")
                    else:
                        logger.info(f"   ❌ Chunk {chunk.id} -> Vector {vector_id}: NOT found in VS")
                        
                except Exception as e:
                    logger.info(f"   ❌ Chunk {chunk.id} -> Vector {vector_id}: Error - {str(e)}")
            
        except Exception as e:
            logger.error(f"❌ Error accessing vector store: {str(e)}")
        
        logger.info("🔍 DRY RUN complete. Use dry_run=False to apply fixes.")
        return
    
    # PRODUCTION REBUILD
    logger.info("🚀 Starting PRODUCTION rebuild of vector store")
    
    # Clear and rebuild vector store
    client = get_qdrant_client()
    collection_name = get_collection_name(tenant_slug, intent='default')
    
    try:
        # Delete existing collection
        logger.info(f"🗑️ Deleting existing collection: {collection_name}")
        client.delete_collection(collection_name)
        
        # Recreate collection
        from apps.core.utils.embedding_consistency import get_embedding_dimensions
        dimensions = get_embedding_dimensions()
        
        logger.info(f"🔧 Creating new collection with {dimensions} dimensions")
        client.create_collection(
            collection_name=collection_name,
            vectors_config={
                "size": dimensions,
                "distance": "Cosine"
            }
        )
        
        # Get embedding model
        embed_model = get_consistent_embedding_model()
        
        # Create vector store
        vector_store = QdrantVectorStore(
            client=client,
            collection_name=collection_name,
            embed_model=embed_model
        )
        
        # Process chunks in batches
        batch_size = 50
        total_chunks = chunks_with_embeddings.count()
        processed = 0
        
        logger.info(f"📝 Processing {total_chunks} chunks in batches of {batch_size}")
        
        for i in range(0, total_chunks, batch_size):
            batch = chunks_with_embeddings[i:i + batch_size]
            nodes = []
            
            for chunk in batch:
                # Create TextNode with proper metadata
                node = TextNode(
                    id_=chunk.embedding.vector_id,
                    text=chunk.text,
                    metadata={
                        "chunk_id": str(chunk.id),
                        "document_id": str(chunk.document.id),
                        "tenant_id": str(chunk.tenant.id),
                        "source_type": chunk.document.source.source_type,
                        "content_type": chunk.metadata.get("content_type", "text"),
                        **chunk.metadata
                    }
                )

                # Generate embedding for the node
                try:
                    embedding = embed_model.get_text_embedding(chunk.text)
                    node.embedding = embedding
                except Exception as e:
                    logger.error(f"❌ Error generating embedding for chunk {chunk.id}: {str(e)}")
                    continue

                nodes.append(node)
            
            # Add batch to vector store
            try:
                vector_store.add(nodes)
                processed += len(batch)
                logger.info(f"✅ Processed batch {i//batch_size + 1}: {processed}/{total_chunks} chunks")
                
            except Exception as e:
                logger.error(f"❌ Error processing batch {i//batch_size + 1}: {str(e)}")
                continue
        
        logger.info(f"🎉 Successfully rebuilt vector store with {processed} chunks")
        
        # Verify the rebuild
        collection_info = client.get_collection(collection_name)
        final_points = collection_info.points_count
        logger.info(f"✅ Final verification: {final_points} points in vector store")
        
        if final_points == processed:
            logger.info("🎉 Vector store rebuild completed successfully!")
        else:
            logger.warning(f"⚠️ Point count mismatch: expected {processed}, got {final_points}")
            
    except Exception as e:
        logger.error(f"❌ Critical error during rebuild: {str(e)}")
        raise


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Fix vector store synchronization")
    parser.add_argument("--tenant", required=True, help="Tenant slug")
    parser.add_argument("--dry-run", action="store_true", help="Analyze without making changes")
    
    args = parser.parse_args()
    
    rebuild_vector_store_for_tenant(args.tenant, dry_run=args.dry_run)
