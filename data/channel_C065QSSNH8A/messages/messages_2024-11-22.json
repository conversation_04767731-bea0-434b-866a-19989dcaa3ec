{"date": "2024-11-22", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1732290819.638489", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> salary changes, keeps failing for column but i can't figure out why", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732290819.638489", "reply_count": 7, "files": [{"id": "F081WMA2DHC", "created": 1732290759, "timestamp": 1732290759, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 133185, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F081WMA2DHC/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F081WMA2DHC/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F081WMA2DHC-7b94cff89f/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F081WMA2DHC-7b94cff89f/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F081WMA2DHC-7b94cff89f/image_360.png", "thumb_360_w": 360, "thumb_360_h": 181, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F081WMA2DHC-7b94cff89f/image_480.png", "thumb_480_w": 480, "thumb_480_h": 241, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F081WMA2DHC-7b94cff89f/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F081WMA2DHC-7b94cff89f/image_720.png", "thumb_720_w": 720, "thumb_720_h": 362, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F081WMA2DHC-7b94cff89f/image_800.png", "thumb_800_w": 800, "thumb_800_h": 402, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F081WMA2DHC-7b94cff89f/image_960.png", "thumb_960_w": 960, "thumb_960_h": 483, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F081WMA2DHC-7b94cff89f/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 515, "original_w": 1898, "original_h": 954, "thumb_tiny": "AwAYADC8EUqCTS+Wvr+tOUZQfSl20AN8tfU1DVgCq350ALRRSY+tAEgkIAGBS+afQVHRQBJ5p9BUdFFABRRRQB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F081WMA2DHC/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F081WMA2DHC-5bae18a79c", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F0825R4PCR2", "created": 1732290817, "timestamp": 1732290817, "name": "DGK PT Adjustments1.csv", "title": "DGK PT Adjustments1.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 947, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F0825R4PCR2/dgk_pt_adjustments1.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F0825R4PCR2/download/dgk_pt_adjustments1.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0825R4PCR2/dgk_pt_adjustments1.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F0825R4PCR2-962f025220", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F0825R4PCR2/dgk_pt_adjustments1.csv/edit", "preview": "Employee Id,Employee Name (Read Only),Annual Salary Currency,Annual Salary (Currency),Annual Salary-Other (Currency),Variable Pay Currency,Variable Pay (%),Variable Pay (Currency),Target Bonus Currency,Target Bonus (%),Target Bonus (Currency),Last Raise Date,Previous Year Salary,Annual Salary OTE (Currency),Pay Mix,Hourly Rate,\"Update Type (NC,A,D,U)\"\r425,<PERSON>,USD,231600,0,USD,0,0,USD,0,0,,174720,0,0,0,U\r425A,<PERSON>-<PERSON>,USD,40800,0,USD,0,0,USD,0,0,,0,0,0,0,U\r850,<PERSON>,USD,178800,0,USD,0,0,USD,0,0,,166200,0,0,0,U\r850A,<PERSON>-A,USD,10800,0,USD,0,0,USD,0,0,,8400,0,0,0,U\r218,<PERSON>,<PERSON>,252600,0,USD,0,0,USD,0,0,,212760,0,0,0,<PERSON>\r218<PERSON>,<PERSON>-<PERSON>,USD,50400,0,USD,0,0,USD,0,0,,43200,0,0,0,U\r541,<PERSON><PERSON>,USD,210000,0,USD,0,0,<PERSON>,0,0,,150240,0,0,0,U\r541A,Lucie Fougner-A,USD,14400,0,USD,0,0,USD,0,0,,4800,0,0,0,U\r,,,,,,,,,,,,,,,,\r,,,,,,,,,,,,,,,,\r,,,,,,,,,,,,,,,,\r,,,,,,,,,,,,,,,,\r,,,,,,,,,,,,,,,,", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Employee Id</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary (Currency)</div><div class=\"cm-col\">Annual Salary-Other (Currency)</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay (Currency)</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus (Currency)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE (Currency)</div><div class=\"cm-col\">Pay Mix</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Update Type (NC,A,D,U)</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">425</div><div class=\"cm-col\">Andrew Scott</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">231600</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">174720</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">425A</div><div class=\"cm-col\">Andrew Scott-A</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">40800</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">850</div><div class=\"cm-col\">Laura Basualdo</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">178800</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">166200</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">850A</div><div class=\"cm-col\">Laura Basualdo-A</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10800</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">8400</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">218</div><div class=\"cm-col\">Laurie Johnston</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">252600</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">212760</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">218A</div><div class=\"cm-col\">Laurie Johnston-A</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">50400</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">43200</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">541</div><div class=\"cm-col\">Lucie Fougner</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">210000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">150240</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">541A</div><div class=\"cm-col\">Lucie Fougner-A</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">14400</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">4800</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 1, "lines_more": 0, "preview_is_truncated": false, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "4y1gN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> salary changes, keeps failing for column but i can't figure out why"}]}]}]}, {"ts": "1732289950.847239", "text": "<@U07M6QKHUC9> I believe you are working on putting requirements for\n• Column configuration\n• Allocation page improvments\nIf Yes, Whats the ETA ? I can plan the eng priorities accordingly.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1732289950.847239", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "ZiWCx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I believe you are working on putting requirements for\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Column configuration"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Allocation page improvments"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "If Yes, Whats the ETA ? I can plan the eng priorities accordingly."}]}]}]}, {"ts": "1732272406.074499", "text": "<@U07EJ2LP44S> Is there a different sheet than the ones we have, for the diversified data upload?\nI have the following references:\n<https://stride-hr.slack.com/archives/C065QSSNH8A/p1732121530423699>\n<https://stride-hr.slack.com/archives/C065QSSNH8A/p1731435999130129>\n<https://stride-hr.slack.com/archives/C065QSSNH8A/p1731531429322849>\n<https://stride-hr.slack.com/archives/C065QSSNH8A/p1731082965879109>", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1732272406.074499", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "jhkGR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Is there a different sheet than the ones we have, for the diversified data upload?\nI have the following references:\n"}, {"type": "link", "url": "https://stride-hr.slack.com/archives/C065QSSNH8A/p1732121530423699"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://stride-hr.slack.com/archives/C065QSSNH8A/p1731435999130129"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://stride-hr.slack.com/archives/C065QSSNH8A/p1731531429322849"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://stride-hr.slack.com/archives/C065QSSNH8A/p1731082965879109"}]}]}]}, {"ts": "1732252378.901619", "text": "<@U07M6QKHUC9> We already have `Position_In_Band_Percent` column in the report. I am not sure how the search missed this.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1732209645.355079", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "NcLhp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " We already have "}, {"type": "text", "text": "Position_In_Band_Percent", "style": {"code": true}}, {"type": "text", "text": " column in the report. I am not sure how the search missed this."}]}]}]}, {"ts": "1732229593.026469", "text": "I acknowledge that these bugs are likely at least in part due to <PERSON><PERSON><PERSON><PERSON><PERSON> asking for daily changes to their data, but we have another bug here with charts: <https://compiify.atlassian.net/browse/COM-4001>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732229593.026469", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14084::05461ba1eaa341dc9a83d1fbc7ebe04c", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4001?atlOrigin=eyJpIjoiM2JhOGIzYjI2ZjRkNDY5ZjgwMmFhNDVkYzM5MjNlMDAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4001 Duplicate Engineering Department in Graphs>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14084::d9a5a26079f84fdba5e14f098f12f31c", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14084\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4001\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4001", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "jDtQA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I acknowledge that these bugs are likely at least in part due to <PERSON><PERSON><PERSON><PERSON><PERSON> asking for daily changes to their data, but we have another bug here with charts: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4001"}]}]}]}, {"ts": "1732223231.559549", "text": "A brief summary of my call with Diversified: <https://www.loom.com/share/cc1ec27e9361443a872daedab4cef8fc?sid=34355eed-4788-4204-ac81-6fb75db590da>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732223231.559549", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "UfcUJ", "video_url": "https://www.loom.com/embed/cc1ec27e9361443a872daedab4cef8fc?sid=34355eed-4788-4204-ac81-6fb75db590da&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/cc1ec27e9361443a872daedab4cef8fc-0d03f9694f2b1ce3-4x3.jpg", "alt_text": "Budgets and Performance Review 📊", "title": {"type": "plain_text", "text": "Budgets and Performance Review 📊", "emoji": true}, "title_url": "https://www.loom.com/share/cc1ec27e9361443a872daedab4cef8fc", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 2 min  ", "emoji": true}}, {"type": "section", "block_id": "05KBq", "text": {"type": "mrkdwn", "text": ":information_source: I discussed feedback from Diversified regarding budget allocation and performance review. They want budgets set in the cycle builder based on...", "verbatim": false}}, {"type": "actions", "block_id": "XHPUK", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/cc1ec27e9361443a872daedab4cef8fc?sid=34355eed-4788-4204-ac81-6fb75db590da"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"cc1ec27e9361443a872daedab4cef8fc\",\"videoName\":\"Budgets and Performance Review 📊\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/cc1ec27e9361443a872daedab4cef8fc?sid=34355eed-4788-4204-ac81-6fb75db590da", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "OL5CG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "A brief summary of my call with Diversified: "}, {"type": "link", "url": "https://www.loom.com/share/cc1ec27e9361443a872daedab4cef8fc?sid=34355eed-4788-4204-ac81-6fb75db590da"}]}]}]}, {"ts": "1732213986.492409", "text": "Valgenesis HRBP call link: <https://app.fireflies.ai/view/Stride-Valgenesis-Weekly::QfleMXLtoegCvL99>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732213986.492409", "reply_count": 4, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}], "attachments": [{"from_url": "https://app.fireflies.ai/view/Stride-Valgenesis-Weekly::QfleMXLtoegCvL99", "thumb_url": "https://files.fireflies.ai/app/img/preview.png", "thumb_width": 1280, "thumb_height": 720, "image_url": "https://files.fireflies.ai/app/img/preview.png", "image_width": 1280, "image_height": 720, "image_bytes": 188965, "service_icon": "https://app.fireflies.ai/favicon.ico", "id": 1, "original_url": "https://app.fireflies.ai/view/Stride-Valgenesis-Weekly::QfleMXLtoegCvL99", "fallback": "Stride/Valgenesis Weekly - Meeting recording by Fireflies.ai", "text": "**HRBP Visibility Improvement:** Expand HRBP visibility to employee level for better support and insight on regional assignments. **System Functionality Challenges:*...", "title": "Stride/Valgenesis Weekly - Meeting recording by Fireflies.ai", "title_link": "https://app.fireflies.ai/view/Stride-Valgenesis-Weekly::QfleMXLtoegCvL99", "service_name": "app.fireflies.ai"}], "blocks": [{"type": "rich_text", "block_id": "q8Frx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis HRBP call link: "}, {"type": "link", "url": "https://app.fireflies.ai/view/Stride-Valgenesis-Weekly::QfleMXLtoegCvL99"}]}]}]}], "created_at": "2025-05-22T21:35:34.685452"}