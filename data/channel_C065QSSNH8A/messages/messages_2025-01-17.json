{"date": "2025-01-17", "channel_id": "C065QSSNH8A", "message_count": 28, "messages": [{"ts": "1737125432.915879", "text": "<@U07EJ2LP44S> Curana data updated. updates:\n• Cycle deleted and new one created\n• Copied salaries from old data and re-uploaded bands ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737123368.746899", "subtype": "thread_broadcast", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}, {"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "OWomm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Curana data updated. updates:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle deleted and new one created"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Copied salaries from old data and re-uploaded bands "}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1737124858.352759", "text": "ok. will get it addressed on Monday", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/AvvE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok. will get it addressed on Monday"}]}]}]}, {"ts": "1737124767.382929", "text": "not the highest priority but that was her feedback", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rbvJd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "not the highest priority but that was her feedback"}]}]}]}, {"ts": "1737124751.720839", "text": "also fyi, not sure if you were there when she said it, but she said the percentages not being aligned looked 'unprofessional'", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737124751.720839", "reply_count": 1, "files": [{"id": "F08956C645R", "created": 1737124749, "timestamp": 1737124749, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 74229, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08956C645R/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08956C645R/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08956C645R-966ff4101e/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08956C645R-966ff4101e/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08956C645R-966ff4101e/image_360.png", "thumb_360_w": 360, "thumb_360_h": 352, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08956C645R-966ff4101e/image_480.png", "thumb_480_w": 480, "thumb_480_h": 469, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08956C645R-966ff4101e/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08956C645R-966ff4101e/image_720.png", "thumb_720_w": 720, "thumb_720_h": 704, "original_w": 786, "original_h": 768, "thumb_tiny": "AwAuADDSJGeT+tJx6/rSkgUZGO/5UAIACcgnj3oJGeT+tKOaMj3/ACoATjHX9aBjkgnn3pcj3/KloAQnHY0Z9jQfpmjnHSgABzRn2NKPpik59KADPsaM0c+lL26UAIc9qOfQfnQc9sUc+1AAM96OfQfnQCe9HPbFABz6D86KOfajPrQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08956C645R/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08956C645R-434721a1c6", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "rZW1Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "also fyi, not sure if you were there when she said it, but she said the percentages not being aligned looked 'unprofessional'"}]}]}]}, {"ts": "**********.639009", "text": "thankyou! let me know when done", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gkmuk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "thankyou! let me know when done"}]}]}]}, {"ts": "**********.756669", "text": "<@U07EJ2LP44S> this issue was noticed and being fixed", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.756669", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "kq9yB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " this issue was noticed and being fixed"}]}]}]}, {"ts": "1737124298.257099", "text": "<@U0690EB5JE5> one of the things i was having trouble with in diversified was changing the employee's indiviual performance to 60%. can you help with that? i was able to change the company performance amount to 40%, but no matter how/what i upload, individual performance stays at 100%.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9u0Bz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " one of the things i was having trouble with in diversified was changing the employee's indiviual performance to 60%. can you help with that? i was able to change the company performance amount to 40%, but no matter how/what i upload, individual performance stays at 100%."}]}]}]}, {"ts": "1737123768.929569", "text": "Ok thank you! I'll get diversified re-setup", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "v28xy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok thank you! I'll get diversified re-setup"}]}]}]}, {"ts": "1737123533.485999", "text": "I am updating curana meanwhile.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "N5sPn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am updating curana meanwhile."}]}]}]}, {"ts": "1737123368.746899", "text": "<@U07EJ2LP44S> Diversified updated. issues fixed\n• Root employee added\n• Delete old cycle and created a new one\n• merit export filter issue is fixed. It was issue only with \"manager\" filter.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737123368.746899", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "Jy+LW", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Diversified updated. issues fixed\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Root employee added"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Delete old cycle and created a new one"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "merit export filter issue is fixed. It was issue only with \"manager\" filter."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1737121885.302309", "text": "Ok, Will get it ready on Monday", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BXj11", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok, Will get it ready on Monday"}]}]}]}, {"ts": "1737121867.868769", "text": "so monday is probably fine", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "so monday is probably fine"}]}]}]}, {"ts": "1737121863.008889", "text": "valgenesis asked for it back for trainign - but it also needs the new build", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "td5nB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "valgenesis asked for it back for trainign - but it also needs the new build"}]}]}]}, {"ts": "1737121840.978649", "text": "Thats not active. Do you need it today?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QVkUA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thats not active. Do you need it today?"}]}]}]}, {"ts": "1737121839.380849", "text": "I think we're going to need 3 of them - one for diversified, one for valgenesis, and one for curana", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Tuaw6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think we're going to need 3 of them - one for diversified, one for valgenesis, and one for curana"}]}]}]}, {"ts": "1737121817.222299", "text": "Also, I can no longer get into <http://stridedemo.stridehr.io|stridedemo.stridehr.io>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737121817.222299", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "c33LZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also, I can no longer get into "}, {"type": "link", "url": "http://stridedemo.stridehr.io", "text": "stridedemo.stridehr.io"}]}]}]}, {"ts": "1737121720.022359", "text": ":beach_with_umbrella: Good Evening :raised_hands:", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "KPoV+", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "beach_with_umbrella", "unicode": "1f3d6-fe0f"}, {"type": "text", "text": " Good Evening "}, {"type": "emoji", "name": "raised_hands", "unicode": "1f64c"}]}]}]}, {"ts": "1737121683.053829", "text": "i came to the beach today", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/C8Ts", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i came to the beach today"}]}]}]}, {"ts": "1737121667.275679", "text": "good evening btw", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "UEG+C", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "good evening btw"}]}]}]}, {"ts": "1737121609.524179", "text": "yes", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IB8ze", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes"}]}]}]}, {"ts": "1737121598.467409", "text": "<@U0690EB5JE5> ok - are you fixing the root employee thing for diversified? the test cycle also needs to be deleted", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RN3Kv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " ok - are you fixing the root employee thing for diversified? the test cycle also needs to be deleted"}]}]}]}, {"ts": "1737121332.740969", "text": "For Curana and Diven", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7tOTG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For Curana and Diven"}]}]}]}, {"ts": "1737121311.013779", "text": "Please hold any data uploads for an hour", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Y9sJf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please hold any data uploads for an hour"}]}]}]}, {"ts": "1737121297.923059", "text": "<@U07EJ2LP44S> I am fixing data. Will update ENV in an hour ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ITU54", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am fixing data. Will update ENV in an hour "}]}]}]}, {"ts": "1737081915.496799", "text": "Please feel free to go ahead and configure performance ratings from settings page.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "AoCum", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please feel free to go ahead and configure performance ratings from settings page."}]}]}]}, {"ts": "1737081886.337709", "text": "<@U07EJ2LP44S> reg. This <https://compiify.atlassian.net/browse/COM-4024>\nWe don't support matrix support by \"Range Penetration\" but only perf rating. And we didn't decide on implementing this earlier and Also I don't know what is \"Range Penetration\" is. I believe it is different from compa ratio.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737081886.337709", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "668yP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " reg. This "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4024"}, {"type": "text", "text": "\nWe don't support matrix support by \"Range Penetration\" but only perf rating. And we didn't decide on implementing this earlier and Also I don't know what is \"Range Penetration\" is. I believe it is different from compa ratio."}]}]}]}, {"ts": "1737080517.264809", "text": "<@U06HN8XDC5A> Please add hireDate to merit view.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737047980.786159", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "2DOOn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " Please add hireDate to merit view."}]}]}]}, {"ts": "1737066367.948479", "text": "<@U0690EB5JE5> diversified has escalated the sso issue to me. Can you pla address this?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737066367.948479", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "tEfWv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " diversified has escalated the sso issue to me. Can you pla address this?"}]}]}]}], "created_at": "2025-05-22T21:35:34.690398"}