#!/usr/bin/env python3
"""
Test script to verify the UI formatting is working in the live application.
"""

import requests
import time
from bs4 import BeautifulSoup

def test_search_formatting():
    """Test the search UI formatting by making a real request."""
    
    print("🌐 Testing Live UI Formatting")
    print("=" * 50)
    
    # Test search endpoint
    base_url = "http://127.0.0.1:8000"
    
    try:
        # First, get the search page to get CSRF token
        session = requests.Session()
        search_page = session.get(f"{base_url}/search/")
        
        if search_page.status_code == 302:
            # Redirected to login, let's try to login first
            login_url = search_page.headers.get('Location')
            if login_url:
                print(f"🔐 Redirected to login: {login_url}")
                print("💡 You need to be logged in to test the search functionality")
                print("📝 Please:")
                print("   1. Open http://127.0.0.1:8000/search/ in your browser")
                print("   2. Log in with your credentials")
                print("   3. Search for 'whats latest on curana?'")
                print("   4. Check if the response has:")
                print("      ✅ Formatted headers (## Latest Updates)")
                print("      ✅ Bulleted lists with proper styling")
                print("      ✅ Bold dates and names")
                print("      ✅ Clean typography and spacing")
                return False
        
        print("✅ Search page accessible")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Django server")
        print("💡 Make sure the server is running with: poetry run python manage.py runserver")
        return False
    except Exception as e:
        print(f"❌ Error testing search: {e}")
        return False

def check_markdown_in_template():
    """Check if the template is correctly set up for markdown."""
    
    print("\n📄 Checking Template Configuration")
    print("=" * 50)
    
    template_path = "/Users/<USER>/Desktop/RAGSearch/multi_source_rag/apps/search/templates/search/search_results.html"
    
    try:
        with open(template_path, 'r') as f:
            content = f.read()
        
        checks = [
            ("{% load search_extras %}", "Template loads search_extras"),
            ("{{ response|markdown_to_html }}", "Uses markdown_to_html filter"),
            ("response-heading", "Has styling classes for headers"),
            ("response-list", "Has styling classes for lists"),
        ]
        
        all_passed = True
        for check, description in checks:
            if check in content:
                print(f"✅ {description}: Found")
            else:
                print(f"❌ {description}: Missing")
                all_passed = False
        
        return all_passed
        
    except FileNotFoundError:
        print(f"❌ Template file not found: {template_path}")
        return False

def main():
    """Main test function."""
    
    print("🚀 Live UI Formatting Test")
    print("=" * 50)
    
    # Check template configuration
    template_ok = check_markdown_in_template()
    
    # Test live search
    search_ok = test_search_formatting()
    
    print("\n📊 Test Results")
    print("=" * 50)
    
    if template_ok and search_ok:
        print("🎉 SETUP COMPLETE!")
        print("✅ Template is configured correctly")
        print("✅ Server is accessible")
        print()
        print("🌐 Manual Testing Steps:")
        print("1. Open http://127.0.0.1:8000/search/ in your browser")
        print("2. Log in if prompted")
        print("3. Search for: 'whats latest on curana?'")
        print("4. Verify the response shows:")
        print("   📋 Structured headers (## Latest Updates)")
        print("   📝 Formatted lists with bullets")
        print("   💪 Bold dates and names")
        print("   🎨 Professional typography")
        print()
        print("🔧 If formatting still doesn't work:")
        print("- Check browser console for JavaScript errors")
        print("- Verify the markdown library is loaded")
        print("- Clear browser cache and refresh")
        
    elif template_ok:
        print("⚠️  Template is OK but server connection failed")
        print("💡 Make sure Django server is running with Poetry")
        
    else:
        print("❌ Template configuration issues found")
        print("💡 Check the template file and filter implementation")

if __name__ == "__main__":
    main()
