{"date": "2024-06-27", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1719447674.802799", "text": "'", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MzIum", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "'"}]}]}]}, {"ts": "1719446412.051649", "text": "<PERSON> (CEO) impersonating as <PERSON><PERSON>, a manager who reports to <PERSON>. When <PERSON> submit a comments on behalf of <PERSON><PERSON>, the comment box is showing <PERSON><PERSON> as comment submitted. I'd think the submitted name should show as \"<PERSON> on behalf of <PERSON><PERSON>\". Thoughts?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719446412.051649", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "YDnTd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> (CEO) impersonating as <PERSON><PERSON>, a manager who reports to <PERSON>. When <PERSON> submit a comments on behalf of <PERSON><PERSON>, the comment box is showing <PERSON><PERSON> as comment submitted. I'd think the submitted name should show as \"<PERSON> on behalf of <PERSON><PERSON>\". Thoughts?"}]}]}]}, {"ts": "1719446173.965319", "text": "<@U065H3M6WJV> <PERSON> (CEO) impersonating as <PERSON><PERSON>, a manager who reports to <PERSON>. <PERSON> is seeing the the 4 charts in org view for all departments and levels. I assume <PERSON> should not see these 4 charts for the whole org but only for her direct reports. Is that correct?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719446173.965319", "reply_count": 2, "files": [{"id": "F07ABBRTZ41", "created": 1719446118, "timestamp": 1719446118, "name": "Screenshot 2024-06-26 at 4.52.56 PM.png", "title": "Screenshot 2024-06-26 at 4.52.56 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 411357, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07ABBRTZ41/screenshot_2024-06-26_at_4.52.56___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07ABBRTZ41/download/screenshot_2024-06-26_at_4.52.56___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 189, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 252, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 379, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 421, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 505, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 539, "original_w": 3282, "original_h": 1726, "thumb_tiny": "AwAZADDRPOOoxzTQgxjJ6YpzEjbjucUuRjORTAb5fOcnqDQygkc4xTgwJIHamvKqHBznGelACbBjG49AKds5zk9c1C87c7AAOMZqRJQxIPBzgc9aNQuPowPSjuKD2pAHHpRx6UHrR2oAMD0owPSkal70Af/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07ABBRTZ41/screenshot_2024-06-26_at_4.52.56___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07ABBRTZ41-09dfa2513c", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "vX1Q7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " <PERSON> (CEO) impersonating as <PERSON><PERSON>, a manager who reports to <PERSON>. <PERSON> is seeing the the 4 charts in org view for all departments and levels. I assume <PERSON> should not see these 4 charts for the whole org but only for her direct reports. Is that correct?"}]}]}]}, {"ts": "1719440020.580269", "text": "Cycle builder in `new-meritview` is now failing on the Bonus award section, may block some testing.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1719440020.580269", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "gr+yM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle builder in "}, {"type": "text", "text": "new-meritview", "style": {"code": true}}, {"type": "text", "text": " is now failing on the Bonus award section, may block some testing."}]}]}]}, {"ts": "1719434956.080919", "text": "<@U065H3M6WJV> can you please confirm  expected behavior for following scenario if for an employee who is  in submitted state\n1. salary in local currency is modified \n2. performance rating recommendation is modified ( no flag raised)\n3. performance rating recommendation is modified and new recommendation raises a flag\n4. currency exchange rate is modified \n5. performance rating is modified", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1719434956.080919", "reply_count": 14, "edited": {"user": "U04DKEFP1K8", "ts": "1719435039.000000"}, "blocks": [{"type": "rich_text", "block_id": "XNW0J", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " can you please confirm  expected behavior for following scenario if for an employee who is  in submitted state\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "salary in local currency is modified "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "performance rating recommendation is modified ( no flag raised)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "performance rating recommendation is modified and new recommendation raises a flag"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "currency exchange rate is modified "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "performance rating is modified"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1719431642.287849", "text": "<@U065H3M6WJV> I am not doing any new testing while you are here so please go ahead and verify those.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "QE3ZM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I am not doing any new testing while you are here so please go ahead and verify those."}]}]}]}, {"ts": "1719428535.231639", "text": "For the tickets that were moved to \"In QA\" today, did you want me to try to verify those? <@U0690EB5JE5>\n\nIt might cause disruptions to other entries/statuses if <@U04DS2MBWP4> is also actively testing, so just want to make sure that's not gonna be an issue.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VqkfC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For the tickets that were moved to \"In QA\" today, did you want me to try to verify those? "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "\n\nIt might cause disruptions to other entries/statuses if "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " is also actively testing, so just want to make sure that's not gonna be an issue."}]}]}]}], "created_at": "2025-05-22T21:35:34.623056"}