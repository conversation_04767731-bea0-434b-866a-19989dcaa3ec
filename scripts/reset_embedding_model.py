#!/usr/bin/env python
"""
Reset the embedding model to force reinitialization.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from apps.core.utils.embedding_consistency import reset_embedding_model, get_embedding_model_info
from apps.core.utils.service_cache import clear_service_cache

def reset_all_embedding_models():
    """Reset all embedding models and caches."""
    print("🔄 Resetting All Embedding Models and Caches")
    print("=" * 60)
    
    # Reset embedding model
    print("🔄 Resetting embedding model...")
    reset_embedding_model()
    print("✅ Embedding model reset")
    
    # Clear service cache
    print("🔄 Clearing service cache...")
    clear_service_cache()
    print("✅ Service cache cleared")
    
    # Check current configuration
    print("\n📊 Current embedding model configuration:")
    model_info = get_embedding_model_info()
    for key, value in model_info.items():
        print(f"   {key}: {value}")
    
    print("\n🎉 All models and caches reset successfully!")
    print("Please restart the Django server to see the changes.")

if __name__ == "__main__":
    reset_all_embedding_models()
