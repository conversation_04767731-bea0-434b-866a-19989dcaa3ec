{"date": "2024-02-20", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1708450260.764309", "text": "FYI <@U0690EB5JE5>, here are the JIRA Epics for some of the upcoming projects:\n• <https://compiify.atlassian.net/browse/COM-2337|Customer Onboarding/Enablement (COM-2337)>\n• <https://compiify.atlassian.net/browse/COM-2338|Analytics &amp; Insights (COM-2338)>\n• <https://compiify.atlassian.net/browse/COM-2339|Total Rewards (COM-2339)>\n• <https://compiify.atlassian.net/browse/COM-2340|Salary Bands V2 (COM-2340)>", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2Wh49", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "FYI "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": ", here are the JIRA Epics for some of the upcoming projects:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2337", "text": "Customer Onboarding/Enablement (COM-2337)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2338", "text": "Analytics & Insights (COM-2338)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2339", "text": "Total Rewards (COM-2339)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2340", "text": "Salary Bands V2 (COM-2340)"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1708396328.056409", "text": "<@U04DKEFP1K8> Eng priorities for next day are <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|on the board> - mainly pulling in the remainder of <https://compiify.atlassian.net/browse/COM-2087|Wave 3> and have moved some tickets to the top of <https://compiify.atlassian.net/browse/COM-2088|Wave 4> for anyone who doesn't have enough assigned.", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "mcFkj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "on the board"}, {"type": "text", "text": " - mainly pulling in the remainder of "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " and have moved some tickets to the top of "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2088", "text": "Wave 4"}, {"type": "text", "text": " for anyone who doesn't have enough assigned."}]}]}]}], "created_at": "2025-05-22T21:35:34.581521"}