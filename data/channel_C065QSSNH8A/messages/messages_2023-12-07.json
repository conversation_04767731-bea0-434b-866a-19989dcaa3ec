{"date": "2023-12-07", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1701971088.617249", "text": "<@U04DKEFP1K8> we need a new domain to send sales emails because we don't want <http://compiify.com|compiify.com> emails to be listed as spam. Can we create domains such as <http://compiifyinc.com|compiifyinc.com>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701971088.617249", "reply_count": 5, "reactions": [{"name": "bulb", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "AgBMo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " we need a new domain to send sales emails because we don't want "}, {"type": "link", "url": "http://compiify.com", "text": "compiify.com"}, {"type": "text", "text": " emails to be listed as spam. Can we create domains such as "}, {"type": "link", "url": "http://compiifyinc.com", "text": "compiifyinc.com"}]}]}]}, {"ts": "1701905084.540399", "text": "I started a <https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=0|draft of UAT test cases> in a new spreadsheet because I'd like us to collaborate on these before sharing them with Digital Asset.\n\nI also created a tab to capture anything I noticed in the current Sandbox environment that could be a concern when the Digital Asset team tries to run their cycle with the existing UI.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701905084.540399", "reply_count": 6, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F068XFR96CD", "created": 1701905087, "timestamp": 1701905087, "name": "UAT test cases (draft)", "title": "UAT test cases (draft)", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU", "external_url": "https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSYkGlprdRTqACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F068XFR96CD/uat_test_cases__draft_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "HfNUT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I started a "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=0", "text": "draft of UAT test cases"}, {"type": "text", "text": " in a new spreadsheet because I'd like us to collaborate on these before sharing them with Digital Asset.\n\nI also created a tab to capture anything I noticed in the current Sandbox environment that could be a concern when the Digital Asset team tries to run their cycle with the existing UI."}]}]}]}], "created_at": "2025-05-22T21:35:34.572151"}