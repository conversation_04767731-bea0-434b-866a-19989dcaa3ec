#!/usr/bin/env python
"""
Clean Database and Reingest Data Script

This script completely cleans the database and vector stores, then performs
fresh ingestion to ensure clean testing environment for the consolidated RAG service.

Usage:
    python scripts/clean_and_reingest.py
"""

import os
import sys
import django
import logging
from datetime import datetime

# Set up Django environment
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "multi_source_rag.config.settings.local")
django.setup()

from django.db import transaction
from apps.accounts.models import Tenant, UserProfile
from apps.documents.models import Document, DocumentChunk, EmbeddingMetadata
from apps.search.models import SearchQuery, SearchResult, ResultCitation
from apps.documents.services.llama_ingestion_service_unified import UnifiedLlamaIndexIngestionService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DatabaseCleaner:
    """Clean database and vector stores for fresh testing."""
    
    def __init__(self, tenant_slug: str = "stride"):
        """Initialize cleaner for specific tenant."""
        self.tenant_slug = tenant_slug
        try:
            self.tenant = Tenant.objects.get(slug=tenant_slug)
            logger.info(f"Using tenant: {self.tenant.name} ({self.tenant.slug})")
        except Tenant.DoesNotExist:
            logger.error(f"Tenant '{tenant_slug}' not found!")
            raise

    def clean_search_data(self):
        """Clean all search-related data."""
        logger.info("🧹 Cleaning search data...")
        
        with transaction.atomic():
            # Delete citations first (foreign key dependency)
            citation_count = ResultCitation.objects.filter(
                result__search_query__tenant=self.tenant
            ).count()
            ResultCitation.objects.filter(
                result__search_query__tenant=self.tenant
            ).delete()
            logger.info(f"   Deleted {citation_count} citations")
            
            # Delete search results
            result_count = SearchResult.objects.filter(
                search_query__tenant=self.tenant
            ).count()
            SearchResult.objects.filter(
                search_query__tenant=self.tenant
            ).delete()
            logger.info(f"   Deleted {result_count} search results")
            
            # Delete search queries
            query_count = SearchQuery.objects.filter(tenant=self.tenant).count()
            SearchQuery.objects.filter(tenant=self.tenant).delete()
            logger.info(f"   Deleted {query_count} search queries")

    def clean_document_data(self):
        """Clean all document-related data."""
        logger.info("🧹 Cleaning document data...")
        
        with transaction.atomic():
            # Delete embedding metadata first
            embedding_count = EmbeddingMetadata.objects.filter(tenant=self.tenant).count()
            EmbeddingMetadata.objects.filter(tenant=self.tenant).delete()
            logger.info(f"   Deleted {embedding_count} embedding metadata records")
            
            # Delete document chunks
            chunk_count = DocumentChunk.objects.filter(tenant=self.tenant).count()
            DocumentChunk.objects.filter(tenant=self.tenant).delete()
            logger.info(f"   Deleted {chunk_count} document chunks")
            
            # Delete documents
            doc_count = Document.objects.filter(tenant=self.tenant).count()
            Document.objects.filter(tenant=self.tenant).delete()
            logger.info(f"   Deleted {doc_count} documents")

    def clean_vector_stores(self):
        """Clean vector store collections."""
        logger.info("🧹 Cleaning vector stores...")
        
        try:
            from apps.core.utils.collection_manager import get_collection_name
            from apps.core.utils.llama_index_vectorstore import get_vector_store
            
            # Clean different collection types
            collection_types = ["conversation", "code", "document", "general"]
            
            for collection_type in collection_types:
                try:
                    collection_name = get_collection_name(self.tenant_slug, intent=collection_type)
                    vector_store = get_vector_store(collection_name=collection_name)
                    
                    # Delete collection if it exists
                    if hasattr(vector_store, 'delete_collection'):
                        vector_store.delete_collection()
                        logger.info(f"   Deleted vector collection: {collection_name}")
                    elif hasattr(vector_store, 'client'):
                        # For Qdrant
                        try:
                            vector_store.client.delete_collection(collection_name)
                            logger.info(f"   Deleted Qdrant collection: {collection_name}")
                        except Exception as e:
                            logger.debug(f"   Collection {collection_name} may not exist: {str(e)}")
                    
                except Exception as e:
                    logger.warning(f"   Could not clean collection {collection_type}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error cleaning vector stores: {str(e)}")

    def perform_full_cleanup(self):
        """Perform complete cleanup of all data."""
        logger.info("🚀 Starting complete database cleanup...")
        logger.info("=" * 60)
        
        # Clean in correct order (respecting foreign key dependencies)
        self.clean_search_data()
        self.clean_document_data()
        self.clean_vector_stores()
        
        logger.info("✅ Complete cleanup finished!")

    def reingest_slack_data(self):
        """Reingest Slack data for testing."""
        logger.info("📥 Starting fresh Slack data ingestion...")
        
        try:
            # Initialize ingestion service
            ingestion_service = UnifiedLlamaIndexIngestionService(tenant_slug=self.tenant_slug)
            
            # Slack configuration
            slack_config = {
                "token": "*********************************************************",
                "channels": [
                    {"id": "C065QSSNH8A", "name": "1-productengineering"},
                    {"id": "C07M2CAS79S", "name": "engineering-issues"},
                    {"id": "C065QSSNH8A", "name": "engineering-team"}  # Using same ID as fallback
                ],
                "max_messages": 100,  # Limit for testing
                "days_back": 30
            }
            
            logger.info(f"Ingesting from {len(slack_config['channels'])} Slack channels...")
            
            # Perform ingestion
            result = ingestion_service.ingest_slack_data(
                token=slack_config["token"],
                channels=slack_config["channels"],
                max_messages=slack_config["max_messages"],
                days_back=slack_config["days_back"]
            )
            
            logger.info(f"✅ Slack ingestion completed:")
            logger.info(f"   Documents: {result.get('documents_created', 0)}")
            logger.info(f"   Chunks: {result.get('chunks_created', 0)}")
            logger.info(f"   Embeddings: {result.get('embeddings_created', 0)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Slack ingestion failed: {str(e)}", exc_info=True)
            return False

    def verify_ingestion(self):
        """Verify that ingestion was successful."""
        logger.info("🔍 Verifying ingestion results...")
        
        # Check document counts
        doc_count = Document.objects.filter(tenant=self.tenant).count()
        chunk_count = DocumentChunk.objects.filter(tenant=self.tenant).count()
        embedding_count = EmbeddingMetadata.objects.filter(tenant=self.tenant).count()
        
        logger.info(f"   Documents: {doc_count}")
        logger.info(f"   Chunks: {chunk_count}")
        logger.info(f"   Embeddings: {embedding_count}")
        
        if doc_count > 0 and chunk_count > 0 and embedding_count > 0:
            logger.info("✅ Ingestion verification successful!")
            return True
        else:
            logger.error("❌ Ingestion verification failed - missing data!")
            return False


def main():
    """Main execution function."""
    try:
        logger.info("🚀 Starting clean database and reingest process...")
        logger.info("=" * 80)
        
        # Initialize cleaner
        cleaner = DatabaseCleaner(tenant_slug="stride")
        
        # Step 1: Clean everything
        cleaner.perform_full_cleanup()
        
        # Step 2: Reingest data
        logger.info("\n" + "=" * 60)
        if not cleaner.reingest_slack_data():
            logger.error("❌ Ingestion failed. Aborting.")
            return 1
        
        # Step 3: Verify ingestion
        logger.info("\n" + "=" * 60)
        if not cleaner.verify_ingestion():
            logger.error("❌ Verification failed. Data may be incomplete.")
            return 1
        
        logger.info("\n" + "=" * 80)
        logger.info("🎉 Clean and reingest process completed successfully!")
        logger.info("🧪 Ready for RAG service testing!")
        logger.info("=" * 80)
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Process failed: {str(e)}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit(main())
