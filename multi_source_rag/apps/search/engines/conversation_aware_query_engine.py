"""
Conversation-Aware Query Engine using LlamaIndex Native Capabilities

This module implements conversation-aware query processing using LlamaIndex's
native query engines with conversation context enhancement.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from llama_index.core.base.response.schema import Response
from llama_index.core.schema import NodeWithScore, QueryBundle
from llama_index.core.query_engine import BaseQueryEngine, RetrieverQueryEngine
from llama_index.core.indices.base import BaseIndex
from llama_index.core.postprocessor import SimilarityPostprocessor, MetadataReplacementPostProcessor
from llama_index.core.response_synthesizers import ResponseMode, get_response_synthesizer
from llama_index.core.retrievers import BaseRetriever
from llama_index.core.query_engine.transform_query_engine import TransformQueryEngine
from llama_index.core.indices.query.query_transform.base import BaseQueryTransform

logger = logging.getLogger(__name__)


class ConversationContextTransform(BaseQueryTransform):
    """Query transform that adds conversation context to queries."""
    
    def __init__(self, conversation_history: Optional[List[str]] = None):
        """
        Initialize conversation context transform.
        
        Args:
            conversation_history: List of previous queries for context
        """
        self.conversation_history = conversation_history or []
        self.context_keywords = {
            'temporal': ['when', 'time', 'date', 'recent', 'latest', 'yesterday', 'today'],
            'participants': ['who', 'user', 'person', 'people', 'team', 'member'],
            'content': ['what', 'about', 'discuss', 'talk', 'mention', 'say'],
            'threads': ['thread', 'reply', 'response', 'follow', 'continue'],
            'reactions': ['reaction', 'emoji', 'like', 'approve', 'agree']
        }
    
    def _run(self, query_bundle: QueryBundle, metadata: Dict[str, Any]) -> QueryBundle:
        """Transform query with conversation context."""
        original_query = query_bundle.query_str
        
        # Add conversation context if available
        enhanced_query = self._enhance_query_with_context(original_query)
        
        # Add metadata filters based on query intent
        enhanced_metadata = self._extract_conversation_filters(enhanced_query)
        
        return QueryBundle(
            query_str=enhanced_query,
            custom_embedding_strs=[enhanced_query],
            embedding=query_bundle.embedding
        )
    
    def _enhance_query_with_context(self, query: str) -> str:
        """Enhance query with conversation context."""
        query_lower = query.lower()
        enhanced_parts = [query]
        
        # Add context from conversation history
        if self.conversation_history:
            recent_context = self._extract_context_from_history()
            if recent_context:
                enhanced_parts.append(f"Context: {recent_context}")
        
        # Add conversation-specific terms based on query intent
        for category, keywords in self.context_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                if category == 'temporal':
                    enhanced_parts.append("conversation timeline discussion")
                elif category == 'participants':
                    enhanced_parts.append("participants users team members")
                elif category == 'threads':
                    enhanced_parts.append("thread replies responses")
                elif category == 'reactions':
                    enhanced_parts.append("reactions engagement feedback")
        
        return " ".join(enhanced_parts)
    
    def _extract_context_from_history(self) -> str:
        """Extract relevant context from conversation history."""
        if not self.conversation_history:
            return ""
        
        # Use last 2 queries for context
        recent_queries = self.conversation_history[-2:]
        
        # Extract key terms from recent queries
        context_terms = []
        for query in recent_queries:
            # Simple keyword extraction
            words = query.lower().split()
            # Filter out common words
            stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
            meaningful_words = [w for w in words if len(w) > 3 and w not in stop_words]
            context_terms.extend(meaningful_words[:3])  # Top 3 terms per query
        
        return " ".join(context_terms[:5])  # Limit total context terms
    
    def _extract_conversation_filters(self, query: str) -> Dict[str, Any]:
        """Extract metadata filters based on query content."""
        filters = {}
        query_lower = query.lower()
        
        # Time-based filters
        if any(term in query_lower for term in ['recent', 'latest', 'today', 'yesterday']):
            # Add recency filter
            filters['recency_boost'] = True
        
        # Conversation type filters
        if any(term in query_lower for term in ['thread', 'discussion']):
            filters['conversation_type'] = ['thread', 'discussion']
        
        if any(term in query_lower for term in ['question', 'answer', 'q&a']):
            filters['conversation_type'] = ['q_and_a']
        
        # Engagement filters
        if any(term in query_lower for term in ['popular', 'active', 'engaged']):
            filters['min_engagement_score'] = 0.5
        
        return filters


class ConversationAwareQueryEngine(BaseQueryEngine):
    """
    LlamaIndex-native conversation-aware query engine.
    
    Enhances standard query engines with conversation context and
    conversation-specific retrieval and synthesis.
    """
    
    def __init__(
        self,
        index: BaseIndex,
        similarity_top_k: int = 10,
        response_mode: str = "tree_summarize",
        conversation_history: Optional[List[str]] = None,
        enable_conversation_context: bool = True,
        **kwargs
    ):
        """
        Initialize conversation-aware query engine.
        
        Args:
            index: Vector index for retrieval
            similarity_top_k: Number of similar documents to retrieve
            response_mode: Response synthesis mode
            conversation_history: Previous queries for context
            enable_conversation_context: Whether to use conversation context
        """
        self.index = index
        self.similarity_top_k = similarity_top_k
        self.conversation_history = conversation_history or []
        self.enable_conversation_context = enable_conversation_context
        
        # Create base retriever
        self.retriever = index.as_retriever(similarity_top_k=similarity_top_k)
        
        # Create post-processors
        self.postprocessors = [
            SimilarityPostprocessor(similarity_cutoff=0.1),
            MetadataReplacementPostProcessor(target_metadata_key="window")
        ]
        
        # Create response synthesizer
        self.response_synthesizer = get_response_synthesizer(
            response_mode=ResponseMode(response_mode),
            use_async=False
        )
        
        # Create base query engine
        self.base_query_engine = RetrieverQueryEngine(
            retriever=self.retriever,
            response_synthesizer=self.response_synthesizer,
            node_postprocessors=self.postprocessors
        )
        
        # Wrap with conversation context if enabled
        if self.enable_conversation_context:
            self.query_engine = TransformQueryEngine(
                self.base_query_engine,
                query_transform=ConversationContextTransform(self.conversation_history)
            )
        else:
            self.query_engine = self.base_query_engine
        
        self.stats = {
            'queries_processed': 0,
            'conversation_context_used': 0,
            'average_response_time': 0.0
        }
    
    def query(self, str_or_query_bundle) -> Response:
        """Execute query with conversation awareness."""
        start_time = datetime.now()
        
        try:
            # Process query
            response = self.query_engine.query(str_or_query_bundle)
            
            # Update conversation history
            if isinstance(str_or_query_bundle, str):
                query_str = str_or_query_bundle
            else:
                query_str = str_or_query_bundle.query_str
            
            self._update_conversation_history(query_str)
            
            # Update statistics
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_stats(processing_time)
            
            # Enhance response with conversation metadata
            enhanced_response = self._enhance_response_with_conversation_context(response)
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Query processing failed: {str(e)}")
            raise
    
    def _update_conversation_history(self, query: str) -> None:
        """Update conversation history with new query."""
        self.conversation_history.append(query)
        
        # Keep only last 10 queries to prevent memory bloat
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
    
    def _update_stats(self, processing_time: float) -> None:
        """Update processing statistics."""
        self.stats['queries_processed'] += 1
        
        if self.enable_conversation_context:
            self.stats['conversation_context_used'] += 1
        
        # Update average response time
        current_avg = self.stats['average_response_time']
        query_count = self.stats['queries_processed']
        self.stats['average_response_time'] = (
            (current_avg * (query_count - 1) + processing_time) / query_count
        )
    
    def _enhance_response_with_conversation_context(self, response: Response) -> Response:
        """Enhance response with conversation-specific metadata."""
        if not response.source_nodes:
            return response
        
        # Add conversation metadata to source nodes
        enhanced_nodes = []
        for node_with_score in response.source_nodes:
            node = node_with_score.node
            metadata = node.metadata or {}
            
            # Add conversation context indicators
            if metadata.get('conversation_type'):
                conversation_indicators = {
                    'is_conversation_aware': True,
                    'conversation_type': metadata.get('conversation_type'),
                    'participant_count': metadata.get('participant_count', 0),
                    'engagement_score': metadata.get('engagement_score', 0.0)
                }
                
                # Update node metadata
                enhanced_metadata = {**metadata, **conversation_indicators}
                node.metadata = enhanced_metadata
            
            enhanced_nodes.append(node_with_score)
        
        # Create enhanced response
        response.source_nodes = enhanced_nodes
        return response
    
    def get_conversation_history(self) -> List[str]:
        """Get current conversation history."""
        return self.conversation_history.copy()
    
    def clear_conversation_history(self) -> None:
        """Clear conversation history."""
        self.conversation_history.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get query engine statistics."""
        return self.stats.copy()
