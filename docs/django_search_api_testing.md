# Django Search API Comprehensive Testing

This document explains how to test the Django Search API to ensure all RAG techniques are working correctly.

## Overview

The Django Search API implements several advanced RAG (Retrieval-Augmented Generation) techniques:

1. **Hybrid Search** - Combines BM25 keyword search with vector semantic search
2. **Query Expansion** - Uses HyDE (Hypothetical Document Embeddings) to expand queries
3. **Multi-Step Reasoning** - Breaks complex queries into sub-questions for better answers
4. **Context-Aware Retrieval** - Uses conversation context for better understanding
5. **Citation Engine** - Automatically provides citations and source references
6. **Router Engine** - Intelligently routes queries to appropriate content types

## Test Scripts

### 1. Standalone API Test Script

**Location**: `scripts/test_django_search_api.py`

This script specifically tests the Django Search API endpoints with various RAG technique combinations.

**Usage**:
```bash
cd scripts
python test_django_search_api.py
```

**Features**:
- Tests all RAG techniques individually and in combination
- Measures performance metrics (response time, accuracy)
- Verifies citation engine functionality
- Tests different output formats (text, json, markdown, table)
- Generates comprehensive test reports

### 2. Comprehensive Ingestion + API Test

**Location**: `multi_source_rag/test_comprehensive_ingestion.py`

This script runs the full pipeline: ingestion → validation → API testing.

**Usage**:
```bash
cd multi_source_rag
python test_comprehensive_ingestion.py
```

**Features**:
- Tests data ingestion with real Slack data
- Validates data quality and integrity
- Tests search functionality through services
- Tests Django Search API with all RAG techniques
- Generates comprehensive reports

## Prerequisites

### 1. Django Server Running

Make sure the Django development server is running:

```bash
cd multi_source_rag
python manage.py runserver
```

The server should be accessible at `http://localhost:8000`.

### 2. Data Ingested

Ensure you have data ingested in the system. You can use the ingestion test script or manually ingest data:

```bash
# Using the comprehensive test (recommended)
python test_comprehensive_ingestion.py

# Or manually through Django shell
python manage.py shell
>>> from apps.documents.services.ingestion_service import IngestionService
>>> # ... run ingestion
```

### 3. Required Dependencies

Install required packages:

```bash
pip install requests  # For API testing
```

## Test Scenarios

### Basic Search Test
- **Query**: "budget adherence testing"
- **RAG Features**: Hybrid search + Context-aware
- **Purpose**: Verify basic functionality

### Query Expansion Test
- **Query**: "bug reports and issues"
- **RAG Features**: Hybrid search + Context-aware + Query expansion (HyDE)
- **Purpose**: Verify query expansion improves results

### Multi-Step Reasoning Test
- **Query**: "What are the main engineering challenges discussed in recent meetings?"
- **RAG Features**: Hybrid search + Context-aware + Multi-step reasoning
- **Purpose**: Verify complex query decomposition

### Full RAG Features Test
- **Query**: "Summarize customer feedback and manager recommendations from the last quarter"
- **RAG Features**: All techniques enabled
- **Purpose**: Verify all techniques work together

### Low Relevance Threshold Test
- **Query**: "testing updates and showstopper bugs"
- **RAG Features**: Hybrid search + Query expansion + Low relevance threshold (0.2)
- **Purpose**: Verify system handles edge cases

## API Endpoint Details

### Search Endpoint
- **URL**: `POST /api/search/`
- **Content-Type**: `application/json`

### Request Parameters

```json
{
  "query": "your search query",
  "tenant_slug": "test-tenant",
  "top_k": 20,
  "use_hybrid_search": true,
  "use_context_aware": true,
  "use_query_expansion": true,
  "use_multi_step_reasoning": true,
  "output_format": "text",
  "min_relevance_score": 0.4
}
```

### Response Format

```json
{
  "status": "success",
  "data": {
    "query": "your search query",
    "answer": "Generated answer...",
    "timestamp": "2025-01-30T10:00:00Z",
    "metrics": {
      "retriever_score": 0.85,
      "confidence_score": 0.92,
      "processing_time": "2.34s",
      "sources_count": 5,
      "is_fallback": false
    },
    "sources": [
      {
        "id": 123,
        "text": "Source text excerpt...",
        "relevance": 0.89,
        "metadata": {
          "title": "Document Title",
          "url": "https://slack.com/permalink",
          "source": "slack",
          "created_at": "2025-01-15T09:00:00Z"
        }
      }
    ]
  }
}
```

## Expected Results

### Success Criteria

1. **Response Time**: < 5 seconds for most queries
2. **Success Rate**: > 90% of API calls succeed
3. **Citation Quality**: All responses include relevant citations
4. **RAG Technique Verification**: All enabled techniques show evidence of working

### RAG Technique Verification

- **Hybrid Search**: Sources include both keyword and semantic matches
- **Query Expansion**: Expanded queries find more relevant results
- **Multi-Step Reasoning**: Complex queries are broken down and answered comprehensively
- **Context-Aware**: Responses consider conversation context
- **Citation Engine**: All responses include source citations with metadata
- **Router Engine**: Queries are routed to appropriate content types

## Troubleshooting

### Common Issues

1. **Server Not Running**
   - Error: Connection refused
   - Solution: Start Django server with `python manage.py runserver`

2. **No Data Available**
   - Error: Empty responses or fallback answers
   - Solution: Run data ingestion first

3. **Import Errors**
   - Error: Module not found
   - Solution: Ensure you're in the correct directory and Django is set up

4. **Timeout Errors**
   - Error: Request timeout
   - Solution: Increase timeout or check server performance

### Performance Issues

If tests are slow:
1. Check Qdrant vector database is running
2. Verify LLM service (Gemini/Ollama) is accessible
3. Consider reducing `top_k` values for faster responses
4. Check system resources (RAM, CPU)

## Interpreting Results

### Test Report Structure

```json
{
  "total_tests": 10,
  "successful_tests": 9,
  "failed_tests": 1,
  "rag_techniques_verified": {
    "hybrid_search": true,
    "query_expansion": true,
    "multi_step_reasoning": true,
    "context_aware": true,
    "citation_engine": true,
    "router_engine": true
  },
  "performance_metrics": {
    "total_time": 25.4,
    "average_time": 2.54,
    "min_time": 1.2,
    "max_time": 4.8
  }
}
```

### Success Indicators

- ✅ All RAG techniques verified as working
- ✅ > 90% test success rate
- ✅ Average response time < 3 seconds
- ✅ All responses include citations
- ✅ No fallback answers for relevant queries

### Warning Indicators

- ⚠️ 70-90% test success rate
- ⚠️ Some RAG techniques not verified
- ⚠️ Average response time 3-5 seconds
- ⚠️ Some responses lack citations

### Failure Indicators

- ❌ < 70% test success rate
- ❌ Multiple RAG techniques not working
- ❌ Average response time > 5 seconds
- ❌ Many fallback answers
- ❌ Server errors or timeouts

## Next Steps

After running tests:

1. **Review Results**: Check which RAG techniques are working
2. **Fix Issues**: Address any failed tests or missing techniques
3. **Performance Tuning**: Optimize slow queries or techniques
4. **Documentation**: Update this document with any new findings
5. **Monitoring**: Set up ongoing monitoring for production use

## Files Generated

Test runs generate the following files:

- `docs/api_test_results_YYYYMMDD_HHMMSS.json` - API test results
- `docs/ingestion_test_report_YYYYMMDD_HHMMSS.json` - Comprehensive test results

These files contain detailed test results and can be used for analysis and debugging.
