#!/usr/bin/env python
"""
Script to test search quality with real Slack data.
"""

import requests
import json
import time
from datetime import datetime

# Test queries based on typical Slack channel content
TEST_QUERIES = [
    # General product engineering queries
    {
        "query": "product engineering discussions",
        "intent": "general",
        "expected_topics": ["product", "engineering", "development"]
    },
    {
        "query": "technical issues and bugs",
        "intent": "issue_tracking", 
        "expected_topics": ["bug", "issue", "error", "problem"]
    },
    {
        "query": "feature development and planning",
        "intent": "planning",
        "expected_topics": ["feature", "development", "planning", "roadmap"]
    },
    {
        "query": "team collaboration and communication",
        "intent": "collaboration",
        "expected_topics": ["team", "collaboration", "meeting", "discussion"]
    },
    {
        "query": "code reviews and pull requests",
        "intent": "code_review",
        "expected_topics": ["code", "review", "pull request", "PR"]
    },
    {
        "query": "deployment and release processes",
        "intent": "deployment",
        "expected_topics": ["deploy", "release", "production", "staging"]
    },
    {
        "query": "performance optimization",
        "intent": "performance",
        "expected_topics": ["performance", "optimization", "speed", "efficiency"]
    },
    {
        "query": "user feedback and requirements",
        "intent": "feedback",
        "expected_topics": ["user", "feedback", "requirements", "customer"]
    }
]

def test_search_api(query, intent="general", output_format="structured"):
    """Test the search API with a specific query."""
    url = "http://localhost:8000/api/search/"
    
    payload = {
        "query": query,
        "intent": intent,
        "output_format": output_format,
        "limit": 10
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        print(f"\n🔍 Testing query: '{query}'")
        print(f"   Intent: {intent}")
        
        start_time = time.time()
        response = requests.post(url, json=payload, headers=headers)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        print(f"   Response time: {response_time:.2f}ms")
        print(f"   Status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Extract key metrics
            answer = data.get("answer", "")
            sources = data.get("sources", [])
            metadata = data.get("metadata", {})
            
            print(f"   Sources found: {len(sources)}")
            print(f"   Answer length: {len(answer)} characters")
            
            # Print answer preview
            if answer:
                preview = answer[:200] + "..." if len(answer) > 200 else answer
                print(f"   Answer preview: {preview}")
            
            # Print source information
            if sources:
                print(f"   Source details:")
                for i, source in enumerate(sources[:3]):  # Show first 3 sources
                    title = source.get("title", "Unknown")
                    score = source.get("score", 0)
                    print(f"     {i+1}. {title} (score: {score:.3f})")
            
            # Print metadata
            if metadata:
                processing_time = metadata.get("processing_time_seconds", 0)
                print(f"   Processing time: {processing_time:.2f}s")
            
            return {
                "success": True,
                "response_time_ms": response_time,
                "answer_length": len(answer),
                "sources_count": len(sources),
                "data": data
            }
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return {
                "success": False,
                "status_code": response.status_code,
                "error": response.text
            }
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

def evaluate_search_quality(results):
    """Evaluate the overall search quality based on test results."""
    print("\n" + "="*60)
    print("🎯 SEARCH QUALITY EVALUATION")
    print("="*60)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r.get("success", False))
    
    print(f"Total tests: {total_tests}")
    print(f"Successful tests: {successful_tests}")
    print(f"Success rate: {(successful_tests/total_tests)*100:.1f}%")
    
    if successful_tests > 0:
        # Calculate average metrics
        response_times = [r["response_time_ms"] for r in results if r.get("success")]
        answer_lengths = [r["answer_length"] for r in results if r.get("success")]
        sources_counts = [r["sources_count"] for r in results if r.get("success")]
        
        print(f"\n📊 Performance Metrics:")
        print(f"Average response time: {sum(response_times)/len(response_times):.2f}ms")
        print(f"Average answer length: {sum(answer_lengths)/len(answer_lengths):.0f} characters")
        print(f"Average sources per query: {sum(sources_counts)/len(sources_counts):.1f}")
        
        # Quality indicators
        print(f"\n✅ Quality Indicators:")
        queries_with_answers = sum(1 for r in results if r.get("answer_length", 0) > 0)
        queries_with_sources = sum(1 for r in results if r.get("sources_count", 0) > 0)
        
        print(f"Queries with answers: {queries_with_answers}/{successful_tests} ({(queries_with_answers/successful_tests)*100:.1f}%)")
        print(f"Queries with sources: {queries_with_sources}/{successful_tests} ({(queries_with_sources/successful_tests)*100:.1f}%)")
        
        # Response time analysis
        fast_responses = sum(1 for rt in response_times if rt < 1000)  # Under 1 second
        print(f"Fast responses (<1s): {fast_responses}/{len(response_times)} ({(fast_responses/len(response_times))*100:.1f}%)")
    
    print("\n" + "="*60)

def main():
    """Main function to run search quality tests."""
    print("🚀 Starting Search Quality Tests with Real Slack Data")
    print("="*60)
    
    results = []
    
    # Test each query
    for test_case in TEST_QUERIES:
        query = test_case["query"]
        intent = test_case["intent"]
        
        result = test_search_api(query, intent)
        results.append(result)
        
        # Small delay between requests
        time.sleep(0.5)
    
    # Evaluate overall quality
    evaluate_search_quality(results)
    
    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"search_quality_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump({
            "timestamp": timestamp,
            "test_queries": TEST_QUERIES,
            "results": results
        }, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    
    return results

if __name__ == "__main__":
    main()
