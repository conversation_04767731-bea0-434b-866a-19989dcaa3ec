{"thread_ts": "1737652647.546999", "channel_id": "C065QSSNH8A", "reply_count": 5, "replies": [{"ts": "1737652689.741449", "text": "yes, thats correct. The role would be automatically assigned, but we still need create login for SSO.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737652647.546999", "blocks": [{"type": "rich_text", "block_id": "GBjl1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes, thats correct. The role would be automatically assigned, but we still need create login for SSO."}]}]}]}, {"ts": "1737653543.088379", "text": "Ok; how do we initiate that? We're about a week out so it's time to do it. They have a long list of people, they are in the system. I don't think I have a data export anywhere that shows me a list of recommenders/approvers. There are a lot.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737652647.546999", "blocks": [{"type": "rich_text", "block_id": "nozx0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok; how do we initiate that? We're about a week out so it's time to do it. They have a long list of people, they are in the system. I don't think I have a data export anywhere that shows me a list of recommenders/approvers. There are a lot."}]}]}]}, {"ts": "1737653651.049359", "text": "I will do in a day. Basically create login for all recommenders correct?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737652647.546999", "blocks": [{"type": "rich_text", "block_id": "WCXhP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will do in a day. Basically create login for all recommenders correct?"}]}]}]}, {"ts": "1737654966.861409", "text": "Yep!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737652647.546999", "blocks": [{"type": "rich_text", "block_id": "M57tO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yep!"}]}]}]}, {"ts": "1737655000.098719", "text": ":+1:", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737652647.546999", "blocks": [{"type": "rich_text", "block_id": "qF+xl", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "+1", "unicode": "1f44d"}]}]}]}], "created_at": "2025-05-22T22:00:49.349493"}