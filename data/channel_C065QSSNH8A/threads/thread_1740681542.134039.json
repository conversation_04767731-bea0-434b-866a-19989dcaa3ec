{"thread_ts": "1740681542.134039", "channel_id": "C065QSSNH8A", "reply_count": 2, "replies": [{"ts": "1740681601.977079", "text": "It also doesn't appear to capture the whole page of info", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740681542.134039", "blocks": [{"type": "rich_text", "block_id": "B/u82", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It also doesn't appear to capture the whole page of info"}]}]}]}, {"ts": "1740722915.239269", "text": "We decided to show the latest 5 entries since there could be entries beyond a single page.\nHere's an example employee and how the export looks in this case. If this looks fine, we can push this", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1740681542.134039", "reactions": [{"name": "thankyouty", "users": ["U0690EB5JE5"], "count": 1}], "files": [{"id": "F08FTQN1MND", "created": 1740722871, "timestamp": 1740722871, "name": "Tyler_Viers_Total_Rewards_20250228.pdf", "title": "Tyler_Viers_Total_Rewards_20250228.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U07MH77PUBV", "user_team": "T04DM97F1UM", "editable": false, "size": 88947397, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FTQN1MND/tyler_viers_total_rewards_20250228.pdf", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FTQN1MND/download/tyler_viers_total_rewards_20250228.pdf", "media_display_type": "unknown", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08FTQN1MND-cbb010dfe1/tyler_viers_total_rewards_20250228_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FTQN1MND/tyler_viers_total_rewards_20250228.pdf", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FTQN1MND-2cba19f2bb", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "/Svfr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We decided to show the latest 5 entries since there could be entries beyond a single page.\nHere's an example employee and how the export looks in this case. If this looks fine, we can push this"}]}]}]}], "created_at": "2025-05-22T22:00:49.356758"}