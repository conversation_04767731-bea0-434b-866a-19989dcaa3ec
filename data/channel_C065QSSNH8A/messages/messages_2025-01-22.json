{"date": "2025-01-22", "channel_id": "C065QSSNH8A", "message_count": 14, "messages": [{"ts": "1737569929.826239", "text": "<@U0690EB5JE5> Can you confirm when Diversifieds data was last updated? It was Monday right? <PERSON> is finding discrepancies in the target bonus amount. Variances she found, attached. She's checking against Paycor right now, the most current, so I would expect it to be aligned.\n\nThere are 18 people who still aren't updated (the ones in the other file I sent you), but the rest of these should be correct. Can you please investigate?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737569929.826239", "reply_count": 3, "files": [{"id": "F08A6QPFZDF", "created": 1737569926, "timestamp": 1737569926, "name": "Quality Check Variances on Bonus Amounts.xlsx", "title": "Quality Check Variances on Bonus Amounts.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 14025, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08A6QPFZDF/quality_check_variances_on_bonus_amounts.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08A6QPFZDF/download/quality_check_variances_on_bonus_amounts.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A6QPFZDF-246e8583b3/quality_check_variances_on_bonus_amounts_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A6QPFZDF-246e8583b3/quality_check_variances_on_bonus_amounts_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08A6QPFZDF/quality_check_variances_on_bonus_amounts.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08A6QPFZDF-55275ad10c", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "2BQlP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you confirm when Diversifieds data was last updated? It was Monday right? <PERSON> is finding discrepancies in the target bonus amount. Variances she found, attached. She's checking against Paycor right now, the most current, so I would expect it to be aligned.\n\nThere are 18 people who still aren't updated (the ones in the other file I sent you), but the rest of these should be correct. Can you please investigate?"}]}]}]}, {"ts": "1737566647.158689", "text": "Bug with Super Admin view (also in VG): <https://compiify.atlassian.net/browse/COM-4070>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737566647.158689", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14153::4e10b3452b2b419dae15343b53fe3ae3", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4070?atlOrigin=eyJpIjoiMGE4NGUzNDk1MmE2NDIzZThiNWZiNjI1NDEyN2M1NzUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4070 HRBP Super Admin View Issue for Kelli Hart>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14153::cbdc59555cc24e4ba72d1fedd1110c2a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14153\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4070\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4070", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "h1UHa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bug with Super Admin view (also in VG): "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4070"}]}]}]}, {"ts": "1737566352.193459", "text": "<@U0690EB5JE5> bug with HRBP filters <https://compiify.atlassian.net/browse/COM-4069>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737566352.193459", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14152::75486ad7bd3c43c5898ca134d60184b6", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4069?atlOrigin=eyJpIjoiNTVlNTNhNDk5Yzk1NGMwODljYmEwN2Y2MmI1MGEzYTAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4069 Bug in HRBP View Filters in Val Genesis>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14152::ea57dc16b253495881cd7f6903e39ea2", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14152\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4069\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4069", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Pzx3j", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " bug with HRBP filters "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4069"}]}]}]}, {"ts": "1737563649.213429", "text": "ok then I will delete it.  I don't have anything on the agenda", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hBm3C", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok then I will delete it.  I don't have anything on the agenda"}]}]}]}, {"ts": "1737563614.033109", "text": "<@U07M6QKHUC9> you are free to delete the Ali care meeting today unless you want to connect with <PERSON>. Right now you own the meeting.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yAauW", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " you are free to delete the Ali care meeting today unless you want to connect with <PERSON>. Right now you own the meeting."}]}]}]}, {"ts": "1737562796.580289", "text": "Great. Will sync tomorrow.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qvYBc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Great. Will sync tomorrow."}]}]}]}, {"ts": "1737562762.969679", "text": "<@U0690EB5JE5> tightly has re-authenticated and is ready for a fresh sync", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Y1qSY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " tightly has re-authenticated and is ready for a fresh sync"}]}]}]}, {"ts": "1737562754.572089", "text": "<@U07EJ2LP44S> Could you please check with Curana. If they are on Free Plan or Paid Plan for APIs? Sync is not working as we are crossing the rate limit.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737562754.572089", "reply_count": 2, "edited": {"user": "U0690EB5JE5", "ts": "1737562822.000000"}, "blocks": [{"type": "rich_text", "block_id": "fytcO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Could you please check with <PERSON><PERSON><PERSON>. If they are on Free Plan or Paid Plan for APIs? Sync is not working as we are crossing the rate limit."}]}]}]}, {"ts": "1737560174.951079", "text": "<@U07EJ2LP44S> comments uploaded to VG ENV.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2l5NI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " comments uploaded to VG ENV."}]}]}]}, {"ts": "1737559928.327129", "text": "<@U0690EB5JE5> can you help me with this file? Diversified needs to update the salary and target bonus of these individuals. I downloaded the employee file, did a vlookup to replace salary and target bonus, and then tried to upload. I tried it with the bonus columns, I tried it without, but I continue to get a 'doesn't match required format' error. I didn't make any other changes to the file.\n\nI'm not sure how to handle it when I need to change the target bonus, and need the system to recalculate the $ amounts, so that's why I tried it a variety of ways, but none of them will take.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737559928.327129", "reply_count": 14, "files": [{"id": "F08AFRRT0TS", "created": 1737559926, "timestamp": 1737559926, "name": "DivEn_18emp.csv", "title": "DivEn_18emp.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 4872, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08AFRRT0TS/diven_18emp.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08AFRRT0TS/download/diven_18emp.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AFRRT0TS/diven_18emp.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08AFRRT0TS-9e1482c617", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AFRRT0TS/diven_18emp.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),,,,,,,,,,,,,,Pay Mix\r\nU,30758,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,<EMAIL>,M,1/10/24,US,16491,,Active,1/10/24,,WHITE,AL,EVP,BusinessDev:Bham:R,,,M,8,Yes,,FULL_TI...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30758</div><div class=\"cm-col\">Michael</div><div class=\"cm-col\">Rigg</div><div class=\"cm-col\">Michael Rigg</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">1/10/24</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16491</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/10/24</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">AL</div><div class=\"cm-col\">EVP</div><div class=\"cm-col\">BusinessDev:Bham:R</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">350000.04</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">125</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">29192</div><div class=\"cm-col\">Douglas</div><div class=\"cm-col\">Kris</div><div class=\"cm-col\">Douglas Kris</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">7/19/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16470</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">7/19/22</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">AL</div><div class=\"cm-col\">SVP</div><div class=\"cm-col\">InvstRel:Bham:R</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">7</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">256035.26</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">50</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">19081</div><div class=\"cm-col\">Todd</div><div class=\"cm-col\">Tetrick</div><div class=\"cm-col\">Todd Tetrick</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">6/8/20</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16369</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">6/8/20</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">WV</div><div class=\"cm-col\">SVP</div><div class=\"cm-col\">Admin:Charleston</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">7</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">270500.1</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">50</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16515</div><div class=\"cm-col\">Mark</div><div class=\"cm-col\">Kirkendall</div><div class=\"cm-col\">Mark Kirkendall</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">5/20/19</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16491</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">5/20/19</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">AL</div><div class=\"cm-col\">EVP &amp; CHRO</div><div class=\"cm-col\">Human Resources:Bham:R</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">350000.04</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">125</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16590</div><div class=\"cm-col\">Teresa</div><div class=\"cm-col\">Odom</div><div class=\"cm-col\">Teresa Odom</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">3/11/19</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16470</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">3/11/19</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">AL</div><div class=\"cm-col\">SVP</div><div class=\"cm-col\">Investor Relations:Bham</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">7</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">229999.9</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">50</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16491</div><div class=\"cm-col\">Robert</div><div class=\"cm-col\">Hutson</div><div class=\"cm-col\">Robert Hutson</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">1/1/01</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">555</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/1/01</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">AL</div><div class=\"cm-col\">CEO</div><div class=\"cm-col\">Executive:Bham</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">9</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">780000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">175</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 19, "lines_more": 17, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "rYHkr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you help me with this file? Diversified needs to update the salary and target bonus of these individuals. I downloaded the employee file, did a vlookup to replace salary and target bonus, and then tried to upload. I tried it with the bonus columns, I tried it without, but I continue to get a 'doesn't match required format' error. I didn't make any other changes to the file.\n\nI'm not sure how to handle it when I need to change the target bonus, and need the system to recalculate the $ amounts, so that's why I tried it a variety of ways, but none of them will take."}]}]}]}, {"ts": "1737538859.795819", "text": "<@U07EJ2LP44S>\n\n• <http://stridedemo.stridehr.io|stridedemo.stridehr.io>\n• <http://demo.stridehr.io|demo.stridehr.io>\n• <http://test.stridehr.io|test.stridehr.io>\n• <http://qa.stridehr.io|qa.stridehr.io>\nAll the ENVs are up and running and updated with demo data. except for <http://demo.stridhr.io|demo.stridhr.io>, rest all have the same data.  <http://demo.stridhr.io|demo.stridhr.io> has the same data but there would edits and cycles created for demo and there would be slight differences in the data.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737474213.649449", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1737538989.000000"}, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "SIv0V", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "http://stridedemo.stridehr.io", "text": "stridedemo.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://demo.stridehr.io", "text": "demo.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAll the ENVs are up and running and updated with demo data. except for "}, {"type": "link", "url": "http://demo.stridhr.io", "text": "demo.stridhr.io"}, {"type": "text", "text": ", rest all have the same data.  "}, {"type": "link", "url": "http://demo.stridhr.io", "text": "demo.stridhr.io"}, {"type": "text", "text": " has the same data but there would edits and cycles created for demo and there would be slight differences in the data."}]}]}]}, {"ts": "1737532109.776949", "text": "<@U07EJ2LP44S> Merge has some issue with sync. I have reached out to the support. Will keep this thread updated.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737484579.982319", "subtype": "thread_broadcast", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "kzUIX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " <PERSON><PERSON> has some issue with sync. I have reached out to the support. Will keep this thread updated."}]}]}]}, {"ts": "1737484912.181909", "text": "<@U0690EB5JE5> Tithely - they have has 6 new hires and 4 terms at least this month, so the data refresh doesn't seem to be complete. Can you look into this one too?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737484912.181909", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "0yGWt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Tithely - they have has 6 new hires and 4 terms at least this month, so the data refresh doesn't seem to be complete. Can you look into this one too?"}]}]}]}, {"ts": "1737484579.982319", "text": "<@U0690EB5JE5> <PERSON><PERSON><PERSON>'s data is showing incorrect hierarchy information. For example, there are about a dozen people reporting to the CEO that should not be. They are checking the HRIS data and all but one we checked do have a manager in the system (that isn't the CEO). <PERSON> said the field name is Supervisor ID that has the correct info. Can you look into this?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737484579.982319", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "S1xNG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON>'s data is showing incorrect hierarchy information. For example, there are about a dozen people reporting to the CEO that should not be. They are checking the HRIS data and all but one we checked do have a manager in the system (that isn't the CEO). <PERSON> said the field name is Supervisor ID that has the correct info. Can you look into this?"}]}]}]}], "created_at": "2025-05-22T21:35:34.705907"}