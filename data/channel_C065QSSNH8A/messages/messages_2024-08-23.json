{"date": "2024-08-23", "channel_id": "C065QSSNH8A", "message_count": 8, "messages": [{"ts": "1724435822.969229", "text": "<@U07EJ2LP44S> lets plan to ask larger customers like valgenesis who plan to use role like HRBP if they will need any explicit data permission. Any requirements will need larger implementation time.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724435822.969229", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "prH5r", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " lets plan to ask larger customers like valgenesis who plan to use role like HRBP if they will need any explicit data permission. Any requirements will need larger implementation time."}]}]}]}, {"ts": "1724434275.072249", "text": "<PERSON><PERSON><PERSON><PERSON> just emailed; they have been acquired by a much larger org - Digicert - they appear to be ~1500 people", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "tiUVB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON> just emailed; they have been acquired by a much larger org - Digicert - they appear to be ~1500 people"}]}]}]}, {"ts": "1724426818.862589", "text": "I forgot to confirm; did we get the requested fields in HiBob for valgenesis? Or do we need to communicate that some are missing? <@U04DKEFP1K8>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1724426818.862589", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "VPdWq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I forgot to confirm; did we get the requested fields in HiBob for valgenesis? Or do we need to communicate that some are missing? "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1724393311.658739", "text": "Agenda for today\n• Queries on CWA SFTP Integration\n• Issues faced by <@U07EJ2LP44S> with data upload\n• Support CAD as base currency. Should we support base currency for org and cycle separately? Which is not recommended IMO.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724393311.658739", "reply_count": 2, "edited": {"user": "U0690EB5JE5", "ts": "1724408685.000000"}, "blocks": [{"type": "rich_text", "block_id": "IIQ/f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Queries on CWA SFTP Integration"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Issues faced by "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " with data upload"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Support CAD as base currency. Should we support base currency for org and cycle separately? Which is not recommended IMO."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724378565.164089", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> would be great if you could add me as optional to every customer call. This is just for my awareness so that I can follow up on the same and also would like listen to every customer call if you could record and share it. Please let me know if the calls are already being recorded and stored somewhere in any form.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724378690.000000"}, "blocks": [{"type": "rich_text", "block_id": "OzYTK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " would be great if you could add me as optional to every customer call. This is just for my awareness so that I can follow up on the same and also would like listen to every customer call if you could record and share it. Please let me know if the calls are already being recorded and stored somewhere in any form."}]}]}]}, {"ts": "1724359847.308329", "text": "Quick clarification question - for testing out the prioritization spreadsheet on Monday, would you like to EXCLUDE all of the current \"Priority 0\" items and only focus on prioritizing everything after the \"by end of Sept\" push?\n\nI am assuming so because the Priority 0 items most likely have to get done no matter what, but please correct me if I misunderstood.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724359847.308329", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "zin1/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Quick clarification question - for testing out the prioritization spreadsheet on Monday, would you like to EXCLUDE all of the current \"Priority 0\" items and only focus on prioritizing everything after the \"by end of Sept\" push?\n\nI am assuming so because the Priority 0 items most likely have to get done no matter what, but please correct me if I misunderstood."}]}]}]}, {"ts": "1724355502.510709", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> I don't think we need the customer implementation call today, unless there are outstanding questions", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1724355502.510709", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "NDlNr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I don't think we need the customer implementation call today, unless there are outstanding questions"}]}]}]}, {"ts": "1724354839.783569", "text": "<@U04DS2MBWP4> <@U07EJ2LP44S> is there a place where I can find recordings of customer or internal calls that might have any sort of discussion that would be relevant to the theme of manager enablement? I like to take short walk breaks during the work day, and would love to be able to listen to some calls to start getting immersed in the problem space.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724354839.783569", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "FbTHZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " is there a place where I can find recordings of customer or internal calls that might have any sort of discussion that would be relevant to the theme of manager enablement? I like to take short walk breaks during the work day, and would love to be able to listen to some calls to start getting immersed in the problem space."}]}]}]}], "created_at": "2025-05-22T21:35:34.626265"}