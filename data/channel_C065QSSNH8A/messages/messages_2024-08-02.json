{"date": "2024-08-02", "channel_id": "C065QSSNH8A", "message_count": 21, "messages": [{"ts": "1722618279.678149", "text": "They are just waiting on us to get the data, so ideally we'd get it ASAP.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722618279.678149", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "5p+cg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They are just waiting on us to get the data, so ideally we'd get it ASAP."}]}]}]}, {"ts": "1722617076.527629", "text": "When is the earlier we need to have it enabled?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Y/SAi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "When is the earlier we need to have it enabled?"}]}]}]}, {"ts": "1722617061.292289", "text": "<@U04DS2MBWP4> <@U07EJ2LP44S> I just checked again and found that We need to request Merge team to enable Paycom for us as its in beta phase.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1722617126.000000"}, "files": [{"id": "F07FA9AVC2W", "created": 1722617033, "timestamp": 1722617033, "name": "Screenshot 2024-08-02 at 10.13.41 PM.png", "title": "Screenshot 2024-08-02 at 10.13.41 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 35645, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07FA9AVC2W/screenshot_2024-08-02_at_10.13.41___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07FA9AVC2W/download/screenshot_2024-08-02_at_10.13.41___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 77, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 103, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 154, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 172, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 206, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FA9AVC2W-08af52d2cb/screenshot_2024-08-02_at_10.13.41___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 220, "original_w": 1263, "original_h": 271, "thumb_tiny": "AwAKADDTppOD3p1N/ioAN3saXd7GlooAbu9jTqKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07FA9AVC2W/screenshot_2024-08-02_at_10.13.41___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07FA9AVC2W-e6f6111b2d", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "RZk9w", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I just checked again and found that We need to request Merge team to enable Paycom for us as its in beta phase."}]}]}]}, {"ts": "1722612701.634649", "text": "I have a little more info about the Paycom integration question I can share", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "P5OIC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have a little more info about the Paycom integration question I can share"}]}]}]}, {"ts": "1722612241.146819", "text": "<@U07EJ2LP44S> and I are meeting 9am PT today, let me add both of you to that invite and we can discuss any pressing item before <PERSON> and i discuss data items", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4UPO0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " and I are meeting 9am PT today, let me add both of you to that invite and we can discuss any pressing item before <PERSON> and i discuss data items"}]}]}]}, {"ts": "1722612168.762789", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FUpwS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1722612157.697279", "text": "No meeting today?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HXcz4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No meeting today?"}]}]}]}, {"ts": "1722608414.749099", "text": "Are there any practifi calls recorded? <@U04DS2MBWP4><@U04DKEFP1K8>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722608414.749099", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "KBf6h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Are there any practifi calls recorded? "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1722579288.747429", "text": "Did we have requirement of supporting hourly/part time employees from any customers?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mJAMs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Did we have requirement of supporting hourly/part time employees from any customers?"}]}]}]}, {"ts": "1722575545.174949", "text": "Yes thats how it is", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Gd9Yc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes thats how it is"}]}]}]}, {"ts": "1722573577.356319", "text": "Also each item in this nav bar will be clickable? meaning we can go to any section after its completed?", "user": "U0690EB5JE5", "type": "message", "files": [{"id": "F07F43SE9L5", "created": 1722573551, "timestamp": 1722573551, "name": "Screenshot 2024-08-02 at 10.09.05 AM.png", "title": "Screenshot 2024-08-02 at 10.09.05 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 27726, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07F43SE9L5/screenshot_2024-08-02_at_10.09.05___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07F43SE9L5/download/screenshot_2024-08-02_at_10.09.05___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F43SE9L5-01f6a51662/screenshot_2024-08-02_at_10.09.05___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F43SE9L5-01f6a51662/screenshot_2024-08-02_at_10.09.05___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F43SE9L5-01f6a51662/screenshot_2024-08-02_at_10.09.05___am_360.png", "thumb_360_w": 237, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F43SE9L5-01f6a51662/screenshot_2024-08-02_at_10.09.05___am_480.png", "thumb_480_w": 316, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07F43SE9L5-01f6a51662/screenshot_2024-08-02_at_10.09.05___am_160.png", "original_w": 398, "original_h": 604, "thumb_tiny": "AwAwAB/RXt06UvPtSDt06UdOm2mA4e+Ka/3aXn2pH+7SAUDgcdqTaP7q0q/dFG0UACgDsB9KR/u0u0Uj/doAVfuilpF+6KWgApr/AHadTX+7QB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07F43SE9L5/screenshot_2024-08-02_at_10.09.05___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07F43SE9L5-2aeaefafd1", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "bqpvn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also each item in this nav bar will be clickable? meaning we can go to any section after its completed?"}]}]}]}, {"ts": "1722573514.886629", "text": "<@U04DS2MBWP4> in the new comp cycle builder figma design. Please do add this feature as well <https://compiify.atlassian.net/browse/COM-2565>", "user": "U0690EB5JE5", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12626::14a935b0508911efb1683337b6fcd8e2", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2565?atlOrigin=eyJpIjoiNmQ1NjllNjBkYWE3NGYwN2IzMmZjZjdiNThjMDNhNjYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2565 Additional planning levels>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12626::14a935b2508911efb1683337b6fcd8e2", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/082e10750208b91e38660f50da682c58?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12626::14a935b1508911efb1683337b6fcd8e2", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12626\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12626\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2565", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "I35at", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " in the new comp cycle builder figma design. Please do add this feature as well "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2565"}]}]}]}, {"ts": "1722556942.583589", "text": "<@U04DS2MBWP4> not sure if you have seen this but since you are redesigning comp builder , it might worth a watch <https://help.lattice.com/hc/en-us/articles/11659407874327-Create-a-Test-Compensation-Cycle> <PERSON><PERSON><PERSON>'s latest update to comp module", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722556942.583589", "reply_count": 1, "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "bmiYU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " not sure if you have seen this but since you are redesigning comp builder , it might worth a watch "}, {"type": "link", "url": "https://help.lattice.com/hc/en-us/articles/11659407874327-Create-a-Test-Compensation-Cycle"}, {"type": "text", "text": " <PERSON><PERSON><PERSON>'s latest update to comp module"}]}]}]}, {"ts": "1722554839.160279", "text": "<@U0690EB5JE5> can we add functionality to track and show promotions budget separately in merit planning even if the promotions budget is combined with salary? Same logic for salary, market adjustments and promotions even if customers has combined each of these components. I think we have a ticket for this in Jira already.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722554839.160279", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "CMcyD", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we add functionality to track and show promotions budget separately in merit planning even if the promotions budget is combined with salary? Same logic for salary, market adjustments and promotions even if customers has combined each of these components. I think we have a ticket for this in Jira already."}]}]}]}, {"ts": "1722554725.943209", "text": "It is duplicated currently and can be removed", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ge15d", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It is duplicated currently and can be removed"}]}]}]}, {"ts": "1722554574.380809", "text": "<@U04DKEFP1K8> currently eligibility cut off date in the proration page is not used in the proration calculations..correct? Any downside to removing that field?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722554574.380809", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "PuvhF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " currently eligibility cut off date in the proration page is not used in the proration calculations..correct? Any downside to removing that field?"}]}]}]}, {"ts": "1722552411.085459", "text": "<@U04DKEFP1K8> did SDF and Nuato use the same currency conversion factor for both cycle configuration and org view? or do we have to have separate  currency conversion factor for cycle configuration and org view", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722552411.085459", "reply_count": 14, "blocks": [{"type": "rich_text", "block_id": "vQgvS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " did SDF and Nuato use the same currency conversion factor for both cycle configuration and org view? or do we have to have separate  currency conversion factor for cycle configuration and org view"}]}]}]}, {"ts": "1722551944.438199", "text": "<@U04DKEFP1K8> could you remind me again why we have flags on the current allocation page and on the eligibility criteria page within comp builder?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722551944.438199", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "iW/bz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " could you remind me again why we have flags on the current allocation page and on the eligibility criteria page within comp builder?"}]}]}]}, {"ts": "1722547151.422019", "text": "<@U07EJ2LP44S> link to jira product discovery <https://compiify.atlassian.net/jira/projects?selectedProjectType=product_discovery>", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "l3vxC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " link to jira product discovery "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/projects?selectedProjectType=product_discovery"}]}]}]}, {"ts": "1722538391.977449", "text": "As I have UX feedback, what's the most useful way to document that? I have a video I captured and talked through. I can post that here, a different channel, or write it up and put it in on drive. Example attached", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722538391.977449", "reply_count": 5, "files": [{"id": "F07FHK3GRBK", "created": 1722538342, "timestamp": 1722538342, "name": "Screen Recording 2024-08-01 at 2.46.49 PM.mov", "title": "Screen Recording 2024-08-01 at 2.46.49 PM.mov", "mimetype": "video/quicktime", "filetype": "mov", "pretty_type": "QuickTime Movie", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 246595469, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "transcription": {"status": "complete", "locale": "en-US", "preview": {"content": "Ok. So I was going through nato's cycle just to get up to speed and going through the merit configuration. And I'm in a reasonably sized, like kind", "has_more": true}}, "mp4": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FHK3GRBK-e626578be7/screen_recording_2024-08-01_at_2.46.49___pm.mp4", "url_private": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FHK3GRBK-e626578be7/screen_recording_2024-08-01_at_2.46.49___pm.mp4", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07FHK3GRBK/download/screen_recording_2024-08-01_at_2.46.49___pm.mov", "vtt": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FHK3GRBK-e626578be7/file.vtt?_xcb=64f96", "hls": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FHK3GRBK-e626578be7/file.m3u8?_xcb=64f96", "hls_embed": "data:application/vnd.apple.mpegurl;base64,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", "mp4_low": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FHK3GRBK-e626578be7/file_trans.mp4", "duration_ms": 100100, "media_display_type": "video", "thumb_video": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FHK3GRBK-e626578be7/screen_recording_2024-08-01_at_2.46.49___pm_thumb_video.jpeg", "thumb_video_w": 1920, "thumb_video_h": 1080, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07FHK3GRBK/screen_recording_2024-08-01_at_2.46.49___pm.mov", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07FHK3GRBK-38f4f5f66a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "5nyiH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "As I have UX feedback, what's the most useful way to document that? I have a video I captured and talked through. I can post that here, a different channel, or write it up and put it in on drive. Example attached"}]}]}]}, {"ts": "1722537263.250379", "text": "Which page is the 'view as' icon on? Where I can see what a specific person sees?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722537263.250379", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "FuaUj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Which page is the 'view as' icon on? Where I can see what a specific person sees?"}]}]}]}], "created_at": "2025-05-22T21:35:34.633556"}