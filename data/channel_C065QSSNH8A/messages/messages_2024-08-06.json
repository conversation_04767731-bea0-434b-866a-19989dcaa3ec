{"date": "2024-08-06", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1722963797.432249", "text": "<@U07EJ2LP44S> <PERSON>'s local auth details are present here in case she cannot use google auth <https://docs.google.com/spreadsheets/d/1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q/edit?gid=0#gid=0>", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ylGQk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " <PERSON>'s local auth details are present here in case she cannot use google auth "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q/edit?gid=0#gid=0"}]}]}]}, {"ts": "1722960021.685999", "text": "Also `sdf-test`, `cwa-test` and `dev-energy` ENV are stopped to save some cost.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "yCp58", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also "}, {"type": "text", "text": "sdf-test", "style": {"code": true}}, {"type": "text", "text": ", "}, {"type": "text", "text": "cwa-test", "style": {"code": true}}, {"type": "text", "text": " and "}, {"type": "text", "text": "dev-energy", "style": {"code": true}}, {"type": "text", "text": " ENV are stopped to save some cost."}]}]}]}, {"ts": "1722959983.395709", "text": "<@U04DKEFP1K8> Fixes in BE-dev and FE-dev (<http://test.stridehr.io|test.stridehr.io>) pushed today.\n• All the feedback/bugs on Vercara edit\n• minor fix on practifi merit page load issue. This is still an issue due to the way cycle is configured and also a gap. We can discuss on this.\n• fix for bug <https://compiify.atlassian.net/browse/COM-3378>\n• Add new job title,  dev complete and testing in progress should be ready tomorrow", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1722959983.395709", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1722960051.000000"}, "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "3rQqL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Fixes in BE-dev and FE-dev ("}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": ") pushed today.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All the feedback/bugs on Vercara edit"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "minor fix on practifi merit page load issue. This is still an issue due to the way cycle is configured and also a gap. We can discuss on this."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "fix for bug "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3378"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Add new job title,  dev complete and testing in progress should be ready tomorrow"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1722955137.292129", "text": "I just created a Practifi channel, but did I do it incorrectly? I created it with an external user but it's not showing under the external connections area (even though I created the channel through that section).", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722955137.292129", "reply_count": 2, "edited": {"user": "U07EJ2LP44S", "ts": "1722955272.000000"}, "blocks": [{"type": "rich_text", "block_id": "vCJX3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I just created a Practifi channel, but did I do it incorrectly? I created it with an external user but it's not showing under the external connections area (even though I created the channel through that section)."}]}]}]}, {"ts": "1722954531.777709", "text": "<@U04DKEFP1K8> the meeting with Rightway is pushed to next week  so now we can move the eng meeting to regular 9 AM PST today? \n\n<@U0690EB5JE5> does that work for you or we can keep it at 10 AM PST", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722954531.777709", "reply_count": 1, "reactions": [{"name": "done", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "R8flc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " the meeting with Rightway is pushed to next week  so now we can move the eng meeting to regular 9 AM PST today? \n\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " does that work for you or we can keep it at 10 AM PST"}]}]}]}, {"ts": "1722950267.199889", "text": "<!here> I have created an epic for Integrations work. Early next week, We will start work on making implementation completely self serve via integration.\nI will keep filling the details in each ticket this week.\n<https://compiify.atlassian.net/browse/COM-3482>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1722950288.000000"}, "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}, {"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13543::46824f2053f611efa48115f6026822d7", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3482?atlOrigin=eyJpIjoiZTUxMjZiOTcxYzk5NGMyYmI2YmY1ZmM4ZWE3MTU3ZDAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3482 Integrations Enhancements>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13543::46824f2253f611efa48115f6026822d7", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13543::46824f2153f611efa48115f6026822d7", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13543\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13543\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3482", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Li5ow", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I have created an epic for Integrations work. Early next week, We will start work on making implementation completely self serve via integration.\nI will keep filling the details in each ticket this week.\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3482"}]}]}]}], "created_at": "2025-05-22T21:35:34.632408"}