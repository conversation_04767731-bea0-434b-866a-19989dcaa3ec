{"date": "2024-09-11", "channel_id": "C065QSSNH8A", "message_count": 16, "messages": [{"ts": "1726076780.892599", "text": "Happy to join the call and get some feedback! I have my availability updated on my calendar, but I do have an appointment today 11:45 - 1pm PST during which I'll be offline", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1726076780.892599", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "v<PERSON>y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Happy to join the call and get some feedback! I have my availability updated on my calendar, but I do have an appointment today 11:45 - 1pm PST during which I'll be offline"}]}]}]}, {"ts": "1726075204.393959", "text": "<@U0690EB5JE5> so far Demo, test and nauto loging is working for me. Rest including valgenesis, div energy, are not working", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726075204.393959", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "gxyKp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " so far Demo, test and nauto loging is working for me. Rest including valgenesis, div energy, are not working"}]}]}]}, {"ts": "1726074882.688949", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1726074833.089709", "text": "They did have one more topic - an equity request for this cycle that we need to discuss internally", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YgDet", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They did have one more topic - an equity request for this cycle that we need to discuss internally"}]}]}]}, {"ts": "1726074764.328009", "text": "<PERSON> is already in this channel :slightly_smiling_face: Cc <@U07HCJ07H7G>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nkwCf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> is already in this channel "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " Cc "}, {"type": "user", "user_id": "U07HCJ07H7G"}]}]}]}, {"ts": "1726074700.928059", "text": "I can send <PERSON> slack message if you need me to", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1726074715.000000"}, "blocks": [{"type": "rich_text", "block_id": "H3n5R", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can send <PERSON> slack message if you need me to"}]}]}]}, {"ts": "1726074674.063329", "text": "ok then let's have <PERSON> be ready to join the call if needed", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fta3q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok then let's have <PERSON> be ready to join the call if needed"}]}]}]}, {"ts": "1726074634.157249", "text": "so we can likely use the time for that.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZbJX9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "so we can likely use the time for that."}]}]}]}, {"ts": "1726074618.765249", "text": "we need to ask about the merit view for bands, but other than that i think the ball is back in our court.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Mpvop", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we need to ask about the merit view for bands, but other than that i think the ball is back in our court."}]}]}]}, {"ts": "1726074596.756109", "text": "unless we have a specific agenda items for a the call today", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "opZOz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "unless we have a specific agenda items for a the call today"}]}]}]}, {"ts": "1726074576.084289", "text": "<@U07EJ2LP44S> If <PERSON> agrees to provide feedback on manager enablement during the call today, we should have <PERSON> be on the call and present her preliminary mock ups. Make sense?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MEc2W", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " If <PERSON> agrees to provide feedback on manager enablement during the call today, we should have <PERSON> be on the call and present her preliminary mock ups. Make sense?"}]}]}]}, {"ts": "1726054258.424419", "text": "<!here> I really need one discussion on below two requirements. Probably today\n• Cycle closure. There are two tickets with details, I am still bit confused confused with terminologies. Need help in getting more clarity.\n• Cycle builder enhacements. <@U04DS2MBWP4> Need to review designs as team and prioritize the scope.\n• Customer requirements open questions\n<@U04DS2MBWP4> Can we move OKR discussion to Friday.\nAlso We haven't discussed on customer requirements after 9/5 and Not sure if anything new coming up and there are still open questions. FYI... I am not available for Thursday meeting.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1726054258.424419", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1726056297.000000"}, "blocks": [{"type": "rich_text", "block_id": "mINGx", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I really need one discussion on below two requirements. Probably today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle closure. There are two tickets with details, I am still bit confused confused with terminologies. Need help in getting more clarity."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle builder enhacements. "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Need to review designs as team and prioritize the scope."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Customer requirements open questions"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Can we move OKR discussion to Friday.\nAlso We haven't discussed on customer requirements after 9/5 and Not sure if anything new coming up and there are still open questions. FYI... I am not available for Thursday meeting."}]}]}]}, {"ts": "**********.425459", "text": "<@U07EJ2LP44S> I was testing `Alayacare <> BambooHR` sync. We are not receiving email and comp data from their HRIS system. Could you please check with customer if the account they have authenticated with has required permissions for us to pull the email? The data pulled has 656 employees. Also please confirm if this count is correct.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.425459", "reply_count": 23, "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "TwEi/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I was testing "}, {"type": "text", "text": "Alayacare <> BambooHR", "style": {"code": true}}, {"type": "text", "text": " sync. We are not receiving email and comp data from their HRIS system. Could you please check with customer if the account they have authenticated with has required permissions for us to pull the email? The data pulled has 656 employees. Also please confirm if this count is correct."}]}]}]}, {"ts": "**********.794379", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> My email for SSO is changed from kapil.gupta to <mailto:<EMAIL>|<EMAIL>>.\n\nI am unable to login into any of the test, demo or production environments anymore. Can we please change my login email to <mailto:<EMAIL>|<EMAIL>> for all environements?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.794379", "reply_count": 3, "edited": {"user": "U07M6QKHUC9", "ts": "**********.000000"}, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "RaY1R", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " My email for SSO is changed from kapil.gupta to "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ".\n\nI am unable to login into any of the test, demo or production environments anymore. Can we please change my login email to "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " for all environements?"}]}]}]}, {"ts": "1726003831.876109", "text": "<@U0690EB5JE5> Draft for supporting base currency is here <https://docs.google.com/document/d/15rIlYquKvlR7HypDu8UsFd9wXnxdtVwHzKHnE3DqMdk/edit>. I have shared it with <PERSON> as well\ncc: <@U07EJ2LP44S> <@U07M6QKHUC9>", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "CV/ZC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Draft for supporting base currency is here "}, {"type": "link", "url": "https://docs.google.com/document/d/15rIlYquKvlR7HypDu8UsFd9wXnxdtVwHzKHnE3DqMdk/edit"}, {"type": "text", "text": ". I have shared it with <PERSON> as well\ncc: "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}]}]}]}, {"ts": "1725999425.113189", "text": "<@U04DKEFP1K8> Can we push ROI calculator today or tomorrow if today is not possible?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1725999425.113189", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "fiafm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can we push ROI calculator today or tomorrow if today is not possible?"}]}]}]}], "created_at": "2025-05-22T21:35:34.640468"}