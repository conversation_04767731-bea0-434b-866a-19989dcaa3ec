{"date": "2025-01-02", "channel_id": "C065QSSNH8A", "message_count": 13, "messages": [{"ts": "1735839765.813049", "text": "<@U0690EB5JE5> Expected behavior question:\n\nAs users submit recommendations and they go up the chain, each manager has to have all their team's managers submit before they themselves can submit up the chain. For example, Employee X reports to Employee Y. Employee X has 3 managers report to them. Those 3 managers have to submit and be approved before Employee <PERSON> can submit to employee Y.\n\nWhat is the expected behavior at the top of the hierarchy?  Not the final approval, but the top of the chain, where it will be submitted for final approval. Should all the hierarchy below have been submitted and approved, or can the final approver somehow override that?\n\nDiversified is reporting that their CEO (top of the chain, but NOT the final approver) was able to submit for final approval without everyone beneath them completing their steps. Is this a bug or expected behavior?\n\n'Mavericks group _(the one she is testing on)_ was complete and sent to <PERSON> (_the CEO_) as I was impersonating <PERSON> the system allowed me to submit all groups to Final Approver without them all being submitted to <PERSON>. Should the system allow that to happen?'", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1735839765.813049", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "mJP+6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Expected behavior question:\n\nAs users submit recommendations and they go up the chain, each manager has to have all their team's managers submit before they themselves can submit up the chain. For example, Employee X reports to Employee Y. Employee X has 3 managers report to them. Those 3 managers have to submit and be approved before Employee X can submit to employee Y.\n\nWhat is the expected behavior at the top of the hierarchy?  Not the final approval, but the top of the chain, where it will be submitted for final approval. Should all the hierarchy below have been submitted and approved, or can the final approver somehow override that?\n\nDiversified is reporting that their CEO (top of the chain, but NOT the final approver) was able to submit for final approval without everyone beneath them completing their steps. Is this a bug or expected behavior?\n\n'Mavericks group "}, {"type": "text", "text": "(the one she is testing on)", "style": {"italic": true}}, {"type": "text", "text": " was complete and sent to Rusty ("}, {"type": "text", "text": "the CEO", "style": {"italic": true}}, {"type": "text", "text": ") as I was impersonating <PERSON> the system allowed me to submit all groups to Final Approver without them all being submitted to <PERSON>. Should the system allow that to happen?'"}]}]}]}, {"ts": "1735837307.452099", "text": "<@U0690EB5JE5> Valgenesis, AlayaCare, Curana, Tithely and Diversifed should stay active. Let's disable everything else. Also for Alayacare, reduce the resources to minimal", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1735837307.452099", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "1kPYO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Valgenesis, AlayaCare, Curana, Tithely and Diversifed should stay active. Let's disable everything else. Also for Alayacare, reduce the resources to minimal"}]}]}]}, {"ts": "1735835416.874559", "text": "just updated to zoom", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "leU1C", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "just updated to zoom"}]}]}]}, {"ts": "1735835378.165689", "text": "<@U07M6QKHUC9> Are we going to use gmeet for the meeting?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+O51A", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Are we going to use gmeet for the meeting?"}]}]}]}, {"ts": "1735833773.557389", "text": "Do you want to use our regular link for that? We can just start it in half an hour.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1735833773.557389", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "jUbK+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you want to use our regular link for that? We can just start it in half an hour."}]}]}]}, {"ts": "1735793704.455749", "text": "Ok lets meet at 8:30 am pst", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "I8jJ8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok lets meet at 8:30 am pst"}]}]}]}, {"ts": "1735793518.007579", "text": "<@U07M6QKHUC9> We have a confusion with tab behaviour. I think we should meet your morning for this. This is reg. UX improvements.\ncc: <@U06HN8XDC5A>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1735793545.000000"}, "blocks": [{"type": "rich_text", "block_id": "U80Ot", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " We have a confusion with tab behaviour. I think we should meet your morning for this. This is reg. UX improvements.\ncc: "}, {"type": "user", "user_id": "U06HN8XDC5A"}]}]}]}, {"ts": "1735786994.311569", "text": "OK, if <PERSON> does not have any agenda will cancel the meeting. I’m trying to finish the paybands prd in the next three days.", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1735787006.000000"}, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "LJjnK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OK, if <PERSON> does not have any agenda will cancel the meeting. I’m trying to finish the pay"}, {"type": "text", "text": "bands prd "}, {"type": "text", "text": "in the next three days."}]}]}]}, {"ts": "1735785155.881459", "text": "<@U07M6QKHUC9> none for tomorrow’s stand up. But I do wanted to know the status of PRDs for other features.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "tnosS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " none for tomorrow’s stand up. But I do wanted to know the status of PRDs for other features."}]}]}]}, {"ts": "1735784305.453909", "text": "<@U07EJ2LP44S> <@U0690EB5JE5> do you have any agenda items for tomorrow’s stand-up?\n\nFriday’s agenda is demo of UX improvements ", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QToVd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " do you have any agenda items for tomorrow’s stand-up?\n\nFriday’s agenda is demo of UX improvements "}]}]}]}, {"ts": "1735767500.911679", "text": "Also, when will the columns work be in production? A couple of customers are hoping to create training material sooner rather than later with their settings.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pHxgf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also, when will the columns work be in production? A couple of customers are hoping to create training material sooner rather than later with their settings."}]}]}]}, {"ts": "1735767448.097159", "text": "<@U0690EB5JE5> how are we doing on the HRBP work? I’m hoping to give Valgenesis an update tomorrow.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1735767448.097159", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "yHKBt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " how are we doing on the HRBP work? I’m hoping to give Val<PERSON> an update tomorrow."}]}]}]}, {"ts": "1735767419.381219", "text": "Happy New Year! ", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hKgT9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Happy New Year! "}]}]}]}], "created_at": "2025-05-22T21:35:34.697409"}