#!/usr/bin/env python
"""
Combined Clean and Ingest Script for RAGSearch

This script provides a one-command solution to clean the database and ingest
fresh data. It combines the functionality of clean_database.py and ingest_data.py
with additional safety checks and rollback capabilities.

Usage:
    python scripts/clean_and_ingest.py --data-dir DATA_DIR [options]
"""

import argparse
import os
import sys
import django
import logging
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.db import transaction
from django.contrib.auth.models import User
from apps.accounts.models import Tenant

# Import our custom scripts
from clean_database import DatabaseCleaner
from ingest_data import DataIngester

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CleanAndIngestManager:
    """Manager for combined clean and ingest operations."""

    def __init__(
        self,
        tenant_slug: str = "default",
        user_email: str = "<EMAIL>",
        backup_enabled: bool = True
    ):
        """
        Initialize the clean and ingest manager.

        Args:
            tenant_slug: Tenant slug for operations
            user_email: User email for processing attribution
            backup_enabled: Whether to create backups before cleaning
        """
        self.tenant_slug = tenant_slug
        self.user_email = user_email
        self.backup_enabled = backup_enabled
        self.cleaner = None
        self.ingester = None
        self.operation_stats = {
            'start_time': None,
            'end_time': None,
            'cleaning_completed': False,
            'ingestion_completed': False,
            'total_cleaned': 0,
            'total_ingested': 0,
            'errors': []
        }

    def initialize_components(self) -> None:
        """Initialize cleaner and ingester components."""
        try:
            self.cleaner = DatabaseCleaner(tenant_slug=self.tenant_slug)
            self.ingester = DataIngester(tenant_slug=self.tenant_slug, user_email=self.user_email)
            logger.info("Initialized cleaner and ingester components")
        except Exception as e:
            logger.error(f"Failed to initialize components: {str(e)}")
            raise

    def validate_prerequisites(self, data_dir: str) -> bool:
        """
        Validate prerequisites for clean and ingest operation.

        Args:
            data_dir: Path to data directory

        Returns:
            bool: True if prerequisites are met
        """
        # Check data directory exists
        if not os.path.exists(data_dir):
            logger.error(f"Data directory does not exist: {data_dir}")
            return False

        # Check data directory has content
        data_path = Path(data_dir)
        if not any(data_path.iterdir()):
            logger.error(f"Data directory is empty: {data_dir}")
            return False

        # Check database connectivity
        try:
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            logger.info("Database connectivity verified")
        except Exception as e:
            logger.error(f"Database connectivity check failed: {str(e)}")
            return False

        # Check vector database connectivity
        try:
            from apps.core.utils.llama_index_vectorstore import get_vector_store
            vector_store = get_vector_store()
            logger.info("Vector database connectivity verified")
        except Exception as e:
            logger.warning(f"Vector database connectivity check failed: {str(e)}")
            # Don't fail on vector DB issues as it might not be critical

        return True

    def create_backup(self) -> Optional[str]:
        """
        Create a backup of current data (placeholder for future implementation).

        Returns:
            Optional[str]: Backup identifier or None if backup failed
        """
        if not self.backup_enabled:
            return None

        # TODO: Implement actual backup functionality
        # This could include:
        # - Database dump
        # - Vector database export
        # - Configuration backup

        logger.info("Backup functionality not yet implemented")
        return None

    def clean_database(self, postgres_only: bool = False, vector_only: bool = False) -> bool:
        """
        Clean the database using the DatabaseCleaner.

        Args:
            postgres_only: Clean only PostgreSQL database
            vector_only: Clean only vector database

        Returns:
            bool: True if cleaning was successful
        """
        try:
            logger.info("Starting database cleaning phase...")

            # Get pre-cleaning stats
            pre_stats = self.cleaner.get_cleaning_stats()
            self.operation_stats['total_cleaned'] = sum(pre_stats.values())

            if self.operation_stats['total_cleaned'] == 0:
                logger.info("Database is already clean")
                self.operation_stats['cleaning_completed'] = True
                return True

            # Perform cleaning
            self.cleaner.clean_all(postgres_only=postgres_only, vector_only=vector_only)
            self.operation_stats['cleaning_completed'] = True

            logger.info(f"Database cleaning completed successfully")
            return True

        except Exception as e:
            error_msg = f"Database cleaning failed: {str(e)}"
            logger.error(error_msg)
            self.operation_stats['errors'].append(error_msg)
            return False

    def ingest_data(
        self,
        data_dir: str,
        source_type: str = "auto",
        batch_size: int = 50,
        limit: Optional[int] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Ingest data using the DataIngester.

        Args:
            data_dir: Path to data directory
            source_type: Type of data source
            batch_size: Batch size for processing
            limit: Optional limit on documents to process
            config: Optional configuration overrides

        Returns:
            bool: True if ingestion was successful
        """
        try:
            logger.info("Starting data ingestion phase...")

            # Auto-detect source type if needed
            if source_type == "auto":
                data_path = Path(data_dir)
                if any(f.name.startswith("channel_") for f in data_path.iterdir() if f.is_dir()):
                    source_type = "slack"
                    logger.info("Auto-detected source type: slack")
                else:
                    source_type = "file"
                    logger.info("Auto-detected source type: file")

            # Perform ingestion
            if source_type == "slack":
                processed, failed = self.ingester.ingest_slack_data(
                    data_dir=data_dir,
                    batch_size=batch_size,
                    limit=limit,
                    config=config
                )
            elif source_type == "file":
                processed, failed = self.ingester.ingest_file_data(
                    data_dir=data_dir,
                    batch_size=batch_size,
                    limit=limit,
                    config=config
                )
            else:
                raise ValueError(f"Unsupported source type: {source_type}")

            self.operation_stats['total_ingested'] = processed
            self.operation_stats['ingestion_completed'] = True

            if failed > 0:
                error_msg = f"Ingestion completed with {failed} failures"
                logger.warning(error_msg)
                self.operation_stats['errors'].append(error_msg)
                return False

            logger.info(f"Data ingestion completed successfully: {processed} documents processed")
            return True

        except Exception as e:
            error_msg = f"Data ingestion failed: {str(e)}"
            logger.error(error_msg)
            self.operation_stats['errors'].append(error_msg)
            return False

    def execute_clean_and_ingest(
        self,
        data_dir: str,
        source_type: str = "auto",
        batch_size: int = 50,
        limit: Optional[int] = None,
        config: Optional[Dict[str, Any]] = None,
        postgres_only: bool = False,
        vector_only: bool = False,
        skip_cleaning: bool = False
    ) -> bool:
        """
        Execute the complete clean and ingest operation.

        Args:
            data_dir: Path to data directory
            source_type: Type of data source
            batch_size: Batch size for processing
            limit: Optional limit on documents to process
            config: Optional configuration overrides
            postgres_only: Clean only PostgreSQL database
            vector_only: Clean only vector database
            skip_cleaning: Skip the cleaning phase

        Returns:
            bool: True if operation was successful
        """
        self.operation_stats['start_time'] = datetime.now()

        try:
            # Initialize components
            self.initialize_components()

            # Validate prerequisites
            if not self.validate_prerequisites(data_dir):
                return False

            # Create backup if enabled
            backup_id = self.create_backup()
            if backup_id:
                logger.info(f"Created backup: {backup_id}")

            # Phase 1: Clean database (if not skipped)
            if not skip_cleaning:
                if not self.clean_database(postgres_only=postgres_only, vector_only=vector_only):
                    return False
            else:
                logger.info("Skipping database cleaning phase")
                self.operation_stats['cleaning_completed'] = True

            # Phase 2: Ingest data
            if not self.ingest_data(data_dir, source_type, batch_size, limit, config):
                return False

            self.operation_stats['end_time'] = datetime.now()
            return True

        except Exception as e:
            error_msg = f"Clean and ingest operation failed: {str(e)}"
            logger.error(error_msg)
            self.operation_stats['errors'].append(error_msg)
            self.operation_stats['end_time'] = datetime.now()
            return False

    def print_summary(self) -> None:
        """Print operation summary."""
        if self.operation_stats['start_time'] and self.operation_stats['end_time']:
            duration = self.operation_stats['end_time'] - self.operation_stats['start_time']
            logger.info(f"Total operation time: {duration.total_seconds():.2f} seconds")

        logger.info("=== Clean and Ingest Summary ===")
        logger.info(f"Cleaning Completed: {self.operation_stats['cleaning_completed']}")
        logger.info(f"Ingestion Completed: {self.operation_stats['ingestion_completed']}")
        logger.info(f"Total Items Cleaned: {self.operation_stats['total_cleaned']}")
        logger.info(f"Total Documents Ingested: {self.operation_stats['total_ingested']}")

        if self.operation_stats['errors']:
            logger.info(f"Errors Encountered: {len(self.operation_stats['errors'])}")
            for error in self.operation_stats['errors']:
                logger.error(f"  - {error}")
        else:
            logger.info("No errors encountered")

        logger.info("================================")


def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(description="Clean database and ingest fresh data")
    parser.add_argument("--data-dir", required=True, help="Path to data directory")
    parser.add_argument("--tenant", default="default", help="Tenant slug (default: default)")
    parser.add_argument("--user-email", default="<EMAIL>", help="User email for processing attribution")
    parser.add_argument("--source-type", choices=["slack", "file", "auto"], default="auto",
                       help="Type of data source (default: auto-detect)")
    parser.add_argument("--batch-size", type=int, default=50, help="Batch size for processing (default: 50)")
    parser.add_argument("--limit", type=int, help="Limit number of documents to process")
    parser.add_argument("--config-file", help="Path to JSON configuration file")
    parser.add_argument("--postgres-only", action="store_true", help="Clean only PostgreSQL database")
    parser.add_argument("--vector-only", action="store_true", help="Clean only vector database")
    parser.add_argument("--skip-cleaning", action="store_true", help="Skip database cleaning phase")
    parser.add_argument("--no-backup", action="store_true", help="Disable backup creation")
    parser.add_argument("--confirm", action="store_true", help="Skip confirmation prompts")

    args = parser.parse_args()

    # Validate arguments
    if args.postgres_only and args.vector_only:
        logger.error("Cannot specify both --postgres-only and --vector-only")
        sys.exit(1)

    # Validate data directory
    if not os.path.exists(args.data_dir):
        logger.error(f"Data directory does not exist: {args.data_dir}")
        sys.exit(1)

    # Load configuration from file if provided
    config = {}
    if args.config_file:
        if os.path.exists(args.config_file):
            with open(args.config_file, 'r') as f:
                config = json.load(f)
            logger.info(f"Loaded configuration from {args.config_file}")
        else:
            logger.error(f"Configuration file does not exist: {args.config_file}")
            sys.exit(1)

    # Confirmation prompt
    if not args.confirm and not args.skip_cleaning:
        logger.warning("This operation will clean the database and ingest fresh data.")
        logger.warning("All existing documents, chunks, and embeddings will be deleted.")
        response = input("Are you sure you want to continue? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            logger.info("Operation cancelled.")
            return

    # Create manager instance
    try:
        manager = CleanAndIngestManager(
            tenant_slug=args.tenant,
            user_email=args.user_email,
            backup_enabled=not args.no_backup
        )
    except Exception as e:
        logger.error(f"Failed to initialize manager: {str(e)}")
        sys.exit(1)

    # Execute operation
    try:
        logger.info("Starting clean and ingest operation...")

        success = manager.execute_clean_and_ingest(
            data_dir=args.data_dir,
            source_type=args.source_type,
            batch_size=args.batch_size,
            limit=args.limit,
            config=config,
            postgres_only=args.postgres_only,
            vector_only=args.vector_only,
            skip_cleaning=args.skip_cleaning
        )

        manager.print_summary()

        if success:
            logger.info("Clean and ingest operation completed successfully!")
        else:
            logger.error("Clean and ingest operation failed!")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.warning("Operation interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
