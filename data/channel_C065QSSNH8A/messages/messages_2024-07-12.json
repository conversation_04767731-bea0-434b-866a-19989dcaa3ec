{"date": "2024-07-12", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1720794218.966829", "text": "We can explore that", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FofRq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can explore that"}]}]}]}, {"ts": "1720765946.676369", "text": "<@U04DS2MBWP4> Me and <@U04DKEFP1K8> realized that HR Admin page is redundant with People insights and Merit view together. I am thinking we should add missing things to People insights and get rid off HR admin page unless we have different information to present there.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1720765967.000000"}, "blocks": [{"type": "rich_text", "block_id": "1TKk4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Me and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " realized that HR Admin page is redundant with People insights and Merit view together. I am thinking we should add missing things to People insights and get rid off HR admin page unless we have different information to present there."}]}]}]}, {"ts": "1720726135.279609", "text": "It will have to tomorrow before he leave for his vacation. He said he is available tomorrow anytime after 10:30 am", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3Qb82", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It will have to tomorrow before he leave for his vacation. He said he is available tomorrow anytime after 10:30 am"}]}]}]}, {"ts": "1720726107.951029", "text": "<@U04DKEFP1K8> I chatted with <PERSON>. He can also complement your QA on <PERSON><PERSON> and provide 2nd set of eyes. He is traveling for 2 weeks starting Saturday but he can still do the QA while in the plane. Since he is is a comp expert, I his feedback will be quite helpful esp when <PERSON><PERSON> is a little but more complex.\n\ncan you please reach out to him and set up a 30 mins with him tomorrow  to walk him thru the <PERSON><PERSON>'s environment? He hasn't seen the Merit 2.0 yet so you will have to walk him through the new worflow as well. His email is <mailto:<EMAIL>|<EMAIL>>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1720726107.951029", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "RIjT0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I chatted with <PERSON>. He can also complement your QA on <PERSON><PERSON> and provide 2nd set of eyes. He is traveling for 2 weeks starting Saturday but he can still do the QA while in the plane. Since he is is a comp expert, I his feedback will be quite helpful esp when <PERSON><PERSON> is a little but more complex.\n\ncan you please reach out to him and set up a 30 mins with him tomorrow  to walk him thru the <PERSON><PERSON>'s environment? He hasn't seen the Merit 2.0 yet so you will have to walk him through the new worflow as well. His email is "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}]}]}]}], "created_at": "2025-05-22T21:35:34.620489"}