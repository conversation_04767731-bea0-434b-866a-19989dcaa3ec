{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2024-02", "message_count": 92, "messages": [{"ts": "1706740686.027659", "text": "<@U0658EW4B8D> <@U065H3M6WJV> Here is 1st of 2 updates for Adjustment letter:\nMultiple bug fixes request under <https://compiify.atlassian.net/browse/COM-2145> have been deployed on qa environment.\n\nPending items i am working on: Additional template required for <PERSON>'s testing. ETA: end of the day. I will send an update once the remaining templated are uploaded on the env.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1706740686.027659", "reply_count": 3, "edited": {"user": "U04DKEFP1K8", "ts": "1706740699.000000"}, "reactions": [{"name": "gratitude-thank-you", "users": ["U065H3M6WJV", "U0658EW4B8D"], "count": 2}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12206::674baf90c08911ee90696bacf15c48de", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2145?atlOrigin=eyJpIjoiN2Y4Yjc4NDlmYjg3NGMyNjhkYzZlNTI2OTUxODM4OTIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2145 Neuroflow UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12206::674baf92c08911ee90696bacf15c48de", "elements": [{"type": "mrkdwn", "text": "Status: *In Progress*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12206::674baf91c08911ee90696bacf15c48de", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12206\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12206\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2145", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "v/Ou3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Here is 1st of 2 updates for Adjustment letter:\nMultiple bug fixes request under "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2145"}, {"type": "text", "text": " have been deployed on qa environment.\n\nPending items i am working on: Additional template required for <PERSON>'s testing. ETA: end of the day. I will send an update once the remaining templated are uploaded on the env."}]}]}]}, {"ts": "1706752612.441269", "text": "Priorities for Eng for the next day:\n• :repeat: Password reset flow (<https://compiify.atlassian.net/browse/COM-2240|COM-2240>) _(I know we're making progress on this and it's expected to be a multi-day effort_ :slightly_smiling_face: _)_\n• SDF: Additional columns for previous salary &amp; raise date (<https://compiify.atlassian.net/browse/COM-2232|COM-2232>)\n• Bulk Actions for adjustment letters (<https://compiify.atlassian.net/browse/COM-2252|COM-2252>)\n• Adjustment letter visibility / privacy (<https://compiify.atlassian.net/browse/COM-2257|COM-2257>)\n• Errors while approving/overriding on QA env (<https://compiify.atlassian.net/browse/COM-2261|COM-2261>)\n• Job level filter for Adjustment letters (<https://compiify.atlassian.net/browse/COM-2250|COM-2250>)\nThen, continue with <https://compiify.atlassian.net/browse/COM-2087|Wave 3> from the top of the list, especially the Reports-related items.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eMc3D", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Password reset flow ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2240", "text": "COM-2240"}, {"type": "text", "text": ") "}, {"type": "text", "text": "(I know we're making progress on this and it's expected to be a multi-day effort ", "style": {"italic": true}}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642", "style": {"italic": true}}, {"type": "text", "text": " )", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF: Additional columns for previous salary & raise date ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2232", "text": "COM-2232"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Bulk Actions for adjustment letters ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2252", "text": "COM-2252"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Adjustment letter visibility / privacy ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2257", "text": "COM-2257"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Errors while approving/overriding on QA env ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2261", "text": "COM-2261"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Job level filter for Adjustment letters ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2250", "text": "COM-2250"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nThen, continue with "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " from the top of the list, especially the Reports-related items."}]}]}]}, {"ts": "1706820381.355689", "text": "<@U04DKEFP1K8> The QA environment should be ready for any testing <@U0658EW4B8D> was supposed to do on adjustment letters today, right? (Did we cover those additional 2 cases you previously said to exclude?)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706820381.355689", "reply_count": 16, "blocks": [{"type": "rich_text", "block_id": "LDME5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " The QA environment should be ready for any testing "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " was supposed to do on adjustment letters today, right? (Did we cover those additional 2 cases you previously said to exclude?)"}]}]}]}, {"ts": "1706836332.808979", "text": "Priorities for Eng for the next day:\n• :repeat: Password reset flow (<https://compiify.atlassian.net/browse/COM-2240|COM-2240>) \n• :repeat: Bulk Actions for adjustment letters (<https://compiify.atlassian.net/browse/COM-2252|COM-2252>) _(BE completed, need FE next)_\n• Incorrect salary increase in adjustment letter (<https://compiify.atlassian.net/browse/COM-2267|COM-2267>)\n• Finish fixes for downloadable reports in Wave 3 (<https://compiify.atlassian.net/browse/COM-2122|COM-2122>, <https://compiify.atlassian.net/browse/COM-2129|COM-2129>, <https://compiify.atlassian.net/browse/COM-2124|COM-2124>, <https://compiify.atlassian.net/browse/COM-2123|COM-2123>, <https://compiify.atlassian.net/browse/COM-2126|COM-2126>, <https://compiify.atlassian.net/browse/COM-2120|COM-2120>)\n• Update Total Rewards values (<https://compiify.atlassian.net/browse/COM-2268|COM-2268>)\n• :repeat: Errors while approving/overriding on QA env (<https://compiify.atlassian.net/browse/COM-2261|COM-2261>)\n• :repeat: Job level filter for Adjustment letters (<https://compiify.atlassian.net/browse/COM-2250|COM-2250>)\nThen, continue with <https://compiify.atlassian.net/browse/COM-2087|Wave 3> from the top to bottom.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706836332.808979", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Mlyc6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Password reset flow ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2240", "text": "COM-2240"}, {"type": "text", "text": ") "}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Bulk Actions for adjustment letters ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2252", "text": "COM-2252"}, {"type": "text", "text": ") "}, {"type": "text", "text": "(BE completed, need FE next)", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Incorrect salary increase in adjustment letter ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2267", "text": "COM-2267"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Finish fixes for downloadable reports in Wave 3 ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2122", "text": "COM-2122"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2129", "text": "COM-2129"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2124", "text": "COM-2124"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2123", "text": "COM-2123"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2126", "text": "COM-2126"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2120", "text": "COM-2120"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Update Total Rewards values ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2268", "text": "COM-2268"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Errors while approving/overriding on QA env ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2261", "text": "COM-2261"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Job level filter for Adjustment letters ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2250", "text": "COM-2250"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nThen, continue with "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " from the top to bottom."}]}]}]}, {"ts": "1706836884.231359", "text": "Mainly just FYI: Here's the \"<https://docs.google.com/presentation/d/1ZR5fmOfrSw35ALW68KDMceXTG3_FjGKOkuEqSTUYoJs/edit?usp=sharing|Quick Guide>\" I put together for <PERSON><PERSON> to use for Stellar managers. I'll see if she feels this is enough or has feedback. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706836884.231359", "reply_count": 9, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4", "U04DKEFP1K8"], "count": 2}], "files": [{"id": "F06HHAJA3EU", "created": 1706836885, "timestamp": 1706836885, "name": "SDF: Quick Guide to Compiify Planning", "title": "SDF: Quick Guide to Compiify Planning", "mimetype": "application/vnd.google-apps.presentation", "filetype": "gpres", "pretty_type": "Google Slides", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 14280, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1ZR5fmOfrSw35ALW68KDMceXTG3_FjGKOkuEqSTUYoJs", "external_url": "https://docs.google.com/presentation/d/1ZR5fmOfrSw35ALW68KDMceXTG3_FjGKOkuEqSTUYoJs/edit?usp=sharing", "url_private": "https://docs.google.com/presentation/d/1ZR5fmOfrSw35ALW68KDMceXTG3_FjGKOkuEqSTUYoJs/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_360.png", "thumb_360_w": 360, "thumb_360_h": 203, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_480.png", "thumb_480_w": 480, "thumb_480_h": 270, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_720.png", "thumb_720_w": 720, "thumb_720_h": 405, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_800.png", "thumb_800_w": 800, "thumb_800_h": 450, "original_w": 800, "original_h": 450, "thumb_tiny": "AwAbADDToophL54AI96AH0VHmTI+VcfWjMg7KfxoAkopgL7hlRj1Bp9ABRRRQA3Yv90UbV/uinUUAFFFFAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06HHAJA3EU/sdf__quick_guide_to_compiify_planning", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "yYFre", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Mainly just FYI: Here's the \""}, {"type": "link", "url": "https://docs.google.com/presentation/d/1ZR5fmOfrSw35ALW68KDMceXTG3_FjGKOkuEqSTUYoJs/edit?usp=sharing", "text": "Quick Guide"}, {"type": "text", "text": "\" I put together for <PERSON><PERSON> to use for Stellar managers. I'll see if she feels this is enough or has feedback. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1706894408.918929", "text": "Hey <@U04DKEFP1K8>, one of today's Loom updates reminded me that we need to improve our hygiene on customer data.\n\nSome options:\n• Ensure external contractors never have actual customer data in their dev environments\n• Ensure screenshots &amp; videos always use stricter sharing permissions (vs \"anyone with link\") \nWhat do you think is the best way to tighten that up?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706894408.918929", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "DNTod", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": ", one of today's Loom updates reminded me that we need to improve our hygiene on customer data.\n\nSome options:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ensure external contractors never have actual customer data in their dev environments"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Ensure screenshots & videos always use stricter sharing permissions (vs \"anyone with link\") "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nWhat do you think is the best way to tighten that up?"}]}]}]}, {"ts": "1706911885.367549", "text": "We are keeping neuroflow's infra turned on or can we turn it off if they are not using?", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NXKH+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We are keeping neuroflow's infra turned on or can we turn it off if they are not using?"}]}]}]}, {"ts": "1706912236.404619", "text": "<@U04DKEFP1K8> Keep it at least until next Tuesday, when we have our beta follow-up call with them", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Bv2No", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Keep it at least until next Tuesday, when we have our beta follow-up call with them"}]}]}]}, {"ts": "1706918738.253719", "text": "<!here> staging upgrade is completed. <PERSON> please verify if all related items are working as expected.\n\nKnown issue: Flag is appears for all employees when range is set for recommendation instead of target.\n                        Need to update compensation data to put some data in column last raise date and previous salary", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1706918738.253719", "reply_count": 6, "reactions": [{"name": "thank-you-colors", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "KOcGa", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " staging upgrade is completed. <PERSON> please verify if all related items are working as expected.\n\nKnown issue: Flag is appears for all employees when range is set for recommendation instead of target.\n                        Need to update compensation data to put some data in column last raise date and previous salary"}]}]}]}, {"ts": "1707154841.785869", "text": "<@U04DKEFP1K8> Is there any issue if I close the \"CFY Sprint 15\" that's currently open in JIRA? It seems that this is older / no longer relevant, but please let me know if there would be any problem.\n\nI'm hoping to come up with a better way for us to track backlog & immediate priorities, and I think a Kanban board, or maybe just another version of a Scrum board like this might help. Also curious if <@U0690EB5JE5> has suggestions on how to keep track of daily priorities for an eng team when things are moving rapidly. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1707154841.785869", "reply_count": 6, "edited": {"user": "U065H3M6WJV", "ts": "1707154918.000000"}, "blocks": [{"type": "rich_text", "block_id": "dPsn+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Is there any issue if I close the \"CFY Sprint 15\" that's currently open in JIRA? It seems that this is older / no longer relevant, but please let me know if there would be any problem.\n\nI'm hoping to come up with a better way for us to track backlog & immediate priorities, and I think a Kanban board, or maybe just another version of a Scrum board like this might help. Also curious if "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " has suggestions on how to keep track of daily priorities for an eng team when things are moving rapidly. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1707161273.457169", "text": "Hey all: what's the intended meaning of the \"Current Equity\" column?\n• Is it supposed to be the sum of the \"Vested Equity\" and \"Unvested Equity\" columns, or some calculation?\n• If it is just the sum of Vested &amp; Unvested, do we need all 3 of these columns?\nThis is just a rough mock as I'm playing around with the data, and how we would be able to show % vested in addition to total granted equity:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1707161273.457169", "reply_count": 3, "files": [{"id": "F06HYT68472", "created": 1707161267, "timestamp": 1707161267, "name": "Screenshot 2024-02-05 at 11.25.05 AM.png", "title": "Screenshot 2024-02-05 at 11.25.05 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 110177, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06HYT68472/screenshot_2024-02-05_at_11.25.05___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06HYT68472/download/screenshot_2024-02-05_at_11.25.05___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_360.png", "thumb_360_w": 360, "thumb_360_h": 71, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_480.png", "thumb_480_w": 480, "thumb_480_h": 95, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_720.png", "thumb_720_w": 720, "thumb_720_h": 142, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_800.png", "thumb_800_w": 800, "thumb_800_h": 158, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_960.png", "thumb_960_w": 960, "thumb_960_h": 190, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HYT68472-d711b5cfa4/screenshot_2024-02-05_at_11.25.05___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 202, "original_w": 2348, "original_h": 464, "thumb_tiny": "AwAJADDS/Gj8aWigBPxo/GlooASilooA/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06HYT68472/screenshot_2024-02-05_at_11.25.05___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06HYT68472-c4ee29c046", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "KBgbt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey all: what's the intended meaning of the \"Current Equity\" column?\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is it supposed to be the sum of the \"Vested Equity\" and \"Unvested Equity\" columns, or some calculation?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "If it is just the sum of Vested & Unvested, do we need all 3 of these columns?"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nThis is just a rough mock as I'm playing around with the data, and how we would be able to show % vested in addition to total granted equity:"}]}]}]}, {"ts": "1707181078.853429", "text": "<@U04DKEFP1K8> Eng priorities for next day are updated on the <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities board>\n• Highest priority is *Password Reset flow*, since we're about to open up to ~10 employees at SDF with local login\n• If all tickets on this board are done, or someone is done and all others are assigned, they can continue working on Wave 3 tickets. \n• Let me know if this format works for you &amp; team, or we can adjust if there are concerns!", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1707181078.853429", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "pAUuV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are updated on the "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities board"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Highest priority is"}, {"type": "text", "text": " Password Reset flow", "style": {"bold": true}}, {"type": "text", "text": ", since we're about to open up to ~10 employees at SDF with local login"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "If all tickets on this board are done, or someone is done and all others are assigned, they can continue working on Wave 3 tickets. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me know if this format works for you & team, or we can adjust if there are concerns!"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1707272300.383209", "text": "<@U04DKEFP1K8> Eng priorities for next day are updated on the <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities board>\n• Highest priority continues to be *Password Reset flow*, since we're about to open up to ~10 employees at SDF with local login\n• I had to reopen at least one ticket - comments provided for what's missing\n• Pulled in a couple new things that will be annoying for SDF users and/or distracting in demos\n• If all tickets on this board are done, or someone is done and all others are assigned, they can continue working on <https://compiify.atlassian.net/browse/COM-2087|Wave 3> tickets.\n", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Zs9R+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are updated on the "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities board"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Highest priority continues to be"}, {"type": "text", "text": " Password Reset flow", "style": {"bold": true}}, {"type": "text", "text": ", since we're about to open up to ~10 employees at SDF with local login"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "I had to reopen at least one ticket - comments provided for what's missing"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Pulled in a couple new things that will be annoying for SDF users and/or distracting in demos"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "If all tickets on this board are done, or someone is done and all others are assigned, they can continue working on "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " tickets."}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "**********.664119", "text": "<@U04DS2MBWP4> Any questions for <PERSON> today? If not, I'll probably spend time on comp cycle builder and/or adjustment letters.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.664119", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "GGRpm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Any questions for <PERSON> today? If not, I'll probably spend time on comp cycle builder and/or adjustment letters."}]}]}]}, {"ts": "**********.690089", "text": "Integration-related question: Merge lists around <https://www.merge.dev/categories/hr-payroll-api|60 different HRIS and payroll providers >that they'll support. Do we expect to show all of these logos in our integrations section, or should we just highlight the top 10-20 using logos and have the others less prominent?\n\nAnd if we wanted to highlight a shorter list, which ones are most important to show?\n• ADP\n• BambooHR\n• Gusto\n• HiBob\n• Paylocity\n• UKG\n• Workday\n• Zenefits\n• ?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "guWMJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Integration-related question: <PERSON><PERSON> lists around "}, {"type": "link", "url": "https://www.merge.dev/categories/hr-payroll-api", "text": "60 different HRIS and payroll providers "}, {"type": "text", "text": "that they'll support. Do we expect to show all of these logos in our integrations section, or should we just highlight the top 10-20 using logos and have the others less prominent?\n\nAnd if we wanted to highlight a shorter list, which ones are most important to show?\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ADP"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "BambooHR"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON>"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON>"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Paylocity"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "UKG"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Workday"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Zenefits"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1707348990.508609", "text": "Rippling", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "utHTH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Rippling"}]}]}]}, {"ts": "1707349015.425919", "text": "Add SFTP connector too", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9GE+C", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Add SFTP connector too"}]}]}]}, {"ts": "1707349121.606599", "text": "They don't actually support <PERSON><PERSON><PERSON> though, unless I'm mistaken? Wasn't <PERSON><PERSON><PERSON> the one that is retracting API access / not providing it to new clients?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wDNGw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They don't actually support <PERSON><PERSON><PERSON> though, unless I'm mistaken? Wasn't <PERSON><PERSON><PERSON> the one that is retracting API access / not providing it to new clients?"}]}]}]}, {"ts": "1707349400.292739", "text": "Thats what they said but we might have to build that integration natively", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zpq4O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thats what they said but we might have to build that integration natively"}]}]}]}, {"ts": "1707349500.809599", "text": "Ok but for now -- in terms of what we can \"light up\" when Merge is online -- which of their ~60 supported ones do you want to have most prominent? Or do you prefer to present everything with equal real estate?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nBpDm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok but for now -- in terms of what we can \"light up\" when Merge is online -- which of their ~60 supported ones do you want to have most prominent? Or do you prefer to present everything with equal real estate?"}]}]}]}, {"ts": "1707349985.496729", "text": "<@U065H3M6WJV> I think we shouldn't show all the integrations that supported by Merge but by us as every integration though effort is very less it would need an effort to test enable any integration.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+R4jD", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I think we shouldn't show all the integrations that supported by Merge but by us as every integration though effort is very less it would need an effort to test enable any integration."}]}]}]}, {"ts": "1707350036.424309", "text": "And reg. <PERSON><PERSON><PERSON>, from what I heard, <PERSON><PERSON> is working with <PERSON><PERSON><PERSON> on a formal partnership.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1707350075.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "OUi8a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And reg. <PERSON><PERSON><PERSON>, from what I heard, <PERSON><PERSON> is working with <PERSON><PERSON><PERSON> on a formal partnership."}]}]}]}, {"ts": "1707350049.737899", "text": "there is no timeline confirmed on that though", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1707350110.000000"}, "blocks": [{"type": "rich_text", "block_id": "iAyom", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "there is no timeline confirmed on that though"}]}]}]}, {"ts": "1707350140.992979", "text": "60 might be too much given we are so early and it can create a dent on our credibility ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "bi/nL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "60 might be too much given we are so early and it can create a dent on our credibility "}]}]}]}, {"ts": "1707350169.634329", "text": "I think 10 to 15 is what we start with and then increase it gradually ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zVmxq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think 10 to 15 is what we start with and then increase it gradually "}]}]}]}, {"ts": "1707350315.598739", "text": "Yes. Also as I said earlier should we show the integrations as well that we haven't tested?. Example. ADP we need to test the integration before we show it to customers on supported list.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hxsfR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes. Also as I said earlier should we show the integrations as well that we haven't tested?. Example. ADP we need to test the integration before we show it to customers on supported list."}]}]}]}, {"ts": "1707350331.595019", "text": "but we can be flexible there as we are still early", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1707350331.595019", "reply_count": 2, "edited": {"user": "U0690EB5JE5", "ts": "1707350382.000000"}, "blocks": [{"type": "rich_text", "block_id": "MO/Ll", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "but we can be flexible there as we are still early"}]}]}]}, {"ts": "1707351552.438879", "text": "FYI: Here's what \"<https://docs.google.com/presentation/d/1eKN9U_ENFZB_kPUm3c0KNUI3awMe31_Fv_xND6uTCB4/edit#slide=id.g2317715dbc6_0_784|Compiify training>\" looks like for SDF's leaders. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1707351552.438879", "reply_count": 5, "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}, {"name": "moneybag", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "blob-yes", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06HBQ0SZD5", "created": 1707351554, "timestamp": 1707351554, "name": "Manager Guide to Compiify", "title": "Manager Guide to Compiify", "mimetype": "application/vnd.google-apps.presentation", "filetype": "gpres", "pretty_type": "Google Slides", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 51632, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1eKN9U_ENFZB_kPUm3c0KNUI3awMe31_Fv_xND6uTCB4", "external_url": "https://docs.google.com/presentation/d/1eKN9U_ENFZB_kPUm3c0KNUI3awMe31_Fv_xND6uTCB4/edit#slide=id.g2317715dbc6_0_784", "url_private": "https://docs.google.com/presentation/d/1eKN9U_ENFZB_kPUm3c0KNUI3awMe31_Fv_xND6uTCB4/edit#slide=id.g2317715dbc6_0_784", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_360.png", "thumb_360_w": 360, "thumb_360_h": 203, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_480.png", "thumb_480_w": 480, "thumb_480_h": 270, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_720.png", "thumb_720_w": 720, "thumb_720_h": 405, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HBQ0SZD5-b69a4f5326/manager_guide_to_compiify_800.png", "thumb_800_w": 800, "thumb_800_h": 450, "original_w": 800, "original_h": 450, "thumb_tiny": "AwAbADC3g56/pS1VnT9+3Lghc8dKiPX70tcvs2Xcv0VUSIzs5yydyKsxp5aBdxb3NTKNhpjqKKKkBdx9aNx9TSUU7sBdx9aSiii9wCiiikB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06HBQ0SZD5/manager_guide_to_compiify", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "FYI: Here's what \""}, {"type": "link", "url": "https://docs.google.com/presentation/d/1eKN9U_ENFZB_kPUm3c0KNUI3awMe31_Fv_xND6uTCB4/edit#slide=id.g2317715dbc6_0_784", "text": "Compiify training"}, {"type": "text", "text": "\" looks like for SDF's leaders. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1707353972.193739", "text": "<@U04DKEFP1K8> Eng priorities for next day are updated on the <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities board>\n• Highest priority continues to be *Password Reset flow*, since we're about to open up to ~10 employees at SDF with local login\n• Make sure the \"In progress\" items are treated as higher priority to complete before pulling any new \"To dos\" since none of those are higher priority tix\n• Pulled in some of the next several tickets in priority from Wave 3 to make sure we're focused on getting through that wave\n• If all tickets on this board are done, or someone is done and all others are assigned, they can continue working on <https://compiify.atlassian.net/browse/COM-2087|Wave 3> tickets.\n", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FnW16", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are updated on the "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities board"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Highest priority continues to be"}, {"type": "text", "text": " Password Reset flow", "style": {"bold": true}}, {"type": "text", "text": ", since we're about to open up to ~10 employees at SDF with local login"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Make sure the \"In progress\" items are treated as higher priority to complete before pulling any new \"To dos\" since none of those are higher priority tix"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Pulled in some of the next several tickets in priority from Wave 3 to make sure we're focused on getting through that wave"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "If all tickets on this board are done, or someone is done and all others are assigned, they can continue working on "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " tickets."}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "**********.307299", "text": "Sharing for visibility: We're about a day and a half overdue for bringing up SDF's full cycle. <PERSON><PERSON><PERSON><PERSON> is currently troubleshooting a showstopper bug, which seems to be related to recent changes that were part of SDF's customizations.\n• They'd asked us to include \"last raise date\" and \"previous salary\" info in the Merit view\n• This was working when it was patched in, but seems to have introduced an issue when there was a full reload of data from scratch. \n<PERSON><PERSON>'s been kind and patient and we're giving her updates / ETAs along the way.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.307299", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "3DHjy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sharing for visibility: We're about a day and a half overdue for bringing up SDF's full cycle. <PERSON><PERSON><PERSON><PERSON> is currently troubleshooting a showstopper bug, which seems to be related to recent changes that were part of SDF's customizations.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They'd asked us to include \"last raise date\" and \"previous salary\" info in the Merit view"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "This was working when it was patched in, but seems to have introduced an issue when there was a full reload of data from scratch. "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n<PERSON><PERSON>'s been kind and patient and we're giving her updates / ETAs along the way."}]}]}]}, {"ts": "**********.121029", "text": "Big thanks to <@U04DKEFP1K8> <@U065H3M6WJV> for working round the clock to get them started and to give them the amazing customer experience that they could only get with Compiify.:pray::heart:", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "panda_work", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "b04d9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Big thanks to "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " for working round the clock to get them started and to give them the amazing customer experience that they could only get with Compiify."}, {"type": "emoji", "name": "pray", "unicode": "1f64f"}, {"type": "emoji", "name": "heart", "unicode": "2764-fe0f"}]}]}]}, {"ts": "**********.157429", "text": "<@U04DKEFP1K8> Eng priorities for next day are updated on the <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities board>\n• All of Katya's product changes/bugs have been added; let's treat those as highest priority for turnaround by tomorrow.\n• Continue finishing the Password reset flow", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "qqfK5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are updated on the "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities board"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All of Katya's product changes/bugs have been added; let's treat those as highest priority for turnaround by tomorrow."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Continue finishing the Password reset flow"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1707503968.042759", "text": "<@U065H3M6WJV> are we all set for SDF or are we still making product changes for them?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1707503968.042759", "reply_count": 10, "blocks": [{"type": "rich_text", "block_id": "vlSy6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " are we all set for SDF or are we still making product changes for them?"}]}]}]}, {"ts": "1707796974.451969", "text": "<@U04DKEFP1K8> <@U04DS2MBWP4> I pulled together a list of the requested changes (including both customizations &amp; bug fixes) that SDF has asked for in <https://docs.google.com/spreadsheets/d/1R36110yTrU-jlpKTVJA4qr9Ndx-AjqS6wjj0p_KEtLM/edit?usp=sharing|this sheet>. This might be useful for us to walk through to understand where we could do better with automation or self-service for future customers, as well as understanding some common themes in product feature gaps.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1707796974.451969", "reply_count": 3, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F06JDFTDRFX", "created": 1707796977, "timestamp": 1707796977, "name": "SDF Change Log", "title": "SDF Change Log", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1R36110yTrU-jlpKTVJA4qr9Ndx-AjqS6wjj0p_KEtLM", "external_url": "https://docs.google.com/spreadsheets/d/1R36110yTrU-jlpKTVJA4qr9Ndx-AjqS6wjj0p_KEtLM/edit?usp=sharing", "url_private": "https://docs.google.com/spreadsheets/d/1R36110yTrU-jlpKTVJA4qr9Ndx-AjqS6wjj0p_KEtLM/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSZsUAk0j9qBQA7miiigAooooAa/agcUOcYoHPWgBR9c0tIAB0paACiiigBr44zQOlKy5oA96AAZ70tFFABRRRQB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06JDFTDRFX/sdf_change_log", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Rko8X", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I pulled together a list of the requested changes (including both customizations & bug fixes) that SDF has asked for in "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1R36110yTrU-jlpKTVJA4qr9Ndx-AjqS6wjj0p_KEtLM/edit?usp=sharing", "text": "this sheet"}, {"type": "text", "text": ". This might be useful for us to walk through to understand where we could do better with automation or self-service for future customers, as well as understanding some common themes in product feature gaps."}]}]}]}, {"ts": "1707797015.000719", "text": "<@U04DKEFP1K8> Eng priorities for next day are <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|updated on the board>.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Guzzi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "updated on the board"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1707801734.363119", "text": "Thanks, <PERSON>\nOn a separate note, I think we should definitely hide the bonus columns for SDF. I think we did that when we first created an environment for them. So I don’t know why it wasn’t done in this environment.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1707801734.363119", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "J73sb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks, <PERSON>\nOn a separate note, I think we should definitely hide the bonus columns for SDF. I think we did that when we first created an environment for them. So I don’t know why it wasn’t done in this environment."}]}]}]}, {"ts": "1707853138.505419", "text": "<@U065H3M6WJV> we are no longer da-test environment. I am planning to purge it at the end of the week. ok?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1707853138.505419", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "mDr/X", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " we are no longer da-test environment. I am planning to purge it at the end of the week. ok?"}]}]}]}, {"ts": "1707869999.941019", "text": "<@U04DS2MBWP4> do you want to keep neuroflow prod environment around until we negotiate with them?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1707869999.941019", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "gRHxV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " do you want to keep neuroflow prod environment around until we negotiate with them?"}]}]}]}, {"ts": "1707878168.460559", "text": "<@U04DKEFP1K8> Eng priorities for next day are <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|on the board>.\n\nI didn't add anything new today because it looks like we still have many leftover in progress or still not started yet. But if anyone needs assignments and can't take something from the current board, they can continue working on <https://compiify.atlassian.net/browse/COM-2087|Wave 3> or move to <https://compiify.atlassian.net/browse/COM-2088|Wave 4> if that's the next available.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HCN+L", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "on the board"}, {"type": "text", "text": ".\n\nI didn't add anything new today because it looks like we still have many leftover in progress or still not started yet. But if anyone needs assignments and can't take something from the current board, they can continue working on "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " or move to "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2088", "text": "Wave 4"}, {"type": "text", "text": " if that's the next available."}]}]}]}, {"ts": "1707932990.275789", "text": "The good news is ... we have Clarity recordings for SDF usage!\n\nThe bad news is... the display isn't rendering correctly in Clarity playback. <@U04DKEFP1K8> is it possible the CSS for production environments isn't publicly accessible? (See <https://learn.microsoft.com/en-us/clarity/session-recordings/troubleshooting-recordings|this article> I found while troubleshooting.)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1707932990.275789", "reply_count": 4, "files": [{"id": "F06JTTH3QKV", "created": 1707932985, "timestamp": 1707932985, "name": "Screenshot 2024-02-14 at 9.48.13 AM.png", "title": "Screenshot 2024-02-14 at 9.48.13 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 165114, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06JTTH3QKV/screenshot_2024-02-14_at_9.48.13___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06JTTH3QKV/download/screenshot_2024-02-14_at_9.48.13___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JTTH3QKV-b3b1614cda/screenshot_2024-02-14_at_9.48.13___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JTTH3QKV-b3b1614cda/screenshot_2024-02-14_at_9.48.13___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JTTH3QKV-b3b1614cda/screenshot_2024-02-14_at_9.48.13___am_360.png", "thumb_360_w": 360, "thumb_360_h": 173, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JTTH3QKV-b3b1614cda/screenshot_2024-02-14_at_9.48.13___am_480.png", "thumb_480_w": 480, "thumb_480_h": 231, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JTTH3QKV-b3b1614cda/screenshot_2024-02-14_at_9.48.13___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JTTH3QKV-b3b1614cda/screenshot_2024-02-14_at_9.48.13___am_720.png", "thumb_720_w": 720, "thumb_720_h": 347, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JTTH3QKV-b3b1614cda/screenshot_2024-02-14_at_9.48.13___am_800.png", "thumb_800_w": 800, "thumb_800_h": 385, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JTTH3QKV-b3b1614cda/screenshot_2024-02-14_at_9.48.13___am_960.png", "thumb_960_w": 960, "thumb_960_h": 462, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JTTH3QKV-b3b1614cda/screenshot_2024-02-14_at_9.48.13___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 493, "original_w": 3070, "original_h": 1478, "thumb_tiny": "AwAXADDSHSlpAe1LQAUUUUAFFFFAAPeiiigAooooAKKKKAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06JTTH3QKV/screenshot_2024-02-14_at_9.48.13___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06JTTH3QKV-f734eb417f", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "bqYDf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The good news is ... we have Clarity recordings for SDF usage!\n\nThe bad news is... the display isn't rendering correctly in Clarity playback. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " is it possible the CSS for production environments isn't publicly accessible? (See "}, {"type": "link", "url": "https://learn.microsoft.com/en-us/clarity/session-recordings/troubleshooting-recordings", "text": "this article"}, {"type": "text", "text": " I found while troubleshooting.)"}]}]}]}, {"ts": "1707959789.669989", "text": "<@U04DKEFP1K8> Eng priorities for next day are <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|on the board>. I cleared some from the \"Done\" column after verifying them either in SDF or Staging environments, and moved one back to \"In Progress\" because it was incomplete.", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "RXvYA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "on the board"}, {"type": "text", "text": ". I cleared some from the \"Done\" column after verifying them either in SDF or Staging environments, and moved one back to \"In Progress\" because it was incomplete."}]}]}]}, {"ts": "1708041929.899089", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> As mentioned today - here is a first cut of the <https://docs.google.com/document/d/111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc/edit|Analytics &amp; Insights PRD> for a v1.\n• Let me know if you have feedback on the overall format or level of detail\n• I've flagged several \"open questions\" throughout the doc - feel free to add comments with your perspective on those\n• The linked Figjam has only rough/wireframe visuals, as the specific designs have not been finalized yet. ", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708041929.899089", "reply_count": 1, "files": [{"id": "F06K0N7TDAR", "created": 1708041932, "timestamp": 1708041932, "name": "Analytics & Insights PRD", "title": "People Insights PRD", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc", "external_url": "https://docs.google.com/document/d/111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc/edit", "url_private": "https://docs.google.com/document/d/111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSOc8MB+FJhv7w/KlYZ+tJhqAFAIJyc/hS03DUc/5NADqKKKAEYc9T+FIOP7x+tK3SmjGP/r0APz7UU3j0/Wjj0/WgB1FFFACN0puR/dpxIHWkCg85oAT/AIDSgn0pdtG2gBaKKKAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06K0N7TDAR/analytics___insights_prd", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "cR2Ql", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " As mentioned today - here is a first cut of the "}, {"type": "link", "url": "https://docs.google.com/document/d/111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc/edit", "text": "Analytics & Insights PRD"}, {"type": "text", "text": " for a v1.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me know if you have feedback on the overall format or level of detail"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "I've flagged several \"open questions\" throughout the doc - feel free to add comments with your perspective on those"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The linked Figjam has only rough/wireframe visuals, as the specific designs have not been finalized yet. "}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1708117100.406219", "text": "<@U04DKEFP1K8> For <https://drive.google.com/drive/u/0/folders/1iaKZEim_JEPRY0GkDeExi5t6hWpitKh_|Nauto>, we now have Employee data, Compensation data, and Salary bands. I think Equity will also be added, but let's start reviewing what they've provided and flag anything that will need special handling in upload. I already asked them to specify which \"tier\" to use for salary bands, separate from \"location\", so that may be similar to what we did for SDF.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708117100.406219", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "gcz1J", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For "}, {"type": "link", "url": "https://drive.google.com/drive/u/0/folders/1iaKZEim_JEPRY0GkDeExi5t6hWpitKh_", "text": "<PERSON><PERSON>"}, {"type": "text", "text": ", we now have Employee data, Compensation data, and Salary bands. I think Equity will also be added, but let's start reviewing what they've provided and flag anything that will need special handling in upload. I already asked them to specify which \"tier\" to use for salary bands, separate from \"location\", so that may be similar to what we did for SDF."}]}]}]}, {"ts": "1708309313.229159", "text": "I've also started a <https://docs.google.com/document/d/15jp8d6rO3_SsU4Jdgl7g96ag9zvIwul_uCcp8Ulw7MI/edit|PRD for Total Rewards settings>, to capture the level of customization we'll need to support in the design.\n\nSome of the main questions I have while completing this:\n• Which types of employee data are customers most likely to use to determine who sees Total Rewards and specific benefits?\n• What configuration will we need from admins (in addition to any uploaded data) in order to display accurate Equity information? ", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708309313.229159", "reply_count": 2, "files": [{"id": "F06KH6CDN68", "created": 1708309315, "timestamp": 1708309315, "name": "Total Rewards Config PRD", "title": "Total Rewards Config PRD", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "15jp8d6rO3_SsU4Jdgl7g96ag9zvIwul_uCcp8Ulw7MI", "external_url": "https://docs.google.com/document/d/15jp8d6rO3_SsU4Jdgl7g96ag9zvIwul_uCcp8Ulw7MI/edit", "url_private": "https://docs.google.com/document/d/15jp8d6rO3_SsU4Jdgl7g96ag9zvIwul_uCcp8Ulw7MI/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSOcjBApMN/eH5UrDjOOab83vQA4Bs8kEfSlpg3UvP+TQA6iiigBrdR1/OgYHcmlJx/wDqpoHNAD80ZpuB3/nS4H+TQAtFFFACN0pgzT2z2OKaCfXP40AHPqKBml3ew/OlBPp+tACjpRRRQB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06KH6CDN68/total_rewards_config_prd", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "sv2vJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I've also started a "}, {"type": "link", "url": "https://docs.google.com/document/d/15jp8d6rO3_SsU4Jdgl7g96ag9zvIwul_uCcp8Ulw7MI/edit", "text": "PRD for Total Rewards settings"}, {"type": "text", "text": ", to capture the level of customization we'll need to support in the design.\n\nSome of the main questions I have while completing this:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Which types of employee data are customers most likely to use to determine who sees Total Rewards and specific benefits?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "What configuration will we need from admins (in addition to any uploaded data) in order to display accurate Equity information? "}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1708396328.056409", "text": "<@U04DKEFP1K8> Eng priorities for next day are <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|on the board> - mainly pulling in the remainder of <https://compiify.atlassian.net/browse/COM-2087|Wave 3> and have moved some tickets to the top of <https://compiify.atlassian.net/browse/COM-2088|Wave 4> for anyone who doesn't have enough assigned.", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "mcFkj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "on the board"}, {"type": "text", "text": " - mainly pulling in the remainder of "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " and have moved some tickets to the top of "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2088", "text": "Wave 4"}, {"type": "text", "text": " for anyone who doesn't have enough assigned."}]}]}]}, {"ts": "1708450260.764309", "text": "FYI <@U0690EB5JE5>, here are the JIRA Epics for some of the upcoming projects:\n• <https://compiify.atlassian.net/browse/COM-2337|Customer Onboarding/Enablement (COM-2337)>\n• <https://compiify.atlassian.net/browse/COM-2338|Analytics &amp; Insights (COM-2338)>\n• <https://compiify.atlassian.net/browse/COM-2339|Total Rewards (COM-2339)>\n• <https://compiify.atlassian.net/browse/COM-2340|Salary Bands V2 (COM-2340)>", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2Wh49", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "FYI "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": ", here are the JIRA Epics for some of the upcoming projects:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2337", "text": "Customer Onboarding/Enablement (COM-2337)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2338", "text": "Analytics & Insights (COM-2338)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2339", "text": "Total Rewards (COM-2339)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2340", "text": "Salary Bands V2 (COM-2340)"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1708455410.571989", "text": "<@U04DKEFP1K8> - question from <PERSON>; I think the answer is 'Yes', <PERSON><PERSON> can see any of the \"Reviewed\" data that <PERSON> inputs, correct?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708455410.571989", "reply_count": 9, "files": [{"id": "F06L4NCF9G9", "created": 1708455408, "timestamp": 1708455408, "name": "Screenshot 2024-02-20 at 10.56.07 AM.png", "title": "Screenshot 2024-02-20 at 10.56.07 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 11970, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06L4NCF9G9/screenshot_2024-02-20_at_10.56.07___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06L4NCF9G9/download/screenshot_2024-02-20_at_10.56.07___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06L4NCF9G9-a02b016f30/screenshot_2024-02-20_at_10.56.07___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06L4NCF9G9-a02b016f30/screenshot_2024-02-20_at_10.56.07___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06L4NCF9G9-a02b016f30/screenshot_2024-02-20_at_10.56.07___am_360.png", "thumb_360_w": 360, "thumb_360_h": 111, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06L4NCF9G9-a02b016f30/screenshot_2024-02-20_at_10.56.07___am_480.png", "thumb_480_w": 480, "thumb_480_h": 147, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06L4NCF9G9-a02b016f30/screenshot_2024-02-20_at_10.56.07___am_160.png", "original_w": 518, "original_h": 159, "thumb_tiny": "AwAOADDQyN3JP507KjuPzoAwSaWgBNy+oo3D1FLRQAm4eopcg9xRRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06L4NCF9G9/screenshot_2024-02-20_at_10.56.07___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06L4NCF9G9-ee01b005dc", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Kop2q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " - question from <PERSON>; I think the answer is 'Yes', <PERSON><PERSON> can see any of the \"Reviewed\" data that <PERSON> inputs, correct?"}]}]}]}, {"ts": "1708466878.483039", "text": "Here's a fun issue :laughing:\n\nSince I have support@compiify set up as an \"alias\", it uses my Gmail profile picture when something comes \"from\" that address. Which is weird if you get an email from support for a password reset, for example:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708466878.483039", "reply_count": 7, "files": [{"id": "F06LG1E0ZKJ", "created": 1708466746, "timestamp": 1708466746, "name": "Screenshot 2024-02-20 at 2.04.57 PM.png", "title": "Screenshot 2024-02-20 at 2.04.57 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 25018, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06LG1E0ZKJ/screenshot_2024-02-20_at_2.04.57___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06LG1E0ZKJ/download/screenshot_2024-02-20_at_2.04.57___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LG1E0ZKJ-eec1038bec/screenshot_2024-02-20_at_2.04.57___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LG1E0ZKJ-eec1038bec/screenshot_2024-02-20_at_2.04.57___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LG1E0ZKJ-eec1038bec/screenshot_2024-02-20_at_2.04.57___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 194, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LG1E0ZKJ-eec1038bec/screenshot_2024-02-20_at_2.04.57___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 259, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LG1E0ZKJ-eec1038bec/screenshot_2024-02-20_at_2.04.57___pm_160.png", "original_w": 505, "original_h": 272, "thumb_tiny": "AwAZADDRLLkrnnFNyv8AfNP2jOcDNHPoPzoAbwejml2/7TUuW9B+dLQAm3/aNKKKKACk5zS0UAJzQM96WigAooooA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06LG1E0ZKJ/screenshot_2024-02-20_at_2.04.57___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06LG1E0ZKJ-7adfd39053", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "AORXh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here's a fun issue "}, {"type": "emoji", "name": "laughing", "unicode": "1f606"}, {"type": "text", "text": "\n\nSince I have support@compiify set up as an \"alias\", it uses my Gmail profile picture when something comes \"from\" that address. Which is weird if you get an email from support for a password reset, for example:"}]}]}]}, {"ts": "1708478498.880109", "text": "<@U04DKEFP1K8> Eng priorities for next day are <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|on the board> including the couple of cleanup tickets for password reset. And can you have a look at the items in <https://compiify.atlassian.net/browse/COM-2087|Wave 3> that have been \"in development\" for some time now?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fvuzG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "on the board"}, {"type": "text", "text": " including the couple of cleanup tickets for password reset. And can you have a look at the items in "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " that have been \"in development\" for some time now?"}]}]}]}, {"ts": "1708545595.196179", "text": "More activity from SDF's managers yesterday :eyes:\n\n(A couple of these \"users\" might be me and <PERSON><PERSON><PERSON><PERSON>, but there's definitely higher activity yesterday after the long weekend.)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708545595.196179", "reply_count": 1, "files": [{"id": "F06KKGDSZ4P", "created": 1708545591, "timestamp": 1708545591, "name": "Screenshot 2024-02-21 at 11.58.46 AM.png", "title": "Screenshot 2024-02-21 at 11.58.46 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 258475, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06KKGDSZ4P/screenshot_2024-02-21_at_11.58.46___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06KKGDSZ4P/download/screenshot_2024-02-21_at_11.58.46___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KKGDSZ4P-418aa8befb/screenshot_2024-02-21_at_11.58.46___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KKGDSZ4P-418aa8befb/screenshot_2024-02-21_at_11.58.46___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KKGDSZ4P-418aa8befb/screenshot_2024-02-21_at_11.58.46___am_360.png", "thumb_360_w": 360, "thumb_360_h": 133, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KKGDSZ4P-418aa8befb/screenshot_2024-02-21_at_11.58.46___am_480.png", "thumb_480_w": 480, "thumb_480_h": 178, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KKGDSZ4P-418aa8befb/screenshot_2024-02-21_at_11.58.46___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KKGDSZ4P-418aa8befb/screenshot_2024-02-21_at_11.58.46___am_720.png", "thumb_720_w": 720, "thumb_720_h": 267, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KKGDSZ4P-418aa8befb/screenshot_2024-02-21_at_11.58.46___am_800.png", "thumb_800_w": 800, "thumb_800_h": 296, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KKGDSZ4P-418aa8befb/screenshot_2024-02-21_at_11.58.46___am_960.png", "thumb_960_w": 960, "thumb_960_h": 355, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KKGDSZ4P-418aa8befb/screenshot_2024-02-21_at_11.58.46___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 379, "original_w": 2280, "original_h": 844, "thumb_tiny": "AwARADDSx7mlpD7UnPrQA6im4J70YPrQA7vSA5oAPc0AYoAWiiigAooooAKKKKAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06KKGDSZ4P/screenshot_2024-02-21_at_11.58.46___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06KKGDSZ4P-a795fa1d55", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "wl+de", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "More activity from SDF's managers yesterday "}, {"type": "emoji", "name": "eyes", "unicode": "1f440"}, {"type": "text", "text": "\n\n(A couple of these \"users\" might be me and <PERSON><PERSON><PERSON><PERSON>, but there's definitely higher activity yesterday after the long weekend.)"}]}]}]}, {"ts": "1708563754.612689", "text": "<@U04DKEFP1K8> Eng priorities for next day are <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|on the board>. I was able to verify &amp; clear out several tickets after today's SDF update, but there are still a lot of items that have been in the \"In Development\" column for several days.\n\n• Are engineers treating items on this board as highest priority?\n• Is anything else getting in the way?\n• For tickets where I found issues and needed to revert to a prior stage, do we have the right process for engineers to see those and complete the additional fixes?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708563754.612689", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "A5ffV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "on the board"}, {"type": "text", "text": ". I was able to verify & clear out several tickets after today's SDF update, but there are still a lot of items that have been in the \"In Development\" column for several days.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Are engineers treating items on this board as highest priority?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Is anything else getting in the way?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "For tickets where I found issues and needed to revert to a prior stage, do we have the right process for engineers to see those and complete the additional fixes?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1708564728.906209", "text": "Also <@U04DKEFP1K8> based on the experience today &amp; the previous one with SDF, I think we'll need to avoid doing mid-day updates to customer environments unless the customer specifically requests it / reports a blocker.\n\nMy observations:\n• We aren't able to provide accurate estimates of how long an update will take\n• One update might introduce other issues, which contributes to this timing issue\n• If a bug that's introduced requires other Eng to help, we won't be able to fix by the customer's EOD\nI know you asked me before starting this update, and I feel like I made the wrong call this time. :pensive: Let's find a process that helps us maintain customers' confidence, because giving them an expectation we don't meet has a high risk of harming our reputation.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708564728.906209", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "cNkt5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " based on the experience today & the previous one with SDF, I think we'll need to avoid doing mid-day updates to customer environments unless the customer specifically requests it / reports a blocker.\n\nMy observations:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We aren't able to provide accurate estimates of how long an update will take"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "One update might introduce other issues, which contributes to this timing issue"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "If a bug that's introduced requires other Eng to help, we won't be able to fix by the customer's EOD"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nI know you asked me before starting this update, and I feel like I made the wrong call this time. "}, {"type": "emoji", "name": "pensive", "unicode": "1f614"}, {"type": "text", "text": " Let's find a process that helps us maintain customers' confidence, because giving them an expectation we don't meet has a high risk of harming our reputation."}]}]}]}, {"ts": "1708583942.164329", "text": "Troubleshooting the issues reported by SDF:\n\n<PERSON>'s 2 sessions from today were 40 min and 63 min long. Let me know when we have a clearer answer to how long it takes before someone would be logged out / have an expired session...", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708583942.164329", "reply_count": 5, "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06KW0QK3TQ", "created": 1708583940, "timestamp": 1708583940, "name": "Screenshot 2024-02-21 at 10.37.36 PM.png", "title": "Screenshot 2024-02-21 at 10.37.36 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 87294, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06KW0QK3TQ/screenshot_2024-02-21_at_10.37.36___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06KW0QK3TQ/download/screenshot_2024-02-21_at_10.37.36___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KW0QK3TQ-4eb12e6e52/screenshot_2024-02-21_at_10.37.36___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KW0QK3TQ-4eb12e6e52/screenshot_2024-02-21_at_10.37.36___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KW0QK3TQ-4eb12e6e52/screenshot_2024-02-21_at_10.37.36___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 76, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KW0QK3TQ-4eb12e6e52/screenshot_2024-02-21_at_10.37.36___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 102, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KW0QK3TQ-4eb12e6e52/screenshot_2024-02-21_at_10.37.36___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KW0QK3TQ-4eb12e6e52/screenshot_2024-02-21_at_10.37.36___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 153, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KW0QK3TQ-4eb12e6e52/screenshot_2024-02-21_at_10.37.36___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 170, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KW0QK3TQ-4eb12e6e52/screenshot_2024-02-21_at_10.37.36___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 204, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KW0QK3TQ-4eb12e6e52/screenshot_2024-02-21_at_10.37.36___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 217, "original_w": 1660, "original_h": 352, "thumb_tiny": "AwAKADDS79uDQPwpaKAG9SRx+dLgc0tFADehzx+dO5oooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06KW0QK3TQ/screenshot_2024-02-21_at_10.37.36___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06KW0QK3TQ-e2831443d8", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "5r2IO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Troubleshooting the issues reported by SDF:\n\n<PERSON>'s 2 sessions from today were 40 min and 63 min long. Let me know when we have a clearer answer to how long it takes before someone would be logged out / have an expired session..."}]}]}]}, {"ts": "1708636322.709469", "text": "<@U065H3M6WJV> can we ask our future customers to find one manager from their team to participate and give us early feedback ( i am getting a feeling we will always get feedback from superadmins but it gets too late when the cycle has started and manager request updates to product at that moment)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708636322.709469", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "s9r5y", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " can we ask our future customers to find one manager from their team to participate and give us early feedback ( i am getting a feeling we will always get feedback from superadmins but it gets too late when the cycle has started and manager request updates to product at that moment)"}]}]}]}, {"ts": "1708701933.083029", "text": "<@U065H3M6WJV> <@U04DKEFP1K8> let me know if there are any progressupdate that I can communicate to <PERSON> on real time updates to the budgets", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1708701933.083029", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "ntgPG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " let me know if there are any progressupdate that I can communicate to <PERSON> on real time updates to the budgets"}]}]}]}, {"ts": "1708707482.086579", "text": "<PERSON><PERSON><PERSON> has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R06LCKHA0MQ", "block_id": "09/IW", "api_decoration_available": false, "call": {"v1": {"id": "R06LCKHA0MQ", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1708707481, "active_participants": [], "all_participants": [{"slack_id": "U065H3M6WJV"}, {"external_id": "33555456", "avatar_url": "", "display_name": "<PERSON><PERSON><PERSON> V"}, {"slack_id": "U04DS2MBWP4"}, {"slack_id": "U04DKEFP1K8"}], "display_id": "819-9232-0030", "join_url": "https://us06web.zoom.us/j/81992320030?pwd=SpkSaEC1JXr3R4a3JB20awO2WGSBF1.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEXzZkYjdkYmIzZTUzZDQ1YjNiNTk2NjZlZjZmZjU1Mjk0JnVzcz1qZ1phOWUwa2dLOXZBWGx0dGdTbk1SWDJmSTJBdmRtRHdkdlVUOEIwZUxFaTRKTzNIUlBHZlExajdsb2w3MUVTWERNRGw3QXAzWElfVFgzU1B5SWRoNlcyclFjNnlMNTVtTUdUT2Z3LjEyZ3FWdFR6TWxQWFBqR2c%3D&action=join&confno=81992320030&pwd=SpkSaEC1JXr3R4a3JB20awO2WGSBF1.1", "name": "Zoom meeting started by <PERSON>", "created_by": "U05185RFCNT", "date_end": 1708707961, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "4WWkc", "text": {"type": "mrkdwn", "text": "Meeting passcode: SpkSaEC1JXr3R4a3JB20awO2WGSBF1.1", "verbatim": false}}]}, {"ts": "1708710516.475199", "text": "<PERSON> is finally logging in this morning &amp; using the tool, so keeping eyes &amp; ears open for any feedback from DA... :eyes:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "partying_face", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "OQoVO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> is finally logging in this morning & using the tool, so keeping eyes & ears open for any feedback from DA... "}, {"type": "emoji", "name": "eyes", "unicode": "1f440"}]}]}]}, {"ts": "1708734330.297639", "text": "<@U04DKEFP1K8> From my initial testing of the realtime updates, I've found 8 more bugs and added them to <https://compiify.atlassian.net/browse/COM-1929|this JIRA>. I think at least 4 of them could be blockers for SDF to use the feature. Can you take a look and assess whether you think eng can solve them by tomorrow?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708734330.297639", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "cWbNp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " From my initial testing of the realtime updates, I've found 8 more bugs and added them to "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1929", "text": "this JIRA"}, {"type": "text", "text": ". I think at least 4 of them could be blockers for SDF to use the feature. Can you take a look and assess whether you think eng can solve them by tomorrow?"}]}]}]}, {"ts": "1708796079.718839", "text": "<@U04DKEFP1K8> <@U065H3M6WJV> how are we doing on resolving the bugs?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9wONo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " how are we doing on resolving the bugs?"}]}]}]}, {"ts": "1708796114.656469", "text": "what should we tell <PERSON>?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "98fp9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "what should we tell <PERSON>?"}]}]}]}, {"ts": "1708796167.994139", "text": "We have received fixes for 4 critical fixes <PERSON> had requested. I am starting to test them now, will be making them available for Rachel Testing in another hour or so.", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xF8sH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We have received fixes for 4 critical fixes <PERSON> had requested. I am starting to test them now, will be making them available for Rachel Testing in another hour or so."}]}]}]}, {"ts": "1708796328.060359", "text": "ok. <@U065H3M6WJV> are you comfortable with releasing to <PERSON> with with these 4 fixes or do we need to fix others as well?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "H9A35", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok. "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " are you comfortable with releasing to <PERSON> with with these 4 fixes or do we need to fix others as well?"}]}]}]}, {"ts": "1708796867.654849", "text": "<@U04DS2MBWP4> these were the 4 critical issues, if those are addressed and no new blockers found, we’ll be good to release the changes", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nDXYw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " these were the 4 critical issues, if those are addressed and no new blockers found, we’ll be good to release the changes"}]}]}]}, {"ts": "1708797076.498829", "text": "I have an appointment starting at 12, is it possible for me to get the changes to sanity test by around 10:30 <@U04DKEFP1K8> ?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9UTCR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have an appointment starting at 12, is it possible for me to get the changes to sanity test by around 10:30 "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " ?"}]}]}]}, {"ts": "1708797327.401409", "text": "Sure <PERSON>. ", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708797327.401409", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "TDE/Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure <PERSON>. "}]}]}]}, {"ts": "1708798040.777829", "text": "<@U065H3M6WJV>If the changes are approve before we confirm lisa and katya , we should ideally give a quick demo to one of them to explain how live budget update ,  review all, unlock all updates work. thoughts?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708798040.777829", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "ssC3k", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": "If the changes are approve before we confirm lisa and katya , we should ideally give a quick demo to one of them to explain how live budget update ,  review all, unlock all updates work. thoughts?"}]}]}]}, {"ts": "1708801246.160899", "text": "Hey <@U04DS2MBWP4> <@U04DKEFP1K8> the first pass isn't positive - I keep finding cases where the amounts in the rows don't match the amount tallied in the budget. It's quite likely that this is due to using a data set that already had some values input, but that's exactly what we'll be dealing with SDF as well. :confused:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708801246.160899", "reply_count": 18, "reactions": [{"name": "face_with_rolling_eyes", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "KpPPs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " the first pass isn't positive - I keep finding cases where the amounts in the rows don't match the amount tallied in the budget. It's quite likely that this is due to using a data set that already had some values input, but that's exactly what we'll be dealing with SDF as well. "}, {"type": "emoji", "name": "confused", "unicode": "1f615"}]}]}]}, {"ts": "1708819159.698029", "text": "<@U04DS2MBWP4> After the latest sync with <@U04DKEFP1K8> , I feel that we have a better understanding of the root causes of the miscalculation, so we might be able to resolve those today. Still a couple hours away from a new build that can be re-tested.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YSVnP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " After the latest sync with "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " , I feel that we have a better understanding of the root causes of the miscalculation, so we might be able to resolve those today. Still a couple hours away from a new build that can be re-tested."}]}]}]}, {"ts": "1708820089.908809", "text": "Thanks <@U065H3M6WJV> <@U04DKEFP1K8> That's good to hear. Looking forward to the outcome of it.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/xTZw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " That's good to hear. Looking forward to the outcome of it."}]}]}]}, {"ts": "1708832240.589889", "text": "Here is the summary of issue triaged / fixed during the day\n1. <https://compiify.atlassian.net/browse/COM-2394|COM-2394> - This was a blocker since in certain cases new lives updates were not getting saved. This is fixed and deployed\n2. One australian contractor adjustment were not getting updated in budget correctly ( since the band was not uploaded for australia) Not a product issue. this is not a blocker currently.\n3. COM-2397 HR Admin page shows incorrect budget overview for manager with non us employees ( root caused and its a minor fix). this is not a blocker currently.\n\n<PERSON> reported  <https://compiify.atlassian.net/browse/COM-2395> but it is not a blocker currently\n\n<PERSON> will add further updates and next set of blocking issue", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708832240.589889", "reply_count": 25, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12455::2eeea580d38f11ee977e6b82976486d9", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2394?atlOrigin=eyJpIjoiMmUwMjEwOWNjNDY0NGU5ZTkxYTkyNDhjZThiZjQ1MDIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2394 New updates are reverted from salary and promotion ...will add more…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12455::2eeea584d38f11ee977e6b82976486d9", "elements": [{"type": "mrkdwn", "text": "Status: *Done*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12455::2eeea581d38f11ee977e6b82976486d9", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12455\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12455\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2394", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:12456::2eeea582d38f11ee977e6b82976486d9", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2395?atlOrigin=eyJpIjoiNTJkNGE0ODM4YmFiNGQ0MWEyNDBlZmExN2MwYmM2ODgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2395 Realtime updates: \"Unlock all\" reverts $0.00 merit increases to bla…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12456::2eeea585d38f11ee977e6b82976486d9", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/393c72388fdc9e69b9048f7ada27eceb?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "yaroslav.v"}, {"type": "mrkdwn", "text": "Assignee: *yaroslav.v*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12456::2eeea583d38f11ee977e6b82976486d9", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12456\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12456\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2395", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "kx1VX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the summary of issue triaged / fixed during the day\n1. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2394", "text": "COM-2394"}, {"type": "text", "text": " - This was a blocker since in certain cases new lives updates were not getting saved. This is fixed and deployed\n2. One australian contractor adjustment were not getting updated in budget correctly ( since the band was not uploaded for australia) Not a product issue. this is not a blocker currently.\n3. "}, {"type": "text", "text": "COM-2397 HR", "style": {"unlink": true}}, {"type": "text", "text": " Admin page shows incorrect budget overview for manager with non us employees ( root caused and its a minor fix). this is not a blocker currently.\n\n<PERSON> reported  "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2395"}, {"type": "text", "text": " but it is not a blocker currently\n\n<PERSON> will add further updates and next set of blocking issue"}]}]}]}, {"ts": "1708888529.061099", "text": "Latest update: sdf-test cycle is reset, I'll start testing the \"clean slate\" case now.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708888529.061099", "reply_count": 80, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "L1RIu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Latest update: sdf-test cycle is reset, I'll start testing the \"clean slate\" case now."}]}]}]}, {"ts": "1708902391.535019", "text": "saurab<PERSON>.jain has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R06LWR81PDF", "block_id": "+6Y0W", "api_decoration_available": false, "call": {"v1": {"id": "R06LWR81PDF", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1708902391, "active_participants": [], "all_participants": [{"slack_id": "U04DKEFP1K8"}, {"slack_id": "U065H3M6WJV"}], "display_id": "869-4550-4247", "join_url": "https://us06web.zoom.us/j/86945504247?pwd=jxckfGIuU4UI0w3zzgjhqHjKdLV5Ta.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEXzE0OTU3YTNkOTA5YjRkMzNhOTJmYzA0ZjY4ZTExNDE3JnVzcz1haG43VGQ0YkxNY0hzVE5peVFBLVQzMWdSaVdsQjhVWnVnbHBvck5NdDRXNHY3MmNZMUxGaElITndTaV92bEJEVk1IanNxb2ZNSG0yUmlXU1lTTkRCdmd6V2Y4M1RJRWZjX3NrS19BLkh0bnRybDA1NDhqUVpiNVc%3D&action=join&confno=86945504247&pwd=jxckfGIuU4UI0w3zzgjhqHjKdLV5Ta.1", "name": "Zoom meeting started by <PERSON><PERSON><PERSON><PERSON>", "created_by": "U05185RFCNT", "date_end": 1708903951, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "zInqM", "text": {"type": "mrkdwn", "text": "Meeting passcode: jxckfGIuU4UI0w3zzgjhqHjKdLV5Ta.1", "verbatim": false}}]}, {"ts": "1708903925.007299", "text": "Cycle recreated, re-inputting data now.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "G/z9X", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle recreated, re-inputting data now."}]}]}]}, {"ts": "1708906626.052269", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> SDF production data is restored\n\n• In the process of resetting, we _lost any prior comments_ that might have been entered. But any other promotion &amp; salary inputs were preserved.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708906626.052269", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "6PgTL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " SDF production data is restored\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In the process of resetting, we "}, {"type": "text", "text": "lost any prior comments", "style": {"italic": true}}, {"type": "text", "text": " that might have been entered. But any other promotion & salary inputs were preserved."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1708906772.791909", "text": "Sounds good", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZZ2v3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sounds good"}]}]}]}, {"ts": "1708907721.512349", "text": "<@U065H3M6WJV> Comments are not lost. I have recovered them from a db dump. Will share a file shortly. We can restore them as well", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708907721.512349", "reply_count": 16, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "9sRbR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Comments are not lost. I have recovered them from a db dump. Will share a file shortly. We can restore them as well"}]}]}]}, {"ts": "1708913783.204469", "text": "<@U04DS2MBWP4> <PERSON> should be able to view her previous update ( i impersonated her and can see the updates too). Looks like she did refresh and was able to see . i have asked her to confirm", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06LFLCUWQM", "created": 1708913758, "timestamp": 1708913758, "name": "Screenshot 2024-02-25 at 6.14.52 PM.png", "title": "Screenshot 2024-02-25 at 6.14.52 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 601573, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06LFLCUWQM/screenshot_2024-02-25_at_6.14.52___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06LFLCUWQM/download/screenshot_2024-02-25_at_6.14.52___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 190, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 253, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 380, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 422, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 506, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LFLCUWQM-1ce8789834/screenshot_2024-02-25_at_6.14.52___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 540, "original_w": 3428, "original_h": 1808, "thumb_tiny": "AwAZADDR47ilyP8AIo70UAHH+RS0lFAC0h6jjNFFAB3opO9BoAWlptFACntRSUtAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06LFLCUWQM/screenshot_2024-02-25_at_6.14.52___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06LFLCUWQM-e7e94d7289", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "E4xiF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " <PERSON> should be able to view her previous update ( i impersonated her and can see the updates too). Looks like she did refresh and was able to see . i have asked her to confirm"}]}]}]}, {"ts": "1708924961.181259", "text": "<@U04DKEFP1K8> Can you update here what we're committing to fix by tomorrow morning? I communicated to <PERSON> that the $0.00 inputs not overwriting values in Export would be fixed; please update me on what else we expect to be able to clear from the known issues overnight. :pray:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "T+Y2U", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can you update here what we're committing to fix by tomorrow morning? I communicated to <PERSON> that the $0.00 inputs not overwriting values in Export would be fixed; please update me on what else we expect to be able to clear from the known issues overnight. "}, {"type": "emoji", "name": "pray", "unicode": "1f64f"}]}]}]}, {"ts": "1708927076.089239", "text": "Here is jira list which engg will target for today\n1. <https://compiify.atlassian.net/browse/COM-2395>\n2. <https://compiify.atlassian.net/browse/COM-2398>\n3. <https://compiify.atlassian.net/browse/COM-2399>\n4. <https://compiify.atlassian.net/browse/COM-2406>", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV", "U04DS2MBWP4"], "count": 2}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12459::fd423d70d46b11eea7653f2df266b93b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2398?atlOrigin=eyJpIjoiYzc2MTIzMDM4Y2IxNGQ1ZGFjZjM5NjllZTkzYzViMDEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2398 Realtime updates: Cycle reports use last \"Reviewed\" value despite n…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12459::fd423d76d46b11eea7653f2df266b93b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/347c906ac625df3f0780bf0833593920?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "Vinod.e"}, {"type": "mrkdwn", "text": "Assignee: *Vinod.e*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12459::fd423d71d46b11eea7653f2df266b93b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12459\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12459\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2398", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:12460::fd423d72d46b11eea7653f2df266b93b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2399?atlOrigin=eyJpIjoiNmQ1YTc1ZDFkMzJlNGI5NjhjNzEwNWMxNzUxOWU5NTUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2399 Realtime updates: Export from Merit view includes older values if m…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12460::fd423d77d46b11eea7653f2df266b93b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/347c906ac625df3f0780bf0833593920?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "Vinod.e"}, {"type": "mrkdwn", "text": "Assignee: *Vinod.e*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12460::fd423d73d46b11eea7653f2df266b93b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12460\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12460\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2399", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 3, "blocks": [{"type": "section", "block_id": "uf:ih:12467::fd423d74d46b11eea7653f2df266b93b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2406?atlOrigin=eyJpIjoiZjkzOTk2NTkxNTUxNGMxOTk3MmRjZTJiZWNhOTkxMWIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2406 Realtime updates: Export behavior is inconsistent for Reviewed/Pend…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12467::fd423d78d46b11eea7653f2df266b93b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/347c906ac625df3f0780bf0833593920?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "Vinod.e"}, {"type": "mrkdwn", "text": "Assignee: *Vinod.e*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12467::fd423d75d46b11eea7653f2df266b93b", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12467\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12467\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2406", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Pa6de", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is jira list which engg will target for today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2395"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2398"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2399"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2406"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1708968848.503889", "text": "Update: I have received fixed for 3 out of 4 fixes from the above list. I am planning to complete my review and merge them at noon", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708968848.503889", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "CGqBw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Update: I have received fixed for 3 out of 4 fixes from the above list. I am planning to complete my review and merge them at noon"}]}]}]}, {"ts": "1708972699.959199", "text": "Here's a promising sign - SDF users are logging back in today to their prod environment... hope the updates hold strong!:crossed_fingers:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "crossed_fingers", "users": ["U04DKEFP1K8", "U04DS2MBWP4", "U0690EB5JE5"], "count": 3}, {"name": "partyparrot", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06LRUETF44", "created": 1708972697, "timestamp": 1708972697, "name": "Screenshot 2024-02-26 at 10.37.04 AM.png", "title": "Screenshot 2024-02-26 at 10.37.04 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 134387, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06LRUETF44/screenshot_2024-02-26_at_10.37.04___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06LRUETF44/download/screenshot_2024-02-26_at_10.37.04___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_360.png", "thumb_360_w": 360, "thumb_360_h": 354, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_480.png", "thumb_480_w": 480, "thumb_480_h": 471, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_720.png", "thumb_720_w": 720, "thumb_720_h": 707, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_800.png", "thumb_800_w": 800, "thumb_800_h": 786, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_960.png", "thumb_960_w": 960, "thumb_960_h": 943, "original_w": 1014, "original_h": 996, "thumb_tiny": "AwAvADDToopMZ60ALRSDpTW69qAH0UxQM8EU+gBD0pabyTQOuKAF9qYW+bGcD604dTgU3BH8NACgjPB/WnA80zB/u0uCewFMY+iiikIKYevb86fTSD6j8qAEHB7fnTvSk2n1H5UoB7mgD//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06LRUETF44/screenshot_2024-02-26_at_10.37.04___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06LRUETF44-f8a8ee4667", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "bN3hc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here's a promising sign - SDF users are logging back in today to their prod environment... hope the updates hold strong!"}, {"type": "emoji", "name": "crossed_fingers", "unicode": "1f91e"}]}]}]}, {"ts": "1708972853.149109", "text": "So after live budget updates were merged managers literally do not need to change the review status from pending review to reviewed. How do we convey this message to them ( just use review all when they are done)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708972853.149109", "reply_count": 6, "edited": {"user": "U04DKEFP1K8", "ts": "1708972873.000000"}, "blocks": [{"type": "rich_text", "block_id": "fu+Xk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So after live budget updates were merged managers literally do not need to change the review status from pending review to reviewed. How do we convey this message to them ( just use review all when they are done)"}]}]}]}, {"ts": "1708984354.302929", "text": "<@U0690EB5JE5> I know we chatted a bit this morning about the data model and how our experience with SDF is putting that to the test. Here's another example (<https://compiify.atlassian.net/browse/COM-2418|COM-2418>) I found today, where we may need to map out what we treat as \"source of truth\" and what level of precision (how many decimal places) we use on the backend to keep things from shifting later.\n\n(Posting in this channel so <@U04DKEFP1K8> has some visibility to the discussion :slightly_smiling_face: )", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708984354.302929", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "A5jo6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I know we chatted a bit this morning about the data model and how our experience with SDF is putting that to the test. Here's another example ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2418", "text": "COM-2418"}, {"type": "text", "text": ") I found today, where we may need to map out what we treat as \"source of truth\" and what level of precision (how many decimal places) we use on the backend to keep things from shifting later.\n\n(Posting in this channel so "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " has some visibility to the discussion "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " )"}]}]}]}, {"ts": "1708986479.501229", "text": "we haven't heard from <PERSON> yet so I am hoping \" no news is good news\"", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1708986479.501229", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "jG83h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we haven't heard from <PERSON> yet so I am hoping \" no news is good news\""}]}]}]}, {"ts": "1709054258.882199", "text": "Checked in on SDF use this morning. 3 users logged in so far today. <PERSON> has completed almost all of his inputs, but left 1 open. <PERSON> is the first one to input \"round numbers\" in the salary increase (\"$8,000\", \"$7,500\") rather than putting \"round percents\" for everyone (\"4.00%\", \"6.50%\") as the other managers have done so far.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709054258.882199", "reply_count": 5, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "s3fF1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Checked in on SDF use this morning. 3 users logged in so far today. <PERSON> has completed almost all of his inputs, but left 1 open. <PERSON> is the first one to input \"round numbers\" in the salary increase (\"$8,000\", \"$7,500\") rather than putting \"round percents\" for everyone (\"4.00%\", \"6.50%\") as the other managers have done so far."}]}]}]}, {"ts": "1709063252.692809", "text": "<PERSON>, <PERSON> is saying <PERSON> is stuck to submit all, can we check if he has submit all reviews by impersonating", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1709063252.692809", "reply_count": 3, "edited": {"user": "U04DKEFP1K8", "ts": "1709063265.000000"}, "blocks": [{"type": "rich_text", "block_id": "BTzce", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>, <PERSON> is saying <PERSON> is stuck to submit all, can we check if he has submit all reviews by impersonating"}]}]}]}, {"ts": "1709074451.823809", "text": "<@U04DKEFP1K8> can you pls confirm here on this thread after you have completed GDPR deliverables for <PERSON> today? We don't want to block the progress on GDPR while we are paying them a monthly fee.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1709074451.823809", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "JF1/S", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you pls confirm here on this thread after you have completed GDPR deliverables for Tyler today? We don't want to block the progress on GDPR while we are paying them a monthly fee."}]}]}]}, {"ts": "1709077152.419619", "text": "Another look at SDF progress... all managers (except the CEO) have input changes for most or all of their employees. :eyes:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709077152.419619", "reply_count": 7, "files": [{"id": "F06MM689LM6", "created": 1709076730, "timestamp": 1709076730, "name": "Screenshot 2024-02-27 at 3.31.48 PM.png", "title": "Screenshot 2024-02-27 at 3.31.48 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 87161, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06MM689LM6/screenshot_2024-02-27_at_3.31.48___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06MM689LM6/download/screenshot_2024-02-27_at_3.31.48___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 235, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 313, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 470, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 522, "original_w": 892, "original_h": 582, "thumb_tiny": "AwAfADDROQehNLk+hpjAbj938TQoH+z+BoAcc+hpc+xppxx0/E0DHt+dAxcnPQ/mKUE+hFIce35UooENbOep/KgZ9f8Ax2hgc9D+dAB9G/OgBTnjr+VA/wA8UEHjg/nQAfQ/nQMXn3paaV+v50oGPX86BH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06MM689LM6/screenshot_2024-02-27_at_3.31.48___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06MM689LM6-3004586c65", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "GoM81", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Another look at SDF progress... all managers (except the CEO) have input changes for most or all of their employees. "}, {"type": "emoji", "name": "eyes", "unicode": "1f440"}]}]}]}, {"ts": "1709158216.483669", "text": "<@U04DKEFP1K8> Checking in on the customer work - do we know how soon we will be able to get PlayQ's data uploaded? And what else is potentially taking higher priority? Ex:\n• SDF code fix review/merge\n• SDF data changes\n• DA issue root-cause\n• Nauto data changes\n• PlayQ environment creation / data upload\n...anything else higher/lower on this list? :zany_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709158216.483669", "reply_count": 15, "reactions": [{"name": "muscle", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "+smRo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Checking in on the customer work - do we know how soon we will be able to get PlayQ's data uploaded? And what else is potentially taking higher priority? Ex:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF code fix review/merge"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF data changes"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "DA issue root-cause"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Nauto data changes"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "PlayQ environment creation / data upload"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n...anything else higher/lower on this list? "}, {"type": "emoji", "name": "zany_face", "unicode": "1f92a"}]}]}]}, {"ts": "1709160384.985419", "text": "<@U065H3M6WJV> i know the issue lisa is reporting, we need to make an update department budget after we did update manager budget but for that we need actual numbers by department. we can tell lisa , we will update it and make it available later tonight or tomorrow morning", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1709160384.985419", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "lcbwK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " i know the issue lisa is reporting, we need to make an update department budget after we did update manager budget but for that we need actual numbers by department. we can tell lisa , we will update it and make it available later tonight or tomorrow morning"}]}]}]}, {"ts": "1709187368.088919", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> How are we managing which tickets get picked up each day? The <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities> board seems to have gone stale, and I know we had a \"drop everything\" while unblocking SDF but we shouldn't still be picking every ticket from <https://compiify.atlassian.net/browse/COM-1929|SDF UAT> (some of these were lower priority, like \"stray checkbox\" or even the label for \"ineligible\" employees).", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709187368.088919", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "A1zkK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " How are we managing which tickets get picked up each day? The "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities"}, {"type": "text", "text": " board seems to have gone stale, and I know we had a \"drop everything\" while unblocking SDF but we shouldn't still be picking every ticket from "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1929", "text": "SDF UAT"}, {"type": "text", "text": " (some of these were lower priority, like \"stray checkbox\" or even the label for \"ineligible\" employees)."}]}]}]}, {"ts": "1709229747.645129", "text": "<@U04DS2MBWP4> Have you connected with <PERSON> directly in the last week? Wonder what's the best way to gauge how things are going, especially since her last questions were more about the \"beta\" unfinished features like the charts.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709229747.645129", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "NbAZH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Have you connected with <PERSON> directly in the last week? Wonder what's the best way to gauge how things are going, especially since her last questions were more about the \"beta\" unfinished features like the charts."}]}]}]}]}