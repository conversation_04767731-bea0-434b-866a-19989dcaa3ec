{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2024-07", "message_count": 180, "messages": [{"ts": "1720024008.016749", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> what is the status of testing equity for Nauto?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1720024008.016749", "reply_count": 17, "blocks": [{"type": "rich_text", "block_id": "zHnZm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " what is the status of testing equity for Nauto?"}]}]}]}, {"ts": "1720024228.077039", "text": "<@U04DS2MBWP4> status on perf rating changes and bug fixes reported by <@U04DKEFP1K8> for cain watters. Issues are fixed and <PERSON><PERSON><PERSON><PERSON> will be dry running during his day. Changes/fixes will be pushed to customer ENVs tomorrow.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "slEQ7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " status on perf rating changes and bug fixes reported by "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " for cain watters. Issues are fixed and <PERSON><PERSON><PERSON><PERSON> will be dry running during his day. Changes/fixes will be pushed to customer ENVs tomorrow."}]}]}]}, {"ts": "1720024257.969799", "text": "Great! Thank you so much for the update", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ljy4K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Great! Thank you so much for the update"}]}]}]}, {"ts": "1720031558.818179", "text": "FYI, we will be completing the new design of the comp builder by end of next week", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1720031558.818179", "reply_count": 1, "reactions": [{"name": "eyes", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "PWsx2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "FYI, we will be completing the new design of the comp builder by end of next week"}]}]}]}, {"ts": "1720041143.293459", "text": "<@U04DS2MBWP4> I will be doing maintenance on sandbox environments after 5pm today. It might be handy to at-least inform vercara about this maintenance since they are using sandbox , we can skip other customers.", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "done", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Q2HHP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I will be doing maintenance on sandbox environments after 5pm today. It might be handy to at-least inform vercara about this maintenance since they are using sandbox , we can skip other customers."}]}]}]}, {"ts": "1720041298.355879", "text": "ok. how long the maintenance will last", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1720041298.355879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "G/GCp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok. how long the maintenance will last"}]}]}]}, {"ts": "1720041352.737859", "text": "ok", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1720053000.388629", "text": "<@U0690EB5JE5> lets keep the eng call tomorrow. We should go over all of the jira tickets for customers to get clarity on what needs to be done and the timeline.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "EGYaI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " lets keep the eng call tomorrow. We should go over all of the jira tickets for customers to get clarity on what needs to be done and the timeline."}]}]}]}, {"ts": "1720068597.225069", "text": "<!here> are updates from testing for today\nEnvironment used: Local\nBuild: Latest version of branch \"cwa-perf-rating\"\nTested both numeric and text based performance rating ( numeric rating with cainwatters dataset and text based rating with staging dataset)\nSimilar to yesterday. I was\n• Able to upload Performance Ratings in decimal as well as text format.\n• Able to configure performance rating settings before cycle create.\n• Able to set recommendation guidelines for performance ratings, including both range and target recommendations for merit increases and market adjustments in the comp cycle builder.\n• Able to view assigned numerical and text based ratings and corresponding recommendation guidelines in the merit view.\n• Able to view numerical and text based performance ratings in exports and reports.\n• Performance rating filter is working as well in merit view\n<@U0690EB5JE5> I copied my db dump here <https://drive.google.com/drive/folders/1S4L7pGZsxDDNdFDSJk6_qn78CUnCj1zW?usp=drive_link>\n\nI was able to see correct budget number ( salary and market adjustment ) for full organization and manager as well\nRaised: <https://compiify.atlassian.net/browse/COM-3406>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1720068597.225069", "reply_count": 6, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13467::dd86123039c011efadf13b438d3d9900", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3406?atlOrigin=eyJpIjoiNmMzODcxZTI2OWJmNGMwZjk1MGQyOTdhYWFmN2EzNGMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3406 Unable to view all users on Settings page user list>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13467::dd86394139c011efadf13b438d3d9900", "elements": [{"type": "mrkdwn", "text": "Status: *In Development*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13467::dd86394039c011efadf13b438d3d9900", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13467\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13467\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3406", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "tQ3Sf", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " are updates from testing for today\nEnvironment used: Local\nBuild: Latest version of branch \"cwa-perf-rating\"\nTested both numeric and text based performance rating ( numeric rating with cainwatters dataset and text based rating with staging dataset)\nSimilar to yesterday. I was\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Able to upload Performance Ratings in decimal as well as text format."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Able to configure performance rating settings before cycle create."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Able to set recommendation guidelines for performance ratings, including both range and target recommendations for merit increases and market adjustments in the comp cycle builder."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Able to view assigned numerical and text based ratings and corresponding recommendation guidelines in the merit view."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Able to view numerical and text based performance ratings in exports and reports."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Performance rating filter is working as well in merit view"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I copied my db dump here "}, {"type": "link", "url": "https://drive.google.com/drive/folders/1S4L7pGZsxDDNdFDSJk6_qn78CUnCj1zW?usp=drive_link"}, {"type": "text", "text": "\n\nI was able to see correct budget number ( salary and market adjustment ) for full organization and manager as well\nRaised: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3406"}]}]}]}, {"ts": "1720116535.110889", "text": "<@U04DS2MBWP4> maintenance is complete and vercara folks can login back in the environment. \n\nThey will need to use new domain going forward\n\n<http://vercara.stridehr.io|vercara.stridehr.io>\n\nNo changes to their creds", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FHTQa", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " maintenance is complete and vercara folks can login back in the environment. \n\nThey will need to use new domain going forward\n\n"}, {"type": "link", "url": "http://vercara.stridehr.io", "text": "vercara.stridehr.io"}, {"type": "text", "text": "\n\nNo changes to their creds"}]}]}]}, {"ts": "1720118531.598649", "text": "Thanks. ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LRx4J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks. "}]}]}]}, {"ts": "1720118620.881919", "text": "I tried  to log into the CWA test environment but wasn’t able to log into it. <PERSON><PERSON><PERSON> said it might be due to some auth issue. ", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1720118620.881919", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "Ivi8F", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I tried  to log into the CWA test environment but "}, {"type": "text", "text": "wasn’t"}, {"type": "text", "text": " able to log into it. <PERSON><PERSON><PERSON> said it might be due to some auth issue. "}]}]}]}, {"ts": "1720181420.107329", "text": "Vercara user use local login or social login?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Cj9kA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Vercara user use local login or social login?"}]}]}]}, {"ts": "1720181449.099919", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FUpwS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1720181554.528609", "text": "I am able to social login into Vercara though", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "W8s7H", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am able to social login into Vercara though"}]}]}]}, {"ts": "1720182308.944619", "text": "Vercara users still using the compiify url. <@U04DS2MBWP4> Not sure if I am supposed to ask them to try `<https://vercara.stridehr.io/>` . Are they aware of rebranding?", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1720185209.000000"}, "blocks": [{"type": "rich_text", "block_id": "pxzSh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Vercara users still using the compiify url. "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Not sure if I am supposed to ask them to try "}, {"type": "link", "url": "https://vercara.stridehr.io/", "style": {"code": true}}, {"type": "text", "text": " . Are they aware of rebranding?"}]}]}]}, {"ts": "1720190848.574519", "text": "Currently they use the Compiify URL. They are not aware of the rebranding yet.  On Monday we are sending emails to all customers announcing stride.", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1720190996.000000"}, "blocks": [{"type": "rich_text", "block_id": "6Um1j", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Currently they use the Compiify URL. They are not aware of the rebranding yet.  On Monday we are sending emails to all customers announcing stride."}]}]}]}, {"ts": "1720191003.916279", "text": "ah I just saw their email", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Acxkr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ah I just saw their email"}]}]}]}, {"ts": "1720191633.337239", "text": "I will go ahead and send them an email with the new link", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "3140o", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will go ahead and send them an email with the new link"}]}]}]}, {"ts": "1720193732.013229", "text": "<@U0690EB5JE5> <PERSON><PERSON><PERSON><PERSON> was supposed to add <PERSON><PERSON><PERSON> <mailto:<EMAIL>|<EMAIL>> as admin. I don't see <PERSON><PERSON><PERSON> in the Vercara env. Can you please add her as admin", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "done", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TRTJK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON><PERSON> was supposed to add <PERSON><PERSON><PERSON> "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " as admin. I don't see <PERSON><PERSON><PERSON> in the Vercara env. Can you please add her as admin"}]}]}]}, {"ts": "1720368071.756769", "text": "I did added <PERSON><PERSON><PERSON> on July 1 and sent her credentials ( only she was marked on the email as it contained her creds)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LYNor", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I did added <PERSON><PERSON><PERSON> on July 1 and sent her credentials ( only she was marked on the email as it contained her creds)"}]}]}]}, {"ts": "1720372297.054849", "text": "Yeah I saw that. Not sure what happened ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mugHy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yeah I saw that"}, {"type": "text", "text": "."}, {"type": "text", "text": " Not sure what happened "}]}]}]}, {"ts": "1720383452.245359", "text": "I will debug in couple of hours ", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LqKL5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will debug in couple of hours "}]}]}]}, {"ts": "**********.233389", "text": "Its fixed", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NhnG5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Its fixed"}]}]}]}, {"ts": "**********.615389", "text": "<PERSON><PERSON><PERSON> created a new account for her", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HZR6W", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> created a new account for her"}]}]}]}, {"ts": "**********.463219", "text": "<@U0690EB5JE5> <https://us06web.zoom.us/j/**********?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R07B66B0VSA", "block_id": "qCNIa", "api_decoration_available": false, "call": {"v1": {"id": "R07B66B0VSA", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1720406548, "active_participants": [], "all_participants": [], "display_id": "925-480-7019", "join_url": "https://us06web.zoom.us/j/**********?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1720525516, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/**********?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "IWg3Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://us06web.zoom.us/j/**********?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1720418448.543149", "text": "<@U04DS2MBWP4> FYI...I am resetting Cainwatters test environment.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YZ1rm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " FYI...I am resetting Cainwatters test environment."}]}]}]}, {"ts": "1720444755.990469", "text": "Ok", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PfDzz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok"}]}]}]}, {"ts": "1720549937.764509", "text": "<@U04DKEFP1K8> can you set up another eng sync for tomorrow so we can go over the progress and issues from QA testing of Merit planning 2.0+Reporting", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1720549937.764509", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Yst4e", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you set up another eng sync for tomorrow so we can go over the progress and issues from QA testing of Merit planning 2.0+Reporting"}]}]}]}, {"ts": "1720550752.435389", "text": "<@U0690EB5JE5> I spoke with the designer. No change in color for toggles unless it's on and off", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zKGoo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I spoke with the designer. No change in color for toggles unless it's on and off"}]}]}]}, {"ts": "**********.789109", "text": "Yes and no button will only have gray color", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ykb+d", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes and no button will only have gray color"}]}]}]}, {"ts": "**********.074699", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> can we make sure we also push the latest updates to <http://test.stridehr.io|test.stridehr.io> account. I just want to make sure <PERSON><PERSON> is seeing the latest version as I am working with her on further enhancements to product", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.074699", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "9jFd7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can we make sure we also push the latest updates to "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": " account. I just want to make sure <PERSON><PERSON> is seeing the latest version as I am working with her on further enhancements to product"}]}]}]}, {"ts": "**********.951029", "text": "<@U04DKEFP1K8> I chatted with <PERSON>. He can also complement your QA on <PERSON><PERSON> and provide 2nd set of eyes. He is traveling for 2 weeks starting Saturday but he can still do the QA while in the plane. Since he is is a comp expert, I his feedback will be quite helpful esp when <PERSON><PERSON> is a little but more complex.\n\ncan you please reach out to him and set up a 30 mins with him tomorrow  to walk him thru the <PERSON><PERSON>'s environment? He hasn't seen the Merit 2.0 yet so you will have to walk him through the new worflow as well. His email is <mailto:<EMAIL>|<EMAIL>>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.951029", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "RIjT0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I chatted with <PERSON>. He can also complement your QA on <PERSON><PERSON> and provide 2nd set of eyes. He is traveling for 2 weeks starting Saturday but he can still do the QA while in the plane. Since he is is a comp expert, I his feedback will be quite helpful esp when <PERSON><PERSON> is a little but more complex.\n\ncan you please reach out to him and set up a 30 mins with him tomorrow  to walk him thru the <PERSON><PERSON>'s environment? He hasn't seen the Merit 2.0 yet so you will have to walk him through the new worflow as well. His email is "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}]}]}]}, {"ts": "1720726135.279609", "text": "It will have to tomorrow before he leave for his vacation. He said he is available tomorrow anytime after 10:30 am", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3Qb82", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It will have to tomorrow before he leave for his vacation. He said he is available tomorrow anytime after 10:30 am"}]}]}]}, {"ts": "1720765946.676369", "text": "<@U04DS2MBWP4> Me and <@U04DKEFP1K8> realized that HR Admin page is redundant with People insights and Merit view together. I am thinking we should add missing things to People insights and get rid off HR admin page unless we have different information to present there.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1720765967.000000"}, "blocks": [{"type": "rich_text", "block_id": "1TKk4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Me and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " realized that HR Admin page is redundant with People insights and Merit view together. I am thinking we should add missing things to People insights and get rid off HR admin page unless we have different information to present there."}]}]}]}, {"ts": "1720794218.966829", "text": "We can explore that", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FofRq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can explore that"}]}]}]}, {"ts": "1720811914.552419", "text": "<@U0690EB5JE5> Rightway wants to get started and kick off the implementation with ADP integration. What do we need to do to sync their data thru ADP integration? Is there any documentation that I can share with them. What do we need from them to enable us to activate the data sync with their ADP HRIS", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1720811914.552419", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "5<PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Rightway wants to get started and kick off the implementation with ADP integration. What do we need to do to sync their data thru ADP integration? Is there any documentation that I can share with them. What do we need from them to enable us to activate the data sync with their ADP HRIS"}]}]}]}, {"ts": "1720933058.554459", "text": "Any update on Vercara requirements?", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1720951777.000000"}, "blocks": [{"type": "rich_text", "block_id": "RGlo9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Any update on Vercara requirements?"}]}]}]}, {"ts": "1720933058.867939", "text": "We already a week late :)", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5liZW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We already a week late :)"}]}]}]}, {"ts": "1720933059.172359", "text": "It’s at risk now :blush: ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0RPe2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It’s"}, {"type": "text", "text": " at risk now "}, {"type": "emoji", "name": "blush", "unicode": "1f60a"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1720933059.504429", "text": "Will start with bonus part but need clarity on OTE requirements the full functionality ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "U0tK+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will start with bonus part but need clarity on OTE requirements the full functionality "}]}]}]}, {"ts": "1720933187.161549", "text": "<@U04DKEFP1K8> the plan was for you to do it on Friday.  ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "n2XvV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " the plan was for you to do it on Friday.  "}]}]}]}, {"ts": "1720933194.265619", "text": "Can you please provide an update?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "kGPy1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can you please provide an update?"}]}]}]}, {"ts": "1720936141.406689", "text": "OTE document is in progress here <https://docs.google.com/document/d/1S_hxzZTXuz2kOyiAljHOMCFI9TafTZeNhbxvfVfLCfI/edit> it is 65% complete", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WYATC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OTE document is in progress here "}, {"type": "link", "url": "https://docs.google.com/document/d/1S_hxzZTXuz2kOyiAljHOMCFI9TafTZeNhbxvfVfLCfI/edit"}, {"type": "text", "text": " it is 65% complete"}]}]}]}, {"ts": "1720936166.733009", "text": "i will spend sometime tomorrow on this too", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "HJn9h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i will spend sometime tomorrow on this too"}]}]}]}, {"ts": "1721039544.906619", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8>\n*Update on `In QA`* *<https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|tickets> .* \nThere were around 150 tickets. I targeted tickets from below categories and brought down the tickets to 44.\n\n• *Old feature tickets* - _These were mostly fresh implementations like meritview 2.0 and tested already by <PERSON> and we have bugs reported from those features. So closed._\n• *UI Cosmetics* - _verified and closed. Added screenshots in the tickets._\n• *HR Admin* - _closed as not valid anymore_\n• *Old Merit view* - _closed as not valid anymore_\nThe remaining tickets are around calculations and functionalities and would need couple of days to verify thoroughly.  I will be targeting to close them by Wednesday or Thursday. Some of them are equity related and will assign them to <@U04DKEFP1K8> as those will be covered with Nauto QA. We can discuss more on this in eng discussion if any further questions.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1721040319.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Zh+q6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Update on ", "style": {"bold": true}}, {"type": "text", "text": "In QA", "style": {"bold": true, "code": true}}, {"type": "text", "text": " ", "style": {"bold": true}}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "tickets", "style": {"bold": true}}, {"type": "text", "text": " . ", "style": {"bold": true}}, {"type": "text", "text": "\nThere were around 150 tickets. I targeted tickets from below categories and brought down the tickets to 44.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Old feature tickets", "style": {"bold": true}}, {"type": "text", "text": " - "}, {"type": "text", "text": "These were mostly fresh implementations like meritview 2.0 and tested already by <PERSON> and we have bugs reported from those features. So closed.", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "UI Cosmetics", "style": {"bold": true}}, {"type": "text", "text": " - "}, {"type": "text", "text": "verified and closed. Added screenshots in the tickets.", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "HR Admin", "style": {"bold": true}}, {"type": "text", "text": " - "}, {"type": "text", "text": "closed as not valid anymore", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Old Merit view", "style": {"bold": true}}, {"type": "text", "text": " -"}, {"type": "text", "text": " closed as not valid anymore", "style": {"italic": true}}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nThe remaining tickets are around calculations and functionalities and would need couple of days to verify thoroughly.  I will be targeting to close them by Wednesday or Thursday. Some of them are equity related and will assign them to "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " as those will be covered with Nauto QA. We can discuss more on this in eng discussion if any further questions."}]}]}]}, {"ts": "1721056102.519369", "text": "Sounds like a plan. Thanks for the update.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0l4GW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sounds like a plan. Thanks for the update."}]}]}]}, {"ts": "1721064003.023959", "text": "<@U04DS2MBWP4> Looks like PlayQ login issue is resolved", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721064003.023959", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "LoLOC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Looks like PlayQ login issue is resolved"}]}]}]}, {"ts": "1721064014.889669", "text": "Please confirm", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "K5TAD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please confirm"}]}]}]}, {"ts": "1721091715.057629", "text": "<!here> is the update from today\n\nCainwatters- salary bands fixes looks good, there are still some unresolved issues from reports which i will connect with mahesh durin gthe standup.\nNauto testing updates are here <https://docs.google.com/document/d/1SLa4vJWY7NF8OqvVsT1bUjGek1QAlADusolsW8gHObM/edit>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1721091715.057629", "reply_count": 2, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "vJp29", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " is the update from today\n\nCainwatters- salary bands fixes looks good, there are still some unresolved issues from reports which i will connect with mahesh durin gthe standup.\nNauto testing updates are here "}, {"type": "link", "url": "https://docs.google.com/document/d/1SLa4vJWY7NF8OqvVsT1bUjGek1QAlADusolsW8gHObM/edit"}]}]}]}, {"ts": "1721112540.145139", "text": "Hi <@U04DKEFP1K8> I have created ticket for Vercara bonus (non-OTE) requirements\n<https://compiify.atlassian.net/browse/COM-3427>\nPlease take a look and adde any details missing.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13488::7bd4f7d0433f11efa754f97f9366b9b1", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3427?atlOrigin=eyJpIjoiODYzZGIwNDRiZWVjNDFlYzgwZjU1ODU5Y2MxMDdiMjAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3427 Allow Employee Level bonus components>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13488::7bd4f7d2433f11efa754f97f9366b9b1", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2a19465d953b8640b8f3558fd4f15da9?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13488::7bd4f7d1433f11efa754f97f9366b9b1", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13488\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13488\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3427", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "u+MfO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hi "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I have created ticket for Vercara bonus (non-OTE) requirements\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3427"}, {"type": "text", "text": "\nPlease take a look and adde any details missing."}]}]}]}, {"ts": "1721143837.460839", "text": "Just wrapped alaya care implementation call, i will be 10 -15 minutes late to today's eng meeting ( kid drop off running late)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EGDv3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just wrapped alaya care implementation call, i will be 10 -15 minutes late to today's eng meeting ( kid drop off running late)"}]}]}]}, {"ts": "1721143870.697089", "text": "I thought we<PERSON> cancelled it", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EvNqp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I thought we<PERSON> cancelled it"}]}]}]}, {"ts": "1721144582.696719", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> Lets meet at 9:15 am pst", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "mNG71", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Lets meet at 9:15 am pst"}]}]}]}, {"ts": "1721214828.916949", "text": "Org insights is Live", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721214828.916949", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "J3pA3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Org insights is Live"}]}]}]}, {"ts": "1721228535.140349", "text": "Might run 15 mnts late to eng sync up", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721228535.140349", "reply_count": 2, "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "DRfb7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Might run 15 mnts late to eng sync up"}]}]}]}, {"ts": "1721230686.157899", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> Current structure for storing customer login credentials is very confusing. Going forward can we please use this <https://docs.google.com/spreadsheets/d/1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q/edit?gid=0#gid=0|spreadsheet>. Let's also migrate the existing and active credentials of customers to this spreadsheet please.", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F07CVDTM7PD", "created": 1721230689, "timestamp": 1721230689, "name": "Customer <PERSON>gin Credentials", "title": "Customer <PERSON>gin Credentials", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 11356, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q", "external_url": "https://docs.google.com/spreadsheets/d/1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q/edit?gid=0#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q/edit?gid=0#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHTooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigD/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07CVDTM7PD/customer_login_credentials", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "qIVNE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Current structure for storing customer login credentials is very confusing. Going forward can we please use this "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q/edit?gid=0#gid=0", "text": "spreadsheet"}, {"type": "text", "text": ". Let's also migrate the existing and active credentials of customers to this spreadsheet please."}]}]}]}, {"ts": "1721231292.781119", "text": "<@U0690EB5JE5> we still have the HR admin page showing up for CWA", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "e1zID", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we still have the HR admin page showing up for CWA"}]}]}]}, {"ts": "1721232140.568319", "text": "zoom is not logging me in", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721232140.568319", "reply_count": 14, "blocks": [{"type": "rich_text", "block_id": "ocke<PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "zoom is not logging me in"}]}]}]}, {"ts": "**********.373739", "text": "<@U04DS2MBWP4> Updates\n1. Microsoft login issue were resolved on production environment (verified with my microsoft id) by mahesh and me. \n2. Here is the analysis for pay band employee count issue. In the attached image application currently displays list of employees filtered by (department, job category, job level and  region)\n       but for correctly mapping the band to employees another identifier was used ( job title).  If job title is taken into account then you will see 7 accounting coordinator.  This should not be treated as a blocker.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.373739", "reply_count": 9, "files": [{"id": "F07CWEBUHGT", "created": **********, "timestamp": **********, "name": "Screenshot 2024-07-17 at 11.20.40 AM.png", "title": "Screenshot 2024-07-17 at 11.20.40 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 219483, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07CWEBUHGT/screenshot_2024-07-17_at_11.20.40___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07CWEBUHGT/download/screenshot_2024-07-17_at_11.20.40___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_360.png", "thumb_360_w": 360, "thumb_360_h": 193, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_480.png", "thumb_480_w": 480, "thumb_480_h": 258, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_720.png", "thumb_720_w": 720, "thumb_720_h": 386, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_800.png", "thumb_800_w": 800, "thumb_800_h": 429, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_960.png", "thumb_960_w": 960, "thumb_960_h": 515, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 550, "original_w": 1852, "original_h": 994, "thumb_tiny": "AwAZADDQ5z93P40uT/dP50velFADcn+6fzoyf7p/OnUd6AG5P90/nQCc8rj8adR3oATvQKx6KANmjvWNRQBs0VjUUAf/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07CWEBUHGT/screenshot_2024-07-17_at_11.20.40___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07CWEBUHGT-16fb4ad1c2", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "kPFu9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Updates\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Microsoft login issue were resolved on production environment (verified with my microsoft id) by mahesh and me. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the analysis for pay band employee count issue. In the attached image application currently displays list of employees filtered by (department, job category, job level and  region)"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "       but for correctly mapping the band to employees another identifier was used ( job title).  If job title is taken into account then you will see 7 accounting coordinator.  This should not be treated as a blocker."}]}]}]}, {"ts": "**********.813619", "text": "saurab<PERSON>.jain has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R07CU7X1KN1", "block_id": "FyqBw", "api_decoration_available": false, "call": {"v1": {"id": "R07CU7X1KN1", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": **********, "active_participants": [], "all_participants": [{"slack_id": "U04DKEFP1K8"}, {"slack_id": "U04DS2MBWP4"}], "display_id": "885-7086-2795", "join_url": "https://us06web.zoom.us/j/88570862795?pwd=tvAz6JiWB4iTzfYVgkLOIsVG9zJ4bk.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEXzg4Zjg0YWNlYzY0OTQ4YmViZmM1YWFlYjNhMTRkOTlhJnVzcz1yU3A1Y0cxdm9PUWdqOHZRYlpSYW5iR1F0UXVxMWxUQm5sU2xyYXBqZmNLa3VkZjgxSW5ERU80OTI2RDdwd0JXbVhkX1BTdU92WjJwTkNubXpOQ1BuQVFpMHFzNTRoWkM1RXVKdkNsSE12SzlCR3JLYk92UnFfcW54Mm5KZDNjYXozRU4xTzUzQTVGR1haRGlTQS43RC1HNEtGQW1IMjlBaVRw&action=join&confno=88570862795&pwd=tvAz6JiWB4iTzfYVgkLOIsVG9zJ4bk.1", "name": "Zoom meeting started by <PERSON><PERSON><PERSON><PERSON>", "created_by": "U05185RFCNT", "date_end": 1721247500, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "WUZQx", "text": {"type": "mrkdwn", "text": "Meeting passcode: tvAz6JiWB4iTzfYVgkLOIsVG9zJ4bk.1", "verbatim": false}}]}, {"ts": "1721255847.123569", "text": "we should cancel tomorrow's biweekly happy hour", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Qvbpx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we should cancel tomorrow's biweekly happy hour"}]}]}]}, {"ts": "1721255930.814579", "text": "which environment are we using for tomorrow's kornferry demo?", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "odUpB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "which environment are we using for tomorrow's kornferry demo?"}]}]}]}, {"ts": "1721255949.737039", "text": "Leta use demo environment ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VVQJ1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Leta use demo environment "}]}]}]}, {"ts": "1721276112.898199", "text": "<@U0690EB5JE5> demo envrionment is the old staging env correct?", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BBA3u", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " demo envrionment is the old staging env correct?"}]}]}]}, {"ts": "1721300623.625159", "text": "<@U04DKEFP1K8> <http://nauto.stridehr.io|nauto.stridehr.io> is ready", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721300623.625159", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "a01Qp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://nauto.stridehr.io", "text": "nauto.stridehr.io"}, {"type": "text", "text": " is ready"}]}]}]}, {"ts": "1721301702.274019", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> UPDATE: Org Insights will be hidden in Production ENVs but not test ENVs", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721214828.916949", "subtype": "thread_broadcast", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "VSfwN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " UPDATE: Org Insights will be hidden in Production ENVs but not test ENVs"}]}]}]}, {"ts": "1721317669.598749", "text": "I have meeting at 9.30am PST. Will have to drop after first 30mnts of eng discussion.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "koS5L", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have meeting at 9.30am PST. Will have to drop after first 30mnts of eng discussion."}]}]}]}, {"ts": "1721324357.223289", "text": "prepping for demo with KornFerry. I am encountering bugs:\nFilters are not always consistent and show errors depending on the filtering sequence in org view\nIn Merit view, I can not filter by performance ratings.\nwill provide more update after the call", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721324357.223289", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "FnJ9i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "prepping for demo with KornFerry. I am encountering bugs:\nFilters are not always consistent and show errors depending on the filtering sequence in org view\nIn Merit view, I can not filter by performance ratings.\nwill provide more update after the call"}]}]}]}, {"ts": "1721324812.418579", "text": "Also the flags shown on the org insights are not correct", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721324812.418579", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "uDcAj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also the flags shown on the org insights are not correct"}]}]}]}, {"ts": "1721325246.446509", "text": "sequence of performance rating scale is not correct in the demo env", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721325246.446509", "reply_count": 1, "files": [{"id": "F07D2KXKVEX", "created": 1721325244, "timestamp": 1721325244, "name": "Screenshot 2024-07-18 at 10.53.15 AM.png", "title": "Screenshot 2024-07-18 at 10.53.15 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 96967, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07D2KXKVEX/screenshot_2024-07-18_at_10.53.15___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07D2KXKVEX/download/screenshot_2024-07-18_at_10.53.15___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_360.png", "thumb_360_w": 360, "thumb_360_h": 208, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_480.png", "thumb_480_w": 480, "thumb_480_h": 278, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_720.png", "thumb_720_w": 720, "thumb_720_h": 417, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_800.png", "thumb_800_w": 800, "thumb_800_h": 463, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_960.png", "thumb_960_w": 960, "thumb_960_h": 556, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 593, "original_w": 1496, "original_h": 866, "thumb_tiny": "AwAbADDS2j0oCgdBQOp+tRXDEIMMRz2oQE2aKo73x95vzo8xv77fnVco7F7IoqiHb+8x/GjzG/vt+dHKFi6OCc9zTJV3rgYPNSUVIir5WODtz9acsJB5AqxgUYFVzAQGNR1C0vlD+6KmwKMClcD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07D2KXKVEX/screenshot_2024-07-18_at_10.53.15___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07D2KXKVEX-0accfc8160", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "5Aki7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "sequence of performance rating scale is not correct in the demo env"}]}]}]}, {"ts": "1721325346.633659", "text": "<@U04DS2MBWP4> I will take a look at the data issues in demo ENV tomorrow. Looks like some data backfill is required. Would be more involved effort.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "mf+qx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I will take a look at the data issues in demo ENV tomorrow. Looks like some data backfill is required. Would be more involved effort."}]}]}]}, {"ts": "1721423947.685819", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> check out the new comp builder workflow. We still need to make revisions to it but here is the first draft. We can go over this during the next eng call and discuss what we could do to make it better. <PERSON><PERSON> is still working on redesigining the allocation page.\nFew things to consider:\nDo we need any setting regarding OTE and paymix in the comp builder?\nShould we include setting adjustment letters within comp builder or outside of it?\nWe should add include and exclude employees by job titles in the eligibility settings?\nWhat is the purpose of final approver in the comp builder? does it make sense to keep it?\nWe need to add cost of living adjustments?\nDo we need top down and bottom up approach?\nMove uploading promotions to settings>Upload\nDo we need currency convertor separately at org level and cycle level?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721423947.685819", "reply_count": 5, "edited": {"user": "U04DS2MBWP4", "ts": "1721427606.000000"}, "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "HNrlm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " check out the new comp builder workflow. We still need to make revisions to it but here is the first draft. We can go over this during the next eng call and discuss what we could do to make it better. <PERSON><PERSON> is still working on redesigining the allocation page.\nFew things to consider:\nDo we need any setting regarding OTE and paymix in the comp builder?\nShould we include setting adjustment letters within comp builder or outside of it?\nWe should add include and exclude employees by job titles in the eligibility settings?\nWhat is the purpose of final approver in the comp builder? does it make sense to keep it?\nWe need to add cost of living adjustments?\nDo we need top down and bottom up approach?\nMove uploading promotions to settings>Upload\nDo we need currency convertor separately at org level and cycle level?"}]}]}]}, {"ts": "1721423955.425009", "text": "<https://www.figma.com/proto/WOJVdr3bbnBloZT9z6pY9K/Design-file-(Copy)?node-id=6926-41358&amp;t=1vz7Nsg0nLh7fisF-1&amp;scaling=scale-down&amp;content-scaling=fixed&amp;page-id=2201%3A74500&amp;starting-point-node-id=6592%3A97897&amp;show-proto-sidebar=1|https://www.figma.com/proto/WOJVdr3bbnBloZT9z6pY9K/Design-file-(Copy)?node-id=6926-[…]74500&amp;starting-point-node-id=6592%3A97897&amp;show-proto-sidebar=1>", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "kzyF6", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://www.figma.com/proto/WOJVdr3bbnBloZT9z6pY9K/Design-file-(Copy)?node-id=6926-41358&t=1vz7Nsg0nLh7fisF-1&scaling=scale-down&content-scaling=fixed&page-id=2201%3A74500&starting-point-node-id=6592%3A97897&show-proto-sidebar=1", "text": "https://www.figma.com/proto/WOJVdr3bbnBloZT9z6pY9K/Design-file-(Copy)?node-id=6926-[…]74500&starting-point-node-id=6592%3A97897&show-proto-sidebar=1"}]}]}]}, {"ts": "1721428347.939479", "text": "do we want to allow them to duplicate old cycle as well as an active cycle?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721428347.939479", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "FvZpb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "do we want to allow them to duplicate old cycle as well as an active cycle?"}]}]}]}, {"ts": "1721447561.869259", "text": "<@U04DS2MBWP4> we should avoid sharing credentials via email as it’s unsecure and everyone in the email.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7DFKn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " we should avoid sharing credentials via email as "}, {"type": "text", "text": "it’s"}, {"type": "text", "text": " unsecure and everyone in the email."}]}]}]}, {"ts": "1721447593.045489", "text": "We should find secure way to share credentials ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721447593.045489", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Ap/Hd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We should find secure way to share credentials "}]}]}]}, {"ts": "1721447603.585839", "text": "To Indvidual ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Zp2uU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "To Indvidual "}]}]}]}, {"ts": "1721579645.007949", "text": "<@U04DKEFP1K8> could you please confirm if we are ready to provide Nauto with their production environment?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721579645.007949", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "nCLvb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " could you please confirm if we are ready to provide Nauto with their production environment?"}]}]}]}, {"ts": "1721637668.003519", "text": "<@U04DS2MBWP4> The design looks great. However my two cents, I feel that we should also review our current cycle builder capabilities and address them at once. Like work on a cycle builder v2.\n\nAlso I think we should prioritize, UX improvements for *Merit View* and *Pay bands* over cycle builder as those would be frequently visited vs Cycle builder for better ROI and impact.\nThere is a lot of scope of improvement in both these areas.\nAnd we also need to work UI design for customer facing *Audit Log* for both Merit planning edits and employee edits*.* This will be very critical feature for a lot of reasons.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721423947.685819", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1721639068.000000"}, "blocks": [{"type": "rich_text", "block_id": "/GW7F", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " The design looks great. However my two cents, I feel that we should also review our current cycle builder capabilities and address them at once. Like work on a cycle builder v2.\n\nAlso I think we should prioritize, UX improvements for "}, {"type": "text", "text": "Merit View", "style": {"bold": true}}, {"type": "text", "text": " and "}, {"type": "text", "text": "Pay bands", "style": {"bold": true}}, {"type": "text", "text": " over cycle builder as those would be frequently visited vs Cycle builder for better ROI and impact.\nThere is a lot of scope of improvement in both these areas.\nAnd we also need to work UI design for customer facing "}, {"type": "text", "text": "<PERSON>t Log", "style": {"bold": true}}, {"type": "text", "text": " for both Merit planning edits and employee edits"}, {"type": "text", "text": ".", "style": {"bold": true}}, {"type": "text", "text": " This will be very critical feature for a lot of reasons."}]}]}]}, {"ts": "1721639788.500669", "text": "<@U04DKEFP1K8> <@U04DS2MBWP4> I have created a new sheet (*`RoadMap-August`* ) and added list of road map items based on what I know.\n<https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=286802099#gid=286802099>\nPlease do take a look and add if anything is missed. We should review this in one of the eng discussions for prioritization.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721639788.500669", "reply_count": 3, "edited": {"user": "U0690EB5JE5", "ts": "1721640095.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06MMTVHJCA", "created": 1709446944, "timestamp": 1709446944, "name": "Project Plan - March to May", "title": "Roadmap - <PERSON><PERSON><PERSON>", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk", "external_url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSJOelJlvSnUUANy3pSjPelooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06MMTVHJCA/project_plan_-_march_to_may", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "iHi0E", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I have created a new sheet ("}, {"type": "text", "text": "RoadMap-August", "style": {"bold": true, "code": true}}, {"type": "text", "text": " ) and added list of road map items based on what I know.\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=286802099#gid=286802099"}, {"type": "text", "text": "\nPlease do take a look and add if anything is missed. We should review this in one of the eng discussions for prioritization."}]}]}]}, {"ts": "1721650569.704989", "text": "<@U04DKEFP1K8> the issue is fixed and recreated the db. Please upload the data and let me know if you still see issue.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721579645.007949", "subtype": "thread_broadcast", "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4", "U04DKEFP1K8"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "St+Wy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " the issue is fixed and recreated the db. Please upload the data and let me know if you still see issue."}]}]}]}, {"ts": "1721658942.583789", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> How do we find out how active CWA is in the prod environment and where are they clicking? Is it thru heap and clarity?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721658942.583789", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "c2/3t", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " How do we find out how active CWA is in the prod environment and where are they clicking? Is it thru heap and clarity?"}]}]}]}, {"ts": "1721669883.323299", "text": "<@U04DKEFP1K8> can you remove rachel from the eng calls&gt;", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "done", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "XdqMy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you remove rachel from the eng calls>"}]}]}]}, {"ts": "1721670518.130919", "text": "Actions items from July 22 eng call:\n\n<@U04DKEFP1K8>\nGet nauto env up and running including <PERSON><PERSON><PERSON>'s login\nSchedule meeting with Delany\nTest sorting functionality\nRevalidate fixed bugs\nTest Vercara's environment for bonus-related issues\nTest org view\nProvide feedback on compensation cycle builder by Wednesday\nSend message to vercara for OT employee sample data\n\n<@U04DS2MBWP4>\nSend note to Playq\nWork with <PERSON>oya on merit planning view categorization\n\n<@U0690EB5JE5>\nThink about and define scope for audit log feature)", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721670518.130919", "reply_count": 9, "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "I8+Xs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Actions items from July 22 eng call:\n\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "\nGet nauto env up and running including <PERSON><PERSON><PERSON>'s login\nSchedule meeting with Delany\nTest sorting functionality\nRevalidate fixed bugs\nTest Vercara's environment for bonus-related issues\nTest org view\nProvide feedback on compensation cycle builder by Wednesday\nSend message to vercara for OT employee sample data\n\n"}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": "\nSend note to Playq\nWork with <PERSON><PERSON> on merit planning view categorization\n\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "\nThink about and define scope for audit log feature)"}]}]}]}, {"ts": "**********.245649", "text": "<@U04DKEFP1K8> are we ready to hand over the test or prod account to valgenesis?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.245649", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "9lRD1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " are we ready to hand over the test or prod account to valgenesis?"}]}]}]}, {"ts": "**********.285349", "text": "<!here>\n\n• Get nauto env up and running including <PERSON><PERSON><PERSON>'s login - :white_check_mark:\n• Schedule meeting with <PERSON><PERSON> - :white_check_mark:\n• Test sorting functionality - In progress\n• Revalidate fixed bugs - :white_check_mark: ( Few equity issues are still unaddressed , will discuss these with <@U0690EB5JE5>)\n• Test Vercara's environment for bonus-related issues - Bonus details are still pending to be implemented in Org view, will proceed once this work completes\n• Test org insights - *In progress ( 60% complete , issues are reported in Nauto testing document)*\n• Provide feedback on compensation cycle builder by Wednesday\n• Send message to vercara for OT employee sample data - :white_check_mark:", "user": "U04DKEFP1K8", "type": "message", "edited": {"user": "U04DKEFP1K8", "ts": "**********.000000"}, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8omdX", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": "\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Get nauto env up and running including <PERSON><PERSON><PERSON>'s login - "}, {"type": "emoji", "name": "white_check_mark", "unicode": "2705"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Schedule meeting with <PERSON><PERSON> - "}, {"type": "emoji", "name": "white_check_mark", "unicode": "2705"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Test sorting functionality - In progress"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Revalidate fixed bugs - "}, {"type": "emoji", "name": "white_check_mark", "unicode": "2705"}, {"type": "text", "text": " ( Few equity issues are still unaddressed , will discuss these with "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Test Vercara's environment for bonus-related issues - Bonus details are still pending to be implemented in Org view, will proceed once this work completes"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Test org insights - "}, {"type": "text", "text": "In progress ( 60% complete , issues are reported in Nauto testing document)", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Provide feedback on compensation cycle builder by Wednesday"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Send message to vercara for OT employee sample data - "}, {"type": "emoji", "name": "white_check_mark", "unicode": "2705"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1721756388.240129", "text": "Action items from July 23 eng call\n<@U04DKEFP1K8>\nComplete testing of org insights and sorting\nTest equity\nFinish the customer implementation roadmap spreadsheet\nRetest merit view task page", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "GUgP0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Action items from July 23 eng call\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "\nComplete testing of org insights and sorting\nTest equity\nFinish the customer implementation roadmap spreadsheet\nRetest merit view task page"}]}]}]}, {"ts": "1721759176.685149", "text": "<!here> Instead of the the leadership meeting, I have set up a call with <PERSON><PERSON> to go over the audit log and merit view organization", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hqg3L", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Instead of the the leadership meeting, I have set up a call with <PERSON><PERSON> to go over the audit log and merit view organization"}]}]}]}, {"ts": "**********.771809", "text": "<@U04DKEFP1K8> are these <https://drive.google.com/drive/u/0/folders/1c6Nbcbg5uE6I-Xz9ZTlb2DgFYCmMpPlG|data templates> stored in the implementation folder up to date? Need to send these to RightwayHealth", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.771809", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "esCQY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " are these "}, {"type": "link", "url": "https://drive.google.com/drive/u/0/folders/1c6Nbcbg5uE6I-Xz9ZTlb2DgFYCmMpPlG", "text": "data templates"}, {"type": "text", "text": " stored in the implementation folder up to date? Need to send these to RightwayHealth"}]}]}]}, {"ts": "**********.772419", "text": "<@U04DS2MBWP4> going forward please download recording of customer implementation related meetings and upload in recording folder for every customer, i will follow the same if i am the organizer.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.772419", "reply_count": 1, "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "done", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "sqwX/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " going forward please download recording of customer implementation related meetings and upload in recording folder for every customer, i will follow the same if i am the organizer."}]}]}]}, {"ts": "**********.098499", "text": "<@U04DS2MBWP4> From we<PERSON>'s inbox can you send me recording link for the meeting that happended with <PERSON><PERSON><PERSON> on July 16th at 8am PT", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.098499", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Cm61T", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " From we<PERSON>'s inbox can you send me recording link for the meeting that happended with <PERSON><PERSON><PERSON> on July 16th at 8am PT"}]}]}]}, {"ts": "**********.245839", "text": "<@U04DS2MBWP4> There was a meeting on july 16th with dgoc team for which <PERSON><PERSON> was organizer , can you find the link to meeting recording , meeting occured between 12-1245 pm", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.245839", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Y/Gan", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " There was a meeting on july 16th with dgoc team for which <PERSON><PERSON> was organizer , can you find the link to meeting recording , meeting occured between 12-1245 pm"}]}]}]}, {"ts": "**********.692199", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> can we please create a prod env for RightWayHealth?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.692199", "reply_count": 3, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "EoP5e", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we please create a prod env for RightWayHealth?"}]}]}]}, {"ts": "**********.793509", "text": "<@U04DKEFP1K8> Have pushed all the fixes/changes to production ", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "rocket", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "IxgqJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Have pushed all the fixes/changes to production "}]}]}]}, {"ts": "**********.407649", "text": "Guys, feel free to add agenda items. Few things on my mind:\n\n1. Updates on action items from last two calls. <@U04DKEFP1K8> it's easier to add checkmarks to the list just like you did above. its way easier to track that way.\n2. Status of OTE for Vercara\n3. Divide and conquer product management\n4. Best practices for converting current beta customers to fully paid for their next cycle", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "t+nIZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Guys, feel free to add agenda items. Few things on my mind:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Updates on action items from last two calls. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " it's easier to add checkmarks to the list just like you did above. its way easier to track that way."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Status of OTE for Vercara"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Divide and conquer product management"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Best practices for converting current beta customers to fully paid for their next cycle"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1721864089.584679", "text": "<!here> update on valgenesis qa, running into some unexpected error ( these may go away if i re-upload the data but i will have engg team confirm that first )\n<@U0690EB5JE5> i will discuss these in standup <https://docs.google.com/document/d/1QAmFOUJ6UKSD_JL3WUxoqNCSq6r3ILmDzKjxqGSSy4k/edit>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1721864089.584679", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "AF9pc", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " update on valgenesis qa, running into some unexpected error ( these may go away if i re-upload the data but i will have engg team confirm that first )\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " i will discuss these in standup "}, {"type": "link", "url": "https://docs.google.com/document/d/1QAmFOUJ6UKSD_JL3WUxoqNCSq6r3ILmDzKjxqGSSy4k/edit"}]}]}]}, {"ts": "1721927821.811799", "text": "for Audit log this was what My thinking.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1721928966.000000"}, "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F07DNGWR5MM", "created": 1721927819, "timestamp": 1721927819, "name": "Screenshot 2024-07-25 at 10.46.31 PM.png", "title": "Screenshot 2024-07-25 at 10.46.31 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 155723, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07DNGWR5MM/screenshot_2024-07-25_at_10.46.31___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07DNGWR5MM/download/screenshot_2024-07-25_at_10.46.31___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07DNGWR5MM-dd9c184421/screenshot_2024-07-25_at_10.46.31___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07DNGWR5MM-dd9c184421/screenshot_2024-07-25_at_10.46.31___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07DNGWR5MM-dd9c184421/screenshot_2024-07-25_at_10.46.31___pm_360.png", "thumb_360_w": 297, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07DNGWR5MM-dd9c184421/screenshot_2024-07-25_at_10.46.31___pm_480.png", "thumb_480_w": 396, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07DNGWR5MM-dd9c184421/screenshot_2024-07-25_at_10.46.31___pm_160.png", "original_w": 573, "original_h": 694, "thumb_tiny": "AwAwACfR7/xUu72P5Ud+lHc8UALmk3ex/KlpB9KAFooooAb/ABdKO/Iowc0YNAC9O1IP92lxikwaAHUUUUAJ/F3o796WigApAfY0tFABRTA7F9pQgf3qf+NAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07DNGWR5MM/screenshot_2024-07-25_at_10.46.31___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07DNGWR5MM-72e9ca3d60", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "EIrJR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "for Audit log this was what My thinking."}]}]}]}, {"ts": "1721927828.926909", "text": "<@U04DS2MBWP4>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8n/+W", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}]}]}]}, {"ts": "1721927853.491149", "text": "No functional categorization just show what changed and who changed. One for employee and one for merit planning. image is missing the author of the change. Image is to just convey the gist of it. This way audit will be simple to implement as well. Let me know your thoughts on this.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1721928101.000000"}, "blocks": [{"type": "rich_text", "block_id": "zA92O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No functional categorization just show what changed and who changed. One for employee and one for merit planning. image is missing the author of the change. Image is to just convey the gist of it. This way audit will be simple to implement as well. Let me know your thoughts on this."}]}]}]}, {"ts": "1721927862.563089", "text": "<@U04DS2MBWP4> kellie's credentials have been updated in the doc", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "7QdbH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " kellie's credentials have been updated in the doc"}]}]}]}, {"ts": "1721928514.113479", "text": "Feedback from Alayacare- They might need HR business partners role in addition to super admin. where HR business partner will only have access to the data for their corresponding departments. I know we have a HR business partner role in user roles but not sure if we ever worked on it", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721928514.113479", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "B0L5D", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Feedback from Alayacare- They might need HR business partners role in addition to super admin. where HR business partner will only have access to the data for their corresponding departments. I know we have a HR business partner role in user roles but not sure if we ever worked on it"}]}]}]}, {"ts": "1721946619.957689", "text": "<@U04DKEFP1K8> Did we make any changes to the merit view of wrt to bonus? if so, where can I see it? I am not seeing any new fields for bonus in the <http://test.stridehr.io|test.stridehr.io>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721946619.957689", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "P62l4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Did we make any changes to the merit view of wrt to bonus? if so, where can I see it? I am not seeing any new fields for bonus in the "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}]}]}]}, {"ts": "1721947946.470399", "text": "<@U0690EB5JE5> retested org insights, added observation here <https://docs.google.com/document/d/1SLa4vJWY7NF8OqvVsT1bUjGek1QAlADusolsW8gHObM/edit>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1721947946.470399", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "BEkau", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " retested org insights, added observation here "}, {"type": "link", "url": "https://docs.google.com/document/d/1SLa4vJWY7NF8OqvVsT1bUjGek1QAlADusolsW8gHObM/edit"}]}]}]}, {"ts": "**********.481169", "text": "<@U0690EB5JE5> I am seeing a lot of bugs/errors on bonus and total target pay calculations. I will let <PERSON><PERSON><PERSON><PERSON> and you completing the testing before I do another round of testing", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.481169", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "3TGh1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am seeing a lot of bugs/errors on bonus and total target pay calculations. I will let <PERSON><PERSON><PERSON><PERSON> and you completing the testing before I do another round of testing"}]}]}]}, {"ts": "**********.514899", "text": "<@U0690EB5JE5> in the demo account, I tried to edit the active cycle by removing equity in the comp builder configurations and now I am unable to go past the bonus calculations screen. Is this a known bug?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.514899", "reply_count": 10, "blocks": [{"type": "rich_text", "block_id": "pt8YA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " in the demo account, I tried to edit the active cycle by removing equity in the comp builder configurations and now I am unable to go past the bonus calculations screen. Is this a known bug?"}]}]}]}, {"ts": "**********.648849", "text": "<@U0690EB5JE5> In the merit planning view, we should change the \"one time bonus\" to \"lump sum award\" since it's lump sum that is typically included with salary budgets. One time bonus will come under the variable comp (bonus) category", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.648849", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "YGgMS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " In the merit planning view, we should change the \"one time bonus\" to \"lump sum award\" since it's lump sum that is typically included with salary budgets. One time bonus will come under the variable comp (bonus) category"}]}]}]}, {"ts": "**********.583399", "text": "For nuato, For  <PERSON>, some of her equity shows 3 yrs and some show 5 yrs vesting. Is this a bug?", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F07ETMS0K16", "created": 1721952106, "timestamp": 1721952106, "name": "Screenshot 2024-07-25 at 5.01.41 PM.png", "title": "Screenshot 2024-07-25 at 5.01.41 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 363953, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07ETMS0K16/screenshot_2024-07-25_at_5.01.41___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07ETMS0K16/download/screenshot_2024-07-25_at_5.01.41___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 300, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 400, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 600, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 666, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 800, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 853, "original_w": 2060, "original_h": 1716, "thumb_tiny": "AwAnADDSzQCCOKWkHbrQAuRnFJkZx3o45o7+5oAPxo/Gjn2o5xxQAtIDSmkoAMnmjnIpe1A6UAJR2/8Ar0evT8qO3b8qAFooooAKBRRQAn50du9LRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07ETMS0K16/screenshot_2024-07-25_at_5.01.41___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07ETMS0K16-ff5777c023", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For nuato, For  <PERSON>, some of her equity shows 3 yrs and some show 5 yrs vesting. Is this a bug?"}]}]}]}, {"ts": "1721952332.935969", "text": "<@U0690EB5JE5> It also appears that equity calculations are wrong? Is this a calc issue or am I missing something?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lswWc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " It also appears that equity calculations are wrong? Is this a calc issue or am I missing something?"}]}]}]}, {"ts": "1721952884.513209", "text": "nevermind, <PERSON><PERSON><PERSON><PERSON> explained. That's how their data is", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721952884.513209", "reply_count": 1, "edited": {"user": "U04DS2MBWP4", "ts": "1721952895.000000"}, "blocks": [{"type": "rich_text", "block_id": "kqTZx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "nevermind, <PERSON><PERSON><PERSON><PERSON> explained. That's how their data is"}]}]}]}, {"ts": "1721955017.938159", "text": "<@U04DS2MBWP4> We will look into the issues reported above and update your morning.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PemW1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " We will look into the issues reported above and update your morning."}]}]}]}, {"ts": "1722005834.657669", "text": "<@U0690EB5JE5> FR and AR is still showing up in drop down list of countires to select even when there are no emp. I brought this issue up previously and we decided to re-upload the data to fix it. Is it also a regression issue? when can we fix it?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722005834.657669", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "3/OVm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " FR and AR is still showing up in drop down list of countires to select even when there are no emp. I brought this issue up previously and we decided to re-upload the data to fix it. Is it also a regression issue? when can we fix it?"}]}]}]}, {"ts": "1722005891.962749", "text": "even if I remove the countries in comp builder, these countries still show up", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722005891.962749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "UiU/a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "even if I remove the countries in comp builder, these countries still show up"}]}]}]}, {"ts": "1722006661.997419", "text": "If you are past the merit cycle due date, can you still submit recommendations?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722006661.997419", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "P5trF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If you are past the merit cycle due date, can you still submit recommendations?"}]}]}]}, {"ts": "**********.734519", "text": "for <PERSON><PERSON>, in merit view target bonus is 5040, whereas in the org view, her variable pay is listed as 126000. it looks like we might still have different sources of truth for reading the data", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.734519", "reply_count": 16, "blocks": [{"type": "rich_text", "block_id": "jhIWJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "for <PERSON><PERSON>, in merit view target bonus is 5040, whereas in the org view, her variable pay is listed as 126000. it looks like we might still have different sources of truth for reading the data"}]}]}]}, {"ts": "**********.870089", "text": "all of my testing is on demo account", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FeapD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "all of my testing is on demo account"}]}]}]}, {"ts": "**********.145529", "text": "as a L5 manager, if I am approving L4's recommendations for their team, I should be able to see if their recommendations were above or below their budget. Today I am not able to see it in the current design. I think it's a flaw", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.145529", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "cw5av", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "as a L5 manager, if I am approving L4's recommendations for their team, I should be able to see if their recommendations were above or below their budget. Today I am not able to see it in the current design. I think it's a flaw"}]}]}]}, {"ts": "**********.351059", "text": "<@U0690EB5JE5> lets meet now", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RB4tZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " lets meet now"}]}]}]}, {"ts": "1722009679.082349", "text": "i wanted to get update on items to test that are ready", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FSgYY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i wanted to get update on items to test that are ready"}]}]}]}, {"ts": "1722009687.210109", "text": "are we not joining Design meeting?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FSDgy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "are we not joining Design meeting?"}]}]}]}, {"ts": "1722009696.528629", "text": "that is at after 30 min", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Vtv6B", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "that is at after 30 min"}]}]}]}, {"ts": "1722009712.456829", "text": "we are joining now", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we are joining now"}]}]}]}, {"ts": "1722009719.162069", "text": "its now", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lKTPL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "its now"}]}]}]}, {"ts": "1722009724.015629", "text": "<@U04DKEFP1K8>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mRXN8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1722009737.065349", "text": "i thought we had used leadership meeting time for it which was 930 am start", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "JuV8K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i thought we had used leadership meeting time for it which was 930 am start"}]}]}]}, {"ts": "1722009739.823979", "text": "will join", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EdfAy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will join"}]}]}]}, {"ts": "1722009899.514049", "text": "<@U0690EB5JE5> did we merge  bonus fixes and ote dev work?", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BAKUi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " did we merge  bonus fixes and ote dev work?"}]}]}]}, {"ts": "1722009984.603019", "text": "<@U04DKEFP1K8> yes I have merged all the fixes for your findings and also target bonus fixes and OTE. I haven't checked in detail as I just came home around my 7pm and merged all the PRs and deployed in dev and staging.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1722010006.000000"}, "blocks": [{"type": "rich_text", "block_id": "mOzjk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " yes I have merged all the fixes for your findings and also target bonus fixes and OTE. I haven't checked in detail as I just came home around my 7pm and merged all the PRs and deployed in dev and staging."}]}]}]}, {"ts": "1722010007.815339", "text": "cool, i will test it then", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "cool, i will test it then"}]}]}]}, {"ts": "1722010028.121119", "text": "also what s the update from vlad on adjustment letters?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722010028.121119", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "KPHAV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "also what s the update from vlad on adjustment letters?"}]}]}]}, {"ts": "1722010424.639709", "text": "<!here> are my primary task for today\n1. Re-test Bonus\n2. Test OTE\n3. Re-sync <PERSON><PERSON>'s data via namely ( this is manual effort)\n4. Re-test merit and org insights feedabck from yesterday ( <@U0690EB5JE5> are these merged as well)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jWpVF", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " are my primary task for today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Re-test Bonus"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Test OTE"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Re-sync Nauto's data via namely ( this is manual effort)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Re-test merit and org insights feedabck from yesterday ( "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " are these merged as well)"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1722013480.497109", "text": "saurab<PERSON>.jain has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R07E67YF98V", "block_id": "qRrM6", "api_decoration_available": false, "call": {"v1": {"id": "R07E67YF98V", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1722013480, "active_participants": [], "all_participants": [{"slack_id": "U04DKEFP1K8"}, {"external_id": "FumCdQY_Rx6JmI_F0M3iGw", "avatar_url": "", "display_name": "<PERSON><PERSON><PERSON>"}], "display_id": "810-1178-7762", "join_url": "https://us06web.zoom.us/j/81011787762?pwd=6gb4JHZoU8ci6pu0NmYPHiaTMKI58k.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEX2ViZDdjYzI2NWJmMzQ5OWY4YTZkNjAwNjAzNjdmNWM1JnVzcz1TQVBfaXRRU0VQWUN5b09WZVF1enFsSVdGaE10cWVhNHlyRWp5ZEhzOWFsRkx1SmdKTlQwbndHWFVEdnZoZGUtRlowVHUwOUdReDVuOFgtbmxlNVJMcFYtbFBVRlRuVlV2WWdRMzFDX1pxUVN3T2NReC0zMmZxX1BLa2xMUmQ5ODJybGs2QU1ZUTNndlh1dXhwazBXRzFXZWlzbFBnay1NRk1PSmZQelpVOWVXV244cjM3bGdxTm90TjltYUhlRUc1ckNyM0dfd2xmaV9CVVZPZThiOTV0LUoudmhXQWFoRTI4dUo1SEgwcg%3D%3D&action=join&confno=81011787762&pwd=6gb4JHZoU8ci6pu0NmYPHiaTMKI58k.1", "name": "Zoom meeting started by <PERSON><PERSON><PERSON><PERSON>", "created_by": "U05185RFCNT", "date_end": 1722014380, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "galNR", "text": {"type": "mrkdwn", "text": "Meeting passcode: 6gb4JHZoU8ci6pu0NmYPHiaTMKI58k.1", "verbatim": false}}]}, {"ts": "1722013487.247719", "text": "<@U0690EB5JE5> we can meet here", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4h2Ue", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we can meet here"}]}]}]}, {"ts": "1722019396.269549", "text": "<@U04DKEFP1K8> what is the ETA of finalizing roadmap implementation sheet?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722019396.269549", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Cdits", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what is the ETA of finalizing roadmap implementation sheet?"}]}]}]}, {"ts": "1722019646.382399", "text": "I will take this over, and will complete it.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YrbNi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will take this over, and will complete it."}]}]}]}, {"ts": "1722019887.324599", "text": "<@U0690EB5JE5> I will time time on you cal to go over the comp builder testing together. I still keep getting stuck every time I try different configurations.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FfAHb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I will time time on you cal to go over the comp builder testing together. I still keep getting stuck every time I try different configurations."}]}]}]}, {"ts": "**********.382069", "text": "<@U0690EB5JE5> can we create login credential for one of our advisors <PERSON> (<mailto:<EMAIL>|<EMAIL>>) for either the test account or demo account.  He will run a test cycle and give us his feedback from a Comp Leader perspective.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.382069", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "ebrvG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we create login credential for one of our advisors <PERSON> ("}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ") for either the test account or demo account.  He will run a test cycle and give us his feedback from a Comp Leader perspective."}]}]}]}, {"ts": "**********.739559", "text": "<@U0690EB5JE5> Verified previously reported bonus issues. Reported new issues for bonus and OTE here <https://docs.google.com/document/d/1SXi8D1g1GQJva0C8q5ZBHWrqJ-j9AoSwz10ikzOw_Hs/edit>. I will be available in your evening on Saturday for any clarification on zoom.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.739559", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "zhYX/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Verified previously reported bonus issues. Reported new issues for bonus and OTE here "}, {"type": "link", "url": "https://docs.google.com/document/d/1SXi8D1g1GQJva0C8q5ZBHWrqJ-j9AoSwz10ikzOw_Hs/edit"}, {"type": "text", "text": ". I will be available in your evening on Saturday for any clarification on zoom."}]}]}]}, {"ts": "1722040983.234669", "text": "<@U0690EB5JE5> Reported couple of error related to merit creation here <https://docs.google.com/document/d/1SLa4vJWY7NF8OqvVsT1bUjGek1QAlADusolsW8gHObM/edit>.\n(<PERSON><PERSON> wants us to delete couple of employees, I was able to delete but merit creation fails afterwards)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722040983.234669", "reply_count": 1, "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "YKPm0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Reported couple of error related to merit creation here "}, {"type": "link", "url": "https://docs.google.com/document/d/1SLa4vJWY7NF8OqvVsT1bUjGek1QAlADusolsW8gHObM/edit"}, {"type": "text", "text": ".\n(<PERSON><PERSON> wants us to delete couple of employees, I was able to delete but merit creation fails afterwards)"}]}]}]}, {"ts": "1722042922.502349", "text": "Sure <@U04DKEFP1K8>. Will take a look", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7FWBK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": ". Will take a look"}]}]}]}, {"ts": "1722044738.985969", "text": "<@U04DKEFP1K8> Are you planning to retest the issues over the weekend? I will try work on the merit creation issues based on that.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ta9LS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Are you planning to retest the issues over the weekend? I will try work on the merit creation issues based on that."}]}]}]}, {"ts": "1722045008.554749", "text": "Yes i have bandwidth on Sunday afternoon ", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OGxte", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes i have bandwidth on Sunday afternoon "}]}]}]}, {"ts": "1722045019.009799", "text": "PT", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "t1s78", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "PT"}]}]}]}, {"ts": "1722048617.483739", "text": ":+1: ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zNKS9", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "+1", "unicode": "1f44d"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1722244823.629049", "text": "Couple of Agenda items for today if nothing higher priority.\n• Engineering priorities to pick up from next week. We will be mostly done with bugs and other current work this week, so we should start putting clear requirements for next priorities\n• Activity Log usecase. <@U04DS2MBWP4> I understand what Activity log is, I am still not clear how this helps additionally vs Audit Log and Employee History together.\n• Adjustment letters Template Generator. There is one more important component of Adjustment letters which we haven't discussed i.e. current process of template generation, its purely manual and we need to think about self serve for this.\n", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1722244985.000000"}, "blocks": [{"type": "rich_text", "block_id": "BrX8E", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Couple of Agenda items for today if nothing higher priority.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Engineering priorities to pick up from next week. We will be mostly done with bugs and other current work this week, so we should start putting clear requirements for next priorities"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Activity Log usecase. "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I understand what Activity log is, I am still not clear how this helps additionally vs Audit Log and Employee History together."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Adjustment letters Template Generator. There is one more important component of Adjustment letters which we haven't discussed i.e. current process of template generation, its purely manual and we need to think about self serve for this."}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1722262856.548989", "text": "Sounds good. ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ndoCd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sounds good. "}]}]}]}, {"ts": "1722266379.987649", "text": "HI <@U07EJ2LP44S> welcome to the team! Looking forward to doing some great things together :partying_face::tada:", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DcoSf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "HI "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " welcome to the team! Looking forward to doing some great things together "}, {"type": "emoji", "name": "partying_face", "unicode": "1f973"}, {"type": "emoji", "name": "tada", "unicode": "1f389"}]}]}]}, {"ts": "1722266470.194269", "text": "Hello! So excited!", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "tada", "users": ["U04DKEFP1K8", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "d/Y46", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hello! So excited!"}]}]}]}, {"ts": "1722267371.475339", "text": "<@U0690EB5JE5> we should add integrations to the priority list. Ideally we should reach a point where we don't have to deal with manual uploads of emp data and comp data", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722267371.475339", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2Z46M", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we should add integrations to the priority list. Ideally we should reach a point where we don't have to deal with manual uploads of emp data and comp data"}]}]}]}, {"ts": "1722267417.105129", "text": "Welcome to the Team <@U07EJ2LP44S>. Excited to learn from you :partyparrot: ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WShuH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Welcome to the Team "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": ". Excited to learn from you "}, {"type": "emoji", "name": "partyparrot"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1722272504.689719", "text": "Roadmap sheet that I was sharing the in meeting today.\n<https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=286802099#gid=286802099>", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06MMTVHJCA", "created": 1709446944, "timestamp": 1709446944, "name": "Project Plan - March to May", "title": "Roadmap - <PERSON><PERSON><PERSON>", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk", "external_url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSJOelJlvSnUUANy3pSjPelooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06MMTVHJCA/project_plan_-_march_to_may", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "FP93F", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Roadmap sheet that I was sharing the in meeting today.\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=286802099#gid=286802099"}]}]}]}, {"ts": "1722273870.865609", "text": "<@U0690EB5JE5> can we pls create social login for <PERSON> for all demo, test and customer environments?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722273870.865609", "reply_count": 10, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "PvQd7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we pls create social login for <PERSON> for all demo, test and customer environments?"}]}]}]}, {"ts": "1722276859.218919", "text": "<!here> I am ready to send out communication to Vercara to update their environment to input Bonus and OTE amounts.\n<@U0690EB5JE5> Good work by the team!!\n<@U07EJ2LP44S> I will be looping you in the email thread with them and we can work out when these updates can go via you going forward.", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}, {"name": "100", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "SgKjW", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I am ready to send out communication to Vercara to update their environment to input Bonus and OTE amounts.\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Good work by the team!!\n"}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I will be looping you in the email thread with them and we can work out when these updates can go via you going forward."}]}]}]}, {"ts": "1722278769.901989", "text": "<@U0690EB5JE5> can we pls create login credentials for another advisor (<PERSON>) for testing environment\nemail <mailto:<EMAIL>|<EMAIL>>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722278769.901989", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "c5uUz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we pls create login credentials for another advisor (<PERSON>) for testing environment\nemail "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}]}]}]}, {"ts": "1722281690.289139", "text": "<@U07EJ2LP44S> Just got off the call with <PERSON>. She is interested in being an IC part-time product manager. We can discuss her fit during the eng call tomorrow. She was 15<PERSON><PERSON>'s first product manager and likes companies at our stage. May be we can give her  1 to 2 projects from the roadmap that <PERSON><PERSON><PERSON> created and see how it goes. What's your experience with her?\n\n<@U0690EB5JE5> <@U04DKEFP1K8> I will share the details during the eng call tomorrow.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Em+8n", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Just got off the call with <PERSON>. She is interested in being an IC part-time product manager. We can discuss her fit during the eng call tomorrow. She was 15Five's first product manager and likes companies at our stage. May be we can give her  1 to 2 projects from the roadmap that <PERSON><PERSON><PERSON> created and see how it goes. What's your experience with her?\n\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I will share the details during the eng call tomorrow."}]}]}]}, {"ts": "1722283533.783259", "text": "I LOVE HER", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6xm4q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I LOVE HER"}]}]}]}, {"ts": "1722283551.030679", "text": "She was one of my favorite PMs, and we've stayed in touch a bit", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "HUu3v", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She was one of my favorite PMs, and we've stayed in touch a bit"}]}]}]}, {"ts": "1722316409.395159", "text": "<@U0690EB5JE5> For compliance purpose can you fill up the following template for atleast 2 of your reportees and send it back by wednesday", "user": "U04DKEFP1K8", "type": "message", "files": [{"id": "F07FAC36HSL", "created": 1722316401, "timestamp": 1722316401, "name": "Copy of Employee Performance Review.docx", "title": "Copy of Employee Performance Review.docx", "mimetype": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filetype": "docx", "pretty_type": "Word Document", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 17148, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07FAC36HSL/copy_of_employee_performance_review.docx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07FAC36HSL/download/copy_of_employee_performance_review.docx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FAC36HSL-b73319ac77/copy_of_employee_performance_review_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F07FAC36HSL-b73319ac77/copy_of_employee_performance_review_thumb_pdf.png", "thumb_pdf_w": 1210, "thumb_pdf_h": 935, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07FAC36HSL/copy_of_employee_performance_review.docx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07FAC36HSL-2afea67bd3", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "hjZQX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " For compliance purpose can you fill up the following template for atleast 2 of your reportees and send it back by wednesday"}]}]}]}, {"ts": "1722316433.915639", "text": "Sure :+1:", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8x92/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure "}, {"type": "emoji", "name": "+1", "unicode": "1f44d"}]}]}]}, {"ts": "1722349539.493299", "text": "has renamed the channel from \"productleadership\" to \"1-productleadership\"", "user": "U04DS2MBWP4", "type": "message", "subtype": "channel_name"}, {"ts": "1722349739.264919", "text": "<!here> Here is the agenda for today's eng meeting. Feel free to add other agenda items:\nPart-Time Product Manager (<PERSON>)\nCustomer Requirements (resume <PERSON><PERSON><PERSON>'s roadmap agenda)\nCustomer Implementation Roadmap\nComp Builder Requirements Doc", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "h0ZEQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Here is the agenda for today's eng meeting. Feel free to add other agenda items:\nPart-Time Product Manager (<PERSON>)\nCustomer Requirements (resume <PERSON><PERSON><PERSON>'s roadmap agenda)\nCustomer Implementation Roadmap\nComp Builder Requirements Doc"}]}]}]}, {"ts": "1722350641.388179", "text": "<@U0690EB5JE5> Looks like Vercara is experiencing the same issue of \"3 dots to edit emp data is not showing up\". Cc <@U07EJ2LP44S>", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "56f4f", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Looks like <PERSON><PERSON><PERSON><PERSON> is experiencing the same issue of \"3 dots to edit emp data is not showing up\". Cc "}, {"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1722350793.358919", "text": "quick update- Comp builder design should be complete by Thursday. New Merit planner (with regrouping) ETA is Friday.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Y+Sbu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "quick update- Comp builder design should be complete by Thursday. New Merit planner (with regrouping) ETA is Friday."}]}]}]}, {"ts": "1722350824.756599", "text": "<@U04DS2MBWP4> will take a look in sometime.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "rUdtX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " will take a look in sometime."}]}]}]}, {"ts": "1722361155.618809", "text": "<!here> I have created an active cycle and option to edit employee details are now visible. Eng team has introdcued a new field to edit last in the UI which needs a validation. I am running that now. We can send a confirmation to Vercara once its done.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722361155.618809", "reply_count": 2, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "3+2HB", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I have created an active cycle and option to edit employee details are now visible. Eng team has introdcued a new field to edit last in the UI which needs a validation. I am running that now. We can send a confirmation to Vercara once its done."}]}]}]}, {"ts": "1722362406.369689", "text": "All intros to <PERSON> are done except Diversified Energy, DegenKolb, and Sonendo. We will do the intro at the time of implementation call with them.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "s1FTz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All intros to <PERSON> are done except Diversified Energy, DegenKolb, and Sonendo. We will do the intro at the time of implementation call with them."}]}]}]}, {"ts": "1722373312.296889", "text": "<@U04DS2MBWP4> ping when you start the implementation meeting , i see you have another meeting in progress", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fUwPi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " ping when you start the implementation meeting , i see you have another meeting in progress"}]}]}]}, {"ts": "1722373339.658449", "text": "<https://us06web.zoom.us/j/81444689654?pwd=et7QrrCbefXeCZpe1964nskETrAtKm.1>", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R07F5AA9BCZ", "block_id": "DmcLW", "api_decoration_available": false, "call": {"v1": {"id": "R07F5AA9BCZ", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1722373340, "active_participants": [], "all_participants": [], "display_id": "814-4468-9654", "join_url": "https://us06web.zoom.us/j/81444689654?pwd=et7QrrCbefXeCZpe1964nskETrAtKm.1", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1722491437, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/81444689654?pwd=et7QrrCbefXeCZpe1964nskETrAtKm.1", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "f2zDB", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://us06web.zoom.us/j/81444689654?pwd=et7QrrCbefXeCZpe1964nskETrAtKm.1"}]}]}]}, {"ts": "**********.013519", "text": "I am on call with <PERSON>. Feel free to join this one", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Xr0/V", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am on call with <PERSON>. Feel free to join this one"}]}]}]}, {"ts": "**********.872819", "text": "<@U04DKEFP1K8> I am not able to sign into Atlassian to approve <PERSON>'s request to join. Do I still have atlassian account?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.872819", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "QjoxP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I am not able to sign into Atlassian to approve <PERSON>'s request to join. Do I still have atlassian account?"}]}]}]}, {"ts": "**********.936369", "text": "Morning team! I have a conflict for todays engineering call; a class I scheduled several weeks ago. Sorry to miss, or if you think it's a critical call let me know.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.936369", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "dikj3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Morning team! I have a conflict for todays engineering call; a class I scheduled several weeks ago. Sorry to miss, or if you think it's a critical call let me know."}]}]}]}, {"ts": "**********.496399", "text": "*<!here>* Here is the agenda for today's eng meeting. Feel free to add other agenda items:\nPrep for call with <PERSON> (mostly updating <PERSON><PERSON><PERSON> on the yesterday's discussion with <PERSON> and <PERSON><PERSON><PERSON><PERSON>)\nComp Builder Requirements Doc\nIntegrations", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Ruid+", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here", "style": {"bold": true}}, {"type": "text", "text": " Here is the agenda for today's eng meeting. Feel free to add other agenda items:\nPrep for call with <PERSON> (mostly updating <PERSON><PERSON><PERSON> on the yesterday's discussion with <PERSON> and <PERSON><PERSON><PERSON><PERSON>)\nComp Builder Requirements Doc\nIntegrations"}]}]}]}, {"ts": "1722441869.879179", "text": "is meeting started?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MQa6A", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "is meeting started?"}]}]}]}, {"ts": "1722441891.100599", "text": "I am waiting for <PERSON><PERSON><PERSON><PERSON> to join. We can meet on my zoom", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "W6mrP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am waiting for <PERSON><PERSON><PERSON><PERSON> to join. We can meet on my zoom"}]}]}]}, {"ts": "1722441901.509049", "text": "<https://us06web.zoom.us/j/**********?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R07EGTA93HD", "block_id": "H88hO", "api_decoration_available": false, "call": {"v1": {"id": "R07EGTA93HD", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1722441902, "active_participants": [], "all_participants": [], "display_id": "925-480-7019", "join_url": "https://us06web.zoom.us/j/**********?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1722534684, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/**********?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "2HwXU", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://us06web.zoom.us/j/**********?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1722442332.340439", "text": "<!here> folks i have some unplanned stuff to take care at home and i will be unavailable between 9am - 10am PT", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "giNGD", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " folks i have some unplanned stuff to take care at home and i will be unavailable between 9am - 10am PT"}]}]}]}, {"ts": "**********.090299", "text": "<@U0690EB5JE5> I tried up edit the employee data in the demo account and I am getting \"Failed to update error\"", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F07EXSF0Y7M", "created": **********, "timestamp": **********, "name": "Screenshot 2024-07-31 at 10.30.25 AM.png", "title": "Screenshot 2024-07-31 at 10.30.25 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 276813, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07EXSF0Y7M/screenshot_2024-07-31_at_10.30.25___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07EXSF0Y7M/download/screenshot_2024-07-31_at_10.30.25___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_360.png", "thumb_360_w": 354, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_480.png", "thumb_480_w": 472, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_720.png", "thumb_720_w": 709, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_800.png", "thumb_800_w": 800, "thumb_800_h": 813, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_960.png", "thumb_960_w": 945, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_1024.png", "thumb_1024_w": 1008, "thumb_1024_h": 1024, "original_w": 1860, "original_h": 1890, "thumb_tiny": "AwAwAC+3EiuDn+dPES91P50y2GEI96sAYoAjEKeh/OjyU9D+dSUmT3zQBG0SBSQOg9ar1bf7jfSqQUhCKALMHQ/Wpqgg+6frU9AAelNBPv8AlTjSD2GKAEf7jfSqlW3+430qoaAJoPun61PUEDAA5IH1qXev94fnQA49KQU15ML8u1j6E4qPzZP7if8AfdOwEr/cb6VUNTea5BDKoGDyGzUJoA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07EXSF0Y7M/screenshot_2024-07-31_at_10.30.25___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07EXSF0Y7M-56b4ae2ae5", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "WU++H", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I tried up edit the employee data in the demo account and I am getting \"Failed to update error\""}]}]}]}, {"ts": "**********.170989", "text": "There are some fixes done today. Let me push those changes to staging.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BfMC8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There are some fixes done today. Let me push those changes to staging."}]}]}]}, {"ts": "**********.751739", "text": "<@U04DS2MBWP4> Are you fine staging ENV being little slow or down for like 30mnts? I can deploy now or will do my morning.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.751739", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "QHIiw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Are you fine staging ENV being little slow or down for like 30mnts? I can deploy now or will do my morning."}]}]}]}, {"ts": "*********7.077989", "text": "That was definitely a bug; not seeing the whole column option box", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "*********7.077989", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "uC3fT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That was definitely a bug; not seeing the whole column option box"}]}]}]}]}