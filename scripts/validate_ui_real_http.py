#!/usr/bin/env python3
"""
Real HTTP test to validate the actual UI experience using requests library.
This simulates exactly what a browser would do.
"""

import requests
import re
import time
from bs4 import BeautifulSoup


def test_real_ui_flow():
    """Test the real UI flow using HTTP requests."""
    print("🌐 Testing Real UI Flow with HTTP Requests")
    print("=" * 60)
    
    # Create a session to maintain cookies
    session = requests.Session()
    base_url = "http://127.0.0.1:8000"
    
    try:
        # Step 1: Get login page and extract CSRF token
        print("🔐 Step 1: Getting login page...")
        login_page = session.get(f"{base_url}/accounts/login/")
        
        if login_page.status_code != 200:
            print(f"❌ Login page failed: {login_page.status_code}")
            return False
            
        # Extract CSRF token
        soup = BeautifulSoup(login_page.content, 'html.parser')
        csrf_token = soup.find('input', {'name': 'csrfmiddlewaretoken'})['value']
        print(f"✅ Login page loaded, CSRF token: {csrf_token[:20]}...")
        
        # Step 2: Login
        print("🔑 Step 2: Logging in...")
        login_data = {
            'username': '<EMAIL>',
            'password': 'admin123',
            'csrfmiddlewaretoken': csrf_token
        }
        
        login_response = session.post(f"{base_url}/accounts/login/", data=login_data)
        
        if login_response.status_code == 200 and 'login' not in login_response.url:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}, URL: {login_response.url}")
            return False
        
        # Step 3: Get search page
        print("🔍 Step 3: Getting search page...")
        search_page = session.get(f"{base_url}/search/")
        
        if search_page.status_code != 200:
            print(f"❌ Search page failed: {search_page.status_code}")
            return False
            
        # Extract CSRF token for search
        soup = BeautifulSoup(search_page.content, 'html.parser')
        csrf_token = soup.find('input', {'name': 'csrfmiddlewaretoken'})['value']
        print(f"✅ Search page loaded, CSRF token: {csrf_token[:20]}...")
        
        # Step 4: Submit search query
        print("🔍 Step 4: Submitting search query...")
        query = "list issues reported on Curana"
        
        search_data = {
            'query': query,
            'use_hybrid_search': 'true',
            'use_context_aware': 'true',  # This should use our enhanced prompt templates
            'use_query_expansion': 'false',
            'use_multi_step_reasoning': 'false',
            'csrfmiddlewaretoken': csrf_token
        }
        
        print(f"📝 Query: {query}")
        print(f"📋 Options: Context-Aware=True, Hybrid=True")
        
        start_time = time.time()
        search_response = session.post(f"{base_url}/search/query/", data=search_data)
        processing_time = time.time() - start_time
        
        print(f"⏱️  Processing time: {processing_time:.2f}s")
        print(f"📊 Response status: {search_response.status_code}")
        
        if search_response.status_code != 200:
            print(f"❌ Search failed: {search_response.status_code}")
            print(f"Response content preview: {search_response.text[:500]}")
            return False
        
        # Step 5: Analyze the actual HTML response
        print("📄 Step 5: Analyzing response content...")
        response_html = search_response.text
        soup = BeautifulSoup(response_html, 'html.parser')
        
        # Find the response content
        response_content = soup.find('div', class_='response-content')
        
        if not response_content:
            print("❌ Could not find response-content div")
            # Try alternative selectors
            response_content = soup.find('div', id='response-text')
            if not response_content:
                print("❌ Could not find response content at all")
                print("🔍 Available divs:")
                divs = soup.find_all('div', class_=True)[:5]
                for div in divs:
                    print(f"   - {div.get('class')}")
                return False
        
        response_text = response_content.get_text()
        response_html_inner = str(response_content)
        
        print(f"✅ Found response content")
        print(f"📊 Response analysis:")
        print(f"   Text length: {len(response_text)} characters")
        print(f"   HTML length: {len(response_html_inner)} characters")
        
        # Analyze response quality
        has_bullets = "•" in response_text or response_html_inner.count("<li>") > 3
        has_bold_dates = "<strong" in response_html_inner and ("February" in response_text or "March" in response_text)
        has_structure = "Issues reported by" in response_text or "Issues Found:" in response_text
        has_detailed_content = len(response_text) > 1000
        has_multiple_issues = response_text.count("•") > 5 or response_html_inner.count("<li>") > 5
        
        print(f"\n🎯 Quality Assessment:")
        print(f"   ✅ Has bullet points: {has_bullets}")
        print(f"   ✅ Has bold dates: {has_bold_dates}")
        print(f"   ✅ Has structure: {has_structure}")
        print(f"   ✅ Has detailed content (>1000 chars): {has_detailed_content}")
        print(f"   ✅ Has multiple issues: {has_multiple_issues}")
        
        # Calculate quality score
        quality_indicators = [has_bullets, has_bold_dates, has_structure, has_detailed_content, has_multiple_issues]
        quality_score = (sum(quality_indicators) / len(quality_indicators)) * 100
        
        print(f"\n📈 Overall Quality Score: {quality_score:.0f}%")
        
        # Show actual response preview
        print(f"\n📄 Actual UI Response Preview (first 800 chars):")
        print("=" * 60)
        print(response_text[:800])
        print("=" * 60)
        
        # Check for citations
        citations = soup.find_all('div', class_='professional-source-card')
        print(f"\n🔗 Citations found: {len(citations)}")
        
        # Overall assessment
        if quality_score >= 80:
            print("\n🎉 SUCCESS: UI is showing detailed, comprehensive responses!")
            return True
        elif quality_score >= 60:
            print("\n👍 PARTIAL SUCCESS: UI shows improved responses but could be better")
            return True
        else:
            print("\n❌ FAILURE: UI is still showing basic/poor quality responses")
            print("🔧 The backend improvements are not reaching the UI properly")
            return False
            
    except Exception as e:
        print(f"❌ Error during UI test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("🚀 Real UI HTTP Validation Test")
    print("Testing the actual user experience via HTTP requests")
    print()
    
    success = test_real_ui_flow()
    
    if success:
        print(f"\n✅ UI TEST PASSED!")
        print(f"🎯 Users are now seeing detailed, comprehensive responses")
        print(f"🎉 The fix is working in the actual UI!")
    else:
        print(f"\n❌ UI TEST FAILED!")
        print(f"🔧 The UI is not showing the expected detailed responses")
        print(f"💡 Further investigation needed...")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
