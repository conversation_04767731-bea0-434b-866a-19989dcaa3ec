{"thread_ts": "1737569929.826239", "channel_id": "C065QSSNH8A", "reply_count": 3, "replies": [{"ts": "1737570615.446619", "text": "I asked specifically about <PERSON> as an example and <PERSON> said her bonus has always been 30%.\n\nWe can just replace the bonus target % with the ones in this file, but I'm not sure where the 20% would have come from in the first place.\n\n<PERSON> is concerned about the timing (understandably) and just wants these employees updated (she doesn't want to have to recheck all the data again, so she doesnt' want another full refresh). So we can just replace them (but upload isn't working for me, so can you?). I'm just worried about where this came from in the first place.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737569929.826239", "blocks": [{"type": "rich_text", "block_id": "0wexq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I asked specifically about <PERSON> as an example and <PERSON> said her bonus has always been 30%.\n\nWe can just replace the bonus target % with the ones in this file, but I'm not sure where the 20% would have come from in the first place.\n\n<PERSON> is concerned about the timing (understandably) and just wants these employees updated (she doesn't want to have to recheck all the data again, so she doesnt' want another full refresh). So we can just replace them (but upload isn't working for me, so can you?). I'm just worried about where this came from in the first place."}]}]}]}, {"ts": "1737592616.608339", "text": "<@U07EJ2LP44S> we never mapped and synced bonus data from integration. This would have come from upload. I will update the ENV as per file you shared above.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737569929.826239", "blocks": [{"type": "rich_text", "block_id": "kXLES", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " we never mapped and synced bonus data from integration. This would have come from upload. I will update the ENV as per file you shared above."}]}]}]}, {"ts": "1737638157.522749", "text": "Bonus and salaries from above sheet updated", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737569929.826239", "blocks": [{"type": "rich_text", "block_id": "h7aZb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bonus and salaries from above sheet updated"}]}]}]}], "created_at": "2025-05-22T22:00:49.350702"}