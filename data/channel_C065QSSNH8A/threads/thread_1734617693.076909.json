{"thread_ts": "1734617693.076909", "channel_id": "C065QSSNH8A", "reply_count": 5, "replies": [{"ts": "1734617816.774909", "text": "<@U07MH77PUBV> let’s make this one as well sortable. ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734617693.076909", "reactions": [{"name": "+1", "users": ["U07MH77PUBV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "569FI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " "}, {"type": "text", "text": "let’s"}, {"type": "text", "text": " make this one as well sortable. "}]}]}]}, {"ts": "1734618211.248629", "text": "<@U07EJ2LP44S> I feel sorting is good enough.  We already have so many filters now adding more will be overkill unless there is no other way", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734617693.076909", "blocks": [{"type": "rich_text", "block_id": "QOzpt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I feel sorting is good enough.  We already have so many filters now adding more will be overkill unless there is no other way"}]}]}]}, {"ts": "1734618550.076669", "text": "We can try it with just sorting. I just know admin‘s use the filtering function heavily, more than sorting function.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734617693.076909", "blocks": [{"type": "rich_text", "block_id": "d/rJM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can try it with just sorting. I just know admin‘s use the filtering function heavily, more than sorting function."}]}]}]}, {"ts": "1734618566.709499", "text": "Also, the sorting function is not a great UI experience generally", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734617693.076909", "edited": {"user": "U07EJ2LP44S", "ts": "1734618581.000000"}, "blocks": [{"type": "rich_text", "block_id": "2oRC3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also, the sorting function is not a great "}, {"type": "text", "text": "U"}, {"type": "text", "text": "I experience generally"}]}]}]}, {"ts": "1734622754.756459", "text": "Do you mean sorting UX isn't good? or in general sorting itself isn't a good UX experience?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734617693.076909", "blocks": [{"type": "rich_text", "block_id": "QQtIe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you mean sorting UX isn't good? or in general sorting itself isn't a good UX experience?"}]}]}]}], "created_at": "2025-05-22T22:00:49.313709"}