{"date": "2023-12-19", "channel_id": "C065QSSNH8A", "message_count": 8, "messages": [{"ts": "1702994688.159459", "text": "<!here> <http://qa.compiify.com|qa.compiify.com> and <https://dev-app.compiify.com/> will be under maintenance for next 3 hours. Access to the environment will be disrupted. I will send an update when the environments becomes accessible", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1702994688.159459", "reply_count": 5, "edited": {"user": "U04DKEFP1K8", "ts": "1702994742.000000"}, "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "attachments": [{"from_url": "https://dev-app.compiify.com/", "service_icon": "https://dev-app.compiify.com/apple-touch-icon.png", "id": 1, "original_url": "https://dev-app.compiify.com/", "fallback": "Compiify", "text": "Web site created using create-react-app", "title": "Compiify", "title_link": "https://dev-app.compiify.com/", "service_name": "dev-app.compiify.com"}], "blocks": [{"type": "rich_text", "block_id": "qan30", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://qa.compiify.com", "text": "qa.compiify.com"}, {"type": "text", "text": " and "}, {"type": "link", "url": "https://dev-app.compiify.com/"}, {"type": "text", "text": " will be under maintenance for next 3 hours. Access to the environment will be disrupted. I will send an update when the environments becomes accessible"}]}]}]}, {"ts": "1702951078.017439", "text": "Update on SLAs\n\nAfter going back and forth with legal, we decided to now include SLA since are not including calculations and exceptions of availability and the credit based on availability among other terms. Given the stage of the company, we will just use the following language:\n\n*Support:* If Customer experiences any errors, bugs, or other issues in its use of the Services, Compiify will use commercially reasonable efforts to respond as soon as possible (“Support”) in order to resolve the issue or provide a suitable workaround. The fee for Support is included in the cost of the Subscription set forth on the Order Form. Customer will send any support requests to Compiify via email (to: <mailto:<EMAIL>|<EMAIL>>).", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}, {"name": "+1", "users": ["U065H3M6WJV", "U04DKEFP1K8"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "HMbFx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Update on SLAs\n\nAfter going back and forth with legal, we decided to now include SLA since are not including calculations and exceptions of availability and the credit based on availability among other terms. Given the stage of the company, we will just use the following language:\n\n"}, {"type": "text", "text": "Support:", "style": {"bold": true}}, {"type": "text", "text": " If Customer experiences any errors, bugs, or other issues in its use of the Services, Compiify will use commercially reasonable efforts to respond as soon as possible (“Support”) in order to resolve the issue or provide a suitable workaround. The fee for Support is included in the cost of the Subscription set forth on the Order Form. Customer will send any support requests to Compiify via email (to: "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ")."}]}]}]}, {"ts": "1702942582.217649", "text": "<PERSON><PERSON>, had a good call with <PERSON> this afternoon. I had him take a quick look at the <https://docs.google.com/document/d/12Uy1Kwh5NLLbGr3tnl30FZSE4SM_c04bsOazifc6F0c/edit|blog post ideas> and captured his reactions, then had him walk through the current QA environment and give some quick feedback on the HR Admin view and Merit view. Some <https://docs.google.com/document/d/1AisVWa1O8S5e6SjiWwAKecpPEKtm9F2SE0PVZf7lnWM/edit|notes here>.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702942582.217649", "reply_count": 1, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06AKSUDLBT", "created": 1702574493, "timestamp": 1702574493, "name": "Possible blog topics", "title": "Possible blog topics", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "12Uy1Kwh5NLLbGr3tnl30FZSE4SM_c04bsOazifc6F0c", "external_url": "https://docs.google.com/document/d/12Uy1Kwh5NLLbGr3tnl30FZSE4SM_c04bsOazifc6F0c/edit", "url_private": "https://docs.google.com/document/d/12Uy1Kwh5NLLbGr3tnl30FZSE4SM_c04bsOazifc6F0c/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACXTJxSZpSM9ajxjgD9KAJMj1oyKZ+f5UYPv+VAD6KKKAA9KjOKkqPjPegAwPWnDA/ipPl96UY7E0AOooooAKjJ/zipKTb70AMpQee1Ox7mlFABRRRQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06AKSUDLBT/possible_blog_topics", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}, {"id": "F06AT498FEG", "created": 1702942584, "timestamp": 1702942584, "name": "<PERSON> (Novo Insights)", "title": "<PERSON> (Novo Insights)", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1AisVWa1O8S5e6SjiWwAKecpPEKtm9F2SE0PVZf7lnWM", "external_url": "https://docs.google.com/document/d/1AisVWa1O8S5e6SjiWwAKecpPEKtm9F2SE0PVZf7lnWM/edit", "url_private": "https://docs.google.com/document/d/1AisVWa1O8S5e6SjiWwAKecpPEKtm9F2SE0PVZf7lnWM/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACXSJxRn6fnQaTaPQ0ALmlpAoFLgelABRRRQAhpAxpxqPHPNAD8mjJpuB6/rS4Ht+dADqKKKACmHk0+igBgX60uPanUUAFFFFAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06AT498FEG/paul_reiman__novo_insights_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "43l7N", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "BTW, had a good call with <PERSON> this afternoon. I had him take a quick look at the "}, {"type": "link", "url": "https://docs.google.com/document/d/12Uy1Kwh5NLLbGr3tnl30FZSE4SM_c04bsOazifc6F0c/edit", "text": "blog post ideas"}, {"type": "text", "text": " and captured his reactions, then had him walk through the current QA environment and give some quick feedback on the HR Admin view and Merit view. Some "}, {"type": "link", "url": "https://docs.google.com/document/d/1AisVWa1O8S5e6SjiWwAKecpPEKtm9F2SE0PVZf7lnWM/edit", "text": "notes here"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1702940448.656509", "text": "ok", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1702940272.508179", "text": "Also happy to get feedback on Total Rewards mocks (spreadsheet), but that's lower priority and I can do that with <PERSON><PERSON> separately if needed", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wX7ob", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also happy to get feedback on Total Rewards mocks (spreadsheet), but that's lower priority and I can do that with <PERSON><PERSON> separately if needed"}]}]}]}, {"ts": "1702940241.990929", "text": "ok", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1702940135.069349", "text": "<@U04DS2MBWP4> main questions are about how they want to approach recommendations and prefill", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pNnYu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " main questions are about how they want to approach recommendations and prefill"}]}]}]}, {"ts": "1702940080.709829", "text": "<@U065H3M6WJV> I am meeting with <PERSON> in few minutes. Do you have any questions for SDF that you need me to ask her since I am on the call anyway?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "uXU8s", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I am meeting with <PERSON> in few minutes. Do you have any questions for SDF that you need me to ask her since I am on the call anyway?"}]}]}]}], "created_at": "2025-05-22T21:35:34.561592"}