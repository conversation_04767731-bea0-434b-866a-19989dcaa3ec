"""
GitHub Wiki interface for document source.
"""

import base64
import hashlib
import json
import logging
import re
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlparse

from django.utils import timezone
from github import Github, GithubException, RateLimitExceededException
from github.ContentFile import ContentFile
from github.Repository import Repository

from .base import DocumentSourceInterface
from .github import RateLimitHandler

# Set up logging
logger = logging.getLogger(__name__)


class GitHubWikiSourceInterface(DocumentSourceInterface):
    """
    Interface for GitHub Wiki document source.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the GitHub Wiki source interface.

        Args:
            config: Configuration for the GitHub Wiki source
        """
        super().__init__(config)
        self.token = config.get("token")
        self.owner = config.get("owner")
        self.repo = config.get("repo")
        self.client = None
        self.rate_limit_handler = RateLimitHandler()
        self.initialize_client()

    def initialize_client(self) -> None:
        """
        Initialize the GitHub client.
        """
        if not self.token:
            raise ValueError("GitHub token is required")

        try:
            self.client = Github(self.token, per_page=100)
        except Exception as e:
            raise ValueError(f"Failed to initialize GitHub client: {str(e)}")

    def validate_config(self) -> bool:
        """
        Validate the configuration.

        Returns:
            bool: True if the configuration is valid, False otherwise
        """
        required_fields = ["token", "owner", "repo"]
        for field in required_fields:
            if not self.config.get(field):
                return False

        return True

    def get_source_type(self) -> str:
        """
        Get the type of the source.

        Returns:
            str: Type of the source
        """
        return "github_wiki"

    def fetch_documents(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch documents from GitHub Wiki.

        Args:
            **kwargs: Additional arguments for fetching documents
                - days: Number of days to fetch (default: 30)
                - max_pages: Maximum number of wiki pages to fetch (default: 100)

        Returns:
            List[Dict[str, Any]]: List of documents
        """
        if not self.client:
            self.initialize_client()

        # Parse arguments
        days = kwargs.get("days", 30)
        max_pages = kwargs.get("max_pages", 100)

        documents = []

        try:
            # Get repository
            repo = self.client.get_repo(f"{self.owner}/{self.repo}")

            # Get wiki repository
            wiki_repo_name = f"{self.owner}/{self.repo}.wiki"
            try:
                wiki_repo = self.client.get_repo(wiki_repo_name)
            except GithubException:
                logger.warning(
                    f"Wiki repository {wiki_repo_name} not found or not accessible"
                )
                return documents

            # Get wiki pages (contents of the wiki repository)
            wiki_contents = wiki_repo.get_contents("")

            # Process wiki pages
            page_count = 0
            for content in wiki_contents:
                if page_count >= max_pages:
                    break

                # Skip non-markdown files
                if not content.name.endswith(".md"):
                    continue

                try:
                    # Get file content
                    file_content = base64.b64decode(content.content).decode("utf-8")

                    # Get last update date
                    last_commit = wiki_repo.get_commits(path=content.path)[0]
                    last_updated = last_commit.commit.author.date

                    # Skip if older than specified days
                    if last_updated < datetime.now(timezone.utc) - timedelta(days=days):
                        continue

                    # Create document
                    document = self._process_wiki_page(
                        content, file_content, last_commit
                    )
                    documents.append(document)

                    page_count += 1
                except Exception as e:
                    logger.error(f"Error processing wiki page {content.name}: {str(e)}")

            return documents

        except RateLimitExceededException:
            # Handle rate limit
            if self.rate_limit_handler.handle_rate_limit(self.client):
                # Retry after waiting
                return self.fetch_documents(**kwargs)
            else:
                raise ValueError(
                    "GitHub API rate limit exceeded and could not be handled"
                )

        except GithubException as e:
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            raise ValueError(f"Error fetching documents from GitHub Wiki: {str(e)}")

    def get_document(self, document_id: str, **kwargs) -> Dict[str, Any]:
        """
        Get a specific document from GitHub Wiki.

        Args:
            document_id: ID of the document to get
            **kwargs: Additional arguments for getting the document

        Returns:
            Dict[str, Any]: Document
        """
        if not self.client:
            self.initialize_client()

        try:
            # Get repository
            wiki_repo_name = f"{self.owner}/{self.repo}.wiki"
            wiki_repo = self.client.get_repo(wiki_repo_name)

            # Get wiki page
            content = wiki_repo.get_contents(document_id)

            # Get file content
            file_content = base64.b64decode(content.content).decode("utf-8")

            # Get last update date
            last_commit = wiki_repo.get_commits(path=content.path)[0]

            # Create document
            document = self._process_wiki_page(content, file_content, last_commit)

            return document

        except RateLimitExceededException:
            # Handle rate limit
            if self.rate_limit_handler.handle_rate_limit(self.client):
                # Retry after waiting
                return self.get_document(document_id, **kwargs)
            else:
                raise ValueError(
                    "GitHub API rate limit exceeded and could not be handled"
                )

        except GithubException as e:
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            raise ValueError(f"Error getting document from GitHub Wiki: {str(e)}")

    def search_documents(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for documents in GitHub Wiki.

        Args:
            query: Query to search for
            **kwargs: Additional arguments for searching documents
                - max_results: Maximum number of results to return (default: 10)

        Returns:
            List[Dict[str, Any]]: List of documents
        """
        if not self.client:
            self.initialize_client()

        # Parse arguments
        max_results = kwargs.get("max_results", 10)

        documents = []

        try:
            # Get repository
            wiki_repo_name = f"{self.owner}/{self.repo}.wiki"
            wiki_repo = self.client.get_repo(wiki_repo_name)

            # Get all wiki pages
            wiki_contents = wiki_repo.get_contents("")

            # Search in wiki pages
            for content in wiki_contents:
                if len(documents) >= max_results:
                    break

                # Skip non-markdown files
                if not content.name.endswith(".md"):
                    continue

                try:
                    # Get file content
                    file_content = base64.b64decode(content.content).decode("utf-8")

                    # Check if query is in content
                    if (
                        query.lower() in file_content.lower()
                        or query.lower() in content.name.lower()
                    ):
                        # Get last update date
                        last_commit = wiki_repo.get_commits(path=content.path)[0]

                        # Create document
                        document = self._process_wiki_page(
                            content, file_content, last_commit
                        )
                        documents.append(document)
                except Exception as e:
                    logger.error(f"Error searching wiki page {content.name}: {str(e)}")

            return documents

        except RateLimitExceededException:
            # Handle rate limit
            if self.rate_limit_handler.handle_rate_limit(self.client):
                # Retry after waiting
                return self.search_documents(query, **kwargs)
            else:
                raise ValueError(
                    "GitHub API rate limit exceeded and could not be handled"
                )

        except GithubException as e:
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            raise ValueError(f"Error searching documents in GitHub Wiki: {str(e)}")

    def _process_wiki_page(
        self, content: ContentFile, file_content: str, last_commit
    ) -> Dict[str, Any]:
        """
        Process a wiki page into a document.

        Args:
            content: GitHub content file
            file_content: Content of the file
            last_commit: Last commit for the file

        Returns:
            Dict[str, Any]: Processed document
        """
        # Extract title from filename
        page_name = content.name.replace(".md", "")
        title = page_name.replace("-", " ").replace("_", " ")

        # Try to extract a better title from the content (first heading)
        heading_match = re.search(r"^#\s+(.+)$", file_content, re.MULTILINE)
        if heading_match:
            title = heading_match.group(1)

        # Create document ID
        document_id = f"wiki/{content.path}"

        # Create document
        document = {
            "id": document_id,
            "external_id": content.path,
            "title": title,
            "content": file_content,
            "content_type": "markdown",
            "metadata": {
                "source_type": "github_wiki",
                "repo": f"{self.owner}/{self.repo}",
                "path": content.path,
                "sha": content.sha,
                "size": content.size,
                "last_updated": last_commit.commit.author.date.isoformat(),
                "last_updated_by": {
                    "name": last_commit.commit.author.name,
                    "email": last_commit.commit.author.email,
                },
                "commit_message": last_commit.commit.message,
                "html_url": f"https://github.com/{self.owner}/{self.repo}/wiki/{page_name}",
            },
            "created_at": last_commit.commit.author.date,
            "updated_at": last_commit.commit.author.date,
        }

        return document
