{"date": "2025-02-18", "channel_id": "C065QSSNH8A", "message_count": 17, "messages": [{"ts": "1739899666.845589", "text": "Sure that’s fine", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8KkO3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure "}, {"type": "text", "text": "that’s"}, {"type": "text", "text": " fine"}]}]}]}, {"ts": "1739898897.554389", "text": "<@U07EJ2LP44S> how do you feel about canceling the leadership calls and make them as-needed going forward. It looks like most of the issues can be handled asyc between you and <PERSON><PERSON><PERSON>.\n<PERSON><PERSON><PERSON> and I can use this time to meet start making progress on the other ideas.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YXEGu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " how do you feel about canceling the leadership calls and make them as-needed going forward. It looks like most of the issues can be handled asyc between you and <PERSON><PERSON><PERSON>.\n<PERSON><PERSON><PERSON> and I can use this time to meet start making progress on the other ideas."}]}]}]}, {"ts": "1739898701.301759", "text": "<@U07EJ2LP44S> I have the fixe ready. I am testing with latest diversified db in local. Will keep you posted.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739893569.641669", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "A36/d", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have the fixe ready. I am testing with latest diversified db in local. Will keep you posted."}]}]}]}, {"ts": "1739896033.103619", "text": "Getting errors. Can you upload this into stridedemo. Tomorrow is fine. One has all the data and the one with a 2 has only the rating column. I can't get either to upload. (I will need to do the same for T<PERSON><PERSON> demo in test.)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739896033.103619", "reply_count": 2, "edited": {"user": "U07EJ2LP44S", "ts": "1739896056.000000"}, "files": [{"id": "F08DSDVH9EZ", "created": 1739896008, "timestamp": 1739896008, "name": "CuranaDemoRatings.csv", "title": "CuranaDemoRatings.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 49297, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DSDVH9EZ/curanademoratings.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DSDVH9EZ/download/curanademoratings.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DSDVH9EZ/curanademoratings.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DSDVH9EZ-c69385d620", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DSDVH9EZ/curanademoratings.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">37</div><div class=\"cm-col\">Khoa</div><div class=\"cm-col\">Pham</div><div class=\"cm-col\">Khoa Pham</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">9/28/20</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">42</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">9/28/20</div><div class=\"cm-col\">9/28/20</div><div class=\"cm-col\">Vietnamese</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Partner Engineering Manager</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">Manager</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col\">Needs Improvement</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">133326.24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">13071</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">130712</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Raj</div><div class=\"cm-col\">Patel</div><div class=\"cm-col\">Raj Patel</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">4/6/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">206</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">4/6/21</div><div class=\"cm-col\">4/6/21</div><div class=\"cm-col\">Indian</div><div class=\"cm-col\">San Francisco</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\">Platform</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">138409.95</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">13182</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">131819</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\">Juan</div><div class=\"cm-col\">Rodriguez</div><div class=\"cm-col\">Juan Rodriguez</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">5/24/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">194</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">5/24/21</div><div class=\"cm-col\">5/24/21</div><div class=\"cm-col\">Hispanic</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col\">Needs Improvement</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">155710</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">14571</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">155710</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30</div><div class=\"cm-col\">Hans</div><div class=\"cm-col\">Wagner</div><div class=\"cm-col\">Hans Wagner</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">9/6/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">206</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">9/6/22</div><div class=\"cm-col\">9/6/22</div><div class=\"cm-col\">German</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\">Platform</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">205837.32</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">13195</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">131947</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">126</div><div class=\"cm-col\">Elena</div><div class=\"cm-col\">Lopez</div><div class=\"cm-col\">Elena Lopez</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">11/1/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">12</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">11/1/21</div><div class=\"cm-col\">11/1/21</div><div class=\"cm-col\">Hispanic</div><div class=\"cm-col\">Remote-Payzone3</div><div class=\"cm-col\">Sr. Engineering Manager</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">Manager</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">153991.9</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">13391</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">133906</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 138, "lines_more": 137, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F08DSDVMPFF", "created": 1739896009, "timestamp": 1739896009, "name": "CuranaDemoRatings2.csv", "title": "CuranaDemoRatings2.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 3751, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DSDVMPFF/curanademoratings2.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DSDVMPFF/download/curanademoratings2.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DSDVMPFF/curanademoratings2.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DSDVMPFF-11bb469b0d", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DSDVMPFF/curanademoratings2.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,Performance Rating,Performance Comment\r\nU,37,Needs Improvement,\r\nU,6,Exceeds Expectations,\r\nU,10,Needs Improvement,\r\nU,30,Exceeds Expectations,\r\nU,126,Meets Expectations,\r\nU,168,Meets Expectations,\r\nU,231,Exceeds Expectations,\r\nU,206,Meets Expectations,\r\nU,19,Exceeds Expectations,\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">37</div><div class=\"cm-col\">Needs Improvement</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\">Needs Improvement</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30</div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">126</div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">168</div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">231</div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">206</div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">19</div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 138, "lines_more": 128, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "owYsJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Getting errors. Can you upload this into stridedemo. Tomorrow is fine. One has all the data and the one with a 2 has only the rating column. I can't get either to upload. (I will need to do the same for T<PERSON><PERSON> demo in test.)"}]}]}]}, {"ts": "1739895575.567639", "text": "Is it because we only upload the performance rating number and not the name? or it is a bug?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1739895575.567639", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "tYiS5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is it because we only upload the performance rating number and not the name? or it is a bug?"}]}]}]}, {"ts": "1739895514.577149", "text": "Thats good; <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and test. are all showing the number vs the name", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "O0hqV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thats good; <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and test. are all showing the number vs the name"}]}]}]}, {"ts": "1739895290.514149", "text": "<@U07EJ2LP44S> I just checked valgenesis. It is showing performance rating name", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/Mo8+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I just checked valgenesis. It is showing performance rating name"}]}]}]}, {"ts": "**********.180759", "text": "<@U0690EB5JE5> I am not able to log into <http://demo.stridehr.io|demo.stridehr.io>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.180759", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "AMvOv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am not able to log into "}, {"type": "link", "url": "http://demo.stridehr.io", "text": "demo.stridehr.io"}]}]}]}, {"ts": "**********.655379", "text": "In it seems like all the accounts, the comp view is showing the rating number not the name. I double checked how it was set up in settings and it's right. Do we need to change something on the back end to have it show the name vs the number?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.655379", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "HtJIG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In it seems like all the accounts, the comp view is showing the rating number not the name. I double checked how it was set up in settings and it's right. Do we need to change something on the back end to have it show the name vs the number?"}]}]}]}, {"ts": "**********.412569", "text": "Yes that's fine. I have another one", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Ca3Yt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes that's fine. I have another one"}]}]}]}, {"ts": "**********.720909", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "**********.548189", "text": "<@U07M6QKHUC9>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lAMKj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}]}]}]}, {"ts": "1739894455.961349", "text": "I think I need the time to fix the issue.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "REdVX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think I need the time to fix the issue."}]}]}]}, {"ts": "1739894430.968549", "text": "<@U0690EB5JE5> <@U07EJ2LP44S> should we use the meeting time to let <PERSON><PERSON><PERSON> fix the bugs? I am fine either way", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XPO2N", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " should we use the meeting time to let <PERSON><PERSON><PERSON> fix the bugs? I am fine either way"}]}]}]}, {"ts": "1739893569.641669", "text": "<@U07EJ2LP44S> I am looking into the issue. Need an hour to root cause. will keep you update every hour or whenever I have an update.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739893569.641669", "reply_count": 6, "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "rGERK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am looking into the issue. Need an hour to root cause. will keep you update every hour or whenever I have an update."}]}]}]}, {"ts": "1739891738.743249", "text": "<@U0690EB5JE5> Also they need another employee changed, and it's not adding up correctly: <PERSON> bonus I changed his total percentage to 15 percent yesterday but the individual performance bucket is not accurate.  His total should be 13779.05", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739891738.743249", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "ZYsZR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Also they need another employee changed, and it's not adding up correctly: <PERSON> bonus I changed his total percentage to 15 percent yesterday but the individual performance bucket is not accurate.  His total should be 13779.05"}]}]}]}, {"ts": "**********.151789", "text": "<@U0690EB5JE5> Flags have disappeared overnight in <PERSON><PERSON><PERSON>'s account. Over 40 people who were flagged are not any longer. <PERSON> is freaking out. I sent her email to you that has the flags from yesterday. Can you address asap?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.151789", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "3GNOi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Flags have disappeared overnight in <PERSON><PERSON><PERSON>'s account. Over 40 people who were flagged are not any longer. <PERSON> is freaking out. I sent her email to you that has the flags from yesterday. Can you address asap?"}]}]}]}], "created_at": "2025-05-22T21:35:34.712474"}