#!/usr/bin/env python
import os, sys, django
sys.path.append('.'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local"); django.setup()

from apps.documents.services.ingestion_service import IngestionService
from apps.accounts.models import Tenant

# Simple ingestion with all data
tenant, _ = Tenant.objects.get_or_create(slug="default", defaults={"name": "Default"})
service = IngestionService(tenant=tenant)

# Create source with 730 days configuration
source_config = {
    "data_dir": "../data/", 
    "channel": "C065QSSNH8A", 
    "custom_days": 730,
    "time_period": "custom"
}

source = service.create_source("Local Slack 730 Days", "local_slack", source_config)
processed, failed = service.process_source(source, batch_size=25, days_back=730)
print(f"✅ Processed: {processed}, Failed: {failed}")
