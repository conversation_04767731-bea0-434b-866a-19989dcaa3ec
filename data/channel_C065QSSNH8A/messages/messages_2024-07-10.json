{"date": "2024-07-10", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "**********.074699", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> can we make sure we also push the latest updates to <http://test.stridehr.io|test.stridehr.io> account. I just want to make sure <PERSON><PERSON> is seeing the latest version as I am working with her on further enhancements to product", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.074699", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "9jFd7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can we make sure we also push the latest updates to "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": " account. I just want to make sure <PERSON><PERSON> is seeing the latest version as I am working with her on further enhancements to product"}]}]}]}, {"ts": "**********.789109", "text": "Yes and no button will only have gray color", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ykb+d", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes and no button will only have gray color"}]}]}]}, {"ts": "**********.435389", "text": "<@U0690EB5JE5> I spoke with the designer. No change in color for toggles unless it's on and off", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zKGoo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I spoke with the designer. No change in color for toggles unless it's on and off"}]}]}]}, {"ts": "**********.764509", "text": "<@U04DKEFP1K8> can you set up another eng sync for tomorrow so we can go over the progress and issues from QA testing of Merit planning 2.0+Reporting", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.764509", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Yst4e", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you set up another eng sync for tomorrow so we can go over the progress and issues from QA testing of Merit planning 2.0+Reporting"}]}]}]}], "created_at": "2025-05-22T21:35:34.620844"}