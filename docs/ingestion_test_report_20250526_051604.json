{"test_summary": {"start_time": "2025-05-26 05:06:12.346218", "end_time": "2025-05-26 05:06:15.817692", "total_duration": "0:00:03.471474", "documents_processed": 12, "documents_failed": 0}, "data_quality": {"total_documents": 12, "total_chunks": 186, "total_embeddings": 186, "documents_with_content": 12, "chunks_with_embeddings": 186, "documents_with_permalinks": 12, "quality_issues": []}, "data_integrity": {"postgresql_chunks": 186, "qdrant_vectors": 0, "matched_vectors": 186, "orphaned_chunks": [], "orphaned_vectors": [], "integrity_issues": []}, "search_functionality": {"test_queries": [{"query": "budget adherence testing", "status": "success", "answer_length": 38, "documents_count": 0, "processing_time": 93.39669299125671}, {"query": "bug reports and issues", "status": "success", "answer_length": 36, "documents_count": 0, "processing_time": 123.2130331993103}, {"query": "manager recommendations", "status": "success", "answer_length": 37, "documents_count": 0, "processing_time": 146.78248405456543}, {"query": "testing updates", "status": "success", "answer_length": 29, "documents_count": 0, "processing_time": 180.98159313201904}, {"query": "showstopper bugs", "status": "success", "answer_length": 30, "documents_count": 0, "processing_time": 9.204533100128174}], "successful_searches": 5, "failed_searches": 0, "search_issues": [], "performance_metrics": {"total_time": 553.5783364772797, "average_time": 110.71566729545593, "min_time": 9.204533100128174, "max_time": 180.98159313201904, "detailed_timings": [{"query": "budget adherence testing", "time_seconds": 93.39669299125671, "time_formatted": "93.40s"}, {"query": "bug reports and issues", "time_seconds": 123.2130331993103, "time_formatted": "123.21s"}, {"query": "manager recommendations", "time_seconds": 146.78248405456543, "time_formatted": "146.78s"}, {"query": "testing updates", "time_seconds": 180.98159313201904, "time_formatted": "180.98s"}, {"query": "showstopper bugs", "time_seconds": 9.204533100128174, "time_formatted": "9.20s"}]}}, "api_test_functionality": {"api_tests": [{"scenario": "Basic Search", "query": "budget adherence testing", "status": "failed", "error": "HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "response_time": 0.007332801818847656}, {"scenario": "Query Expansion Test", "query": "bug reports and issues", "status": "failed", "error": "HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "response_time": 0.004828691482543945}, {"scenario": "Multi-Step Reasoning Test", "query": "What are the main engineering challenges discussed in recent meetings?", "status": "failed", "error": "HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "response_time": 0.004563808441162109}, {"scenario": "Full RAG Features Test", "query": "Summarize customer feedback and manager recommendations from the last quarter", "status": "failed", "error": "HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "response_time": 0.004712820053100586}, {"scenario": "Low Relevance Threshold Test", "query": "testing updates and showstopper bugs", "status": "failed", "error": "HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "response_time": 0.004650115966796875}], "successful_api_calls": 0, "failed_api_calls": 5, "api_issues": ["Scenario 'Basic Search': HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "Scenario 'Query Expansion Test': HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "Scenario 'Multi-Step Reasoning Test': HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "Scenario 'Full RAG Features Test': HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}", "Scenario 'Low Relevance Threshold Test': HTTP 401: {\"detail\":\"Authentication credentials were not provided.\"}"], "rag_techniques_tested": {"hybrid_search": false, "query_expansion": false, "multi_step_reasoning": false, "context_aware": false, "citation_engine": false, "router_engine": false}, "performance_metrics": {"total_time": 0.026088237762451172, "average_time": 0.005217647552490235, "min_time": 0.004563808441162109, "max_time": 0.007332801818847656, "detailed_timings": [{"scenario": "Basic Search", "query": "budget adherence testing", "time_seconds": 0.007332801818847656, "time_formatted": "0.01s"}, {"scenario": "Query Expansion Test", "query": "bug reports and issues", "time_seconds": 0.004828691482543945, "time_formatted": "0.00s"}, {"scenario": "Multi-Step Reasoning Test", "query": "What are the main engineering challenges discussed in recent meetings?", "time_seconds": 0.004563808441162109, "time_formatted": "0.00s"}, {"scenario": "Full RAG Features Test", "query": "Summarize customer feedback and manager recommendations from the last quarter", "time_seconds": 0.004712820053100586, "time_formatted": "0.00s"}, {"scenario": "Low Relevance Threshold Test", "query": "testing updates and showstopper bugs", "time_seconds": 0.004650115966796875, "time_formatted": "0.00s"}]}}, "overall_status": "FAIL"}