#!/usr/bin/env python3
"""
Debug script to investigate the 3 search issues:
1. "whats latest on curana?" yields no results
2. HuggingFace is initialized every query
3. Citations have issues
"""

import os, sys, django
sys.path.append('./multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

import time
from apps.search.services.rag_service import RAGService
from apps.accounts.models import Tenant
from django.contrib.auth.models import User
from apps.documents.models import DocumentChunk

def debug_issue_1_search_results():
    """Debug Issue 1: Search query yields no results."""
    print("🔍 Issue 1: Debugging 'whats latest on curana?' search...")

    # Get test user and tenant
    user = User.objects.get(username='testuser')
    tenant = Tenant.objects.get(slug='stride')

    print(f"   User: {user.username}")
    print(f"   Tenant: {tenant.name}")

    # Check curana data availability
    curana_chunks = DocumentChunk.objects.filter(
        document__source__tenant=tenant,
        text__icontains='curana'
    )
    print(f"   📊 Available curana chunks: {curana_chunks.count()}")

    if curana_chunks.exists():
        sample = curana_chunks.first()
        print(f"   Sample chunk: {sample.text[:150]}...")

    # Test the search
    try:
        print("\n   🔎 Testing search query...")
        start_time = time.time()

        rag_service = RAGService(user=user, tenant_slug=tenant.slug)
        search_result, retrieved_docs = rag_service.search(
            query_text="whats latest on curana?",
            top_k=10,
            min_relevance_score=0.1,  # Very low threshold
            use_hybrid_search=True,
            use_context_aware=True,
            use_query_expansion=False,
            use_multi_step_reasoning=False
        )

        end_time = time.time()
        search_time = end_time - start_time

        print(f"   ⏱️  Search completed in {search_time:.2f}s")
        print(f"   📊 Retrieved docs: {len(retrieved_docs) if retrieved_docs else 0}")
        print(f"   📊 Citations: {search_result.citations.count() if search_result else 0}")

        if search_result:
            print(f"   📝 Generated answer length: {len(search_result.generated_answer)}")
            print(f"   📝 Answer preview: {search_result.generated_answer[:200]}...")
        else:
            print("   ❌ No search result generated")

        return search_result, retrieved_docs, search_time

    except Exception as e:
        print(f"   ❌ Search failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None, 0

def debug_issue_2_embedding_initialization():
    """Debug Issue 2: HuggingFace initialization on every query."""
    print("\n🔧 Issue 2: Debugging HuggingFace initialization...")

    user = User.objects.get(username='testuser')
    tenant = Tenant.objects.get(slug='stride')

    # Test multiple searches to see initialization pattern
    queries = [
        "test query 1",
        "test query 2",
        "test query 3"
    ]

    for i, query in enumerate(queries, 1):
        print(f"\n   🔎 Search {i}: '{query}'")
        start_time = time.time()

        try:
            rag_service = RAGService(user=user, tenant_slug=tenant.slug)
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=5,
                min_relevance_score=0.3
            )

            end_time = time.time()
            search_time = end_time - start_time
            print(f"   ⏱️  Search {i} time: {search_time:.2f}s")

        except Exception as e:
            print(f"   ❌ Search {i} failed: {e}")

def debug_issue_3_citations():
    """Debug Issue 3: Citations issues."""
    print("\n📚 Issue 3: Debugging citations...")

    user = User.objects.get(username='testuser')
    tenant = Tenant.objects.get(slug='stride')

    try:
        rag_service = RAGService(user=user, tenant_slug=tenant.slug)
        search_result, retrieved_docs = rag_service.search(
            query_text="engineering team discussions",
            top_k=10,
            min_relevance_score=0.2
        )

        if search_result:
            citations = search_result.citations.all()
            print(f"   📊 Total citations: {citations.count()}")

            for i, citation in enumerate(citations[:5], 1):
                print(f"\n   📖 Citation {i}:")
                print(f"      Rank: {citation.rank}")
                print(f"      Relevance: {citation.relevance_score}")

                if citation.document_chunk:
                    chunk = citation.document_chunk
                    print(f"      Chunk ID: {chunk.id}")
                    print(f"      Text preview: {chunk.text[:100]}...")

                    if chunk.document:
                        doc = chunk.document
                        print(f"      Document: {doc.title}")
                        print(f"      Source: {doc.source.name if doc.source else 'No source'}")
                        print(f"      Permalink: {doc.permalink or 'No permalink'}")
                    else:
                        print("      ❌ No document linked to chunk")
                else:
                    print("      ❌ No document chunk linked to citation")
        else:
            print("   ❌ No search result to analyze citations")

    except Exception as e:
        print(f"   ❌ Citations debug failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main debug function."""
    print("🐛 RAG Search Issues Debug Script")
    print("=" * 50)

    # Issue 1: Search results
    search_result, retrieved_docs, search_time = debug_issue_1_search_results()

    # Issue 2: Embedding initialization
    debug_issue_2_embedding_initialization()

    # Issue 3: Citations
    debug_issue_3_citations()

    print("\n" + "=" * 50)
    print("🏁 Debug completed")

if __name__ == "__main__":
    main()
