#!/usr/bin/env python
"""
Quick Embedding Fix Script

This script provides a quick way to test and fix embedding consistency issues
without waiting for model downloads or dealing with network issues.

Usage:
    python scripts/quick_embedding_fix.py
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'multi_source_rag.settings')
django.setup()

def test_embedding_fix():
    """Test the embedding consistency fix with better error handling."""
    
    print("🔧 Quick Embedding Consistency Fix Test")
    print("=" * 50)
    
    try:
        print("\n1️⃣ Testing import...")
        from apps.core.utils.embedding_consistency import (
            get_embedding_model_info,
            EmbeddingModelConfig,
            reset_embedding_model
        )
        print("   ✅ Import successful")
        
        print("\n2️⃣ Getting model configuration...")
        model_name, dimensions = EmbeddingModelConfig.get_model_config()
        print(f"   ✅ Model: {model_name}")
        print(f"   ✅ Dimensions: {dimensions}")
        
        print("\n3️⃣ Checking Gemini preference...")
        is_gemini_preferred = EmbeddingModelConfig.is_gemini_preferred()
        print(f"   ✅ Gemini preferred: {is_gemini_preferred}")
        
        if is_gemini_preferred:
            gemini_key = os.environ.get("GEMINI_API_KEY", "")
            if gemini_key:
                print(f"   ✅ Gemini API key found: {gemini_key[:10]}...")
            else:
                print("   ⚠️  Gemini preferred but no API key found")
        
        print("\n4️⃣ Testing model info (without initialization)...")
        # Reset first to avoid cached state
        reset_embedding_model()
        
        # This should work without initializing the actual model
        try:
            model_info = get_embedding_model_info()
            if "error" in model_info:
                print(f"   ⚠️  Model info error: {model_info['error']}")
                print("   💡 This is expected if model hasn't been initialized yet")
            else:
                print(f"   ✅ Model info retrieved successfully")
                for key, value in model_info.items():
                    print(f"      {key}: {value}")
        except Exception as e:
            print(f"   ⚠️  Model info failed: {e}")
            print("   💡 This is expected if model hasn't been initialized yet")
        
        print("\n5️⃣ Testing with Gemini (if available)...")
        if is_gemini_preferred and os.environ.get("GEMINI_API_KEY"):
            try:
                # Set environment to prefer Gemini
                os.environ["USE_GEMINI_EMBEDDING"] = "true"
                
                from apps.core.utils.embedding_consistency import get_consistent_embedding_model
                print("   🚀 Attempting Gemini initialization...")
                model = get_consistent_embedding_model()
                print(f"   ✅ Gemini model initialized: {type(model).__name__}")
                
                # Test a quick embedding
                test_text = "test embedding"
                embedding = model.get_text_embedding(test_text)
                print(f"   ✅ Test embedding successful: {len(embedding)} dimensions")
                
                return True
                
            except Exception as e:
                print(f"   ❌ Gemini initialization failed: {e}")
                print("   💡 Falling back to HuggingFace...")
        
        print("\n6️⃣ Testing with HuggingFace (fallback)...")
        try:
            # Reset and force HuggingFace
            reset_embedding_model()
            os.environ.pop("USE_GEMINI_EMBEDDING", None)
            
            from apps.core.utils.embedding_consistency import get_consistent_embedding_model
            print("   🚀 Attempting HuggingFace initialization...")
            print("   ⏳ This may take a moment to download the model...")
            
            model = get_consistent_embedding_model()
            print(f"   ✅ HuggingFace model initialized: {type(model).__name__}")
            
            # Test a quick embedding
            test_text = "test embedding"
            embedding = model.get_text_embedding(test_text)
            print(f"   ✅ Test embedding successful: {len(embedding)} dimensions")
            
            return True
            
        except Exception as e:
            print(f"   ❌ HuggingFace initialization failed: {e}")
            print(f"   💡 Error details: {str(e)[:200]}...")
            
            if "connection" in str(e).lower() or "network" in str(e).lower():
                print("\n🌐 NETWORK ISSUE DETECTED")
                print("   The embedding model needs to download from the internet.")
                print("   Solutions:")
                print("   1. Check your internet connection")
                print("   2. Try again later")
                print("   3. Use Gemini API instead (set GEMINI_API_KEY)")
                print("   4. Use a pre-downloaded model")
            
            return False
        
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


def provide_solutions():
    """Provide solutions based on the test results."""
    
    print("\n" + "=" * 50)
    print("💡 SOLUTIONS & RECOMMENDATIONS")
    print("=" * 50)
    
    print("\n🎯 Option 1: Use Gemini API (Recommended)")
    print("   - Fast initialization (no downloads)")
    print("   - Reliable cloud-based service")
    print("   - Setup:")
    print("     export GEMINI_API_KEY=your_api_key_here")
    print("     export USE_GEMINI_EMBEDDING=true")
    
    print("\n🎯 Option 2: Pre-download HuggingFace Model")
    print("   - Download model when you have good internet")
    print("   - Use offline afterwards")
    print("   - Setup:")
    print("     python -c \"from sentence_transformers import SentenceTransformer; SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')\"")
    
    print("\n🎯 Option 3: Use Cached Model (if available)")
    print("   - Check if model is already downloaded:")
    print("     ls ~/.cache/huggingface/transformers/")
    print("     ls ~/.cache/torch/sentence_transformers/")
    
    print("\n🎯 Option 4: Temporary Workaround")
    print("   - Skip embedding consistency for now")
    print("   - Use the old system temporarily")
    print("   - Apply fix when network is stable")
    
    print("\n📋 Next Steps:")
    print("   1. Choose one of the options above")
    print("   2. Run the setup commands from docs/SETUP_COMMANDS_FIXED.md")
    print("   3. Test the RAG system through the UI")


if __name__ == "__main__":
    print("🚀 Starting Quick Embedding Fix Test...")
    
    success = test_embedding_fix()
    
    if success:
        print("\n🎉 EMBEDDING CONSISTENCY FIX WORKING!")
        print("   You can now proceed with the full setup commands.")
        print("   See: docs/SETUP_COMMANDS_FIXED.md")
    else:
        print("\n⚠️  EMBEDDING INITIALIZATION ISSUES DETECTED")
        provide_solutions()
    
    print(f"\n📄 For detailed setup instructions, see:")
    print(f"   docs/SETUP_COMMANDS_FIXED.md")
    print(f"   docs/EMBEDDING_CONSISTENCY_FIX.md")
