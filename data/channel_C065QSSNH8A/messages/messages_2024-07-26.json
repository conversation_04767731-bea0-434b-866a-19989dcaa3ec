{"date": "2024-07-26", "channel_id": "C065QSSNH8A", "message_count": 31, "messages": [{"ts": "1722013487.247719", "text": "<@U0690EB5JE5> we can meet here", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4h2Ue", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we can meet here"}]}]}]}, {"ts": "1722013480.497109", "text": "saurab<PERSON>.jain has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R07E67YF98V", "block_id": "qRrM6", "api_decoration_available": false, "call": {"v1": {"id": "R07E67YF98V", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1722013480, "active_participants": [], "all_participants": [{"slack_id": "U04DKEFP1K8"}, {"external_id": "FumCdQY_Rx6JmI_F0M3iGw", "avatar_url": "", "display_name": "<PERSON><PERSON><PERSON>"}], "display_id": "810-1178-7762", "join_url": "https://us06web.zoom.us/j/81011787762?pwd=6gb4JHZoU8ci6pu0NmYPHiaTMKI58k.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEX2ViZDdjYzI2NWJmMzQ5OWY4YTZkNjAwNjAzNjdmNWM1JnVzcz1TQVBfaXRRU0VQWUN5b09WZVF1enFsSVdGaE10cWVhNHlyRWp5ZEhzOWFsRkx1SmdKTlQwbndHWFVEdnZoZGUtRlowVHUwOUdReDVuOFgtbmxlNVJMcFYtbFBVRlRuVlV2WWdRMzFDX1pxUVN3T2NReC0zMmZxX1BLa2xMUmQ5ODJybGs2QU1ZUTNndlh1dXhwazBXRzFXZWlzbFBnay1NRk1PSmZQelpVOWVXV244cjM3bGdxTm90TjltYUhlRUc1ckNyM0dfd2xmaV9CVVZPZThiOTV0LUoudmhXQWFoRTI4dUo1SEgwcg%3D%3D&action=join&confno=81011787762&pwd=6gb4JHZoU8ci6pu0NmYPHiaTMKI58k.1", "name": "Zoom meeting started by <PERSON><PERSON><PERSON><PERSON>", "created_by": "U05185RFCNT", "date_end": 1722014380, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "galNR", "text": {"type": "mrkdwn", "text": "Meeting passcode: 6gb4JHZoU8ci6pu0NmYPHiaTMKI58k.1", "verbatim": false}}]}, {"ts": "1722010424.639709", "text": "<!here> are my primary task for today\n1. Re-test Bonus\n2. Test OTE\n3. Re-sync <PERSON><PERSON>'s data via namely ( this is manual effort)\n4. Re-test merit and org insights feedabck from yesterday ( <@U0690EB5JE5> are these merged as well)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jWpVF", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " are my primary task for today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Re-test Bonus"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Test OTE"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Re-sync Nauto's data via namely ( this is manual effort)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Re-test merit and org insights feedabck from yesterday ( "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " are these merged as well)"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1722010028.121119", "text": "also what s the update from vlad on adjustment letters?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722010028.121119", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "KPHAV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "also what s the update from vlad on adjustment letters?"}]}]}]}, {"ts": "1722010007.815339", "text": "cool, i will test it then", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "cool, i will test it then"}]}]}]}, {"ts": "1722009984.603019", "text": "<@U04DKEFP1K8> yes I have merged all the fixes for your findings and also target bonus fixes and OTE. I haven't checked in detail as I just came home around my 7pm and merged all the PRs and deployed in dev and staging.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1722010006.000000"}, "blocks": [{"type": "rich_text", "block_id": "mOzjk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " yes I have merged all the fixes for your findings and also target bonus fixes and OTE. I haven't checked in detail as I just came home around my 7pm and merged all the PRs and deployed in dev and staging."}]}]}]}, {"ts": "1722009899.514049", "text": "<@U0690EB5JE5> did we merge  bonus fixes and ote dev work?", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BAKUi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " did we merge  bonus fixes and ote dev work?"}]}]}]}, {"ts": "1722009739.823979", "text": "will join", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EdfAy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will join"}]}]}]}, {"ts": "1722009737.065349", "text": "i thought we had used leadership meeting time for it which was 930 am start", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "JuV8K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i thought we had used leadership meeting time for it which was 930 am start"}]}]}]}, {"ts": "1722009724.015629", "text": "<@U04DKEFP1K8>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mRXN8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1722009719.162069", "text": "its now", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lKTPL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "its now"}]}]}]}, {"ts": "1722009712.456829", "text": "we are joining now", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we are joining now"}]}]}]}, {"ts": "1722009696.528629", "text": "that is at after 30 min", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Vtv6B", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "that is at after 30 min"}]}]}]}, {"ts": "1722009687.210109", "text": "are we not joining Design meeting?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FSDgy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "are we not joining Design meeting?"}]}]}]}, {"ts": "1722009679.082349", "text": "i wanted to get update on items to test that are ready", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FSgYY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i wanted to get update on items to test that are ready"}]}]}]}, {"ts": "1722009660.351059", "text": "<@U0690EB5JE5> lets meet now", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RB4tZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " lets meet now"}]}]}]}, {"ts": "**********.145529", "text": "as a L5 manager, if I am approving L4's recommendations for their team, I should be able to see if their recommendations were above or below their budget. Today I am not able to see it in the current design. I think it's a flaw", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.145529", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "cw5av", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "as a L5 manager, if I am approving L4's recommendations for their team, I should be able to see if their recommendations were above or below their budget. Today I am not able to see it in the current design. I think it's a flaw"}]}]}]}, {"ts": "**********.870089", "text": "all of my testing is on demo account", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FeapD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "all of my testing is on demo account"}]}]}]}, {"ts": "**********.734519", "text": "for <PERSON><PERSON>, in merit view target bonus is 5040, whereas in the org view, her variable pay is listed as 126000. it looks like we might still have different sources of truth for reading the data", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.734519", "reply_count": 16, "blocks": [{"type": "rich_text", "block_id": "jhIWJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "for <PERSON><PERSON>, in merit view target bonus is 5040, whereas in the org view, her variable pay is listed as 126000. it looks like we might still have different sources of truth for reading the data"}]}]}]}, {"ts": "**********.997419", "text": "If you are past the merit cycle due date, can you still submit recommendations?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.997419", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "P5trF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If you are past the merit cycle due date, can you still submit recommendations?"}]}]}]}, {"ts": "1722005891.962749", "text": "even if I remove the countries in comp builder, these countries still show up", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722005891.962749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "UiU/a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "even if I remove the countries in comp builder, these countries still show up"}]}]}]}, {"ts": "1722005834.657669", "text": "<@U0690EB5JE5> FR and AR is still showing up in drop down list of countires to select even when there are no emp. I brought this issue up previously and we decided to re-upload the data to fix it. Is it also a regression issue? when can we fix it?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722005834.657669", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "3/OVm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " FR and AR is still showing up in drop down list of countires to select even when there are no emp. I brought this issue up previously and we decided to re-upload the data to fix it. Is it also a regression issue? when can we fix it?"}]}]}]}, {"ts": "1721955017.938159", "text": "<@U04DS2MBWP4> We will look into the issues reported above and update your morning.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PemW1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " We will look into the issues reported above and update your morning."}]}]}]}, {"ts": "1721952884.513209", "text": "nevermind, <PERSON><PERSON><PERSON><PERSON> explained. That's how their data is", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721952884.513209", "reply_count": 1, "edited": {"user": "U04DS2MBWP4", "ts": "1721952895.000000"}, "blocks": [{"type": "rich_text", "block_id": "kqTZx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "nevermind, <PERSON><PERSON><PERSON><PERSON> explained. That's how their data is"}]}]}]}, {"ts": "1721952332.935969", "text": "<@U0690EB5JE5> It also appears that equity calculations are wrong? Is this a calc issue or am I missing something?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lswWc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " It also appears that equity calculations are wrong? Is this a calc issue or am I missing something?"}]}]}]}, {"ts": "1721952171.583399", "text": "For nuato, For  <PERSON>, some of her equity shows 3 yrs and some show 5 yrs vesting. Is this a bug?", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F07ETMS0K16", "created": 1721952106, "timestamp": 1721952106, "name": "Screenshot 2024-07-25 at 5.01.41 PM.png", "title": "Screenshot 2024-07-25 at 5.01.41 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 363953, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07ETMS0K16/screenshot_2024-07-25_at_5.01.41___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07ETMS0K16/download/screenshot_2024-07-25_at_5.01.41___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 300, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 400, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 600, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 666, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 800, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ETMS0K16-553766d94f/screenshot_2024-07-25_at_5.01.41___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 853, "original_w": 2060, "original_h": 1716, "thumb_tiny": "AwAnADDSzQCCOKWkHbrQAuRnFJkZx3o45o7+5oAPxo/Gjn2o5xxQAtIDSmkoAMnmjnIpe1A6UAJR2/8Ar0evT8qO3b8qAFooooAKBRRQAn50du9LRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07ETMS0K16/screenshot_2024-07-25_at_5.01.41___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07ETMS0K16-ff5777c023", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For nuato, For  <PERSON>, some of her equity shows 3 yrs and some show 5 yrs vesting. Is this a bug?"}]}]}]}, {"ts": "1721951433.648849", "text": "<@U0690EB5JE5> In the merit planning view, we should change the \"one time bonus\" to \"lump sum award\" since it's lump sum that is typically included with salary budgets. One time bonus will come under the variable comp (bonus) category", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721951433.648849", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "YGgMS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " In the merit planning view, we should change the \"one time bonus\" to \"lump sum award\" since it's lump sum that is typically included with salary budgets. One time bonus will come under the variable comp (bonus) category"}]}]}]}, {"ts": "**********.514899", "text": "<@U0690EB5JE5> in the demo account, I tried to edit the active cycle by removing equity in the comp builder configurations and now I am unable to go past the bonus calculations screen. Is this a known bug?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.514899", "reply_count": 10, "blocks": [{"type": "rich_text", "block_id": "pt8YA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " in the demo account, I tried to edit the active cycle by removing equity in the comp builder configurations and now I am unable to go past the bonus calculations screen. Is this a known bug?"}]}]}]}, {"ts": "**********.481169", "text": "<@U0690EB5JE5> I am seeing a lot of bugs/errors on bonus and total target pay calculations. I will let <PERSON><PERSON><PERSON><PERSON> and you completing the testing before I do another round of testing", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.481169", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "3TGh1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am seeing a lot of bugs/errors on bonus and total target pay calculations. I will let <PERSON><PERSON><PERSON><PERSON> and you completing the testing before I do another round of testing"}]}]}]}, {"ts": "**********.470399", "text": "<@U0690EB5JE5> retested org insights, added observation here <https://docs.google.com/document/d/1SLa4vJWY7NF8OqvVsT1bUjGek1QAlADusolsW8gHObM/edit>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.470399", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "BEkau", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " retested org insights, added observation here "}, {"type": "link", "url": "https://docs.google.com/document/d/1SLa4vJWY7NF8OqvVsT1bUjGek1QAlADusolsW8gHObM/edit"}]}]}]}, {"ts": "1721946619.957689", "text": "<@U04DKEFP1K8> Did we make any changes to the merit view of wrt to bonus? if so, where can I see it? I am not seeing any new fields for bonus in the <http://test.stridehr.io|test.stridehr.io>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721946619.957689", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "P62l4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Did we make any changes to the merit view of wrt to bonus? if so, where can I see it? I am not seeing any new fields for bonus in the "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}]}]}]}], "created_at": "2025-05-22T21:35:34.616100"}