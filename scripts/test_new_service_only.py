#!/usr/bin/env python
"""
Test New Consolidated RAG Service Only

This script tests only the new ConsolidatedRAGService to ensure it works
independently before comparing with the old service.

Usage:
    python scripts/test_new_service_only.py
"""

import os
import sys
import django
import logging
import time

# Set up Django environment
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "multi_source_rag.config.settings.local")
django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.search.services.rag_service_new import ConsolidatedRAGService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_new_service():
    """Test the new consolidated RAG service."""
    logger.info("🚀 Testing New Consolidated RAG Service")
    logger.info("=" * 60)
    
    # Get test user and tenant
    user = User.objects.filter(username__in=['mahesh', 'testuser', 'admin']).first()
    tenant = Tenant.objects.filter(slug='stride').first()
    
    if not user:
        logger.error("❌ No test user found. Please create a user first.")
        return False
    if not tenant:
        logger.error("❌ No 'stride' tenant found. Please create tenant first.")
        return False
        
    logger.info(f"Using user: {user.username}, tenant: {tenant.slug}")
    
    # Test queries with different feature combinations
    test_queries = [
        {
            "query": "What are the latest issues reported?",
            "features": {},
            "description": "Basic search"
        },
        {
            "query": "What problems did Rachel mention?",
            "features": {"use_query_expansion": True},
            "description": "Search with query expansion"
        },
        {
            "query": "Explain authentication implementation issues",
            "features": {"use_multi_step_reasoning": True, "reasoning_mode": "sub_question"},
            "description": "Multi-step reasoning with sub-questions"
        },
        {
            "query": "Show me all bug reports and solutions",
            "features": {"use_query_expansion": True, "use_multi_step_reasoning": True},
            "description": "All features enabled"
        }
    ]
    
    try:
        # Initialize service
        logger.info("🔧 Initializing ConsolidatedRAGService...")
        service = ConsolidatedRAGService(user=user, tenant_slug=tenant.slug)
        logger.info("✅ Service initialized successfully")
        
        # Test each query
        for i, test_case in enumerate(test_queries, 1):
            logger.info(f"\n--- Test {i}/{len(test_queries)}: {test_case['description']} ---")
            logger.info(f"Query: '{test_case['query']}'")
            logger.info(f"Features: {test_case['features']}")
            
            try:
                start_time = time.time()
                
                result, docs = service.search(
                    query_text=test_case['query'],
                    top_k=20,
                    min_relevance_score=0.15,
                    **test_case['features']
                )
                
                processing_time = time.time() - start_time
                citations_count = result.citations.count()
                
                logger.info(f"✅ Success: {processing_time:.2f}s, {citations_count} citations, {len(docs)} docs")
                logger.info(f"   Answer length: {len(result.generated_answer)} chars")
                logger.info(f"   Retriever score: {result.retriever_score_avg:.3f}")
                logger.info(f"   Confidence: {result.llm_confidence_score:.3f}")
                
                # Show first 200 chars of answer
                answer_preview = result.generated_answer[:200] + "..." if len(result.generated_answer) > 200 else result.generated_answer
                logger.info(f"   Answer preview: {answer_preview}")
                
            except Exception as e:
                logger.error(f"❌ Test {i} failed: {str(e)}")
                return False
        
        # Test service statistics
        logger.info(f"\n📊 Service Statistics:")
        stats = service.get_stats()
        for key, value in stats.items():
            if isinstance(value, dict):
                logger.info(f"   {key}:")
                for sub_key, sub_value in value.items():
                    logger.info(f"     {sub_key}: {sub_value}")
            else:
                logger.info(f"   {key}: {value}")
        
        logger.info("\n✅ All tests passed! New service is working correctly.")
        return True
        
    except Exception as e:
        logger.error(f"❌ Service test failed: {str(e)}", exc_info=True)
        return False


def main():
    """Main execution."""
    try:
        success = test_new_service()
        
        if success:
            logger.info("\n🎉 New service test completed successfully!")
            logger.info("🔄 Ready to run comparison tests with old service.")
            return 0
        else:
            logger.error("\n❌ New service test failed!")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Test execution failed: {str(e)}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit(main())
