#!/usr/bin/env python
"""
Test script to validate LocalSlackSourceInterface works with the actual data structure.

This script tests that the interface correctly reads from data/channel_C065QSSNH8A/
and validates all functionality including document creation, token estimation, and
the 500-token chunking strategy.

Usage:
    python scripts/test_local_slack_data_structure.py
"""

import os
import sys
import django
import logging
from datetime import datetime
from typing import Dict, Any, List

# Set up Django
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'multi_source_rag'))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from apps.documents.interfaces.local_slack import LocalSlackSourceInterface

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_data_structure_detection():
    """Test that the interface correctly detects the data structure."""
    print("\n🧪 TESTING DATA STRUCTURE DETECTION")
    print("=" * 50)

    # Test with correct data directory path
    config = {
        "data_dir": "data/",
        "channel": "C065QSSNH8A"
    }

    try:
        interface = LocalSlackSourceInterface(config)

        print(f"✅ Interface created successfully")
        print(f"📂 Data dir: {interface.data_dir}")
        print(f"📂 Channel ID: {interface.channel_id}")
        print(f"📂 Channel dir: {interface.channel_dir}")

        if interface.channel_dir:
            print(f"✅ Channel directory found")
            print(f"📂 Messages dir: {interface.messages_dir}")
            print(f"📂 Threads dir: {interface.threads_dir}")
            print(f"📂 Users dir: {interface.users_dir}")
            print(f"📂 Metadata dir: {interface.metadata_dir}")

            # Check directory existence
            dirs_exist = {
                "messages": interface.messages_dir.exists(),
                "threads": interface.threads_dir.exists(),
                "users": interface.users_dir.exists(),
                "metadata": interface.metadata_dir.exists()
            }

            for dir_name, exists in dirs_exist.items():
                status = "✅" if exists else "⚠️"
                print(f"{status} {dir_name} directory exists: {exists}")

            return True
        else:
            print("❌ Channel directory not found")
            return False

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_configuration_validation():
    """Test configuration validation with the actual data structure."""
    print("\n🧪 TESTING CONFIGURATION VALIDATION")
    print("=" * 50)

    config = {
        "data_dir": "data/",
        "channel": "C065QSSNH8A"
    }

    try:
        interface = LocalSlackSourceInterface(config)
        is_valid = interface.validate_config()

        print(f"✅ Configuration validation result: {is_valid}")

        if is_valid:
            print("✅ All validation checks passed")
        else:
            print("⚠️ Some validation checks failed (check logs above)")

        return is_valid

    except Exception as e:
        print(f"❌ Error during validation: {e}")
        return False


def test_staged_data_info():
    """Test getting staged data information."""
    print("\n🧪 TESTING STAGED DATA INFO")
    print("=" * 50)

    config = {
        "data_dir": "data/",
        "channel": "C065QSSNH8A"
    }

    try:
        interface = LocalSlackSourceInterface(config)
        info = interface.get_staged_data_info()

        print(f"✅ Staged data info retrieved")
        print(f"📊 Channel ID: {info.channel_id}")
        print(f"📊 Available dates: {len(info.available_dates)}")
        print(f"📊 Total messages: {info.total_messages}")
        print(f"📊 User count: {info.user_count}")
        print(f"📊 Thread count: {info.thread_count}")

        if info.available_dates:
            print(f"📊 Date range: {info.available_dates[0]} to {info.available_dates[-1]}")

        # Validate data quality
        if info.total_messages > 0:
            print("✅ Messages found in data")
        else:
            print("❌ No messages found")
            return False

        if info.user_count > 0:
            print("✅ Users found in data")
        else:
            print("⚠️ No users found")

        return True

    except Exception as e:
        print(f"❌ Error getting staged data info: {e}")
        return False


def test_document_creation():
    """Test document creation with 500-token chunking strategy."""
    print("\n🧪 TESTING DOCUMENT CREATION")
    print("=" * 50)

    config = {
        "data_dir": "data/",
        "channel": "C065QSSNH8A"
    }

    try:
        interface = LocalSlackSourceInterface(config)

        # Test with small limit for faster testing
        docs = interface.fetch_documents(limit=3, days_back=30)

        print(f"✅ Fetched {len(docs)} documents")

        if not docs:
            print("❌ No documents created")
            return False

        # Analyze first document
        doc = docs[0]
        print(f"\n📄 Sample Document Analysis:")
        print(f"   - ID: {doc['id']}")
        print(f"   - Title: {doc['title']}")
        print(f"   - Content length: {len(doc['content'])} characters")
        print(f"   - Content type: {doc.get('content_type', 'unknown')}")

        # Analyze metadata
        metadata = doc['metadata']
        print(f"\n📊 Metadata Analysis:")
        print(f"   - Chunking strategy: {metadata['chunking_strategy']}")
        print(f"   - Estimated tokens: {metadata['estimated_tokens']}")
        print(f"   - Has overlap: {metadata['has_overlap']}")
        print(f"   - Message count: {metadata['message_count']}")
        print(f"   - Participants: {metadata['participant_count']}")
        print(f"   - Thread count: {metadata['thread_count']}")
        print(f"   - Engagement score: {metadata['engagement_score']:.2f}")

        # Validate token-based chunking
        if metadata['chunking_strategy'] == 'token_based_500':
            print("✅ Using correct 500-token chunking strategy")
        else:
            print(f"⚠️ Unexpected chunking strategy: {metadata['chunking_strategy']}")

        # Validate token count
        estimated_tokens = metadata['estimated_tokens']
        if estimated_tokens <= 500:
            print(f"✅ Token count within limit: {estimated_tokens} <= 500")
        else:
            print(f"⚠️ Token count exceeds limit: {estimated_tokens} > 500")

        # Test all documents
        token_counts = [doc['metadata']['estimated_tokens'] for doc in docs]
        avg_tokens = sum(token_counts) / len(token_counts)
        max_tokens = max(token_counts)
        min_tokens = min(token_counts)

        print(f"\n📈 Token Statistics for {len(docs)} documents:")
        print(f"   - Average tokens: {avg_tokens:.1f}")
        print(f"   - Token range: {min_tokens} - {max_tokens}")
        print(f"   - Documents over 500 tokens: {sum(1 for t in token_counts if t > 500)}")

        return True

    except Exception as e:
        print(f"❌ Error creating documents: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_message_loading():
    """Test message loading functionality."""
    print("\n🧪 TESTING MESSAGE LOADING")
    print("=" * 50)

    config = {
        "data_dir": "data/",
        "channel": "C065QSSNH8A"
    }

    try:
        interface = LocalSlackSourceInterface(config)

        # Test loading messages for a specific time period
        messages = interface.fetch_channel_messages(
            days_back=7,  # Last 7 days
            include_threads=True,
            filter_bots=True
        )

        print(f"✅ Loaded {len(messages)} messages from last 7 days")

        if messages:
            # Analyze message structure
            sample_msg = messages[0]
            print(f"\n📧 Sample Message Analysis:")
            print(f"   - User: {sample_msg.user_name}")
            print(f"   - Timestamp: {sample_msg.timestamp}")
            print(f"   - Text length: {len(sample_msg.text)} characters")
            print(f"   - Has reactions: {len(sample_msg.reactions) > 0}")
            print(f"   - Reply count: {sample_msg.reply_count}")
            print(f"   - Has files: {len(sample_msg.files) > 0}")

            # Count message types
            thread_messages = sum(1 for msg in messages if msg.thread_ts and msg.thread_ts != msg.timestamp)
            parent_messages = len(messages) - thread_messages

            print(f"\n📊 Message Type Distribution:")
            print(f"   - Parent messages: {parent_messages}")
            print(f"   - Thread replies: {thread_messages}")
            print(f"   - Total messages: {len(messages)}")

        return True

    except Exception as e:
        print(f"❌ Error loading messages: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 TESTING LOCAL SLACK DATA STRUCTURE COMPATIBILITY")
    print("=" * 70)

    tests = [
        test_data_structure_detection,
        test_configuration_validation,
        test_staged_data_info,
        test_message_loading,
        test_document_creation
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ PASSED")
            else:
                print("❌ FAILED")
        except Exception as e:
            print(f"❌ CRASHED: {e}")

    print(f"\n📊 TEST RESULTS")
    print("=" * 30)
    print(f"Passed: {passed}/{total}")
    print(f"Success rate: {(passed/total)*100:.1f}%")

    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ LocalSlackSourceInterface is working correctly with the data structure")
        print("✅ Ready for production use with data/channel_C065QSSNH8A/")
        return True
    else:
        print("⚠️ Some tests failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
