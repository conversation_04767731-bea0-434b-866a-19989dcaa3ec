{"date": "2024-07-24", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1721832209.407649", "text": "Guys, feel free to add agenda items. Few things on my mind:\n\n1. Updates on action items from last two calls. <@U04DKEFP1K8> it's easier to add checkmarks to the list just like you did above. its way easier to track that way.\n2. Status of OTE for Vercara\n3. Divide and conquer product management\n4. Best practices for converting current beta customers to fully paid for their next cycle", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "t+nIZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Guys, feel free to add agenda items. Few things on my mind:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Updates on action items from last two calls. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " it's easier to add checkmarks to the list just like you did above. its way easier to track that way."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Status of OTE for Vercara"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Divide and conquer product management"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Best practices for converting current beta customers to fully paid for their next cycle"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1721831403.793509", "text": "<@U04DKEFP1K8> Have pushed all the fixes/changes to production ", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "rocket", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "IxgqJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Have pushed all the fixes/changes to production "}]}]}]}, {"ts": "**********.692199", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> can we please create a prod env for RightWayHealth?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.692199", "reply_count": 3, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "EoP5e", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we please create a prod env for RightWayHealth?"}]}]}]}, {"ts": "**********.245839", "text": "<@U04DS2MBWP4> There was a meeting on july 16th with dgoc team for which <PERSON><PERSON> was organizer , can you find the link to meeting recording , meeting occured between 12-1245 pm", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.245839", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Y/Gan", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " There was a meeting on july 16th with dgoc team for which <PERSON><PERSON> was organizer , can you find the link to meeting recording , meeting occured between 12-1245 pm"}]}]}]}, {"ts": "**********.098499", "text": "<@U04DS2MBWP4> From we<PERSON>'s inbox can you send me recording link for the meeting that happended with <PERSON><PERSON><PERSON> on July 16th at 8am PT", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.098499", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Cm61T", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " From we<PERSON>'s inbox can you send me recording link for the meeting that happended with <PERSON><PERSON><PERSON> on July 16th at 8am PT"}]}]}]}, {"ts": "1721778020.772419", "text": "<@U04DS2MBWP4> going forward please download recording of customer implementation related meetings and upload in recording folder for every customer, i will follow the same if i am the organizer.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1721778020.772419", "reply_count": 1, "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "done", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "sqwX/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " going forward please download recording of customer implementation related meetings and upload in recording folder for every customer, i will follow the same if i am the organizer."}]}]}]}, {"ts": "**********.771809", "text": "<@U04DKEFP1K8> are these <https://drive.google.com/drive/u/0/folders/1c6Nbcbg5uE6I-Xz9ZTlb2DgFYCmMpPlG|data templates> stored in the implementation folder up to date? Need to send these to RightwayHealth", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.771809", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "esCQY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " are these "}, {"type": "link", "url": "https://drive.google.com/drive/u/0/folders/1c6Nbcbg5uE6I-Xz9ZTlb2DgFYCmMpPlG", "text": "data templates"}, {"type": "text", "text": " stored in the implementation folder up to date? Need to send these to RightwayHealth"}]}]}]}], "created_at": "2025-05-22T21:35:34.617103"}