{"thread_ts": "1739323324.586419", "channel_id": "C065QSSNH8A", "reply_count": 8, "replies": [{"ts": "1739325482.071109", "text": "Yes, he’s just going to want to take the bonus amounts that they decide on in the bonus cycle and upload them into the regular merit cycle. That means it’ll probably need to be updated midcycle, since they both launch at the same time. So we would start the cycle with no value in that field and then add it.And that way all of the information is in one cycle for them to download.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739323324.586419", "blocks": [{"type": "rich_text", "block_id": "sk0cN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, he’s just going to want to take the bonus amounts that they decide on in the bonus cycle and upload them into the regular merit cycle. That means it’ll probably need to be updated midcycle, since they both launch at the same time. So we would start the cycle with no value in that field and then add it.And that way all of the information is in one cycle for them to download."}]}]}]}, {"ts": "1739326098.780449", "text": ":+1: ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739323324.586419", "blocks": [{"type": "rich_text", "block_id": "TnuD3", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "thumbsup", "unicode": "1f44d"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1739764770.917339", "text": "<@U07EJ2LP44S> I am thinking to keep the same field \"Bonus Award\" in ready only mode instead of adding new one. But we will need to keep the component and budgets the same way as of today. This makes the implementation very simple for time being.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739323324.586419", "blocks": [{"type": "rich_text", "block_id": "ogCpm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am thinking to keep the same field \"Bonus Award\" in ready only mode instead of adding new one. But we will need to keep the component and budgets the same way as of today. This makes the implementation very simple for time being."}]}]}]}, {"ts": "1739764799.814879", "text": "Let me know if this is fine with you. Lets push for this with customer and any more improvements will be for future.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739323324.586419", "blocks": [{"type": "rich_text", "block_id": "DKpLv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me know if this is fine with you. Lets push for this with customer and any more improvements will be for future."}]}]}]}, {"ts": "1739797652.861229", "text": "If we can upload to it, that is fine.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739323324.586419", "blocks": [{"type": "rich_text", "block_id": "n+0hZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If we can upload to it, that is fine."}]}]}]}, {"ts": "1739797682.338839", "text": "You mean upload the data? ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739323324.586419", "edited": {"user": "U0690EB5JE5", "ts": "1739800398.000000"}, "blocks": [{"type": "rich_text", "block_id": "IXrsQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "You mean upload the data? "}]}]}]}, {"ts": "1739809957.864169", "text": "Yes; we just need to be able to populate the column mid-cycle through an upload.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739323324.586419", "blocks": [{"type": "rich_text", "block_id": "lT1iq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes; we just need to be able to populate the column mid-cycle through an upload."}]}]}]}, {"ts": "1739810485.853629", "text": "that I will take care of it from backend.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739323324.586419", "blocks": [{"type": "rich_text", "block_id": "ThX6m", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "that I will take care of it from backend."}]}]}]}], "created_at": "2025-05-22T22:00:49.340133"}