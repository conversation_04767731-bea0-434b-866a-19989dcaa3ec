{"date": "2024-09-26", "channel_id": "C065QSSNH8A", "message_count": 21, "messages": [{"ts": "1727366633.510289", "text": "Let's meet at 9:30 <@U07EJ2LP44S> <@U07NBMXTL1E> <@U04DKEFP1K8>", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "L9gNI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let's meet at 9:30 "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1727366608.208529", "text": "okay", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "cixRH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "okay"}]}]}]}, {"ts": "1727366555.068269", "text": "I’d like to grab some food but then yes", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NxQEj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I’d like to grab some food but then yes"}]}]}]}, {"ts": "1727366401.614109", "text": "<!here> Do we want to do the demo data stories now?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BtgkA", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Do we want to do the demo data stories now?"}]}]}]}, {"ts": "1727366316.251759", "text": "Lol. Will give us some more time to push the last minute changes we discussed in the call.\n<@U0690EB5JE5> <@U04DKEFP1K8> FYA", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ze2yE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON>. Will give us some more time to push the last minute changes we discussed in the call.\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " FYA"}]}]}]}, {"ts": "1727366176.489339", "text": "<!channel> ^", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ew51r", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "channel"}, {"type": "text", "text": " ^"}]}]}]}, {"ts": "1727365368.827979", "text": "^^ <@U07M6QKHUC9> <@U04DKEFP1K8>", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oBZUF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "^^ "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1727365351.369639", "text": "She said 915 on monday", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ooK6a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She said 915 on monday"}]}]}]}, {"ts": "1727365331.482219", "text": "<PERSON> is pushing LOLOL", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "E9PVn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> is pushing LOLOL"}]}]}]}, {"ts": "1727363238.594889", "text": "Base currency is ready for merging. Will do tomorrow as it would impact SDF meeting.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "raDyE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Base currency is ready for merging. Will do tomorrow as it would impact SDF meeting."}]}]}]}, {"ts": "1727363196.811739", "text": "There two new features released recently\n• Hide self in merit planning and reports for super admins\n• HRBP role", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1727363208.000000"}, "blocks": [{"type": "rich_text", "block_id": "fgm7W", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There two new features released recently\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hide self in merit planning and reports for super admins"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "HRBP role"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1727363005.781369", "text": "Couple more couldn't reproduce or not issues\n• <https://compiify.atlassian.net/browse/COM-3632>\n• <https://compiify.atlassian.net/browse/COM-3646>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VRACw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Couple more couldn't reproduce or not issues\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3632"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3646"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1727362915.039209", "text": "<@U04DKEFP1K8> Issues fixed today\n• <https://compiify.atlassian.net/browse/COM-3642>\n• <https://compiify.atlassian.net/browse/COM-3628>\n• <https://compiify.atlassian.net/browse/COM-3629>\n• <https://compiify.atlassian.net/browse/COM-3643>\n• <https://compiify.atlassian.net/browse/COM-3644>\n• <https://compiify.atlassian.net/browse/COM-3645>\n• <https://compiify.atlassian.net/browse/COM-3647>\n", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727362915.039209", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "AoREl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Issues fixed today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3642"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3628"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3629"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3643"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3644"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3645"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3647"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1727360658.244279", "text": "I have impersonated <PERSON> in SDF Test. She is a manager and not a cycle admin. Shouldn't comp builder be hidden?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727360658.244279", "reply_count": 5, "files": [{"id": "F07PKMASZMF", "created": 1727360635, "timestamp": 1727360635, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 142179, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07PKMASZMF/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07PKMASZMF/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_360.png", "thumb_360_w": 360, "thumb_360_h": 306, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_480.png", "thumb_480_w": 480, "thumb_480_h": 408, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_720.png", "thumb_720_w": 720, "thumb_720_h": 611, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_800.png", "thumb_800_w": 800, "thumb_800_h": 679, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_960.png", "thumb_960_w": 960, "thumb_960_h": 815, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PKMASZMF-2b61dfcd5a/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 870, "original_w": 1420, "original_h": 1206, "thumb_tiny": "AwAoADDQKsTw5H4UoBC4JyfWmhlZmAxkHninjPt+FMBAn+0350qjAxkn60mfY0Z570gHUUmaWgBvOT16+tLRgelFAACfSgE+n60cjtQfpQAUtIKWgBKKD0NB6GgBQPSkwKUdBRQAYooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07PKMASZMF/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07PKMASZMF-945966ce03", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "87+ob", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have impersonated <PERSON> in SDF Test. She is a manager and not a cycle admin. Shouldn't comp builder be hidden?"}]}]}]}, {"ts": "1727303433.925219", "text": "<@U07EJ2LP44S> you are driving the sdf demo tomorrow?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1727303433.925219", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "dHXU/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " you are driving the sdf demo tomorrow?"}]}]}]}, {"ts": "1727303346.716499", "text": "yes, thanks", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xoxCv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes, thanks"}]}]}]}, {"ts": "1727302543.579549", "text": "I have not, but I can work on it in the morning if need be. Basically updating the right column?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0obFD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have not, but I can work on it in the morning if need be. Basically updating the right column?"}]}]}]}, {"ts": "1727302285.147919", "text": "<@U07EJ2LP44S> are you working on cleaning up the doc \"Compiify Improvements for SDF\" for tomorrow's call or is it <@U04DKEFP1K8>?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "aA3K5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " are you working on cleaning up the doc \"Compiify Improvements for SDF\" for tomorrow's call or is it "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1727298055.895689", "text": "<@U07EJ2LP44S> I’ve completed a sanity check for the sdf-test, and the environment looks good to proceed with tomorrow’s demo. However, there are a few issues with the cycle builder that will need to be addressed (we should avoid showcasing these during the demo).\n1. additional planning level <https://compiify.atlassian.net/browse/COM-3642>\n2. proration <https://compiify.atlassian.net/browse/COM-3645>\n3. dates for cycle <https://compiify.atlassian.net/browse/COM-3647>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13703::aa6f4271af9f41d59c6fddd9a650acaf", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3642?atlOrigin=eyJpIjoiZDNhZmQ2Yjg1MTI4NGRjZDljMmQ5ZjQ5MGQyZTY4ZWIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3642 Cycle edit failure on sdf-test [9/25 PT testing] on including addit…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13703::a46afa83dd784e77b4c2620ee2ab60a7", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13703\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3642\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3642", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:13706::c5ce9be1d17745dc9871b7c89d3a0b1c", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3645?atlOrigin=eyJpIjoiNWI5MzE1MmYzZTFjNGQ2YzgzNGIwZWViOTI0ZjUwZmQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3645 When editing a cycle using proration, cannot move on from proration…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13706::b0d8660e51ff48ffbacb12e4fc99981e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13706\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3645\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3645", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 3, "blocks": [{"type": "section", "block_id": "uf:ih:13708::f37c8e7c38ca41c3a5c86a20e7d25e4d", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3647?atlOrigin=eyJpIjoiNDdhMmU4NzFlN2MyNDZkZGE1OWFkNzgxNWU3ODMzNmQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3647 Should allow a past date when setting up a cycle>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13708::1abe5cd9ded149b282c4ab9df101f16e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13708\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3647\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3647", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "omvCJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I’ve completed a sanity check for the sdf-test, and the environment looks good to proceed with tomorrow’s demo. However, there are a few issues with the cycle builder that will need to be addressed (we should avoid showcasing these during the demo).\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "additional planning level "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3642"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "proration "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3645"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "dates for cycle "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3647"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1727291414.736329", "text": "<@U04DKEFP1K8> as an admin, how do I made edits after I have reviewed and accepted a manager's recommendation? I am unable to do it in the demo env", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727291414.736329", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "OYZpl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " as an admin, how do I made edits after I have reviewed and accepted a manager's recommendation? I am unable to do it in the demo env"}]}]}]}, {"ts": "1727289079.728989", "text": "<@U07EJ2LP44S> For tomorrow's sdf call here are critical talking point around which you can prep the demo\n1. SSO support ( SAML with google) - ( Numerous reset password last time)\n2. Using the bulk edit feature + employee edit feature to allow sdf admins to update any data items without Stride team's support ( Numerous data update issue was requested last time)\n3. Data consistency in app + any downloadable reports\n4. Updated merit workflow ( they needed Managers to take greater responsibility in the process, new updates allows that)\n    a. Updated promotion workflow with improved custom job title support\n    b. Improved flags with addition of manual flags\n5. Org and People Insights ( <PERSON> had to prep hand made charts last time around)\n6. Improved pay positioning ( optional to bring it up)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1727289079.728989", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "8QOx0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " For tomorrow's sdf call here are critical talking point around which you can prep the demo\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SSO support ( SAML with google) - ( Numerous reset password last time)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Using the bulk edit feature + employee edit feature to allow sdf admins to update any data items without Stride team's support ( Numerous data update issue was requested last time)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Data consistency in app + any downloadable reports"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Updated merit workflow ( they needed Managers to take greater responsibility in the process, new updates allows that)"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Updated promotion workflow with improved custom job title support"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Improved flags with addition of manual flags"}]}], "style": "ordered", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>g and People Insights ( <PERSON> had to prep hand made charts last time around)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Improved pay positioning ( optional to bring it up)"}]}], "style": "ordered", "indent": 0, "offset": 4, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.655431"}