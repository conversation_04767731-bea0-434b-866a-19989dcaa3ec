{"date": "2025-02-19", "channel_id": "C065QSSNH8A", "message_count": 34, "messages": [{"ts": "1739986529.116449", "text": "<@U0690EB5JE5> There's a bug/use case issue with the market adjustment for Curana. If you put in a market adjustment and it pushes the employee beyond the threshold (so for example from .57 to .61), the market adjustment then becomes uneditable. We should be able to edit the market adjustment until submission.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739986529.116449", "reply_count": 3, "files": [{"id": "F08DMTNV7ST", "created": 1739986526, "timestamp": 1739986526, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 26570, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DMTNV7ST/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DMTNV7ST/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_360.png", "thumb_360_w": 360, "thumb_360_h": 103, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_480.png", "thumb_480_w": 480, "thumb_480_h": 137, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_720.png", "thumb_720_w": 720, "thumb_720_h": 206, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_800.png", "thumb_800_w": 800, "thumb_800_h": 229, "original_w": 916, "original_h": 262, "thumb_tiny": "AwANADDSP1NGP9o0EZpMD3oAX8aWkxRigBaKTHNGKAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DMTNV7ST/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DMTNV7ST-7a517721ec", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "WHfCq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " There's a bug/use case issue with the market adjustment for Curana. If you put in a market adjustment and it pushes the employee beyond the threshold (so for example from .57 to .61), the market adjustment then becomes uneditable. We should be able to edit the market adjustment until submission."}]}]}]}, {"ts": "1739984435.437539", "text": "<@U0690EB5JE5> If we use the range functionality for recommendations, is it taking the middle value to estimate budgets? Or the high value?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739984435.437539", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "Nr3rv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " If we use the range functionality for recommendations, is it taking the middle value to estimate budgets? Or the high value?"}]}]}]}, {"ts": "1739983471.538559", "text": "<@U07M6QKHUC9> <http://demo.stridehr.io|demo.stridehr.io>  is up now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739976734.472849", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "RrSx2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://demo.stridehr.io", "text": "demo.stridehr.io"}, {"type": "text", "text": "  is up now."}]}]}]}, {"ts": "1739983278.931099", "text": "<@U07EJ2LP44S> Rolled back the comments column change. Things should be in the same state as yesterday.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3KP0g", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Rolled back the comments column change. Things should be in the same state as yesterday."}]}]}]}, {"ts": "**********.559549", "text": "This is to the main curanahealth account, the only thing I changed was update type and the performance rating name", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8Eo5I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is to the main curanahealth account, the only thing I changed was update type and the performance rating name"}]}]}]}, {"ts": "**********.522929", "text": "I am getting the Curana performance ratings ready for upload; I'll give it a shot but if it doesn't work i'll send to you", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "RfqX2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am getting the Curana performance ratings ready for upload; I'll give it a shot but if it doesn't work i'll send to you"}]}]}]}, {"ts": "**********.854149", "text": "Ok I am rolling back the comments thing just in case. I will dig in deeper tomorrow on the comments part.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.854149", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "zi8Tq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok I am rolling back the comments thing just in case. I will dig in deeper tomorrow on the comments part."}]}]}]}, {"ts": "1739981722.339159", "text": "Everything seems to be ok now so if we can not touch it that would be great", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LCx8R", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Everything seems to be ok now so if we can not touch it that would be great"}]}]}]}, {"ts": "1739981697.286449", "text": "She was impersonating <PERSON>, I also didn't try to reproduce. She just said she's been impersonating then downloading and it was workign for everyone until this morning, so probably the comments thing?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HoqIf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She was impersonating <PERSON>, I also didn't try to reproduce. She just said she's been impersonating then downloading and it was workign for everyone until this morning, so probably the comments thing?"}]}]}]}, {"ts": "1739981533.721819", "text": "I am unable to reproduce the issue locally", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YWl3i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am unable to reproduce the issue locally"}]}]}]}, {"ts": "1739981523.477589", "text": "<@U07EJ2LP44S> Can you please check with her who she was impersonating?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PhJG4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Can you please check with her who she was impersonating?"}]}]}]}, {"ts": "1739980857.765099", "text": "I think the comments column change we made today is causing the issue. Let me rollback the change for time being.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rW1XP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think the comments column change we made today is causing the issue. Let me rollback the change for time being."}]}]}]}, {"ts": "1739980817.526249", "text": "She was trying to export the merit table while impersonating but it gave her errors then it crashed", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "kgxP3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She was trying to export the merit table while impersonating but it gave her errors then it crashed"}]}]}]}, {"ts": "1739980795.218829", "text": "<@U0690EB5JE5> Diversified down again", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IkxY+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Diversified down again"}]}]}]}, {"ts": "1739980263.587899", "text": "<@U0690EB5JE5> Tith<PERSON> says it's fine to just use total rewards as long as it's downloadable and with those changes they asked for", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739980263.587899", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "G0zPd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> says it's fine to just use total rewards as long as it's downloadable and with those changes they asked for"}]}]}]}, {"ts": "1739980228.234569", "text": "<@U0690EB5JE5> can you turn on another environment for me to run a demo in? I now need a bonus-only cycle for the Curana demo.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739980228.234569", "reply_count": 17, "blocks": [{"type": "rich_text", "block_id": "BBwe+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you turn on another environment for me to run a demo in? I now need a bonus-only cycle for the Curana demo."}]}]}]}, {"ts": "1739977343.954059", "text": "30 mnts will check ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "prCfU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "30 mnts will check "}]}]}]}, {"ts": "1739977333.453979", "text": "Let me check some depoyment issue probably ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ka4Op", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me check some depoyment "}, {"type": "text", "text": "issue probably "}]}]}]}, {"ts": "1739977277.099479", "text": "<@U0690EB5JE5> Diversified seems to be down. <PERSON> cannot get in, it says to create a new cycle, and I get this:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739977277.099479", "reply_count": 16, "files": [{"id": "F08DUMG60TG", "created": 1739977272, "timestamp": 1739977272, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 89537, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DUMG60TG/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DUMG60TG/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_360.png", "thumb_360_w": 360, "thumb_360_h": 216, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_480.png", "thumb_480_w": 480, "thumb_480_h": 288, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_720.png", "thumb_720_w": 720, "thumb_720_h": 433, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_800.png", "thumb_800_w": 800, "thumb_800_h": 481, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_960.png", "thumb_960_w": 960, "thumb_960_h": 577, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 615, "original_w": 1754, "original_h": 1054, "thumb_tiny": "AwAcADDSpKWigBOlLmkpCRnmgB2aM0gOaWgAooooAKayKxyygn3p1FACABRgDApaKKAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DUMG60TG/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DUMG60TG-75b0cd046f", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "lZhgB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Diversified seems to be down. <PERSON> cannot get in, it says to create a new cycle, and I get this:"}]}]}]}, {"ts": "1739976796.338019", "text": "It's a one off demo to an investor", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "WnrLC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It's a one off demo to an investor"}]}]}]}, {"ts": "1739976734.472849", "text": "<@U0690EB5JE5> I have a demo tomorrow. Can we enable <http://demo.stridehr.io|demo.stridehr.io>?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1739976734.472849", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "RI0Ba", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I have a demo tomorrow. Can we enable "}, {"type": "link", "url": "http://demo.stridehr.io", "text": "demo.stridehr.io"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1739974725.994419", "text": "<@U0690EB5JE5> you replied to <PERSON>‘s email from back in January but it’s <PERSON><PERSON><PERSON> who had the issue with SSO. Is it the configuration in Curana that you updated?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739974725.994419", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Yfxh9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " you replied to <PERSON>‘s email from back in January but it’s <PERSON><PERSON><PERSON> who had the issue with SSO. Is it the configuration in Curana that you updated?"}]}]}]}, {"ts": "1739965673.114909", "text": "<@U07EJ2LP44S> uploaded", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739896033.103619", "subtype": "thread_broadcast", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "d<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " uploaded"}]}]}]}, {"ts": "1739924573.115709", "text": "<@U0690EB5JE5> Based on the amount of budget used (adding up total bonus awards), the system is showing an incorrect amount remaining. The budget is  240,261.57, the usage of the budget is 233651.00. That should leave 6610 but the system is showing only 3273. There a handful of prorated employees, but there's no scenario where I can come up with the right amount remaining. He should have double what he has left.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739924573.115709", "reply_count": 4, "files": [{"id": "F08EAAA4SAD", "created": 1739924354, "timestamp": 1739924354, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 113792, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08EAAA4SAD/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08EAAA4SAD/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_360.png", "thumb_360_w": 360, "thumb_360_h": 82, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_480.png", "thumb_480_w": 480, "thumb_480_h": 109, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_720.png", "thumb_720_w": 720, "thumb_720_h": 163, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_800.png", "thumb_800_w": 800, "thumb_800_h": 182, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_960.png", "thumb_960_w": 960, "thumb_960_h": 218, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 232, "original_w": 2220, "original_h": 504, "thumb_tiny": "AwAKADDR4z3/ACo496SlzTAOPej8/wAqSigAOcfL19waUZ4znPsKSgUAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08EAAA4SAD/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08EAAA4SAD-5627b73181", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "/2IgM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Based on the amount of budget used (adding up total bonus awards), the system is showing an incorrect amount remaining. The budget is  240,261.57, the usage of the budget is 233651.00. That should leave 6610 but the system is showing only 3273. There a handful of prorated employees, but there's no scenario where I can come up with the right amount remaining. He should have double what he has left."}]}]}]}, {"ts": "1739907863.464999", "text": "<@U07EJ2LP44S> There is some UX issue in org edit which is causing this issue. Please do click on percent inputs for UI to auto calculate the numbers correctly.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739891738.743249", "subtype": "thread_broadcast", "files": [{"id": "F08DFTABRHD", "created": 1739907859, "timestamp": 1739907859, "name": "Screenshot 2025-02-19 at 1.12.35 AM.png", "title": "Screenshot 2025-02-19 at 1.12.35 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 91985, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DFTABRHD/screenshot_2025-02-19_at_1.12.35___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DFTABRHD/download/screenshot_2025-02-19_at_1.12.35___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_360.png", "thumb_360_w": 290, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_480.png", "thumb_480_w": 387, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_720.png", "thumb_720_w": 580, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_800.png", "thumb_800_w": 644, "thumb_800_h": 800, "original_w": 691, "original_h": 858, "thumb_tiny": "AwAwACbRbnuRSqCO+aB1/wDrUtABRSEZ60bR6CgBaKBxRQAnUn/GlpO9LQAjf55pR05pDQOlAC0UDmigBp6//WpRRgelA+mKAA0dqZK5UjEbP9KZ5z4/1D07MCYUtRxyFiQYmT3NSUgP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DFTABRHD/screenshot_2025-02-19_at_1.12.35___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DFTABRHD-bad800f4b4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "oas0h", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " There is some UX issue in org edit which is causing this issue. Please do click on percent inputs for UI to auto calculate the numbers correctly."}]}]}]}, {"ts": "1739907780.694879", "text": "Will upload tomorrow", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739896033.103619", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "w4ltn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will upload tomorrow"}]}]}]}, {"ts": "1739907484.270799", "text": "<@U07EJ2LP44S> The flags issue is fixed and published the cycle as well. There are 63 flags now.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "z+zoI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " The flags issue is fixed and published the cycle as well. There are 63 flags now."}]}]}]}, {"ts": "1739907114.128789", "text": "let me know what details are required.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "p1dR/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "let me know what details are required."}]}]}]}, {"ts": "1739907089.543489", "text": "not sure if we have those columns added in org view. I can generate one tomorrow or day after.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739907089.543489", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "fatpB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "not sure if we have those columns added in org view. I can generate one tomorrow or day after."}]}]}]}, {"ts": "1739906988.377399", "text": "We can comparing with org view i.e. employee report", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "t8F3S", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can comparing with org view i.e. employee report"}]}]}]}, {"ts": "1739906838.647289", "text": "Thank you; she's asking if there's any other way to filter by people who have had their attainment changed, but I think the answer is no. Unless I'm missing something?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Zv4v9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you; she's asking if there's any other way to filter by people who have had their attainment changed, but I think the answer is no. Unless I'm missing something?"}]}]}]}, {"ts": "1739906378.494079", "text": "<@U07EJ2LP44S> UPDATE: There few more issues we found while testing. Fixes are being deployed. Will update here once done.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739893569.641669", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "pemIK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " UPDATE: There few more issues we found while testing. Fixes are being deployed. Will update here once done."}]}]}]}, {"ts": "1739904428.253229", "text": "Also, Curana just set their recommender level to ALL MANAGERS in the cycle - so this will be a really large cycle. If there's anything we need to do to prep for that, please lets do it!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739904428.253229", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "RfRze", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also, Curana just set their recommender level to ALL MANAGERS in the cycle - so this will be a really large cycle. If there's anything we need to do to prep for that, please lets do it!"}]}]}]}, {"ts": "1739904174.386769", "text": "Just sent an email about Curana SSO - need to investigate", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EOMzt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just sent an email about Curana SSO - need to investigate"}]}]}]}], "created_at": "2025-05-22T21:35:34.711664"}