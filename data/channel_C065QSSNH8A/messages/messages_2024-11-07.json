{"date": "2024-11-07", "channel_id": "C065QSSNH8A", "message_count": 24, "messages": [{"ts": "1731001600.213929", "text": "Valgenesis doesn't have performance ratings option:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1731001600.213929", "reply_count": 5, "files": [{"id": "F08006HF128", "created": 1731001596, "timestamp": 1731001596, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 183017, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08006HF128/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08006HF128/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08006HF128-d0c03b9f93/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08006HF128-d0c03b9f93/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08006HF128-d0c03b9f93/image_360.png", "thumb_360_w": 360, "thumb_360_h": 213, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08006HF128-d0c03b9f93/image_480.png", "thumb_480_w": 480, "thumb_480_h": 284, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08006HF128-d0c03b9f93/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08006HF128-d0c03b9f93/image_720.png", "thumb_720_w": 720, "thumb_720_h": 425, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08006HF128-d0c03b9f93/image_800.png", "thumb_800_w": 800, "thumb_800_h": 473, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08006HF128-d0c03b9f93/image_960.png", "thumb_960_w": 960, "thumb_960_h": 567, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08006HF128-d0c03b9f93/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 605, "original_w": 1886, "original_h": 1114, "thumb_tiny": "AwAcADDRxzTqKKAI/MO/bsbrjOOKfz6j8qga4CsRtPBoW4DOF2Hk4quVk3RYFBGRSAH1pakoKKKKAGn6mjj1NOpMYoAMgdzS0maM0Af/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08006HF128/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08006HF128-2a49897423", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "fAwYn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis doesn't have performance ratings option:"}]}]}]}, {"ts": "1730998989.407579", "text": "<https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "2HwXU", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1730994989.266839", "text": "Yes, looks like its their IT team. It happened  4pm IST.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1730994998.000000"}, "blocks": [{"type": "rich_text", "block_id": "acJ/3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, looks like its their IT team. It happened  4pm IST."}]}]}]}, {"ts": "1730994935.014219", "text": "I don’t know. I am assuming it was their IT team. I pinged <PERSON> directly about it.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "KQvvn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I don’t know. I am assuming it was their IT team. I pinged <PERSON> directly about it."}]}]}]}, {"ts": "1730994600.957989", "text": "<@U07EJ2LP44S> do we know why valgenesis removed themselves from the slack channel?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jTQOp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " do we know why valgenesis removed themselves from the slack channel?"}]}]}]}, {"ts": "1730994040.963299", "text": "*Update on budget Issues:* \nWe have the fixes ready. Couldn't deploy my evening as I had to go out for a consultation. I can deploy if thats okay to do now.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MZHZf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Update on budget Issues: ", "style": {"bold": true}}, {"type": "text", "text": "\nWe have the fixes ready. Couldn't deploy my evening as I had to go out for a consultation. I can deploy if thats okay to do now."}]}]}]}, {"ts": "1730992283.671069", "text": "Need to discuss Alayacare's IC mapping (can we turn it off) and subsquent chart changes for compa ratios", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1730992283.671069", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "B0LUr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Need to discuss Alayacare's IC mapping (can we turn it off) and subsquent chart changes for compa ratios"}]}]}]}, {"ts": "1730955122.229109", "text": "*<@U07M6QKHUC9>* *<@U07EJ2LP44S>* *<@U04DKEFP1K8>* \n*Agenda for today:*\n• Job Level\n• Bonus requirement clarifications, review and timeline\n• HRBP for Valgenesis if time permits\nFYI ... <@U07MH77PUBV>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1730961122.000000"}, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9", "U07MH77PUBV"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "RWjgG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9", "style": {"bold": true}}, {"type": "text", "text": " ", "style": {"bold": true}}, {"type": "user", "user_id": "U07EJ2LP44S", "style": {"bold": true}}, {"type": "text", "text": " ", "style": {"bold": true}}, {"type": "user", "user_id": "U04DKEFP1K8", "style": {"bold": true}}, {"type": "text", "text": " ", "style": {"bold": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Agenda for today:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Level"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Bonus requirement clarifications, review and timeline"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "HRBP for Valgenesis if time permits"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nFYI ... "}, {"type": "user", "user_id": "U07MH77PUBV"}]}]}]}, {"ts": "1730954507.492309", "text": "Unfortunately we missed our deadline due to the issues recently. We can definitely show a demo early next week.\n<@U07MH77PUBV> Pitch in if its not possibly by early next week.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730930775.621489", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1730955152.000000"}, "blocks": [{"type": "rich_text", "block_id": "K/Pqs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Unfortunately we missed our deadline due to the issues recently. We can definitely show a demo early next week.\n"}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " Pitch in if its not possibly by early next week."}]}]}]}, {"ts": "1730933068.966099", "text": "My understanding is that it was almost done, but let’s wait  for <PERSON><PERSON><PERSON> to confirm", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1730933081.000000"}, "blocks": [{"type": "rich_text", "block_id": "ugr3b", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "My understanding is that it was almost done, but let’s"}, {"type": "text", "text": " wait "}, {"type": "text", "text": " for <PERSON><PERSON><PERSON> to confirm"}]}]}]}, {"ts": "1730930775.621489", "text": "We initially promised <PERSON> we’d be done with the bonus build out by first of November. Can we be ready to show this to her on Monday?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1730930775.621489", "reply_count": 1, "files": [{"id": "F07V988UFNY", "created": 1730930745, "timestamp": 1730930745, "name": "IMG_4136.jpg", "title": "IMG_4136", "mimetype": "image/jpeg", "filetype": "jpg", "pretty_type": "JPEG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 65774, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07V988UFNY/img_4136.jpg", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07V988UFNY/download/img_4136.jpg", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07V988UFNY-f37cea7f24/img_4136_64.jpg", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07V988UFNY-f37cea7f24/img_4136_80.jpg", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07V988UFNY-f37cea7f24/img_4136_360.jpg", "thumb_360_w": 360, "thumb_360_h": 219, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07V988UFNY-f37cea7f24/img_4136_480.jpg", "thumb_480_w": 480, "thumb_480_h": 292, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07V988UFNY-f37cea7f24/img_4136_160.jpg", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07V988UFNY-f37cea7f24/img_4136_720.jpg", "thumb_720_w": 720, "thumb_720_h": 438, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07V988UFNY-f37cea7f24/img_4136_800.jpg", "thumb_800_w": 800, "thumb_800_h": 487, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07V988UFNY-f37cea7f24/img_4136_960.jpg", "thumb_960_w": 960, "thumb_960_h": 584, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07V988UFNY-f37cea7f24/img_4136_1024.jpg", "thumb_1024_w": 1024, "thumb_1024_h": 623, "original_w": 1179, "original_h": 717, "thumb_tiny": "AwAdADC20souggT5Pp+ualzJngr+RoL4JHHtzSeZ6AfnQAo8zIyVx9KcM55I/KmeZ9PzpfM/3fzoAec44oqPzOOg/OnKWOOBj60ANYHecZ/WjY3rz9TUneigBhVvX9TSbG9f1NSUUAR7G7H9TTlBHpinUUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07V988UFNY/img_4136.jpg", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07V988UFNY-5c80c5627b", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Z7IKO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We initially promised <PERSON> we’d be done with the bonus build out by first of November. Can we be ready to show this to her on Monday?"}]}]}]}, {"ts": "1730923385.369779", "text": "<@U0690EB5JE5> this is a question for you\n<https://compiify.atlassian.net/browse/COM-3970>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730923385.369779", "reply_count": 8, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14053::e05ed9a25b584df39f4589c483a74cd4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3970?atlOrigin=eyJpIjoiODlmZDg4NWUyOTllNGZlYmJmOGJhODg3ZmRiMTI0MTMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3970 Discrepancy in Job Titles and Job Levels>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14053::14edf576e15148b3a4c1af712f07ebe8", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14053\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3970\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3970", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "4+t2i", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " this is a question for you\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3970"}]}]}]}, {"ts": "1730922923.392689", "text": "<@U0690EB5JE5> enhancement <https://compiify.atlassian.net/browse/COM-3969>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730922923.392689", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14052::197962bbe5f540ef9a22db66028ff1b9", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3969?atlOrigin=eyJpIjoiNTBmZjExYzQzOGVjNDA4NmJiY2I2NTg5YjY3MGI5MGEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3969 Add Batch Select Feature in Organization View for Editing Employees>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14052::8fbc66b27c6c478793a35affc97b6c1c", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14052\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3969\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3969", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "LQxBG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " enhancement "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3969"}]}]}]}, {"ts": "1730922196.431249", "text": "<@U04DKEFP1K8> how would calculation of annualized salary would work if total working hours are different. For example. US has 40 hr work week but AU might have 37 hr work week. In that we have to have location based annualized salary calculation?\n\nI think's that the case with Tithely", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730921455.890249", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "OblNn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " how would calculation of annualized salary would work if total working hours are different. For example. US has 40 hr work week but AU might have 37 hr work week. In that we have to have location based annualized salary calculation?\n\nI think's that the case with Tithely"}]}]}]}, {"ts": "1730922062.620849", "text": "As in they have hourly but they are just annualizing salaries and not treating them any differently.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0hhAa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "As in they have hourly but they are just annualizing salaries and not treating them any differently."}]}]}]}, {"ts": "1730922027.360929", "text": "Neither of them are really doing anything with hourly", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VjCi6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Neither of them are really doing anything with hourly"}]}]}]}, {"ts": "1730921854.239209", "text": "<@U07EJ2LP44S> which one of our customer is using hourly employees? Diversifies and Curana?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "kp2Uf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " which one of our customer is using hourly employees? Diversifies and Curana?"}]}]}]}, {"ts": "1730921723.015729", "text": "<@U0690EB5JE5> Tithely will include the CEO in the merit review process. How do you want to handle it? add a fake employee for the root hierarchy? or we should think about improving the workflow to include CEOs in the merit process?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730921723.015729", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "Be5Kd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Tithely will include the CEO in the merit review process. How do you want to handle it? add a fake employee for the root hierarchy? or we should think about improving the workflow to include CEOs in the merit process?"}]}]}]}, {"ts": "1730921543.138619", "text": "hourly is different, but i don't think we've seen a real use case for hourly yet. mostly they just annualize an hourly rate", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1730921543.138619", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "/a3v+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "hourly is different, but i don't think we've seen a real use case for hourly yet. mostly they just annualize an hourly rate"}]}]}]}, {"ts": "1730921513.092009", "text": "PT is just the less than full time people, but they probably are still annualized", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "01QPd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "PT is just the less than full time people, but they probably are still annualized"}]}]}]}, {"ts": "1730921489.122799", "text": "from what I understand", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0B+FP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "from what I understand"}]}]}]}, {"ts": "1730921486.021069", "text": "PT and hourly are different", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "KyA68", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "PT and hourly are different"}]}]}]}, {"ts": "1730921455.890249", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> is the comp type for hourly employees supposed to be part-time or hourly in the app?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730921455.890249", "reply_count": 18, "blocks": [{"type": "rich_text", "block_id": "BfYvB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " is the comp type for hourly employees supposed to be part-time or hourly in the app?"}]}]}]}, {"ts": "1730918478.385809", "text": "<!here> cainwatters is making progress and have used 60% of their salary budget so far. No news from them is good news :tada:", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730918478.385809", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "U2Zda", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " cainwatters is making progress and have used 60% of their salary budget so far. No news from them is good news "}, {"type": "emoji", "name": "tada", "unicode": "1f389"}]}]}]}], "created_at": "2025-05-22T21:35:34.671172"}