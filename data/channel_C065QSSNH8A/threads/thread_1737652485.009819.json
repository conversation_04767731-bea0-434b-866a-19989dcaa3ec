{"thread_ts": "1737652485.009819", "channel_id": "C065QSSNH8A", "reply_count": 13, "replies": [{"ts": "1737652508.109569", "text": "ok, will take a look at this tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737652485.009819", "blocks": [{"type": "rich_text", "block_id": "cBclR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok, will take a look at this tomorrow."}]}]}]}, {"ts": "1737654543.437469", "text": "<@U07EJ2LP44S> can it wait until tomorrow?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737652485.009819", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "LBhKI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can it wait until tomorrow?"}]}]}]}, {"ts": "1737655246.404709", "text": "The sooner the better, but I don't think 12 hours or so is going to make or break.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737652485.009819", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "GAAMX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The sooner the better, but I don't think 12 hours or so is going to make or break."}]}]}]}, {"ts": "1737657757.281489", "text": "<@U0690EB5JE5> This is the list of employees that should be in the cycle.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737652485.009819", "files": [{"id": "F08A0P1VBD1", "created": 1737657753, "timestamp": 1737657753, "name": "2025 Reviews.xlsx", "title": "2025 Reviews.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 39915, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08A0P1VBD1/2025_reviews.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08A0P1VBD1/download/2025_reviews.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A0P1VBD1-c7399a9208/2025_reviews_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A0P1VBD1-c7399a9208/2025_reviews_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08A0P1VBD1/2025_reviews.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08A0P1VBD1-1f0e8e7b22", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "FNJFL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " This is the list of employees that should be in the cycle."}]}]}]}, {"ts": "1737657821.948709", "text": ":+1:", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737652485.009819", "blocks": [{"type": "rich_text", "block_id": "qF+xl", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "+1", "unicode": "1f44d"}]}]}]}, {"ts": "1737953411.708969", "text": "<@U07EJ2LP44S> Curana ENV is reset.\n• Pulled data from the dump shared above\n• And filtered employees who are eligible for cycle and also made sure to include managers even if they are not eligible to keep the hierarchy. Filtered the employee list from sheet above `2025 Revieiws.xlsx` . I observed around 4 employees ('6847', '3939', '4193', '3782') from the eligible list are missing or inactive in dump shared by customer.\n• Cycle is not created yet. Lets create once they confirm data looks good\n• Uploaded bands as well\nPlease review and let me know if any issues.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737652485.009819", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1737953549.000000"}, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "0cGOl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Curana ENV is reset.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Pulled data from the dump shared above"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "And filtered employees who are eligible for cycle and also made sure to include managers even if they are not eligible to keep the hierarchy. Filtered the employee list from sheet above "}, {"type": "text", "text": "2025 Revieiws.xlsx", "style": {"code": true}}, {"type": "text", "text": " . I observed around 4 employees ('6847', '3939', '4193', '3782') from the eligible list are missing or inactive in dump shared by customer."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle is not created yet. Lets create once they confirm data looks good"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Uploaded bands as well"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nPlease review and let me know if any issues."}]}]}]}, {"ts": "1737990229.285319", "text": "I don't suppose you saved the old cycle settings, did you? They will want to look at it in the cycle view, at least their leader will.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737652485.009819", "blocks": [{"type": "rich_text", "block_id": "8u7RF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I don't suppose you saved the old cycle settings, did you? They will want to look at it in the cycle view, at least their leader will."}]}]}]}, {"ts": "1737991405.382309", "text": "<@U0690EB5JE5> Did my comment come through? I dont' see it in my phone slack, only on my laptop.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737652485.009819", "blocks": [{"type": "rich_text", "block_id": "zzYIt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Did my comment come through? I dont' see it in my phone slack, only on my laptop."}]}]}]}, {"ts": "1737991461.210719", "text": "Yes I have the old data with me", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737652485.009819", "blocks": [{"type": "rich_text", "block_id": "VaH51", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes I have the old data with me"}]}]}]}, {"ts": "1737991474.729959", "text": "Do you want me to create the cycle?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737652485.009819", "blocks": [{"type": "rich_text", "block_id": "aojcS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you want me to create the cycle"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1737991536.199029", "text": "I will create cycle like 30mnts from now and update here", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737652485.009819", "blocks": [{"type": "rich_text", "block_id": "t7swf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will create cycle like 30mnts from now and update here"}]}]}]}, {"ts": "1737991816.911299", "text": "Yes that would be great!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737652485.009819", "blocks": [{"type": "rich_text", "block_id": "BMhZZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes that would be great!"}]}]}]}, {"ts": "1737994641.732529", "text": "<@U07EJ2LP44S> cycle is created.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737652485.009819", "blocks": [{"type": "rich_text", "block_id": "Q3c+g", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " cycle is created."}]}]}]}], "created_at": "2025-05-22T22:00:49.349636"}