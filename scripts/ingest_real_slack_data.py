#!/usr/bin/env python
"""
Script to ingest real Slack data using the LlamaIndex-based ingestion service.
"""

import os
import sys
import django
import logging
from datetime import datetime

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "multi_source_rag.config.settings.local")
django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk
from apps.documents.services.llama_ingestion_service_unified import UnifiedLlamaIndexIngestionService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def print_stats():
    """Print current database statistics."""
    print("\n=== Database Statistics ===")
    print(f"Raw Documents: {RawDocument.objects.count()}")
    print(f"Document Chunks: {DocumentChunk.objects.count()}")
    print(f"Document Sources: {DocumentSource.objects.count()}")
    print(f"Tenants: {Tenant.objects.count()}")


def create_document_source(tenant):
    """Create a document source for local Slack data."""
    # Configuration for the LocalSlackSourceInterface
    config = {
        "data_dir": "data/",  # Path to directory containing channel_C065QSSNH8A
        "time_period": "monthly",  # Group messages by month for better context
        "enable_semantic_cross_refs": True,  # Enable semantic cross-references
        "quality_threshold": 0.3,  # Only include documents with quality score >= 0.3
        "max_documents_per_channel": 500,  # Limit documents per channel
        "include_threads": True,  # Include thread conversations
        "filter_bots": True,  # Filter out bot messages
        "summarize_documents": False,  # Don't summarize for faster processing
    }
    
    source = DocumentSource.objects.create(
        tenant=tenant,
        name="1-productengineering Slack Channel",
        source_type="local_slack",
        config=config,
        is_active=True,
        description="Real Slack data from 1-productengineering channel (C065QSSNH8A)"
    )
    
    logger.info(f"Created document source: {source.name}")
    return source


def main():
    """Main function to ingest real Slack data."""
    logger.info("🚀 Starting real Slack data ingestion with LlamaIndex...")
    
    # Print initial stats
    print_stats()
    
    try:
        # Get or create test tenant
        tenant, created = Tenant.objects.get_or_create(
            slug="test-tenant",
            defaults={"name": "Test Tenant"}
        )
        
        if created:
            logger.info(f"Created new tenant: {tenant.name}")
        else:
            logger.info(f"Using existing tenant: {tenant.name}")
        
        # Get or create test user
        user, created = User.objects.get_or_create(
            username="test-user",
            defaults={"email": "<EMAIL>"}
        )
        
        if created:
            logger.info(f"Created new user: {user.username}")
        else:
            logger.info(f"Using existing user: {user.username}")
        
        # Create document source
        source = create_document_source(tenant)
        
        # Create ingestion service
        logger.info("🔧 Initializing LlamaIndex ingestion service...")
        ingestion_service = UnifiedLlamaIndexIngestionService(tenant, user)
        
        # Process the source
        logger.info("📥 Starting data ingestion...")
        start_time = datetime.now()
        
        processed, failed = ingestion_service.process_source(
            source=source,
            batch_size=50,  # Process in smaller batches
            limit=100  # Limit to first 100 documents for testing
        )
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # Print results
        logger.info("✅ Ingestion completed!")
        print(f"\n=== Ingestion Results ===")
        print(f"Processed: {processed} documents")
        print(f"Failed: {failed} documents")
        print(f"Processing time: {processing_time:.2f} seconds")
        print(f"Average time per document: {processing_time/max(processed, 1):.2f} seconds")
        
        # Print final stats
        print_stats()
        
        # Get ingestion stats
        stats = ingestion_service.get_stats()
        print(f"\n=== Ingestion Service Stats ===")
        for key, value in stats.items():
            print(f"{key}: {value}")
        
    except Exception as e:
        logger.error(f"❌ Error during ingestion: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
