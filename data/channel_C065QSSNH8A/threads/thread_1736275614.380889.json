{"thread_ts": "1736275614.380889", "channel_id": "C065QSSNH8A", "reply_count": 17, "replies": [{"ts": "1736275648.939929", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1736275817.982239", "text": "getting an error", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "qjOvA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "getting an error"}]}]}]}, {"ts": "1736275930.077009", "text": "same.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "p2vOw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "same."}]}]}]}, {"ts": "1736275985.960789", "text": "Let me try them one by one", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "VX0wI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me try them one by one"}]}]}]}, {"ts": "1736276083.176749", "text": "Nope that didn't work either", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "GdrPD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Nope that didn't work either"}]}]}]}, {"ts": "1736276135.377909", "text": "<@U0690EB5JE5> can you try this when you get in? I am trying to add the root user to Diversified. I added a new line for that user, and changed the CEO to report to them, but it's not accepting the file. I've tried a few variations but I just keep getting an internal error.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "N/xPO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you try this when you get in? I am trying to add the root user to Diversified. I added a new line for that user, and changed the CEO to report to them, but it's not accepting the file. I've tried a few variations but I just keep getting an internal error."}]}]}]}, {"ts": "1736276157.799569", "text": "I think I can fix it. give me some time, It;s a data issue", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "SluEd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think I can fix it. give me some time, It;s a data issue"}]}]}]}, {"ts": "1736276192.916639", "text": "Ok. Here's the source file", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736275614.380889", "files": [{"id": "F087N7R4M0S", "created": 1736276189, "timestamp": 1736276189, "name": "DGOCRootUser.xls", "title": "DGOCRootUser.xls", "mimetype": "application/vnd.ms-excel", "filetype": "xls", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 437760, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F087N7R4M0S/dgocrootuser.xls", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F087N7R4M0S/download/dgocrootuser.xls", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F087N7R4M0S-e57488d221/dgocrootuser_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F087N7R4M0S-e57488d221/dgocrootuser_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F087N7R4M0S/dgocrootuser.xls", "permalink_public": "https://slack-files.com/T04DM97F1UM-F087N7R4M0S-ec556db814", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "3ZofS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. Here's the source file"}]}]}]}, {"ts": "1736288423.740589", "text": "Still getting an error", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "klApx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Still getting an error"}]}]}]}, {"ts": "1736300369.039219", "text": "<@U0690EB5JE5> FYA", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "lWfKo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " FYA"}]}]}]}, {"ts": "1736300441.321919", "text": "it shows no errors during the review changes workflow but when I click Yes on Apply changes, it shows errors", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "aYue2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "it shows no errors during the review changes workflow but when I click Yes on Apply changes, it shows errors"}]}]}]}, {"ts": "1736300541.378309", "text": "Will take a look today ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "LVivE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take a look today "}]}]}]}, {"ts": "1736316265.750819", "text": "This is an edge case which will be fixed in upcoming release. Add first root employee is not happening in the sequence it is supposed to be. We need to add the employee first and then change the manager later.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "gVubn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is an edge case which will be fixed in upcoming release. Add first root employee is not happening in the sequence it is supposed to be. We need to add the employee first and then change the manager later."}]}]}]}, {"ts": "1736347636.131609", "text": "<@U07EJ2LP44S> Please add root employee first then change manager from Org View edit.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "Wf6qC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please add root employee first then change manager from Org View edit."}]}]}]}, {"ts": "1736347658.723259", "text": "if you still face issue. We can debug this in the call.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "b/6Es", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "if you still face issue. We can debug this in the call."}]}]}]}, {"ts": "1736347755.745689", "text": "Are you saying add the root employee but make them report to someone else? Or try to add a second root employee", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "B/d57", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Are you saying add the root employee but make them report to someone else? Or try to add a second root employee"}]}]}]}, {"ts": "1736347814.265259", "text": "add root employee reporting to `C` post that update doing together is causing some conflict.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736275614.380889", "blocks": [{"type": "rich_text", "block_id": "20+/f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "add root employee reporting to "}, {"type": "text", "text": "C", "style": {"code": true}}, {"type": "text", "text": " post that update doing together is causing some conflict."}]}]}]}], "created_at": "2025-05-22T22:00:49.336275"}