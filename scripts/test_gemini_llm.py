#!/usr/bin/env python
"""
Test script to verify Gemini LLM configuration and functionality.

This script tests:
1. Gemini LLM initialization
2. Gemini embedding initialization
3. Basic LLM functionality
4. Fallback to Ollama if Gemini fails
"""

import os
import sys
import django
import logging

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv('/Users/<USER>/Desktop/RAGSearch/multi_source_rag/.env')

django.setup()

from django.conf import settings
from apps.core.utils.gemini_llm import (
    get_gemini_llm,
    get_gemini_embedding,
    is_gemini_available,
    get_llm_status
)
from apps.core.utils.llama_index_llm import get_llm, initialize_llms
from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content, initialize_embedding_models

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_gemini_availability():
    """Test if Gemini is properly configured and available."""
    print("🔍 Testing Gemini availability...")

    # Check configuration
    api_key = getattr(settings, 'GEMINI_API_KEY', '')
    model = getattr(settings, 'GEMINI_MODEL', '')
    embedding_model = getattr(settings, 'GEMINI_EMBEDDING_MODEL', '')

    print(f"  API Key configured: {'✅' if api_key else '❌'}")
    print(f"  Model: {model}")
    print(f"  Embedding Model: {embedding_model}")

    # Check if Gemini is available
    available = is_gemini_available()
    print(f"  Gemini Available: {'✅' if available else '❌'}")

    return available


def test_llm_status():
    """Test LLM status reporting."""
    print("\n📊 Testing LLM status...")

    status = get_llm_status()

    print("  Gemini Status:")
    gemini_status = status.get('gemini', {})
    print(f"    Available: {'✅' if gemini_status.get('available') else '❌'}")
    print(f"    API Key: {'✅' if gemini_status.get('api_key_configured') else '❌'}")
    print(f"    Model: {gemini_status.get('model')}")
    print(f"    Embedding: {gemini_status.get('embedding_model')}")

    print("  Ollama Status:")
    ollama_status = status.get('ollama', {})
    print(f"    Available: {'✅' if ollama_status.get('available') else '❌'}")
    print(f"    Host: {ollama_status.get('host')}")
    print(f"    Model: {ollama_status.get('model')}")


def test_llm_initialization():
    """Test LLM initialization."""
    print("\n🚀 Testing LLM initialization...")

    try:
        initialize_llms()
        print("  ✅ LLM initialization completed")
    except Exception as e:
        print(f"  ❌ LLM initialization failed: {e}")
        return False

    return True


def test_embedding_initialization():
    """Test embedding model initialization."""
    print("\n🔗 Testing embedding initialization...")

    try:
        initialize_embedding_models()
        print("  ✅ Embedding initialization completed")
    except Exception as e:
        print(f"  ❌ Embedding initialization failed: {e}")
        return False

    return True


def test_llm_functionality():
    """Test basic LLM functionality."""
    print("\n💬 Testing LLM functionality...")

    try:
        # Get LLM instance
        llm = get_llm()
        print(f"  ✅ Got LLM instance: {type(llm).__name__}")

        # Test simple completion
        test_prompt = "What is the capital of France? Answer in one word."
        response = llm.complete(test_prompt)
        print(f"  ✅ LLM response: {response.text.strip()}")

        return True

    except Exception as e:
        print(f"  ❌ LLM functionality test failed: {e}")
        return False


def test_embedding_functionality():
    """Test basic embedding functionality."""
    print("\n🔍 Testing embedding functionality...")

    try:
        # Get embedding model
        embedding_model = get_embedding_model_for_content()
        print(f"  ✅ Got embedding model: {type(embedding_model).__name__}")

        # Test embedding generation
        test_text = "This is a test sentence for embedding."
        embeddings = embedding_model.get_text_embedding(test_text)
        print(f"  ✅ Generated embedding with dimension: {len(embeddings)}")

        return True

    except Exception as e:
        print(f"  ❌ Embedding functionality test failed: {e}")
        return False


def test_gemini_specific():
    """Test Gemini-specific functionality."""
    print("\n🤖 Testing Gemini-specific functionality...")

    if not is_gemini_available():
        print("  ⚠️  Gemini not available, skipping specific tests")
        return True

    try:
        # Test Gemini LLM directly
        gemini_llm = get_gemini_llm()
        print(f"  ✅ Got Gemini LLM: {type(gemini_llm).__name__}")

        # Test Gemini embedding directly
        gemini_embedding = get_gemini_embedding()
        print(f"  ✅ Got Gemini embedding: {type(gemini_embedding).__name__}")

        return True

    except Exception as e:
        print(f"  ❌ Gemini-specific test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Testing Gemini LLM Configuration\n")

    tests = [
        test_gemini_availability,
        test_llm_status,
        test_llm_initialization,
        test_embedding_initialization,
        test_llm_functionality,
        test_embedding_functionality,
        test_gemini_specific,
    ]

    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"  ❌ Test {test.__name__} crashed: {e}")
            results.append(False)

    # Summary
    passed = sum(1 for r in results if r)
    total = len(results)

    print(f"\n📋 Test Summary: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Gemini LLM is properly configured.")
    else:
        print("⚠️  Some tests failed. Check the configuration and logs.")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
