{"date": "2024-07-23", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1721759176.685149", "text": "<!here> Instead of the the leadership meeting, I have set up a call with <PERSON><PERSON> to go over the audit log and merit view organization", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hqg3L", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Instead of the the leadership meeting, I have set up a call with <PERSON><PERSON> to go over the audit log and merit view organization"}]}]}]}, {"ts": "1721756388.240129", "text": "Action items from July 23 eng call\n<@U04DKEFP1K8>\nComplete testing of org insights and sorting\nTest equity\nFinish the customer implementation roadmap spreadsheet\nRetest merit view task page", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "GUgP0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Action items from July 23 eng call\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "\nComplete testing of org insights and sorting\nTest equity\nFinish the customer implementation roadmap spreadsheet\nRetest merit view task page"}]}]}]}, {"ts": "1721697277.285349", "text": "<!here>\n\n• Get nauto env up and running including <PERSON><PERSON><PERSON>'s login - :white_check_mark:\n• Schedule meeting with <PERSON><PERSON> - :white_check_mark:\n• Test sorting functionality - In progress\n• Revalidate fixed bugs - :white_check_mark: ( Few equity issues are still unaddressed , will discuss these with <@U0690EB5JE5>)\n• Test Vercara's environment for bonus-related issues - Bonus details are still pending to be implemented in Org view, will proceed once this work completes\n• Test org insights - *In progress ( 60% complete , issues are reported in Nauto testing document)*\n• Provide feedback on compensation cycle builder by Wednesday\n• Send message to vercara for OT employee sample data - :white_check_mark:", "user": "U04DKEFP1K8", "type": "message", "edited": {"user": "U04DKEFP1K8", "ts": "1721697417.000000"}, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8omdX", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": "\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Get nauto env up and running including <PERSON><PERSON><PERSON>'s login - "}, {"type": "emoji", "name": "white_check_mark", "unicode": "2705"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Schedule meeting with <PERSON><PERSON> - "}, {"type": "emoji", "name": "white_check_mark", "unicode": "2705"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Test sorting functionality - In progress"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Revalidate fixed bugs - "}, {"type": "emoji", "name": "white_check_mark", "unicode": "2705"}, {"type": "text", "text": " ( Few equity issues are still unaddressed , will discuss these with "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Test Vercara's environment for bonus-related issues - Bonus details are still pending to be implemented in Org view, will proceed once this work completes"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Test org insights - "}, {"type": "text", "text": "In progress ( 60% complete , issues are reported in Nauto testing document)", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Provide feedback on compensation cycle builder by Wednesday"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Send message to vercara for OT employee sample data - "}, {"type": "emoji", "name": "white_check_mark", "unicode": "2705"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "**********.245649", "text": "<@U04DKEFP1K8> are we ready to hand over the test or prod account to valgenesis?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.245649", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "9lRD1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " are we ready to hand over the test or prod account to valgenesis?"}]}]}]}], "created_at": "2025-05-22T21:35:34.617439"}