{"thread_ts": "1735839765.813049", "channel_id": "C065QSSNH8A", "reply_count": 5, "replies": [{"ts": "1735855720.080019", "text": "expected behavior- higher level manager can not review approve until the lower level mangers have submitted for approval.\n\nIn this case, the root hierarchy (CEO) is able to submit for final approval probably because of either of the reasons below:\n\n1. CEO is a root employee. \n2. admin impersonating as root employee or any other planning manager can review and approve even though lower level managers have not submitted. It is expected behavior.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1735839765.813049", "blocks": [{"type": "rich_text", "block_id": "3Y//J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "expected behavior- higher level manager can not review approve until the lower level mangers have submitted for approval.\n\nIn this case, the root hierarchy (CEO) is able to submit for final approval probably because of either of the reasons below:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "CEO is a root employee. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "admin impersonating as root employee or any other planning manager can review and approve even though lower level managers have not submitted. It is expected behavior."}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1735860655.926059", "text": "Just so I’m clear, you are saying that the root employee is allowed to do whatever they want essentially. There are no restrictions on them submitting for final approval.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1735839765.813049", "blocks": [{"type": "rich_text", "block_id": "2o3yr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just so I’m clear, you are saying that the root employee is allowed to do whatever they want essentially. There are no restrictions on them submitting for final approval."}]}]}]}, {"ts": "1735864005.340849", "text": "I believe they have same rights as an admin", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1735839765.813049", "blocks": [{"type": "rich_text", "block_id": "cWd6a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I believe they have same rights as an admin"}]}]}]}, {"ts": "1735864763.970929", "text": "This could also be the same impersonation issue that we have discussed this multiple times before.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1735839765.813049", "blocks": [{"type": "rich_text", "block_id": "024KK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This could also be the same impersonation issue that we have discussed this multiple times before."}]}]}]}, {"ts": "1735884933.573009", "text": "<@U07EJ2LP44S> higher up in hierarchy can override reporting recommenders. Please raise a if this is not working for any manager. We will take a look.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1735839765.813049", "blocks": [{"type": "rich_text", "block_id": "RM5Ya", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " higher up in hierarchy can override reporting recommenders. Please raise a if this is not working for any manager. We will take a look."}]}]}]}], "created_at": "2025-05-22T22:00:49.337211"}