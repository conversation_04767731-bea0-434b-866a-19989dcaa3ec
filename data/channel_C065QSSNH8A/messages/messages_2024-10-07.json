{"date": "2024-10-07", "channel_id": "C065QSSNH8A", "message_count": 16, "messages": [{"ts": "1728324233.292099", "text": "Back online", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IJV4Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Back online"}]}]}]}, {"ts": "1728321721.125059", "text": "<@U04DKEFP1K8> are you still able to join the 10:30 call? or should I move it?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1728321721.125059", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "wS93x", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " are you still able to join the 10:30 call? or should I move it?"}]}]}]}, {"ts": "1728317897.603399", "text": "<@U07EJ2LP44S> can you pls move the <PERSON>'s recording under Nauto&gt;Testimonials", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1728059339.460349", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "SpHUs", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can you pls move the <PERSON>'s recording under Na<PERSON>>Testimonials"}]}]}]}, {"ts": "1728316891.976869", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1728316888.675789", "text": "Nothing specific either; I will cancel", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Utc5w", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Nothing specific either; I will cancel"}]}]}]}, {"ts": "1728316871.889869", "text": "I don’t have any specific agenda for today ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qxIh+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " have any specific agenda for today "}]}]}]}, {"ts": "1728316836.427699", "text": "Are their any specific agenda items we need to discuss? <@U07EJ2LP44S> <@U0690EB5JE5>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MnjWD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Are their any specific agenda items we need to discuss? "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}]}]}]}, {"ts": "1728316795.431679", "text": "i just walked in the door but could use the time if we don't have to meet", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yv9F6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i just walked in the door but could use the time if we don't have to meet"}]}]}]}, {"ts": "1728316779.443509", "text": "ah, let's cancel the meeting today if both <PERSON><PERSON><PERSON><PERSON> and <PERSON> can't make it", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XASTQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ah, let's cancel the meeting today if both <PERSON><PERSON><PERSON><PERSON> and <PERSON> can't make it"}]}]}]}, {"ts": "1728315229.290039", "text": "<@U04DKEFP1K8> Alaycare ENV. I  have the fix for issues reported but need to test. I will test cycle creation tomorrow and sanity test. Please hold testing the ENV for a day.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VrxmG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Alaycare ENV. I  have the fix for issues reported but need to test. I will test cycle creation tomorrow and sanity test. Please hold testing the ENV for a day."}]}]}]}, {"ts": "1728315127.641939", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> Issues fixed today\n<https://compiify.atlassian.net/browse/COM-3677>\n<https://compiify.atlassian.net/browse/COM-3676>,\n<https://compiify.atlassian.net/browse/COM-3715>,\n<https://compiify.atlassian.net/browse/COM-3713>,\n<https://compiify.atlassian.net/browse/COM-3712>,\n<https://compiify.atlassian.net/browse/COM-3711>,\n<https://compiify.atlassian.net/browse/COM-3694>,\n<https://compiify.atlassian.net/browse/COM-3656>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EsVmb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Issues fixed today\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3677"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3676"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3715"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3713"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3712"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3711"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3694"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3656"}]}]}]}, {"ts": "1728314288.173919", "text": "I’m actually at urgent care with my daughter", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "o9+Tp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I’m actually at urgent care with my daughter"}]}]}]}, {"ts": "1728314090.567589", "text": "I have to take my son to doctor appointment at 9am today. ", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zBsxt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have to take my son to doctor appointment at 9am today. "}]}]}]}, {"ts": "1728266379.431839", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> This will be considerable effort. Can we take up after Nov? We can rename the column appropriately for now\n<https://compiify.atlassian.net/browse/COM-3695>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1728266379.431839", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13756::ec628c867e3e45758ef8ee02dbce9446", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3695?atlOrigin=eyJpIjoiMzNiZTA5MjJlOTdmNGU2ZDgwOTI3YzhkNGM2NjJhNDIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3695 Separate One-Time Bonus from Salary in Merit and Promotion Cycle>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13756::a87e3aa2c4864d0f85bfc803d9260781", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13756\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3695\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3695", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "S3nul", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This will be considerable effort. Can we take up after Nov? We can rename the column appropriately for now\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3695"}]}]}]}, {"ts": "1728265752.055929", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> <@U07M6QKHUC9> As <PERSON><PERSON> gives the ability to create tickets. I request to make sure that We are not creating tickets unrelated to product-eng work :slightly_smiling_face:  or link them to an epic that can be ignored or filtered easily later. I am sure such tickets we won't be able to diligently close due to the parallel threads we deal with and they leave the backlog spammed if not managed correctly.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1728265752.055929", "reply_count": 5, "edited": {"user": "U0690EB5JE5", "ts": "1728269056.000000"}, "blocks": [{"type": "rich_text", "block_id": "yUK39", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " As <PERSON><PERSON> gives the ability to create tickets. I request to make sure that We are not creating tickets unrelated to product-eng work "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": "  or link them to an epic that can be ignored or filtered easily later. I am sure such tickets we won't be able to diligently close due to the parallel threads we deal with and they leave the backlog spammed if not managed correctly."}]}]}]}, {"ts": "1728265566.514359", "text": "<@U04DKEFP1K8> Do we need to implement this feature for Nov cycle?\n<https://compiify.atlassian.net/browse/COM-3520>", "user": "U0690EB5JE5", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13581::022fcd060422441e9d2fd22a6554a40a", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3520?atlOrigin=eyJpIjoiYmE0Yjc0MWExYjI1NGFiYzk5Yjc4OTRlMTBiMzAyODYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3520 Support Recommendations by Perf rating with PIB>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13581::361681261626432f84bc0a0747174f0b", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/082e10750208b91e38660f50da682c58?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13581\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3520\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3520", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "pyQpU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Do we need to implement this feature for Nov cycle?\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3520"}]}]}]}], "created_at": "2025-05-22T21:35:34.650717"}