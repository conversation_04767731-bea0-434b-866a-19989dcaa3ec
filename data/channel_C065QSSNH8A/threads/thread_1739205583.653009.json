{"thread_ts": "1739205583.653009", "channel_id": "C065QSSNH8A", "reply_count": 20, "replies": [{"ts": "1739205620.246709", "text": "Changes I made: delete number from earned amount $ and update earned %.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "4bVOr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Changes I made: delete number from earned amount $ and update earned %."}]}]}]}, {"ts": "1739206096.440769", "text": "<@U07EJ2LP44S> Can  just tell me which column thats to updated?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "c65dF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Can  just tell me which column thats to updated?"}]}]}]}, {"ts": "1739206432.171979", "text": "BD BE", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "11zrR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "BD BE"}]}]}]}, {"ts": "1739206448.614129", "text": "<@U07EJ2LP44S> Done. Uploaded", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739205583.653009", "files": [{"id": "F08CED8K6TG", "created": 1739206446, "timestamp": 1739206446, "name": "Diversified_CornerstoneUpdate.csv", "title": "Diversified_CornerstoneUpdate.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": true, "size": 6981, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08CED8K6TG/diversified_cornerstoneupdate.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08CED8K6TG/download/diversified_cornerstoneupdate.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08CED8K6TG/diversified_cornerstoneupdate.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08CED8K6TG-4f13615555", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08CED8K6TG/diversified_cornerstoneupdate.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,Earned Individual Performance (%)\r\nU,30882,0\r\nU,30905,0\r\nU,30904,0\r\nU,30903,0\r\nU,30906,0\r\nU,30897,0\r\nU,30885,0\r\nU,30886,0\r\nU,30876,0\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">Earned Individual Performance (%)</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30882</div><div class=\"cm-col cm-num\">0</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30905</div><div class=\"cm-col cm-num\">0</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30904</div><div class=\"cm-col cm-num\">0</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30903</div><div class=\"cm-col cm-num\">0</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30906</div><div class=\"cm-col cm-num\">0</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30897</div><div class=\"cm-col cm-num\">0</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30885</div><div class=\"cm-col cm-num\">0</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30886</div><div class=\"cm-col cm-num\">0</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30876</div><div class=\"cm-col cm-num\">0</div></div></div>\n</div>\n", "lines": 548, "lines_more": 538, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "8YM3M", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Done. Uploaded"}]}]}]}, {"ts": "1739206640.675799", "text": "Thank you looks great", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "aPq6+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you looks great"}]}]}]}, {"ts": "1739206662.443789", "text": "I think they are 99% ready to go tomorrow.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "71uJw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think they are 99% ready to go tomorrow."}]}]}]}, {"ts": "1739206686.514769", "text": "cool. Whats 1% remaining?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "pCEjQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "cool. Whats 1% remaining?"}]}]}]}, {"ts": "1739206727.492329", "text": "There may be a few manual changes to employees performance attainment (what you just uploaded) and I'll make those in the org view edit", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "ugoZ8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There may be a few manual changes to employees performance attainment (what you just uploaded) and I'll make those in the org view edit"}]}]}]}, {"ts": "1739206828.011999", "text": "ah okay :+1:", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "pSL41", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ah okay "}, {"type": "emoji", "name": "+1", "unicode": "1f44d"}]}]}]}, {"ts": "1739207495.427319", "text": "I'm checkign and it looks like the people I updated to 0% did not stick", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "XKcIE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'm checkign and it looks like the people I updated to 0% did not stick"}]}]}]}, {"ts": "1739207500.423839", "text": "their old number is still there", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "fNkkB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "their old number is still there"}]}]}]}, {"ts": "1739207607.619059", "text": "Oh ok let me check ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "tuz7l", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Oh ok let me check "}]}]}]}, {"ts": "1739207755.297399", "text": "<@U07EJ2LP44S> can you share an example to confirm?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "RdmqJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can you share an example to confirm?"}]}]}]}, {"ts": "1739207942.536809", "text": "wait it looks fine now", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "0uQOn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "wait it looks fine now"}]}]}]}, {"ts": "1739207955.970369", "text": "I just uploaded again for zeros", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "rZvmR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I just uploaded again for zeros"}]}]}]}, {"ts": "1739207963.279419", "text": "", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739205583.653009", "files": [{"id": "F08CM4R4NRH", "created": 1739207961, "timestamp": 1739207961, "name": "Diversified_CornerstoneUpdate_Zeros.csv", "title": "Diversified_CornerstoneUpdate_Zeros.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": true, "size": 2344, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08CM4R4NRH/diversified_cornerstoneupdate_zeros.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08CM4R4NRH/download/diversified_cornerstoneupdate_zeros.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08CM4R4NRH/diversified_cornerstoneupdate_zeros.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08CM4R4NRH-2d7d2f8759", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08CM4R4NRH/diversified_cornerstoneupdate_zeros.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Earned Individual Performance (%),Earned Individual Performance Amount,Target Bonus Currency\r\nU,30882,<PERSON>,<PERSON>,<PERSON>,0,0,USD\r\nU,30905,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,0,0,USD\r\nU,30904,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,0,0,USD\r\nU,30903,<PERSON>,<PERSON>,<PERSON>,0,0,USD\r\nU,30906,<PERSON>,<PERSON>,<PERSON>,0,0,USD\r\nU,30897,<PERSON>,<PERSON>,<PERSON>,0,0,USD\r\nU,30885,<PERSON>,<PERSON>,<PERSON>,0,0,USD\r\nU,30886,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,0,0,USD\r\nU,30876,<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,0,0,<PERSON>\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Target Bonus Currency</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30882</div><div class=\"cm-col\">Sabrina</div><div class=\"cm-col\">Smith</div><div class=\"cm-col\"><PERSON></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30905</div><div class=\"cm-col\">Caren</div><div class=\"cm-col\">Haga</div><div class=\"cm-col\">Caren Haga</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30904</div><div class=\"cm-col\">Carlina</div><div class=\"cm-col\">Favors</div><div class=\"cm-col\">Carlina Favors</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30903</div><div class=\"cm-col\">Kimberly</div><div class=\"cm-col\">Chaney</div><div class=\"cm-col\">Kimberly Chaney</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30906</div><div class=\"cm-col\">Ryan</div><div class=\"cm-col\">Jones</div><div class=\"cm-col\">Ryan Jones</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30897</div><div class=\"cm-col\">David</div><div class=\"cm-col\">Lewis</div><div class=\"cm-col\">David Lewis</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30885</div><div class=\"cm-col\">Hugh</div><div class=\"cm-col\">Drake</div><div class=\"cm-col\">Hugh Drake</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30886</div><div class=\"cm-col\">Derek</div><div class=\"cm-col\">Pringle</div><div class=\"cm-col\">Derek Pringle</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30876</div><div class=\"cm-col\">Hawley</div><div class=\"cm-col\">Gary</div><div class=\"cm-col\">Hawley Gary</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div></div></div>\n</div>\n", "lines": 48, "lines_more": 38, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}]}, {"ts": "1739207977.790149", "text": "Oh ok - yea it was essentially all the top people", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "YrcHA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Oh ok - yea it was essentially all the top people"}]}]}]}, {"ts": "1739207984.943059", "text": "M8", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "8e<PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "M8"}]}]}]}, {"ts": "1739207987.085949", "text": "looks good now", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "LDkGv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "looks good now"}]}]}]}, {"ts": "1739207994.080739", "text": ":+1:", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739205583.653009", "blocks": [{"type": "rich_text", "block_id": "qF+xl", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "+1", "unicode": "1f44d"}]}]}]}], "created_at": "2025-05-22T22:00:49.341386"}