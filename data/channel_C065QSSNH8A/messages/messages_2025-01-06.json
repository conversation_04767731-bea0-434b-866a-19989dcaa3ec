{"date": "2025-01-06", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1736186637.517919", "text": "Valgenesis letters: <https://compiify.atlassian.net/browse/COM-4046>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736186637.517919", "reply_count": 1, "reactions": [{"name": "eyes", "users": ["U07M6QKHUC9"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14129::b1860475cbff45ea9ea66c59a2bd9f76", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4046?atlOrigin=eyJpIjoiMmRjZmVhNzgwNzYwNDBmM2I4YmVjN2RjYzBhNjg2NTEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4046 Adjustment Letters>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14129::dae011615da64454a174c12b04f2198c", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14129\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4046\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4046", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "A6xQo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis letters: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4046"}]}]}]}, {"ts": "1736179258.405619", "text": "Kk cool", "user": "U07NBMXTL1E", "type": "message", "blocks": [{"type": "rich_text", "block_id": "bDz3T", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Kk cool"}]}]}]}, {"ts": "1736179243.997379", "text": "<PERSON> and <PERSON>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Kw8Sy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> and <PERSON>"}]}]}]}, {"ts": "1736178599.002939", "text": "Who do we want to be on these precycle analytics calls?", "user": "U07NBMXTL1E", "type": "message", "files": [{"id": "F087TTYB4FK", "created": 1736178547, "timestamp": 1736178547, "name": "Screenshot 2025-01-06 at 4.48.47 PM.png", "title": "Screenshot 2025-01-06 at 4.48.47 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07NBMXTL1E", "user_team": "T04DM97F1UM", "editable": false, "size": 37881, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F087TTYB4FK/screenshot_2025-01-06_at_4.48.47_pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F087TTYB4FK/download/screenshot_2025-01-06_at_4.48.47_pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F087TTYB4FK-a2f5a163b5/screenshot_2025-01-06_at_4.48.47_pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F087TTYB4FK-a2f5a163b5/screenshot_2025-01-06_at_4.48.47_pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F087TTYB4FK-a2f5a163b5/screenshot_2025-01-06_at_4.48.47_pm_360.png", "thumb_360_w": 360, "thumb_360_h": 122, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F087TTYB4FK-a2f5a163b5/screenshot_2025-01-06_at_4.48.47_pm_480.png", "thumb_480_w": 480, "thumb_480_h": 163, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F087TTYB4FK-a2f5a163b5/screenshot_2025-01-06_at_4.48.47_pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F087TTYB4FK-a2f5a163b5/screenshot_2025-01-06_at_4.48.47_pm_720.png", "thumb_720_w": 720, "thumb_720_h": 244, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F087TTYB4FK-a2f5a163b5/screenshot_2025-01-06_at_4.48.47_pm_800.png", "thumb_800_w": 800, "thumb_800_h": 271, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F087TTYB4FK-a2f5a163b5/screenshot_2025-01-06_at_4.48.47_pm_960.png", "thumb_960_w": 960, "thumb_960_h": 326, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F087TTYB4FK-a2f5a163b5/screenshot_2025-01-06_at_4.48.47_pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 347, "original_w": 1126, "original_h": 382, "thumb_tiny": "AwAQADDQG/c27AXtSjJ/iH5U49O/4UmD6mgBaKTB/vGjB/vGgBaKTB/vGlAx3zQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F087TTYB4FK/screenshot_2025-01-06_at_4.48.47_pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F087TTYB4FK-acfd6ccec0", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ApAIU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Who do we want to be on these precycle analytics calls?"}]}]}]}, {"ts": "1736140764.848849", "text": "Agenda topics for today:\n• UX improvements demo\n• Upcoming cycles and pending customer requirements\n", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1736141238.000000"}, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "R0lWf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda topics for today:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "UX improvements demo"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Upcoming cycles and pending customer requirements"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}], "created_at": "2025-05-22T21:35:34.696389"}