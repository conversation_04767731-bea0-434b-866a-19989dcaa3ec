{"date": "2024-12-06", "channel_id": "C065QSSNH8A", "message_count": 14, "messages": [{"ts": "1733498510.956169", "text": "Diversified Feedback: <https://www.loom.com/share/cd8ca41eefea4a099aa62ed3ca73dd42?sid=f03cd08e-b82f-452d-8c34-79c035359f1f>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733498510.956169", "reply_count": 4, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "tiNWE", "video_url": "https://www.loom.com/embed/cd8ca41eefea4a099aa62ed3ca73dd42?sid=f03cd08e-b82f-452d-8c34-79c035359f1f&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/cd8ca41eefea4a099aa62ed3ca73dd42-6e1e801b70bd38d6-4x3.jpg", "alt_text": "Improving Data Visualization 📊", "title": {"type": "plain_text", "text": "Improving Data Visualization 📊", "emoji": true}, "title_url": "https://www.loom.com/share/cd8ca41eefea4a099aa62ed3ca73dd42", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 5 min  ", "emoji": true}}, {"type": "section", "block_id": "Fg/4D", "text": {"type": "mrkdwn", "text": ":information_source: I had a call with <PERSON> from Diversified who's struggling with understanding complex numbers. The solution involves hiding columns and changing...", "verbatim": false}}, {"type": "actions", "block_id": "p+SBG", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/cd8ca41eefea4a099aa62ed3ca73dd42?sid=f03cd08e-b82f-452d-8c34-79c035359f1f"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"cd8ca41eefea4a099aa62ed3ca73dd42\",\"videoName\":\"Improving Data Visualization 📊\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/cd8ca41eefea4a099aa62ed3ca73dd42?sid=f03cd08e-b82f-452d-8c34-79c035359f1f", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "cEeKv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified Feedback: "}, {"type": "link", "url": "https://www.loom.com/share/cd8ca41eefea4a099aa62ed3ca73dd42?sid=f03cd08e-b82f-452d-8c34-79c035359f1f"}]}]}]}, {"ts": "1733493495.996899", "text": "<@U07EJ2LP44S> I don’t have anything specific to discuss. Let me know if you are fine to skip stand up. ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733493495.996899", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "+NZQd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " have anything specific to discuss. Let me know if you are fine to skip stand up. "}]}]}]}, {"ts": "1733477928.704629", "text": "<!here> I caught a stomach flu tonight and might not be able to join the standup in the morning.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733477928.704629", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "By+8w", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I caught a stomach flu tonight and might not be able to join the standup in the morning."}]}]}]}, {"ts": "1733469541.014609", "text": "Removed duplicate entry. Long term fix is being worked on, That duplicate entry was through bands upload.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.877479", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "+RDPq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Removed duplicate entry. Long term fix is being worked on, That duplicate entry was through bands upload."}]}]}]}, {"ts": "1733469502.370419", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> Fixed the data. This is issue with new upload improvements changes which we used to upload the data in div energy. We will fix it.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733411180.688089", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "4pxFJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Fixed the data. This is issue with new upload improvements changes which we used to upload the data in div energy. We will fix it."}]}]}]}, {"ts": "1733437920.630109", "text": "Diversified had a TON of feedback on the overall usability of the bonus page, and I do agree with all her feedback. May be easiest to go through it live on the call together tomorrow.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "B9Vtk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified had a TON of feedback on the overall usability of the bonus page, and I do agree with all her feedback. May be easiest to go through it live on the call together tomorrow."}]}]}]}, {"ts": "1733437751.803919", "text": "<@U07M6QKHUC9> I need to send SAML instructions to Diversified. Is there a different document now? I checked the drive folder but it is currently empty. I can dig up the old doc if there isn't a newer version.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733437751.803919", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "2I8gv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I need to send SAML instructions to Diversified. Is there a different document now? I checked the drive folder but it is currently empty. I can dig up the old doc if there isn't a newer version."}]}]}]}, {"ts": "1733432415.148929", "text": "We just had a great call with <PERSON>uster 300 emp. There is 75% chance they will convert. Let's keep the fingers crossed :partying_face:", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "p<PERSON>tsy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We just had a great call with <PERSON>uster 300 emp. There is 75% chance they will convert. Let's keep the fingers crossed "}, {"type": "emoji", "name": "partying_face", "unicode": "1f973"}]}]}]}, {"ts": "**********.877479", "text": "Tithely data issue with Canada listed twice: <https://compiify.atlassian.net/browse/COM-4019>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.877479", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14102::7e29b5bd27664cde9978a3e5e3ac0b66", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4019?atlOrigin=eyJpIjoiMjIwYTRmYzZmNjFmNDMyNWIxZTY1Y2EyNWM0MWRhZjYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4019 Issue: Remove Duplicate Country Entries in Tithely's Account>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14102::a0357cdabb0a4417bab88138e021c7e3", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14102\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4019\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4019", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "XeplU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Tithely data issue with Canada listed twice: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4019"}]}]}]}, {"ts": "1733430169.433909", "text": "<@U0690EB5JE5> can you delete the active cycle in stridedemo for valgenesis? <https://compiify.atlassian.net/browse/COM-4018>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14101::a8977a77b7da46e59d40bd715341342a", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4018?atlOrigin=eyJpIjoiYTUzYmEwOTgwMzBmNDkyMjgwOGY5NjdmNjkzNTg0NDQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4018 Deleting Annual Cycle 2024 in Stride Demo>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14101::16a432e86e4346c28086fdd65fbb3a8a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14101\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4018\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4018", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "hzcu4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you delete the active cycle in stridedemo for valgenesis? "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4018"}]}]}]}, {"ts": "1733430038.014629", "text": "<@U0690EB5JE5> We have a second customer that needs a demo training enviornmnet. We have <http://stridedemo.stridehr.io|stridedemo.stridehr.io> but we'll need a second instance for Tithely, as Valgenesis is using that one and we'll need different cycle settings for Tithley. Can we duplicate the environment? Maybe call it <http://tithelydemo.stridehr.io|tithelydemo.stridehr.io>?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733430038.014629", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "KF1os", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We have a second customer that needs a demo training enviornmnet. We have "}, {"type": "link", "url": "http://stridedemo.stridehr.io", "text": "stridedemo.stridehr.io"}, {"type": "text", "text": " but we'll need a second instance for <PERSON>ith<PERSON>, as Valgenesis is using that one and we'll need different cycle settings for Tithley. Can we duplicate the environment? Maybe call it "}, {"type": "link", "url": "http://tithelydemo.stridehr.io", "text": "tithelydemo.stridehr.io"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1733426116.327719", "text": "<@U07EJ2LP44S> Tithely don't have any employees in \"CANADA\" so it looks like a bug as well. Please create tickets for all of the bugs we saw today", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "aFn1x", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Tithely don't have any employees in \"CANADA\" so it looks like a bug as well. Please create tickets for all of the bugs we saw today"}]}]}]}, {"ts": "1733426040.907889", "text": "Thanks :slightly_smiling_face: Also I got good intel from Valgensis about both letters and pay bands", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "tada", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "W6X0D", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " Also I got good intel from Valgensis about both letters and pay bands"}]}]}]}, {"ts": "1733425974.694719", "text": "<@U07EJ2LP44S> you did an awesome job with Tithely. Glad I asked you to do it :slightly_smiling_face:\n\n <@U0690EB5JE5> It looks like audit log bug appeared again. changes are not being updated correctly in the audit log in Tithely env.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733425974.694719", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "GhT2M", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " you did an awesome job with <PERSON><PERSON><PERSON>. Glad I asked you to do it "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": "\n\n "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " It looks like audit log bug appeared again. changes are not being updated correctly in the audit log in Tithely env."}]}]}]}], "created_at": "2025-05-22T21:35:34.681248"}