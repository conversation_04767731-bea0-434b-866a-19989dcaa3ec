{"date": "2024-10-09", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1728493173.327029", "text": "Sorry got dropped due to network issues ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gqrst", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sorry got dropped"}, {"type": "text", "text": " "}, {"type": "text", "text": "due to network issues"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1728488835.097169", "text": "<@U04DKEFP1K8> is this the most updated azure SSO instructions? <https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1728488835.097169", "reply_count": 4, "files": [{"id": "F07M69YTJ3T", "created": 1725990406, "timestamp": 1725990406, "name": "Setting up Azure AD as SAML enterprise connection", "title": "Setting up Azure AD as SAML enterprise connection", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 124649, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50", "external_url": "https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit", "url_private": "https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1035, "thumb_tiny": "AwAwACXTJxSZ9v1ob6ZpAD7UALk+lLSc+1LzQAUUUUAI30puDTmpvHqaAF+alyfSjj1pfxoAKKKKAEP1pu0HvTyM03YKADb70uBSbBSgAdKAFooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07M69YTJ3T/setting_up_azure_ad_as_saml_enterprise_connection", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "QCPmS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " is this the most updated azure SSO instructions? "}, {"type": "link", "url": "https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit"}]}]}]}, {"ts": "1728487490.507119", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> FYI it is now commonplace for a CSV upload to show 'internal error' after upload, but the file did indeed get uploaded and the changes made.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1728487490.507119", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "uxt7t", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " FYI it is now commonplace for a CSV upload to show 'internal error' after upload, but the file did indeed get uploaded and the changes made."}]}]}]}, {"ts": "1728479593.253119", "text": "In demo view, is there a way to change the name of <PERSON><PERSON><PERSON> to anything that doesn't have an underscore in it?", "user": "U07NBMXTL1E", "type": "message", "thread_ts": "1728479593.253119", "reply_count": 11, "files": [{"id": "F07R3KM017U", "created": 1728479542, "timestamp": 1728479542, "name": "Screenshot 2024-10-09 at 9.12.16 AM.png", "title": "Screenshot 2024-10-09 at 9.12.16 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07NBMXTL1E", "user_team": "T04DM97F1UM", "editable": false, "size": 156951, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07R3KM017U/screenshot_2024-10-09_at_9.12.16_am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07R3KM017U/download/screenshot_2024-10-09_at_9.12.16_am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R3KM017U-261c15b09c/screenshot_2024-10-09_at_9.12.16_am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R3KM017U-261c15b09c/screenshot_2024-10-09_at_9.12.16_am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R3KM017U-261c15b09c/screenshot_2024-10-09_at_9.12.16_am_360.png", "thumb_360_w": 360, "thumb_360_h": 158, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R3KM017U-261c15b09c/screenshot_2024-10-09_at_9.12.16_am_480.png", "thumb_480_w": 480, "thumb_480_h": 211, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R3KM017U-261c15b09c/screenshot_2024-10-09_at_9.12.16_am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R3KM017U-261c15b09c/screenshot_2024-10-09_at_9.12.16_am_720.png", "thumb_720_w": 720, "thumb_720_h": 317, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R3KM017U-261c15b09c/screenshot_2024-10-09_at_9.12.16_am_800.png", "thumb_800_w": 800, "thumb_800_h": 352, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R3KM017U-261c15b09c/screenshot_2024-10-09_at_9.12.16_am_960.png", "thumb_960_w": 960, "thumb_960_h": 422, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07R3KM017U-261c15b09c/screenshot_2024-10-09_at_9.12.16_am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 451, "original_w": 1682, "original_h": 740, "thumb_tiny": "AwAVADDSJ5ozxQetHagAByKWkHSloAKKKKAExmjHGKWigBAMUtFFABRRRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07R3KM017U/screenshot_2024-10-09_at_9.12.16_am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07R3KM017U-adce4142c5", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "zJTUz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In demo view, is there a way to change the name of <PERSON><PERSON><PERSON> to anything that doesn't have an underscore in it?"}]}]}]}, {"ts": "1728455716.632859", "text": "<@U04DKEFP1K8> Alayacare performance rating sync. Looks like we don't have that anywhere in the APIs.\nis `9 Box Placement` custom field? And there is no relevant B<https://documentation.bamboohr.com/reference|amboo hr API >as well.\n<https://compiify.atlassian.net/browse/COM-3728>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1728455781.000000"}, "blocks": [{"type": "rich_text", "block_id": "uabaH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Alayacare performance rating sync. Looks like we don't have that anywhere in the APIs.\nis "}, {"type": "text", "text": "9 Box Placement", "style": {"code": true}}, {"type": "text", "text": " custom field? And there is no relevant B"}, {"type": "link", "url": "https://documentation.bamboohr.com/reference", "text": "amboo hr API "}, {"type": "text", "text": "as well.\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3728"}]}]}]}, {"ts": "1728438465.704489", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> Did we decide anything on this ticket?\n<https://compiify.atlassian.net/browse/COM-3675>", "user": "U0690EB5JE5", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13736::8bdbc84e5f98464a9f52982139a47dea", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3675?atlOrigin=eyJpIjoiNDQwNDYwYTc3NWFkNGI0ZGFmZDVhMmQwNWU2YzlmNTgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3675 Issue: Performance rating toggle in Merit view not toggling correct…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13736::ed3ad93f95b94f9b937b65b7c5cf22ba", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/281f945ce313d91068467caedc043d5f?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13736\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3675\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3675", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "FdlOU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Did we decide anything on this ticket?\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3675"}]}]}]}, {"ts": "1728437719.939309", "text": "I am traveling to my native my evening for <https://en.wikipedia.org/wiki/Vijayadashami|Dasara festival>. I will join leadership stand up call Via phone.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1728437719.939309", "reply_count": 1, "attachments": [{"image_url": "https://upload.wikimedia.org/wikipedia/commons/thumb/8/89/Navratri_Navaratri_festival_preparations_and_performance_arts_collage.jpg/1200px-Navratri_Navaratri_festival_preparations_and_performance_arts_collage.jpg", "image_width": 1200, "image_height": 599, "image_bytes": 265279, "from_url": "https://en.wikipedia.org/wiki/Vijay<PERSON><PERSON>i", "service_icon": "https://a.slack-edge.com/80588/img/unfurl_icons/wikipedia.png", "id": 1, "original_url": "https://en.wikipedia.org/wiki/Vijay<PERSON><PERSON>i", "fallback": "wikipedia: <PERSON><PERSON><PERSON><PERSON>", "text": "Vijay<PERSON>shami (Sanskrit: विजयादशमी, romanized: <PERSON><PERSON><PERSON><PERSON><PERSON>), more commonly known as <PERSON><PERSON><PERSON>, and also known as <PERSON><PERSON> or <PERSON>ain, is a major Hindu festival celebrated every year at the end of Durga Puja and Navaratri. It is observed on the tenth day of the month of Ashvin, the seventh in the Hindu lunisolar calendar. The festival typically falls in the Gregorian calendar months of September and October.\nVijayadashami is observed for different reasons and celebrated differently in various parts of the India and Nepal. In the southern, eastern, northeastern, and some northern states of India, Vijayadashami marks the end of Durga Puja, commemorating goddess <PERSON><PERSON>'s victory against <PERSON><PERSON><PERSON><PERSON> to restore and protect dharma. In the northern, central, and western states, it marks the end of <PERSON><PERSON><PERSON> and commemorates the deity <PERSON>'s victory over <PERSON><PERSON>. Alternatively, it marks a reverence for one of the aspects of goddess <PERSON> Durga.\nVijayadashami celebrations include processions to a river or ocean front that involve carrying clay statues of <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, accompanied by music and chants, after which the images are immersed in the water for dissolution and farewell. In other places, towering effigies of <PERSON><PERSON>, symbolising evil, are burnt with fireworks, marking evil's destruction. The festival also starts the preparations for <PERSON><PERSON>i, the important festival of lights, which is celebrated twenty days after <PERSON><PERSON><PERSON><PERSON>.", "title": "<PERSON><PERSON><PERSON><PERSON>", "title_link": "https://en.wikipedia.org/wiki/Vijay<PERSON><PERSON>i", "author_name": "Wikipedia", "author_link": "https://en.wikipedia.org/"}], "blocks": [{"type": "rich_text", "block_id": "C7TWV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am traveling to my native my evening for "}, {"type": "link", "url": "https://en.wikipedia.org/wiki/Vijay<PERSON><PERSON>i", "text": "Dasara festival"}, {"type": "text", "text": ". I will join leadership stand up call Via phone."}]}]}]}, {"ts": "1728426173.067669", "text": "<@U0690EB5JE5> does changes such as hiding and displaying of columns in merit and org view are specific to each user? if admin wants to hide certain columns, today it can only be done in the back end?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1728426173.067669", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "wpHXc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " does changes such as hiding and displaying of columns in merit and org view are specific to each user? if admin wants to hide certain columns, today it can only be done in the back end?"}]}]}]}, {"ts": "1728424711.128289", "text": "Yup. I put a bug in. This is what I was talking about, <@U04DKEFP1K8> ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1728409960.945199", "subtype": "thread_broadcast", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "rDGQi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yup. I put a bug in. This is what I was talking about, "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}]}]}]}], "created_at": "2025-05-22T21:35:34.649202"}