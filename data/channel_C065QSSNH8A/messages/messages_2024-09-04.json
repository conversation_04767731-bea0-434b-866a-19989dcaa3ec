{"date": "2024-09-04", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1725466851.695219", "text": "Is there a way for me to get access to <http://Fireflies.ai|Fireflies.ai> for note taking during customer interviews? At this point I only have two scheduled, both later today, so I just need access for today. (One with our HR advisor <PERSON>, and one with a  personal contact who is a Sr. Director of a 900 person company for a manager perspective)\n\nI will record the interviews too, but having the automatic notes would save a bit of time not having to re-listen.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1725466851.695219", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "dwPoi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is there a way for me to get access to "}, {"type": "link", "url": "http://Fireflies.ai", "text": "Fireflies.ai"}, {"type": "text", "text": " for note taking during customer interviews? At this point I only have two scheduled, both later today, so I just need access for today. (One with our HR advisor <PERSON>, and one with a  personal contact who is a Sr. Director of a 900 person company for a manager perspective)\n\nI will record the interviews too, but having the automatic notes would save a bit of time not having to re-listen."}]}]}]}, {"ts": "1725465451.190229", "text": "Ok", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PfDzz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok"}]}]}]}, {"ts": "1725465422.236779", "text": "<@U04DS2MBWP4> I am holding deployment to demo ENV as it will disable people insights alerts. I have been thinking to fix and merge but unable to get to that issue. ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "W5FdS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I am holding deployment to demo ENV as it will disable people insights alerts. I have been thinking to fix and merge but unable to get to that issue. "}]}]}]}, {"ts": "1725465292.804739", "text": "Nice. Did you mean they are deployed to just the Test environment and not the demo environment yet? <@U0690EB5JE5> ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "dxmrP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Nice. Did you mean they are deployed to just the Test environment and not the demo environment yet? "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1725445709.582779", "text": "<!here> *Eng* *Updates*:\nFollowing changes are deployed until Test ENVs today\n• Ability to exclude employees by job title, compensation type\n• You can now run sync directly from integrations page (Screenshot below)\n> Clicking on *Sync* - will sync changes since Last Successful sync date\n> Clicking on *Full Sync* - will sync whole Org\n> Clicking on *Remove -* will delete the integration. \n> Please note its in MVP stage and UI needs more enhancements like combining both sync buttons and have date input to sync from any give date etc.\n> *What happens when you run sync:*\n>     ◦ It pulls all the data and updates/creates employees. *Caveat*:  There might some scenarios we may not supposed to overwrite and this will be handled in next iteration as part of delta sync work\n>     ◦ *Sets root employee as super admin* which is required for Org View to load.\n>     ◦ *Calculates Job Level* based on Reporting structure - highest number being the root employee\n>     ◦ *Sets Job Category* as `M` is employee has at least one employee reporting and `IC` if none reporting\n>     ◦ Tenure was not being updated and is being done now.\n>     ◦ *For Valgenesis:* Apart from pulling target bonus and division\n>         ▪︎ Since country is missing the country is extracted from currency and region\n>         ▪︎ Last raise date is being pulled from remote data\n> Basically from now on We can bring up Org View just clicking this button if we have integration. I have test this for Valgenesis. I am testing this for Div energy where I am seeing some issues which i am guessing data specific.\n> \n> *Upcoming work on integrations:*\n> • Add more controls to integrations page to give inputs like sync date, full sync etc...\n> • Delta sync. Ability to review, edit and approve on going changes like New Hire, Termination and  updates to existing employees\n> • Advanced feature - Ability to map remote data, at least internally. This is there already in merge page. But will have to brainstorm more. But this will be post November\n*Other changes pushed from the priority list until yesterday:*\n• COLA Component\n• View and edit perf rating in Org View\n• Region column in Org View", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1725445709.582779", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1725450246.000000"}, "reactions": [{"name": "rocket", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F07L80F355F", "created": 1725444937, "timestamp": 1725444937, "name": "Screenshot 2024-09-04 at 3.43.38 PM.png", "title": "Screenshot 2024-09-04 at 3.43.38 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 171443, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07L80F355F/screenshot_2024-09-04_at_3.43.38___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07L80F355F/download/screenshot_2024-09-04_at_3.43.38___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 199, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 266, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 399, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 443, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 532, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07L80F355F-a8acfd8c64/screenshot_2024-09-04_at_3.43.38___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 567, "original_w": 1907, "original_h": 1056, "thumb_tiny": "AwAaADDQ4zzTuKbjIzx+NKFB7CgBeKOKTaM9vyoKj2oAXikOKXaPQUmAemKAF6D0pR0oFFAB3oNFFABSClooA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07L80F355F/screenshot_2024-09-04_at_3.43.38___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07L80F355F-6b99cd28f0", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "RHiAI", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}, {"type": "text", "text": "Eng", "style": {"bold": true}}, {"type": "text", "text": " "}, {"type": "text", "text": "Updates", "style": {"bold": true}}, {"type": "text", "text": ":\nFollowing changes are deployed until Test ENVs today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ability to exclude employees by job title, compensation type"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "You can now run sync directly from integrations page (Screenshot below)"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_quote", "elements": [{"type": "text", "text": "Clicking on"}, {"type": "text", "text": " Sync", "style": {"bold": true}}, {"type": "text", "text": " - will sync changes since Last Successful sync date\nClicking on"}, {"type": "text", "text": " Full Sync", "style": {"bold": true}}, {"type": "text", "text": " - will sync whole Org\nClicking on "}, {"type": "text", "text": "Remove - ", "style": {"bold": true}}, {"type": "text", "text": "will delete the integration. \nPlease note its in MVP stage and UI needs more enhancements like combining both sync buttons and have date input to sync from any give date etc.\n"}, {"type": "text", "text": "What happens when you run sync:", "style": {"bold": true}}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It pulls all the data and updates/creates employees. "}, {"type": "text", "text": "Caveat", "style": {"bold": true}}, {"type": "text", "text": ":  There might some scenarios we may not supposed to overwrite and this will be handled in next iteration as part of delta sync work"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Sets root employee as super admin", "style": {"bold": true}}, {"type": "text", "text": " which is required for Org View to load."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Calculates Job Level", "style": {"bold": true}}, {"type": "text", "text": " based on Reporting structure - highest number being the root employee"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Sets Job Category", "style": {"bold": true}}, {"type": "text", "text": " as "}, {"type": "text", "text": "M", "style": {"code": true}}, {"type": "text", "text": " is employee has at least one employee reporting and "}, {"type": "text", "text": "IC", "style": {"code": true}}, {"type": "text", "text": " if none reporting"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Tenure was not being updated and is being done now."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "For Valgenesis: ", "style": {"bold": true}}, {"type": "text", "text": "Apart from pulling target bonus and division"}]}], "style": "bullet", "indent": 1, "border": 1}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Since country is missing the country is extracted from currency and region"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Last raise date is being pulled from remote data"}]}], "style": "bullet", "indent": 2, "border": 1}, {"type": "rich_text_quote", "elements": [{"type": "text", "text": "Basically from now on We can bring up Org View just clicking this button if we have integration. I have test this for Valgenesis. I am testing this for Div energy where I am seeing some issues which i am guessing data specific.\n\n"}, {"type": "text", "text": "Upcoming work on integrations:", "style": {"bold": true}}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Add more controls to integrations page to give inputs like sync date, full sync etc..."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Delta sync. Ability to review, edit and approve on going changes like New Hire, Termination and  updates to existing employees"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Advanced feature - Ability to map remote data, at least internally. This is there already in merge page. But will have to brainstorm more. But this will be post November"}]}], "style": "bullet", "indent": 0, "border": 1}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Other changes pushed from the priority list until yesterday:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "COLA Component"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "View and edit perf rating in Org View"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Region column in Org View"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1725400099.691879", "text": "<!here> I'd like to go ahead and get the initial Manager Enablement Solution Proposal (early PRD) review and feedback session scheduled for next Monday, 9/9. Would you like to do it during your regular Leadership Standup? We'll need at least the full hour. Let me know if you'd like me to send a new invite with agenda, or if you just want to change the agenda of the existing meeting.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1725400099.691879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "jrf56", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I'd like to go ahead and get the initial Manager Enablement Solution Proposal (early PRD) review and feedback session scheduled for next Monday, 9/9. Would you like to do it during your regular Leadership Standup? We'll need at least the full hour. Let me know if you'd like me to send a new invite with agenda, or if you just want to change the agenda of the existing meeting."}]}]}]}], "created_at": "2025-05-22T21:35:34.642446"}