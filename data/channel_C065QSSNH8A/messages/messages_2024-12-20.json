{"date": "2024-12-20", "channel_id": "C065QSSNH8A", "message_count": 11, "messages": [{"ts": "1734718047.294219", "text": "UI/UX ticket: <https://compiify.atlassian.net/browse/COM-4036>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14119::ab84e554582745019ed471da6ae127d9", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4036?atlOrigin=eyJpIjoiYmRkNGU0MGJkYjBlNGI0ZjgzZmFkZTI2NWYyYzEyZmQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4036 UI/UX changes needed>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14119::313a12d485e44fe9b5fee5152bf2db03", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14119\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4036\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4036", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "uEuMf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "UI/UX ticket: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4036"}]}]}]}, {"ts": "1734716688.537129", "text": "So we've used those filters for Verified / Likely to Engage and what we're seeing now is that a lot of the emails get the green check, but still blocked as spam...not sure if this is a Senders thing, or an Apollo data quality issue, but its not like we're sending a crazy amount of emails to random or unverified people purposefully", "user": "U07NBMXTL1E", "type": "message", "thread_ts": "1734716688.537129", "reply_count": 13, "files": [{"id": "F086ENQNLF3", "created": 1734716619, "timestamp": 1734716619, "name": "Screenshot 2024-12-20 at 12.43.35 PM.png", "title": "Screenshot 2024-12-20 at 12.43.35 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07NBMXTL1E", "user_team": "T04DM97F1UM", "editable": false, "size": 391606, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F086ENQNLF3/screenshot_2024-12-20_at_12.43.35_pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F086ENQNLF3/download/screenshot_2024-12-20_at_12.43.35_pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_360.png", "thumb_360_w": 360, "thumb_360_h": 146, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_480.png", "thumb_480_w": 480, "thumb_480_h": 194, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_720.png", "thumb_720_w": 720, "thumb_720_h": 291, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_800.png", "thumb_800_w": 800, "thumb_800_h": 324, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_960.png", "thumb_960_w": 960, "thumb_960_h": 388, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 414, "original_w": 2794, "original_h": 1130, "thumb_tiny": "AwATADDSHWmtnf3/AFpw6/jTW+/0/SgB1FFAoADQKD0oFADQTk/Wmuf3oHHbtTh94/WmP/rh+FDBD88UoNJ2pRQArdKQHg0rdKaOhoA//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F086ENQNLF3/screenshot_2024-12-20_at_12.43.35_pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F086ENQNLF3-496c91d275", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "OeWSK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So we've used those filters for Verified / Likely to Engage and what we're seeing now is that a lot of the emails get the green check, but still blocked as spam...not sure if this is a Senders thing, or an Apollo data quality issue, but its not like we're sending a crazy amount of emails to random or unverified people purposefully"}]}]}]}, {"ts": "1734711609.236899", "text": "<@U07NBMXTL1E> pls unenroll all unverified contacts from all of the active sequences immediately. It's hurting our domain reputation", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07NBMXTL1E"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "RRDya", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " pls unenroll all unverified contacts from all of the active sequences immediately. It's hurting our domain reputation"}]}]}]}, {"ts": "1734661754.611649", "text": "<!here> I've a conflict with another call for standup tomorrow. will join about 15 mins late", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "gsdB+", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I've a conflict with another call for standup tomorrow. will join about 15 mins late"}]}]}]}, {"ts": "1734652225.579739", "text": "<@U07NBMXTL1E> can you pls set up a 1 hr call with vestwell during 1st or 2nd week of January to better understand their requirements and pain points on Precycle budget planning and analytics?", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07NBMXTL1E"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "dKg6a", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " can you pls set up a 1 hr call with vestwell during 1st or 2nd week of January to better understand their requirements and pain points on Precycle budget planning and analytics?"}]}]}]}, {"ts": "1734651605.887059", "text": "<@U07NBMXTL1E> Could you please get the feedback on Precycle budget planning and analytics in the two customer calls you have tomorrow? <@U07EJ2LP44S> Amanda can you pls join the 7 am pst call <PERSON><PERSON> has with one of the prospects to validate Precycle budget planning and analytics idea?\n\n<@U07NBMXTL1E> Let's collect feedback from 20 customers on this idea. Either <PERSON> or I will join the call depending on timing so please coordinate with us for all intro and demo calls you are having with 400+ emp customers over the next 3  weeks.", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07NBMXTL1E", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "oRtTO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " Could you please get the feedback on Precycle budget planning and analytics in the two customer calls you have tomorrow? "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Amanda can you pls join the 7 am pst call <PERSON><PERSON> has with one of the prospects to validate Precycle budget planning and analytics idea?\n\n"}, {"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " Let's collect feedback from 20 customers on this idea. Either <PERSON> or I will join the call depending on timing so please coordinate with us for all intro and demo calls you are having with 400+ emp customers over the next 3  weeks."}]}]}]}, {"ts": "1734651344.076449", "text": "Agenda for Tomorrow standup:\n• Review of Total Rewards PRD\n• Align on action items tracker so that we can track actions items going forward.\n• Finalize the execution plan along with activity timeline for Precycle budget planning and analytics. <PERSON> to lead this project. (only if time permits. If not <PERSON><PERSON><PERSON> and <PERSON> can do this offline)\nActions from Dec 19 standup\n• <PERSON> to gather detailed feedback from Curana, AlayaCare, &amp; Diversified on Precycle budget planning and analytics. Due Date Jan 15th\n• Ka<PERSON>l to document key features/functionalities for Comp Planner\n• Dedicate one day per week to go over the progress and status of strategic product initiatives, learnings, customer and prospect feedback etc.\n• Amanda to get customer approval for G2 reviews (discussed outside of standup)", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for Tomorrow standup:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Review of Total Rewards PRD"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Align on action items tracker so that we can track actions items going forward."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Finalize the execution plan along with activity timeline for Precycle budget planning and analytics. <PERSON> to lead this project. (only if time permits. If not <PERSON><PERSON><PERSON> and <PERSON> can do this offline)"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nActions from Dec 19 standup\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Amanda to gather detailed feedback from Curana, AlayaCare, & Diversified on Precycle budget planning and analytics. Due Date Jan 15th"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> to document key features/functionalities for Comp Planner"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Dedicate one day per week to go over the progress and status of strategic product initiatives, learnings, customer and prospect feedback etc."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Amanda to get customer approval for G2 reviews (discussed outside of standup)"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1734650531.802349", "text": "<@U07EJ2LP44S> <PERSON><PERSON> agreed to submit the G2 reviews,\nBefore they renew, they want to see that we have incorporated their feedback in the product and they will not need the manual support for things they needed in their August Cycle. I know you had a feedback call with them. Can you also go through their slack channel to create a list of issues where they needed manual support from us.\nWe will need to show them that we have addressed all of those issues.\n\nLet's go through each of those items in the standup on Monday if they need engineering work", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734650531.802349", "reply_count": 3, "edited": {"user": "U07M6QKHUC9", "ts": "1734651665.000000"}, "blocks": [{"type": "rich_text", "block_id": "28F70", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " <PERSON><PERSON> agreed to submit the G2 reviews,\nBefore they renew, they want to see that we have incorporated their feedback in the product and they will not need the manual support for things they needed in their August Cycle. I know you had a feedback call with them. Can you also go through their slack channel to create a list of issues where they needed manual support from us.\nWe will need to show them that we have addressed all of those issues.\n\nLet's go through each of those items in the standup on Monday if they need engineering work"}]}]}]}, {"ts": "1734639441.024379", "text": "<!here> We do have one more 800 emp company that have validated the needs for pre-cycle budget planning and analytics.\n<https://app.fireflies.ai/view/Stride-Introduction-w-Tosh-RewardStyle::Q57CrAEBjMJ6hbue> check out the last 15 mins of the call", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734639441.024379", "reply_count": 1, "attachments": [{"image_url": "https://files.fireflies.ai/app/img/preview.png", "image_width": 1280, "image_height": 720, "image_bytes": 188965, "from_url": "https://app.fireflies.ai/view/Stride-Introduction-w-Tosh-RewardStyle::Q57CrAEBjMJ6hbue", "service_icon": "https://app.fireflies.ai/favicon.ico", "thumb_url": "https://files.fireflies.ai/app/img/preview.png", "thumb_width": 1280, "thumb_height": 720, "id": 1, "original_url": "https://app.fireflies.ai/view/Stride-Introduction-w-Tosh-RewardStyle::Q57CrAEBjMJ6hbue", "fallback": "Stride Introduction w/ Tosh @ RewardStyle - Meeting recording by Fireflies.ai", "text": "In the Product Introduction and Requirements Gathering meeting titled \"Stride Introduction w/ Tosh @ RewardStyle,\" participants discussed Stride, a compensation plan...", "title": "Stride Introduction w/ Tosh @ RewardStyle - Meeting recording by Fireflies.ai", "title_link": "https://app.fireflies.ai/view/Stride-Introduction-w-Tosh-RewardStyle::Q57CrAEBjMJ6hbue", "service_name": "app.fireflies.ai"}], "blocks": [{"type": "rich_text", "block_id": "tOpMt", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " We do have one more 800 emp company that have validated the needs for pre-cycle budget planning and analytics.\n"}, {"type": "link", "url": "https://app.fireflies.ai/view/Stride-Introduction-w-Tosh-RewardStyle::Q57CrAEBjMJ6hbue"}, {"type": "text", "text": " check out the last 15 mins of the call"}]}]}]}, {"ts": "1734635937.828179", "text": "For the first and third calls, sure. For Alfatech in between, no. They're getting close to closure so adding new variables will probably do nothing but add confusion or give them an excuse to push this out further", "user": "U07NBMXTL1E", "type": "message", "thread_ts": "1734635937.828179", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "kMAbJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For the first and third calls, sure. For Alfatech in between, no. They're getting close to closure so adding new variables will probably do nothing but add confusion or give them an excuse to push this out further"}]}]}]}, {"ts": "1734635710.536529", "text": "<@U07NBMXTL1E> we have three customer call today. Can we try to squeeze in 10 min during these calls to learn about customer's painpoints on Pre-Cycle budget planning and analytics to validate the pain points <PERSON><PERSON><PERSON> brought up?\nI think it should be fairly easy to do with <PERSON><PERSON> for sure since we are meeting them for the 3rd time today.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ni23i", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " we have three customer call today. Can we try to squeeze in 10 min during these calls to learn about customer's painpoints on Pre-Cycle budget planning and analytics to validate the pain points <PERSON>est<PERSON> brought up?\nI think it should be fairly easy to do with <PERSON><PERSON> for sure since we are meeting them for the 3rd time today."}]}]}]}], "created_at": "2025-05-22T21:35:34.699045"}