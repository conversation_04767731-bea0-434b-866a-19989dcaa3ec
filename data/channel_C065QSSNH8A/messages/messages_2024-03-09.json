{"date": "2024-03-09", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1709961484.513449", "text": "We might also have to think about including higher precision for %s in our downloadable reports... :thinking_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709961484.513449", "reply_count": 3, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ZVjna", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We might also have to think about including higher precision for %s in our downloadable reports... "}, {"type": "emoji", "name": "thinking_face", "unicode": "1f914"}]}]}]}, {"ts": "1709959076.202339", "text": "And, a customer need for an audit log (either customer-facing or at least available to us for supporting them) -- <PERSON> just asked what a manager had input before she edited it this week :zany_face:\n\n(Fortunately I had an earlier snapshot and was able to answer :wink: )", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709959076.202339", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "PS+27", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And, a customer need for an audit log (either customer-facing or at least available to us for supporting them) -- <PERSON> just asked what a manager had input before she edited it this week "}, {"type": "emoji", "name": "zany_face", "unicode": "1f92a"}, {"type": "text", "text": "\n\n(Fortunately I had an earlier snapshot and was able to answer "}, {"type": "emoji", "name": "wink", "unicode": "1f609"}, {"type": "text", "text": " )"}]}]}]}, {"ts": "1709940017.215719", "text": "<PERSON> caught another issue with the downloadable files, ugh. :confused:\n<https://compiify.atlassian.net/browse/COM-2482>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709940017.215719", "reply_count": 3, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12543::6d3319d0dda211eea5c779ddd910214c", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2482?atlOrigin=eyJpIjoiZGRkN2FhMGY2ZWM5NGJjYzkwMDcxOTYwY2VhZTE1NTciLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2482 Increase % value in Export file doesn't match Merit view>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12543::6d3340e0dda211eea5c779ddd910214c", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12543::6d3319d1dda211eea5c779ddd910214c", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12543\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12543\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2482", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "tSmQg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> caught another issue with the downloadable files, ugh. "}, {"type": "emoji", "name": "confused", "unicode": "1f615"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2482"}]}]}]}, {"ts": "1709935815.071769", "text": "I noticed that SDF has started \"locking in\" some final values, but they seem to be going team-by-team. So far <PERSON> has only approved/overwritten the values for the Legal and Vibrant teams...", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709935815.071769", "reply_count": 25, "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06P2HGT3JM", "created": 1709935809, "timestamp": 1709935809, "name": "Screenshot 2024-03-08 at 2.02.10 PM.png", "title": "Screenshot 2024-03-08 at 2.02.10 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 173208, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06P2HGT3JM/screenshot_2024-03-08_at_2.02.10___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06P2HGT3JM/download/screenshot_2024-03-08_at_2.02.10___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 253, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 337, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 505, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 562, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 674, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P2HGT3JM-6e4747a63f/screenshot_2024-03-08_at_2.02.10___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 719, "original_w": 1658, "original_h": 1164, "thumb_tiny": "AwAhADDSZQRzn86ricJlQp4PrVg9Kov99vrQBdjfegbGKCOe/wCBplv/AKkU8j2/WgBRx2NLTcdOP1p3egBD0qi332+tXj0qkytuPynr6UAWbf8A1Ip5x/s/jTYAREMjFOJ9x+VAB6fdpe/akz05H5U7vQAHpQaD0oNABRRRQAUUUUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06P2HGT3JM/screenshot_2024-03-08_at_2.02.10___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06P2HGT3JM-4750b1f846", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06NM0KGGBX", "created": 1709935812, "timestamp": 1709935812, "name": "Screenshot 2024-03-08 at 2.02.24 PM.png", "title": "Screenshot 2024-03-08 at 2.02.24 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 225888, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06NM0KGGBX/screenshot_2024-03-08_at_2.02.24___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06NM0KGGBX/download/screenshot_2024-03-08_at_2.02.24___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 310, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 414, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 621, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 690, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 828, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NM0KGGBX-d74d42eeb8/screenshot_2024-03-08_at_2.02.24___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 883, "original_w": 1656, "original_h": 1428, "thumb_tiny": "AwApADDSYAjmq6yqHACEHOM5qwelUl/1o/3qAL1NI56H8DThTSPb9aAFHHY/nS03HTj9aXvQAHp2qkv+tH+9V09O1Ul/1o/3qAL2KaR7D8adzTT9V/GgAwOOBS45pPT7tO79qAEPSqaq3mA7T19Ku0UAAppPv+lOooAbnpz+lO70UUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06NM0KGGBX/screenshot_2024-03-08_at_2.02.24___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06NM0KGGBX-ccafc37b9a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "vk4we", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I noticed that SDF has started \"locking in\" some final values, but they seem to be going team-by-team. So far <PERSON> has only approved/overwritten the values for the Legal and Vibrant teams..."}]}]}]}], "created_at": "2025-05-22T21:35:34.610970"}