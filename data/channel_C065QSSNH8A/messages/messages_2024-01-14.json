{"date": "2024-01-14", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "**********.171229", "text": "<@U04DKEFP1K8> We need to turn on DKIM for our secondtry domain <http://compiifyinc.com|compiifyinc.com> and part of the process includes updating DKIM key throu the domain provider. I am assuming it's AWS? can we do this tomorrow. Here are link to the steps we need to follow:\n<https://support.google.com/a/answer/180504>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.171229", "reply_count": 5, "attachments": [{"from_url": "https://support.google.com/a/answer/180504", "service_icon": "https://support.google.com/favicon.ico", "id": 1, "original_url": "https://support.google.com/a/answer/180504", "fallback": "Turn on DKIM for your domain - Google Workspace Admin Help", "text": "Protect against spoofing &amp; phishing, and help prevent messages from being marked as spamFollow the steps in this article to get your DomainKeys Identified Mail (DKIM) key, add the key to your domain p", "title": "Turn on DKIM for your domain - Google Workspace Admin Help", "title_link": "https://support.google.com/a/answer/180504", "service_name": "support.google.com"}], "blocks": [{"type": "rich_text", "block_id": "Kw8do", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " We need to turn on DKIM for our secondtry domain "}, {"type": "link", "url": "http://compiifyinc.com", "text": "compiifyinc.com"}, {"type": "text", "text": " and part of the process includes updating DKIM key throu the domain provider. I am assuming it's AWS? can we do this tomorrow. Here are link to the steps we need to follow:\n"}, {"type": "link", "url": "https://support.google.com/a/answer/180504"}]}]}]}, {"ts": "**********.219539", "text": "<@U065H3M6WJV> Looks like <PERSON> is completing the 2nd comp survey at the end of this month. Now that we have total rewards portal, could we check if he is open to including us under the total rewards category as well esp now that <PERSON><PERSON> will be using our total rewards", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.219539", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "XiHcr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Looks like <PERSON> is completing the 2nd comp survey at the end of this month. Now that we have total rewards portal, could we check if he is open to including us under the total rewards category as well esp now that <PERSON><PERSON> will be using our total rewards"}]}]}]}, {"ts": "1705246556.086259", "text": "<@U04DKEFP1K8> , currently both <https://compiify.com/> and <https://www.compiify.com/> are accessible and ranking separately from a marketing perspsective.. Can we implement a redirect for all traffic to use the www- address or the non-www address to avoid both confusion and potential duplicate content warnings from search engines? What's the ETA?", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"from_url": "https://compiify.com/", "id": 1, "original_url": "https://compiify.com/", "fallback": "Compiify", "text": "Compiify software", "title": "Compiify", "title_link": "https://compiify.com/", "service_name": "compiify.com"}, {"from_url": "https://www.compiify.com/", "id": 2, "original_url": "https://www.compiify.com/", "fallback": "Compiify", "text": "Compiify software", "title": "Compiify", "title_link": "https://www.compiify.com/", "service_name": "compiify.com"}], "blocks": [{"type": "rich_text", "block_id": "gYWvl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " , currently both "}, {"type": "link", "url": "https://compiify.com/"}, {"type": "text", "text": " and "}, {"type": "link", "url": "https://www.compiify.com/"}, {"type": "text", "text": " are accessible and ranking separately from a marketing perspsective.. Can we implement a redirect for all traffic to use the www- address or the non-www address to avoid both confusion and potential duplicate content warnings from search engines? What's the ETA?"}]}]}]}, {"ts": "1705245639.176289", "text": "<@U065H3M6WJV> are we able to fully support the hourly employees? It looks like it's a gap in Assemble from the marketing audit report.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1705245639.176289", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "QByyr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " are we able to fully support the hourly employees? It looks like it's a gap in Assemble from the marketing audit report."}]}]}]}], "created_at": "2025-05-22T21:35:34.592508"}