{"thread_ts": "1738165831.896369", "channel_id": "C065QSSNH8A", "reply_count": 24, "replies": [{"ts": "1738165870.399349", "text": "", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "files": [{"id": "F08AMUVAS30", "created": 1738165869, "timestamp": 1738165869, "name": "PayrightNoVariable.csv", "title": "PayrightNoVariable.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 48886, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08AMUVAS30/payrightnovariable.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08AMUVAS30/download/payrightnovariable.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AMUVAS30/payrightnovariable.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08AMUVAS30-d0fe37cd67", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AMUVAS30/payrightnovariable.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">37</div><div class=\"cm-col\">Khoa</div><div class=\"cm-col\">Pham</div><div class=\"cm-col\">Khoa Pham</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">9/28/20</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">42</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">9/28/20</div><div class=\"cm-col\">9/28/20</div><div class=\"cm-col\">Vietnamese</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Partner Engineering Manager</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">Manager</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">133326.24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">13332.62</div><div class=\"cm-col cm-num\">5333.05</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">7999.57</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">5333.05</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">5679.7</div><div class=\"cm-col cm-num\">71</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">130712</div><div class=\"cm-col cm-num\">146397.24</div><div class=\"cm-col\"></div><div class=\"cm-col\">91.07/8.93</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Raj</div><div class=\"cm-col\">Patel</div><div class=\"cm-col\">Raj Patel</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">4/6/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">206</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">4/6/21</div><div class=\"cm-col\">4/6/21</div><div class=\"cm-col\">Indian</div><div class=\"cm-col\">San Francisco</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\">Platform</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">138409.95</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">131819</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\">Juan</div><div class=\"cm-col\">Rodriguez</div><div class=\"cm-col\">Juan Rodriguez</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">5/24/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">194</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">5/24/21</div><div class=\"cm-col\">5/24/21</div><div class=\"cm-col\">Hispanic</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">155710</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">15571</div><div class=\"cm-col cm-num\">6228.4</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">9342.6</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">6228.4</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">6726.67</div><div class=\"cm-col cm-num\">72</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">155710</div><div class=\"cm-col cm-num\">170281</div><div class=\"cm-col\"></div><div class=\"cm-col\">91.44/8.56</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30</div><div class=\"cm-col\">Hans</div><div class=\"cm-col\">Wagner</div><div class=\"cm-col\">Hans Wagner</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">9/6/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">206</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">9/6/22</div><div class=\"cm-col\">9/6/22</div><div class=\"cm-col\">German</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\">Platform</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">205837.32</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">131947</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">126</div><div class=\"cm-col\">Elena</div><div class=\"cm-col\">Lopez</div><div class=\"cm-col\">Elena Lopez</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">11/1/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">12</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">11/1/21</div><div class=\"cm-col\">11/1/21</div><div class=\"cm-col\">Hispanic</div><div class=\"cm-col\">Remote-Payzone3</div><div class=\"cm-col\">Sr. Engineering Manager</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">Manager</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">153991.9</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">15399.19</div><div class=\"cm-col cm-num\">6159.68</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">9239.51</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">6159.68</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">6744.85</div><div class=\"cm-col cm-num\">73</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">133906</div><div class=\"cm-col cm-num\">167382.9</div><div class=\"cm-col\"></div><div class=\"cm-col\">92.00/8.00</div></div></div>\n</div>\n", "lines": 138, "lines_more": 137, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}]}, {"ts": "1738165914.974159", "text": "The goal is to get the variable pay section in the merit view from showing up (the nav bar above the table)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "4muKt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The goal is to get the variable pay section in the merit view from showing up (the nav bar above the table)"}]}]}]}, {"ts": "1738166049.100229", "text": "<@U07EJ2LP44S> So you are trying to add OTE employees?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "IRC2N", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " So you are trying to add OTE employees?"}]}]}]}, {"ts": "1738166060.646639", "text": "what columns are you trying to update?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "R/IRx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "what columns are you trying to update?"}]}]}]}, {"ts": "1738166258.670629", "text": "Can you change the compensation type to OTE and try again?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "yuBt0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can you change the compensation type to OTE and try again?"}]}]}]}, {"ts": "1738166270.229739", "text": "I am trying to delete OTE info", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "I0B4y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am trying to delete OTE info"}]}]}]}, {"ts": "1738166297.126909", "text": "I am trying to get the variable pay section OUT so I can mimic the diversified env", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "sYsIJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am trying to get the variable pay section OUT so I can mimic the diversified env"}]}]}]}, {"ts": "1738166300.702229", "text": "Sorry I wasn't clear", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "BjJxK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sorry I wasn't clear"}]}]}]}, {"ts": "1738166335.355339", "text": "then remove OTE related salary info, like OTE, Paymix, Variable pay", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738165831.896369", "files": [{"id": "F08AN0Y9MT8", "created": 1738166332, "timestamp": 1738166332, "name": "Screenshot 2025-01-29 at 9.27.54 PM.png", "title": "Screenshot 2025-01-29 at 9.27.54 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 40301, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08AN0Y9MT8/screenshot_2025-01-29_at_9.27.54___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08AN0Y9MT8/download/screenshot_2025-01-29_at_9.27.54___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AN0Y9MT8-4df2eca4ee/screenshot_2025-01-29_at_9.27.54___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AN0Y9MT8-4df2eca4ee/screenshot_2025-01-29_at_9.27.54___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AN0Y9MT8-4df2eca4ee/screenshot_2025-01-29_at_9.27.54___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 90, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AN0Y9MT8-4df2eca4ee/screenshot_2025-01-29_at_9.27.54___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 119, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AN0Y9MT8-4df2eca4ee/screenshot_2025-01-29_at_9.27.54___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AN0Y9MT8-4df2eca4ee/screenshot_2025-01-29_at_9.27.54___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 179, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08AN0Y9MT8-4df2eca4ee/screenshot_2025-01-29_at_9.27.54___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 199, "original_w": 948, "original_h": 236, "thumb_tiny": "AwALADDQP0pe3Sm0ooAOwp2M009BTxQAm2gDFLRQB//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AN0Y9MT8/screenshot_2025-01-29_at_9.27.54___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08AN0Y9MT8-70dd89cc23", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "YLB/u", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "then remove OTE related salary info, like OTE, Paymix, Variable pay"}]}]}]}, {"ts": "1738166831.421199", "text": "I still can't get it to work.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "files": [{"id": "F08AX76CQ3E", "created": 1738166830, "timestamp": 1738166830, "name": "PayrightNoVariable.csv", "title": "PayrightNoVariable.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 47930, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08AX76CQ3E/payrightnovariable.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08AX76CQ3E/download/payrightnovariable.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AX76CQ3E/payrightnovariable.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08AX76CQ3E-e9fdda789f", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08AX76CQ3E/payrightnovariable.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">37</div><div class=\"cm-col\">Khoa</div><div class=\"cm-col\">Pham</div><div class=\"cm-col\">Khoa Pham</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">9/28/20</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">42</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">9/28/20</div><div class=\"cm-col\">9/28/20</div><div class=\"cm-col\">Vietnamese</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Partner Engineering Manager</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">Manager</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">133326.24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">13332.62</div><div class=\"cm-col cm-num\">5333.05</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">7999.57</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">5333.05</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">5679.7</div><div class=\"cm-col cm-num\">71</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">130712</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Raj</div><div class=\"cm-col\">Patel</div><div class=\"cm-col\">Raj Patel</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">4/6/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">206</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">4/6/21</div><div class=\"cm-col\">4/6/21</div><div class=\"cm-col\">Indian</div><div class=\"cm-col\">San Francisco</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\">Platform</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">138409.95</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">131819</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\">Juan</div><div class=\"cm-col\">Rodriguez</div><div class=\"cm-col\">Juan Rodriguez</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">5/24/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">194</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">5/24/21</div><div class=\"cm-col\">5/24/21</div><div class=\"cm-col\">Hispanic</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">155710</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">15571</div><div class=\"cm-col cm-num\">6228.4</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">9342.6</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">6228.4</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">6726.67</div><div class=\"cm-col cm-num\">72</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">155710</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30</div><div class=\"cm-col\">Hans</div><div class=\"cm-col\">Wagner</div><div class=\"cm-col\">Hans Wagner</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">9/6/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">206</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">9/6/22</div><div class=\"cm-col\">9/6/22</div><div class=\"cm-col\">German</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\">Platform</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">205837.32</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">131947</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">126</div><div class=\"cm-col\">Elena</div><div class=\"cm-col\">Lopez</div><div class=\"cm-col\">Elena Lopez</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">11/1/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">12</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">11/1/21</div><div class=\"cm-col\">11/1/21</div><div class=\"cm-col\">Hispanic</div><div class=\"cm-col\">Remote-Payzone3</div><div class=\"cm-col\">Sr. Engineering Manager</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">Manager</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">153991.9</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">15399.19</div><div class=\"cm-col cm-num\">6159.68</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">9239.51</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">6159.68</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">6744.85</div><div class=\"cm-col cm-num\">73</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">133906</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 138, "lines_more": 137, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "S3imC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I still can't get it to work."}]}]}]}, {"ts": "1738166842.290299", "text": "I removed OTE amounts, paymix, annual ote etc", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "KYp9J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I removed OTE amounts, paymix, annual ote etc"}]}]}]}, {"ts": "1738167056.330399", "text": "there are some duplicate email addresses. Let me clean up. This is very old data.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "ibSu2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "there are some duplicate email addresses. Let me clean up. This is very old data."}]}]}]}, {"ts": "1738167136.753159", "text": "Yes, it's hard to manipulate the data in these environments. :confused:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "ZWXtQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, it's hard to manipulate the data in these environments. "}, {"type": "emoji", "name": "confused", "unicode": "1f615"}]}]}]}, {"ts": "1738167837.182299", "text": "<@U07EJ2LP44S> updated", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738165831.896369", "files": [{"id": "F08B7FFSG9X", "created": 1738167827, "timestamp": 1738167827, "name": "test - Sheet1 (1).csv", "title": "test - Sheet1 (1).csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": true, "size": 47951, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08B7FFSG9X/test_-_sheet1__1_.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08B7FFSG9X/download/test_-_sheet1__1_.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08B7FFSG9X/test_-_sheet1__1_.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08B7FFSG9X-aa0f50cea2", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08B7FFSG9X/test_-_sheet1__1_.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">243</div><div class=\"cm-col\">Aarav</div><div class=\"cm-col\">Patel</div><div class=\"cm-col\">Aarav Patel</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">7/8/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">199</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">7/6/21</div><div class=\"cm-col\">7/6/21</div><div class=\"cm-col\">Indian</div><div class=\"cm-col\">San Francisco</div><div class=\"cm-col\">Product Marketing Lead</div><div class=\"cm-col\">Marketing</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Communications</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Communications</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">132457.5</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col cm-num\">7947.45</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">126150</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">46</div><div class=\"cm-col\">Ahmed</div><div class=\"cm-col\">Khoury</div><div class=\"cm-col\">Ahmed Khoury</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">10/31/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">31</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">10/31/22</div><div class=\"cm-col\">10/31/22</div><div class=\"cm-col\">Arab</div><div class=\"cm-col\">Remote-Payzone3</div><div class=\"cm-col\">Head of Finance</div><div class=\"cm-col\">Finance &amp; Operations</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Finance</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Finance</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">167416</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">20</div><div class=\"cm-col cm-num\">33483.2</div><div class=\"cm-col cm-num\">13393.28</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">20089.92</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">13393.28</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">16875.53</div><div class=\"cm-col cm-num\">84</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">167416</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">141</div><div class=\"cm-col\">Aisha</div><div class=\"cm-col\">Nkosi</div><div class=\"cm-col\">Aisha Nkosi</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">8/26/19</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">21</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">8/26/19</div><div class=\"cm-col\">8/26/19</div><div class=\"cm-col\">African</div><div class=\"cm-col\">San Francisco</div><div class=\"cm-col\">Senior Executive Assistant</div><div class=\"cm-col\">Executive</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Executive Assistant</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Executive Assistant</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">158147.85</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col cm-num\">23722.18</div><div class=\"cm-col cm-num\">9488.87</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">14233.31</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">9488.87</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">13094.64</div><div class=\"cm-col cm-num\">92</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">150617</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">219</div><div class=\"cm-col\">Aisha</div><div class=\"cm-col\">Tanaka</div><div class=\"cm-col\">Aisha Tanaka</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">3/1/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">85</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">3/1/21</div><div class=\"cm-col\">3/1/21</div><div class=\"cm-col\">Japanese</div><div class=\"cm-col\">New York Office</div><div class=\"cm-col\">Legal Counsel</div><div class=\"cm-col\">Legal &amp; Policy</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Legal</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Legal</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">154112</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">6164.48</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">137600</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">166</div><div class=\"cm-col\">Aissatou</div><div class=\"cm-col\">Traor</div><div class=\"cm-col\">Aissatou Traor</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">10/3/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">11</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">10/3/22</div><div class=\"cm-col\">10/3/22</div><div class=\"cm-col\">African</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Developer Advocate</div><div class=\"cm-col\">Dev Tools</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Developer Relations</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Developer Relations</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">134300</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">5372</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">134300</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 138, "lines_more": 137, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "HvdkJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " updated"}]}]}]}, {"ts": "1738167950.217239", "text": "There one employee still OTE and corrected that from Org view edit.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "kuvlu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There one employee still OTE and corrected that from Org view edit."}]}]}]}, {"ts": "1738168792.864829", "text": "Thank you! I republished and variable pay is now gone.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "/oMjW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you! I republished and variable pay is now gone."}]}]}]}, {"ts": "1738168869.126539", "text": "Can this env be upgraded to go a bit faster when Im working with it?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "UPhVe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can this env be upgraded to go a bit faster when Im working with it?"}]}]}]}, {"ts": "1738169004.775629", "text": "how long it needs needed? it will add more to AWS cost.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "coLu8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "how long it needs needed? it will add more to AWS cost."}]}]}]}, {"ts": "1738169044.570909", "text": "Let me see if I can do without for now, I just got all my screenshots, I think.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "BLmt4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me see if I can do without for now, I just got all my screenshots, I think."}]}]}]}, {"ts": "1738169050.801399", "text": "But i cannot live demo with it if needed", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "KFKJd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "But i cannot live demo with it if needed"}]}]}]}, {"ts": "1738169058.314319", "text": "I might need it for when we do live training", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "or1Ip", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I might need it for when we do live training"}]}]}]}, {"ts": "1738169126.459639", "text": "Got it. when is that? Do you want me to upgrade now or you will let me know.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "wr0Wf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Got it. when is that? Do you want me to upgrade now or you will let me know."}]}]}]}, {"ts": "1738169541.657359", "text": "I will let you know. the training is a week from tomorrow", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "+TMnC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will let you know. the training is a week from tomorrow"}]}]}]}, {"ts": "1738169554.637799", "text": ":+1:", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738165831.896369", "blocks": [{"type": "rich_text", "block_id": "qF+xl", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "+1", "unicode": "1f44d"}]}]}]}], "created_at": "2025-05-22T22:00:49.346488"}