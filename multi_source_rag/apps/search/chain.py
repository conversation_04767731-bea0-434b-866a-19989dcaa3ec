"""
LangChain components for the search functionality.
"""

from typing import Any, Dict, List, Optional, Tuple

from apps.core.utils.llm import answer_question
from apps.core.utils.vectorstore import search_vector_store
from apps.documents.models import DocumentChunk
from langchain.schema import Document


class RAGChain:
    """
    Retrieval-Augmented Generation chain for the search functionality.
    """

    def __init__(
        self,
        collection_name: Optional[str] = None,
        tenant_id: Optional[int] = None,
        k: int = 5,
    ):
        """
        Initialize the RAG chain.

        Args:
            collection_name: Name of the collection to use
            tenant_id: ID of the tenant
            k: Number of results to return
        """
        self.collection_name = collection_name
        self.tenant_id = tenant_id
        self.k = k

    def retrieve(self, query: str) -> List[Tuple[Document, float]]:
        """
        Retrieve documents from the vector store.

        Args:
            query: Query to search for

        Returns:
            List[Tuple[Document, float]]: List of documents and their similarity scores
        """
        # Create metadata filter if tenant ID is provided
        metadata_filter = None
        if self.tenant_id:
            metadata_filter = {"tenant_id": self.tenant_id}

        # Search the vector store
        results = search_vector_store(
            query=query,
            collection_name=self.collection_name,
            metadata_filter=None,
            k=self.k,
        )

        return results

    def generate(self, query: str, context_docs: List[Document]) -> str:
        """
        Generate an answer based on the query and context documents.

        Args:
            query: Query to answer
            context_docs: List of context documents

        Returns:
            str: Generated answer
        """
        # Generate answer
        answer = answer_question(
            question=query, context_docs=context_docs, streaming=False
        )

        return answer

    def run(self, query: str) -> Dict[str, Any]:
        """
        Run the RAG chain.

        Args:
            query: Query to answer

        Returns:
            Dict[str, Any]: Result of the RAG chain
        """
        # Retrieve documents
        retrieval_results = self.retrieve(query)

        # Extract documents
        documents = [doc for doc, _ in retrieval_results]

        # Generate answer
        answer = self.generate(query, documents)

        # Create result
        result = {
            "query": query,
            "answer": answer,
            "documents": documents,
            "retrieval_results": retrieval_results,
        }

        return result

    def get_document_chunks(
        self, retrieval_results: List[Tuple[Document, float]]
    ) -> List[Dict[str, Any]]:
        """
        Get document chunks from retrieval results.

        Args:
            retrieval_results: List of retrieval results

        Returns:
            List[Dict[str, Any]]: List of document chunks
        """
        chunks = []

        for doc, score in retrieval_results:
            # Try different ways to identify the document chunk
            chunk_id = doc.metadata.get("chunk_id")
            document_id = doc.metadata.get("document_id")
            chunk_index = doc.metadata.get("chunk_index")

            if chunk_id:
                # Try to get document chunk directly by ID
                try:
                    chunk = DocumentChunk.objects.get(id=chunk_id)
                    chunks.append({"chunk": chunk, "score": score})
                    continue
                except DocumentChunk.DoesNotExist:
                    pass

            # Fall back to document_id + chunk_index if available
            if document_id and chunk_index is not None:
                try:
                    # Get document chunk from database
                    chunk = DocumentChunk.objects.get(
                        document_id=document_id, chunk_index=chunk_index
                    )

                    # Add chunk to list
                    chunks.append({"chunk": chunk, "score": score})
                except DocumentChunk.DoesNotExist:
                    pass

        return chunks
