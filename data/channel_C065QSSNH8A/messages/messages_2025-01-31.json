{"date": "2025-01-31", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1738342580.211129", "text": "<@U0690EB5JE5> <PERSON><PERSON> uploaded promotions with $0, and selected do not let managers edit. But they are still editable. Can you look?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738342580.211129", "reply_count": 12, "files": [{"id": "F08B18TVB5L", "created": 1738342575, "timestamp": 1738342575, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 332930, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08B18TVB5L/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08B18TVB5L/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08B18TVB5L-3752362021/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08B18TVB5L-3752362021/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08B18TVB5L-3752362021/image_360.png", "thumb_360_w": 360, "thumb_360_h": 202, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08B18TVB5L-3752362021/image_480.png", "thumb_480_w": 480, "thumb_480_h": 269, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08B18TVB5L-3752362021/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08B18TVB5L-3752362021/image_720.png", "thumb_720_w": 720, "thumb_720_h": 404, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08B18TVB5L-3752362021/image_800.png", "thumb_800_w": 800, "thumb_800_h": 449, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08B18TVB5L-3752362021/image_960.png", "thumb_960_w": 960, "thumb_960_h": 539, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08B18TVB5L-3752362021/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 575, "original_w": 2356, "original_h": 1322, "thumb_tiny": "AwAaADDS79aMeho70c+v6UAH40fjRz60fjQAfQ0c49TR+NGPegA70UUUAFBoooAKKKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08B18TVB5L/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08B18TVB5L-271ebb6a14", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "0Nq1g", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Valgenesis uploaded promotions with $0, and selected do not let managers edit. But they are still editable. Can you look?"}]}]}]}, {"ts": "1738342439.775899", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1738342207.341869", "text": "Yes it must be there but it’s compa ratio", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xca3O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes it must be there"}, {"type": "text", "text": " "}, {"type": "text", "text": "but "}, {"type": "text", "text": "it’s"}, {"type": "text", "text": " compa ratio"}]}]}]}, {"ts": "1738342136.961759", "text": "<@U0690EB5JE5> I am not seeing the range penetration in demo env. Can you pls enable it? I thought you did it for demo too after you enabled it for Tithely", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Jp9x5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am not seeing the range penetration in demo env. Can you pls enable it? I thought you did it for demo too after you enabled it for Tithely"}]}]}]}, {"ts": "1738338144.212329", "text": "<@U0690EB5JE5> Can you tell me what we're mapping paybands with in Curana? IE is it department and title? Can it just be title?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738338144.212329", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "eyRmq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you tell me what we're mapping paybands with in Curana? IE is it department and title? Can it just be title?"}]}]}]}, {"ts": "1738337929.251449", "text": "<@U0690EB5JE5> Trying to upload last raise date for Tith<PERSON>, getting a format error.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738337929.251449", "reply_count": 3, "files": [{"id": "F08BW71SS0Y", "created": 1738337927, "timestamp": 1738337927, "name": "TithelyLastRaiseDate.csv", "title": "TithelyLastRaiseDate.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 3096, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08BW71SS0Y/tithelylastraisedate.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08BW71SS0Y/download/tithelylastraisedate.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08BW71SS0Y/tithelylastraisedate.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08BW71SS0Y-9416dcdb32", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08BW71SS0Y/tithelylastraisedate.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,Last Raise Date\r\nU,234,03/25/2024\r\nU,326,09/13/2023\r\nU,167,03/25/2024\r\nU,172,04/08/2024\r\nU,57,04/01/2022\r\nU,13,09/23/2024\r\nU,332,03/25/2024\r\nU,232,03/25/2024\r\nU,134,03/25/2024\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">Last Raise Date</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">234</div><div class=\"cm-col\">03/25/2024</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">326</div><div class=\"cm-col\">09/13/2023</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">167</div><div class=\"cm-col\">03/25/2024</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">172</div><div class=\"cm-col\">04/08/2024</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">57</div><div class=\"cm-col\">04/01/2022</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">13</div><div class=\"cm-col\">09/23/2024</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">332</div><div class=\"cm-col\">03/25/2024</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">232</div><div class=\"cm-col\">03/25/2024</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">134</div><div class=\"cm-col\">03/25/2024</div></div></div>\n</div>\n", "lines": 172, "lines_more": 162, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "OFOnX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Trying to upload last raise date for <PERSON><PERSON><PERSON>, getting a format error."}]}]}]}, {"ts": "1738289199.027319", "text": "<@U07EJ2LP44S> will get these fixed today.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Cw7DO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " will get these fixed today."}]}]}]}, {"ts": "1738271341.229379", "text": "diversified issue 2 - the export of the merit table is not including target bonus % OR bonus award $. both critical fields for the export.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738271341.229379", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "yBYPE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "diversified issue 2 - the export of the merit table is not including target bonus % OR bonus award $. both critical fields for the export."}]}]}]}, {"ts": "1738271308.186779", "text": "<@U0690EB5JE5> two issues in diversified cycle. 1) the bonus amount I am inputting into the cycle is not the same as the bonus amount on the allocation and merit pages. it is a higher amount than the budget.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738271308.186779", "reply_count": 26, "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " two issues in diversified cycle. 1) the bonus amount I am inputting into the cycle is not the same as the bonus amount on the allocation and merit pages. it is a higher amount than the budget."}]}]}]}], "created_at": "2025-05-22T21:35:34.703163"}