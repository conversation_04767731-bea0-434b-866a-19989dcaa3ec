#!/usr/bin/env python3
"""
Quick performance test for optimized RAG system.
"""

import os
import sys
import django
import time

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.rag_service import RAGService
from apps.accounts.models import Tenant, User

def test_optimized_performance():
    """Test the optimized system performance."""
    print("🚀 Testing Optimized RAG Performance")
    print("=" * 60)
    
    # Initialize service
    tenant = Tenant.objects.get(slug='stride')
    user = User.objects.first()
    service = RAGService(user=user, tenant_slug=tenant.slug, user)
    
    # Test queries (simple to medium complexity)
    test_queries = [
        "List issues reported by <PERSON>",
        "What did Rachel mention?",
        "Show me bug reports",
    ]
    
    total_time = 0
    successful_queries = 0
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}: '{query}'")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            search_result, retrieved_docs = service.search(
                query_text=query,
                top_k=5,  # Reduced for faster testing
                min_relevance_score=0.1
            )
            
            end_time = time.time()
            duration = end_time - start_time
            total_time += duration
            successful_queries += 1
            
            # Check results
            citations = search_result.citations.all()
            citation_count = citations.count()
            
            print(f"⏱️  Duration: {duration:.2f}s")
            print(f"📊 Retrieved: {len(retrieved_docs)} docs")
            print(f"🔗 Citations: {citation_count}")
            print(f"📝 Answer: {len(search_result.generated_answer)} chars")
            
            # Show first citation
            if citation_count > 0:
                first_citation = citations.first()
                print(f"✅ First citation: {first_citation.document_chunk.document.title[:50]}...")
                if first_citation.document_chunk.profile:
                    print(f"👤 Profile: {first_citation.document_chunk.profile.display_name}")
            
            # Performance assessment
            if duration < 30:
                print("🎯 Performance: ✅ EXCELLENT")
            elif duration < 45:
                print("🎯 Performance: ✅ GOOD")
            elif duration < 60:
                print("🎯 Performance: ⚠️  ACCEPTABLE")
            else:
                print("🎯 Performance: ❌ NEEDS IMPROVEMENT")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            end_time = time.time()
            duration = end_time - start_time
            print(f"⏱️  Failed after: {duration:.2f}s")
    
    # Summary
    print(f"\n📊 PERFORMANCE SUMMARY")
    print("=" * 60)
    
    if successful_queries > 0:
        avg_time = total_time / successful_queries
        print(f"✅ Successful queries: {successful_queries}/{len(test_queries)}")
        print(f"⏱️  Average time: {avg_time:.2f}s")
        print(f"⏱️  Total time: {total_time:.2f}s")
        
        # Performance rating
        if avg_time < 30:
            print("🎯 Overall Performance: ✅ PRODUCTION READY")
        elif avg_time < 45:
            print("🎯 Overall Performance: ✅ GOOD FOR PRODUCTION")
        elif avg_time < 60:
            print("🎯 Overall Performance: ⚠️  ACCEPTABLE")
        else:
            print("🎯 Overall Performance: ❌ NEEDS MORE OPTIMIZATION")
    else:
        print("❌ No successful queries")

if __name__ == "__main__":
    test_optimized_performance()
