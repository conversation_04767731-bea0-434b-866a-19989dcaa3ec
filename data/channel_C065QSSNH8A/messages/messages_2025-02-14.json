{"date": "2025-02-14", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "**********.624009", "text": "Diversified is looking for a better way to view flagged employees, and their bonus award. So they would want to see the percentage being awarded inside the flagged employees report. Is this a change we can make?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.624009", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "Z34iv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified is looking for a better way to view flagged employees, and their bonus award. So they would want to see the percentage being awarded inside the flagged employees report. Is this a change we can make?"}]}]}]}, {"ts": "**********.253309", "text": "resharing: <https://curanahealthmip.stridehr.io/organization> is ready", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.144469", "subtype": "thread_broadcast", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "5N5yW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "resharing: "}, {"type": "link", "url": "https://curanahealthmip.stridehr.io/organization"}, {"type": "text", "text": " is ready"}]}]}]}, {"ts": "**********.025449", "text": "Just talked to <PERSON><PERSON><PERSON>. A few things.\n\n1. I updated ratings in the cycle builder and once I changed numbers I could not proceed. *The next button doesn't do anything now that the data is updated.* We are trying to put in the ratings I shared yesterday - I will also need this to work in the stridedemo environment.  (Also their ratings are all going to be the same regardless of location - do we still have to copy the ratings to every single country?) \n2. A few comments for TotalRewards - 1) they asked about a download, I confirmed it was coming. 2) *they asked to remove the compensation band info* 3) *they asked if we can add a percentage increase along with the $ increase* for the salary change info 3) they would like to add a ''Total increase over time' section, where it would display the total $/% increase since hire date. We thought putting this in the current comp band area would look nice. 4) they would like to add a couple more things to their benefits, like 401k matching. Also we discussed HSA/FSA in conversation and they have no match for that so its not a benefit.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.025449", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "tR4Jj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just talked to <PERSON><PERSON><PERSON>. A few things.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I updated ratings in the cycle builder and once I changed numbers I could not proceed. "}, {"type": "text", "text": "The next button doesn't do anything now that the data is updated.", "style": {"bold": true}}, {"type": "text", "text": " We are trying to put in the ratings I shared yesterday - I will also need this to work in the stridedemo environment.  (Also their ratings are all going to be the same regardless of location - do we still have to copy the ratings to every single country?) "}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n2. A few comments for TotalRewards - 1) they asked about a download, I confirmed it was coming. 2) "}, {"type": "text", "text": "they asked to remove the compensation band info ", "style": {"bold": true}}, {"type": "text", "text": "3) "}, {"type": "text", "text": "they asked if we can add a percentage increase along with the $ increase", "style": {"bold": true}}, {"type": "text", "text": " for the salary change info 3) they would like to add a ''Total increase over time' section, where it would display the total $/% increase since hire date. We thought putting this in the current comp band area would look nice. 4) they would like to add a couple more things to their benefits, like 401k matching. Also we discussed HSA/FSA in conversation and they have no match for that so its not a benefit."}]}]}]}], "created_at": "2025-05-22T21:35:34.713386"}