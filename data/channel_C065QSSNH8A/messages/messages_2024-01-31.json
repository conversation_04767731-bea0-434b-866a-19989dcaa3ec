{"date": "2024-01-31", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1706666954.334779", "text": "<@U0658EW4B8D> Can you share your Adjustment letters test &amp; pass/fail spreadsheet here?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706666954.334779", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "TYKur", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " Can you share your Adjustment letters test & pass/fail spreadsheet here?"}]}]}]}, {"ts": "1706666931.246259", "text": "Priorities for Eng for the next day:\n• :repeat: Password reset flow (<https://compiify.atlassian.net/browse/COM-2240|COM-2240>)\n• Adjustment Letter tickets in Neuroflow UAT (<https://compiify.atlassian.net/browse/COM-2145|COM-2145>) with the exception of <https://compiify.atlassian.net/browse/COM-2251|COM-2251> which does not have design yet\n• :repeat: Allow clicking recommended values to input (<https://compiify.atlassian.net/browse/COM-2013|COM-2013>)\nAfter these, continue with <https://compiify.atlassian.net/browse/COM-2087|Wave 3> from the top of the list, especially the Reports-related items.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4xjjS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Password reset flow ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2240", "text": "COM-2240"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Adjustment Letter tickets in Neuroflow UAT ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2145", "text": "COM-2145"}, {"type": "text", "text": ") with the exception of "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2251", "text": "COM-2251"}, {"type": "text", "text": " which does not have design yet"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Allow clicking recommended values to input ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2013", "text": "COM-2013"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAfter these, continue with "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " from the top of the list, especially the Reports-related items."}]}]}]}, {"ts": "1706662687.254479", "text": "Open to feedback: I've created a high-level \"<https://www.figma.com/file/HDEGIXOCpeSBahiqDzb0Vc/User-Flows%3A-Comp-Planning-Cycle?type=whiteboard&amp;node-id=0-1&amp;t=KpHOZkF1EwQnmUn6-0|User flow>\" to try and map how different roles in an organization are involved in a comp planning cycle. It may be a little simplified, but trying to capture some of the main tasks that each role needs to complete and how that fits into the context of what's happening around them with budgets, approvals, and deadlines.\n\nFeel free to add post-it note comments in the Figjam, or to just Slack me any feedback you have. I'm hoping this will help our UX designer ramp up more quickly on the concept of comp cycle workflows. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706662687.254479", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "OpJuI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Open to feedback: I've created a high-level \""}, {"type": "link", "url": "https://www.figma.com/file/HDEGIXOCpeSBahiqDzb0Vc/User-Flows%3A-Comp-Planning-Cycle?type=whiteboard&node-id=0-1&t=KpHOZkF1EwQnmUn6-0", "text": "User flow"}, {"type": "text", "text": "\" to try and map how different roles in an organization are involved in a comp planning cycle. It may be a little simplified, but trying to capture some of the main tasks that each role needs to complete and how that fits into the context of what's happening around them with budgets, approvals, and deadlines.\n\nFeel free to add post-it note comments in the Figjam, or to just Slack me any feedback you have. I'm hoping this will help our UX designer ramp up more quickly on the concept of comp cycle workflows. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1706648206.601819", "text": "<@U04DKEFP1K8> I think we'll end up needing to support some bulk actions for generating, releasing, and downloading adjustment letters. Was anything started along those lines, or do we need to spec it out from scratch?\n\nI also think it'll need filtering in the employee list (rather than only having search for a single employee) so that someone could generate/download letters for an entire team or department.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706648206.601819", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "vaL6W", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I think we'll end up needing to support some bulk actions for generating, releasing, and downloading adjustment letters. Was anything started along those lines, or do we need to spec it out from scratch?\n\nI also think it'll need filtering in the employee list (rather than only having search for a single employee) so that someone could generate/download letters for an entire team or department."}]}]}]}, {"ts": "1706645960.188759", "text": "Doing some light testing on adjustment letters myself, a few things I noticed:\n• \"Deleting\" an adjustment letter doesn't seem to remove the icons for the previously generated letter, and clicking those will produce an error\n• At least one instance has a broken image for the logo\n• The formatting of the monetary values should include comma separators\n• The formatting of the dates might look better using \"Month DD, YYYY\" instead of \"MM-DD-YYYY\"\n• I can't tell how much of my template edits were used, because I see some of my changes and formatting applied, but the line breaks are still different from what I'd input.\nExamples attached -- <PERSON><PERSON> was working for one but not the other.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706645960.188759", "reply_count": 3, "files": [{"id": "F06G5MYBWMC", "created": 1706645944, "timestamp": 1706645944, "name": "salary_bonus_1706645582066.pdf", "title": "salary_bonus_1706645582066.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 38588, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06G5MYBWMC/salary_bonus_1706645582066.pdf", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06G5MYBWMC/download/salary_bonus_1706645582066.pdf", "media_display_type": "unknown", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G5MYBWMC-2c838d299a/salary_bonus_1706645582066_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06G5MYBWMC/salary_bonus_1706645582066.pdf", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06G5MYBWMC-bee0cc79a5", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06H14WJPQQ", "created": 1706645946, "timestamp": 1706645946, "name": "bonus_1706645716134.pdf", "title": "bonus_1706645716134.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 25724, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06H14WJPQQ/bonus_1706645716134.pdf", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06H14WJPQQ/download/bonus_1706645716134.pdf", "media_display_type": "unknown", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F06H14WJPQQ-2739e0a183/bonus_1706645716134_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06H14WJPQQ/bonus_1706645716134.pdf", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06H14WJPQQ-6e32d8e361", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ZOTmq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Doing some light testing on adjustment letters myself, a few things I noticed:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "\"Deleting\" an adjustment letter doesn't seem to remove the icons for the previously generated letter, and clicking those will produce an error"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "At least one instance has a broken image for the logo"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The formatting of the monetary values should include comma separators"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The formatting of the dates might look better using \"Month DD, YYYY\" instead of \"MM-DD-YYYY\""}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "I can't tell how much of my template edits were used, because I see some of my changes and formatting applied, but the line breaks are still different from what I'd input."}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nExamples attached -- <PERSON><PERSON> was working for one but not the other."}]}]}]}], "created_at": "2025-05-22T21:35:34.586288"}