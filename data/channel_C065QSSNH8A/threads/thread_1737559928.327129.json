{"thread_ts": "1737559928.327129", "channel_id": "C065QSSNH8A", "reply_count": 14, "replies": [{"ts": "1737559984.074219", "text": "I removed the columns in this version b/c I'm pretty sure you said this version of the system doesn't require extraneous columns.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737559928.327129", "blocks": [{"type": "rich_text", "block_id": "IgEvn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I removed the columns in this version b/c I'm pretty sure you said this version of the system doesn't require extraneous columns."}]}]}]}, {"ts": "1737560144.247759", "text": "Will take a look. ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737559928.327129", "blocks": [{"type": "rich_text", "block_id": "q9b4x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take a look. "}]}]}]}, {"ts": "1737561763.687799", "text": "<@U07EJ2LP44S> The issue is the file has currency but not amount for target bonus. Removing currency column will fix the issue. Will get error message fixed.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737559928.327129", "edited": {"user": "U0690EB5JE5", "ts": "1737562976.000000"}, "blocks": [{"type": "rich_text", "block_id": "1YOWV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " The issue is the file has currency but not amount for target bonus. Removing currency column will fix the issue. Will get error message fixed."}]}]}]}, {"ts": "1737561800.660579", "text": "This worked locally for me,", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737559928.327129", "files": [{"id": "F089CMR8D55", "created": 1737561790, "timestamp": 1737561790, "name": "Updated - DivEn_18emp (2).csv", "title": "Updated - DivEn_18emp (2).csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": true, "size": 560, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089CMR8D55/updated_-_diven_18emp__2_.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089CMR8D55/download/updated_-_diven_18emp__2_.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089CMR8D55/updated_-_diven_18emp__2_.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089CMR8D55-8e1d6bb10b", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F089CMR8D55/updated_-_diven_18emp__2_.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,Annual Salary Currency,Annual Salary Amount,Target Bonus (%)\r\nU,30758,USD,350000.04,125\r\nU,29192,USD,256035.26,50\r\nU,19081,USD,270500.1,50\r\nU,16515,USD,350000.04,125\r\nU,16590,USD,229999.9,50\r\nU,16491,USD,780000,175\r\nU,16470,USD,500000.02,150\r\nU,26410,USD,260000,50\r\nU,16369,USD,305000.02,75\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Target Bonus (%)</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30758</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">350000.04</div><div class=\"cm-col cm-num\">125</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">29192</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">256035.26</div><div class=\"cm-col cm-num\">50</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">19081</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">270500.1</div><div class=\"cm-col cm-num\">50</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16515</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">350000.04</div><div class=\"cm-col cm-num\">125</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16590</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">229999.9</div><div class=\"cm-col cm-num\">50</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16491</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">780000</div><div class=\"cm-col cm-num\">175</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16470</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">500000.02</div><div class=\"cm-col cm-num\">150</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">26410</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">260000</div><div class=\"cm-col cm-num\">50</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16369</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">305000.02</div><div class=\"cm-col cm-num\">75</div></div></div>\n</div>\n", "lines": 19, "lines_more": 9, "preview_is_truncated": false, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "UbuBl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This worked locally for me,"}]}]}]}, {"ts": "1737569898.382679", "text": "<@U0690EB5JE5> This is still not working. I deleted the currency field, did not work. Then I deleted the variable fields to just make sure that wasn't causing an issue, it still didn't work.\n\nWe're up against the wire here and need to get the data correct asap.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737559928.327129", "files": [{"id": "F089U3Z7SMQ", "created": 1737569897, "timestamp": 1737569897, "name": "DivEn_18empNoCurrency.csv", "title": "DivEn_18empNoCurrency.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 4512, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089U3Z7SMQ/diven_18empnocurrency.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089U3Z7SMQ/download/diven_18empnocurrency.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089U3Z7SMQ/diven_18empnocurrency.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089U3Z7SMQ-115f2a8c2e", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F089U3Z7SMQ/diven_18empnocurrency.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Target Bonus (%),,,,,,,,,,,,,,Pay Mix\r\nU,30758,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,<EMAIL>,M,1/10/24,US,16491,,Active,1/10/24,,WHITE,AL,EVP,BusinessDev:Bham:R,,,M,8,Yes,,FULL_TIME,,N/A,,,,,,,Regular,N/A,,,,,,USD,350000.04,125,,,,,,,,,,,,,,\r\n<PERSON>,29192,<PERSON>,<PERSON>,<PERSON> <PERSON>,d<PERSON><PERSON>@dgoc...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30758</div><div class=\"cm-col\">Michael</div><div class=\"cm-col\">Rigg</div><div class=\"cm-col\">Michael Rigg</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">1/10/24</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16491</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/10/24</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">AL</div><div class=\"cm-col\">EVP</div><div class=\"cm-col\">BusinessDev:Bham:R</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">350000.04</div><div class=\"cm-col cm-num\">125</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">29192</div><div class=\"cm-col\">Douglas</div><div class=\"cm-col\">Kris</div><div class=\"cm-col\">Douglas Kris</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">7/19/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16470</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">7/19/22</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">AL</div><div class=\"cm-col\">SVP</div><div class=\"cm-col\">InvstRel:Bham:R</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">7</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">256035.26</div><div class=\"cm-col cm-num\">50</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">19081</div><div class=\"cm-col\">Todd</div><div class=\"cm-col\">Tetrick</div><div class=\"cm-col\">Todd Tetrick</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">6/8/20</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16369</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">6/8/20</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">WV</div><div class=\"cm-col\">SVP</div><div class=\"cm-col\">Admin:Charleston</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">7</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">270500.1</div><div class=\"cm-col cm-num\">50</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16515</div><div class=\"cm-col\">Mark</div><div class=\"cm-col\">Kirkendall</div><div class=\"cm-col\">Mark Kirkendall</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">5/20/19</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16491</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">5/20/19</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">AL</div><div class=\"cm-col\">EVP &amp; CHRO</div><div class=\"cm-col\">Human Resources:Bham:R</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">350000.04</div><div class=\"cm-col cm-num\">125</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16590</div><div class=\"cm-col\">Teresa</div><div class=\"cm-col\">Odom</div><div class=\"cm-col\">Teresa Odom</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">3/11/19</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16470</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">3/11/19</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">AL</div><div class=\"cm-col\">SVP</div><div class=\"cm-col\">Investor Relations:Bham</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">7</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">229999.9</div><div class=\"cm-col cm-num\">50</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16491</div><div class=\"cm-col\">Robert</div><div class=\"cm-col\">Hutson</div><div class=\"cm-col\">Robert Hutson</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">1/1/01</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">555</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/1/01</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">AL</div><div class=\"cm-col\">CEO</div><div class=\"cm-col\">Executive:Bham</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">9</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">780000</div><div class=\"cm-col cm-num\">175</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16470</div><div class=\"cm-col\">Bradley</div><div class=\"cm-col\">Gray</div><div class=\"cm-col\">Bradley Gray</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">10/24/16</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16491</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">10/24/16</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">AL</div><div class=\"cm-col\">PRESIDENT &amp; CFO</div><div class=\"cm-col\">Executive:Bham</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">500000.02</div><div class=\"cm-col cm-num\">150</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 19, "lines_more": 16, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "dPtca", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " This is still not working. I deleted the currency field, did not work. Then I deleted the variable fields to just make sure that wasn't causing an issue, it still didn't work.\n\nWe're up against the wire here and need to get the data correct asap."}]}]}]}, {"ts": "1737592722.491229", "text": "I should have been little more clearer, You could have just used the file I shared, just keeping the fields to update. It’s fine, I will take care of it.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737559928.327129", "blocks": [{"type": "rich_text", "block_id": "ydUn6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I should have been little more clearer, You could have just used the file I shared, just keeping the fields to update. "}, {"type": "text", "text": "It’s"}, {"type": "text", "text": " fine, I will take care of it."}]}]}]}, {"ts": "1737647432.317129", "text": "I tried uploading your exact file; I just downloaded from here, and uploaded to Diversified. Still getting an error.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737559928.327129", "files": [{"id": "F08ACD4S03B", "created": 1737647426, "timestamp": 1737647426, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 107711, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08ACD4S03B/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08ACD4S03B/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ACD4S03B-b3d3ad02af/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ACD4S03B-b3d3ad02af/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ACD4S03B-b3d3ad02af/image_360.png", "thumb_360_w": 360, "thumb_360_h": 179, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ACD4S03B-b3d3ad02af/image_480.png", "thumb_480_w": 480, "thumb_480_h": 238, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ACD4S03B-b3d3ad02af/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ACD4S03B-b3d3ad02af/image_720.png", "thumb_720_w": 720, "thumb_720_h": 357, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ACD4S03B-b3d3ad02af/image_800.png", "thumb_800_w": 800, "thumb_800_h": 397, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ACD4S03B-b3d3ad02af/image_960.png", "thumb_960_w": 960, "thumb_960_h": 476, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ACD4S03B-b3d3ad02af/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 508, "original_w": 1750, "original_h": 868, "thumb_tiny": "AwAXADC1QKT8TS0AKq7mAzin+V71GCQeDS7m/vGgB3lgsQG5HUUrR4UnNM3N/eNG5sYLE0ANzS0neloAKKKQ0ALRQKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08ACD4S03B/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08ACD4S03B-53e65ff913", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "JUar8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I tried uploading your exact file; I just downloaded from here, and uploaded to Diversified. Still getting an error."}]}]}]}, {"ts": "1737647462.342169", "text": "It looks like there's only 6 left that are wrong after that last upload so I'll manually change them", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737559928.327129", "blocks": [{"type": "rich_text", "block_id": "8xakw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It looks like there's only 6 left that are wrong after that last upload so I'll manually change them"}]}]}]}, {"ts": "1737647555.317649", "text": "Let me share the file I uploaded. You can just keep the columns you want to update", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737559928.327129", "blocks": [{"type": "rich_text", "block_id": "g0Qf6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me share the file I uploaded. You can just keep the columns you want to update"}]}]}]}, {"ts": "1737647719.895439", "text": "", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737559928.327129", "files": [{"id": "F089T4VB16J", "created": 1737647718, "timestamp": 1737647718, "name": "Updated - Diven_Bonus (1).csv", "title": "Updated - Diven_Bonus (1).csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": true, "size": 1348, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089T4VB16J/updated_-_diven_bonus__1_.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089T4VB16J/download/updated_-_diven_bonus__1_.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089T4VB16J/updated_-_diven_bonus__1_.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089T4VB16J-75cf0a72f4", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F089T4VB16J/updated_-_diven_bonus__1_.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,Target Bonus (%),Annual Salary Currency,Annual Salary Amount\r\nU,8390,15,USD,98420.14\r\nU,10364,30,USD,201304.22\r\nU,11456,15,USD,107890.90\r\nU,11473,15,USD,132754.96\r\nU,11554,15,USD,96762.64\r\nU,16368,15,USD,115000.08\r\nU,16371,30,USD,226600.14\r\nU,16393,30,USD,180000.08\r\nU,16401,25,USD,152424.74\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">8390</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">98420.14</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">10364</div><div class=\"cm-col cm-num\">30</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">201304.22</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">11456</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">107890.90</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">11473</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">132754.96</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">11554</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">96762.64</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16368</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">115000.08</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16371</div><div class=\"cm-col cm-num\">30</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">226600.14</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16393</div><div class=\"cm-col cm-num\">30</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">180000.08</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16401</div><div class=\"cm-col cm-num\">25</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">152424.74</div></div></div>\n</div>\n", "lines": 49, "lines_more": 39, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}]}, {"ts": "1737647997.867999", "text": "<@U07EJ2LP44S> share the remaining employees, I will update.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737559928.327129", "blocks": [{"type": "rich_text", "block_id": "McYUj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " share the remaining employees, I will update."}]}]}]}, {"ts": "1737648207.739419", "text": "It will not upload again :confused: Here's the file.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737559928.327129", "files": [{"id": "F089X0FDL4D", "created": 1737648206, "timestamp": 1737648206, "name": "DivFinal6.csv", "title": "DivFinal6.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 276, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089X0FDL4D/divfinal6.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089X0FDL4D/download/divfinal6.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089X0FDL4D/divfinal6.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089X0FDL4D-8d991f7433", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F089X0FDL4D/divfinal6.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,Target Bonus (%),Annual Salary Currency,Annual Salary Amount\r\nU,16369,75,USD,\"305,000.02\"\r\nU,16460,50,USD,\"258,337.30\"\r\nU,16590,50,USD,\"229,999.90\"\r\nU,17710,50,USD,\"260,999.96\"\r\nU,26410,50,USD,\"260,000.00\"\r\nU,29192,50,USD,\"256,035.26\"", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16369</div><div class=\"cm-col cm-num\">75</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">305,000.02</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16460</div><div class=\"cm-col cm-num\">50</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">258,337.30</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">16590</div><div class=\"cm-col cm-num\">50</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">229,999.90</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">17710</div><div class=\"cm-col cm-num\">50</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">260,999.96</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">26410</div><div class=\"cm-col cm-num\">50</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">260,000.00</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">29192</div><div class=\"cm-col cm-num\">50</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">256,035.26</div></div></div>\n</div>\n", "lines": 7, "lines_more": 0, "preview_is_truncated": false, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "RtIAP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It will not upload again "}, {"type": "emoji", "name": "confused", "unicode": "1f615"}, {"type": "text", "text": " Here's the file."}]}]}]}, {"ts": "1737648714.929949", "text": "<@U07EJ2LP44S> Done. There is  a comma in the amount values.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737559928.327129", "blocks": [{"type": "rich_text", "block_id": "/cXao", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Done. There is  a comma in the amount values."}]}]}]}, {"ts": "1737648791.712939", "text": "Share all uploads with me going forward. I will take care of it. Loop me in the customer email where these updates are being shared.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737559928.327129", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "3VQFE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Share all uploads with me going forward. I will take care of it. Loop me in the customer email where these updates are being shared."}]}]}]}], "created_at": "2025-05-22T22:00:49.351108"}