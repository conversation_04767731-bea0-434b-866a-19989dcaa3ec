/* Main styles for Multi-Source RAG System */

/* Global styles */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.footer {
    margin-top: auto;
}

/* Search styles */
.search-form {
    max-width: 800px;
    margin: 0 auto;
}

.search-results {
    margin-top: 2rem;
}

.source-item {
    border-left: 3px solid #0d6efd;
    padding-left: 1rem;
    margin-bottom: 1rem;
}

/* Conversation styles */
.conversation-container {
    max-height: 600px;
    overflow-y: auto;
}

.message {
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
}

.message-user {
    background-color: #f8f9fa;
    margin-left: 2rem;
}

.message-assistant {
    background-color: #e9ecef;
    margin-right: 2rem;
}

/* Document styles */
.document-card {
    height: 100%;
    transition: transform 0.2s;
}

.document-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Source styles */
.source-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.5rem;
}

/* Loading indicator */
.loading-spinner {
    display: none;
    text-align: center;
    margin: 2rem 0;
}

.loading-spinner.active {
    display: block;
}

/* Citation styles */
.citation-badge {
    display: inline-block;
    background-color: #0d6efd;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    margin-right: 5px;
    cursor: pointer;
    font-size: 0.8rem;
}

.citation-highlight {
    background-color: rgba(13, 110, 253, 0.2);
    padding: 2px 4px;
    border-radius: 3px;
}

.relevance-score {
    width: 100px;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    position: relative;
    display: inline-block;
    margin: 0 10px;
}

.relevance-indicator {
    position: absolute;
    top: 0;
    left: 0;
    height: 8px;
    width: 8px;
    background-color: #0d6efd;
    border-radius: 50%;
    transform: translateX(-4px);
}

/* Error message styles */
.error-message .message-content {
    padding: 0;
}

.error-message .alert {
    margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .message-user, .message-assistant {
        margin-left: 0;
        margin-right: 0;
    }
}
