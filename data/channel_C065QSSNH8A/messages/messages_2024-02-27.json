{"date": "2024-02-27", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1709054258.882199", "text": "Checked in on SDF use this morning. 3 users logged in so far today. <PERSON> has completed almost all of his inputs, but left 1 open. <PERSON> is the first one to input \"round numbers\" in the salary increase (\"$8,000\", \"$7,500\") rather than putting \"round percents\" for everyone (\"4.00%\", \"6.50%\") as the other managers have done so far.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709054258.882199", "reply_count": 5, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "s3fF1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Checked in on SDF use this morning. 3 users logged in so far today. <PERSON> has completed almost all of his inputs, but left 1 open. <PERSON> is the first one to input \"round numbers\" in the salary increase (\"$8,000\", \"$7,500\") rather than putting \"round percents\" for everyone (\"4.00%\", \"6.50%\") as the other managers have done so far."}]}]}]}, {"ts": "1708986479.501229", "text": "we haven't heard from <PERSON> yet so I am hoping \" no news is good news\"", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1708986479.501229", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "jG83h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we haven't heard from <PERSON> yet so I am hoping \" no news is good news\""}]}]}]}, {"ts": "1708984354.302929", "text": "<@U0690EB5JE5> I know we chatted a bit this morning about the data model and how our experience with SDF is putting that to the test. Here's another example (<https://compiify.atlassian.net/browse/COM-2418|COM-2418>) I found today, where we may need to map out what we treat as \"source of truth\" and what level of precision (how many decimal places) we use on the backend to keep things from shifting later.\n\n(Posting in this channel so <@U04DKEFP1K8> has some visibility to the discussion :slightly_smiling_face: )", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708984354.302929", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "A5jo6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I know we chatted a bit this morning about the data model and how our experience with SDF is putting that to the test. Here's another example ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2418", "text": "COM-2418"}, {"type": "text", "text": ") I found today, where we may need to map out what we treat as \"source of truth\" and what level of precision (how many decimal places) we use on the backend to keep things from shifting later.\n\n(Posting in this channel so "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " has some visibility to the discussion "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " )"}]}]}]}, {"ts": "1708972853.149109", "text": "So after live budget updates were merged managers literally do not need to change the review status from pending review to reviewed. How do we convey this message to them ( just use review all when they are done)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708972853.149109", "reply_count": 6, "edited": {"user": "U04DKEFP1K8", "ts": "1708972873.000000"}, "blocks": [{"type": "rich_text", "block_id": "fu+Xk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So after live budget updates were merged managers literally do not need to change the review status from pending review to reviewed. How do we convey this message to them ( just use review all when they are done)"}]}]}]}, {"ts": "1708972699.959199", "text": "Here's a promising sign - SDF users are logging back in today to their prod environment... hope the updates hold strong!:crossed_fingers:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "crossed_fingers", "users": ["U04DKEFP1K8", "U04DS2MBWP4", "U0690EB5JE5"], "count": 3}, {"name": "partyparrot", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06LRUETF44", "created": 1708972697, "timestamp": 1708972697, "name": "Screenshot 2024-02-26 at 10.37.04 AM.png", "title": "Screenshot 2024-02-26 at 10.37.04 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 134387, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06LRUETF44/screenshot_2024-02-26_at_10.37.04___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06LRUETF44/download/screenshot_2024-02-26_at_10.37.04___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_360.png", "thumb_360_w": 360, "thumb_360_h": 354, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_480.png", "thumb_480_w": 480, "thumb_480_h": 471, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_720.png", "thumb_720_w": 720, "thumb_720_h": 707, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_800.png", "thumb_800_w": 800, "thumb_800_h": 786, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06LRUETF44-d7acc91964/screenshot_2024-02-26_at_10.37.04___am_960.png", "thumb_960_w": 960, "thumb_960_h": 943, "original_w": 1014, "original_h": 996, "thumb_tiny": "AwAvADDToopMZ60ALRSDpTW69qAH0UxQM8EU+gBD0pabyTQOuKAF9qYW+bGcD604dTgU3BH8NACgjPB/WnA80zB/u0uCewFMY+iiikIKYevb86fTSD6j8qAEHB7fnTvSk2n1H5UoB7mgD//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06LRUETF44/screenshot_2024-02-26_at_10.37.04___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06LRUETF44-f8a8ee4667", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "bN3hc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here's a promising sign - SDF users are logging back in today to their prod environment... hope the updates hold strong!"}, {"type": "emoji", "name": "crossed_fingers", "unicode": "1f91e"}]}]}]}], "created_at": "2025-05-22T21:35:34.614905"}