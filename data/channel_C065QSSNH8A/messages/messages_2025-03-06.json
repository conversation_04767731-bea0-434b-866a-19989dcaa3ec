{"date": "2025-03-06", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1741285260.775169", "text": "<@U07EJ2LP44S> For Tithely. I am not seeing any planners except root emp and <PERSON> under My Tasks. Is that expected?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741285260.775169", "reply_count": 4, "files": [{"id": "F08H7FQLKGQ", "created": 1741285208, "timestamp": 1741285208, "name": "Screenshot 2025-03-06 at 10.19.50 AM.png", "title": "Screenshot 2025-03-06 at 10.19.50 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 193544, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08H7FQLKGQ/screenshot_2025-03-06_at_10.19.50___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08H7FQLKGQ/download/screenshot_2025-03-06_at_10.19.50___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_360.png", "thumb_360_w": 360, "thumb_360_h": 134, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_480.png", "thumb_480_w": 480, "thumb_480_h": 179, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_720.png", "thumb_720_w": 720, "thumb_720_h": 269, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_800.png", "thumb_800_w": 800, "thumb_800_h": 299, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_960.png", "thumb_960_w": 960, "thumb_960_h": 359, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H7FQLKGQ-f70508f43f/screenshot_2025-03-06_at_10.19.50___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 383, "original_w": 2698, "original_h": 1008, "thumb_tiny": "AwARADDRI560oHrzQaO3SgBaKQUtABRRRQA00NQaGoAUdKWkHSloAKKKKAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08H7FQLKGQ/screenshot_2025-03-06_at_10.19.50___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08H7FQLKGQ-2d5fc6f9a8", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Z/l9X", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " For Tithely. I am not seeing any planners except root emp and <PERSON> under My Tasks. Is that expected?"}]}]}]}, {"ts": "1741274110.605149", "text": "<@U07M6QKHUC9> This is a case we have hit for the first time. We have the fix but need to test a bit more before deployment for regression. I will get some time my morning and will get it fixed that time. Please buy another day for this fix. The issue due to HireDate being greater then cut-off date in eligibility rules which is an exception system never supported this case.\nWe have to handle this exception in case of proration. Proration is set to 0 due to this scenario and adjustments have been calculated to 0.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741220267.544949", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1741274519.000000"}, "blocks": [{"type": "rich_text", "block_id": "9gF+X", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " This is a case we have hit for the first time. We have the fix but need to test a bit more before deployment for regression. I will get some time my morning and will get it fixed that time. Please buy another day for this fix. The issue due to HireDate being greater then cut-off date in eligibility rules which is an exception system never supported this case.\nWe have to handle this exception in case of proration. Proration is set to 0 due to this scenario and adjustments have been calculated to 0."}]}]}]}, {"ts": "1741274017.744759", "text": "<@U07EJ2LP44S> This automatically got fixed when I just refreshed payband identifiers to trigger band calculation. Could you please confirm if things look good now. It looks fine to me now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741197243.744769", "subtype": "thread_broadcast", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ERz5z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This automatically got fixed when I just refreshed payband identifiers to trigger band calculation. Could you please confirm if things look good now. It looks fine to me now."}]}]}]}, {"ts": "1741220267.544949", "text": "<@U0690EB5JE5> just forwarded you an email from curana? Can you please address it?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741220267.544949", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "4xmbH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " just forwarded you an email from curana? Can you please address it?"}]}]}]}, {"ts": "1741209936.928769", "text": "<@U0690EB5JE5> can we pls add this emp to Curana merit cycle only?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741209936.928769", "reply_count": 4, "files": [{"id": "F08GF7CEG4C", "created": 1741209924, "timestamp": 1741209924, "name": "<PERSON> Add_Employees_Curana-Stride 01.23 (1) (1).xlsx", "title": "<PERSON> Add_Employees_Curana-Stride 01.23 (1) (1).xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 973497, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GF7CEG4C/carlson_add_employees_curana-stride_01.23__1___1_.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GF7CEG4C/download/carlson_add_employees_curana-stride_01.23__1___1_.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GF7CEG4C-c0c020be54/carlson_add_employees_curana-stride_01.23__1___1__converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GF7CEG4C-c0c020be54/carlson_add_employees_curana-stride_01.23__1___1__thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GF7CEG4C/carlson_add_employees_curana-stride_01.23__1___1_.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GF7CEG4C-2ebc79486e", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "DgbHN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we pls add this emp to Curana merit cycle only?"}]}]}]}], "created_at": "2025-05-22T21:35:34.715274"}