{"date": "2024-07-16", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1721144582.696719", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> Lets meet at 9:15 am pst", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "mNG71", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Lets meet at 9:15 am pst"}]}]}]}, {"ts": "1721143870.697089", "text": "I thought we<PERSON> cancelled it", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EvNqp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I thought we<PERSON> cancelled it"}]}]}]}, {"ts": "1721143837.460839", "text": "Just wrapped alaya care implementation call, i will be 10 -15 minutes late to today's eng meeting ( kid drop off running late)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EGDv3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just wrapped alaya care implementation call, i will be 10 -15 minutes late to today's eng meeting ( kid drop off running late)"}]}]}]}, {"ts": "1721112540.145139", "text": "Hi <@U04DKEFP1K8> I have created ticket for Vercara bonus (non-OTE) requirements\n<https://compiify.atlassian.net/browse/COM-3427>\nPlease take a look and adde any details missing.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13488::7bd4f7d0433f11efa754f97f9366b9b1", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3427?atlOrigin=eyJpIjoiODYzZGIwNDRiZWVjNDFlYzgwZjU1ODU5Y2MxMDdiMjAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3427 Allow Employee Level bonus components>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13488::7bd4f7d2433f11efa754f97f9366b9b1", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2a19465d953b8640b8f3558fd4f15da9?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13488::7bd4f7d1433f11efa754f97f9366b9b1", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13488\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13488\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3427", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "u+MfO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hi "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I have created ticket for Vercara bonus (non-OTE) requirements\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3427"}, {"type": "text", "text": "\nPlease take a look and adde any details missing."}]}]}]}, {"ts": "1721091715.057629", "text": "<!here> is the update from today\n\nCainwatters- salary bands fixes looks good, there are still some unresolved issues from reports which i will connect with mahesh durin gthe standup.\nNauto testing updates are here <https://docs.google.com/document/d/1SLa4vJWY7NF8OqvVsT1bUjGek1QAlADusolsW8gHObM/edit>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1721091715.057629", "reply_count": 2, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "vJp29", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " is the update from today\n\nCainwatters- salary bands fixes looks good, there are still some unresolved issues from reports which i will connect with mahesh durin gthe standup.\nNauto testing updates are here "}, {"type": "link", "url": "https://docs.google.com/document/d/1SLa4vJWY7NF8OqvVsT1bUjGek1QAlADusolsW8gHObM/edit"}]}]}]}], "created_at": "2025-05-22T21:35:34.619359"}