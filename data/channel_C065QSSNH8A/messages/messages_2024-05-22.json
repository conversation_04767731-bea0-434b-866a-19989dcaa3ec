{"date": "2024-05-22", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1716330842.420499", "text": "<@U065H3M6WJV> Can you please review <PERSON><PERSON>'s in the sheet titled \"Final To-Do's\" in this document <https://docs.google.com/spreadsheets/d/1iEM2qLPPZ6CXxIsCNmzwZ41rqtAViwEYK9ZibQnpWtM/edit#gid=0>.  If any of the item is not immediately required by June 15th please add your comment in column \"Notes / Comment\"\n\nNote:\n1. These are subset of currently pending To-Do's that engineering needs to address. I have also attached all pending To-Do's in another sheet titled \"All To-Do's\".\nOnce you have reviewed i will add a label to all the jira's and bring up a dashboard that folks in channel can track on a daily basis.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1716330842.420499", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "lKERL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Can you please review <PERSON><PERSON>'s in the sheet titled \"Final To-Do's\" in this document "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1iEM2qLPPZ6CXxIsCNmzwZ41rqtAViwEYK9ZibQnpWtM/edit#gid=0"}, {"type": "text", "text": ".  If any of the item is not immediately required by June 15th please add your comment in column \"Notes / Comment\"\n\nNote:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "These are subset of currently pending To-Do's that engineering needs to address. I have also attached all pending To-Do's in another sheet titled \"All To-Do's\"."}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nOnce you have reviewed i will add a label to all the jira's and bring up a dashboard that folks in channel can track on a daily basis."}]}]}]}], "created_at": "2025-05-22T21:35:34.599719"}