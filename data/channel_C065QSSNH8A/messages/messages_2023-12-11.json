{"date": "2023-12-11", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1702317696.513439", "text": "yep responding to her. I think we all could use some time bacl", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nDwZX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yep responding to her. I think we all could use some time bacl"}]}]}]}, {"ts": "1702317642.870999", "text": "I saw <PERSON><PERSON>'s note about canceling / rescheduling -- I'm planning to reuse the time unless we still need to get together on that topic (just Compiify folks) ? <@U04DS2MBWP4>", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jg32f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I saw <PERSON><PERSON>'s note about canceling / rescheduling -- I'm planning to reuse the time unless we still need to get together on that topic (just Compiify folks) ? "}, {"type": "user", "user_id": "U04DS2MBWP4"}]}]}]}, {"ts": "1702307869.362879", "text": "Do we have a timeline and plan of action for DA? When are we handing them over the pre-production environment?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702307869.362879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "c9dyA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we have a timeline and plan of action for DA? When are we handing them over the pre-production environment?"}]}]}]}, {"ts": "1702297425.081069", "text": "<!here> Here is an update on implementing Digital Asset workflow within Compiify\nThere are 2 tasks that have been created\n1. Managing a cycle without pay bands information ( 4 issue already reported during today's testing <https://compiify.atlassian.net/browse/COM-1859>)\n2. Managing a cycle with partial pay bands information  ( Issue for this use case will be tracked here <https://compiify.atlassian.net/browse/COM-1866>)", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:11920::21622680982011ee96f59110f874eb6e", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-1859?atlOrigin=eyJpIjoiMDdlNjYxOTk2ZTlmNDFhMTgyZWZlOTk2ZDM3MTFmZWEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-1859 Manage a cycle without uploading salary ranges / pay bands data for…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:11920::21622684982011ee96f59110f874eb6e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:11920::21622681982011ee96f59110f874eb6e", "elements": [{"type": "button", "action_id": "{\"issueId\":\"11920\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"11920\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-1859", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:11927::21622682982011ee96f59110f874eb6e", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-1866?atlOrigin=eyJpIjoiNDczMmY2NjMzZmNlNDRmMzlkNTg1NjVlOTJmNTJhMTAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-1866 Manage a cycle with uploading partial salary ranges / pay bands dat…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:11927::21622685982011ee96f59110f874eb6e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:11927::21622683982011ee96f59110f874eb6e", "elements": [{"type": "button", "action_id": "{\"issueId\":\"11927\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"11927\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-1866", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "2qCCe", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Here is an update on implementing Digital Asset workflow within Compiify\nThere are 2 tasks that have been created\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Managing a cycle without pay bands information ( 4 issue already reported during today's testing "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1859"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Managing a cycle with partial pay bands information  ( Issue for this use case will be tracked here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1866"}, {"type": "text", "text": ")"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.569036"}