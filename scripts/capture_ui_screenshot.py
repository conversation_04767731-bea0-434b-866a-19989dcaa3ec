#!/usr/bin/env python3
"""
Capture a screenshot of the UI showing the improved detailed responses.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException


def capture_ui_screenshot():
    """Capture a screenshot of the improved UI."""
    print("📸 Capturing UI Screenshot")
    print("=" * 40)
    
    # Setup Chrome with screenshot capabilities
    chrome_options = Options()
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        print("✅ Browser initialized")
        
        # Navigate to login
        driver.get("http://127.0.0.1:8000/accounts/login/")
        
        # Login
        username_field = driver.find_element(By.NAME, "username")
        password_field = driver.find_element(By.NAME, "password")
        
        username_field.send_keys("<EMAIL>")
        password_field.send_keys("admin123")
        
        login_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        login_button.click()
        
        time.sleep(3)
        print("✅ Logged in")
        
        # Navigate to search
        if "/search/" not in driver.current_url:
            driver.get("http://127.0.0.1:8000/search/")
        
        # Wait for search form
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.NAME, "query"))
        )
        
        # Ensure Context-Aware is checked
        try:
            context_aware_checkbox = driver.find_element(By.NAME, "use_context_aware")
            if not context_aware_checkbox.is_selected():
                context_aware_checkbox.click()
        except:
            pass
        
        # Submit search
        query_field = driver.find_element(By.NAME, "query")
        query_field.clear()
        query_field.send_keys("list issues reported on Curana")
        
        search_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        search_button.click()
        
        print("🔍 Search submitted, waiting for results...")
        
        # Wait for results (up to 60 seconds)
        try:
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.CLASS_NAME, "response-content"))
            )
            print("✅ Results loaded")
            
            # Scroll to make sure everything is visible
            driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            
            # Take screenshot
            screenshot_path = "/Users/<USER>/Desktop/RAGSearch/docs/ui_detailed_response_screenshot.png"
            driver.save_screenshot(screenshot_path)
            
            print(f"📸 Screenshot saved to: {screenshot_path}")
            
            # Also capture just the response area
            response_element = driver.find_element(By.CLASS_NAME, "response-content")
            response_screenshot_path = "/Users/<USER>/Desktop/RAGSearch/docs/response_content_screenshot.png"
            response_element.screenshot(response_screenshot_path)
            
            print(f"📸 Response screenshot saved to: {response_screenshot_path}")
            
            return True
            
        except TimeoutException:
            print("❌ Timeout waiting for results")
            # Take screenshot anyway to see what happened
            driver.save_screenshot("/Users/<USER>/Desktop/RAGSearch/docs/error_screenshot.png")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
        
    finally:
        driver.quit()
        print("🧹 Browser closed")


def main():
    """Main function."""
    print("🚀 UI Screenshot Capture")
    print("Capturing the improved detailed response UI")
    print()
    
    success = capture_ui_screenshot()
    
    if success:
        print("\n✅ Screenshots captured successfully!")
        print("📁 Check the docs/ folder for:")
        print("   - ui_detailed_response_screenshot.png (full page)")
        print("   - response_content_screenshot.png (response area only)")
    else:
        print("\n❌ Screenshot capture failed")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
