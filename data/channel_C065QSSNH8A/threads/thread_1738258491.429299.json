{"thread_ts": "1738258491.429299", "channel_id": "C065QSSNH8A", "reply_count": 4, "replies": [{"ts": "1738258504.517409", "text": "", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738258491.429299", "files": [{"id": "F08B48R8C4U", "created": 1738258501, "timestamp": 1738258501, "name": "vgpromos.csv", "title": "vgpromos.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 382, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08B48R8C4U/vgpromos.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08B48R8C4U/download/vgpromos.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08B48R8C4U/vgpromos.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08B48R8C4U-900ea9a689", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08B48R8C4U/vgpromos.csv/edit", "preview": "Employee Id,Employee Name (Read Only),Is Eligible for Promotion,Proposed Promotion Increase,Proposed Promotion Increase Percent,Proposed Promotion Increase Currency,Job Title,Band Id\r\n1613,<PERSON><PERSON><PERSON><PERSON><PERSON>,Yes,0,0,INR,Senior Software Engineer,\r\n1630,<PERSON><PERSON><PERSON><PERSON> Subramaniam,Yes,0,0,INR,\"Vice President, Finance\",\r\n1972,<PERSON>,Yes,0,0,<PERSON><PERSON>,\"Director, Engineering\",", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Employee Id</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Is Eligible for Promotion</div><div class=\"cm-col\">Proposed Promotion Increase</div><div class=\"cm-col\">Proposed Promotion Increase Percent</div><div class=\"cm-col\">Proposed Promotion Increase Currency</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Band Id</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">1613</div><div class=\"cm-col\"><PERSON><PERSON><PERSON><PERSON><PERSON>ub<PERSON>nian</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">INR</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">1630</div><div class=\"cm-col\">Prabhakaran Subramaniam</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">INR</div><div class=\"cm-col\">Vice President, Finance</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">1972</div><div class=\"cm-col\">Andre Parreira</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">EUR</div><div class=\"cm-col\">Director, Engineering</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 4, "lines_more": 0, "preview_is_truncated": false, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}]}, {"ts": "1738258518.131539", "text": "We want the $ to be 0 by design", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738258491.429299", "blocks": [{"type": "rich_text", "block_id": "Cqh7m", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We want the $ to be 0 by design"}]}]}]}, {"ts": "1738260383.775289", "text": "<@U07EJ2LP44S> This issue is fixed and uploaded. Since the budget was combined it was not allowing. I removed that check in the code and uploaded.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738258491.429299", "blocks": [{"type": "rich_text", "block_id": "Hom5L", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This issue is fixed and uploaded. Since the budget was combined it was not allowing. I removed that check in the code and uploaded."}]}]}]}, {"ts": "1738271270.762479", "text": "thamk you!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738258491.429299", "blocks": [{"type": "rich_text", "block_id": "ihLGh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "thamk you!"}]}]}]}], "created_at": "2025-05-22T22:00:49.345968"}