{"thread_ts": "1734650531.802349", "channel_id": "C065QSSNH8A", "reply_count": 3, "replies": [{"ts": "1734709505.976549", "text": "I went back through the channel and it was a lot of bugs (which are all corrected) and data issues (she was trying to use the CSV uploads and they were challenging so she came to us to do a lot of data refreshes). I don't think there's any other manual work other than having to come to us for the letters", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734650531.802349", "blocks": [{"type": "rich_text", "block_id": "3bqTy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I went back through the channel and it was a lot of bugs (which are all corrected) and data issues (she was trying to use the CSV uploads and they were challenging so she came to us to do a lot of data refreshes). I don't think there's any other manual work other than having to come to us for the letters"}]}]}]}, {"ts": "1734709512.165219", "text": "I will rewatch her feedback call though", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734650531.802349", "blocks": [{"type": "rich_text", "block_id": "7tNlB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will rewatch her feedback call though"}]}]}]}, {"ts": "1734711485.810959", "text": "Feedback call was overall positive. Her main points were:\n\nThey keep execs separate from everyone else in the comp cycle, including their budgets, so that was a challenge within Stride\n\nThey really liked the equity columns but wanted three - vested/unvested/total\n\nWants improvements in cycle insights (filtering by department more easily) - this may be more of a data structure issue than an actual insights issue\n\nOur 'levels' didn't match their levels (ongoing, known issue)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734650531.802349", "blocks": [{"type": "rich_text", "block_id": "jQV6K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Feedback call was overall positive. Her main points were:\n\nThey keep execs separate from everyone else in the comp cycle, including their budgets, so that was a challenge within Stride\n\nThey really liked the equity columns but wanted three - vested/unvested/total\n\nWants improvements in cycle insights (filtering by department more easily) - this may be more of a data structure issue than an actual insights issue\n\nOur 'levels' didn't match their levels (ongoing, known issue)"}]}]}]}], "created_at": "2025-05-22T22:00:49.339641"}