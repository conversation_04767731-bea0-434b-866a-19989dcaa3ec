#!/usr/bin/env python
"""
RAG Service Migration Verification Script

This script verifies that the RAG service migration was successful by testing:
1. New RAGService can be imported and initialized
2. Service cache integration works correctly
3. Search functionality works with real data
4. Old services are properly archived
5. Backend API integration is functional

Usage:
    cd multi_source_rag
    python ../scripts/verify_migration.py
"""

import os
import sys
import django
from pathlib import Path

# Set up Django
sys.path.append('.')
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

def print_banner():
    """Print verification banner."""
    print("🔍 RAG SERVICE MIGRATION VERIFICATION")
    print("=" * 50)

def test_new_service_import():
    """Test that new RAGService can be imported."""
    print("📦 Testing new RAGService import...")
    
    try:
        from apps.search.services.rag_service import RAGService
        print("✅ RAGService imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import RAGService: {e}")
        return False

def test_service_initialization():
    """Test service initialization."""
    print("\n🔧 Testing service initialization...")
    
    try:
        from apps.search.services.rag_service import RAGService
        from django.contrib.auth.models import User
        from apps.accounts.models import Tenant
        
        # Get test user and tenant
        user = User.objects.get(username='mahesh')
        tenant = Tenant.objects.get(slug='stride')
        
        # Initialize service
        service = RAGService(user=user, tenant_slug=tenant.slug)
        print("✅ RAGService initialized successfully")
        return service
        
    except Exception as e:
        print(f"❌ Service initialization failed: {e}")
        return None

def test_service_cache():
    """Test service cache integration."""
    print("\n💾 Testing service cache...")
    
    try:
        from apps.core.utils.service_cache import get_cached_rag_service
        
        # Get cached service instances
        service1 = get_cached_rag_service('stride', 1)
        service2 = get_cached_rag_service('stride', 1)
        
        if service1 is service2:
            print("✅ Service caching working correctly")
            return True
        else:
            print("⚠️  Service caching may not be working optimally")
            return False
            
    except Exception as e:
        print(f"❌ Service cache test failed: {e}")
        return False

def test_search_functionality(service):
    """Test search functionality with real data."""
    print("\n🔍 Testing search functionality...")
    
    try:
        # Test basic search
        result, docs = service.search(
            query_text="What issues did Rachel mention?",
            top_k=5,
            min_relevance_score=0.15
        )
        
        print(f"✅ Search completed successfully:")
        print(f"   Documents: {len(docs)}")
        print(f"   Citations: {result.citations.count()}")
        print(f"   Answer length: {len(result.generated_answer)} chars")
        
        # Test with feature flags
        result2, docs2 = service.search(
            query_text="Engineering challenges",
            top_k=3,
            use_query_expansion=True
        )
        
        print(f"✅ Feature flag search completed:")
        print(f"   Documents: {len(docs2)}")
        print(f"   Citations: {result2.citations.count()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Search functionality test failed: {e}")
        return False

def test_archived_services():
    """Test that old services are properly archived."""
    print("\n📁 Testing archived services...")
    
    services_dir = Path("apps/search/services")
    archive_dir = services_dir / "archive"
    
    # Check that archive directory exists
    if not archive_dir.exists():
        print("❌ Archive directory does not exist")
        return False
    
    # Check that old services are in archive
    expected_files = [
        "rag_service_old.py",
        "unified_rag_service.py", 
        "enhanced_rag_service.py"
    ]
    
    missing_files = []
    for file_name in expected_files:
        if not (archive_dir / file_name).exists():
            missing_files.append(file_name)
    
    if missing_files:
        print(f"❌ Missing archived files: {missing_files}")
        return False
    
    # Check that main service file exists
    if not (services_dir / "rag_service.py").exists():
        print("❌ Main rag_service.py file does not exist")
        return False
    
    print("✅ All services properly archived")
    print(f"   Archive location: {archive_dir}")
    print(f"   Archived files: {len(expected_files)}")
    
    return True

def test_api_integration():
    """Test that API can use the new service."""
    print("\n🌐 Testing API integration...")

    try:
        # Import the views that use RAGService
        from apps.search.views import search_query, add_message
        print("✅ Search views imported successfully")

        # Check that it can import RAGService
        from apps.search.services.rag_service import RAGService
        print("✅ API can import new RAGService")

        # Verify the views are using the correct import
        import inspect
        source_code = inspect.getsource(search_query)
        if "from apps.search.services.rag_service import RAGService" in source_code:
            print("✅ Views are using correct RAGService import")
        else:
            print("⚠️  Views may be using old import")

        return True

    except Exception as e:
        print(f"❌ API integration test failed: {e}")
        return False

def test_statistics():
    """Test service statistics functionality."""
    print("\n📊 Testing service statistics...")
    
    try:
        from apps.search.services.rag_service import RAGService
        from django.contrib.auth.models import User
        from apps.accounts.models import Tenant
        
        user = User.objects.get(username='mahesh')
        tenant = Tenant.objects.get(slug='stride')
        service = RAGService(user=user, tenant_slug=tenant.slug)
        
        # Get statistics
        stats = service.get_stats()
        
        print("✅ Service statistics retrieved:")
        for key, value in stats.items():
            if isinstance(value, dict):
                print(f"   {key}: {len(value)} items")
            else:
                print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Statistics test failed: {e}")
        return False

def main():
    """Main verification function."""
    print_banner()
    
    tests = [
        ("Import Test", test_new_service_import),
        ("Initialization Test", test_service_initialization),
        ("Cache Test", test_service_cache),
        ("Archive Test", test_archived_services),
        ("API Integration Test", test_api_integration),
        ("Statistics Test", test_statistics),
    ]
    
    results = {}
    service = None
    
    for test_name, test_func in tests:
        if test_name == "Initialization Test":
            service = test_func()
            results[test_name] = service is not None
        elif test_name == "Search Test" and service:
            results[test_name] = test_func(service)
        else:
            results[test_name] = test_func()
    
    # Run search test if service was initialized
    if service:
        print("\n🔍 Running search functionality test...")
        results["Search Test"] = test_search_functionality(service)
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, passed_test in results.items():
        status = "✅ PASSED" if passed_test else "❌ FAILED"
        print(f"{test_name}: {status}")
        if passed_test:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 MIGRATION VERIFICATION SUCCESSFUL!")
        print("✅ All systems operational with new RAGService")
        return 0
    else:
        print("\n❌ MIGRATION VERIFICATION FAILED!")
        print("⚠️  Some issues detected - review failed tests")
        return 1

if __name__ == "__main__":
    exit(main())
