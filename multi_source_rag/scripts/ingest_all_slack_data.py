#!/usr/bin/env python
"""
Complete Slack Data Ingestion Script

This script ingests all Slack data from the data/slack/ folder using the
UnifiedLlamaIndexIngestionService with enhanced metadata and skip-chunking.

Features:
- Processes all JSON files in data/slack/
- Uses enhanced metadata for better filtering
- Applies skip-chunking for pre-optimized Slack data
- Comprehensive error handling and statistics
- Progress tracking and logging

Usage:
    python manage.py shell -c "exec(open('scripts/ingest_all_slack_data.py').read())"
"""

import json
from pathlib import Path
from datetime import datetime
from django.db import transaction
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk
from apps.documents.services.llama_ingestion_service_unified import UnifiedLlamaIndexIngestionService
from apps.core.utils.slack_metadata_enhancer import enhance_slack_metadata


def extract_technical_terms(text: str) -> list:
    """Extract technical terms from text."""
    technical_keywords = [
        'api', 'database', 'server', 'client', 'endpoint', 'authentication',
        'authorization', 'deployment', 'docker', 'kubernetes', 'aws', 'gcp',
        'azure', 'microservice', 'frontend', 'backend', 'javascript', 'python',
        'java', 'react', 'vue', 'angular', 'node', 'express', 'django',
        'flask', 'spring', 'mongodb', 'postgresql', 'mysql', 'redis',
        'elasticsearch', 'kafka', 'rabbitmq', 'nginx', 'apache', 'ssl',
        'https', 'oauth', 'jwt', 'rest', 'graphql', 'websocket', 'grpc'
    ]
    
    text_lower = text.lower()
    found_terms = []
    
    for term in technical_keywords:
        if term in text_lower:
            found_terms.append(term)
    
    return list(set(found_terms))


def parse_slack_messages(messages: list) -> dict:
    """Parse Slack messages and create enhanced metadata."""
    if not messages:
        return {}
    
    # Sort messages by timestamp
    sorted_messages = sorted(messages, key=lambda x: float(x.get('ts', 0)))
    
    # Extract information
    participants = set()
    reactions = []
    has_code = False
    has_threads = False
    thread_count = 0
    
    for msg in sorted_messages:
        # Extract user
        user = msg.get('user', 'unknown')
        if user and user != 'unknown':
            participants.add(user)
        
        # Check for code blocks
        text = msg.get('text', '')
        if '```' in text or '`' in text:
            has_code = True
        
        # Check for threads
        if msg.get('thread_ts'):
            has_threads = True
            if msg.get('thread_ts') == msg.get('ts'):
                thread_count += 1
        
        # Extract reactions
        if 'reactions' in msg:
            for reaction in msg['reactions']:
                reactions.append({
                    'name': reaction.get('name', ''),
                    'count': reaction.get('count', 0)
                })
    
    # Calculate time range
    start_time = None
    end_time = None
    if sorted_messages:
        start_ts = float(sorted_messages[0].get('ts', 0))
        end_ts = float(sorted_messages[-1].get('ts', 0))
        start_time = datetime.fromtimestamp(start_ts).isoformat()
        end_time = datetime.fromtimestamp(end_ts).isoformat()
    
    # Create content by joining messages
    content_parts = []
    for msg in sorted_messages:
        user = msg.get('user', 'unknown')
        text = msg.get('text', '')
        ts = msg.get('ts', '')
        
        if text:
            # Convert timestamp to readable format
            try:
                dt = datetime.fromtimestamp(float(ts))
                time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
            except:
                time_str = ts
            
            content_parts.append(f"[{time_str}] {user}: {text}")
    
    content = "\n".join(content_parts)
    
    # Calculate quality score
    quality_score = min(1.0, (
        len(participants) * 0.2 +
        len(reactions) * 0.1 +
        (1.0 if has_threads else 0.0) * 0.3 +
        min(len(sorted_messages) / 20.0, 1.0) * 0.4
    ))
    
    return {
        'content': content,
        'metadata': {
            'message_count': len(sorted_messages),
            'participants': list(participants),
            'start_time': start_time,
            'end_time': end_time,
            'reactions': reactions,
            'has_code': has_code,
            'has_threads': has_threads,
            'thread_count': thread_count,
            'quality_score': quality_score,
            'technical_terms': extract_technical_terms(content)
        }
    }


def process_slack_file(file_path: Path) -> dict:
    """Process a single Slack JSON file and create document."""
    print(f"📥 Processing: {file_path.name}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        messages = data.get('messages', [])
        if not messages:
            print(f"   ⚠️  No messages found")
            return None
        
        print(f"   📊 Found {len(messages)} messages")
        
        # Parse messages
        parsed_data = parse_slack_messages(messages)
        if not parsed_data:
            print(f"   ❌ Failed to parse messages")
            return None
        
        # Create document
        doc_id = f"slack_{file_path.stem}"
        title = f"Slack Messages - {file_path.stem}"
        
        document = {
            'id': doc_id,
            'title': title,
            'content': parsed_data['content'],
            'content_type': 'text/slack',
            'metadata': {
                'source_file': file_path.name,
                'channel': file_path.stem.split('_')[0] if '_' in file_path.stem else 'unknown',
                **parsed_data['metadata']
            }
        }
        
        # Enhance metadata
        enhanced_document = enhance_slack_metadata(document, "local_slack")
        
        print(f"   ✅ Created document: {len(enhanced_document['content'])} chars, {enhanced_document['metadata'].get('message_count', 0)} messages")
        return enhanced_document
        
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return None


def main():
    """Main ingestion function."""
    print("🚀 COMPLETE SLACK DATA INGESTION")
    print("=" * 60)
    
    # Configuration
    data_dir = Path("data/slack")
    tenant_slug = "default"
    
    if not data_dir.exists():
        print(f"❌ Data directory not found: {data_dir}")
        return
    
    # Statistics
    stats = {
        'start_time': datetime.now(),
        'files_found': 0,
        'files_processed': 0,
        'documents_created': 0,
        'documents_ingested': 0,
        'errors': 0
    }
    
    try:
        # 1. Setup tenant and source
        print("📋 Setting up tenant and document source...")
        tenant, created = Tenant.objects.get_or_create(
            slug=tenant_slug,
            defaults={"name": f"Tenant {tenant_slug}"}
        )
        
        source, created = DocumentSource.objects.get_or_create(
            tenant=tenant,
            name="Complete Slack Data",
            source_type="local_slack",
            defaults={
                "config": {
                    "data_dir": str(data_dir),
                    "file_format": "json",
                    "processing_date": datetime.now().isoformat()
                }
            }
        )
        print(f"✅ Tenant: {tenant_slug}, Source: {source.name}")
        
        # 2. Find all JSON files
        print("\n🔍 Finding JSON files...")
        json_files = list(data_dir.glob("*.json"))
        stats['files_found'] = len(json_files)
        
        if not json_files:
            print(f"❌ No JSON files found in {data_dir}")
            return
        
        print(f"📄 Found {len(json_files)} JSON files:")
        for f in json_files:
            print(f"   {f.name}")
        
        # 3. Process all files
        print(f"\n📥 Processing {len(json_files)} files...")
        all_documents = []
        
        for json_file in json_files:
            try:
                document = process_slack_file(json_file)
                if document:
                    all_documents.append(document)
                    stats['documents_created'] += 1
                stats['files_processed'] += 1
            except Exception as e:
                print(f"   ❌ Error processing {json_file.name}: {str(e)}")
                stats['errors'] += 1
        
        print(f"\n✅ Created {len(all_documents)} documents from {stats['files_processed']} files")
        
        if not all_documents:
            print("❌ No documents to ingest")
            return
        
        # 4. Initialize ingestion service
        print("\n🔄 Initializing ingestion service...")
        ingestion_service = UnifiedLlamaIndexIngestionService(tenant_slug)
        print("✅ Service initialized")
        
        # 5. Ingest all documents
        print(f"\n🚀 Ingesting {len(all_documents)} documents...")
        
        for i, doc in enumerate(all_documents):
            try:
                print(f"   Processing {i+1}/{len(all_documents)}: {doc.get('title', 'Untitled')}")
                
                with transaction.atomic():
                    result = ingestion_service._process_single_document(source, doc)
                    
                    if result:
                        stats['documents_ingested'] += 1
                        print(f"   ✅ Ingested successfully (ID: {result.id})")
                    else:
                        print(f"   ⚠️  No result returned")
                        
            except Exception as e:
                stats['errors'] += 1
                print(f"   ❌ Error: {str(e)}")
                continue
        
        # 6. Print final statistics
        stats['end_time'] = datetime.now()
        duration = stats['end_time'] - stats['start_time']
        
        print("\n" + "=" * 60)
        print("📊 INGESTION COMPLETED")
        print("=" * 60)
        print(f"⏱️  Duration: {duration}")
        print(f"📁 Files found: {stats['files_found']}")
        print(f"📄 Files processed: {stats['files_processed']}")
        print(f"📝 Documents created: {stats['documents_created']}")
        print(f"✅ Documents ingested: {stats['documents_ingested']}")
        print(f"❌ Errors: {stats['errors']}")
        
        if stats['documents_ingested'] > 0:
            success_rate = (stats['documents_ingested'] / stats['documents_created']) * 100
            print(f"📈 Success rate: {success_rate:.1f}%")
            
            # Check database
            total_docs = RawDocument.objects.filter(tenant=tenant, source=source).count()
            total_chunks = DocumentChunk.objects.filter(tenant=tenant, document__source=source).count()
            
            print(f"\n📊 DATABASE STATUS:")
            print(f"   Raw documents: {total_docs}")
            print(f"   Document chunks: {total_chunks}")
            
            print(f"\n🎉 Successfully ingested {stats['documents_ingested']} Slack documents!")
            print("💡 You can now search this data using the RAG system")
            print("🔍 Try searching for:")
            print("   - Technical discussions")
            print("   - Specific users or topics")
            print("   - Code-related conversations")
            print("   - Recent discussions")
        else:
            print("\n❌ No documents were successfully ingested")
        
    except Exception as e:
        print(f"\n❌ Fatal error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
else:
    # When executed via exec(), run main automatically
    main()
