from django import template
from django.utils.html import escape
from django.utils.safestring import mark_safe
import re
import bleach
from datetime import datetime

# Optional markdown import
try:
    import markdown
    MARKDOWN_AVAILABLE = True
except ImportError:
    MARKDOWN_AVAILABLE = False

register = template.Library()


def preprocess_for_simple_formatting(text):
    """
    Preprocess text for simple, clean formatting:
    - Preserve detailed formatting from enhanced prompt templates
    - Remove headers and complex formatting only for basic responses
    - Keep only simple bullet points for unstructured content
    - Ensure clean, scannable output
    """
    if not text:
        return text

    # Check if this is already well-formatted content from enhanced prompt templates
    has_detailed_formatting = (
        "•" in text and  # Has Unicode bullet points
        len(text) > 300 and  # Is reasonably detailed
        ("Issues reported by" in text or "Issues Found:" in text or
         "Summary of" in text or "Information about" in text)  # Has proper headers
    )

    if has_detailed_formatting:
        # This is already well-formatted from enhanced prompt templates, preserve it
        # Only do minimal cleanup
        text = re.sub(r'\n{4,}', '\n\n', text)  # Limit excessive newlines to double
        return text.strip()

    # Apply legacy simple formatting for basic responses
    # Remove all headers (##, ###, etc.) - we want simple bullet points only
    text = re.sub(r'^#{1,6}\s+.*$', '', text, flags=re.MULTILINE)

    # Remove excessive bold formatting - keep text simple
    text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)

    # Ensure bullet points start on new lines
    text = re.sub(r'(\S)\s*(-\s)', r'\1\n\2', text)

    # Clean up multiple consecutive newlines
    text = re.sub(r'\n{3,}', '\n', text)

    # Remove empty lines at start and end
    text = text.strip()

    return text


def format_text_as_professional_html(text):
    """
    Enhanced text to HTML formatting with professional styling.
    """
    if not text:
        return ""

    # Clean the text first
    cleaned_text = text.strip()

    # Split into lines for processing
    lines = cleaned_text.split('\n')
    formatted_lines = []
    in_list = False

    for line in lines:
        stripped = line.strip()

        # Skip empty lines
        if not stripped:
            if in_list:
                formatted_lines.append('</ul>' if 'ul' in formatted_lines[-1] else '</ol>')
                in_list = False
            formatted_lines.append('<br>')
            continue

        # Process headers first (before escaping)
        if stripped.startswith('### '):
            if in_list:
                formatted_lines.append('</ul>' if 'ul' in formatted_lines[-1] else '</ol>')
                in_list = False
            header_text = stripped[4:].strip()
            # Remove any markdown formatting from headers
            header_text = re.sub(r'\*\*(.+?)\*\*', r'\1', header_text)
            formatted_lines.append(f'<h3 class="response-heading response-h3">{escape(header_text)}</h3>')
            continue

        elif stripped.startswith('## '):
            if in_list:
                formatted_lines.append('</ul>' if 'ul' in formatted_lines[-1] else '</ol>')
                in_list = False
            header_text = stripped[3:].strip()
            # Remove any markdown formatting from headers
            header_text = re.sub(r'\*\*(.+?)\*\*', r'\1', header_text)
            formatted_lines.append(f'<h2 class="response-heading response-h2">{escape(header_text)}</h2>')
            continue

        elif stripped.startswith('# '):
            if in_list:
                formatted_lines.append('</ul>' if 'ul' in formatted_lines[-1] else '</ol>')
                in_list = False
            header_text = stripped[2:].strip()
            # Remove any markdown formatting from headers
            header_text = re.sub(r'\*\*(.+?)\*\*', r'\1', header_text)
            formatted_lines.append(f'<h1 class="response-heading response-h1">{escape(header_text)}</h1>')
            continue

        # Process list items
        if stripped.startswith('- ') or stripped.startswith('* '):
            if not in_list:
                formatted_lines.append('<ul class="response-list professional-list">')
                in_list = True
            list_content = stripped[2:].strip()
            # Process markdown in list content
            list_content = escape(list_content)
            list_content = re.sub(r'\*\*(.+?)\*\*', r'<strong class="response-emphasis">\1</strong>', list_content)
            list_content = re.sub(r'\*(.+?)\*', r'<em class="response-italic">\1</em>', list_content)
            formatted_lines.append(f'<li class="response-list-item">{list_content}</li>')
            continue

        elif re.match(r'^\d+\.\s', stripped):
            if not in_list:
                formatted_lines.append('<ol class="response-list professional-list">')
                in_list = True
            list_content = re.sub(r'^\d+\.\s', '', stripped)
            # Process markdown in list content
            list_content = escape(list_content)
            list_content = re.sub(r'\*\*(.+?)\*\*', r'<strong class="response-emphasis">\1</strong>', list_content)
            list_content = re.sub(r'\*(.+?)\*', r'<em class="response-italic">\1</em>', list_content)
            formatted_lines.append(f'<li class="response-list-item">{list_content}</li>')
            continue

        # Regular paragraph
        if in_list:
            formatted_lines.append('</ul>' if 'ul' in formatted_lines[-1] else '</ol>')
            in_list = False

        # Process markdown in paragraph content
        paragraph_content = escape(stripped)
        paragraph_content = re.sub(r'\*\*(.+?)\*\*', r'<strong class="response-emphasis">\1</strong>', paragraph_content)
        paragraph_content = re.sub(r'\*(.+?)\*', r'<em class="response-italic">\1</em>', paragraph_content)
        formatted_lines.append(f'<p class="response-paragraph">{paragraph_content}</p>')

    # Close any open lists
    if in_list:
        formatted_lines.append('</ul>' if 'ul' in formatted_lines[-1] else '</ol>')

    return '\n'.join(formatted_lines)


def apply_professional_styling(html_content):
    """
    Apply professional styling classes to HTML content.
    """
    # Headers with professional styling
    html_content = html_content.replace('<h1>', '<h1 class="response-heading response-h1">')
    html_content = html_content.replace('<h2>', '<h2 class="response-heading response-h2">')
    html_content = html_content.replace('<h3>', '<h3 class="response-heading response-h3">')
    html_content = html_content.replace('<h4>', '<h4 class="response-heading response-h4">')
    html_content = html_content.replace('<h5>', '<h5 class="response-heading response-h5">')
    html_content = html_content.replace('<h6>', '<h6 class="response-heading response-h6">')

    # Lists with professional styling
    html_content = html_content.replace('<ul>', '<ul class="response-list professional-list">')
    html_content = html_content.replace('<ol>', '<ol class="response-list professional-list">')
    html_content = html_content.replace('<li>', '<li class="response-list-item">')

    # Paragraphs with professional styling
    html_content = html_content.replace('<p>', '<p class="response-paragraph">')

    # Tables with professional styling
    html_content = html_content.replace('<table>', '<table class="table table-striped response-table professional-table">')

    # Code blocks with professional styling
    html_content = html_content.replace('<code>', '<code class="response-code">')
    html_content = html_content.replace('<pre>', '<pre class="response-pre">')

    # Strong and emphasis with controlled styling
    html_content = re.sub(r'<strong>([^<]+)</strong>', r'<strong class="response-emphasis">\1</strong>', html_content)
    html_content = re.sub(r'<em>([^<]+)</em>', r'<em class="response-italic">\1</em>', html_content)

    return html_content


def humanize_dates(text):
    """
    Convert ISO date strings to human-readable format.

    Examples:
    - "2024-11-06" -> "November 6, 2024"
    - "2024-03-15" -> "March 15, 2024"
    """
    # Pattern for YYYY-MM-DD format
    date_pattern = r'\b(\d{4})-(\d{2})-(\d{2})\b'

    def replace_date(match):
        try:
            year, month, day = match.groups()
            date_obj = datetime(int(year), int(month), int(day))
            # Use %-d to remove leading zero from day, %-m for month if needed
            return date_obj.strftime("%B %-d, %Y")
        except (ValueError, TypeError):
            return match.group(0)  # Return original if parsing fails

    return re.sub(date_pattern, replace_date, text)


def clean_document_references(text):
    """
    Remove redundant document references like [Document 2921] from text.
    Preserves markdown structure by maintaining line breaks.
    """
    # Remove [Document XXXX] patterns
    text = re.sub(r'\[Document \d+\]', '', text)

    # Remove standalone document references at the end of sentences
    text = re.sub(r'\s*Document \d+\s*\.', '.', text)

    # Clean up extra spaces but preserve line breaks
    text = re.sub(r'[ \t]+', ' ', text)  # Only collapse spaces and tabs, not newlines
    text = re.sub(r' +\.', '.', text)    # Remove spaces before periods
    text = re.sub(r'^ +', '', text, flags=re.MULTILINE)  # Remove leading spaces on lines
    text = re.sub(r' +$', '', text, flags=re.MULTILINE)  # Remove trailing spaces on lines

    return text.strip()


@register.filter
def multiply(value, arg):
    """Multiply the value by the argument."""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0


@register.filter
def safe_html(value):
    """
    Render HTML content safely after sanitizing.

    This filter allows sanitized HTML content to be rendered in templates without escaping.
    """
    # Define allowed tags and attributes
    allowed_tags = [
        'a', 'abbr', 'acronym', 'b', 'blockquote', 'br', 'code', 'div', 'em',
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'hr', 'i', 'li', 'ol', 'p',
        'pre', 'span', 'strong', 'table', 'tbody', 'td', 'th', 'thead', 'tr', 'ul'
    ]
    allowed_attrs = {
        'a': ['href', 'title', 'target', 'rel'],
        'abbr': ['title'],
        'acronym': ['title'],
        'div': ['class', 'id'],
        'span': ['class', 'id'],
        'p': ['class'],
        'pre': ['class'],
        'code': ['class'],
        'table': ['class', 'width', 'border', 'cellspacing', 'cellpadding'],
        'th': ['scope', 'colspan', 'rowspan'],
        'td': ['colspan', 'rowspan', 'align', 'valign']
    }

    # Sanitize HTML
    cleaned_html = bleach.clean(
        value,
        tags=allowed_tags,
        attributes=allowed_attrs,
        strip=True
    )

    return mark_safe(cleaned_html)


@register.filter
def format_source_type(value):
    """Format source type string for display."""
    if not value:
        return "Unknown"

    # Convert to lowercase and remove special characters
    formatted = re.sub(r'[^a-zA-Z0-9]', ' ', value.lower())

    # Title case and trim
    return formatted.title().strip()


@register.filter
def highlight_text(text, term):
    """
    Highlight occurrences of a term in text.
    """
    if not term or not text:
        return mark_safe(text)

    # Escape HTML first
    escaped_text = escape(text)

    # Escape the search term for regex
    escaped_term = re.escape(term)

    # Case-insensitive replacement with highlighting
    pattern = re.compile(r'(' + escaped_term + ')', re.IGNORECASE)
    highlighted = pattern.sub(r'<span class="citation-highlight">\1</span>', escaped_text)

    return mark_safe(highlighted)


@register.filter
def truncate_middle(text, length):
    """
    Truncate text in the middle, preserving start and end.
    """
    if not text:
        return ""

    if len(text) <= length:
        return text

    # Determine how many characters to keep at start and end
    half_length = length // 2
    start = text[:half_length]
    end = text[len(text) - half_length:]

    return f"{start}...{end}"


@register.filter
def get_json_value(data, key):
    """
    Get a value from a JSON object (dict) by key.
    """
    if not data or not key:
        return None

    keys = key.split('.')
    result = data

    try:
        for k in keys:
            if k.isdigit() and isinstance(result, (list, tuple)):
                result = result[int(k)]
            elif isinstance(result, dict):
                result = result.get(k)
            else:
                return None
        return result
    except (KeyError, IndexError, TypeError):
        return None


@register.filter
def markdown_to_html(value):
    """
    Convert markdown text to HTML with enhanced professional formatting.

    This filter converts markdown content to HTML with support for:
    - Headers (H1-H6) with professional styling
    - Lists (ordered and unordered) with proper spacing
    - Bold and italic text with controlled emphasis
    - Links with hover effects
    - Code blocks with syntax highlighting
    - Tables with responsive design
    - Professional paragraph structure
    """
    if not value:
        return ""

    # Clean and preprocess the text
    cleaned_value = clean_document_references(value)
    cleaned_value = humanize_dates(cleaned_value)

    # Check if this is detailed formatting from enhanced prompt templates
    has_detailed_formatting = (
        "•" in cleaned_value and  # Has Unicode bullet points
        len(cleaned_value) > 300 and  # Is reasonably detailed
        ("Issues reported by" in cleaned_value or "Issues Found:" in cleaned_value or
         "Summary of" in cleaned_value or "Information about" in cleaned_value)  # Has proper headers
    )

    if has_detailed_formatting:
        # Preserve detailed formatting - only do minimal preprocessing
        cleaned_value = preprocess_for_simple_formatting(cleaned_value)
        # Keep Unicode bullets for detailed responses - they look better
        # Only clean up excessive whitespace
        cleaned_value = re.sub(r'\n{4,}', '\n\n', cleaned_value)
    else:
        # Apply full preprocessing for basic responses
        cleaned_value = preprocess_for_simple_formatting(cleaned_value)
        # Convert Unicode bullets to standard markdown bullets for basic responses
        cleaned_value = re.sub(r'^• ', '- ', cleaned_value, flags=re.MULTILINE)
        cleaned_value = re.sub(r'\n• ', '\n- ', cleaned_value)
        # Remove standalone bullet characters and empty lines between bullets
        cleaned_value = re.sub(r'^\s*•\s*$', '', cleaned_value, flags=re.MULTILINE)
        # Clean up trailing bullets
        cleaned_value = re.sub(r' •\s*$', '', cleaned_value, flags=re.MULTILINE)
        # Remove extra blank lines between list items to keep them together
        cleaned_value = re.sub(r'\n\n(-\s)', r'\n\1', cleaned_value)

    # If markdown is not available, do enhanced text formatting
    if not MARKDOWN_AVAILABLE:
        return format_text_as_professional_html(cleaned_value)

    # Configure markdown with extensions
    md = markdown.Markdown(
        extensions=[
            'markdown.extensions.fenced_code',
            'markdown.extensions.tables',
            'markdown.extensions.sane_lists',
            'markdown.extensions.nl2br',
        ],
        extension_configs={
            'markdown.extensions.fenced_code': {
                'lang_prefix': 'language-',
            }
        }
    )

    # Convert markdown to HTML
    html_content = md.convert(cleaned_value)

    # Apply professional styling classes
    html_content = apply_professional_styling(html_content)

    # Sanitize the HTML to ensure security
    allowed_tags = [
        'a', 'abbr', 'acronym', 'b', 'blockquote', 'br', 'code', 'div', 'em',
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'hr', 'i', 'li', 'ol', 'p',
        'pre', 'span', 'strong', 'table', 'tbody', 'td', 'th', 'thead', 'tr', 'ul'
    ]
    allowed_attrs = {
        'a': ['href', 'title', 'target', 'rel'],
        'abbr': ['title'],
        'acronym': ['title'],
        'div': ['class', 'id'],
        'span': ['class', 'id'],
        'p': ['class'],
        'pre': ['class'],
        'code': ['class'],
        'h1': ['class'],
        'h2': ['class'],
        'h3': ['class'],
        'h4': ['class'],
        'h5': ['class'],
        'h6': ['class'],
        'ul': ['class'],
        'ol': ['class'],
        'li': ['class'],
        'table': ['class', 'width', 'border', 'cellspacing', 'cellpadding'],
        'th': ['scope', 'colspan', 'rowspan', 'class'],
        'td': ['colspan', 'rowspan', 'align', 'valign', 'class']
    }

    # Sanitize HTML
    cleaned_html = bleach.clean(
        html_content,
        tags=allowed_tags,
        attributes=allowed_attrs,
        strip=True
    )

    return mark_safe(cleaned_html)


def format_text_as_html(text):
    """
    Basic text to HTML formatting when markdown is not available.
    """
    if not text:
        return ""

    # Clean the input text first
    cleaned_text = clean_document_references(text)
    cleaned_text = humanize_dates(cleaned_text)

    # Preprocess Unicode bullets to standard markdown bullets before HTML escaping
    cleaned_text = re.sub(r'^• ', '- ', cleaned_text, flags=re.MULTILINE)
    cleaned_text = re.sub(r'\n• ', '\n- ', cleaned_text)
    # Remove standalone bullet characters
    cleaned_text = re.sub(r'^\s*•\s*$', '', cleaned_text, flags=re.MULTILINE)
    # Clean up trailing bullets
    cleaned_text = re.sub(r' •\s*$', '', cleaned_text, flags=re.MULTILINE)

    # Escape HTML after bullet preprocessing
    html_content = escape(cleaned_text)

    # Convert markdown-style headers
    html_content = re.sub(r'^### (.+)$', r'<h3 class="response-heading">\1</h3>', html_content, flags=re.MULTILINE)
    html_content = re.sub(r'^## (.+)$', r'<h2 class="response-heading">\1</h2>', html_content, flags=re.MULTILINE)
    html_content = re.sub(r'^# (.+)$', r'<h1 class="response-heading">\1</h1>', html_content, flags=re.MULTILINE)

    # Convert markdown-style bold and italic
    html_content = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', html_content)
    html_content = re.sub(r'\*(.+?)\*', r'<em>\1</em>', html_content)

    # Convert markdown-style lists
    lines = html_content.split('\n')
    in_list = False
    formatted_lines = []

    for line in lines:
        stripped = line.strip()
        # Handle standard markdown bullets and Unicode bullets
        if stripped.startswith('- ') or stripped.startswith('* ') or stripped.startswith('• '):
            if not in_list:
                formatted_lines.append('<ul class="response-list professional-list">')
                in_list = True
            # Remove the bullet character and any trailing bullets
            content = stripped[2:].strip()
            # Clean up any trailing bullet characters
            content = content.rstrip('•').strip()
            if content:  # Only add non-empty list items
                formatted_lines.append(f'<li class="response-list-item">{content}</li>')
        # Handle standalone bullet characters
        elif stripped == '•':
            # Skip standalone bullet characters
            continue
        elif stripped.startswith(('1. ', '2. ', '3. ', '4. ', '5. ', '6. ', '7. ', '8. ', '9. ')):
            if not in_list:
                formatted_lines.append('<ol class="response-list professional-list">')
                in_list = True
            formatted_lines.append(f'<li class="response-list-item">{stripped[3:]}</li>')
        else:
            if in_list:
                formatted_lines.append('</ul>' if formatted_lines[-2].startswith('<ul') else '</ol>')
                in_list = False
            # Skip empty bullet lines
            if stripped and stripped != '•':
                formatted_lines.append(f'<p class="response-paragraph">{line}</p>')
            elif not stripped:
                formatted_lines.append('<br>')

    if in_list:
        formatted_lines.append('</ul>')

    return '\n'.join(formatted_lines)
