#!/usr/bin/env python
"""
Test the search view logic to see what tenant is being used.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from django.contrib.auth.models import User
from apps.search.services.rag_service import RAGService

def test_search_view_logic():
    """Test the search view logic."""
    print("🔍 Testing Search View Logic")
    print("=" * 50)
    
    # Get the user
    user = User.objects.get(email="<EMAIL>")
    print(f"User: {user.email}")
    
    # Test the tenant determination logic from the view
    print(f"\n🔧 Testing tenant determination logic:")
    
    tenant_slug = (
        user.profile.tenant.slug
        if hasattr(user, "profile") and user.profile.tenant
        else "stride"
    )
    
    print(f"   Determined tenant slug: {tenant_slug}")
    
    # Test creating RAGService with this tenant
    print(f"\n🔧 Testing RAGService creation:")
    try:
        rag_service = RAGService(
            user=user,
            tenant_slug=tenant_slug
        )
        
        print(f"   ✅ RAGService created successfully")
        print(f"   Service tenant slug: {rag_service.tenant_slug}")
        print(f"   Service tenant: {rag_service.tenant.slug if rag_service.tenant else 'None'}")
        print(f"   Unified service: {type(rag_service.unified_service).__name__ if rag_service.unified_service else 'None'}")
        
        if rag_service.unified_service:
            print(f"   Unified service tenant: {rag_service.unified_service.tenant.slug if rag_service.unified_service.tenant else 'None'}")
        
        # Test a simple search
        print(f"\n🔍 Testing search:")
        result, docs = rag_service.search("List issues reported by Amanda", top_k=5)
        
        print(f"   ✅ Search completed")
        print(f"   Result ID: {result.id}")
        print(f"   Result tenant: {result.search_query.tenant.slug}")
        print(f"   Citations: {result.citations.count()}")
        
        if result.citations.exists():
            print(f"   📖 First citation:")
            citation = result.citations.first()
            print(f"      Chunk ID: {citation.document_chunk.id}")
            print(f"      Chunk tenant: {citation.document_chunk.tenant.slug}")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_search_view_logic()
