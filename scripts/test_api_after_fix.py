#!/usr/bin/env python
"""
Test Django Search API After Service Configuration Fix

This script tests the Django Search API after fixing the service mismatch
between EnhancedRAGService and UnifiedRAGService.

The fix:
- Changed API to use RAGService (which delegates to UnifiedRAGService)
- This matches the working configuration from test_comprehensive_ingestion.py

Author: AI Assistant
Date: 2025-01-30
"""

import os
import sys
import requests
import json
import time
from datetime import datetime

def test_api_endpoint():
    """Test the Django Search API endpoint with the fixed configuration."""
    print("🔧 Testing Django Search API after service configuration fix...")
    print("=" * 70)
    
    # API configuration
    base_url = "http://localhost:8000"
    api_endpoint = f"{base_url}/api/search/"
    
    # Test if server is running
    try:
        response = requests.get(f"{base_url}/admin/", timeout=5)
        print("✅ Django server is accessible")
    except requests.exceptions.RequestException as e:
        print(f"❌ Django server may not be running: {e}")
        print("Please start the Django server with: python manage.py runserver")
        return False
    
    # Test queries that worked in the comprehensive test
    test_queries = [
        {
            'name': 'Basic Search Test',
            'query': 'budget adherence testing',
            'params': {
                'tenant_slug': 'test-tenant',
                'top_k': 10,
                'use_hybrid_search': True,
                'use_context_aware': True,
                'use_query_expansion': False,
                'use_multi_step_reasoning': False,
                'min_relevance_score': 0.15
            }
        },
        {
            'name': 'Query Expansion Test',
            'query': 'bug reports and issues',
            'params': {
                'tenant_slug': 'test-tenant',
                'top_k': 15,
                'use_hybrid_search': True,
                'use_context_aware': True,
                'use_query_expansion': True,
                'use_multi_step_reasoning': False,
                'min_relevance_score': 0.15
            }
        },
        {
            'name': 'Multi-Step Reasoning Test',
            'query': 'What are the main engineering challenges discussed?',
            'params': {
                'tenant_slug': 'test-tenant',
                'top_k': 20,
                'use_hybrid_search': True,
                'use_context_aware': True,
                'use_query_expansion': False,
                'use_multi_step_reasoning': True,
                'min_relevance_score': 0.15
            }
        }
    ]
    
    results = {
        'successful_tests': 0,
        'failed_tests': 0,
        'test_details': [],
        'total_time': 0.0
    }
    
    for test_case in test_queries:
        print(f"\n🧪 Running: {test_case['name']}")
        print(f"   Query: '{test_case['query']}'")
        
        try:
            # Prepare request payload
            payload = {
                'query': test_case['query'],
                **test_case['params']
            }
            
            # Make API request with timing
            start_time = time.time()
            response = requests.post(
                api_endpoint,
                json=payload,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': 'Token 9b2f4cc4a805a9e7f4f9bbffc5517534caade2e4'  # From previous test
                },
                timeout=120  # 2 minute timeout for complex queries
            )
            end_time = time.time()
            query_time = end_time - start_time
            
            results['total_time'] += query_time
            
            if response.status_code == 200:
                response_data = response.json()
                
                if response_data.get('status') == 'success' and 'data' in response_data:
                    data = response_data['data']
                    answer = data.get('answer', '')
                    sources = data.get('sources', [])
                    metrics = data.get('metrics', {})
                    
                    results['successful_tests'] += 1
                    results['test_details'].append({
                        'test': test_case['name'],
                        'status': 'success',
                        'time': query_time,
                        'answer_length': len(answer),
                        'sources_count': len(sources),
                        'confidence_score': metrics.get('confidence_score', 0),
                        'retriever_score': metrics.get('retriever_score', 0),
                        'is_fallback': metrics.get('is_fallback', False)
                    })
                    
                    print(f"   ✅ Success!")
                    print(f"      - Time: {query_time:.2f}s")
                    print(f"      - Answer length: {len(answer)} chars")
                    print(f"      - Sources found: {len(sources)}")
                    print(f"      - Confidence: {metrics.get('confidence_score', 0):.3f}")
                    print(f"      - Retriever score: {metrics.get('retriever_score', 0):.3f}")
                    print(f"      - Is fallback: {metrics.get('is_fallback', False)}")
                    
                    # Show first part of answer
                    if answer:
                        preview = answer[:150] + "..." if len(answer) > 150 else answer
                        print(f"      - Answer preview: {preview}")
                    
                else:
                    results['failed_tests'] += 1
                    results['test_details'].append({
                        'test': test_case['name'],
                        'status': 'failed',
                        'error': 'Invalid response structure',
                        'time': query_time
                    })
                    print(f"   ❌ Failed: Invalid response structure")
                    print(f"      Response: {response_data}")
                    
            else:
                results['failed_tests'] += 1
                error_text = response.text[:200] if response.text else "No response text"
                results['test_details'].append({
                    'test': test_case['name'],
                    'status': 'failed',
                    'error': f"HTTP {response.status_code}: {error_text}",
                    'time': query_time
                })
                print(f"   ❌ HTTP Error {response.status_code}")
                print(f"      Response: {error_text}")
                
        except requests.exceptions.Timeout:
            results['failed_tests'] += 1
            results['test_details'].append({
                'test': test_case['name'],
                'status': 'timeout',
                'error': 'Request timeout'
            })
            print(f"   ❌ Timeout after 2 minutes")
            
        except Exception as e:
            results['failed_tests'] += 1
            results['test_details'].append({
                'test': test_case['name'],
                'status': 'error',
                'error': str(e)
            })
            print(f"   ❌ Error: {e}")
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 API TEST SUMMARY")
    print("=" * 70)
    print(f"✅ Successful tests: {results['successful_tests']}")
    print(f"❌ Failed tests: {results['failed_tests']}")
    print(f"⏱️  Total time: {results['total_time']:.2f}s")
    print(f"⏱️  Average time: {results['total_time'] / len(test_queries):.2f}s")
    
    if results['successful_tests'] > 0:
        print("\n🎉 API is working with the fixed UnifiedRAGService configuration!")
        print("The service mismatch has been resolved.")
    else:
        print("\n❌ API tests failed. Check the error messages above.")
    
    # Save results to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"../docs/api_test_results_{timestamp}.json"
    
    try:
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n📄 Results saved to: {results_file}")
    except Exception as e:
        print(f"\n❌ Failed to save results: {e}")
    
    return results['successful_tests'] > 0


def main():
    """Main function to run the API test."""
    print("🔧 Django Search API Test After Service Configuration Fix")
    print("This test verifies that the API now uses the working UnifiedRAGService")
    
    success = test_api_endpoint()
    
    if success:
        print("\n✅ API configuration fix successful!")
        return 0
    else:
        print("\n❌ API configuration fix failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
