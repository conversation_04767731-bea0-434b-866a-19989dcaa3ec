{"date": "2024-09-24", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1727200233.227179", "text": "<!here> Competitor's demo if anyone is interested\n<https://drive.google.com/file/d/1cUqnc13an-rKQptELecGqT8ZDTpZe9SN/view?usp=sharing|Simply Merit Demo>\n<https://drive.google.com/file/d/1Vv7rumS3oUqPBfFs_9ANgWqbk9ARNQba/view?usp=sharing|Assemble Demo>\n<https://drive.google.com/file/d/1l85o1oOj5TJd7WTledJWRn40Q829d_dR/view?usp=sharing|Comprehensive Demo>\n<https://drive.google.com/file/d/1DlFF3ZUuicTRbPoT-NdhKbpty2mE1b_0/view?usp=sharing|Kamsa Demo>\n<https://drive.google.com/file/d/1482slTDH1EScVH5OHbY7SpygG2TuuO5R/view?usp=sharing|Pequity Demo>", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1727200293.000000"}, "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "files": [{"id": "F07PHM4VDCG", "created": 1727200250, "timestamp": 1727200250, "name": "Demo-Kamsa.MOV", "title": "Demo-Kamsa.MOV", "mimetype": "video/quicktime", "filetype": "mov", "pretty_type": "QuickTime Movie", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1DlFF3ZUuicTRbPoT-NdhKbpty2mE1b_0", "external_url": "https://drive.google.com/file/d/1DlFF3ZUuicTRbPoT-NdhKbpty2mE1b_0/view?usp=sharing", "url_private": "https://drive.google.com/file/d/1DlFF3ZUuicTRbPoT-NdhKbpty2mE1b_0/view?usp=sharing", "media_display_type": "video", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07PHM4VDCG/demo-kamsa.mov", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F07NXCE16EQ", "created": 1727200250, "timestamp": 1727200250, "name": "Assemble_Demo.MOV", "title": "Assemble_Demo.MOV", "mimetype": "video/quicktime", "filetype": "mov", "pretty_type": "QuickTime Movie", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1Vv7rumS3oUqPBfFs_9ANgWqbk9ARNQba", "external_url": "https://drive.google.com/file/d/1Vv7rumS3oUqPBfFs_9ANgWqbk9ARNQba/view?usp=sharing", "url_private": "https://drive.google.com/file/d/1Vv7rumS3oUqPBfFs_9ANgWqbk9ARNQba/view?usp=sharing", "media_display_type": "video", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NXCE16EQ/assemble_demo.mov", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F07NEAZK5HD", "created": 1727200256, "timestamp": 1727200256, "name": "SimplyMerit Demo1.mp4", "title": "SimplyMerit Demo1.mp4", "mimetype": "video/mp4", "filetype": "mp4", "pretty_type": "MPEG 4 Video", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 134115745, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1cUqnc13an-rKQptELecGqT8ZDTpZe9SN", "external_url": "https://drive.google.com/file/d/1cUqnc13an-rKQptELecGqT8ZDTpZe9SN/view?usp=sharing", "url_private": "https://drive.google.com/file/d/1cUqnc13an-rKQptELecGqT8ZDTpZe9SN/view?usp=sharing", "media_display_type": "video", "thumb_video": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NEAZK5HD-05ec70bd67/simplymerit_demo1_thumb_video.jpeg", "thumb_video_w": 1504, "thumb_video_h": 856, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NEAZK5HD/simplymerit_demo1.mp4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F07NS1T0XEZ", "created": 1727200281, "timestamp": 1727200281, "name": "Comprehensive Demo.mp4", "title": "Comprehensive Demo.mp4", "mimetype": "video/mp4", "filetype": "mp4", "pretty_type": "MPEG 4 Video", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 1032061817, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1l85o1oOj5TJd7WTledJWRn40Q829d_dR", "external_url": "https://drive.google.com/file/d/1l85o1oOj5TJd7WTledJWRn40Q829d_dR/view?usp=sharing", "url_private": "https://drive.google.com/file/d/1l85o1oOj5TJd7WTledJWRn40Q829d_dR/view?usp=sharing", "media_display_type": "video", "thumb_video": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NS1T0XEZ-f8fefa0752/comprehensive_demo_thumb_video.jpeg", "thumb_video_w": 1280, "thumb_video_h": 720, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NS1T0XEZ/comprehensive_demo.mp4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "rdi1X", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Competitor's demo if anyone is interested\n"}, {"type": "link", "url": "https://drive.google.com/file/d/1cUqnc13an-rKQptELecGqT8ZDTpZe9SN/view?usp=sharing", "text": "Simply Merit Demo"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://drive.google.com/file/d/1Vv7rumS3oUqPBfFs_9ANgWqbk9ARNQba/view?usp=sharing", "text": "Assemble Demo"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://drive.google.com/file/d/1l85o1oOj5TJd7WTledJWRn40Q829d_dR/view?usp=sharing", "text": "Comprehensive Demo"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://drive.google.com/file/d/1DlFF3ZUuicTRbPoT-NdhKbpty2mE1b_0/view?usp=sharing", "text": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://drive.google.com/file/d/1482slTDH1EScVH5OHbY7SpygG2TuuO5R/view?usp=sharing", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}]}]}, {"ts": "1727132392.307489", "text": "<@U07EJ2LP44S> I hope <PERSON> was able to successfully demo to managers with no major issues. I guess no news is good news:slightly_smiling_face:", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727132392.307489", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "/+07V", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I hope <PERSON> was able to successfully demo to managers with no major issues. I guess no news is good news"}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1727122084.994479", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I have users set up for Sonendo but the integrations page isn't loading. This happens on pretty much every new environment. Should be enabling something else when we create the internal users?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727122084.994479", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "c5+yK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I have users set up for Sonendo but the integrations page isn't loading. This happens on pretty much every new environment. Should be enabling something else when we create the internal users?"}]}]}]}, {"ts": "**********.335259", "text": "<@U0690EB5JE5> Demo account issues are recorded here <https://compiify.atlassian.net/browse/COM-3626>", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13687::db2ede9d2fc946c8934ccc88944c3bc2", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3626?atlOrigin=eyJpIjoiY2QwOTNjMjEyNTcyNGFjNjkwNTNlZTkzODNhOTY1MzMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3626 Demo account issues [Sep 2024]>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13687::8f50684ab098441486996468c3919d68", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13687\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3626\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3626", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "vYqjR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Demo account issues are recorded here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3626"}]}]}]}, {"ts": "**********.585039", "text": "Tithly signed.:partying_face::moneybag: <@U07EJ2LP44S> just introduced you to them to kick off the implementation process.", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "/Zw1g", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> signed."}, {"type": "emoji", "name": "partying_face", "unicode": "1f973"}, {"type": "emoji", "name": "moneybag", "unicode": "1f4b0"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " just introduced you to them to kick off the implementation process."}]}]}]}], "created_at": "2025-05-22T21:35:34.636493"}