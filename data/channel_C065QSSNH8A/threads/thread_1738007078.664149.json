{"thread_ts": "1738007078.664149", "channel_id": "C065QSSNH8A", "reply_count": 7, "replies": [{"ts": "1738039857.205589", "text": "Will reset the ENV with this data.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738007078.664149", "blocks": [{"type": "rich_text", "block_id": "6iIae", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will reset the ENV with this data."}]}]}]}, {"ts": "1738070891.386379", "text": "<@U07EJ2LP44S> updated and cycle created. I think there would be issue with band assign due to title mismatches. We can discuss when we meet ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738007078.664149", "blocks": [{"type": "rich_text", "block_id": "Tlcw5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " updated and cycle created. I think there would be issue with band assign due to title mismatches. We can discuss when we meet "}]}]}]}, {"ts": "1738075838.857959", "text": "She's going to manually change the titles in the application - so I'll just send her the ones that aren't matched. Thank you!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738007078.664149", "blocks": [{"type": "rich_text", "block_id": "WOG7d", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She's going to manually change the titles in the application - so I'll just send her the ones that aren't matched. Thank you!"}]}]}]}, {"ts": "1738075875.544079", "text": "Cool", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738007078.664149", "blocks": [{"type": "rich_text", "block_id": "4u7SE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Cool"}]}]}]}, {"ts": "1738076232.562669", "text": "There's only a handful, so looks easy enough", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738007078.664149", "blocks": [{"type": "rich_text", "block_id": "qVnTd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There's only a handful, so looks easy enough"}]}]}]}, {"ts": "1738076766.335469", "text": "<@U07EJ2LP44S> Also the performance &amp; compa ratio matrix heads are updated as per Tithleys settings. Have created cycle but have not update the ranges. I can take that up tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738007078.664149", "blocks": [{"type": "rich_text", "block_id": "yrD1S", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Also the performance & compa ratio matrix heads are updated as per Tithleys settings. Have created cycle but have not update the ranges. I can take that up tomorrow."}]}]}]}, {"ts": "1738079003.666819", "text": "<@U07EJ2LP44S> FYI...These two employees were earlier deliberately deleted and not true terminations. So there would be difference of 2 employees vs dump i.e. 174 vs 172 in ENV.\n1. 280\t- <PERSON>\n2. 300 -\t<PERSON>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738007078.664149", "edited": {"user": "U0690EB5JE5", "ts": "1738079611.000000"}, "blocks": [{"type": "rich_text", "block_id": "CQRBD", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " FYI...These two employees were earlier deliberately deleted and not true terminations. So there would be difference of 2 employees vs dump i.e. 174 vs 172 in ENV.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "280\t- <PERSON>"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "300 -\t<PERSON>"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}], "created_at": "2025-05-22T22:00:49.347858"}