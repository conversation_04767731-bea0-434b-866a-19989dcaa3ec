{"date": "2024-06-25", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1719328694.740679", "text": "For the Jira tickets, that are listed as done in Jira, are they already QA  RE-TESTED by <PERSON> or do we still need to re-test them for accuracy?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719328694.740679", "reply_count": 2, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "pb7kH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For the Jira tickets, that are listed as done in Jira, are they already QA  RE-TESTED by <PERSON> or do we still need to re-test them for accuracy?"}]}]}]}, {"ts": "1719327662.672459", "text": "<@U065H3M6WJV> last week you had mentioned that you have completed about 80% of the QA testing. What are the functionalities that are yet to be QA tested. I know equity is not tested yet. What else is remaining?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4HlXA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " last week you had mentioned that you have completed about 80% of the QA testing. What are the functionalities that are yet to be QA tested. I know equity is not tested yet. What else is remaining?"}]}]}]}, {"ts": "1719263661.500879", "text": "<@U065H3M6WJV> Is valgenesis pushing performance ratings from CA to hibob?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719263661.500879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "+XyJc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Is valgenesis pushing performance ratings from CA to hibob?"}]}]}]}, {"ts": "1719262679.493269", "text": "<@U065H3M6WJV> were previous calls with Valgenesis recorded? if so, can you share the recordings?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719262679.493269", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "jJNpl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " were previous calls with <PERSON><PERSON> recorded? if so, can you share the recordings?"}]}]}]}, {"ts": "1719262331.158409", "text": "<@U0690EB5JE5> can we pls add equity in tomorrow eng discussion as well? I need to understand what changes we are making, what else needs to be done in order to meet <PERSON><PERSON>'s requrements. Thanks", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1719262383.000000"}, "blocks": [{"type": "rich_text", "block_id": "oRTkC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we pls add equity in tomorrow eng discussion as well? I need to understand what changes we are making, what else needs to be done in order to meet <PERSON><PERSON>'s requrements. Thanks"}]}]}]}, {"ts": "1719261551.194239", "text": "how do I login to test the Merit 2.0?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719261551.194239", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "OwN5f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "how do I login to test the Merit 2.0?"}]}]}]}, {"ts": "1719261342.596699", "text": "<@U04DKEFP1K8> do you want to meet now for going over valgenesis env?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719261342.596699", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "HXyGZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " do you want to meet now for going over valgenesis env?"}]}]}]}, {"ts": "1719261319.206199", "text": "<@U04DKEFP1K8> what's the Epic number for Merit 2.0 issues that <PERSON> has raised. Is it this one?\n<https://compiify.atlassian.net/browse/COM-2341>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719261319.206199", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12402::46656620326911ef9187bb4686506007", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2341?atlOrigin=eyJpIjoiOTY1MmU1MmQxMGRjNDVjMWFjZGI3YWYwZWJlNGJhMjciLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2341 Merit View V2 Feedback>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12402::46656622326911ef9187bb4686506007", "elements": [{"type": "mrkdwn", "text": "Status: *In Development*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/082e10750208b91e38660f50da682c58?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12402::46656621326911ef9187bb4686506007", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12402\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12402\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2341", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Zl1xK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what's the Epic number for Merit 2.0 issues that <PERSON> has raised. Is it this one?\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2341"}]}]}]}, {"ts": "1719255765.365529", "text": "<@U065H3M6WJV> Can you please make me the owner of eng discussion series starting tomorrow", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1719255765.365529", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "y6Bmy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Can you please make me the owner of eng discussion series starting tomorrow"}]}]}]}], "created_at": "2025-05-22T21:35:34.623928"}