{"date": "2024-10-01", "channel_id": "C065QSSNH8A", "message_count": 18, "messages": [{"ts": "1727803038.352629", "text": "<!here> i will be away between 130pm - 330pm pst today ( i need to drive my parents to airport)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5Zd9c", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " i will be away between 130pm - 330pm pst today ( i need to drive my parents to airport)"}]}]}]}, {"ts": "**********.717399", "text": "Also what is the status of pulling in HRIS data to <PERSON><PERSON><PERSON>'s account? (<@U04DKEFP1K8> <@U0690EB5JE5>) It's been some time, maybe a week and a half at least. (I know the urgency of demo took over)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.717399", "reply_count": 22, "edited": {"user": "U07EJ2LP44S", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "qnasx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also what is the status of pulling in HRIS data to <PERSON><PERSON><PERSON>'s account? ("}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": ") It's been some time, maybe a week and a half at least. (I know the urgency of demo took over)"}]}]}]}, {"ts": "**********.138819", "text": "As I find general UX bugs, what epic should I use? For example, this (found in Curana)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.138819", "reply_count": 1, "files": [{"id": "F07PFSARUAK", "created": **********, "timestamp": **********, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 50449, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07PFSARUAK/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07PFSARUAK/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PFSARUAK-0e519e8511/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PFSARUAK-0e519e8511/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PFSARUAK-0e519e8511/image_360.png", "thumb_360_w": 360, "thumb_360_h": 237, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PFSARUAK-0e519e8511/image_480.png", "thumb_480_w": 480, "thumb_480_h": 316, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PFSARUAK-0e519e8511/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PFSARUAK-0e519e8511/image_720.png", "thumb_720_w": 720, "thumb_720_h": 473, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PFSARUAK-0e519e8511/image_800.png", "thumb_800_w": 800, "thumb_800_h": 526, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PFSARUAK-0e519e8511/image_960.png", "thumb_960_w": 960, "thumb_960_h": 631, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PFSARUAK-0e519e8511/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 673, "original_w": 1092, "original_h": 718, "thumb_tiny": "AwAfADDTNJhvUflTZf8AVt9KbbkmPn1p20uBLRRRSAKKKKAGTf6pvpVbcREMEjk9KtsNwxTfKHTAx9KpOwEO5vOIycY/pTQ7eUfmPUd6seWM54z64o8oYxgY+lF0MhDN5kfzHoO9WaZ5YyDxkdOKeKTdxH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07PFSARUAK/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07PFSARUAK-7ffed9e78c", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "rx3cz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "As I find general UX bugs, what epic should I use? For example, this (found in Curana)"}]}]}]}, {"ts": "1727795010.413539", "text": "<@U04DKEFP1K8> We still looking into cycle issues reported. Will share an update in tomorrow’s call.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "qjD8t", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " We still looking into cycle issues reported. Will share an update in "}, {"type": "text", "text": "tomorrow’s"}, {"type": "text", "text": " call."}]}]}]}, {"ts": "1727794974.585869", "text": "Feel better <PERSON><PERSON><PERSON>, I will send you the recording!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727794974.585869", "reply_count": 1, "reactions": [{"name": "thankyouty", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "u0C8I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Feel better <PERSON><PERSON><PERSON>, I will send you the recording!"}]}]}]}, {"ts": "1727794950.768019", "text": "<!here> I am having mild headache and tiredness since my evening. Need to rest. Please record the session with <PERSON>. I will go through it my tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727794950.768019", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "URtFM", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I am having mild headache and tiredness since my evening. Need to rest. Please record the session with <PERSON>. I will go through it my tomorrow."}]}]}]}, {"ts": "1727794872.159389", "text": "<@U07M6QKHUC9> Could you give me the basics about <http://Tithe.ly|Tithe.ly> for our call today? Number of employees, HRIS, what kind of cycles - anything you know", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727794872.159389", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "rpAZW", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Could you give me the basics about "}, {"type": "link", "url": "http://Tithe.ly", "text": "Tithe.ly"}, {"type": "text", "text": " for our call today? Number of employees, HRIS, what kind of cycles - anything you know"}]}]}]}, {"ts": "1727750912.003789", "text": "Sure ", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hRHH0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure "}]}]}]}, {"ts": "1727750685.425919", "text": "<@U07M6QKHUC9> FYI … tomorrow is central holiday in India. But I can attend the call tomorrow.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4QRHN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " FYI "}, {"type": "text", "text": "…"}, {"type": "text", "text": " tomorrow is central holiday in India. But I can attend the call tomorrow."}]}]}]}, {"ts": "1727747690.063629", "text": "<@U07EJ2LP44S> I am sure you already are thinking about it, but can we please double check all the issues in the SDF environment before hand it over to them. If we can delay until the comp builder is clickable, that would be ideal. ", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1727747710.000000"}, "blocks": [{"type": "rich_text", "block_id": "DmnRZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am sure you already are thinking about it, but can we please double check all the issues in the SDF environment before hand it over to them. If we can del"}, {"type": "text", "text": "ay "}, {"type": "text", "text": "until the comp builder is clickable, that would be ideal. "}]}]}]}, {"ts": "1727747594.028629", "text": "She has created a detailed document for the usability experience and we can review that async So that we can focus tomorrow’s call on her concept for cycle planning assistant, which can be a good differentiation, not a sales perspective, but also from a customer retention perspective", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sbnHl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She has created a detailed document for the usability experience and we can review that async So that we can focus tomorrow’s call on her concept for cycle planning assistant, which can be a good differentiation, not a sales perspective, but also from a customer retention perspective"}]}]}]}, {"ts": "1727747521.964749", "text": "<PERSON> is going to be present presenting her concept for cycle planning assistant so will have to on the above agenda on Wednesday", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hrGjK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> is going to be present presenting her concept for cycle planning assistant so will have to on the above agenda on Wednesday"}]}]}]}, {"ts": "1727745719.676489", "text": "Agenda for next leadership stand up\n• Review and align on the eng deliveries so far and next priorities\n• Focus on QA from today for Nov Cycles", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727745719.676489", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1727746717.000000"}, "blocks": [{"type": "rich_text", "block_id": "XiZO3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for next leadership stand up\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Review and align on the eng deliveries so far and next priorities"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Focus on QA from today for Nov Cycles"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1727741248.492299", "text": "<@U0690EB5JE5> we really need to fix these instabilities in the comp builder as soon as possible. It continues to be one of our biggest pain points right now", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727741248.492299", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "9B8t7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we really need to fix these instabilities in the comp builder as soon as possible. It continues to be one of our biggest pain points right now"}]}]}]}, {"ts": "1727740269.137319", "text": "<!here> *Update on Demo Environment:*\n\t•\tThe issue where compensation bands were not being assigned to employees has been resolved (17 / 137 employees do not have bands assigned which is as expected).\n\t•\tCountries *FR* and *AR* have been removed from the environment.\n\t•\tA full reset was not required.\n\t•\t*Open issue*: There is a build issue preventing the addition of bonus recommendations for *CA* ( <PERSON> discussed it with <@U0690EB5JE5> later tonight).", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "1O65Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}, {"type": "text", "text": "Update on Demo Environment:", "style": {"bold": true}}, {"type": "text", "text": "\n\t•\tThe issue where compensation bands were not being assigned to employees has been resolved (17 / 137 employees do not have bands assigned which is as expected).\n\t•\tCountries "}, {"type": "text", "text": "FR", "style": {"bold": true}}, {"type": "text", "text": " and "}, {"type": "text", "text": "AR", "style": {"bold": true}}, {"type": "text", "text": " have been removed from the environment.\n\t•\tA full reset was not required.\n\t•\t"}, {"type": "text", "text": "Open issue", "style": {"bold": true}}, {"type": "text", "text": ": There is a build issue preventing the addition of bonus recommendations for "}, {"type": "text", "text": "CA", "style": {"bold": true}}, {"type": "text", "text": " ( <PERSON> discussed it with "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " later tonight)."}]}]}]}, {"ts": "1727725196.539579", "text": "Also, let get on a call to fix the DKIM issue. It's a little time sensitive so <PERSON> can start new campaigns", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QWMrn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also, let get on a call to fix the DKIM issue. It's a little time sensitive so <PERSON> can start new campaigns"}]}]}]}, {"ts": "1727725151.851639", "text": "<@U04DKEFP1K8> Were we able to fix the demo env yet? If not, what's blocking us", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727725151.851639", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "jkDrm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Were we able to fix the demo env yet? If not, what's blocking us"}]}]}]}, {"ts": "1727721652.956229", "text": "This is an example of one of the little towns, chimney rock, that was swept away by the hurricane.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727721652.956229", "reply_count": 4, "reactions": [{"name": "flushed", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F07PMUY279B", "created": 1727721632, "timestamp": 1727721632, "name": "Image from iOS.jpg", "title": "Image from iOS", "mimetype": "image/jpeg", "filetype": "jpg", "pretty_type": "JPEG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 136776, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "subtype": "slack_image", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07PMUY279B/image_from_ios.jpg", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07PMUY279B/download/image_from_ios.jpg", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PMUY279B-c0ba3f1d35/image_from_ios_64.jpg", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PMUY279B-c0ba3f1d35/image_from_ios_80.jpg", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PMUY279B-c0ba3f1d35/image_from_ios_360.jpg", "thumb_360_w": 360, "thumb_360_h": 270, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PMUY279B-c0ba3f1d35/image_from_ios_480.jpg", "thumb_480_w": 480, "thumb_480_h": 360, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PMUY279B-c0ba3f1d35/image_from_ios_160.jpg", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PMUY279B-c0ba3f1d35/image_from_ios_720.jpg", "thumb_720_w": 720, "thumb_720_h": 540, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PMUY279B-c0ba3f1d35/image_from_ios_800.jpg", "thumb_800_w": 800, "thumb_800_h": 600, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PMUY279B-c0ba3f1d35/image_from_ios_960.jpg", "thumb_960_w": 960, "thumb_960_h": 720, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PMUY279B-c0ba3f1d35/image_from_ios_1024.jpg", "thumb_1024_w": 1024, "thumb_1024_h": 768, "original_w": 1080, "original_h": 810, "thumb_tiny": "AwAkADDQAp2Kow33QSD8RVtJUcZDCi4Dm+6aiY4FLPKqQuQwJA6ZqrHcJJwcg+lNCJi2aQUvGOKazKoyzAUwM8PwcbSfQdanRC3Yj3NChR0FSBsVDKGTRjy2wxziq6RFiQrZI9BVpzkY65FMj3Rr83A60BYQROF4Y801oTg4b5vSkeVw3y81JHLu/wD10XHYBTj2popx7UCGklckdRUI+cszcketSt0P0qJP4/pSY0P8tTGGOc5FMVQCy84IJ/Kph/qh9RUS/fP+6f60Af/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07PMUY279B/image_from_ios.jpg", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07PMUY279B-372b1c5005", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "gO43B", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is an example of one of the little towns, chimney rock, that was swept away by the hurricane."}]}]}]}], "created_at": "2025-05-22T21:35:34.653565"}