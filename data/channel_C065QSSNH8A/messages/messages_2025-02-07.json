{"date": "2025-02-07", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1738951027.888099", "text": "<@U0690EB5JE5> We also need to turn on the email notifications for Diversified as of EOD Monday", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738951027.888099", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "4yqst", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We also need to turn on the email notifications for Diversified as of EOD Monday"}]}]}]}, {"ts": "1738950116.671609", "text": "<@U0690EB5JE5> Can you please upload this for tithely. it is just performance data changes - performance period start/end and score. I am getting a format error as per usual. I left all the other columns exactly as they were since sometimes it seems to want a random column.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738950116.671609", "reply_count": 1, "files": [{"id": "F08C4L7QWCE", "created": 1738950086, "timestamp": 1738950086, "name": "Tithely_PerformanceScores.csv", "title": "Tithely_PerformanceScores.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 45024, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08C4L7QWCE/tithely_performancescores.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08C4L7QWCE/download/tithely_performancescores.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08C4L7QWCE/tithely_performancescores.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08C4L7QWCE-cf3214e822", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08C4L7QWCE/tithely_performancescores.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">234</div><div class=\"cm-col\">David</div><div class=\"cm-col\">Wickstrom</div><div class=\"cm-col\">David Wickstrom</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">10/16/17</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">266</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">10/16/17</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">FL</div><div class=\"cm-col\">Engineer II</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">91416</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/25/24</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">326</div><div class=\"cm-col\">Nicholas</div><div class=\"cm-col\">White</div><div class=\"cm-col\">Nicholas White</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">10/9/23</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">26</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">10/9/23</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">MA</div><div class=\"cm-col\">Product Manager</div><div class=\"cm-col\">Product Management</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">125000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">9/13/23</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">167</div><div class=\"cm-col\">Bradley</div><div class=\"cm-col\">White</div><div class=\"cm-col\">Bradley White</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">4/13/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">218</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">4/13/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">TN</div><div class=\"cm-col\">Engineer 2</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">90537</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/25/24</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">172</div><div class=\"cm-col\">Cory</div><div class=\"cm-col\">Walz</div><div class=\"cm-col\">Cory Walz</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">5/17/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">26</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">5/17/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">OR</div><div class=\"cm-col\">Technical Product Manager</div><div class=\"cm-col\">Product Management</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">95000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">4/8/24</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">57</div><div class=\"cm-col\">Cory</div><div class=\"cm-col\">Wadstrom</div><div class=\"cm-col\">Cory Wadstrom</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">2/4/19</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">18</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">2/4/19</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">AR</div><div class=\"cm-col\">Sales Operations Manager</div><div class=\"cm-col\">Sales</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">135000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">4/1/22</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">13</div><div class=\"cm-col\">Tyler</div><div class=\"cm-col\">Viers</div><div class=\"cm-col\">Tyler Viers</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">9/25/17</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">199</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">9/25/17</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">TN</div><div class=\"cm-col\">Sr. Customer Support Advocate</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Part Time</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">50960</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">9/23/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">24.5</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 174, "lines_more": 173, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "EEzMR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you please upload this for tithely. it is just performance data changes - performance period start/end and score. I am getting a format error as per usual. I left all the other columns exactly as they were since sometimes it seems to want a random column."}]}]}]}, {"ts": "1738949320.475859", "text": "<@U0690EB5JE5> BUG (I think? maybe due to the budget thing) - Added <PERSON> to recommenders list. Confirmed he's there. He's not in the budget allocation. After publishing, he's not in the merit view. He is assigned a manager role. How do we get him into the merit cycle as a recommender?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738949320.475859", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "1xlhC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " BUG (I think? maybe due to the budget thing) - Added <PERSON> to recommenders list. Confirmed he's there. He's not in the budget allocation. After publishing, he's not in the merit view. He is assigned a manager role. How do we get him into the merit cycle as a recommender?"}]}]}]}, {"ts": "1738949029.584709", "text": "and please add <PERSON>, <mailto:<PERSON><PERSON><PERSON><PERSON>@dgoc.com|<PERSON><PERSON><PERSON><PERSON>@dgoc.com> to SSO", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738949029.584709", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "U3hbo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "and please add <PERSON>, "}, {"type": "link", "url": "mailto:<PERSON><PERSON><EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " to SSO"}]}]}]}, {"ts": "1738949005.630699", "text": "<@U0690EB5JE5> can you re-run diversifieds budget with this new number: 14514944.4", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738949005.630699", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "MOusa", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you re-run diversifieds budget with this new number: 14514944.4"}]}]}]}, {"ts": "1738944405.568679", "text": "Would we be able to change the continue with azure ad icon to continue with microsoft sso instead?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738944405.568679", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "8aCes", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Would we be able to change the continue with azure ad icon to continue with microsoft sso instead?"}]}]}]}, {"ts": "**********.773559", "text": "<@U0690EB5JE5> There is also an issue with the overall data, employees are not showing a status or their performance ratings. I explained the best I could here: <https://www.loom.com/share/7ccedf644cfb4c00b552d22aefe64878?sid=b88f140d-c2c7-4f40-98ea-d631fe6c38b6>\n\nAlso I had data in the filters at the beginning of the call and by the end the filters were showing blank.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.773559", "reply_count": 9, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "mSFu0", "video_url": "https://www.loom.com/embed/7ccedf644cfb4c00b552d22aefe64878?sid=b88f140d-c2c7-4f40-98ea-d631fe6c38b6&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/7ccedf644cfb4c00b552d22aefe64878-0758ca4d4e03f48e-4x3.jpg", "alt_text": "Tidally Account Data Issues 🔍", "title": {"type": "plain_text", "text": "Tidally Account Data Issues 🔍", "emoji": true}, "title_url": "https://www.loom.com/share/7ccedf644cfb4c00b552d22aefe64878", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 4 min  ", "emoji": true}}, {"type": "section", "block_id": "ACBCo", "text": {"type": "mrkdwn", "text": ":information_source: In this video, I discuss the recent findings regarding <PERSON><PERSON><PERSON>'s account data while we were supposed to focus on training. We discovered that the...", "verbatim": false}}, {"type": "actions", "block_id": "SXezv", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/7ccedf644cfb4c00b552d22aefe64878?sid=b88f140d-c2c7-4f40-98ea-d631fe6c38b6"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"7ccedf644cfb4c00b552d22aefe64878\",\"videoName\":\"Tidally Account Data Issues 🔍\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://www.loom.com/share/7ccedf644cfb4c00b552d22aefe64878?sid=b88f140d-c2c7-4f40-98ea-d631fe6c38b6", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "0UqDu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " There is also an issue with the overall data, employees are not showing a status or their performance ratings. I explained the best I could here: "}, {"type": "link", "url": "https://www.loom.com/share/7ccedf644cfb4c00b552d22aefe64878?sid=b88f140d-c2c7-4f40-98ea-d631fe6c38b6"}, {"type": "text", "text": "\n\nAlso I had data in the filters at the beginning of the call and by the end the filters were showing blank."}]}]}]}], "created_at": "2025-05-22T21:35:34.701425"}