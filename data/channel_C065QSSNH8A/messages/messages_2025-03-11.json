{"date": "2025-03-11", "channel_id": "C065QSSNH8A", "message_count": 20, "messages": [{"ts": "1741716511.279239", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1741716285.391739", "text": "Functionally we cannot fix this as numbers are edited at manager level and there is no way we can fix this with current allocation behaviour ", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1741716388.000000"}, "blocks": [{"type": "rich_text", "block_id": "FVDqu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Functionally we cannot"}, {"type": "text", "text": " fix this"}, {"type": "text", "text": " as numbers are edited at manager level and there is no way we can fix this with current allocation behaviour "}]}]}]}, {"ts": "1741716225.308839", "text": "Yes that’s correct ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "B/TgJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes that’s correct "}]}]}]}, {"ts": "1741716018.720809", "text": "are we saying that if admin makes changes to allocation in comp builder, and publishes the cycle, then cycle insights are meaningless for budget utilization metrics?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "2I3hl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "are we saying that if admin makes changes to allocation in comp builder, and publishes the cycle, then cycle insights are meaningless for budget utilization metrics?"}]}]}]}, {"ts": "1741715611.834709", "text": "You're welcome L<PERSON>. I try.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3ta6c", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "You're welcome L<PERSON>. I try."}]}]}]}, {"ts": "1741715493.195519", "text": "Thank You <@U07EJ2LP44S> , You saved me quite some time :slightly_smiling_face: .", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pq+zh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank You "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " , You saved me quite some time "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " ."}]}]}]}, {"ts": "1741715465.272579", "text": "<@U07M6QKHUC9> yes <PERSON> is correct. The numbers in insights are system calculated but merit table is manually edited numbers. This cannot be fixed as the edited numbers are at the manager level.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XzlYK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " yes <PERSON> is correct. The numbers in insights are system calculated but merit table is manually edited numbers. This cannot be fixed as the edited numbers are at the manager level."}]}]}]}, {"ts": "1741715093.490609", "text": "on a call. will review later", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qRCfI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "on a call. will review later"}]}]}]}, {"ts": "1741715057.490489", "text": "ok. <@U07M6QKHUC9> I will confirm if its because of the edited numbers, if not then This definitely takes time as the code is very complicated to debug and fix as well.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RF495", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok. "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I will confirm if its because of the edited numbers, if not then This definitely takes time as the code is very complicated to debug and fix as well."}]}]}]}, {"ts": "1741714984.687119", "text": "The calculation logic looks complicated in the code for insights, its difficult to debug without any details.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mqfG6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The calculation logic looks complicated in the code for insights, its difficult to debug without any details."}]}]}]}, {"ts": "1741714973.271669", "text": "before the cycle started but yes", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6VchR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "before the cycle started but yes"}]}]}]}, {"ts": "1741714968.490079", "text": "Yes they edited in the allocations page", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "bInIU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes they edited in the allocations page"}]}]}]}, {"ts": "1741714937.944599", "text": "<@U07EJ2LP44S> did they edit the budgets?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "JdOuJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " did they edit the budgets?"}]}]}]}, {"ts": "1741714879.962809", "text": "It might be because they did an override of the budget? I am wondering if the original % allocation is being applied, not the edited allocation changes they made.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nom04", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It might be because they did an override of the budget? I am wondering if the original % allocation is being applied, not the edited allocation changes they made."}]}]}]}, {"ts": "1741713163.764809", "text": "Let me check, but would push back deployments at this time.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9xv/f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me check, but would push back deployments at this time."}]}]}]}, {"ts": "1741712868.281679", "text": "ideally today since they are in the middle of cycle", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nOTLm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ideally today since they are in the middle of cycle"}]}]}]}, {"ts": "1741712840.084959", "text": "<@U07M6QKHUC9> will check in sometime, will need to debug. Can this wait till tomorrow as this needs code fix and deployment if there is a bug.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "2UCrE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " will check in sometime, will need to debug. Can this wait till tomorrow as this needs code fix and deployment if there is a bug."}]}]}]}, {"ts": "1741712253.787109", "text": "<@U0690EB5JE5> Check out <PERSON>'s comments on Tithely channel. Can you address this?\n\nHello! I have a question about the cycle insights tab. It looks like the numbers aren’t reflective of the cycle. For example, the total CX budget is $145k but the insights are showing it as $118k (screenshots attached). This is the same case for all departments. The total number is off because of that as well. Any ideas on how to correct this?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "as7pg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Check out <PERSON>'s comments on Tithely channel. Can you address this?\n\nHello! I have a question about the cycle insights tab. It looks like the numbers aren’t reflective of the cycle. For example, the total CX budget is $145k but the insights are showing it as $118k (screenshots attached). This is the same case for all departments. The total number is off because of that as well. Any ideas on how to correct this?"}]}]}]}, {"ts": "1741708188.658629", "text": "She’s wanting column freeze by user", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741708188.658629", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Nq02i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She’s wanting column freeze by user"}]}]}]}, {"ts": "1741707997.936689", "text": "<@U07EJ2LP44S> we do show the breakdown of merit components in both my tasks and in comp planner view. It's not perfect but we do have this feature.  We also have the ability to adjust columns freeze. Is there something more to what <PERSON> is asking?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ATVM8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " we do show the breakdown of merit components in both my tasks and in comp planner view. It's not perfect but we do have this feature.  We also have the ability to adjust columns freeze. Is there something more to what <PERSON> is asking?"}]}]}]}], "created_at": "2025-05-22T21:35:34.714103"}