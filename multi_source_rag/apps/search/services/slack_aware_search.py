"""
Slack-Aware Search Service with Enhanced Metadata Filtering

This service provides advanced search capabilities specifically designed for Slack data,
utilizing the enhanced metadata for powerful filtering and context-aware retrieval.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

from apps.core.utils.llama_index_vectorstore import search_vector_store
from apps.core.utils.slack_metadata_enhancer import create_slack_search_filter
from apps.core.utils.collection_manager import get_collection_name

logger = logging.getLogger(__name__)


@dataclass
class SlackSearchParams:
    """Parameters for Slack-aware search."""
    
    # Time-based filters
    days_back: Optional[int] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    
    # User and participation filters
    participants: Optional[List[str]] = None
    exclude_participants: Optional[List[str]] = None
    min_participants: Optional[int] = None
    
    # Channel filters
    channels: Optional[List[str]] = None
    channel_types: Optional[List[str]] = None  # public, private_group, direct_message
    
    # Content quality filters
    quality_tier: Optional[str] = None  # high, medium, low, very_low
    min_quality_score: Optional[float] = None
    
    # Engagement filters
    has_reactions: Optional[bool] = None
    min_reactions: Optional[int] = None
    highly_reacted: Optional[bool] = None
    
    # Technical content filters
    has_code: Optional[bool] = None
    is_technical: Optional[bool] = None
    technical_terms: Optional[List[str]] = None
    
    # Conversation filters
    conversation_size: Optional[str] = None  # small, medium, large, very_large
    has_threads: Optional[bool] = None
    has_questions: Optional[bool] = None
    
    # Content pattern filters
    has_urls: Optional[bool] = None
    has_mentions: Optional[bool] = None


class SlackAwareSearchService:
    """
    Advanced search service for Slack data with metadata-based filtering.
    """
    
    def __init__(self, tenant_slug: str):
        """
        Initialize the Slack-aware search service.
        
        Args:
            tenant_slug: Tenant slug for search isolation
        """
        self.tenant_slug = tenant_slug
    
    def search(
        self,
        query: str,
        params: Optional[SlackSearchParams] = None,
        k: int = 10,
        collection_name: Optional[str] = None
    ) -> List[Tuple[Dict[str, Any], float]]:
        """
        Perform Slack-aware search with enhanced metadata filtering.
        
        Args:
            query: Search query
            params: Slack search parameters
            k: Number of results to return
            collection_name: Optional collection name override
            
        Returns:
            List of (document, score) tuples
        """
        if params is None:
            params = SlackSearchParams()
        
        # Create metadata filter from search parameters
        metadata_filter = self._create_metadata_filter(params)
        
        # Determine collection name
        if not collection_name:
            collection_name = get_collection_name(self.tenant_slug, intent="conversation")
        
        logger.info(f"Searching Slack data with query: '{query}' and {len(metadata_filter)} filters")
        
        # Perform vector search with metadata filtering
        results = search_vector_store(
            query=query,
            collection_name=collection_name,
            tenant_slug=self.tenant_slug,
            metadata_filter=metadata_filter,
            k=k,
            intent="conversation"
        )
        
        # Convert results to our format
        formatted_results = []
        for doc, score in results:
            doc_dict = {
                "content": doc.page_content,
                "metadata": doc.metadata,
                "score": score
            }
            formatted_results.append((doc_dict, score))
        
        logger.info(f"Found {len(formatted_results)} Slack results")
        return formatted_results
    
    def _create_metadata_filter(self, params: SlackSearchParams) -> Dict[str, Any]:
        """
        Create metadata filter from search parameters.
        
        Args:
            params: Slack search parameters
            
        Returns:
            Dict[str, Any]: Metadata filter for vector search
        """
        filter_kwargs = {}
        
        # Time-based filtering
        time_range = self._create_time_range_filter(params)
        if time_range:
            filter_kwargs["time_range"] = time_range
        
        # User filtering
        if params.participants:
            filter_kwargs["participants"] = params.participants
        
        # Channel filtering
        if params.channels:
            filter_kwargs["channels"] = params.channels
        
        # Quality filtering
        if params.quality_tier:
            filter_kwargs["quality_tier"] = params.quality_tier
        
        # Boolean filters
        if params.has_code is not None:
            filter_kwargs["has_code"] = params.has_code
        
        if params.has_reactions is not None:
            filter_kwargs["has_reactions"] = params.has_reactions
        
        if params.is_technical is not None:
            filter_kwargs["is_technical"] = params.is_technical
        
        if params.has_threads is not None:
            filter_kwargs["has_threads"] = params.has_threads
        
        if params.has_questions is not None:
            filter_kwargs["has_questions"] = params.has_questions
        
        if params.has_urls is not None:
            filter_kwargs["has_urls"] = params.has_urls
        
        if params.has_mentions is not None:
            filter_kwargs["has_mentions"] = params.has_mentions
        
        # Conversation size filtering
        if params.conversation_size:
            filter_kwargs["conversation_size"] = params.conversation_size
        
        # Advanced filters
        if params.min_quality_score is not None:
            filter_kwargs["quality_score"] = {"range": {"gte": params.min_quality_score}}
        
        if params.min_reactions is not None:
            filter_kwargs["total_reactions"] = {"range": {"gte": params.min_reactions}}
        
        if params.min_participants is not None:
            filter_kwargs["participant_count"] = {"range": {"gte": params.min_participants}}
        
        if params.highly_reacted is not None:
            filter_kwargs["highly_reacted"] = params.highly_reacted
        
        # Create the actual filter
        return create_slack_search_filter(**filter_kwargs)
    
    def _create_time_range_filter(self, params: SlackSearchParams) -> Optional[Dict[str, int]]:
        """
        Create time range filter from parameters.
        
        Args:
            params: Slack search parameters
            
        Returns:
            Optional[Dict[str, int]]: Time range filter with timestamps
        """
        time_range = {}
        
        # Handle days_back parameter
        if params.days_back is not None:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=params.days_back)
            time_range["start_timestamp"] = int(start_time.timestamp())
            time_range["end_timestamp"] = int(end_time.timestamp())
        
        # Handle explicit date range
        if params.start_date:
            try:
                start_dt = datetime.fromisoformat(params.start_date)
                time_range["start_timestamp"] = int(start_dt.timestamp())
            except ValueError:
                logger.warning(f"Invalid start_date format: {params.start_date}")
        
        if params.end_date:
            try:
                end_dt = datetime.fromisoformat(params.end_date)
                time_range["end_timestamp"] = int(end_dt.timestamp())
            except ValueError:
                logger.warning(f"Invalid end_date format: {params.end_date}")
        
        return time_range if time_range else None
    
    def search_recent_discussions(
        self,
        query: str,
        days_back: int = 7,
        min_participants: int = 2,
        k: int = 10
    ) -> List[Tuple[Dict[str, Any], float]]:
        """
        Search for recent discussions with multiple participants.
        
        Args:
            query: Search query
            days_back: Number of days to look back
            min_participants: Minimum number of participants
            k: Number of results to return
            
        Returns:
            List of (document, score) tuples
        """
        params = SlackSearchParams(
            days_back=days_back,
            min_participants=min_participants,
            has_threads=True
        )
        return self.search(query, params, k)
    
    def search_technical_discussions(
        self,
        query: str,
        has_code: bool = True,
        quality_tier: str = "medium",
        k: int = 10
    ) -> List[Tuple[Dict[str, Any], float]]:
        """
        Search for technical discussions with code.
        
        Args:
            query: Search query
            has_code: Filter for discussions with code
            quality_tier: Minimum quality tier
            k: Number of results to return
            
        Returns:
            List of (document, score) tuples
        """
        params = SlackSearchParams(
            has_code=has_code,
            is_technical=True,
            quality_tier=quality_tier
        )
        return self.search(query, params, k)
    
    def search_popular_content(
        self,
        query: str,
        min_reactions: int = 3,
        quality_tier: str = "high",
        k: int = 10
    ) -> List[Tuple[Dict[str, Any], float]]:
        """
        Search for popular content with reactions.
        
        Args:
            query: Search query
            min_reactions: Minimum number of reactions
            quality_tier: Minimum quality tier
            k: Number of results to return
            
        Returns:
            List of (document, score) tuples
        """
        params = SlackSearchParams(
            min_reactions=min_reactions,
            quality_tier=quality_tier,
            has_reactions=True
        )
        return self.search(query, params, k)
    
    def search_by_participants(
        self,
        query: str,
        participants: List[str],
        days_back: Optional[int] = None,
        k: int = 10
    ) -> List[Tuple[Dict[str, Any], float]]:
        """
        Search for conversations involving specific participants.
        
        Args:
            query: Search query
            participants: List of participant usernames
            days_back: Optional time limit
            k: Number of results to return
            
        Returns:
            List of (document, score) tuples
        """
        params = SlackSearchParams(
            participants=participants,
            days_back=days_back
        )
        return self.search(query, params, k)


# Convenience functions for easy integration
def search_slack_content(
    tenant_slug: str,
    query: str,
    **kwargs
) -> List[Tuple[Dict[str, Any], float]]:
    """
    Convenience function for Slack content search.
    
    Args:
        tenant_slug: Tenant slug
        query: Search query
        **kwargs: Search parameters
        
    Returns:
        List of (document, score) tuples
    """
    service = SlackAwareSearchService(tenant_slug)
    params = SlackSearchParams(**kwargs)
    return service.search(query, params)


def search_recent_slack_discussions(
    tenant_slug: str,
    query: str,
    days_back: int = 7
) -> List[Tuple[Dict[str, Any], float]]:
    """
    Convenience function for recent Slack discussions.
    
    Args:
        tenant_slug: Tenant slug
        query: Search query
        days_back: Number of days to look back
        
    Returns:
        List of (document, score) tuples
    """
    service = SlackAwareSearchService(tenant_slug)
    return service.search_recent_discussions(query, days_back)
