"""
Django management command to create API tokens for testing.
"""

import secrets
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token
from apps.accounts.models import Tenant


class Command(BaseCommand):
    help = 'Create an API token for a user'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            default='test_user',
            help='Username for the API token (default: test_user)'
        )
        parser.add_argument(
            '--tenant-slug',
            type=str,
            default='test-tenant',
            help='Tenant slug (default: test-tenant)'
        )
        parser.add_argument(
            '--create-user',
            action='store_true',
            help='Create the user if it does not exist'
        )

    def handle(self, *args, **options):
        username = options['username']
        tenant_slug = options['tenant_slug']
        create_user = options['create_user']

        # Get or create tenant
        tenant, created = Tenant.objects.get_or_create(
            slug=tenant_slug,
            defaults={
                'name': f'Test Tenant ({tenant_slug})',
                'is_active': True
            }
        )
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created tenant: {tenant.name}')
            )
        else:
            self.stdout.write(f'Using existing tenant: {tenant.name}')

        # Get or create user
        try:
            user = User.objects.get(username=username)
            self.stdout.write(f'Using existing user: {username}')
        except User.DoesNotExist:
            if create_user:
                user = User.objects.create_user(
                    username=username,
                    email=f'{username}@example.com',
                    password='testpassword123'
                )
                self.stdout.write(
                    self.style.SUCCESS(f'Created user: {username}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f'User {username} does not exist. Use --create-user to create it.')
                )
                return

        # Create or get API token
        token, created = Token.objects.get_or_create(user=user)
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created new API token for {username}')
            )
        else:
            self.stdout.write(f'Using existing API token for {username}')

        self.stdout.write('')
        self.stdout.write(self.style.SUCCESS('API Token Details:'))
        self.stdout.write(f'  Username: {user.username}')
        self.stdout.write(f'  Tenant: {tenant.name} ({tenant.slug})')
        self.stdout.write(f'  Token: {token.key}')
        self.stdout.write('')
        self.stdout.write('Use this token in API requests:')
        self.stdout.write(f'  Authorization: Token {token.key}')
        self.stdout.write('')
        self.stdout.write('Example curl command:')
        self.stdout.write(f'  curl -H "Authorization: Token {token.key}" \\')
        self.stdout.write(f'       -H "Content-Type: application/json" \\')
        self.stdout.write(f'       -d \'{{"query": "test query", "tenant_slug": "{tenant.slug}"}}\' \\')
        self.stdout.write(f'       http://localhost:8000/api/search/')
