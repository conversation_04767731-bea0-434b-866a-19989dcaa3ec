{"thread_ts": "1737659237.807219", "channel_id": "C065QSSNH8A", "reply_count": 3, "replies": [{"ts": "1737659574.064629", "text": "Yes root employee doesn’t show up by design. Which is the reason we added one dummy root in Diven", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737659237.807219", "blocks": [{"type": "rich_text", "block_id": "59LW9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes root employee "}, {"type": "text", "text": "doesn’t"}, {"type": "text", "text": " show up by design. Which is the reason we added one dummy root in Diven"}]}]}]}, {"ts": "1737659765.542609", "text": "Why would we not show the root user? We should be able to see them in the org view the same way we can see anyone else.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737659237.807219", "blocks": [{"type": "rich_text", "block_id": "BJVZT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Why would we not show the root user? We should be able to see them in the org view the same way we can see anyone else."}]}]}]}, {"ts": "1737659977.323099", "text": "In general we don’t show the login user itself in their view. Super Admin view by default has root employee view. Yeah not ideal.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737659237.807219", "reactions": [{"name": "white_check_mark", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "gOq+I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In general we "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " show the login user itself in their view. Super Admin view by default has root employee view. Yeah not ideal."}]}]}]}], "created_at": "2025-05-22T22:00:49.348994"}