{"date": "2025-03-05", "channel_id": "C065QSSNH8A", "message_count": 15, "messages": [{"ts": "1741197243.744769", "text": "<@U07MH77PUBV> Can you tell why the employee Director of Product <PERSON> / Director of Product is not mapping to the Director of Product payband? He used to be Group Product Manager and even after his title change he is not remapping to the band. This is still Tithely", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741197243.744769", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "z41Gi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " Can you tell why the employee Director of Product <PERSON> / Director of Product is not mapping to the Director of Product payband? He used to be Group Product Manager and even after his title change he is not remapping to the band. This is still Tithely"}]}]}]}, {"ts": "1741196471.285449", "text": "<@U0690EB5JE5> <@U07MH77PUBV> Another update to a Tithely payband", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741196471.285449", "reply_count": 2, "files": [{"id": "F08GDUKVDTN", "created": 1741196470, "timestamp": 1741196470, "name": "TithelyPeopleOpsUpdate.csv", "title": "TithelyPeopleOpsUpdate.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 704, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GDUKVDTN/tithelypeopleopsupdate.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GDUKVDTN/download/tithelypeopleopsupdate.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GDUKVDTN/tithelypeopleopsupdate.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GDUKVDTN-2fa77e5ee7", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GDUKVDTN/tithelypeopleopsupdate.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Band Id,Effective Start Date,Effective End Date,Compensation Grade,Location (Country),Location (Region),Team,Department,Job Family,Job Subfamily,Description,Tier,Job Category,Job Level,Job Title,Frequency,Salary Currency,Salary Min Amount,Salary Target Amount,Salary Max Amount,Variable Pay Currency,Variable Pay Target (%),Variable Pay Target Amount,Equity Currency,Equity Target Amount,Equity Target (unit),Equity Target (%),Min Equity (unit),Max Equity (unit),Equity Participation Rate (%),OTE Min Amount,OTE Target Amount,OTE Max Amount,Pay Mix\r\nU,59,1/1/21,,,US,,,People Ops,People Ops,,,0,Mgr,0,Head of People Ops,Annual,USD,126615,164600,189923,,0,0,,0,0,0,0,0,0,0,0,0,", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Band Id</div><div class=\"cm-col\">Effective Start Date</div><div class=\"cm-col\">Effective End Date</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Location (Country)</div><div class=\"cm-col\">Location (Region)</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">Job Subfamily</div><div class=\"cm-col\">Description</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Frequency</div><div class=\"cm-col\">Salary Currency</div><div class=\"cm-col\">Salary Min Amount</div><div class=\"cm-col\">Salary Target Amount</div><div class=\"cm-col\">Salary Max Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay Target (%)</div><div class=\"cm-col\">Variable Pay Target Amount</div><div class=\"cm-col\">Equity Currency</div><div class=\"cm-col\">Equity Target Amount</div><div class=\"cm-col\">Equity Target (unit)</div><div class=\"cm-col\">Equity Target (%)</div><div class=\"cm-col\">Min Equity (unit)</div><div class=\"cm-col\">Max Equity (unit)</div><div class=\"cm-col\">Equity Participation Rate (%)</div><div class=\"cm-col\">OTE Min Amount</div><div class=\"cm-col\">OTE Target Amount</div><div class=\"cm-col\">OTE Max Amount</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">59</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">People Ops</div><div class=\"cm-col\">People Ops</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Mgr</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Head of People Ops</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">126615</div><div class=\"cm-col cm-num\">164600</div><div class=\"cm-col cm-num\">189923</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 2, "lines_more": 0, "preview_is_truncated": false, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "mF2pA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " Another update to a Tithely payband"}]}]}]}, {"ts": "1741187003.554229", "text": "Let me do it in 30 min", "user": "U07MH77PUBV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fUoQh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me do it in 30 min"}]}]}]}, {"ts": "1741185520.684429", "text": "<@U07MH77PUBV> Can you upload this file to tithely's paybands? We were missing the Job Family and it didn't work last time.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741185520.684429", "reply_count": 1, "files": [{"id": "F08GLBZTS2D", "created": 1741185519, "timestamp": 1741185519, "name": "TithelyBandsUpdateMar5.csv", "title": "TithelyBandsUpdateMar5.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1292, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GLBZTS2D/tithelybandsupdatemar5.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GLBZTS2D/download/tithelybandsupdatemar5.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GLBZTS2D/tithelybandsupdatemar5.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GLBZTS2D-36e1995924", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GLBZTS2D/tithelybandsupdatemar5.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Band Id,Effective Start Date,Effective End Date,Compensation Grade,Location (Country),Location (Region),Team,Department,Job Family,Job Subfamily,Description,Tier,Job Category,Job Level,Job Title,Frequency,Salary Currency,Salary Min Amount,Salary Target Amount,Salary Max Amount,Variable Pay Currency,Variable Pay Target (%),Variable Pay Target Amount,Equity Currency,Equity Target Amount,Equity Target (unit),Equity Target (%),Min Equity (unit),Max Equity (unit),Equity Participation Rate (%),OTE Min Amount,OTE Target Amount,OTE Max Amount,Pay Mix\r\nU,112,1/1/21,,N/A,US,N/A,N/A,Marketing,Marketing,N/A,N/A,0,IC,0,Content Manager,Annual,USD,89680,112100,134520,,,,,,,,,,,,,,\r\nU,113,1/1/21,,N/A,US,N/A,N/A,Product Development,Product Development,N/A,N/A,0,M,0,Director of Product,Annual,USD,158800,198500,238200,,,,,,,,,,,,,,\r\nU,114,1/1/21,,N/A,AU,N/A,N/A,Engineering,Engineering,N/A,N/A,0,IC,0,Senior Software Engineer II,Annual,AUD,111384.62,144800,167076.92,,,,,,,,,,,,,,\r\nU,115,1/1/21,,N/A,AU,N/A...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Band Id</div><div class=\"cm-col\">Effective Start Date</div><div class=\"cm-col\">Effective End Date</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Location (Country)</div><div class=\"cm-col\">Location (Region)</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">Job Subfamily</div><div class=\"cm-col\">Description</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Frequency</div><div class=\"cm-col\">Salary Currency</div><div class=\"cm-col\">Salary Min Amount</div><div class=\"cm-col\">Salary Target Amount</div><div class=\"cm-col\">Salary Max Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay Target (%)</div><div class=\"cm-col\">Variable Pay Target Amount</div><div class=\"cm-col\">Equity Currency</div><div class=\"cm-col\">Equity Target Amount</div><div class=\"cm-col\">Equity Target (unit)</div><div class=\"cm-col\">Equity Target (%)</div><div class=\"cm-col\">Min Equity (unit)</div><div class=\"cm-col\">Max Equity (unit)</div><div class=\"cm-col\">Equity Participation Rate (%)</div><div class=\"cm-col\">OTE Min Amount</div><div class=\"cm-col\">OTE Target Amount</div><div class=\"cm-col\">OTE Max Amount</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">112</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">US</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Marketing</div><div class=\"cm-col\">Marketing</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Content Manager</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">89680</div><div class=\"cm-col cm-num\">112100</div><div class=\"cm-col cm-num\">134520</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">113</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">US</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Product Development</div><div class=\"cm-col\">Product Development</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Director of Product</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">158800</div><div class=\"cm-col cm-num\">198500</div><div class=\"cm-col cm-num\">238200</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">114</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">AU</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Senior Software Engineer II</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">AUD</div><div class=\"cm-col cm-num\">111384.62</div><div class=\"cm-col cm-num\">144800</div><div class=\"cm-col cm-num\">167076.92</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">115</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">AU</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Software Engineer III</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">AUD</div><div class=\"cm-col cm-num\">86538.46</div><div class=\"cm-col cm-num\">112500</div><div class=\"cm-col cm-num\">129807.69</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">116</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">AU</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Sr. Staff Software Engineer (PT)</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">AUD</div><div class=\"cm-col cm-num\">72269.23</div><div class=\"cm-col cm-num\">93950</div><div class=\"cm-col cm-num\">112740</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 6, "lines_more": 1, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ihKAR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " Can you upload this file to tithely's paybands? We were missing the Job Family and it didn't work last time."}]}]}]}, {"ts": "1741185329.034079", "text": "Ooooh wait you're out so maybe not", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ukqOQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ooooh wait you're out so maybe not"}]}]}]}, {"ts": "1741184853.592609", "text": "<@U0690EB5JE5> Tithely is asking if there's any way to change the performance matrix table one more time to:\n\nWould there be any chance to change the compa ratio ranges again? It should be:\n&lt;0.80 / 0.80-0.90 / 0.91-1.10 / &gt;1.10", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741184853.592609", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "QC6cw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Tithely is asking if there's any way to change the performance matrix table one more time to:\n\nWould there be any chance to change the compa ratio ranges again? It should be:\n<0.80 / 0.80-0.90 / 0.91-1.10 / >1.10"}]}]}]}, {"ts": "1741140679.884499", "text": "<@U0690EB5JE5> my understanding was that the budgets are prorated in the comp builder when we calculate the budget. But that’s is not happening for curana. Can you please clarify if budget calculations in the comp builder incorporate, probation, or not?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741140679.884499", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "jpKtz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " my understanding was that the budgets are prorated in the comp builder when we calculate the budget. But that’s is not happening for curana. Can you please clarify if budget calculations in the comp builder incorporate, probation, or not?"}]}]}]}, {"ts": "1741140522.990729", "text": "<@U0690EB5JE5> I just forwarded you a couple of emails from Curana. Can you please address those?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741140522.990729", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "j1nub", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I just forwarded you a couple of emails from <PERSON><PERSON><PERSON>. Can you please address those?"}]}]}]}, {"ts": "1741140514.891209", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> Gentle Reminder! one of our engineers will be available to help with uploads. If there are any bugs, Bug fixes might take 24hrs.\n<@U07MH77PUBV> will be on call Wednesday and <@U06HN8XDC5A> will be on call on Thursday. Friday I am OOO but can be available my late evening.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741016402.109749", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1741141222.000000"}, "blocks": [{"type": "rich_text", "block_id": "Q/zXE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Gentle Reminder! one of our engineers will be available to help with uploads. If there are any bugs, Bug fixes might take 24hrs.\n"}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " will be on call Wednesday and "}, {"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " will be on call on Thursday. Friday I am OOO but can be available my late evening."}]}]}]}, {"ts": "1741139997.441429", "text": "<@U07EJ2LP44S> Done. employee id `365` doesn't exist in the ENV and the rating is cannot be updated. Rest all updates are done,", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741138728.242149", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1741142239.000000"}, "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "A4Hzq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Done. employee id "}, {"type": "text", "text": "365", "style": {"code": true}}, {"type": "text", "text": " doesn't exist in the ENV and the rating is cannot be updated. Rest all updates are done,"}]}]}]}, {"ts": "1741138728.242149", "text": "<@U0690EB5JE5> <PERSON> is going to post a couple of sheets to the main title Slack channel. One is some additional last race dates, and when is the performance readings. Can you please upload these so that they’re ready for their cycle tomorrow morning?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741138728.242149", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "9glaO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON> is going to post a couple of sheets to the main title Slack channel. One is some additional last race dates, and when is the performance readings. Can you please upload these so that they’re ready for their cycle tomorrow morning?"}]}]}]}, {"ts": "1741117294.079889", "text": "<@U0690EB5JE5> <@U07M6QKHUC9> FYI On curana, every time I edit the cycle it continues to change the budget %. I am having to change it to 3.7 every time I publish (which I've had to do a couple times to include employees). So if you make edits make sure you reset the budget.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741117294.079889", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "j/XKC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " FYI On curana, every time I edit the cycle it continues to change the budget %. I am having to change it to 3.7 every time I publish (which I've had to do a couple times to include employees). So if you make edits make sure you reset the budget."}]}]}]}, {"ts": "1741116976.219409", "text": "<@U0690EB5JE5> I have a specific question on the budget allocation page. Tithelys budget is based on ALL salaries, not just eligible salaries. So they will likely need to overwrite budgets on that page. If they do, do they have to edit EVERY field or will any of it 'roll down'? So for example, if a department has two recommenders under it, do they need to update the budget on all three of those lines? Or if they update the topline number will it roll down the new amounts to the 2 managers? I think the answer is they have to update every single amount.\n\nA secondary question, what about 'left for team' amounts. Do they need to overwrite that field as well when they change the department budgets?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741116976.219409", "reply_count": 1, "reactions": [{"name": "eyes", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "qM5ZM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I have a specific question on the budget allocation page. Tithelys budget is based on ALL salaries, not just eligible salaries. So they will likely need to overwrite budgets on that page. If they do, do they have to edit EVERY field or will any of it 'roll down'? So for example, if a department has two recommenders under it, do they need to update the budget on all three of those lines? Or if they update the topline number will it roll down the new amounts to the 2 managers? I think the answer is they have to update every single amount.\n\nA secondary question, what about 'left for team' amounts. Do they need to overwrite that field as well when they change the department budgets?"}]}]}]}, {"ts": "1741116837.232479", "text": "<@U0690EB5JE5> <@U07M6QKHUC9> The currency toggle in Tithely wasn't changing the currency, it was only changing the name of the currency. Once I hit save again on the localization numbers it started working (no changes were made to the numbers, I just hit save on the currency conversion rates page). Just FYI in case something happens mid-cycle.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741116837.232479", "reply_count": 9, "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "IP57/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " The currency toggle in Tithely wasn't changing the currency, it was only changing the name of the currency. Once I hit save again on the localization numbers it started working (no changes were made to the numbers, I just hit save on the currency conversion rates page). Just FYI in case something happens mid-cycle."}]}]}]}, {"ts": "1741113596.156199", "text": "<@U0690EB5JE5> We have a bug with comments in Curana Main: when a manager is trying to review a team and add a comment. It will not accept the comment. I impersonated the manager <PERSON> and was able to reproduce. It WILL allow her to add a comment if she uses the search bar to find a user, but not if she's in the regular list view and tries to add a comment.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741113596.156199", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "hNjHQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We have a bug with comments in Curana Main: when a manager is trying to review a team and add a comment. It will not accept the comment. I impersonated the manager <PERSON> and was able to reproduce. It WILL allow her to add a comment if she uses the search bar to find a user, but not if she's in the regular list view and tries to add a comment."}]}]}]}], "created_at": "2025-05-22T21:35:34.715575"}