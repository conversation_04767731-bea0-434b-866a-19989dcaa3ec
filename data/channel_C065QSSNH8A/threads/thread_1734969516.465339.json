{"thread_ts": "1734969516.465339", "channel_id": "C065QSSNH8A", "reply_count": 26, "replies": [{"ts": "1734969559.747709", "text": "Please create a new cycle. Integration sync is not updating the merit view correctly. I am looking into the issue.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "FyW9H", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please create a new cycle. Integration sync is not updating the merit view correctly. I am looking into the issue."}]}]}]}, {"ts": "1734969602.950349", "text": "OK, I can re-create it, they had set it up according to their specifications", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "ax<PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OK, I can re-create it, they had set it up according to their specifications"}]}]}]}, {"ts": "1734969610.094829", "text": "Do we need to then delete the old cycle?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "RDH4O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we need to then delete the old cycle?"}]}]}]}, {"ts": "1734969684.549479", "text": "yes you will have to close the cycle before to create a new one.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "ZmnMx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes you will have to close the cycle before to create a new one."}]}]}]}, {"ts": "1734971054.389219", "text": "I don't want to impact the new data by closing the cycle; can we delete it from the backend?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "VrBQF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I don't want to impact the new data by closing the cycle; can we delete it from the backend?"}]}]}]}, {"ts": "1734971088.625729", "text": "let me check", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "NIoRq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "let me check"}]}]}]}, {"ts": "1734971106.444979", "text": "i need to get the settings first", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "jnj01", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i need to get the settings first"}]}]}]}, {"ts": "1734971111.463049", "text": "doing that now", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "n5iNP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "doing that now"}]}]}]}, {"ts": "1734971241.609429", "text": "should I wait ? I am ready to delete.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "fNUbw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "should I wait ? I am ready to delete."}]}]}]}, {"ts": "1734971279.131199", "text": "<@U07EJ2LP44S>?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "7iluN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1734971302.269829", "text": "yes wait, i'm grabbing all the settings", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "H2teC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes wait, i'm grabbing all the settings"}]}]}]}, {"ts": "1734971310.919619", "text": "ok", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1734971317.000039", "text": "like this:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "files": [{"id": "F086A0D24TU", "created": 1734971313, "timestamp": 1734971313, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 155698, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F086A0D24TU/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F086A0D24TU/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F086A0D24TU-eb12a0bbb2/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F086A0D24TU-eb12a0bbb2/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F086A0D24TU-eb12a0bbb2/image_360.png", "thumb_360_w": 360, "thumb_360_h": 279, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F086A0D24TU-eb12a0bbb2/image_480.png", "thumb_480_w": 480, "thumb_480_h": 373, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F086A0D24TU-eb12a0bbb2/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F086A0D24TU-eb12a0bbb2/image_720.png", "thumb_720_w": 720, "thumb_720_h": 559, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F086A0D24TU-eb12a0bbb2/image_800.png", "thumb_800_w": 800, "thumb_800_h": 621, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F086A0D24TU-eb12a0bbb2/image_960.png", "thumb_960_w": 960, "thumb_960_h": 745, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F086A0D24TU-eb12a0bbb2/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 795, "original_w": 1322, "original_h": 1026, "thumb_tiny": "AwAlADDQwM//AF6NvtS80vNACbR6UcjgUvNNzg9RQA7mikz6mgNnpQA6iiigApnOf/10+oz16fpQAvIP/wCulA78036fypwA645oAdRRRQAU3bz1/SnUUAN2f5xS0tFAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F086A0D24TU/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F086A0D24TU-8fd3434b21", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "n1wk4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "like this:"}]}]}]}, {"ts": "1734971333.848099", "text": "is it possible to duplicate the cycle by chance?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "uZIZq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "is it possible to duplicate the cycle by chance?"}]}]}]}, {"ts": "1734971393.148409", "text": "Unfortunately, there is no easy way to do this. But its a good feature to implement.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734969516.465339", "edited": {"user": "U0690EB5JE5", "ts": "1734971404.000000"}, "blocks": [{"type": "rich_text", "block_id": "6FeAh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Unfortunately, there is no easy way to do this. But its a good feature to implement."}]}]}]}, {"ts": "1734971601.249439", "text": "Yes, people would probably want this for their future cycles", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "oKWOG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, people would probably want this for their future cycles"}]}]}]}, {"ts": "1734971607.561139", "text": "I just captured all the info, you can delete", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "S7esJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I just captured all the info, you can delete"}]}]}]}, {"ts": "1734971951.631129", "text": "Deleted,", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "I6Smx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Deleted,"}]}]}]}, {"ts": "1734972372.830089", "text": "Did the data upload go fine? I'll add their CTC and last raise date info before I create the cycle if so", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "9JLm/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Did the data upload go fine? I'll add their CTC and last raise date info before I create the cycle if so"}]}]}]}, {"ts": "1734972468.552879", "text": "I have synced the data. Only client can tell if there are any discrepancies with employees. It looked good per my spot checks. :crossed_fingers:", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734969516.465339", "edited": {"user": "U0690EB5JE5", "ts": "1734972483.000000"}, "blocks": [{"type": "rich_text", "block_id": "pbYFF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have synced the data. Only client can tell if there are any discrepancies with employees. It looked good per my spot checks. "}, {"type": "emoji", "name": "crossed_fingers", "unicode": "1f91e"}]}]}]}, {"ts": "1734972540.290779", "text": "Ok great. So long as nothing failed or was weird that's fine", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "jpnQy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok great. So long as nothing failed or was weird that's fine"}]}]}]}, {"ts": "1734972552.867799", "text": "yes", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "IB8ze", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes"}]}]}]}, {"ts": "1734972694.607789", "text": "I'm sorry I just realized something, this is on me - but can we upload comments again for valgenesis? They wanted to put the reason for last raise as a comment to employees", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "OaqAU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'm sorry I just realized something, this is on me - but can we upload comments again for valgenesis? They wanted to put the reason for last raise as a comment to employees"}]}]}]}, {"ts": "1734972724.909179", "text": "Not today, just in general. I think they'd want to see it next week though", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "wuoND", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Not today, just in general. I think they'd want to see it next week though"}]}]}]}, {"ts": "1734972752.039899", "text": "Please create a ticket with comments in the same format as dgoc", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734969516.465339", "blocks": [{"type": "rich_text", "block_id": "wIAKn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please create a ticket with comments in the same format as dgoc"}]}]}]}, {"ts": "1734972758.365839", "text": "Ok I can do that", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "XfpYT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok I can do that"}]}]}]}], "created_at": "2025-05-22T22:00:49.338650"}