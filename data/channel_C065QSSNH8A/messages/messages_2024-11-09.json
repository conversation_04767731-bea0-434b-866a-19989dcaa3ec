{"date": "2024-11-09", "channel_id": "C065QSSNH8A", "message_count": 12, "messages": [{"ts": "1731113028.471669", "text": "<@U0690EB5JE5> I am unable to move past the equity screen in demo env after I added promotions as a comp component.\n<https://compiify.atlassian.net/browse/COM-3974>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1731113028.471669", "reply_count": 3, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14057::75c4fc3d174c4ba286dd212cd15e0861", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3974?atlOrigin=eyJpIjoiNDI3NzcwYmMzMmU5NDI1OWEyZWQxODM5MWEyMjY5YjYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3974 Issue: Unable to progress past the equity screen in Cycle Builder>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14057::23a0a28791a9473eb82938243032526e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14057\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3974\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3974", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "/Pedo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am unable to move past the equity screen in demo env after I added promotions as a comp component.\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3974"}]}]}]}, {"ts": "1731112350.507289", "text": "<@U0690EB5JE5> here is the vendor form. The items that I have listed in developing are the ones that we need to discuss specifically. But we also need to ensure that items listed as Strong are demoable with light engineering effort.  Cc <@U07EJ2LP44S>", "user": "U07M6QKHUC9", "type": "message", "files": [{"id": "F0807Q497U4", "created": 1731112210, "timestamp": 1731112210, "name": "Vendor Submission - Cycle Management.xlsx", "title": "Vendor Submission - Cycle Management.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 189280, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F0807Q497U4/vendor_submission_-_cycle_management.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F0807Q497U4/download/vendor_submission_-_cycle_management.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F0807Q497U4-304c70895d/vendor_submission_-_cycle_management_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F0807Q497U4-304c70895d/vendor_submission_-_cycle_management_thumb_pdf.png", "thumb_pdf_w": 935, "thumb_pdf_h": 1210, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0807Q497U4/vendor_submission_-_cycle_management.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F0807Q497U4-248d011ebc", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "4ryDx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " here is the vendor form. The items that I have listed in developing are the ones that we need to discuss specifically. But we also need to ensure that items listed as Strong are demoable with light engineering effort.  Cc "}, {"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1731106442.883359", "text": "We can discuss this over the weekend or on Monday morning. But that report is due on Monday afternoon.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1731106442.883359", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "5ze5u", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can discuss this over the weekend or on Monday morning. But that report is due on Monday afternoon."}]}]}]}, {"ts": "1731106419.338449", "text": "<@U0690EB5JE5> I am gonna need some help from you to gauge the technical complexity of some of the features we are including in the vendor report. I just wanna make sure we are representing ourselves correctly.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "x4klw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am gonna need some help from you to gauge the technical complexity of some of the features we are including in the vendor report. I just wanna make sure we are representing ourselves correctly."}]}]}]}, {"ts": "1731106067.989699", "text": "<https://compiify.atlassian.net/browse/COM-3973>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1731106067.989699", "reply_count": 31, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14056::d0616d62cea942988128e01ec049d05f", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3973?atlOrigin=eyJpIjoiOWRlODA0ZjBhYjk2NDBmOTgwZGNmNzdmOTA0ZWI0YWYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3973 Issue: Promotion component disappearing when selecting one combined…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14056::e3232328ee5449d1b458dd4919210bbf", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14056\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3973\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3973", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "6xgDF", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3973"}]}]}]}, {"ts": "1731106055.685599", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> <@U04DKEFP1K8> This is expected behaviour as far as I know since beginning. Since budget is combined for Salary and Promotion, there is no budgetting and separate columns for promotion in cycle and merit view. I think only thing we need to address is, remove the component in the UX while answer to the question is Yes to make it clear. I think the current bheviour is how it is supposed to work.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1731106091.000000"}, "blocks": [{"type": "rich_text", "block_id": "5Q3L/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " This is expected behaviour as far as I know since beginning. Since budget is combined for Salary and Promotion, there is no budgetting and separate columns for promotion in cycle and merit view. I think only thing we need to address is, remove the component in the UX while answer to the question is Yes to make it clear. I think the current bheviour is how it is supposed to work."}]}]}]}, {"ts": "1731105399.122859", "text": "Possibly Infra issue if even logos images take time to load. I will check with infra. \nAlso FYI… HRBP has some non blocking known slowness issue for which we have PR to improve a little and QA in progress for that.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1731090508.188969", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "kAXhY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Possibly Infra issue if even logos images take time to load. I will check with infra. \nAlso FYI"}, {"type": "text", "text": "…"}, {"type": "text", "text": " HRBP has some non blocking known slowness issue for which we have PR to improve a little and QA in progress for that."}]}]}]}, {"ts": "**********.481599", "text": "logining off.", "user": "U06HN8XDC5A", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "U2si4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "logining off."}]}]}]}, {"ts": "**********.398159", "text": "can we change the flag % from account to account? for example if v<PERSON><PERSON><PERSON> wants a flag a 5% variation and <PERSON><PERSON><PERSON><PERSON><PERSON> wants it at 2%, is that possible? (<PERSON><PERSON><PERSON><PERSON> has asked for this ability)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.398159", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "PAngu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "can we change the flag % from account to account? for example if v<PERSON><PERSON><PERSON> wants a flag a 5% variation and <PERSON><PERSON><PERSON><PERSON><PERSON> wants it at 2%, is that possible? (<PERSON><PERSON><PERSON><PERSON> has asked for this ability)"}]}]}]}, {"ts": "**********.863359", "text": "jinx", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "cw69V", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "jinx"}]}]}]}, {"ts": "**********.122759", "text": "Lol sorry jix", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pbu7n", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Lol sorry jix"}]}]}]}, {"ts": "**********.859619", "text": "\"Tool is still loading but slower than normal to the point myself and one of our HRBPs are getting a blank screen which will then load the navigation pane and logos before finally fully loading. just want to let you know that some users are experiencing this\"", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0JjTG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "\"Tool is still loading but slower than normal to the point myself and one of our HRBPs are getting a blank screen which will then load the navigation pane and logos before finally fully loading. just want to let you know that some users are experiencing this\""}]}]}]}], "created_at": "2025-05-22T21:35:34.669749"}