#!/usr/bin/env python3
"""
Comprehensive analysis of the actual data to understand what we can test.
"""

import os
import sys
import django
import json
from collections import defaultdict, Counter
import re

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.documents.models import DocumentChunk, Document
from apps.accounts.models import UserPlatformProfile

def analyze_ingested_data():
    """Analyze what data has been ingested into the system."""
    print("🔍 Analyzing Ingested Data")
    print("=" * 80)
    
    # Check documents
    documents = Document.objects.all()
    print(f"📊 Total documents: {documents.count()}")
    
    # Group by source type
    source_types = {}
    for doc in documents:
        source_type = doc.source.source_type if doc.source else 'unknown'
        source_types[source_type] = source_types.get(source_type, 0) + 1
    
    print(f"📊 Documents by source type:")
    for source_type, count in source_types.items():
        print(f"   {source_type}: {count}")
    
    # Check chunks
    chunks = DocumentChunk.objects.all()
    print(f"📊 Total chunks: {chunks.count()}")
    
    # Check profiles
    profiles = UserPlatformProfile.objects.all()
    print(f"📊 Total profiles: {profiles.count()}")
    
    for profile in profiles:
        chunk_count = DocumentChunk.objects.filter(profile=profile).count()
        print(f"   {profile.display_name}: {chunk_count} chunks")

def analyze_raw_data_content():
    """Analyze the raw data files to understand content themes."""
    print(f"\n🔍 Analyzing Raw Data Content")
    print("=" * 80)
    
    # Load user mapping
    with open('/Users/<USER>/Desktop/RAGSearch/data/channel_C065QSSNH8A/users/users.json', 'r') as f:
        user_data = json.load(f)
        users = user_data['users']
    
    # Analyze recent messages for themes
    themes = defaultdict(int)
    user_activity = defaultdict(int)
    issue_keywords = defaultdict(int)
    
    # Sample a few recent files
    sample_files = [
        '/Users/<USER>/Desktop/RAGSearch/data/channel_C065QSSNH8A/messages/messages_2024-12-20.json',
        '/Users/<USER>/Desktop/RAGSearch/data/channel_C065QSSNH8A/messages/messages_2024-11-20.json',
        '/Users/<USER>/Desktop/RAGSearch/data/channel_C065QSSNH8A/messages/messages_2024-10-20.json',
        '/Users/<USER>/Desktop/RAGSearch/data/channel_C065QSSNH8A/messages/messages_2024-09-20.json',
        '/Users/<USER>/Desktop/RAGSearch/data/channel_C065QSSNH8A/messages/messages_2024-08-20.json',
    ]
    
    all_messages = []
    
    for file_path in sample_files:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                data = json.load(f)
                messages = data.get('messages', [])
                all_messages.extend(messages)
                
                for msg in messages:
                    user_id = msg.get('user', '')
                    text = msg.get('text', '').lower()
                    
                    if user_id in users:
                        user_name = users[user_id]['name']
                        user_activity[user_name] += 1
                    
                    # Look for common themes
                    if 'bug' in text or 'issue' in text or 'problem' in text:
                        themes['bugs_issues'] += 1
                    if 'test' in text or 'testing' in text:
                        themes['testing'] += 1
                    if 'curana' in text:
                        themes['curana'] += 1
                    if 'paycom' in text:
                        themes['paycom'] += 1
                    if 'integration' in text:
                        themes['integration'] += 1
                    if 'deploy' in text or 'deployment' in text:
                        themes['deployment'] += 1
                    if 'fix' in text or 'fixed' in text:
                        themes['fixes'] += 1
                    if 'error' in text:
                        themes['errors'] += 1
                    
                    # Extract specific issue keywords
                    issue_patterns = [
                        r'com-\d+', r'bug', r'error', r'fail', r'broken', r'issue',
                        r'problem', r'crash', r'timeout', r'slow', r'performance'
                    ]
                    
                    for pattern in issue_patterns:
                        matches = re.findall(pattern, text)
                        issue_keywords[pattern] += len(matches)
    
    print(f"📊 Analyzed {len(all_messages)} messages from sample files")
    
    print(f"\n📊 User Activity (top 10):")
    for user, count in sorted(user_activity.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"   {user}: {count} messages")
    
    print(f"\n📊 Content Themes:")
    for theme, count in sorted(themes.items(), key=lambda x: x[1], reverse=True):
        print(f"   {theme}: {count} mentions")
    
    print(f"\n📊 Issue Keywords:")
    for keyword, count in sorted(issue_keywords.items(), key=lambda x: x[1], reverse=True)[:10]:
        if count > 0:
            print(f"   {keyword}: {count} mentions")

def find_specific_content_examples():
    """Find specific examples of content for testing."""
    print(f"\n🔍 Finding Specific Content Examples")
    print("=" * 80)
    
    # Look for Amanda's messages specifically
    amanda_files = []
    curana_files = []
    issue_files = []
    
    # Check a broader range of files
    data_dir = '/Users/<USER>/Desktop/RAGSearch/data/channel_C065QSSNH8A/messages'
    
    for filename in sorted(os.listdir(data_dir))[-20:]:  # Last 20 files
        if filename.endswith('.json'):
            file_path = os.path.join(data_dir, filename)
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    messages = data.get('messages', [])
                    
                    for msg in messages:
                        text = msg.get('text', '').lower()
                        user = msg.get('user', '')
                        
                        # Check for Amanda (U07EJ2LP44S)
                        if user == 'U07EJ2LP44S':
                            amanda_files.append((filename, msg.get('text', '')[:100]))
                        
                        # Check for Curana mentions
                        if 'curana' in text:
                            curana_files.append((filename, msg.get('text', '')[:100]))
                        
                        # Check for issue/bug mentions
                        if any(word in text for word in ['bug', 'issue', 'problem', 'error', 'fail']):
                            issue_files.append((filename, msg.get('text', '')[:100]))
            except Exception as e:
                print(f"Error reading {filename}: {e}")
    
    print(f"📊 Amanda's messages found: {len(amanda_files)}")
    for filename, text in amanda_files[:5]:
        print(f"   {filename}: {text}...")
    
    print(f"\n📊 Curana mentions found: {len(curana_files)}")
    for filename, text in curana_files[:5]:
        print(f"   {filename}: {text}...")
    
    print(f"\n📊 Issue mentions found: {len(issue_files)}")
    for filename, text in issue_files[:5]:
        print(f"   {filename}: {text}...")

def suggest_test_queries():
    """Suggest realistic test queries based on the data."""
    print(f"\n💡 Suggested Test Queries Based on Data")
    print("=" * 80)
    
    # Simple queries
    simple_queries = [
        "What did Amanda say?",
        "Show me messages from Rachel",
        "What is Curana?",
        "Tell me about testing",
        "What bugs were reported?",
    ]
    
    # Medium complexity queries
    medium_queries = [
        "List issues reported by Amanda",
        "What problems did Rachel mention about testing?",
        "Summarize Curana integration issues",
        "What deployment problems were discussed?",
        "Show me all COM ticket references",
    ]
    
    # Complex queries
    complex_queries = [
        "Summarize all issues reported about Curana integration by Amanda and Rachel in the last 6 months",
        "What are the main testing challenges mentioned by the team and how were they resolved?",
        "Compare the issues reported by different team members about deployment and integration",
        "What patterns can you identify in the bug reports and how do they relate to specific features?",
        "Analyze the evolution of Curana-related discussions over time and identify key decision points",
    ]
    
    print("🔹 Simple Queries:")
    for i, query in enumerate(simple_queries, 1):
        print(f"   {i}. {query}")
    
    print("\n🔹 Medium Complexity Queries:")
    for i, query in enumerate(medium_queries, 1):
        print(f"   {i}. {query}")
    
    print("\n🔹 Complex Queries:")
    for i, query in enumerate(complex_queries, 1):
        print(f"   {i}. {query}")

if __name__ == "__main__":
    analyze_ingested_data()
    analyze_raw_data_content()
    find_specific_content_examples()
    suggest_test_queries()
