{"date": "2025-01-21", "channel_id": "C065QSSNH8A", "message_count": 10, "messages": [{"ts": "1737483864.336749", "text": "<@U0690EB5JE5> <PERSON><PERSON><PERSON> is no longer able to login to stride using SSO. This was working last week. Can you check?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737483864.336749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "jUauF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> is no longer able to login to stride using SSO. This was working last week. Can you check?"}]}]}]}, {"ts": "1737480351.728199", "text": "<@U0690EB5JE5> on a call with cindy, it doesn't look like the proration modifier is being applied to the bonus", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737480351.728199", "reply_count": 13, "blocks": [{"type": "rich_text", "block_id": "TizsQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " on a call with cindy, it doesn't look like the proration modifier is being applied to the bonus"}]}]}]}, {"ts": "1737479788.380309", "text": "<@U07M6QKHUC9> Demo is being updated. Please check after an hour.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "mBL2Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Demo is being updated. Please check after an hour."}]}]}]}, {"ts": "1737479052.794629", "text": "<@U07EJ2LP44S> Tithley", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737356697.486389", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "LDSQN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Tithley"}]}]}]}, {"ts": "1737477144.316929", "text": "Yes let's at least meet even for short time", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hovTG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes let's at least meet even for short time"}]}]}]}, {"ts": "1737475858.696289", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> is meeting still required?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737466306.695409", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "rS7NP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " is meeting still required?"}]}]}]}, {"ts": "1737474213.649449", "text": "<@U0690EB5JE5> Can we also duplicate the stridedemo instance three more times? We need a training environment for Val<PERSON>, Curana, Tithely, and Diversified. They all have different settings so I can't reuse the same environment, and they're all on a similar timeframe. Curana is the latest so I may be able to reuse Val<PERSON> for that one if they are done with it in time, but it'll be close.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737474213.649449", "reply_count": 18, "blocks": [{"type": "rich_text", "block_id": "jt++8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can we also duplicate the stridedemo instance three more times? We need a training environment for Val<PERSON>, Curana, Tithely, and Diversified. They all have different settings so I can't reuse the same environment, and they're all on a similar timeframe. <PERSON><PERSON><PERSON> is the latest so I may be able to reuse Valgenesis for that one if they are done with it in time, but it'll be close."}]}]}]}, {"ts": "1737473926.335459", "text": "<@U0690EB5JE5> Are we able to make the export of the merit table only the columns that are in the view? Right now it's exporting everything. For Diversified, that means they have a bajillion columns but they're only using a handful.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737473926.335459", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "OwFKh", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Are we able to make the export of the merit table only the columns that are in the view? Right now it's exporting everything. For Diversified, that means they have a bajillion columns but they're only using a handful."}]}]}]}, {"ts": "1737466306.695409", "text": "<@U07EJ2LP44S> I don’t have any specific agenda for the meeting. I am fine skipping if there is nothing to discuss.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737466306.695409", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "oN1D9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " have any specific agenda for the meeting"}, {"type": "text", "text": "."}, {"type": "text", "text": " I am fine skipping if there is nothing to discuss."}]}]}]}, {"ts": "1737404102.746119", "text": "I need help with this file. It's an update for Valgenesis. They did an export from the system and then made changes - Adds, Deletes, Updates. A variety of things changed, including some manager changes. I checked the file for all the usual reasons it might fail (date formatting etc) but there's a LOT in here and I'm sure I'm missing something. I am just getting an internal error message, file does not match required format. Can you help upload, <@U0690EB5JE5>? VGUpdatejan20 is the file I pulled out into CSV. The other one is the file they sent me.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737404102.746119", "reply_count": 5, "files": [{"id": "F089G5PCL91", "created": 1737403853, "timestamp": 1737403853, "name": "VGUpdatejan20.csv", "title": "VGUpdatejan20.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 211297, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089G5PCL91/vgupdatejan20.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089G5PCL91/download/vgupdatejan20.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089G5PCL91/vgupdatejan20.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089G5PCL91-3338871942", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F089G5PCL91/vgupdatejan20.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary(base)</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">1868</div><div class=\"cm-col\">Sivakumar</div><div class=\"cm-col\">Manisekar</div><div class=\"cm-col\">Sivakumar Manisekar</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">7/1/22</div><div class=\"cm-col\">IN</div><div class=\"cm-col cm-num\">1067</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">7/1/22</div><div class=\"cm-col\">6/30/22</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Chennai Office (India)</div><div class=\"cm-col\">Team Lead, Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Bhuvanes devi Rajan</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\">3/1/24</div><div class=\"cm-col\">2/28/25</div><div class=\"cm-col\">Did Not Meet Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">INR</div><div class=\"cm-col cm-num\">1887310</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">INR</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">INR</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/29/23</div><div class=\"cm-col cm-num\">1810860</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">2367</div><div class=\"cm-col\">Keerthiga</div><div class=\"cm-col\">Radhakrishnan</div><div class=\"cm-col\">Keerthiga Radhakrishnan</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">1/16/25</div><div class=\"cm-col\">IN</div><div class=\"cm-col cm-num\">1179</div><div class=\"cm-col cm-num\">2331</div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/16/25</div><div class=\"cm-col\">4/16/25</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Chennai Office (India)</div><div class=\"cm-col\">Product Owner</div><div class=\"cm-col\">Product Management</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">FT</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Product Management</div><div class=\"cm-col\">Bhuvanes devi Rajan</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/3/24</div><div class=\"cm-col\">2/28/25</div><div class=\"cm-col\">Not Rated</div><div class=\"cm-col\"></div><div class=\"cm-col\">INR</div><div class=\"cm-col cm-num\">2000000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">INR</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">INR</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">01/16/2025</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">2368</div><div class=\"cm-col\">Akhil</div><div class=\"cm-col\">Jayan</div><div class=\"cm-col\">Akhil Jayan</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">1/16/25</div><div class=\"cm-col\">IN</div><div class=\"cm-col cm-num\">1630</div><div class=\"cm-col cm-num\">2331</div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/16/25</div><div class=\"cm-col\">4/16/25</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Chennai Office (India)</div><div class=\"cm-col\">Admin Manager</div><div class=\"cm-col\">Administration</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">FT</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\">Bhuvanes devi Rajan</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/3/24</div><div class=\"cm-col\">2/28/25</div><div class=\"cm-col\">Not Rated</div><div class=\"cm-col\"></div><div class=\"cm-col\">INR</div><div class=\"cm-col cm-num\">1500000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">INR</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">INR</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">01/16/2025</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">2366</div><div class=\"cm-col\">Terrance</div><div class=\"cm-col\">Blackwell</div><div class=\"cm-col\">Terrance Blackwell</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">1/13/25</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">1009</div><div class=\"cm-col cm-num\">2155</div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/13/25</div><div class=\"cm-col\">4/13/25</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Texas, USA</div><div class=\"cm-col\">Chief Delivery Officer</div><div class=\"cm-col\">Professional Services</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">FT</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\">Kellie Hart</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/3/24</div><div class=\"cm-col\">2/28/25</div><div class=\"cm-col\">Not Rated</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">300000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">20</div><div class=\"cm-col cm-num\">60000</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">01/13/2025</div><div class=\"cm-col cm-num\">300000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">2364</div><div class=\"cm-col\">Joana</div><div class=\"cm-col\">Pra&ccedil;a</div><div class=\"cm-col\">Joana Pra&ccedil;a</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">1/6/25</div><div class=\"cm-col\">PT</div><div class=\"cm-col cm-num\">2117</div><div class=\"cm-col cm-num\">2255</div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/6/25</div><div class=\"cm-col\">4/6/25</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Lisbon Office (Portugal)</div><div class=\"cm-col\">Application Engineer - Tier 1</div><div class=\"cm-col\">Customer Technical Services</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">FT</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Tier 1</div><div class=\"cm-col\">Francisca Ribeiro</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/3/24</div><div class=\"cm-col\">2/28/25</div><div class=\"cm-col\">Not Rated</div><div class=\"cm-col\"></div><div class=\"cm-col\">EUR</div><div class=\"cm-col cm-num\">28000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">EUR</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">EUR</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">01/06/2025</div><div class=\"cm-col cm-num\">28000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 557, "lines_more": 556, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F089UURNY1X", "created": 1737403894, "timestamp": 1737403894, "name": "VG EmployeeDataTemplateUPDATE (01.17.2025).xlsx", "title": "VG EmployeeDataTemplateUPDATE (01.17.2025).xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 447488, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089UURNY1X/vg_employeedatatemplateupdate__01.17.2025_.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089UURNY1X/download/vg_employeedatatemplateupdate__01.17.2025_.xlsx", "media_display_type": "unknown", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089UURNY1X/vg_employeedatatemplateupdate__01.17.2025_.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089UURNY1X-4dd7652063", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "J1ptW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I need help with this file. It's an update for Valgenesis. They did an export from the system and then made changes - Adds, Deletes, Updates. A variety of things changed, including some manager changes. I checked the file for all the usual reasons it might fail (date formatting etc) but there's a LOT in here and I'm sure I'm missing something. I am just getting an internal error message, file does not match required format. Can you help upload, "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "? VGUpdatejan20 is the file I pulled out into CSV. The other one is the file they sent me."}]}]}]}], "created_at": "2025-05-22T21:35:34.706660"}