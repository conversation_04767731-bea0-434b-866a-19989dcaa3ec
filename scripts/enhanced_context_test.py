#!/usr/bin/env python3
"""
Enhanced Context Test - Test improved retrieval with more comprehensive results.
"""

import os
import sys
import django
import time

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.unified_rag_service import UnifiedRAGService
from apps.accounts.models import Tenant, User

def test_enhanced_context():
    """Test the enhanced context retrieval."""
    print("🚀 Testing Enhanced Context Retrieval")
    print("=" * 70)

    # Initialize service
    tenant = Tenant.objects.get(slug='stride')
    user = User.objects.first()
    service = UnifiedRAGService(tenant.slug, user)

    # Test query that should have lots of context
    query = "whats latest on curana?"

    print(f"🔍 Query: '{query}'")
    print("-" * 50)

    start_time = time.time()

    try:
        # Test with enhanced settings
        search_result, retrieved_docs = service.search(
            query_text=query,
            top_k=15,  # Request more results
            min_relevance_score=0.05,  # Very low threshold for comprehensive results
            use_hybrid_search=True
        )

        end_time = time.time()
        duration = end_time - start_time

        # Analyze results
        citations = search_result.citations.all()
        citation_count = citations.count()

        print(f"⏱️  Duration: {duration:.2f}s")
        print(f"📊 Retrieved Documents: {len(retrieved_docs)}")
        print(f"🔗 Citations: {citation_count}")
        print(f"⭐ Average Score: {search_result.retriever_score_avg:.3f}")
        print(f"📝 Answer Length: {len(search_result.generated_answer)} characters")

        print(f"\n📋 DETAILED CITATION ANALYSIS:")
        print("=" * 50)

        for i, citation in enumerate(citations, 1):
            chunk = citation.document_chunk
            doc = chunk.document

            print(f"\n🔗 Citation {i}:")
            print(f"   📊 Relevance Score: {citation.relevance_score:.4f}")
            print(f"   📄 Document: {doc.title}")
            print(f"   📅 Created: {doc.created_at.strftime('%Y-%m-%d %H:%M')}")
            print(f"   🔗 Link: {doc.permalink}")

            if chunk.profile:
                print(f"   👤 Profile: {chunk.profile.display_name}")

            # Show content preview
            content = getattr(chunk, 'content', '') or getattr(chunk, 'text', '') or 'No content available'
            content_preview = content[:200].replace('\n', ' ')
            print(f"   📝 Content: {content_preview}...")

            # Show metadata
            if chunk.metadata:
                print(f"   🏷️  Metadata: {chunk.metadata}")

        print(f"\n📄 ANSWER PREVIEW:")
        print("=" * 50)
        answer_preview = search_result.generated_answer[:500]
        print(f"{answer_preview}...")

        # Analyze score distribution
        scores = [citation.relevance_score for citation in citations]
        if scores:
            print(f"\n📊 SCORE DISTRIBUTION:")
            print("=" * 50)
            print(f"   Highest Score: {max(scores):.4f}")
            print(f"   Lowest Score: {min(scores):.4f}")
            print(f"   Average Score: {sum(scores)/len(scores):.4f}")
            print(f"   Score Range: {max(scores) - min(scores):.4f}")

        # Test with different thresholds
        print(f"\n🔬 THRESHOLD ANALYSIS:")
        print("=" * 50)

        thresholds = [0.05, 0.10, 0.15, 0.20, 0.25]
        for threshold in thresholds:
            qualifying_citations = [c for c in citations if c.relevance_score >= threshold]
            print(f"   Threshold {threshold:.2f}: {len(qualifying_citations)} citations qualify")

        # Performance assessment
        print(f"\n🎯 PERFORMANCE ASSESSMENT:")
        print("=" * 50)

        if citation_count >= 8:
            print("✅ EXCELLENT: Rich context with 8+ citations")
        elif citation_count >= 5:
            print("✅ GOOD: Adequate context with 5+ citations")
        elif citation_count >= 3:
            print("⚠️  FAIR: Limited context with 3+ citations")
        else:
            print("❌ POOR: Insufficient context")

        if duration < 45:
            print("✅ PERFORMANCE: Fast response time")
        elif duration < 60:
            print("⚠️  PERFORMANCE: Acceptable response time")
        else:
            print("❌ PERFORMANCE: Slow response time")

        return True

    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_multiple_queries():
    """Test multiple queries to see context variation."""
    print(f"\n🔍 MULTI-QUERY CONTEXT ANALYSIS")
    print("=" * 70)

    tenant = Tenant.objects.get(slug='stride')
    user = User.objects.first()
    service = UnifiedRAGService(tenant.slug, user)

    test_queries = [
        "What issues did Amanda report?",
        "Tell me about testing challenges",
        "Show me deployment problems",
        "What did the team discuss about bugs?",
        "Latest updates on product features"
    ]

    results = []

    for query in test_queries:
        print(f"\n🔍 Query: '{query}'")
        print("-" * 40)

        try:
            start_time = time.time()
            search_result, retrieved_docs = service.search(
                query_text=query,
                top_k=12,
                min_relevance_score=0.08,
                use_hybrid_search=True
            )
            duration = time.time() - start_time

            citations = search_result.citations.all()
            citation_count = citations.count()

            print(f"   ⏱️  {duration:.1f}s | 🔗 {citation_count} citations | ⭐ {search_result.retriever_score_avg:.3f} avg score")

            results.append({
                'query': query,
                'duration': duration,
                'citations': citation_count,
                'avg_score': search_result.retriever_score_avg
            })

        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            results.append({
                'query': query,
                'error': str(e)
            })

    # Summary
    print(f"\n📊 SUMMARY:")
    print("=" * 50)

    successful_results = [r for r in results if 'error' not in r]
    if successful_results:
        avg_citations = sum(r['citations'] for r in successful_results) / len(successful_results)
        avg_duration = sum(r['duration'] for r in successful_results) / len(successful_results)
        avg_score = sum(r['avg_score'] for r in successful_results) / len(successful_results)

        print(f"✅ Successful queries: {len(successful_results)}/{len(test_queries)}")
        print(f"📊 Average citations: {avg_citations:.1f}")
        print(f"⏱️  Average duration: {avg_duration:.1f}s")
        print(f"⭐ Average score: {avg_score:.3f}")

        if avg_citations >= 6:
            print("🎯 CONTEXT QUALITY: ✅ EXCELLENT")
        elif avg_citations >= 4:
            print("🎯 CONTEXT QUALITY: ✅ GOOD")
        else:
            print("🎯 CONTEXT QUALITY: ⚠️  NEEDS IMPROVEMENT")

if __name__ == "__main__":
    print("🚀 Enhanced Context Retrieval Testing")
    print("Testing improved filtering criteria and comprehensive results")
    print("=" * 70)

    # Test single query with detailed analysis
    success = test_enhanced_context()

    if success:
        # Test multiple queries for comparison
        test_multiple_queries()

    print(f"\n✅ Enhanced context testing complete!")
