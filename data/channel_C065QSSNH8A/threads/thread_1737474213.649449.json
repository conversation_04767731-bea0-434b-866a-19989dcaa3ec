{"thread_ts": "1737474213.649449", "channel_id": "C065QSSNH8A", "reply_count": 18, "replies": [{"ts": "1737474274.041919", "text": "They won't need to be up too long, just enough for me to set up the data and cycle, and do a series of screenshots.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "IUkTD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They won't need to be up too long, just enough for me to set up the data and cycle, and do a series of screenshots."}]}]}]}, {"ts": "1737474952.237749", "text": "<@U07M6QKHUC9> can we use <http://demo.strider.io|demo.strider.io>?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "HECrK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " can we use demo.strider.io?"}]}]}]}, {"ts": "1737474990.677409", "text": "yes we can", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "wZMIv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes we can"}]}]}]}, {"ts": "1737474992.300959", "text": "Basically we can use\n• stridedemo\n• demo\n• test\n• qa ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "N/Khw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Basically we can use"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "stridedemo"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "demo"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "test"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "qa "}]}], "style": "bullet", "indent": 0, "offset": 0, "border": 0}]}]}, {"ts": "1737475008.104209", "text": "but we will need a demo instance for the analyst's demo on 25th", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "xq4He", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "but we will need a demo instance for the analyst's demo on 25th"}]}]}]}, {"ts": "1737475035.494379", "text": "so may be after that we can use that for Tithely", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "CqPFv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "so may be after that we can use that for Tithely"}]}]}]}, {"ts": "1737475039.099579", "text": "<@U07EJ2LP44S> can one wait until next week?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "DpR2E", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can one wait until next week?"}]}]}]}, {"ts": "1737475044.575469", "text": "yes for sure", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "FT5z2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes for sure"}]}]}]}, {"ts": "1737475049.649359", "text": "I can bring up 3 tomorrow ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "SXMyA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can bring up 3 tomorrow "}]}]}]}, {"ts": "1737475066.721249", "text": "we don't even know curanas settings yet, so that one can wait easily", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "K10DU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we don't even know curanas settings yet, so that one can wait easily"}]}]}]}, {"ts": "1737475123.228029", "text": "they may need to be up for a live training at some point but i can let you know timeframes for that too", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "HHVam", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "they may need to be up for a live training at some point but i can let you know timeframes for that too"}]}]}]}, {"ts": "1737475139.718909", "text": "for now i mostly just want to get all the screenshots so i can create their training document", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "8VAX8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "for now i mostly just want to get all the screenshots so i can create their training document"}]}]}]}, {"ts": "1737475174.982459", "text": "Ok should I wait until you come back for each one of them?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "ivGRW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok should I wait until you come back for each one of them?"}]}]}]}, {"ts": "1737475200.772049", "text": "ideally i could get them a little sooner b/c i think i need to adjust the data", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "q1vY9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ideally i could get them a little sooner b/c i think i need to adjust the data"}]}]}]}, {"ts": "1737475208.133669", "text": "and for diversified i need to upload their bonus info", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "bYh+2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "and for diversified i need to upload their bonus info"}]}]}]}, {"ts": "1737475214.241129", "text": "so it'll take a bit of time", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "6yiTj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "so it'll take a bit of time"}]}]}]}, {"ts": "1737475250.087719", "text": "Ok will bring two more tomorrow and demo you can use it next week", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737474213.649449", "blocks": [{"type": "rich_text", "block_id": "gxUwg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok will bring two more tomorrow and demo you can use it next week"}]}]}]}, {"ts": "1737538859.795819", "text": "<@U07EJ2LP44S>\n\n• <http://stridedemo.stridehr.io|stridedemo.stridehr.io>\n• <http://demo.stridehr.io|demo.stridehr.io>\n• <http://test.stridehr.io|test.stridehr.io>\n• <http://qa.stridehr.io|qa.stridehr.io>\nAll the ENVs are up and running and updated with demo data. except for <http://demo.stridhr.io|demo.stridhr.io>, rest all have the same data.  <http://demo.stridhr.io|demo.stridhr.io> has the same data but there would edits and cycles created for demo and there would be slight differences in the data.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737474213.649449", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1737538989.000000"}, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "SIv0V", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "http://stridedemo.stridehr.io", "text": "stridedemo.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://demo.stridehr.io", "text": "demo.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAll the ENVs are up and running and updated with demo data. except for "}, {"type": "link", "url": "http://demo.stridhr.io", "text": "demo.stridhr.io"}, {"type": "text", "text": ", rest all have the same data.  "}, {"type": "link", "url": "http://demo.stridhr.io", "text": "demo.stridhr.io"}, {"type": "text", "text": " has the same data but there would edits and cycles created for demo and there would be slight differences in the data."}]}]}]}], "created_at": "2025-05-22T22:00:49.352716"}