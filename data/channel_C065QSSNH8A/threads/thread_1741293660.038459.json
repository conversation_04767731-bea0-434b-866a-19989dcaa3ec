{"thread_ts": "1741293660.038459", "channel_id": "C065QSSNH8A", "reply_count": 2, "replies": [{"ts": "1741320788.498419", "text": "Will mitigate if it’s really an issue", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741293660.038459", "blocks": [{"type": "rich_text", "block_id": "KiJUQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will mitigate if "}, {"type": "text", "text": "it’s"}, {"type": "text", "text": " really an issue"}]}]}]}, {"ts": "1741344252.124149", "text": "<@U07EJ2LP44S> This is taken care. Unlike other customers we didn't delete and create a new cycle after the data updates and there is an issue with uploads which doesn't carry title changes to cycle, However I have mitigated the issue should be good now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741293660.038459", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "F/wki", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is taken care. Unlike other customers we didn't delete and create a new cycle after the data updates and there is an issue with uploads which doesn't carry title changes to cycle, However I have mitigated the issue should be good now."}]}]}]}], "created_at": "2025-05-22T22:00:49.366677"}