{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2024-05", "message_count": 26, "messages": [{"ts": "1714568576.779389", "text": "<@U065H3M6WJV> Practifi's sandbox is ready at <https://practifi.compiify.com/>. Social Login ( google) is enabled on it for you to login.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1714568576.779389", "reply_count": 6, "attachments": [{"from_url": "https://practifi.compiify.com/", "service_icon": "https://practifi.compiify.com/apple-touch-icon.png", "id": 1, "original_url": "https://practifi.compiify.com/", "fallback": "Compiify", "text": "Web site created using create-react-app", "title": "Compiify", "title_link": "https://practifi.compiify.com/", "service_name": "practifi.compiify.com"}], "blocks": [{"type": "rich_text", "block_id": "ha/VC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " <PERSON><PERSON><PERSON><PERSON>'s sandbox is ready at "}, {"type": "link", "url": "https://practifi.compiify.com/"}, {"type": "text", "text": ". Social Login ( google) is enabled on it for you to login."}]}]}]}, {"ts": "1714598285.612259", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> For Merit 2.0, I've updated <https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit#heading=h.cdw134u67616|this doc> with more detail on the different cases we need to support in the planner view. The \"task list\" design will likely have some more refinement between now and next week, so the planner (\"manager\") view is higher confidence design to work on.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1714598285.612259", "reply_count": 1, "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "files": [{"id": "F07033J43GE", "created": 1713895996, "timestamp": 1713895996, "name": "Merit 2.0 designs (in progress)", "title": "Merit 2.0 designs (in progress)", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10", "external_url": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit", "url_private": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSJ+v4Ckz9fyobGRnNIMepoAeDmimYH96l+X1oAdRQOlFACEA0mB6CnGkFACYHpR0FLRQAA5GaWgUUAITikyP8ilLAGkDA+tABuH+RS5+tJlfWgbQetADhRQDmigD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07033J43GE/merit_2", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "EXJrq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For Merit 2.0, I've updated "}, {"type": "link", "url": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit#heading=h.cdw134u67616", "text": "this doc"}, {"type": "text", "text": " with more detail on the different cases we need to support in the planner view. The \"task list\" design will likely have some more refinement between now and next week, so the planner (\"manager\") view is higher confidence design to work on."}]}]}]}, {"ts": "1714740610.381389", "text": "<@U065H3M6WJV> <@U04DS2MBWP4>\n\nHere are links to loom video prepared by engineering team for work completed on two features\n1. Revised Salary Bands:  <https://www.loom.com/share/a6504552a03b40d4a88702f7d7dc8b03>\n2. Enable data edit: <https://www.loom.com/share/bf4cd223646f4fc2947ace93227ec918>\nBoth of these features are deployed and available to view on all sandboxes for upcoming customers as well <http://sdf-test.compiify.com|sdf-test.compiify.com> and <http://test.compiify.com|test.compiify.com>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1714740610.381389", "reply_count": 14, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "iWoI9", "video_url": "https://www.loom.com/embed/bf4cd223646f4fc2947ace93227ec918?unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/bf4cd223646f4fc2947ace93227ec918-4x3.jpg", "alt_text": "Loom Message - 3 May 2024", "title": {"type": "plain_text", "text": "Loom Message - 3 May 2024", "emoji": true}, "title_url": "https://www.loom.com/share/bf4cd223646f4fc2947ace93227ec918", "author_name": "<PERSON><PERSON><PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 2 min  👀 2 views  ", "emoji": true}}, {"type": "actions", "block_id": "0WQzg", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/bf4cd223646f4fc2947ace93227ec918"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"bf4cd223646f4fc2947ace93227ec918\",\"videoName\":\"Loom Message - 3 May 2024\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/bf4cd223646f4fc2947ace93227ec918", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "A/75b", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": "\n\nHere are links to loom video prepared by engineering team for work completed on two features\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Revised Salary Bands:  "}, {"type": "link", "url": "https://www.loom.com/share/a6504552a03b40d4a88702f7d7dc8b03"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Enable data edit: "}, {"type": "link", "url": "https://www.loom.com/share/bf4cd223646f4fc2947ace93227ec918"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Both of these features are deployed and available to view on all sandboxes for upcoming customers as well "}, {"type": "link", "url": "http://sdf-test.compiify.com", "text": "sdf-test.compiify.com"}, {"type": "text", "text": " and "}, {"type": "link", "url": "http://test.compiify.com", "text": "test.compiify.com"}]}]}]}, {"ts": "1715035370.082369", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> more details added to the <https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit|Merit 2.0 requirements doc> to explain the logic for the timeline steps for the new module. Also some refinements have been made in this <https://www.figma.com/file/WOJVdr3bbnBloZT9z6pY9K/Design-file-(Copy)?type=design&amp;node-id=5554-75155&amp;mode=design&amp;t=RccoICNOg9CCd8h7-0|design file> for some of the more detailed examples.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1715035370.082369", "reply_count": 2, "edited": {"user": "U065H3M6WJV", "ts": "1715035372.000000"}, "blocks": [{"type": "rich_text", "block_id": "crCP1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " more details added to the "}, {"type": "link", "url": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit", "text": "Merit 2.0 requirements doc"}, {"type": "text", "text": " to explain the logic for the timeline steps for the new module. Also some refinements have been made in this "}, {"type": "link", "url": "https://www.figma.com/file/WOJVdr3bbnBloZT9z6pY9K/Design-file-(Copy)?type=design&node-id=5554-75155&mode=design&t=RccoICNOg9CCd8h7-0", "text": "design file"}, {"type": "text", "text": " for some of the more detailed examples."}]}]}]}, {"ts": "1715184253.957859", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> In addition to the quirk we saw in the pay bands with currency conversion, I saw some inconsistencies in the Organization tab as well. I'll add a few screens in the thread.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1715184253.957859", "reply_count": 6, "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "x4p6v", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " In addition to the quirk we saw in the pay bands with currency conversion, I saw some inconsistencies in the Organization tab as well. I'll add a few screens in the thread."}]}]}]}, {"ts": "1715208748.434879", "text": "<@U0690EB5JE5> What's the ETA on Vercara's goal-document customization? I have another sync with them on Friday and would like to give them an update on that timing.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1715208748.434879", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "VYMsT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " What's the ETA on Vercar<PERSON>'s goal-document customization? I have another sync with them on Friday and would like to give them an update on that timing."}]}]}]}, {"ts": "1715273291.487359", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I see the charts on Organization page have reverted in customer environments but not on staging ... was there a different plan for when this would be reverted in staging env?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1715273291.487359", "reply_count": 3, "files": [{"id": "F073EJVAEKS", "created": 1715273285, "timestamp": 1715273285, "name": "Screenshot 2024-05-09 at 9.46.32 AM.png", "title": "Screenshot 2024-05-09 at 9.46.32 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 1279843, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F073EJVAEKS/screenshot_2024-05-09_at_9.46.32___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F073EJVAEKS/download/screenshot_2024-05-09_at_9.46.32___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F073EJVAEKS-5faeed8894/screenshot_2024-05-09_at_9.46.32___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F073EJVAEKS-5faeed8894/screenshot_2024-05-09_at_9.46.32___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F073EJVAEKS-5faeed8894/screenshot_2024-05-09_at_9.46.32___am_360.png", "thumb_360_w": 360, "thumb_360_h": 150, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F073EJVAEKS-5faeed8894/screenshot_2024-05-09_at_9.46.32___am_480.png", "thumb_480_w": 480, "thumb_480_h": 200, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F073EJVAEKS-5faeed8894/screenshot_2024-05-09_at_9.46.32___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F073EJVAEKS-5faeed8894/screenshot_2024-05-09_at_9.46.32___am_720.png", "thumb_720_w": 720, "thumb_720_h": 300, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F073EJVAEKS-5faeed8894/screenshot_2024-05-09_at_9.46.32___am_800.png", "thumb_800_w": 800, "thumb_800_h": 334, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F073EJVAEKS-5faeed8894/screenshot_2024-05-09_at_9.46.32___am_960.png", "thumb_960_w": 960, "thumb_960_h": 400, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F073EJVAEKS-5faeed8894/screenshot_2024-05-09_at_9.46.32___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 427, "original_w": 4850, "original_h": 2022, "thumb_tiny": "AwAUADDRwDzS4HpSYBxkUbQD0oAMDnijjHSgADOKXFADcrv24560uBS456UUAHpR3o9KO9ACdqX0pO1L6UAHeijvRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F073EJVAEKS/screenshot_2024-05-09_at_9.46.32___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F073EJVAEKS-e1cbe1f52b", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "A/0NV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I see the charts on Organization page have reverted in customer environments but not on staging ... was there a different plan for when this would be reverted in staging env?"}]}]}]}, {"ts": "1715306883.803369", "text": "Friday Demos\n- Merit View 2.0\n- Stride Wesite", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Sdn4r", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Friday Demos\n- Merit View 2.0\n- Stride Wesite"}]}]}]}, {"ts": "1715357297.200519", "text": "<!here> did we finalize <http://stridenow.io|stridenow.io> as the address?", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NJHwg", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " did we finalize "}, {"type": "link", "url": "http://stridenow.io", "text": "stridenow.io"}, {"type": "text", "text": " as the address?"}]}]}]}, {"ts": "1715363901.193489", "text": "<@U065H3M6WJV> <@U04DKEFP1K8> <@U0690EB5JE5> SDF wants to see that we have fixed all of their issues before agreeing to do their next cycle with us. They want to do their own diligence now before moving ahead with us.  Let's discuss the game plan in the next eng meeting.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV", "U04DKEFP1K8", "U0690EB5JE5"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "xgtds", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " SDF wants to see that we have fixed all of their issues before agreeing to do their next cycle with us. They want to do their own diligence now before moving ahead with us.  Let's discuss the game plan in the next eng meeting."}]}]}]}, {"ts": "1715379532.446499", "text": "Hey <@U0690EB5JE5> <@U04DKEFP1K8> - <PERSON><PERSON><PERSON> at Vercara asked how they should input their salary bands and sent me <https://docs.google.com/spreadsheets/d/13vyDxX6tHcJAOpQ-xdNwN5EkfwRb9GOm/edit#gid=295722079|this Excel sheet> to show their existing format. (I uploaded &amp; converted to Google Sheets so we can work on it without the customer's visibility.)\n\nSome questions in the thread....", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1715379532.446499", "reply_count": 7, "edited": {"user": "U065H3M6WJV", "ts": "1715379538.000000"}, "blocks": [{"type": "rich_text", "block_id": "8wy2w", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " - <PERSON><PERSON><PERSON> at Vercara asked how they should input their salary bands and sent me "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/13vyDxX6tHcJAOpQ-xdNwN5EkfwRb9GOm/edit#gid=295722079", "text": "this Excel sheet"}, {"type": "text", "text": " to show their existing format. (I uploaded & converted to Google Sheets so we can work on it without the customer's visibility.)\n\nSome questions in the thread...."}]}]}]}, {"ts": "1715394404.570659", "text": "<@U065H3M6WJV> <@U04DS2MBWP4> <@U04DKEFP1K8> I would like to suggest a small(may be big) change in the way we used work with Lasoft engineers vs FTEs. The issues/requirements were always flowing via <PERSON><PERSON><PERSON><PERSON> and sometimes me. But with full time engineers, as I expect them to own and act like subject matter experts, thinking to create a new slack channel to report customer issues, bugs and any back and forth questions on the ongoing work. We can still use this channel for any new requirements/product strategy etc.. which are yet be finalized before engineers can start going through the same. Please let me know your thoughts on this :slightly_smiling_face:", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1715394404.570659", "reply_count": 5, "edited": {"user": "U0690EB5JE5", "ts": "1715394492.000000"}, "blocks": [{"type": "rich_text", "block_id": "q86P5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I would like to suggest a small(may be big) change in the way we used work with Lasoft engineers vs FTEs. The issues/requirements were always flowing via Saurab<PERSON> and sometimes me. But with full time engineers, as I expect them to own and act like subject matter experts, thinking to create a new slack channel to report customer issues, bugs and any back and forth questions on the ongoing work. We can still use this channel for any new requirements/product strategy etc.. which are yet be finalized before engineers can start going through the same. Please let me know your thoughts on this "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1715394441.208169", "text": "<@U065H3M6WJV> You will be talking to engineers directly sometimes with this approach :slightly_smiling_face:", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1715394618.000000"}, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TMuFm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " You will be talking to engineers directly sometimes with this approach "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1715606937.553519", "text": "<!here> deployment process documentation is available here <https://docs.google.com/document/d/1QJIyHywqQlLHDc0SYnOUg1GWwngU7OAF8fU8DR0_djs/edit> ( still in WIP, eta to complete is 5/14 IST). Link is available on eng discussion document too", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "S9uzq", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " deployment process documentation is available here "}, {"type": "link", "url": "https://docs.google.com/document/d/1QJIyHywqQlLHDc0SYnOUg1GWwngU7OAF8fU8DR0_djs/edit"}, {"type": "text", "text": " ( still in WIP, eta to complete is 5/14 IST). Link is available on eng discussion document too"}]}]}]}, {"ts": "1715793835.335089", "text": "I listened to the rest of the call from after I dropped -- just to confirm, I knew we might not have our delivery dates locked in to share with <PERSON> yet, and am planning to use today's call to get more info on exactly what they need in the promotion cycle. But I might quickly let her see a preview of how much we have identified and fixed -- my goal is to have this finalized for her by end of week.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1715793835.335089", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "j73/H", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I listened to the rest of the call from after I dropped -- just to confirm, I knew we might not have our delivery dates locked in to share with <PERSON> yet, and am planning to use today's call to get more info on exactly what they need in the promotion cycle. But I might quickly let her see a preview of how much we have identified and fixed -- my goal is to have this finalized for her by end of week."}]}]}]}, {"ts": "1715814203.809169", "text": "I know <PERSON><PERSON><PERSON><PERSON>'s unable to join the next regularly scheduled eng discussion - <@U0690EB5JE5> any topics that would make sense for us to cover without him?\n• Any update on plan that you and <PERSON><PERSON><PERSON><PERSON> have aligned on for freezing/branching builds\n• Anything to cover on Stride website?\n• Vercara customization?\n• Anything else?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1715814203.809169", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "d/oLP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I know <PERSON><PERSON><PERSON><PERSON>'s unable to join the next regularly scheduled eng discussion - "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " any topics that would make sense for us to cover without him?\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Any update on plan that you and <PERSON><PERSON><PERSON><PERSON> have aligned on for freezing/branching builds"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Anything to cover on Stride website?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Vercara customization?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Anything else?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1715814501.605699", "text": "Yes we should also discuss the domain migration to stride", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zEiNw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes we should also discuss the domain migration to stride"}]}]}]}, {"ts": "1715963292.705469", "text": "Need a few min break before the sdf/quality call", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nG9y+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Need a few min break before the sdf/quality call"}]}]}]}, {"ts": "1715980668.835639", "text": "Logged the issue I mentioned with Bonus Award (<https://compiify.atlassian.net/browse/COM-3006|COM-3006>), also captured in a <https://www.loom.com/share/1ea7de4e7486409b8c9087755a3d4dad?sid=925d1bac-7d21-4292-892c-a5ddb38d4b86|Loom video>.", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DKEFP1K8", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "GhScT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Logged the issue I mentioned with Bonus Award ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3006", "text": "COM-3006"}, {"type": "text", "text": "), also captured in a "}, {"type": "link", "url": "https://www.loom.com/share/1ea7de4e7486409b8c9087755a3d4dad?sid=925d1bac-7d21-4292-892c-a5ddb38d4b86", "text": "Loom video"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1716330842.420499", "text": "<@U065H3M6WJV> Can you please review <PERSON><PERSON>'s in the sheet titled \"Final To-Do's\" in this document <https://docs.google.com/spreadsheets/d/1iEM2qLPPZ6CXxIsCNmzwZ41rqtAViwEYK9ZibQnpWtM/edit#gid=0>.  If any of the item is not immediately required by June 15th please add your comment in column \"Notes / Comment\"\n\nNote:\n1. These are subset of currently pending To-Do's that engineering needs to address. I have also attached all pending To-Do's in another sheet titled \"All To-Do's\".\nOnce you have reviewed i will add a label to all the jira's and bring up a dashboard that folks in channel can track on a daily basis.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1716330842.420499", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "lKERL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Can you please review <PERSON><PERSON>'s in the sheet titled \"Final To-Do's\" in this document "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1iEM2qLPPZ6CXxIsCNmzwZ41rqtAViwEYK9ZibQnpWtM/edit#gid=0"}, {"type": "text", "text": ".  If any of the item is not immediately required by June 15th please add your comment in column \"Notes / Comment\"\n\nNote:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "These are subset of currently pending To-Do's that engineering needs to address. I have also attached all pending To-Do's in another sheet titled \"All To-Do's\"."}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nOnce you have reviewed i will add a label to all the jira's and bring up a dashboard that folks in channel can track on a daily basis."}]}]}]}, {"ts": "1716421784.334139", "text": "<@U065H3M6WJV> Apart from CWA, are their any customers who don't have established job levels?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1716421784.334139", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "dXoFz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Apart from CWA, are their any customers who don't have established job levels?"}]}]}]}, {"ts": "1716550061.869069", "text": "I have cancelled the demo meeting for today and will do it offline. There were two features:\n*#1. filtering for adjustment letter*\n<https://www.loom.com/share/8d2b8d369ac7435890478073e61ece53?sid=289dce4d-9c1c-442e-bb3c-1b9a049298b0>\n\n*#2. currency exchange. Will share the loom recordings.* TBD\n\n*#3. Auto Expand Nav Bar*\n<https://www.loom.com/share/26f4341c02564131a15d531a2c617d15?sid=53e87007-eeb7-4501-a515-3f2da54e3e8a>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1716568898.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "w5KD1", "video_url": "https://www.loom.com/embed/8d2b8d369ac7435890478073e61ece53?sid=289dce4d-9c1c-442e-bb3c-1b9a049298b0&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/8d2b8d369ac7435890478073e61ece53-4x3.jpg", "alt_text": "Compensation Cycle 2022 | Compiify - 24 May 2024", "title": {"type": "plain_text", "text": "Compensation Cycle 2022 | Compiify - 24 May 2024", "emoji": true}, "title_url": "https://www.loom.com/share/8d2b8d369ac7435890478073e61ece53", "author_name": "<PERSON><PERSON><PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 33 sec  👀 1 view  ", "emoji": true}}, {"type": "actions", "block_id": "zvtaM", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/8d2b8d369ac7435890478073e61ece53?sid=289dce4d-9c1c-442e-bb3c-1b9a049298b0"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"8d2b8d369ac7435890478073e61ece53\",\"videoName\":\"Compensation Cycle 2022 | Compiify - 24 May 2024\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/8d2b8d369ac7435890478073e61ece53?sid=289dce4d-9c1c-442e-bb3c-1b9a049298b0", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "aE+NP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have cancelled the demo meeting for today and will do it offline. There were two features:\n"}, {"type": "text", "text": "#1. filtering for adjustment letter", "style": {"bold": true}}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://www.loom.com/share/8d2b8d369ac7435890478073e61ece53?sid=289dce4d-9c1c-442e-bb3c-1b9a049298b0"}, {"type": "text", "text": "\n\n"}, {"type": "text", "text": "#2. currency exchange. Will share the loom recordings.", "style": {"bold": true}}, {"type": "text", "text": " TBD\n\n"}, {"type": "text", "text": "#3. Auto Expand Nav Bar", "style": {"bold": true}}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://www.loom.com/share/26f4341c02564131a15d531a2c617d15?sid=53e87007-eeb7-4501-a515-3f2da54e3e8a"}]}]}]}, {"ts": "1716569298.931919", "text": "<!here> <https://docs.google.com/document/d/1RTbo30sJVdfbgDZxj8YyM5oOtj6-dfrIZe4htDzsrOw/edit?addon_store#heading=h.292abij1oe38> People insights data validation analysis", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "heRpg", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://docs.google.com/document/d/1RTbo30sJVdfbgDZxj8YyM5oOtj6-dfrIZe4htDzsrOw/edit?addon_store#heading=h.292abij1oe38"}, {"type": "text", "text": " People insights data validation analysis"}]}]}]}, {"ts": "1717036127.935939", "text": "<!here> I need to pickup my wife from bustand around 9pm IST, can we move the eng discussion by an hour?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1717036127.935939", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "jnJpQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I need to pickup my wife from bustand around 9pm IST, can we move the eng discussion by an hour?"}]}]}]}, {"ts": "1717036179.558599", "text": "i.e. 10 am PST", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "JhWNX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i.e. 10 am PST"}]}]}]}, {"ts": "1717094514.561549", "text": "<!here> is today's update for people insight\n1. Item 1,3, and 8 were pending as of yesterday from the description provided in <https://compiify.atlassian.net/browse/COM-3091>. Fixes for 1 &amp; 3 are in code review. No progress on item 8\n2. UI updates: Items reported under <https://compiify.atlassian.net/browse/COM-3090> were  reviewed and merged ( comment section has details about few pending  / blocking items remaining)\n3. <https://compiify.atlassian.net/browse/COM-3095> was opened to fix BUDGET SPEND BY DEPARTMENT. Fix for this jira is in code review \n4. New issue opened: <https://compiify.atlassian.net/browse/COM-3116>\n5. Fix for <https://compiify.atlassian.net/browse/COM-3072> is in code review \nOpen items to track for friday\n1. <https://compiify.atlassian.net/browse/COM-3116>\n2. <https://compiify.atlassian.net/browse/COM-3118>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13152::4a025c701eb411ef9f02e1d8ecdd1bb4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3091?atlOrigin=eyJpIjoiMjRmYzU0MDI5NzFhNGM1NTk2MDQyZTc2MjYyNmFmMWQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3091 People Insights: Match the current Implementation with the PRD>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13152::4a0283821eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "mrkdwn", "text": "Status: *In Development*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2a19465d953b8640b8f3558fd4f15da9?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13152::4a025c711eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13152\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13152\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3091", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:13151::4a025c721eb411ef9f02e1d8ecdd1bb4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3090?atlOrigin=eyJpIjoiMTUzOTIwYTNlYmRhNDNkNjk4MGE4ZTY2OGYwODdlNTgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3090 People Insights | Match the current UI with the Figma Design>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13151::4a0283831eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "mrkdwn", "text": "Status: *Blocked*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/606486a6aee240006877c236/2b20eeb2-9337-436b-afa5-c418e47dbe4c/48?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13151::4a025c731eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13151\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13151\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3090", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 3, "blocks": [{"type": "section", "block_id": "uf:ih:13156::4a025c741eb411ef9f02e1d8ecdd1bb4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3095?atlOrigin=eyJpIjoiMTE3MzBkZWIwYjM2NDA4NGJlN2YzNmE5ZGU2OTZmNTIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3095 Budget spend is adding up against a single department even when the…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13156::4a0283841eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/79ad6fec1eb23766c2c2c46cb17078ab?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13156::4a025c751eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13156\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13156\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3095", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 4, "blocks": [{"type": "section", "block_id": "uf:ih:13177::4a025c761eb411ef9f02e1d8ecdd1bb4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3116?atlOrigin=eyJpIjoiZmFhNDIyOTQ4MjdlNGQwNzlmMjRhNDMxYWJiMDAyMzQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3116 Position-in-band employee counts don't add up>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13177::4a0283851eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2a19465d953b8640b8f3558fd4f15da9?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13177::4a025c771eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13177\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13177\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3116", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 5, "blocks": [{"type": "section", "block_id": "uf:ih:13133::4a0283801eb411ef9f02e1d8ecdd1bb4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3072?atlOrigin=eyJpIjoiNDEyNjIzNzA3YmY4NGQzMGJkMzE2ZDFiODQ4OGJiZmYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3072 Unable to load people insights after doing bulk update of any categ…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13133::4a0283861eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/79ad6fec1eb23766c2c2c46cb17078ab?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13133::4a0283811eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13133\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13133\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3072", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "8qS9K", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " is today's update for people insight\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Item 1,3, and 8 were pending as of yesterday from the description provided in "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3091"}, {"type": "text", "text": ". Fixes for 1 & 3 are in code review. No progress on item 8"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "UI updates: Items reported under "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3090"}, {"type": "text", "text": " were  reviewed and merged ( comment section has details about few pending  / blocking items remaining)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3095"}, {"type": "text", "text": " was opened to fix BUDGET SPEND BY DEPARTMENT. Fix for this jira is in code review "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "New issue opened: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3116"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Fix for "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3072"}, {"type": "text", "text": " is in code review "}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nOpen items to track for friday\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3116"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3118"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}]}