#!/usr/bin/env python3
"""
Debug why curana chunks aren't being found by vector search.
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.unified_rag_service import UnifiedRAGService
from apps.accounts.models import Tenant, User
from apps.documents.models import DocumentChunk

def debug_curana_vector_search():
    """Debug why curana chunks aren't found by vector search."""
    print("🔍 Debugging Curana Vector Search")
    print("=" * 50)
    
    # Get tenant and user
    tenant = Tenant.objects.get(slug='stride')
    user = User.objects.filter(email='<EMAIL>').first()
    
    if not user:
        print("❌ User not found")
        return
    
    # Get curana chunks from database
    curana_chunks = DocumentChunk.objects.filter(
        tenant=tenant,
        text__icontains='curana'
    )
    
    print(f"📊 Found {curana_chunks.count()} chunks containing 'curana' in database")
    
    # Show curana chunk details
    for i, chunk in enumerate(curana_chunks):
        print(f"\n📄 Curana Chunk {i+1} (ID: {chunk.id}):")
        print(f"   Node ID: {chunk.metadata.get('node_id')}")
        print(f"   Text: {chunk.text[:200]}...")
        
        # Check if this chunk has embeddings
        if hasattr(chunk, 'embedding'):
            print(f"   ✅ Has embedding metadata: {chunk.embedding.vector_id}")
        else:
            print(f"   ❌ No embedding metadata found")
    
    print("\n" + "=" * 50)
    print("🔍 Testing direct vector search for curana chunks...")
    
    # Initialize RAG service
    rag_service = UnifiedRAGService('stride', user)
    
    # Test different search queries
    test_queries = [
        "curana",
        "Curana",
        "latest curana",
        "curana update",
        "curana news",
        "diversified curana"
    ]
    
    for query in test_queries:
        print(f"\n🔎 Testing query: '{query}'")
        
        # Execute query directly with citation engine
        response = rag_service.citation_engine.query(query)
        source_nodes = response.source_nodes
        
        print(f"   📊 Found {len(source_nodes)} nodes")
        
        # Check if any of the returned nodes are curana chunks
        curana_node_ids = [chunk.metadata.get('node_id') for chunk in curana_chunks]
        found_curana = False
        
        for node in source_nodes:
            if node.node_id in curana_node_ids:
                found_curana = True
                print(f"   ✅ Found curana chunk: {node.node_id} (score: {getattr(node, 'score', 'N/A')})")
        
        if not found_curana:
            print(f"   ❌ No curana chunks found in results")
            
            # Show what was found instead
            if source_nodes:
                print(f"   🔍 Top result: {source_nodes[0].node_id} (score: {getattr(source_nodes[0], 'score', 'N/A')})")
                
                # Find the chunk for this node
                top_chunk = DocumentChunk.objects.filter(
                    tenant=tenant,
                    metadata__node_id=source_nodes[0].node_id
                ).first()
                
                if top_chunk:
                    print(f"   📄 Top result text: {top_chunk.text[:100]}...")

if __name__ == "__main__":
    debug_curana_vector_search()
