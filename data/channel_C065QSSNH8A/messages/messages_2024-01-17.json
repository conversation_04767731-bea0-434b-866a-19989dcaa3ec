{"date": "2024-01-17", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1705447648.244929", "text": "<@U065H3M6WJV> let me know when staging environment can be updated to latest build, it is now running a very old build ", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1705447648.244929", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "/prWM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " let me know when staging environment can be updated to latest build, it is now running a very old build "}]}]}]}, {"ts": "1705447587.090079", "text": "<@U065H3M6WJV> <http://dev-app.compiify.com|dev-app.compiify.com> will be taken down later today. It will be replaced by a new environment immediately ( with a new url though)", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "QzXFo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://dev-app.compiify.com", "text": "dev-app.compiify.com"}, {"type": "text", "text": " will be taken down later today. It will be replaced by a new environment immediately ( with a new url though)"}]}]}]}, {"ts": "1705440442.844299", "text": "<@U04DKEFP1K8> Let's get Clarity/Heap enabled there (wait about an hour so that we don't disrupt their immediate testing), and be ready if they ask to make specific changes to budget values. My guess is they won't be able to do it easily from the Allocate page and we might need to do that via a backend update.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705440442.844299", "reply_count": 1, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "tQoKw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Let's get Clarity/Heap enabled there (wait about an hour so that we don't disrupt their immediate testing), and be ready if they ask to make specific changes to budget values. My guess is they won't be able to do it easily from the Allocate page and we might need to do that via a backend update."}]}]}]}, {"ts": "1705440386.361029", "text": "Had a good call with them. Immediate feedback was that the \"reviewed\" step feels like too much for a manager during planning while they try to see impact to the budget, so I let them know that it's already something we've identified to improve in the next version. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705440386.361029", "reply_count": 1, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "eyes", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "9tv9o", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Had a good call with them. Immediate feedback was that the \"reviewed\" step feels like too much for a manager during planning while they try to see impact to the budget, so I let them know that it's already something we've identified to improve in the next version. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1705438905.187649", "text": "I have another meeting", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EG9VM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have another meeting"}]}]}]}, {"ts": "1705438879.384269", "text": "Who all is joining for <PERSON>eur<PERSON><PERSON>? <https://us06web.zoom.us/j/86095313675?pwd=4PSvrfaAP869AKfKuBGhLaRPJxN383.1>", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R06EUNP6U64", "block_id": "Wj60x", "api_decoration_available": false, "call": {"v1": {"id": "R06EUNP6U64", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1705438879, "active_participants": [], "all_participants": [], "display_id": "860-9531-3675", "join_url": "https://us06web.zoom.us/j/86095313675?pwd=4PSvrfaAP869AKfKuBGhLaRPJxN383.1", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1705525819, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/86095313675?pwd=4PSvrfaAP869AKfKuBGhLaRPJxN383.1", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "CK3Bg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Who all is joining for <PERSON>eur<PERSON><PERSON>? "}, {"type": "link", "url": "https://us06web.zoom.us/j/86095313675?pwd=4PSvrfaAP869AKfKuBGhLaRPJxN383.1"}]}]}]}], "created_at": "2025-05-22T21:35:34.591265"}