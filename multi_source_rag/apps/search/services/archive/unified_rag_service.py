"""
Unified LlamaIndex RAG Service - Production Ready

This service completely replaces custom RAG logic with LlamaIndex end-to-end implementation:
- Router query engine for intelligent query routing
- Citation query engine for automatic citation tracking
- Specialized retrievers for different content types
- Advanced query processing and response synthesis
- Production-ready error handling and monitoring
"""

import logging
import time
from typing import Any, Dict, List, Optional, Tuple

from django.contrib.auth.models import User

# LlamaIndex imports
from llama_index.core import VectorStoreIndex
from llama_index.core.query_engine import (
    RouterQueryEngine,
    RetrieverQueryEngine,
    CitationQueryEngine
)
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.tools import QueryEngineTool
from llama_index.core.selectors import LLMSingleSelector
from llama_index.core.response_synthesizers import ResponseMode
from llama_index.core.prompts import PromptTemplate

# App imports
from apps.accounts.models import Tenant
from apps.core.utils.collection_manager import get_collection_name
from apps.core.utils.llama_index_llm import get_llm
from apps.core.utils.llama_index_vectorstore import get_vector_store
from apps.core.utils.response_formatter import format_response
from apps.core.utils.prompt_templates import get_prompt_template, format_prompt
from apps.core.utils.query_classifier import classify_query
from apps.documents.models import DocumentChunk
from apps.search.models import ResultCitation, SearchQuery, SearchResult

logger = logging.getLogger(__name__)


class UnifiedRAGService:
    """
    Production-ready LlamaIndex RAG service that replaces all custom logic.

    Features:
    - Router query engine for intelligent query routing
    - Citation query engine for automatic citation tracking
    - Specialized retrievers for different content types
    - Advanced response synthesis and formatting
    - Comprehensive error handling and monitoring
    """

    def __init__(self, tenant: str, user: Optional[User] = None):
        """
        Initialize the unified RAG service.

        Args:
            tenant: Tenant slug
            user: User performing the search
        """
        self.tenant_slug = tenant
        self.user = user

        # Get tenant object
        try:
            self.tenant = Tenant.objects.get(slug=tenant)
        except Tenant.DoesNotExist:
            raise ValueError(f"Tenant with slug '{tenant}' not found")

        # Initialize components
        self._initialize_components()

        # Processing statistics
        self.stats = {
            "queries_processed": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0
        }

    def _initialize_components(self):
        """Initialize LlamaIndex components."""
        logger.info("🔧 Initializing unified RAG system components...")

        # Initialize LlamaIndex components if not already done
        self._ensure_llamaindex_initialized()

        # Build specialized query engines
        self.query_engines = self._build_query_engines()

        # Build router query engine
        self.router_engine = self._build_router_engine()

        # Build citation engine
        self.citation_engine = self._build_citation_engine()

        logger.info("✅ Unified RAG system components initialized")

    def _ensure_llamaindex_initialized(self):
        """Ensure LlamaIndex components are initialized."""
        try:
            # Initialize embedding models
            from apps.core.utils.llama_index_embeddings import initialize_embedding_models
            initialize_embedding_models()

            # Initialize LLMs
            from apps.core.utils.llama_index_llm import initialize_llms
            initialize_llms()

            # Initialize vector stores
            from apps.core.utils.llama_index_vectorstore import initialize_vector_stores
            initialize_vector_stores()

            # Set global LlamaIndex settings
            from llama_index.core import Settings
            from apps.core.utils.llama_index_setup import llama_index_registry

            # CRITICAL FIX: Use consistent embedding model across the entire system
            from apps.core.utils.embedding_consistency import set_global_embedding_model, validate_embedding_consistency

            # Set the globally consistent embedding model
            set_global_embedding_model()

            # Validate consistency
            if not validate_embedding_consistency():
                logger.error("Embedding model consistency validation failed!")
                raise RuntimeError("Embedding model inconsistency detected")

            logger.info("Set globally consistent embedding model for RAG service")

            # Set global LLM
            llm = llama_index_registry.get_llm()
            if llm:
                Settings.llm = llm
                logger.info("Set global LLM")

            logger.info("LlamaIndex components initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LlamaIndex components: {str(e)}")
            # Continue with degraded functionality
            pass

    def _build_query_engines(self) -> Dict[str, QueryEngineTool]:
        """
        Build specialized query engines for different content types.

        Returns:
            Dictionary of query engine tools
        """
        engines = {}

        # Conversation engine (Slack, chat)
        engines["conversation"] = QueryEngineTool.from_defaults(
            query_engine=self._build_conversation_engine(),
            name="conversation_search",
            description="Search Slack conversations, chat messages, and discussion threads"
        )

        # Code engine (GitHub, code files)
        engines["code"] = QueryEngineTool.from_defaults(
            query_engine=self._build_code_engine(),
            name="code_search",
            description="Search GitHub repositories, code files, PRs, and issues"
        )

        # Document engine (general documents)
        engines["document"] = QueryEngineTool.from_defaults(
            query_engine=self._build_document_engine(),
            name="document_search",
            description="Search general documents, markdown files, and text content"
        )

        return engines

    def _build_conversation_engine(self) -> RetrieverQueryEngine:
        """Build specialized engine for conversation content."""
        collection_name = get_collection_name(self.tenant_slug, intent="conversation")
        vector_store = get_vector_store(collection_name=collection_name)
        index = VectorStoreIndex.from_vector_store(vector_store)

        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=10
        )

        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            llm=get_llm(),
            response_synthesizer_kwargs={
                "response_mode": "tree_summarize"
            }
        )

    def _build_code_engine(self) -> RetrieverQueryEngine:
        """Build specialized engine for code content."""
        collection_name = get_collection_name(self.tenant_slug, intent="code")
        vector_store = get_vector_store(collection_name=collection_name)
        index = VectorStoreIndex.from_vector_store(vector_store)

        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=15
        )

        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            llm=get_llm(),
            response_synthesizer_kwargs={
                "response_mode": "compact"
            }
        )

    def _build_document_engine(self) -> RetrieverQueryEngine:
        """Build specialized engine for document content."""
        collection_name = get_collection_name(self.tenant_slug, intent="document")
        vector_store = get_vector_store(collection_name=collection_name)
        index = VectorStoreIndex.from_vector_store(vector_store)

        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=12
        )

        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            llm=get_llm(),
            response_synthesizer_kwargs={
                "response_mode": "tree_summarize"
            }
        )

    def _build_router_engine(self) -> RouterQueryEngine:
        """Build router query engine for intelligent query routing."""
        return RouterQueryEngine(
            selector=LLMSingleSelector.from_defaults(llm=get_llm()),
            query_engine_tools=list(self.query_engines.values()),
            verbose=True
        )

    def _build_citation_engine(self) -> RetrieverQueryEngine:
        """Build optimized single-call query engine with enhanced prompts."""
        # Use the conversation engine's retriever for citations
        collection_name = get_collection_name(self.tenant_slug, intent="conversation")
        vector_store = get_vector_store(collection_name=collection_name)
        index = VectorStoreIndex.from_vector_store(vector_store)

        # Use retriever with configurable results for comprehensive context
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=15  # OPTIMIZED: Reduced from 20 to 15 to prevent MAX_TOKENS issues
        )

        # Create query engine with enhanced prompt templates
        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            llm=get_llm(),
            response_synthesizer_kwargs={
                "response_mode": "compact",  # Single LLM call mode
                "use_async": False
            }
        )

    def _query_with_enhanced_prompts(self, query_text: str):
        """
        Execute query with enhanced prompt templates based on query classification.

        Args:
            query_text: The query text

        Returns:
            LlamaIndex response with enhanced prompts
        """
        # Classify the query to determine the appropriate prompt template
        classification = classify_query(query_text)
        query_type = classification["type"]
        confidence = classification.get("confidence", 0.0)

        logger.info(f"Classified query as '{query_type}' (confidence: {confidence:.2f})")

        # Get the enhanced prompt template for this query type
        prompt_template = get_prompt_template(
            query=query_text,
            query_type=query_type
        )

        # Create a custom LlamaIndex prompt template with correct variable names
        from llama_index.core.prompts import PromptTemplate as LlamaPromptTemplate

        # Convert our template to use LlamaIndex's expected variable names
        # LlamaIndex expects {context_str} and {query_str} by default
        llama_template = prompt_template.replace("{context}", "{context_str}").replace("{question}", "{query_str}")

        # Create the LlamaIndex prompt template
        llama_prompt = LlamaPromptTemplate(llama_template)

        # Log the template being used for debugging
        logger.info(f"Using enhanced prompt template for '{query_type}' query")
        logger.debug(f"Template preview: {llama_template[:200]}...")

        # Update ALL relevant prompt templates in the citation engine
        prompt_updates = {
            "response_synthesizer:text_qa_template": llama_prompt,
            "response_synthesizer:refine_template": llama_prompt,
            "response_synthesizer:summary_template": llama_prompt,
        }

        # Apply the prompt updates
        self.citation_engine.update_prompts(prompt_updates)

        # Execute the query with enhanced prompts
        response = self.citation_engine.query(query_text)

        logger.info(f"Query executed with enhanced '{query_type}' prompt template")

        return response

    def search(
        self,
        query_text: str,
        top_k: int = 20,
        metadata_filter: Optional[Dict[str, Any]] = None,
        output_format: str = "text",
        min_relevance_score: float = 0.10,  # ENHANCED: Lowered from 0.15 to 0.10 for more comprehensive context
        use_query_expansion: bool = False,
        use_multi_step_reasoning: bool = False,
        use_hybrid_search: bool = True,
        use_context_aware: bool = True
    ) -> Tuple[SearchResult, List[Tuple[Any, float]]]:
        """
        Perform a search using the unified RAG system with enhanced features.

        Args:
            query_text: Query text
            top_k: Number of results to return
            metadata_filter: Filter to apply to search results
            output_format: Output format ('text', 'json', 'markdown', 'table')
            min_relevance_score: Minimum relevance score for documents (default 0.10, optimized for comprehensive context)
            use_query_expansion: Whether to use query expansion (delegated to enhanced service)
            use_multi_step_reasoning: Whether to use multi-step reasoning (delegated to enhanced service)
            use_hybrid_search: Whether to use hybrid search (delegated to enhanced service)
            use_context_aware: Whether to use context-aware search (enhanced prompts)

        Returns:
            Tuple of (SearchResult, retrieved_documents)
        """
        start_time = time.time()

        try:
            # If advanced features are requested, delegate to enhanced service
            if use_query_expansion or use_multi_step_reasoning:
                logger.info("Advanced features requested, delegating to enhanced RAG service")
                from apps.search.services.enhanced_rag_service import EnhancedRAGService

                enhanced_service = EnhancedRAGService(self.tenant_slug, self.user)
                return enhanced_service.search(
                    query_text=query_text,
                    top_k=top_k,
                    metadata_filter=metadata_filter,
                    output_format=output_format,
                    min_relevance_score=min_relevance_score,
                    use_query_expansion=use_query_expansion,
                    use_multi_step_reasoning=use_multi_step_reasoning,
                    use_hybrid_search=use_hybrid_search
                )

            # Create search query record
            search_query = self._create_search_query(query_text, metadata_filter)

            # Apply enhanced prompt templates
            response = self._query_with_enhanced_prompts(query_text)

            # Extract source nodes for citations
            source_nodes = response.source_nodes

            # Filter by relevance score
            filtered_nodes = [
                node for node in source_nodes
                if node.score >= min_relevance_score
            ][:top_k]

            # Calculate processing time
            processing_time = time.time() - start_time

            # Create search result
            search_result = self._create_search_result(
                search_query, response, filtered_nodes, output_format, processing_time
            )

            # Create citations
            self._create_citations(search_result, filtered_nodes)

            # Update statistics
            self._update_stats(processing_time)

            # Convert nodes to documents for return
            retrieved_docs = [(node, node.score) for node in filtered_nodes]

            logger.info(f"Search completed in {processing_time:.2f}s with {len(retrieved_docs)} results")

            return search_result, retrieved_docs

        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            raise

    def _create_search_query(
        self,
        query_text: str,
        metadata_filter: Optional[Dict[str, Any]] = None
    ) -> SearchQuery:
        """
        Create a search query record.

        Args:
            query_text: Query text
            metadata_filter: Metadata filter

        Returns:
            SearchQuery instance
        """
        return SearchQuery.objects.create(
            tenant=self.tenant,
            user=self.user,
            query_text=query_text,
            search_params={
                "metadata_filter": metadata_filter or {},
                "service_type": "unified_rag"
            }
        )

    def _create_search_result(
        self,
        search_query: SearchQuery,
        response: Any,
        source_nodes: List[Any],
        output_format: str,
        processing_time: float
    ) -> SearchResult:
        """
        Create a search result record.

        Args:
            search_query: Search query
            response: LlamaIndex response
            source_nodes: Source nodes
            output_format: Output format
            processing_time: Processing time in seconds

        Returns:
            SearchResult instance
        """
        # Extract response text from LlamaIndex response object
        response_text = ""
        if hasattr(response, 'response'):
            response_text = response.response
        elif hasattr(response, 'answer'):
            response_text = response.answer
        else:
            response_text = str(response)

        # Format response based on output format
        formatted_answer = format_response(
            response_text,
            output_format,
            source_nodes,
            search_query.query_text
        )

        # Calculate average retriever score
        avg_score = 0.0
        if source_nodes:
            scores = [getattr(node, 'score', 0.0) for node in source_nodes]
            avg_score = sum(scores) / len(scores)

        return SearchResult.objects.create(
            search_query=search_query,
            user=self.user,
            generated_answer=formatted_answer,
            retriever_score_avg=avg_score,
            llm_confidence_score=0.8  # Default confidence score
        )

    def _create_citations(
        self,
        search_result: SearchResult,
        source_nodes: List[Any]
    ) -> List[ResultCitation]:
        """
        Create citation records from source nodes with deduplication.

        Args:
            search_result: Search result
            source_nodes: Source nodes

        Returns:
            List of ResultCitation instances
        """
        citations = []
        seen_chunks = set()  # Track chunks we've already created citations for

        for i, node in enumerate(source_nodes):
            try:
                # Try to find the corresponding document chunk using EmbeddingMetadata
                node_id = node.node_id

                # Use EmbeddingMetadata to map vector_id to chunk
                from apps.documents.models import EmbeddingMetadata
                chunk = EmbeddingMetadata.get_chunk_by_vector_id(node_id)

                # Ensure chunk belongs to the correct tenant
                if chunk and chunk.tenant != self.tenant:
                    chunk = None

                if chunk:
                    # Check if we've already created a citation for this chunk
                    if chunk.id in seen_chunks:
                        logger.debug(f"Skipping duplicate citation for chunk {chunk.id} (node {node_id})")
                        continue

                    # Check if citation already exists in database (extra safety)
                    existing_citation = ResultCitation.objects.filter(
                        result=search_result,
                        document_chunk=chunk
                    ).first()

                    if existing_citation:
                        logger.debug(f"Citation already exists for result {search_result.id} and chunk {chunk.id}")
                        citations.append(existing_citation)
                        seen_chunks.add(chunk.id)
                        continue

                    # Create new citation
                    citation = ResultCitation.objects.create(
                        result=search_result,
                        document_chunk=chunk,
                        relevance_score=getattr(node, 'score', 0.0),
                        rank=len(citations) + 1  # Use actual citation count for rank
                    )
                    citations.append(citation)
                    seen_chunks.add(chunk.id)

                else:
                    # Skip creating citation if no chunk is found
                    # since ResultCitation requires a document_chunk
                    logger.warning(f"Could not find chunk for node {node_id}, skipping citation")
                    continue

            except Exception as e:
                logger.error(f"Error creating citation for node {node.node_id}: {str(e)}")

        logger.info(f"Created {len(citations)} unique citations for search result {search_result.id}")
        return citations

    def _update_stats(self, processing_time: float):
        """
        Update processing statistics.

        Args:
            processing_time: Processing time in seconds
        """
        self.stats["queries_processed"] += 1
        self.stats["total_processing_time"] += processing_time
        self.stats["average_processing_time"] = (
            self.stats["total_processing_time"] / self.stats["queries_processed"]
        )

    def get_stats(self) -> Dict[str, Any]:
        """
        Get processing statistics.

        Returns:
            Dictionary of processing statistics
        """
        return self.stats.copy()
