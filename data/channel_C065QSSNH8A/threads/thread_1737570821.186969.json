{"thread_ts": "1737570821.186969", "channel_id": "C065QSSNH8A", "reply_count": 6, "replies": [{"ts": "1737571003.717779", "text": "<@U07EJ2LP44S> Let's hop on a zoom call so I can better understand what's causing the need for additional time or where do you need more support.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737570821.186969", "blocks": [{"type": "rich_text", "block_id": "e9QSD", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Let's hop on a zoom call so I can better understand what's causing the need for additional time or where do you need more support."}]}]}]}, {"ts": "1737571010.829179", "text": "do you want to chat now?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737570821.186969", "blocks": [{"type": "rich_text", "block_id": "v92S1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "do you want to chat now?"}]}]}]}, {"ts": "1737571201.887859", "text": "No I don't. I'm frustrated and not in a place to have a productive conversation. But I've been working 6 hours today alone just on the issues with Curana, Valgenesis, and Diversified, trying to figure out what's wrong with the data, the best solutions going forward, investigating bugs VG is finding, following up on bugs we found yesterday, and so on. I cannot support these 4 cycles customers alone in 20 hours a week. I haven't even touched the work that needs to be done for the training.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737570821.186969", "blocks": [{"type": "rich_text", "block_id": "s+EI2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No I don't. I'm frustrated and not in a place to have a productive conversation. But I've been working 6 hours today alone just on the issues with Curana, Valgenesis, and Diversified, trying to figure out what's wrong with the data, the best solutions going forward, investigating bugs VG is finding, following up on bugs we found yesterday, and so on. I cannot support these 4 cycles customers alone in 20 hours a week. I haven't even touched the work that needs to be done for the training."}]}]}]}, {"ts": "1737571321.445649", "text": "ok. Let's go over this during the standup tomorrow.\n<@U0690EB5JE5> how can you help <PERSON> with the all of these issues? Can you make yourself more available to support the implementation of these customers.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737570821.186969", "blocks": [{"type": "rich_text", "block_id": "nNk0I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok. Let's go over this during the standup tomorrow.\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " how can you help <PERSON> with the all of these issues? Can you make yourself more available to support the implementation of these customers."}]}]}]}, {"ts": "1737571378.337989", "text": "OK", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737570821.186969", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2Tbq0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OK"}]}]}]}, {"ts": "1737592518.229619", "text": "<@U07EJ2LP44S> I completely understand how these issues are frustrating. Let’s discuss how I can help you in closing in these implementations.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737570821.186969", "blocks": [{"type": "rich_text", "block_id": "4Hbea", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I completely understand how these issues are frustrating"}, {"type": "text", "text": "."}, {"type": "text", "text": " "}, {"type": "text", "text": "Let’s"}, {"type": "text", "text": " discuss how I can help you in closing in these implementations."}]}]}]}], "created_at": "2025-05-22T22:00:49.350470"}