{"date": "2023-12-01", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1701454047.145049", "text": "Walking through <https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit|this questionnaire> -- feel free to co-edit", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F0687EKLWDA", "created": 1701394886, "timestamp": 1701394886, "name": "Cycle Config - Q1 2024 - Stellar Development Foundation", "title": "Cycle Config - Q1 2024 - Stellar Development Foundation", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw", "external_url": "https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit", "url_private": "https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSY49PxNJuP+z+dK34flSD2I/KgBct6D86Ue9Jz6j8qUZ70AFFFFACN05pAR70rDIpnHegCTP1oz9fypnFLketADqKAciigAJxUdPYZFMoAUYz/wDWpf8AgQpuKUcdv1oAeOnXNFA6UUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0687EKLWDA/cycle_config_-_q1_2024_-_stellar_development_foundation", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "oOql/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Walking through "}, {"type": "link", "url": "https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit", "text": "this questionnaire"}, {"type": "text", "text": " -- feel free to co-edit"}]}]}]}, {"ts": "1701394884.332609", "text": "For the call with <PERSON><PERSON>, since we already have their earlier responses to the discovery document, I think it'd be best to focus on questions specific to their upcoming comp cycle first. I've <https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit|drafted some questions here> with the goal of walking through it live and having them explain any nuances. Please feel free to suggest additions for any key components of a major comp cycle.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701394884.332609", "reply_count": 2, "files": [{"id": "F0687EKLWDA", "created": 1701394886, "timestamp": 1701394886, "name": "Cycle Config - Q1 2024 - Stellar Development Foundation", "title": "Cycle Config - Q1 2024 - Stellar Development Foundation", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw", "external_url": "https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit", "url_private": "https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSY49PxNJuP+z+dK34flSD2I/KgBct6D86Ue9Jz6j8qUZ70AFFFFACN05pAR70rDIpnHegCTP1oz9fypnFLketADqKAciigAJxUdPYZFMoAUYz/wDWpf8AgQpuKUcdv1oAeOnXNFA6UUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0687EKLWDA/cycle_config_-_q1_2024_-_stellar_development_foundation", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "egN4b", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For the call with <PERSON><PERSON>, since we already have their earlier responses to the discovery document, I think it'd be best to focus on questions specific to their upcoming comp cycle first. I've "}, {"type": "link", "url": "https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit", "text": "drafted some questions here"}, {"type": "text", "text": " with the goal of walking through it live and having them explain any nuances. Please feel free to suggest additions for any key components of a major comp cycle."}]}]}]}, {"ts": "1701386801.575719", "text": "I'd like to send the <https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0|project plan> over to DA by tomorrow.\n• Any more edits needed, or is this ready to go?\n• We don't have any further meetings on the calendar with them (yet), correct?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701386801.575719", "reply_count": 2, "edited": {"user": "U065H3M6WJV", "ts": "1701386810.000000"}, "blocks": [{"type": "rich_text", "block_id": "ziqIv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'd like to send the "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "text": "project plan"}, {"type": "text", "text": " over to DA by tomorrow.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Any more edits needed, or is this ready to go?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "We don't have any further meetings on the calendar with them (yet), correct?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1701374715.147419", "text": "<@U04DKEFP1K8> One thing that would help me is a current list of what's actively being worked on, or next up on your list, for the Eng team. Can you share just a high-level bullet list of which items are already in progress?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701374715.147419", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "NbZPC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " One thing that would help me is a current list of what's actively being worked on, or next up on your list, for the Eng team. Can you share just a high-level bullet list of which items are already in progress?"}]}]}]}, {"ts": "1701370310.848299", "text": "<@U0658EW4B8D> <https://docs.google.com/document/d/1ki-kXXrdjOX7XmzStYKS7Emz_NXii682/edit?usp=sharing&amp;ouid=107994932584597228039&amp;rtpof=true&amp;sd=true|Customer discovery doc>", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F068S275RL0", "created": 1701370313, "timestamp": 1701370313, "name": "Customer discovery document_v1.docx", "title": "Customer discovery document_v1.docx", "mimetype": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filetype": "docx", "pretty_type": "Word Document", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 110971, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1ki-kXXrdjOX7XmzStYKS7Emz_NXii682", "external_url": "https://docs.google.com/document/d/1ki-kXXrdjOX7XmzStYKS7Emz_NXii682/edit?usp=sharing&ouid=107994932584597228039&rtpof=true&sd=true", "url_private": "https://docs.google.com/document/d/1ki-kXXrdjOX7XmzStYKS7Emz_NXii682/edit?usp=sharing&ouid=107994932584597228039&rtpof=true&sd=true", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_360.png", "thumb_360_w": 255, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_480.png", "thumb_480_w": 340, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_800.png", "thumb_800_w": 800, "thumb_800_h": 1131, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_960.png", "thumb_960_w": 679, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1131, "thumb_tiny": "AwAwACHSJwM03zF9acTikI3c7iKAE8xfWlBJ5HSgAD+In6mnZoAKKKKAGt24pOPSiQuMbEDfU4pm6X/nkv8A33QBJlfSlBGeKi3yf88l/wC+/wD61PRnJ+ZAo9mzQA+iiigAooOe1Jz7UALRSc+1Lz3FABRRRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F068S275RL0/customer_discovery_document_v1.docx", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "otIOm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://docs.google.com/document/d/1ki-kXXrdjOX7XmzStYKS7Emz_NXii682/edit?usp=sharing&ouid=107994932584597228039&rtpof=true&sd=true", "text": "Customer discovery doc"}]}]}]}], "created_at": "2025-05-22T21:35:34.574012"}