{"thread_ts": "1739547050.624009", "channel_id": "C065QSSNH8A", "reply_count": 7, "replies": [{"ts": "1739547331.838279", "text": "They can use filters and download export report which effectively is the same with more columns ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739547050.624009", "blocks": [{"type": "rich_text", "block_id": "FyrNe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They can use filters and download export report which effectively is the same with more columns "}]}]}]}, {"ts": "1739548157.020129", "text": "The export does not contain the award percentage. It also doesn't contain a flag marker. I can filter by flags and export, but they do want to look at the entire employee base and see flags and the % in the export.\n\nI could hack together the flags part by combining two sheets, but it still does not export award %.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739547050.624009", "blocks": [{"type": "rich_text", "block_id": "nO2qM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The export does not contain the award percentage. It also doesn't contain a flag marker. I can filter by flags and export, but they do want to look at the entire employee base and see flags and the % in the export.\n\nI could hack together the flags part by combining two sheets, but it still does not export award %."}]}]}]}, {"ts": "1739548284.626069", "text": "Ok let get back on this on Monday. Worst case I can pull a report from backend", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739547050.624009", "blocks": [{"type": "rich_text", "block_id": "vamGP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok let get back on this on Monday. Worst case I can pull a report from backend"}]}]}]}, {"ts": "1739548369.847959", "text": "Ok. I'll let her know. Ideally she wants a full cycle export that includes if someone is flagged, and includes every field in the main table (like bonus % award).", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739547050.624009", "blocks": [{"type": "rich_text", "block_id": "e86qF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. I'll let her know. Ideally she wants a full cycle export that includes if someone is flagged, and includes every field in the main table (like bonus % award)."}]}]}]}, {"ts": "1739548412.830689", "text": "Sure let me see if it’s easy to do ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739547050.624009", "blocks": [{"type": "rich_text", "block_id": "m2LKT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure let me see if "}, {"type": "text", "text": "it’s"}, {"type": "text", "text": " easy to do "}]}]}]}, {"ts": "1739797971.724729", "text": "<@U07EJ2LP44S> Please let me know if this works. I think obvioulsy comments will be expected which is being worked on.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739547050.624009", "files": [{"id": "F08EB33F28G", "created": 1739797910, "timestamp": 1739797910, "name": "February Bonus Cycle - 2025-02-17.csv", "title": "February Bonus Cycle - 2025-02-17.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": true, "size": 102128, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08EB33F28G/february_bonus_cycle_-_2025-02-17.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08EB33F28G/download/february_bonus_cycle_-_2025-02-17.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08EB33F28G/february_bonus_cycle_-_2025-02-17.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08EB33F28G-901d61e39a", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08EB33F28G/february_bonus_cycle_-_2025-02-17.csv/edit", "preview": "Employee ID,Employee Name,Department,Manager Name,Hire Date,Proration Modifier,Current Salary (Local),Target Bonus (Local),Target Bonus Percentage,Target Company Performance (Local),Target Company Performance Percentage,Earned Company Performance (Local),Earned Company Performance Percentage,Target Individual Performance (Local),Target Individual Performance Percentage,Earned Individual Performance (Local),Earned Individual Performance Percentage,Bonus Award (Local),Bonus Award Percentage,Is Flagged\n16491,<PERSON>,Executive:<PERSON><PERSON>,Root Employee,1/1/2001,1.00,\"807,999.92\",\"1,413,999.86\",175.00%,\"565,599.94\",40.00%,\"565,599.94\",100.00%,\"848,399.92\",60.00%,\"848,399.92\",100.00%,\"848,399.92\",100.00%,No\n16515,<PERSON>,Human Resources:Bham:R,<PERSON>,5/20/2019,1.00,\"350,000.04\",\"437,500.05\",125.00%,\"175,000.02\",40.00%,\"175,000.02\",100.00%,\"262,500.03\",60.00%,\"262,500.03\",100.00%,\"262,500.03\",100.00%,No\n30758,<PERSON><PERSON>,BusinessDev:B<PERSON>:<PERSON>,<PERSON>,1/10/2024,0.98,\"350,000.04\",\"437,500.05\",125.00%...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Employee ID</div><div class=\"cm-col\">Employee Name</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Manager Name</div><div class=\"cm-col\">Hire Date</div><div class=\"cm-col\">Proration Modifier</div><div class=\"cm-col\">Current Salary (Local)</div><div class=\"cm-col\">Target Bonus (Local)</div><div class=\"cm-col\">Target Bonus Percentage</div><div class=\"cm-col\">Target Company Performance (Local)</div><div class=\"cm-col\">Target Company Performance Percentage</div><div class=\"cm-col\">Earned Company Performance (Local)</div><div class=\"cm-col\">Earned Company Performance Percentage</div><div class=\"cm-col\">Target Individual Performance (Local)</div><div class=\"cm-col\">Target Individual Performance Percentage</div><div class=\"cm-col\">Earned Individual Performance (Local)</div><div class=\"cm-col\">Earned Individual Performance Percentage</div><div class=\"cm-col\">Bonus Award (Local)</div><div class=\"cm-col\">Bonus Award Percentage</div><div class=\"cm-col\">Is Flagged</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">16491</div><div class=\"cm-col\">Robert Hutson</div><div class=\"cm-col\">Executive:Bham</div><div class=\"cm-col\">Root Employee</div><div class=\"cm-col\">1/1/2001</div><div class=\"cm-col cm-num\">1.00</div><div class=\"cm-col\">807,999.92</div><div class=\"cm-col\">1,413,999.86</div><div class=\"cm-col\">175.00%</div><div class=\"cm-col\">565,599.94</div><div class=\"cm-col\">40.00%</div><div class=\"cm-col\">565,599.94</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">848,399.92</div><div class=\"cm-col\">60.00%</div><div class=\"cm-col\">848,399.92</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">848,399.92</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">No</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">16515</div><div class=\"cm-col\">Mark Kirkendall</div><div class=\"cm-col\">Human Resources:Bham:R</div><div class=\"cm-col\">Robert Hutson</div><div class=\"cm-col\">5/20/2019</div><div class=\"cm-col cm-num\">1.00</div><div class=\"cm-col\">350,000.04</div><div class=\"cm-col\">437,500.05</div><div class=\"cm-col\">125.00%</div><div class=\"cm-col\">175,000.02</div><div class=\"cm-col\">40.00%</div><div class=\"cm-col\">175,000.02</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">262,500.03</div><div class=\"cm-col\">60.00%</div><div class=\"cm-col\">262,500.03</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">262,500.03</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">No</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">30758</div><div class=\"cm-col\">Michael Rigg</div><div class=\"cm-col\">BusinessDev:Bham:R</div><div class=\"cm-col\">Robert Hutson</div><div class=\"cm-col\">1/10/2024</div><div class=\"cm-col cm-num\">0.98</div><div class=\"cm-col\">350,000.04</div><div class=\"cm-col\">437,500.05</div><div class=\"cm-col\">125.00%</div><div class=\"cm-col\">175,000.02</div><div class=\"cm-col\">40.00%</div><div class=\"cm-col\">175,000.02</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">262,500.03</div><div class=\"cm-col\">60.00%</div><div class=\"cm-col\">262,500.03</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">257,250.03</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">No</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">16369</div><div class=\"cm-col\">Maverick Bentley</div><div class=\"cm-col\">Admin:Pike Midstream</div><div class=\"cm-col\">Robert Hutson</div><div class=\"cm-col\">1/24/2011</div><div class=\"cm-col cm-num\">1.00</div><div class=\"cm-col\">305,000.02</div><div class=\"cm-col\">228,750.02</div><div class=\"cm-col\">75.00%</div><div class=\"cm-col\">91,500.01</div><div class=\"cm-col\">40.00%</div><div class=\"cm-col\">91,500.01</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">137,250.01</div><div class=\"cm-col\">60.00%</div><div class=\"cm-col\">137,250.01</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">137,250.01</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">No</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">22396</div><div class=\"cm-col\">Ronald Ridgway</div><div class=\"cm-col\">MktEnrgySerGroup:Richmond</div><div class=\"cm-col\">Robert Hutson</div><div class=\"cm-col\">3/3/2021</div><div class=\"cm-col cm-num\">1.00</div><div class=\"cm-col\">350,000.04</div><div class=\"cm-col\">437,500.05</div><div class=\"cm-col\">125.00%</div><div class=\"cm-col\">175,000.02</div><div class=\"cm-col\">40.00%</div><div class=\"cm-col\">175,000.02</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">262,500.03</div><div class=\"cm-col\">60.00%</div><div class=\"cm-col\">262,500.03</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">262,500.03</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">No</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">16661</div><div class=\"cm-col\">Benjamin Sullivan</div><div class=\"cm-col\">Legal Services:Charleston</div><div class=\"cm-col\">Robert Hutson</div><div class=\"cm-col\">6/10/2019</div><div class=\"cm-col cm-num\">1.00</div><div class=\"cm-col\">449,999.94</div><div class=\"cm-col\">674,999.91</div><div class=\"cm-col\">150.00%</div><div class=\"cm-col\">269,999.96</div><div class=\"cm-col\">40.00%</div><div class=\"cm-col\">269,999.96</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">404,999.95</div><div class=\"cm-col\">60.00%</div><div class=\"cm-col\">404,999.95</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">404,999.95</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">No</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">16470</div><div class=\"cm-col\">Bradley Gray</div><div class=\"cm-col\">Executive:Bham</div><div class=\"cm-col\">Robert Hutson</div><div class=\"cm-col\">10/24/2016</div><div class=\"cm-col cm-num\">1.00</div><div class=\"cm-col\">500,000.02</div><div class=\"cm-col\">750,000.03</div><div class=\"cm-col\">150.00%</div><div class=\"cm-col\">300,000.01</div><div class=\"cm-col\">40.00%</div><div class=\"cm-col\">300,000.01</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">450,000.02</div><div class=\"cm-col\">60.00%</div><div class=\"cm-col\">450,000.02</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">450,000.02</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">No</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">22849</div><div class=\"cm-col\">Christopher Shaunfield</div><div class=\"cm-col\">InformationTech:Bham:R</div><div class=\"cm-col\">David Myers</div><div class=\"cm-col\">4/5/2021</div><div class=\"cm-col cm-num\">1.00</div><div class=\"cm-col\">250,956.94</div><div class=\"cm-col\">125,478.47</div><div class=\"cm-col\">50.00%</div><div class=\"cm-col\">50,191.39</div><div class=\"cm-col\">40.00%</div><div class=\"cm-col\">50,191.39</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">75,287.08</div><div class=\"cm-col\">60.00%</div><div class=\"cm-col\">74,534.21</div><div class=\"cm-col\">99.00%</div><div class=\"cm-col\">74,534.21</div><div class=\"cm-col\">99.00%</div><div class=\"cm-col\">No</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">30516</div><div class=\"cm-col\">Patty Reutter</div><div class=\"cm-col\">OT Apps:Charleston:R</div><div class=\"cm-col\">Nathaniel Bookwalter</div><div class=\"cm-col\">3/13/2023</div><div class=\"cm-col cm-num\">1.00</div><div class=\"cm-col\">121,065.10</div><div class=\"cm-col\">18,159.76</div><div class=\"cm-col\">15.00%</div><div class=\"cm-col\">7,263.90</div><div class=\"cm-col\">40.00%</div><div class=\"cm-col\">7,263.90</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">10,895.86</div><div class=\"cm-col\">60.00%</div><div class=\"cm-col\">10,895.86</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">10,895.86</div><div class=\"cm-col\">100.00%</div><div class=\"cm-col\">No</div></div></div>\n</div>\n", "lines": 509, "lines_more": 505, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "YkjuP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please let me know if this works. I think obvioulsy comments will be expected which is being worked on."}]}]}]}, {"ts": "1739798645.246419", "text": "% columns and isFlagged added in export report and this change is already live.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739547050.624009", "edited": {"user": "U0690EB5JE5", "ts": "1739798674.000000"}, "blocks": [{"type": "rich_text", "block_id": "JLj1Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "% columns and isFlagged added in export report and this change is already live."}]}]}]}], "created_at": "2025-05-22T22:00:49.364147"}