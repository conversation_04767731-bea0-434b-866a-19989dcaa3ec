{"date": "2024-09-23", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "**********.927899", "text": "<@U07EJ2LP44S> Curana Health Data sync update:\n• We are not receiving the comp info from sync \n• There are  total 2155 employees from sync \n• There are around 269 employees who do not have manager\n• There are 127 employees missing status \"Active\" or \"Inactive\" \nCould you please check with customer whats the criteria to pull data. Also request them to allow access to comp info.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.927899", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "bX1gt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Curana Health Data sync update:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We are not receiving the comp info from sync "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "There are  total 2155 employees from sync "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "There are around 269 employees who do not have manager"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "There are 127 employees missing status \"Active\" or \"Inactive\" "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Could you please check with customer whats the criteria to pull data. Also request them to allow access to comp info."}]}]}]}, {"ts": "**********.858729", "text": "<!here> I saw this note from <PERSON> in her transition doc. Should we remove budget from the view itself? We have pushed recently a change to show breakdown as well :slightly_smiling_face:", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.858729", "reply_count": 2, "edited": {"user": "U0690EB5JE5", "ts": "1727087725.000000"}, "files": [{"id": "F07P8E1GL00", "created": 1727086156, "timestamp": 1727086156, "name": "Screenshot 2024-09-23 at 3.38.19 PM.png", "title": "Screenshot 2024-09-23 at 3.38.19 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 267630, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07P8E1GL00/screenshot_2024-09-23_at_3.38.19___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07P8E1GL00/download/screenshot_2024-09-23_at_3.38.19___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 201, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 268, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 401, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 446, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 535, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8E1GL00-69e512ff7b/screenshot_2024-09-23_at_3.38.19___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 571, "original_w": 1604, "original_h": 894, "thumb_tiny": "AwAaADDSf7pqLBPY1NScZoAj2H0o2N6VJ3xRQBHtb0p6Agc9adRQAU3vTqKAE/ipaKKACiiigD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07P8E1GL00/screenshot_2024-09-23_at_3.38.19___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07P8E1GL00-adadbbf41e", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "jXhqU", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I saw this note from <PERSON> in her transition doc. Should we remove budget from the view itself? We have pushed recently a change to show breakdown as well "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1727084976.424709", "text": "Also the CEO was already super admin. Did you use reset mode by any chance to upload data?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727084976.424709", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "nwaS7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also the CEO was already super admin. Did you use reset mode by any chance to upload data?"}]}]}]}, {"ts": "1727084945.366729", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> Regarding CWA cycle creation issue. There are missing data\n• Job Category is missing for a lot employees\n• CEO is not super admin\nPlease fix these data issues you should be good to create cycle.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727084945.366729", "reply_count": 3, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Vr0gd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Regarding CWA cycle creation issue. There are missing data\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Category is missing for a lot employees"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "CEO is not super admin"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Please fix these data issues you should be good to create cycle."}]}]}]}, {"ts": "1727070502.869149", "text": "<!here> Today would like review all customer requirements and their priorities. In the stand up.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1727070513.000000"}, "blocks": [{"type": "rich_text", "block_id": "rW+vX", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Today would like review all customer requirements and their priorities. In the stand up."}]}]}]}], "created_at": "2025-05-22T21:35:34.637040"}