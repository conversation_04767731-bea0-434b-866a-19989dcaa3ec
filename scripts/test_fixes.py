#!/usr/bin/env python3
"""
Test script to verify the fixes for the 3 issues:
1. Search query "whats latest on curana?" yields no results
2. HuggingFace is initialized every query
3. Citations have issues
"""

import os, sys, django
sys.path.append('./multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

import time
from apps.search.services.rag_service import RAGService
from apps.accounts.models import Tenant
from django.contrib.auth.models import User

def test_fix_1_curana_search():
    """Test Fix 1: Improved search for 'whats latest on curana?'"""
    print("🔍 Testing Fix 1: 'whats latest on curana?' search...")
    
    user = User.objects.get(username='testuser')
    tenant = Tenant.objects.get(slug='stride')
    
    try:
        start_time = time.time()
        
        rag_service = RAGService(user=user, tenant_slug=tenant.slug)
        search_result, retrieved_docs = rag_service.search(
            query_text="whats latest on curana?",
            top_k=20,
            min_relevance_score=0.1,  # Lower threshold for temporal queries
            use_hybrid_search=True,
            use_context_aware=True
        )
        
        end_time = time.time()
        search_time = end_time - start_time
        
        print(f"   ⏱️  Search completed in {search_time:.2f}s")
        print(f"   📊 Retrieved docs: {len(retrieved_docs) if retrieved_docs else 0}")
        print(f"   📊 Citations: {search_result.citations.count() if search_result else 0}")
        
        if search_result:
            print(f"   📝 Generated answer length: {len(search_result.generated_answer)}")
            answer_preview = search_result.generated_answer[:300].replace('\n', ' ')
            print(f"   📝 Answer preview: {answer_preview}...")
            
            # Check if the answer mentions Curana
            if 'curana' in search_result.generated_answer.lower():
                print("   ✅ Answer contains Curana content!")
                return True
            else:
                print("   ❌ Answer does not mention Curana")
                return False
        else:
            print("   ❌ No search result generated")
            return False
            
    except Exception as e:
        print(f"   ❌ Search failed: {e}")
        return False

def test_fix_2_embedding_performance():
    """Test Fix 2: Embedding model caching and performance"""
    print("\n🚀 Testing Fix 2: Embedding model performance...")
    
    user = User.objects.get(username='testuser')
    tenant = Tenant.objects.get(slug='stride')
    
    # Test multiple searches to measure performance improvement
    queries = [
        "engineering team updates",
        "product development status", 
        "recent slack discussions"
    ]
    
    total_time = 0
    for i, query in enumerate(queries, 1):
        print(f"\n   🔎 Search {i}: '{query}'")
        start_time = time.time()
        
        try:
            rag_service = RAGService(user=user, tenant_slug=tenant.slug)
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=10,
                min_relevance_score=0.2
            )
            
            end_time = time.time()
            search_time = end_time - start_time
            total_time += search_time
            
            print(f"   ⏱️  Search {i} time: {search_time:.2f}s")
            
            # Check if subsequent searches are faster (cached models)
            if i > 1 and search_time < 30:  # Should be much faster after first load
                print(f"   ✅ Search {i} is faster (cached models working)")
            elif i == 1:
                print(f"   📊 First search baseline: {search_time:.2f}s")
            else:
                print(f"   ⚠️  Search {i} still slow: {search_time:.2f}s")
                
        except Exception as e:
            print(f"   ❌ Search {i} failed: {e}")
    
    avg_time = total_time / len(queries)
    print(f"\n   📊 Average search time: {avg_time:.2f}s")
    
    # Performance should be good if average is under 20s
    if avg_time < 20:
        print("   ✅ Performance improved - embedding caching working!")
        return True
    else:
        print("   ⚠️  Performance still needs improvement")
        return False

def test_fix_3_citations():
    """Test Fix 3: Citations functionality"""
    print("\n📚 Testing Fix 3: Citations functionality...")
    
    user = User.objects.get(username='testuser')
    tenant = Tenant.objects.get(slug='stride')
    
    try:
        rag_service = RAGService(user=user, tenant_slug=tenant.slug)
        search_result, retrieved_docs = rag_service.search(
            query_text="engineering team discussions about curana",
            top_k=15,
            min_relevance_score=0.15
        )
        
        if search_result:
            citations = search_result.citations.all()
            print(f"   📊 Total citations: {citations.count()}")
            
            if citations.count() > 0:
                print("   ✅ Citations are being created")
                
                # Check citation quality
                valid_citations = 0
                for citation in citations[:3]:
                    print(f"\n   📖 Citation {citation.rank}:")
                    print(f"      Relevance: {citation.relevance_score:.3f}")
                    
                    if citation.document_chunk:
                        chunk = citation.document_chunk
                        print(f"      Chunk ID: {chunk.id}")
                        
                        if chunk.document:
                            doc = chunk.document
                            print(f"      Document: {doc.title}")
                            print(f"      Source: {doc.source.name if doc.source else 'No source'}")
                            
                            if doc.permalink:
                                print(f"      Permalink: {doc.permalink}")
                                valid_citations += 1
                            else:
                                print("      ⚠️  No permalink")
                        else:
                            print("      ❌ No document linked")
                    else:
                        print("      ❌ No chunk linked")
                
                if valid_citations > 0:
                    print(f"   ✅ {valid_citations} valid citations with permalinks")
                    return True
                else:
                    print("   ❌ No valid citations found")
                    return False
            else:
                print("   ❌ No citations created")
                return False
        else:
            print("   ❌ No search result to analyze")
            return False
            
    except Exception as e:
        print(f"   ❌ Citations test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Testing Fixes for RAG Search Issues")
    print("=" * 50)
    
    # Test Fix 1: Curana search
    fix1_success = test_fix_1_curana_search()
    
    # Test Fix 2: Embedding performance
    fix2_success = test_fix_2_embedding_performance()
    
    # Test Fix 3: Citations
    fix3_success = test_fix_3_citations()
    
    print("\n" + "=" * 50)
    print("📊 Fix Test Results:")
    print(f"   Fix 1 (Curana Search): {'✅ FIXED' if fix1_success else '❌ NEEDS WORK'}")
    print(f"   Fix 2 (Embedding Performance): {'✅ FIXED' if fix2_success else '❌ NEEDS WORK'}")
    print(f"   Fix 3 (Citations): {'✅ WORKING' if fix3_success else '❌ NEEDS WORK'}")
    
    if fix1_success and fix2_success and fix3_success:
        print("\n🎉 All fixes are working correctly!")
    else:
        print("\n⚠️  Some issues still need attention.")

if __name__ == "__main__":
    main()
