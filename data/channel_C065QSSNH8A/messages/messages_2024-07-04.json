{"date": "2024-07-04", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1720116535.110889", "text": "<@U04DS2MBWP4> maintenance is complete and vercara folks can login back in the environment. \n\nThey will need to use new domain going forward\n\n<http://vercara.stridehr.io|vercara.stridehr.io>\n\nNo changes to their creds", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FHTQa", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " maintenance is complete and vercara folks can login back in the environment. \n\nThey will need to use new domain going forward\n\n"}, {"type": "link", "url": "http://vercara.stridehr.io", "text": "vercara.stridehr.io"}, {"type": "text", "text": "\n\nNo changes to their creds"}]}]}]}, {"ts": "1720068597.225069", "text": "<!here> are updates from testing for today\nEnvironment used: Local\nBuild: Latest version of branch \"cwa-perf-rating\"\nTested both numeric and text based performance rating ( numeric rating with cainwatters dataset and text based rating with staging dataset)\nSimilar to yesterday. I was\n• Able to upload Performance Ratings in decimal as well as text format.\n• Able to configure performance rating settings before cycle create.\n• Able to set recommendation guidelines for performance ratings, including both range and target recommendations for merit increases and market adjustments in the comp cycle builder.\n• Able to view assigned numerical and text based ratings and corresponding recommendation guidelines in the merit view.\n• Able to view numerical and text based performance ratings in exports and reports.\n• Performance rating filter is working as well in merit view\n<@U0690EB5JE5> I copied my db dump here <https://drive.google.com/drive/folders/1S4L7pGZsxDDNdFDSJk6_qn78CUnCj1zW?usp=drive_link>\n\nI was able to see correct budget number ( salary and market adjustment ) for full organization and manager as well\nRaised: <https://compiify.atlassian.net/browse/COM-3406>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1720068597.225069", "reply_count": 6, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13467::dd86123039c011efadf13b438d3d9900", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3406?atlOrigin=eyJpIjoiNmMzODcxZTI2OWJmNGMwZjk1MGQyOTdhYWFmN2EzNGMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3406 Unable to view all users on Settings page user list>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13467::dd86394139c011efadf13b438d3d9900", "elements": [{"type": "mrkdwn", "text": "Status: *In Development*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13467::dd86394039c011efadf13b438d3d9900", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13467\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13467\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3406", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "tQ3Sf", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " are updates from testing for today\nEnvironment used: Local\nBuild: Latest version of branch \"cwa-perf-rating\"\nTested both numeric and text based performance rating ( numeric rating with cainwatters dataset and text based rating with staging dataset)\nSimilar to yesterday. I was\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Able to upload Performance Ratings in decimal as well as text format."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Able to configure performance rating settings before cycle create."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Able to set recommendation guidelines for performance ratings, including both range and target recommendations for merit increases and market adjustments in the comp cycle builder."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Able to view assigned numerical and text based ratings and corresponding recommendation guidelines in the merit view."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Able to view numerical and text based performance ratings in exports and reports."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Performance rating filter is working as well in merit view"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I copied my db dump here "}, {"type": "link", "url": "https://drive.google.com/drive/folders/1S4L7pGZsxDDNdFDSJk6_qn78CUnCj1zW?usp=drive_link"}, {"type": "text", "text": "\n\nI was able to see correct budget number ( salary and market adjustment ) for full organization and manager as well\nRaised: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3406"}]}]}]}, {"ts": "1720053000.388629", "text": "<@U0690EB5JE5> lets keep the eng call tomorrow. We should go over all of the jira tickets for customers to get clarity on what needs to be done and the timeline.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "EGYaI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " lets keep the eng call tomorrow. We should go over all of the jira tickets for customers to get clarity on what needs to be done and the timeline."}]}]}]}, {"ts": "1720041352.737859", "text": "ok", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1720041298.355879", "text": "ok. how long the maintenance will last", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1720041298.355879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "G/GCp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok. how long the maintenance will last"}]}]}]}, {"ts": "1720041143.293459", "text": "<@U04DS2MBWP4> I will be doing maintenance on sandbox environments after 5pm today. It might be handy to at-least inform vercara about this maintenance since they are using sandbox , we can skip other customers.", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "done", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Q2HHP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I will be doing maintenance on sandbox environments after 5pm today. It might be handy to at-least inform vercara about this maintenance since they are using sandbox , we can skip other customers."}]}]}]}, {"ts": "1720031558.818179", "text": "FYI, we will be completing the new design of the comp builder by end of next week", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1720031558.818179", "reply_count": 1, "reactions": [{"name": "eyes", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "PWsx2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "FYI, we will be completing the new design of the comp builder by end of next week"}]}]}]}], "created_at": "2025-05-22T21:35:34.621690"}