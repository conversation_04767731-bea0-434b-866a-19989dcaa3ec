{"date": "2025-02-26", "channel_id": "C065QSSNH8A", "message_count": 21, "messages": [{"ts": "1740584641.150179", "text": "I guess we did UI deployment and that would sometimes cause this issue. clearing the cookies or reloading it multiple times would solve the problem.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QC3qU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I guess we did UI deployment and that would sometimes cause this issue. clearing the cookies or reloading it multiple times would solve the problem."}]}]}]}, {"ts": "**********.170459", "text": "<@U07EJ2LP44S> Looks like user was able to login and We haven't made any changes that would impact login. Could you please ask user to clear cookies and try with the actual url `<https://curanahealthmip.stridehr.io/>` `<https://curanahealth.stridehr.io/>`", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.713999", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "FjsH9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Looks like user was able to login and We haven't made any changes that would impact login. Could you please ask user to clear cookies and try with the actual url "}, {"type": "link", "url": "https://curanahealthmip.stridehr.io/", "style": {"code": true}}, {"type": "text", "text": " "}, {"type": "link", "url": "https://curanahealth.stridehr.io/", "style": {"code": true}}]}]}]}, {"ts": "**********.330099", "text": "<@U07EJ2LP44S> This is done.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.519829", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "NpIa6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is done."}]}]}]}, {"ts": "**********.587619", "text": "<@U0690EB5JE5> We need to remove another last raise date for <PERSON> in the Curana main account. I thought about just manually overriding it by including her in the cycle manually, but I'm a little concerned that republishing will rock the boat. Can you remove her last raise date completely?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.587619", "reply_count": 15, "blocks": [{"type": "rich_text", "block_id": "YfP8/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We need to remove another last raise date for <PERSON> in the Curana main account. I thought about just manually overriding it by including her in the cycle manually, but I'm a little concerned that republishing will rock the boat. Can you remove her last raise date completely?"}]}]}]}, {"ts": "**********.116679", "text": "<@U0690EB5JE5> I copied you into the email thread. Can you help? this is the SSO thing with <PERSON>", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XvsEo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I copied you into the email thread. Can you help? this is the SSO thing with <PERSON>"}]}]}]}, {"ts": "**********.414759", "text": "I will have update the email in case the one in the system is not the correct one ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GhNuV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will have update the email in case the one in the system is not the correct one "}]}]}]}, {"ts": "1740580799.092289", "text": "No it’s definitely email issue then", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "2CQ2M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No "}, {"type": "text", "text": "it’s"}, {"type": "text", "text": " definitely email issue then"}]}]}]}, {"ts": "**********.713999", "text": "<@U0690EB5JE5> we have one person at curana unable to login via sso. He keeps refreshing back to a blank screen, which is strange. <PERSON>. I did notice his email in our system is timothy.puri, and his email he's sending from is tim.puri, so I asked them to check that. But is there anything else on our end?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.713999", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "NWIfZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we have one person at curana unable to login via sso. He keeps refreshing back to a blank screen, which is strange. <PERSON>. I did notice his email in our system is timothy.puri, and his email he's sending from is tim.puri, so I asked them to check that. But is there anything else on our end?"}]}]}]}, {"ts": "1740580479.217569", "text": "<PERSON> will share the letters tomorrow with the feedback addressed", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "kZYyp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> will share the letters tomorrow with the feedback addressed"}]}]}]}, {"ts": "1740580446.530429", "text": "<@U0690EB5JE5> Valgenesis is ready for letters! If those changes are done from their feedback we should be good.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740580446.530429", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "PZ5uq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Valgenesis is ready for letters! If those changes are done from their feedback we should be good."}]}]}]}, {"ts": "1740568321.488139", "text": "<@U07EJ2LP44S> We have addressed the feedback and updated the ENV with data shared above. There are some minor nitpicks we are addressing but should be good to share with customer for initial feedback. We may have to update the data gain based on the answers to the questions above.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "subtype": "thread_broadcast", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "KyIvM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We have addressed the feedback and updated the ENV with data shared above. There are some minor nitpicks we are addressing but should be good to share with customer for initial feedback. We may have to update the data gain based on the answers to the questions above."}]}]}]}, {"ts": "1740565974.454709", "text": "Please confirm on Curana updates.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "s2cBE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please confirm on Curana updates."}]}]}]}, {"ts": "1740565946.104409", "text": "<@U07EJ2LP44S> All data updates are taken care except for <PERSON><PERSON><PERSON>'s one employee termination.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lI8GC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " All data updates are taken care except for <PERSON><PERSON><PERSON>'s one employee termination."}]}]}]}, {"ts": "1740547477.180219", "text": "cc: <@U07MH77PUBV>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "j4uwO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "cc: "}, {"type": "user", "user_id": "U07MH77PUBV"}]}]}]}, {"ts": "1740547180.129609", "text": "<@U07EJ2LP44S> Couple of questions with the data\n• We have only employer values. Should we show only employer contributions?\n• And the amounts are \"monthly\"/ \"weekly\"/ Half yearly  (looks like annual though just need confirmation? Do we need to show annual numbers or as is from the file?\n• Also should we include internet stipend as \"Other Cateogry?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "/K6j+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Couple of questions with the data\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We have only employer values. Should we show only employer contributions?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "And the amounts are \"monthly\"/ \"weekly\"/ Half yearly  (looks like annual though just need confirmation? Do we need to show annual numbers or as is from the file?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Also should we include internet stipend as \"Other Cateogry?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1740543459.260599", "text": "All the employees except \"*<PERSON>\" deleted. This employee is a manager and has two reportees. System will allow only after update manager of the reporting employees.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.519829", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "PGDjZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All the employees except \""}, {"type": "text", "text": "<PERSON>", "style": {"bold": true}}, {"type": "text", "text": "\" deleted. This employee is a manager and has two reportees. System will allow only after update manager of the reporting employees."}]}]}]}, {"ts": "**********.185639", "text": "Will take care ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EjPOt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take care "}]}]}]}, {"ts": "**********.455499", "text": "One more - <PERSON><PERSON><PERSON> just sent me this file. These are employees that need to be added to the account. The ones in red we can skip as they aren't elibile. I just got this and cannot format tonight. Sending over for your help! This is the main account.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.455499", "reply_count": 1, "files": [{"id": "F08F36DPZR8", "created": **********, "timestamp": **********, "name": "Add_Employees_Curana-Stride 01.23 (1).xlsx", "title": "Add_Employees_Curana-Stride 01.23 (1).xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 1513730, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F36DPZR8/add_employees_curana-stride_01.23__1_.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F36DPZR8/download/add_employees_curana-stride_01.23__1_.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F36DPZR8-d2997504fe/add_employees_curana-stride_01.23__1__converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F36DPZR8-d2997504fe/add_employees_curana-stride_01.23__1__thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F36DPZR8/add_employees_curana-stride_01.23__1_.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F36DPZR8-758683ddd8", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ooMVU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "One more - <PERSON><PERSON><PERSON> just sent me this file. These are employees that need to be added to the account. The ones in red we can skip as they aren't elibile. I just got this and cannot format tonight. Sending over for your help! This is the main account."}]}]}]}, {"ts": "**********.494759", "text": "Sure <@U07EJ2LP44S> will take care of all the data updates today.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "d2tnE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " will take care of all the data updates today."}]}]}]}, {"ts": "**********.383749", "text": "<@U0690EB5JE5> Here's an updated file for Curana MIP only. I'm attaching directly what he sent me; I'm not sure what rows are updated but I know it was 50+ people getting their salary and bonus $ changed.", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F08F4HD160L", "created": **********, "timestamp": **********, "name": "Curana Health_EmployeeDataTemplate_202502251506_utc.xlsx", "title": "Curana Health_EmployeeDataTemplate_202502251506_utc.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 112398, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F4HD160L/curana_health_employeedatatemplate_202502251506_utc.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F4HD160L/download/curana_health_employeedatatemplate_202502251506_utc.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4HD160L-914c1925e3/curana_health_employeedatatemplate_202502251506_utc_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4HD160L-914c1925e3/curana_health_employeedatatemplate_202502251506_utc_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F4HD160L/curana_health_employeedatatemplate_202502251506_utc.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F4HD160L-eb3378b1d6", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "a/52+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Here's an updated file for Curana MIP only. I'm attaching directly what he sent me; I'm not sure what rows are updated but I know it was 50+ people getting their salary and bonus $ changed."}]}]}]}, {"ts": "**********.947339", "text": "We need to remove the last raise date for <PERSON><PERSON> in the primary Curana account. Can you upload this <@U0690EB5JE5>  It doesn't look like I can change it in the application.", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F08F2DYKCBU", "created": **********, "timestamp": **********, "name": "PlowmanDeleteLastRaiseDate.csv", "title": "PlowmanDeleteLastRaiseDate.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1550, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F2DYKCBU/plowmandeletelastraisedate.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F2DYKCBU/download/plowmandeletelastraisedate.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F2DYKCBU/plowmandeletelastraisedate.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F2DYKCBU-d3c9a43f98", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F2DYKCBU/plowmandeletelastraisedate.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U </div><div class=\"cm-col cm-num\">2994</div><div class=\"cm-col\">KAYLEE</div><div class=\"cm-col\">PLOWMAN</div><div class=\"cm-col\">KAYLEE PLOWMAN</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">1/30/23</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">8122</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/30/23</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">TN</div><div class=\"cm-col\">Marketing Coordinator</div><div class=\"cm-col\">Marketing</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Shared Services</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">50000.08</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 2, "lines_more": 1, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "yLGwI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We need to remove the last raise date for <PERSON><PERSON> in the primary Curana account. Can you upload this "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "  It doesn't look like I can change it in the application."}]}]}]}], "created_at": "2025-05-22T21:35:34.709434"}