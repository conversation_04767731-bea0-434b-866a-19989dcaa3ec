{"thread_ts": "1739811374.839069", "channel_id": "C065QSSNH8A", "reply_count": 8, "replies": [{"ts": "1739811485.612599", "text": "<@U07EJ2LP44S> how is this uploaded via Org edit or csv upload?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739811374.839069", "blocks": [{"type": "rich_text", "block_id": "gTlGk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " how is this uploaded via Org edit or csv upload?"}]}]}]}, {"ts": "1739811517.948619", "text": "She updated it either in org view or manually in the cycle, i'm not sure which. But not via upload.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739811374.839069", "blocks": [{"type": "rich_text", "block_id": "c7TDR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She updated it either in org view or manually in the cycle, i'm not sure which. But not via upload."}]}]}]}, {"ts": "1739811598.154529", "text": "I think upload via CSV has worked so far. Share me what needs to updated. I will check.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739811374.839069", "blocks": [{"type": "rich_text", "block_id": "5j2T9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think upload via CSV has worked so far. Share me what needs to updated. I will check."}]}]}]}, {"ts": "1739811613.732289", "text": "will look into org view tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739811374.839069", "blocks": [{"type": "rich_text", "block_id": "gQK23", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will look into org view tomorrow."}]}]}]}, {"ts": "1739811668.392099", "text": "He needs to be at 75% achievement of individual target.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739811374.839069", "files": [{"id": "F08DPBP6799", "created": 1739811666, "timestamp": 1739811666, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 5028, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DPBP6799/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DPBP6799/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPBP6799-875f1a8d59/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPBP6799-875f1a8d59/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPBP6799-875f1a8d59/image_360.png", "thumb_360_w": 326, "thumb_360_h": 89, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPBP6799-875f1a8d59/image_160.png", "original_w": 326, "original_h": 89, "thumb_tiny": "AwANADDQYsDxn8s0mW9W/wC+aftBOcc0tADM5GCpP4U4AdQMUtFACMcKfWmqTkZJ/EU5lDDBFII1ByBzU+9cD//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DPBP6799/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DPBP6799-b360071d55", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "MzHYU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "He needs to be at 75% achievement of individual target."}]}]}]}, {"ts": "1739811706.896789", "text": "This was the only employee that we didn't do via CSV b/c they didn't have the rating yet (employee was on leave) so shouldn't be an issue for other employees", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739811374.839069", "blocks": [{"type": "rich_text", "block_id": "2UJaA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This was the only employee that we didn't do via CSV b/c they didn't have the rating yet (employee was on leave) so shouldn't be an issue for other employees"}]}]}]}, {"ts": "1739815260.218969", "text": "<@U07EJ2LP44S> This I need to dig little bit. Will get this fixed by tomorrow.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739811374.839069", "blocks": [{"type": "rich_text", "block_id": "bXF3T", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This I need to dig little bit. Will get this fixed by tomorrow."}]}]}]}, {"ts": "1739815794.981159", "text": "<@U07EJ2LP44S> I have mitigated it from backend,", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739811374.839069", "blocks": [{"type": "rich_text", "block_id": "sb7ua", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have mitigated it from backend,"}]}]}]}], "created_at": "2025-05-22T22:00:49.363460"}