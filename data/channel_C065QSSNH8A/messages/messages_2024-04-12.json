{"date": "2024-04-12", "channel_id": "C065QSSNH8A", "message_count": 8, "messages": [{"ts": "1712935752.270409", "text": "I am good with Friday", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "wKPuM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am good with Friday"}]}]}]}, {"ts": "1712935739.394989", "text": "I can move it to Monday if required.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "La9QA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can move it to Monday if required."}]}]}]}, {"ts": "1712935738.798839", "text": "ok", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1712935709.624899", "text": "<@U04DS2MBWP4> that was only last week's instance.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "k5Loi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " that was only last week's instance."}]}]}]}, {"ts": "1712935678.644319", "text": "<@U0690EB5JE5> I think you were going to move the Friday's weekly demo meetings to a different day so that <PERSON><PERSON><PERSON><PERSON> can also join. Is it still happening today?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nI+ps", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I think you were going to move the Friday's weekly demo meetings to a different day so that <PERSON><PERSON><PERSON><PERSON> can also join. Is it still happening today?"}]}]}]}, {"ts": "1712883236.136299", "text": "<!here> all the UAT tasks have been linked under a parent jira <https://compiify.atlassian.net/browse/COM-2695>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12756::24660610f86711ee98bdadf1b74e41eb", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2695?atlOrigin=eyJpIjoiN2Q4ODJkNTA1NWFhNDY0MmE0Yzc3MDBiMzU1NjcyNjUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2695 Customer UAT task list>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12756::24662d21f86711ee98bdadf1b74e41eb", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12756::24662d20f86711ee98bdadf1b74e41eb", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12756\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12756\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2695", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "raDP5", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " all the UAT tasks have been linked under a parent jira "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2695"}]}]}]}, {"ts": "1712876044.497979", "text": "Per the discussion earlier today, I pinged both our contacts at Rightway to see if they're able to get us connected to their ADP admin sooner. I got an autoreply saying <PERSON> is on maternity leave already, so it's a low likelihood but I did copy <PERSON><PERSON><PERSON> on her team as well.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1712876044.497979", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "QaG/n", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Per the discussion earlier today, I pinged both our contacts at Rightway to see if they're able to get us connected to their ADP admin sooner. I got an autoreply saying <PERSON> is on maternity leave already, so it's a low likelihood but I did copy <PERSON><PERSON><PERSON> on her team as well."}]}]}]}, {"ts": "1712872862.169409", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> Nauto uses Namely and according to <https://help.merge.dev/en/articles/5758382-integration-onboarding-characterization|this Merge page> it seems like this might be one of the simpler authentication types. Would there be a way for us to provide Nauto with the newer integration page and see if they can connect? (Is there any risk of it wiping out any of their current data/settings unintentionally?)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1712872862.169409", "reply_count": 7, "attachments": [{"image_url": "https://downloads.intercomcdn.com/i/o/379082/107841514fe57a3f0b2a855a/065e08fde79f8a1ee25eb6318655bdc9.jpg", "image_width": 1200, "image_height": 628, "image_bytes": 74161, "from_url": "https://help.merge.dev/en/articles/5758382-integration-onboarding-characterization", "id": 1, "original_url": "https://help.merge.dev/en/articles/5758382-integration-onboarding-characterization", "fallback": "Integration onboarding characterization | Merge Help Center", "text": "Helpful guidance on our offered integrations", "title": "Integration onboarding characterization | Merge Help Center", "title_link": "https://help.merge.dev/en/articles/5758382-integration-onboarding-characterization", "service_name": "help.merge.dev"}], "blocks": [{"type": "rich_text", "block_id": "Gk7uF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " <PERSON><PERSON> uses Namely and according to "}, {"type": "link", "url": "https://help.merge.dev/en/articles/5758382-integration-onboarding-characterization", "text": "this Merge page"}, {"type": "text", "text": " it seems like this might be one of the simpler authentication types. Would there be a way for us to provide Nauto with the newer integration page and see if they can connect? (Is there any risk of it wiping out any of their current data/settings unintentionally?)"}]}]}]}], "created_at": "2025-05-22T21:35:34.605240"}