#!/usr/bin/env python
"""
Debug the initialization order to find where the wrong model is being set.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from django.conf import settings
from apps.core.utils.embedding_consistency import EmbeddingModelConfig, get_consistent_embedding_model, reset_embedding_model

def debug_initialization_order():
    """Debug the initialization order."""
    print("🔍 Debugging Initialization Order")
    print("=" * 60)
    
    # Check environment variables
    print("📊 Environment Variables:")
    print(f"   EMBEDDING_MODEL_NAME: {os.environ.get('EMBEDDING_MODEL_NAME', 'Not set')}")
    
    # Check Django settings
    print(f"\n📊 Django Settings:")
    print(f"   EMBEDDING_MODEL_NAME: {getattr(settings, 'EMBEDDING_MODEL_NAME', 'Not set')}")
    
    # Check EmbeddingModelConfig
    print(f"\n📊 EmbeddingModelConfig:")
    model_name, dimensions = EmbeddingModelConfig.get_model_config()
    print(f"   Model name: {model_name}")
    print(f"   Dimensions: {dimensions}")
    
    # Reset and test initialization
    print(f"\n🔄 Resetting and testing initialization:")
    reset_embedding_model()
    
    # Test get_consistent_embedding_model step by step
    print(f"\n🔧 Testing get_consistent_embedding_model():")
    try:
        model = get_consistent_embedding_model()
        print(f"   ✅ Model created: {type(model).__name__}")
        
        # Check model attributes
        if hasattr(model, 'model_name'):
            print(f"   Model name: {model.model_name}")
        if hasattr(model, '_model'):
            print(f"   Underlying model: {model._model}")
        if hasattr(model, '_model_name'):
            print(f"   Model name attr: {model._model_name}")
            
        # Test embedding
        try:
            test_embedding = model.get_text_embedding("test")
            print(f"   ✅ Test embedding dimensions: {len(test_embedding)}")
        except Exception as e:
            print(f"   ❌ Test embedding failed: {e}")
            
    except Exception as e:
        print(f"   ❌ Model creation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_initialization_order()
