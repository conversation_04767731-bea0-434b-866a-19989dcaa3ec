{"date": "2025-02-17", "channel_id": "C065QSSNH8A", "message_count": 8, "messages": [{"ts": "**********.879759", "text": "<@U0690EB5JE5> Can the <http://test.striderhr.io|test.striderhr.io> and stridedemo accounts currency be switched to USD? they are defaulted to canada and it doesn't look like i can edit that in the settings.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.879759", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "/66N4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can the "}, {"type": "link", "url": "http://test.striderhr.io", "text": "test.striderhr.io"}, {"type": "text", "text": " and stridedemo accounts currency be switched to USD? they are defaulted to canada and it doesn't look like i can edit that in the settings."}]}]}]}, {"ts": "**********.839069", "text": "Diversified: this employee's earned % had to be added after the cycle started and the numbers are not adding up:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.839069", "reply_count": 8, "files": [{"id": "F08DPATEMLK", "created": **********, "timestamp": **********, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 34731, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DPATEMLK/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DPATEMLK/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_360.png", "thumb_360_w": 360, "thumb_360_h": 22, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_480.png", "thumb_480_w": 480, "thumb_480_h": 30, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_720.png", "thumb_720_w": 720, "thumb_720_h": 45, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_800.png", "thumb_800_w": 800, "thumb_800_h": 49, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_960.png", "thumb_960_w": 960, "thumb_960_h": 59, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 63, "original_w": 2134, "original_h": 132, "thumb_tiny": "AwACADDRHWl7GkHU0djQAvajtR2o7UAHajvR2pO9AH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DPATEMLK/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DPATEMLK-faac02d4ad", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "CNFLl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified: this employee's earned % had to be added after the cycle started and the numbers are not adding up:"}]}]}]}, {"ts": "1739810189.626809", "text": "<@U0690EB5JE5> <PERSON> at Diversified is unable to view his own team in the tasks page. He is a SuperAdmin and a recommender with 15 directs. He is also the final approver.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739810189.626809", "reply_count": 3, "edited": {"user": "U07EJ2LP44S", "ts": "1739810235.000000"}, "files": [{"id": "F08DP82V5QT", "created": 1739810187, "timestamp": 1739810187, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 26724, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DP82V5QT/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DP82V5QT/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_360.png", "thumb_360_w": 360, "thumb_360_h": 68, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_480.png", "thumb_480_w": 480, "thumb_480_h": 91, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_720.png", "thumb_720_w": 720, "thumb_720_h": 137, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_800.png", "thumb_800_w": 800, "thumb_800_h": 152, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_960.png", "thumb_960_w": 960, "thumb_960_h": 182, "original_w": 1001, "original_h": 190, "thumb_tiny": "AwAJADDROKOMdKdRQAgxjigHNLRQAUUUUAf/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DP82V5QT/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DP82V5QT-36a65ab44d", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "oB6Lb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON> at Diversified is unable to view his own team in the tasks page. He is a SuperAdmin and a recommender with 15 directs. He is also the final approver."}]}]}]}, {"ts": "1739806308.164669", "text": "<@U0690EB5JE5> Can you delete the existing cycle in stridedemo?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739806308.164669", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "rOo6R", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you delete the existing cycle in stridedemo?"}]}]}]}, {"ts": "1739767062.393409", "text": "I think they want to check something they will log in. the only reason we are disabling environments is because we want to save on the cloud costs, but it is not a standard practice to do so", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1739767062.393409", "reply_count": 2, "edited": {"user": "U07M6QKHUC9", "ts": "1739767074.000000"}, "blocks": [{"type": "rich_text", "block_id": "lI1PL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think they want to check something they will log in. the only reason we are disabling environments is because we want to save on the cloud costs, but it is not a standard "}, {"type": "text", "text": "practice "}, {"type": "text", "text": "to do so"}]}]}]}, {"ts": "1739766717.917229", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> Dgoc is back online. I will not stop the ENV going forward. Just curious, Why are they still logging in after cycle is closed? I am just wondering about the use case.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1739766727.000000"}, "blocks": [{"type": "rich_text", "block_id": "BNlVN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Dgoc is back online. I will not stop the ENV going forward. Just curious, Why are they still logging in after cycle is closed? I am just wondering about the use case."}]}]}]}, {"ts": "1739766422.713809", "text": "<@U07EJ2LP44S> Recommenders should be able to SSO if <PERSON> was. Could you please confirm the same with them?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739468020.144469", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "5qJ71", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Recommenders should be able to SSO if <PERSON> was. Could you please confirm the same with them?"}]}]}]}, {"ts": "1739766363.866509", "text": "<@U07EJ2LP44S> We are targeting to wrap up the Total Rewards thing by end of this week. One question I have is, After incorporating their feedback, This pretty much covers their adjustment letters. Could you please check if they are still expecting adjustment letters separately? which I feel is redundant. Can we convince them to use Total rewards instead of adjustment letters.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739474866.025449", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1739766493.000000"}, "blocks": [{"type": "rich_text", "block_id": "3oIqH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We are targeting to wrap up the Total Rewards thing by end of this week. One question I have is, After incorporating their feedback, This pretty much covers their adjustment letters. Could you please check if they are still expecting adjustment letters separately? which I feel is redundant. Can we convince them to use Total rewards instead of adjustment letters."}]}]}]}], "created_at": "2025-05-22T21:35:34.712989"}