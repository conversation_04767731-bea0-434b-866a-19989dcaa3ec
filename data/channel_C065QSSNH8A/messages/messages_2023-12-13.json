{"date": "2023-12-13", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1702491401.506729", "text": "Hey team, I have to hop at 11:30 because of a meeting that popped up at Affirm. any flexibility on the call today? Happy to do another half hour or more later on", "user": "U0658EW4B8D", "type": "message", "thread_ts": "1702491401.506729", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Ylz69", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey team, I have to hop at 11:30 because of a meeting that popped up at Affirm. any flexibility on the call today? Happy to do another half hour or more later on"}]}]}]}, {"ts": "1702490786.561259", "text": "Love it. I can do it", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "keanu_thanks", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "oz3kn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Love it. I can do it"}]}]}]}, {"ts": "1702490700.752529", "text": "Hey y'all. For our working session, I would ideally like to try a test run through a merit planning workflow, and even to record that for later reference.\n• Maybe we can have <@U0658EW4B8D> be a guinea pig and see if he can complete merit &amp; bonus recommendations for \"direct reports\" without getting stuck\n• And, I want to see how a leader or executive with multiple managers would see the effects of someone else's recommendations, so maybe <@U04DKEFP1K8> and <@U04DS2MBWP4> can help with that walkthrough\nI'm finding that I get a little confused in the current UI, and our designer definitely does not understand these flows yet, so having a recorded walkthrough could be really useful for both of us. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oLYtw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey y'all. For our working session, I would ideally like to try a test run through a merit planning workflow, and even to record that for later reference.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Maybe we can have "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " be a guinea pig and see if he can complete merit & bonus recommendations for \"direct reports\" without getting stuck"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "And, I want to see how a leader or executive with multiple managers would see the effects of someone else's recommendations, so maybe "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " and "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " can help with that walkthrough"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nI'm finding that I get a little confused in the current UI, and our designer definitely does not understand these flows yet, so having a recorded walkthrough could be really useful for both of us. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "**********.056629", "text": "<PERSON><PERSON> has already uploaded their <https://docs.google.com/spreadsheets/d/1fopvCam0v0gztD8wMj-vNJuo-luar8Yk4t2gtG6uMhA/edit#gid=0|benefits eligibility sheet> -- we'll still need a few of the averages for things like medical coverage, but this will help us think through Total Rewards requirements in more detail. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "moneybag", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06A8M2HBUZ", "created": **********, "timestamp": **********, "name": "SDF Benefits Eligibility", "title": "SDF Benefits Eligibility", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 60099, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1fopvCam0v0gztD8wMj-vNJuo-luar8Yk4t2gtG6uMhA", "external_url": "https://docs.google.com/spreadsheets/d/1fopvCam0v0gztD8wMj-vNJuo-luar8Yk4t2gtG6uMhA/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1fopvCam0v0gztD8wMj-vNJuo-luar8Yk4t2gtG6uMhA/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHRLHdgYpjSMD2/KnFSXzjtTXVieBQJieY3tTldiwBxTdjelKqsGBIoKRLRRRQIKKKKACiiigAooooAKKKKACiiigAooooA/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06A8M2HBUZ/sdf_benefits_eligibility", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "1onpq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON> has already uploaded their "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1fopvCam0v0gztD8wMj-vNJuo-luar8Yk4t2gtG6uMhA/edit#gid=0", "text": "benefits eligibility sheet"}, {"type": "text", "text": " -- we'll still need a few of the averages for things like medical coverage, but this will help us think through Total Rewards requirements in more detail. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "**********.517769", "text": "My quick notes from the call:\n\n• <PERSON><PERSON> grants\n    ◦ Aligns to prior grant. Additional grant spaced over the remaining time period\n    ◦ Looks at average amount over the previous month. Need to communicate \"based on value on [date]\"\n    ◦ Comp committee assigns a value on backend for planning cycle, but that's not announced to the employees.\n    ◦ Maybe updated quarterly in the tool\n• Perf ratings\n    ◦ Not numeric, different labels (Superstar, etc.)\n• Salary bands\n    ◦ Can share current data, may update in Jan\n• Total Rewards\n    ◦ Need - list of benefits &amp; perks with dollar values\n    ◦ Data is kept in Zenefits, employees can go to profile and look. But doesn't capture all the other areas.\n    ◦ Free lunches - but only for certain employees (locations)\n    ◦ Have eligibility spreadsheet. Some contractors given full-time treatment.\n    ◦ Most important, right benefits assigned to right person. Great to have visibility to the total of cost of perks &amp; benefits. \n    ◦ Medical - cost can range from $600 to $2000 monthly. Okay sharing a range with a caveat.\n    ◦ Differentiate ft / part time / contractor\n    ◦ Position in range - not sure if okay to share\n    ◦ Maybe put the maximum amount? On thinking, prefers average", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LbqVg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "My quick notes from the call:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Lumen grants"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Aligns to prior grant. Additional grant spaced over the remaining time period"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Looks at average amount over the previous month. Need to communicate \"based on value on [date]\""}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Comp committee assigns a value on backend for planning cycle, but that's not announced to the employees."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Maybe updated quarterly in the tool"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Perf ratings"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Not numeric, different labels (Superstar, etc.)"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Salary bands"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can share current data, may update in Jan"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Total Rewards"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Need - list of benefits & perks with dollar values"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Data is kept in Zenefits, employees can go to profile and look. But doesn't capture all the other areas."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Free lunches - but only for certain employees (locations)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Have eligibility spreadsheet. Some contractors given full-time treatment."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Most important, right benefits assigned to right person. Great to have visibility to the total of cost of perks & benefits. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Medical - cost can range from $600 to $2000 monthly. Okay sharing a range with a caveat."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Differentiate ft / part time / contractor"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Position in range - not sure if okay to share"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Maybe put the maximum amount? On thinking, prefers average"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}, {"ts": "**********.520609", "text": "<@U065H3M6WJV> In the master data templates, can we mark the optional fields?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.520609", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "cZEck", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " In the master data templates, can we mark the optional fields?"}]}]}]}], "created_at": "2025-05-22T21:35:34.567863"}