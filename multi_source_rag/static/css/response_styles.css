/* Response Styling for RAG Search */

/* Main response container */
.humanized-response {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #333;
    margin-bottom: 1.5rem;
}

/* Introduction paragraph */
.response-intro {
    font-size: 1.1rem;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 1rem;
    border-left: 3px solid #3498db;
    padding-left: 0.75rem;
}

/* Enhanced paragraphs */
.enhanced-paragraph {
    margin-bottom: 1rem;
    text-align: justify;
}

/* Closing paragraph */
.response-closing {
    font-style: italic;
    color: #555;
    margin-top: 1.5rem;
    border-top: 1px solid #eee;
    padding-top: 0.75rem;
}

/* Source references */
.source-reference {
    font-weight: 500;
    color: #3498db;
}

/* Citations */
sup {
    color: #e74c3c;
    font-weight: 500;
    cursor: pointer;
}

sup:hover {
    text-decoration: underline;
}

/* References section */
.references-section {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #e74c3c;
}

.references-section h6 {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #2c3e50;
}

.reference-item {
    margin-bottom: 0.5rem;
    padding-left: 1rem;
}

/* Code blocks */
pre {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 1rem;
    overflow-x: auto;
    border-left: 3px solid #2ecc71;
}

code {
    font-family: 'Fira Code', 'Courier New', Courier, monospace;
    color: #16a085;
}

/* Lists */
.humanized-response ul, 
.humanized-response ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
}

.humanized-response li {
    margin-bottom: 0.5rem;
}

/* Emphasis */
.humanized-response strong {
    color: #2c3e50;
    font-weight: 600;
}

.humanized-response em {
    color: #7f8c8d;
}

/* Citation badges */
.citation-badge {
    display: inline-block;
    background-color: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    font-size: 12px;
    margin-right: 5px;
    cursor: pointer;
}

/* Citation numbers in text */
.citation-number {
    color: #e74c3c;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
}

.citation-number:hover {
    text-decoration: underline;
}

/* Citation details */
.citation-detail {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 0.75rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    border-left: 3px solid #e74c3c;
    display: none;
}

.citation-detail.active {
    display: block;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .humanized-response {
        font-size: 0.95rem;
    }
    
    .response-intro {
        font-size: 1rem;
    }
}
