{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2023-11", "message_count": 42, "messages": [{"ts": "1699991652.244959", "text": "*Team- Lets use this channel for all product related discussions*", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV", "U04DKEFP1K8"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "ACCX7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Team- Lets use this channel for all product related discussions", "style": {"bold": true}}]}]}]}, {"ts": "1699994446.702349", "text": "I may just drop questions here as I think of them, if that's all right. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "KSdne", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I may just drop questions here as I think of them, if that's all right. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1699994479.501539", "text": "For evaluating Merge -- do we know which HRIS systems our beta customers will need to integrate with? (Is this captured with the LOIs for each customer?)", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ruK+h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For evaluating Merge -- do we know which HRIS systems our beta customers will need to integrate with? (Is this captured with the LOIs for each customer?)"}]}]}]}, {"ts": "1700004457.983609", "text": "our beta customer use zenfits,  Hibob and paylocity", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1700004468.000000"}, "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "qQr+y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "our beta customer use zenfits,  Hibob and paylocity"}]}]}]}, {"ts": "**********.336059", "text": "Good afternoon -- I spent a little time reviewing the Merge API docs and comparing their available fields to the data structure in Compiify's upload formats: <https://docs.google.com/document/d/1zV3XvRMvcCE7cFHkKYaZWkG_LZQVuaoc7lDFbnyCuzE/edit|Data mapping table>\n\nI think the most concerning gaps would be the inability to identify categories that map to compensation ranges, for example:\n• Job Category\n• Job Level \n• Job Family\n• Job Family Group\n• Compensation Grade\nSo, we would need to know if some of these are supported as pass-through or custom data fields from the set of HRIS providers we need to support.\n\nThis is in addition to the question of whether they could provide new integrations to capture equity data at an employee level.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.336059", "reply_count": 6, "files": [{"id": "F065HJUC895", "created": **********, "timestamp": **********, "name": "Data mapping - Merge API", "title": "Data mapping - Merge API", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1zV3XvRMvcCE7cFHkKYaZWkG_LZQVuaoc7lDFbnyCuzE", "external_url": "https://docs.google.com/document/d/1zV3XvRMvcCE7cFHkKYaZWkG_LZQVuaoc7lDFbnyCuzE/edit", "url_private": "https://docs.google.com/document/d/1zV3XvRMvcCE7cFHkKYaZWkG_LZQVuaoc7lDFbnyCuzE/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSOc9cUfjSMMnrSbRng0AOx70YPrQR/tU05B6k0APopmT70ZPoaAFbqKCOaU9aPzoATFBODS01utAC7vajd7UmDRg0AOPWkobrQetAC0hxnnNLSHr0zQAYHvRge9H4UfhQB//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F065HJUC895/data_mapping_-_merge_api", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "8bWJv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Good afternoon -- I spent a little time reviewing the Merge API docs and comparing their available fields to the data structure in Compiify's upload formats: "}, {"type": "link", "url": "https://docs.google.com/document/d/1zV3XvRMvcCE7cFHkKYaZWkG_LZQVuaoc7lDFbnyCuzE/edit", "text": "Data mapping table"}, {"type": "text", "text": "\n\nI think the most concerning gaps would be the inability to identify categories that map to compensation ranges, for example:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Category"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Level "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Family"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Family Group"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Compensation Grade"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nSo, we would need to know if some of these are supported as pass-through or custom data fields from the set of HRIS providers we need to support.\n\nThis is in addition to the question of whether they could provide new integrations to capture equity data at an employee level."}]}]}]}, {"ts": "**********.186989", "text": "<@U065H3M6WJV>, <PERSON><PERSON><PERSON>, and I get together with <PERSON> from Tuesday to Friday in the evening, between 4 pm and 6 pm (the meeting usually lasts for about an hour, but it can be a bit longer). We are primarily discussing new features / answers for some core comp specific questions. If you would like, I can add you in this meeting right away. It's completely optional though. Please let me know.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.186989", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "OXQP8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": ", <PERSON><PERSON><PERSON>, and I get together with <PERSON> from Tuesday to Friday in the evening, between 4 pm and 6 pm (the meeting usually lasts for about an hour, but it can be a bit longer). We are primarily discussing new features / answers for some core comp specific questions. If you would like, I can add you in this meeting right away. It's completely optional though. Please let me know."}]}]}]}, {"ts": "1700154371.268189", "text": "This is how pave is configuring the total rewards and offer lettters\n<https://support.pave.com/hc/en-us/articles/5829282609687-Configuring-Benefits-that-are-displayed-in-Total-Rewards-Visual-Offer-Letter>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Vklhp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is how pave is configuring the total rewards and offer lettters\n"}, {"type": "link", "url": "https://support.pave.com/hc/en-us/articles/5829282609687-Configuring-Benefits-that-are-displayed-in-Total-Rewards-Visual-Offer-Letter"}]}]}]}, {"ts": "1700154380.449959", "text": "they have already done the control panel", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gxcAQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "they have already done the control panel"}]}]}]}, {"ts": "1700254267.408169", "text": "Competitive set that Merge is already supporting via HRIS/payroll integrations:\n• <https://www.assemble.inc/|Assemble>\n• <https://pequity.com/|Pequity>\n• <https://www.payscale.com/|Payscale>\n• <https://ravio.com/|Ravio>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1700254267.408169", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "QpZI0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Competitive set that Merge is already supporting via HRIS/payroll integrations:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://www.assemble.inc/", "text": "Assemble"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://pequity.com/", "text": "Pequity"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://www.payscale.com/", "text": "Payscale"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://ravio.com/", "text": "<PERSON><PERSON>"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1700611436.988169", "text": "Do we have a working session with <PERSON> today? I just noticed <PERSON><PERSON><PERSON> declined the meeting and <PERSON>'s response is 'Maybe' ..", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1700611436.988169", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "yFsre", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we have a working session with <PERSON> today? I just noticed <PERSON><PERSON><PERSON> declined the meeting and <PERSON>'s response is 'Maybe' .."}]}]}]}, {"ts": "1701102502.123969", "text": "Welcome aboard <@U065H3M6WJV> :tada:", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "partying_face", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "AEWfh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Welcome aboard "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "tada", "unicode": "1f389"}]}]}]}, {"ts": "1701102550.688149", "text": "<@U04DKEFP1K8> <@U065H3M6WJV> I set up a time for us to chat at 10 am pst to align on the agenda and objectives for the call with digital assets.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV", "U04DKEFP1K8"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "M/dDI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I set up a time for us to chat at 10 am pst to align on the agenda and objectives for the call with digital assets."}]}]}]}, {"ts": "1701107002.193579", "text": "<@U04DKEFP1K8> devapp (<https://dev-app.compiify.com/login>) is not working for me", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"from_url": "https://dev-app.compiify.com/login", "service_icon": "https://dev-app.compiify.com/apple-touch-icon.png", "id": 1, "original_url": "https://dev-app.compiify.com/login", "fallback": "Compiify", "text": "Web site created using create-react-app", "title": "Compiify", "title_link": "https://dev-app.compiify.com/login", "service_name": "dev-app.compiify.com"}], "blocks": [{"type": "rich_text", "block_id": "hkcSR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " devapp ("}, {"type": "link", "url": "https://dev-app.compiify.com/login"}, {"type": "text", "text": ") is not working for me"}]}]}]}, {"ts": "1701107587.362409", "text": "He changed the login <@U04DS2MBWP4> :slightly_smiling_face:\n\nTry <mailto:<EMAIL>|<EMAIL>> instead as the username", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DTHg2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "He changed the login "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": "\n\nTry "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " instead as the username"}]}]}]}, {"ts": "1701108088.910069", "text": "it works. Thanks Rachel", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "x4p6q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "it works. Thanks Rachel"}]}]}]}, {"ts": "1701109530.836359", "text": "<PERSON><PERSON><PERSON><PERSON>- we can't hear you", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "w3rxm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON>- we can't hear you"}]}]}]}, {"ts": "1701110867.700499", "text": "I see <PERSON><PERSON> put a meeting on our calendars for tomorrow to keep talking about <PERSON><PERSON> - are we ready for another sync? Do we have any tentative plan for implementing a proof of concept with their sandbox, for example?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701110867.700499", "reply_count": 13, "blocks": [{"type": "rich_text", "block_id": "2tby3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I see <PERSON><PERSON> put a meeting on our calendars for tomorrow to keep talking about <PERSON><PERSON> - are we ready for another sync? Do we have any tentative plan for implementing a proof of concept with their sandbox, for example?"}]}]}]}, {"ts": "1701115252.105739", "text": "<@U04DKEFP1K8> Do we have a secure way for customers to share real employee data? Emailing CSV fils is probably not the best option...", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701115252.105739", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "RDh4O", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Do we have a secure way for customers to share real employee data? Emailing CSV fils is probably not the best option..."}]}]}]}, {"ts": "1701115277.167279", "text": "DA will be a good one for us to build customizations for companies with no structured processes in place", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FgTld", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA will be a good one for us to build customizations for companies with no structured processes in place"}]}]}]}, {"ts": "1701115321.817339", "text": "Yes, this was a fascinating call! Feels like we'll learn a lot, and <PERSON><PERSON><PERSON> is especially forthcoming with her thoughts. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zUBC4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, this was a fascinating call! Feels like we'll learn a lot, and <PERSON><PERSON><PERSON> is especially forthcoming with her thoughts. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1701115571.329359", "text": "BT<PERSON>, does <PERSON><PERSON><PERSON><PERSON><PERSON> give you a full transcript of each call? I was taking some notes on the side, but I'm sure I didn't capture everything.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701115571.329359", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "ntad1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "BT<PERSON>, does <PERSON><PERSON><PERSON><PERSON><PERSON> give you a full transcript of each call? I was taking some notes on the side, but I'm sure I didn't capture everything."}]}]}]}, {"ts": "1701119645.090839", "text": "Dan's notes from call with DA\n\nJan 12 - Budgets decided / *Cycle Kickoff*\nFeb 14 - Recommendations submitted / *Cycle End*\nFeb 16 - CFO review\nMarch 14-18 - *Comp Commitee Reviews*\nMarch 25-29 - *Comp Conversations*\nApril 1 - *Effective date*\nApril 10 - Get increases to Payroll\nApril 15 - *First payroll US*\nMonthly - All other\n\n\n5-point rating scale\n1 - New / Training\n2 - Meets some expectations\n3 - Meets Most expectations\n4 - Exceeds Expectations\n5 - Above and Beyond\n\nNo Merit matrix nor clearly defined ranges\n\nBonus is most typically a spot bonus but will sometimes be lump sum in lieu of\n\nDo have variable/sales employees\n\nEquity is D-Units but won’t be input during cycle, no need for vested/unvested\n\n219 EEs\n57 Mgrs\n\nTechnology / PM have job leveling, everyone else no\n\n5-6 regions\nUK, Budapest, Sydney, Hong Kong, Zurich, US", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "lkX0Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>'s notes from call with DA\n\nJan 12 - Budgets decided / "}, {"type": "text", "text": "Cycle Kickoff", "style": {"bold": true}}, {"type": "text", "text": "\nFeb 14 - Recommendations submitted / "}, {"type": "text", "text": "Cycle End", "style": {"bold": true}}, {"type": "text", "text": "\nFeb 16 - CFO review\nMarch 14-18 - "}, {"type": "text", "text": "Comp Commitee Reviews", "style": {"bold": true}}, {"type": "text", "text": "\nMarch 25-29 - "}, {"type": "text", "text": "Comp Conversations", "style": {"bold": true}}, {"type": "text", "text": "\nApril 1 - "}, {"type": "text", "text": "Effective date", "style": {"bold": true}}, {"type": "text", "text": "\nApril 10 - Get increases to Payroll\nApril 15 - "}, {"type": "text", "text": "First payroll US", "style": {"bold": true}}, {"type": "text", "text": "\nMonthly - All other\n\n\n5-point rating scale\n1 - New / Training\n2 - Meets some expectations\n3 - Meets Most expectations\n4 - Exceeds Expectations\n5 - Above and Beyond\n\nNo Merit matrix nor clearly defined ranges\n\nBonus is most typically a spot bonus but will sometimes be lump sum in lieu of\n\nDo have variable/sales employees\n\nEquity is D-Units but won’t be input during cycle, no need for vested/unvested\n\n219 EEs\n57 Mgrs\n\nTechnology / PM have job leveling, everyone else no\n\n5-6 regions\nUK, Budapest, Sydney, Hong Kong, Zurich, US"}]}]}]}, {"ts": "1701119792.078279", "text": "<@U04DKEFP1K8> can you add <PERSON> to all advisor calls including <PERSON> and <PERSON>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "GcAO8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you add <PERSON> to all advisor calls including <PERSON> and <PERSON>"}]}]}]}, {"ts": "1701131681.885769", "text": "Based on today's call with Digital Asset, it seems like these would be the main features we'd need to support for their first compensation cycle:\n\n• Google SSO\n    ◦ _Anticipate 3 users this cycle: <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (CFO)_\n• CSV upload of employee &amp; salary data\n• Ability to input Salary bands in Compiify (since they don't have pre-defined bands to upload)\n    ◦ C_ould Compiify help *suggest* salary bands based on the imported employee data?_\n• Ability to upload or directly input performance ratings in Compiify\n• Assign separate budgets for merit/promo and for spot bonuses\n• Ability to input perf &amp; comp on behalf of managers \n    ◦ _Super Admin access may be sufficient for this requirement_\n• Toggle between USD and local currency\n• Ability to send comp adjustment letters from Compiify\n\nAnything else?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gDF1n", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Based on today's call with Digital Asset, it seems like these would be the main features we'd need to support for their first compensation cycle:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Google SSO"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Anticipate 3 users this cycle: <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (CFO)", "style": {"italic": true}}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "CSV upload of employee & salary data"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Ability to input Salary bands in Compiify (since they don't have pre-defined bands to upload)"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "C"}, {"type": "text", "text": "ould Compiify help ", "style": {"italic": true}}, {"type": "text", "text": "suggest ", "style": {"bold": true, "italic": true}}, {"type": "text", "text": "salary bands based on the imported employee data?", "style": {"italic": true}}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ability to upload or directly input performance ratings in Compiify"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Assign separate budgets for merit/promo and for spot bonuses"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Ability to input perf & comp on behalf of managers "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Super Admin access may be sufficient for this requirement", "style": {"italic": true}}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Toggle between USD and local currency"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Ability to send comp adjustment letters from Compiify"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n\nAnything else?"}]}]}]}, {"ts": "1701137348.913659", "text": "I think you covered most of it. Given they haven't figured out the pay bands, job leveling and job architecture, we will have to discuss the workflows in more details. Lets chat more during the call with <PERSON> tomorrow.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nCaeS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think you covered most of it. Given they haven't figured out the pay bands, job leveling and job architecture, we will have to discuss the workflows in more details. Lets chat more during the call with <PERSON> tomorrow."}]}]}]}, {"ts": "1701150311.478749", "text": "<@U065H3M6WJV> I have added you to recurring advisor call with Paul &amp; Angela ( Made you the owner as well) Please accept ownership change. Once you are are the owner you can modify video conferencing detail sand add your zoom link. You are already part of recurring session with <PERSON> ( I have made you the owner for these sessions as well).", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1701150311.478749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "q/o5E", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I have added you to recurring advisor call with <PERSON> Angela ( Made you the owner as well) Please accept ownership change. Once you are are the owner you can modify video conferencing detail sand add your zoom link. You are already part of recurring session with <PERSON> ( I have made you the owner for these sessions as well)."}]}]}]}, {"ts": "1701205114.444339", "text": "<@U04DKEFP1K8> I was just giving a product demo to sales candidate. The organization tab of merit view appears to be broken in the demo environment. Can you fix it?\nWe really need to fix breaking of demos in the calls.\n<@U065H3M6WJV> any suggestions on how we can silo the demo environment to prevent such breakdowns?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701205114.444339", "reply_count": 2, "edited": {"user": "U04DS2MBWP4", "ts": "1701205134.000000"}, "blocks": [{"type": "rich_text", "block_id": "t5Htd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I was just giving a product demo to sales candidate. The organization tab of merit view appears to be broken in the demo environment. Can you fix it?\nWe really need to fix breaking of demos in the calls.\n"}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " any suggestions on how we can silo the demo environment to prevent such breakdowns?"}]}]}]}, {"ts": "1701205237.897429", "text": "If it's underlying data shifts (like changing which username works), this could be solvable by creating at least 2 different organizations and keeping one stable for demo.\n\nBut if it's more about code pushes breaking the sandbox ... you would end up needing separate sandboxes. :thinking_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8sLh5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If it's underlying data shifts (like changing which username works), this could be solvable by creating at least 2 different organizations and keeping one stable for demo.\n\nBut if it's more about code pushes breaking the sandbox ... you would end up needing separate sandboxes. "}, {"type": "emoji", "name": "thinking_face", "unicode": "1f914"}]}]}]}, {"ts": "1701205427.351099", "text": "I thought that's what <PERSON><PERSON><PERSON><PERSON> was doing- creating two separate environments. I'll wait for <PERSON><PERSON><PERSON><PERSON> to respond.\nWe really need to figure this out now as it has been a consistent issue on the live demo calls.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701205427.351099", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "aHFYY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I thought that's what <PERSON><PERSON><PERSON><PERSON> was doing- creating two separate environments. I'll wait for <PERSON><PERSON><PERSON><PERSON> to respond.\nWe really need to figure this out now as it has been a consistent issue on the live demo calls."}]}]}]}, {"ts": "1701222207.030239", "text": "<@U0658EW4B8D> We can use this channel for updates on PRDs and the customer implementation plans :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4", "U0658EW4B8D"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "CmTUp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " We can use this channel for updates on PRDs and the customer implementation plans "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1701226526.071019", "text": "<@U04DKEFP1K8> When you have a chance -- I did a mockup of what we discussed with <PERSON><PERSON> this morning for OTE comp planning values, using <https://docs.google.com/spreadsheets/d/14A5nElJkzocHtMbcvnC_Mf-nm9ckhmSq0J1-QHUwKh8/edit#gid=0|this Google Sheet>. Take a look and let me know whether it matches what you were expecting? We could also share this with <PERSON><PERSON> to get her feedback before we lock in the requirements. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701226526.071019", "reply_count": 1, "reactions": [{"name": "rocket", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F068E8NKTQQ", "created": 1701226528, "timestamp": 1701226528, "name": "Compiify mockup: OTE adjustments", "title": "Compiify mockup: OTE adjustments", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "14A5nElJkzocHtMbcvnC_Mf-nm9ckhmSq0J1-QHUwKh8", "external_url": "https://docs.google.com/spreadsheets/d/14A5nElJkzocHtMbcvnC_Mf-nm9ckhmSq0J1-QHUwKh8/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/14A5nElJkzocHtMbcvnC_Mf-nm9ckhmSq0J1-QHUwKh8/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHT5pOfalooATnvS0UUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F068E8NKTQQ/compiify_mockup__ote_adjustments", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "w3JBG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " When you have a chance -- I did a mockup of what we discussed with <PERSON><PERSON> this morning for OTE comp planning values, using "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/14A5nElJkzocHtMbcvnC_Mf-nm9ckhmSq0J1-QHUwKh8/edit#gid=0", "text": "this Google Sheet"}, {"type": "text", "text": ". Take a look and let me know whether it matches what you were expecting? We could also share this with <PERSON><PERSON> to get her feedback before we lock in the requirements. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1701226641.616159", "text": "<@U0658EW4B8D> would be good to hear your feedback too. :slightly_smiling_face: And holler at me if the Google macro breaks. :zany_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701226641.616159", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "sjMXH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " would be good to hear your feedback too. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " And holler at me if the Google macro breaks. "}, {"type": "emoji", "name": "zany_face", "unicode": "1f92a"}]}]}]}, {"ts": "1701231198.746489", "text": "<https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0|Project Plan> coming together", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "CgKLg", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "text": "Project Plan"}, {"type": "text", "text": " coming together"}]}]}]}, {"ts": "1701235535.612679", "text": "Nice <@U0658EW4B8D> ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Fj1av", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Nice "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1701264164.689749", "text": "Hey <@U0658EW4B8D> and <@U065H3M6WJV> daily recurring working session is currently scheduled for start at 530am IST. Can we find another schedule between 9am pst - 1pm pst instead?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1701264164.689749", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "nj4s2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " and "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " daily recurring working session is currently scheduled for start at 530am IST. Can we find another schedule between 9am pst - 1pm pst instead?"}]}]}]}, {"ts": "1701275432.794879", "text": "<!here> I will be on vacation on Dec 7th and 8th ( will be available on call  / text / slack if there anything urgent. Here is my cell <tel:5082808112|************>)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "o49h+", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I will be on vacation on Dec 7th and 8th ( will be available on call  / text / slack if there anything urgent. Here is my cell "}, {"type": "link", "url": "tel:5082808112", "text": "************"}, {"type": "text", "text": ")"}]}]}]}, {"ts": "1701294534.997319", "text": "<!here> Exciting update. We just got a verbal acceptance from a solid candidate for the founding sales role with a start date of Jan 3.. He is based out of Raleigh.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "allclap", "users": ["U065H3M6WJV", "U0658EW4B8D"], "count": 2}, {"name": "christmas_parrot", "users": ["U065H3M6WJV"], "count": 1}, {"name": "tada", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ZYmZ/", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Exciting update. We just got a verbal acceptance from a solid candidate for the founding sales role with a start date of Jan 3.. He is based out of Raleigh."}]}]}]}, {"ts": "1701296151.602419", "text": "<@U0658EW4B8D> I know we won't have <PERSON><PERSON><PERSON><PERSON> in today's working session. I think it'd be great to try and hit these topics this afternoon with your help:\n• DA project plan - share your initial thoughts &amp; any questions we need to chase down\n• Handling OTE in merit &amp; org view\n• Prioritizing items from the Front RFP spreadsheet (we'll walk through this live)", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lTPDz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " I know we won't have <PERSON><PERSON><PERSON><PERSON> in today's working session. I think it'd be great to try and hit these topics this afternoon with your help:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA project plan - share your initial thoughts & any questions we need to chase down"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Handling OTE in merit & org view"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Prioritizing items from the Front RFP spreadsheet (we'll walk through this live)"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1701322204.269929", "text": "Update: Wohooo!<PERSON> has signed the offer letter :tada:. His start date is Jan 3. Here is his Linkedln <https://www.linkedin.com/in/wesley-yarber/>\n\nPlease send him a connection request on your linkedln. Also it would be a great gesture to text him at ‭(<tel:919)210-0496|*************>‬ introducing yourself and send him a welcome message. We want to make him feel we are super excited about him joining the team.\n\nPlease confirm with a check mark after you have sent him the welcome message. :blush:", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D", "U04DKEFP1K8"], "count": 2}, {"name": "white_check_mark", "users": ["U065H3M6WJV"], "count": 1}], "attachments": [{"from_url": "https://www.linkedin.com/in/wesley-yarber/", "service_icon": "https://static.licdn.com/aero-v1/sc/h/al2o9zrvru7aqj8e1x2rzsrca", "id": 1, "original_url": "https://www.linkedin.com/in/wesley-yarber/", "fallback": "<PERSON> Con<PERSON>u | LinkedIn", "text": "View Wesley Y.’s profile on LinkedIn, the world’s largest professional community. <PERSON> has 8 jobs listed on their profile. See the complete profile on LinkedIn and discover <PERSON>’s connections and jobs at similar companies.", "title": "<PERSON> Con<PERSON>u | LinkedIn", "title_link": "https://www.linkedin.com/in/wesley-yarber/", "service_name": "linkedin.com"}], "blocks": [{"type": "rich_text", "block_id": "15fBN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Update: Wohooo!<PERSON> has signed the offer letter "}, {"type": "emoji", "name": "tada", "unicode": "1f389"}, {"type": "text", "text": ". His start date is Jan 3. Here is his Linkedln "}, {"type": "link", "url": "https://www.linkedin.com/in/wesley-yarber/"}, {"type": "text", "text": "\n\nPlease send him a connection request on your linkedln. Also it would be a great gesture to text him at ‭(*************‬ introducing yourself and send him a welcome message. We want to make him feel we are super excited about him joining the team.\n\nPlease confirm with a check mark after you have sent him the welcome message. "}, {"type": "emoji", "name": "blush", "unicode": "1f60a"}]}]}]}, {"ts": "1701322291.025799", "text": "His key goal would be to generate $500K in 2024 and bring in 40 new logos.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "K05eC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "His key goal would be to generate $500K in 2024 and bring in 40 new logos."}]}]}]}, {"ts": "1701322413.276239", "text": "Alright team\n\n• Here is the finalized <https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0|Digital Assets Implementation Project Plan>\n• Here is the finalized <https://docs.google.com/spreadsheets/d/1v9GuZ_hLL7gztLiaecNW2yg9WnsMOw792bdyGUT8IpU/edit#gid=0|[Master] Implementation Project Plan Template>", "user": "U0658EW4B8D", "type": "message", "edited": {"user": "U0658EW4B8D", "ts": "1701322581.000000"}, "reactions": [{"name": "rocket", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F067YMCKUV8", "created": 1701322473, "timestamp": 1701322473, "name": "[Master] Implementation Project Plan", "title": "[Template] Implementation Project Plan", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 104064, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1v9GuZ_hLL7gztLiaecNW2yg9WnsMOw792bdyGUT8IpU", "external_url": "https://docs.google.com/spreadsheets/d/1v9GuZ_hLL7gztLiaecNW2yg9WnsMOw792bdyGUT8IpU/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1v9GuZ_hLL7gztLiaecNW2yg9WnsMOw792bdyGUT8IpU/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHSbdngj8abl/Vfypx60gFK4CZf1X8qUbs8kY+lHelouAtFFFMAooooAMc0UUUAFFFFABRRRQAUUUUAFFFFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F067YMCKUV8/_master__implementation_project_plan", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}, {"id": "F067S2J5BF0", "created": 1701322474, "timestamp": 1701322474, "name": "Digital Assets Implementation Project Plan", "title": "Digital Asset Implementation Project Plan", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE", "external_url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSbdnjH403L+q/lTj1popXAMv6r+VKN2eSMfSkHWnUXAWiiimAUUUUAGOaKKKACiiigAooooAKKKKACiiigD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F067S2J5BF0/digital_assets_implementation_project_plan", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "OCUQo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Alright team\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the finalized "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "text": "Digital Assets Implementation Project Plan"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the finalized "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1v9GuZ_hLL7gztLiaecNW2yg9WnsMOw792bdyGUT8IpU/edit#gid=0", "text": "[Master] Implementation Project Plan Template"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1701322487.383439", "text": "Thank you <@U0658EW4B8D> :moneybag:", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "yFpCl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "moneybag", "unicode": "1f4b0"}]}]}]}]}