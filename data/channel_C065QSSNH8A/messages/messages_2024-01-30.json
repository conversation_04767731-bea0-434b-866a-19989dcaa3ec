{"date": "2024-01-30", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1706581245.786499", "text": "Priorities for Eng for the next day:\n• Password reset flow (<https://compiify.atlassian.net/browse/COM-2240|COM-2240>) - This will be more critical when we invite SDF managers into the tool!\n• Prevent \"Reviewed\" with empty fields (<https://compiify.atlassian.net/browse/COM-2144|COM-2144>) - This was introducing other anomalies with \"Reviewed\" employees, but I think we can fix just with this one so I increased its urgency within Wave 3\n• Allow clicking recommended values to input (<https://compiify.atlassian.net/browse/COM-2013|COM-2013>)\n• Enter key / Duplicate comment issue (<https://compiify.atlassian.net/browse/COM-2242|COM-2242>)\nAfter these, continue with <https://compiify.atlassian.net/browse/COM-2087|Wave 3> from the top of the list, especially the Reports-related items.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706581245.786499", "reply_count": 4, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Xcvnn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Password reset flow ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2240", "text": "COM-2240"}, {"type": "text", "text": ") - This will be more critical when we invite SDF managers into the tool!"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Prevent \"Reviewed\" with empty fields ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2144", "text": "COM-2144"}, {"type": "text", "text": ") - This was introducing other anomalies with \"Reviewed\" employees, but I think we can fix just with this one so I increased its urgency within Wave 3"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Allow clicking recommended values to input ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2013", "text": "COM-2013"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Enter key / Duplicate comment issue ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2242", "text": "COM-2242"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAfter these, continue with "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " from the top of the list, especially the Reports-related items."}]}]}]}, {"ts": "1706580270.679249", "text": "<@U0658EW4B8D> The QA environment should be updated now with the corrected adjustment letters; will you have time to test tomorrow? I've changed our meeting time to 5p (because I'm on a demo at 4), is it possible to have some initial testing &amp; feedback ready before then?", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "NluA+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " The QA environment should be updated now with the corrected adjustment letters; will you have time to test tomorrow? I've changed our meeting time to 5p (because I'm on a demo at 4), is it possible to have some initial testing & feedback ready before then?"}]}]}]}, {"ts": "1706571404.806089", "text": "SDF training session is in the books! :books:\n• The repurposed sdf-test environment allowed us to demo SDF's customizations without exposing any real salaries\n• Their managers had the most questions about pay bands, mainly wanting reassurance that each employee's pay band would be the correct one for their location &amp; currency\n• <PERSON><PERSON> had compiled the main talking points she wanted to cover, so that made it easier to focus the training on just what the managers needed to know\nNext up:\n• We'll need to email login credentials when <PERSON><PERSON>/<PERSON> give us the green light (they want to do additional updates to the comp bands first)\n• We'll ask those managers to use <mailto:<EMAIL>|<EMAIL>> to send their questions &amp; feedback\n• Compiify team may need to make manual updates to promos once their committee approves/rejects promo nominations (around Feb 23)\n• Depending on how <PERSON>/<PERSON><PERSON> want to handle adjustment letters, we may need to do an additional training session with the same leaders at the end of cycle to show how to download and distribute those", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706571404.806089", "reply_count": 3, "reactions": [{"name": "heart", "users": ["U04DS2MBWP4", "U04DKEFP1K8", "U0658EW4B8D"], "count": 3}, {"name": "rocket", "users": ["U04DS2MBWP4", "U0658EW4B8D"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "ECFRJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF training session is in the books! "}, {"type": "emoji", "name": "books", "unicode": "1f4da"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The repurposed sdf-test environment allowed us to demo SDF's customizations without exposing any real salaries"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Their managers had the most questions about pay bands, mainly wanting reassurance that each employee's pay band would be the correct one for their location & currency"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON> had compiled the main talking points she wanted to cover, so that made it easier to focus the training on just what the managers needed to know"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nNext up:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We'll need to email login credentials when <PERSON><PERSON><PERSON> give us the green light (they want to do additional updates to the comp bands first)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "We'll ask those managers to use "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " to send their questions & feedback"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Compiify team may need to make manual updates to promos once their committee approves/rejects promo nominations (around Feb 23)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Depending on how <PERSON>/<PERSON><PERSON> want to handle adjustment letters, we may need to do an additional training session with the same leaders at the end of cycle to show how to download and distribute those"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.586809"}