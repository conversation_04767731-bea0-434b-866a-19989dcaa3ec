{"date": "2024-08-27", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1724780247.656619", "text": "Thank You <@U07EJ2LP44S> :slightly_smiling_face:", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "zzz", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nGi6Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank You "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1724778313.925969", "text": "<@U07EJ2LP44S> Reg. Active Integrations as of today.\n• Only *Vercara*, *Div Energy* have authenticated. \n• *Valgenesis* connection got invalidated, We need to ask them to re-authenticate. \n• *Alayacare* has not authenticated.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1724778880.000000"}, "files": [{"id": "F07JB0JGTK9", "created": 1724778239, "timestamp": 1724778239, "name": "Screenshot 2024-08-27 at 10.33.34 PM.png", "title": "Screenshot 2024-08-27 at 10.33.34 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 107589, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07JB0JGTK9/screenshot_2024-08-27_at_10.33.34___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07JB0JGTK9/download/screenshot_2024-08-27_at_10.33.34___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 209, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 279, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 419, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 465, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 558, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07JB0JGTK9-48c329721b/screenshot_2024-08-27_at_10.33.34___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 595, "original_w": 1309, "original_h": 761, "thumb_tiny": "AwAbADDRKgmlAx0o70GgA/GlpKKAFopKKACl70UUAFFFFAAOlIKWigD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07JB0JGTK9/screenshot_2024-08-27_at_10.33.34___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07JB0JGTK9-f45bc219d1", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "mlRQm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Reg. Active Integrations as of today.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Only "}, {"type": "text", "text": "Vercar<PERSON>", "style": {"bold": true}}, {"type": "text", "text": ", "}, {"type": "text", "text": "Div Energy", "style": {"bold": true}}, {"type": "text", "text": " have authenticated. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis", "style": {"bold": true}}, {"type": "text", "text": " connection got invalidated, We need to ask them to re-authenticate. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Alayacare", "style": {"bold": true}}, {"type": "text", "text": " has not authenticated."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724774247.423269", "text": "<!here> I am bit tired and very sleepy today. Not sure if I will be able to stay for whole 1.5 hrs meeting.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ym5VU", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I am bit tired and very sleepy today. Not sure if I will be able to stay for whole 1.5 hrs meeting."}]}]}]}, {"ts": "1724723238.735399", "text": "*Agenda for tomorrow* (in addition to anything you all have)\n1. *Manager enablement* - I've drafted an interactive exercise for us to run through using Mir<PERSON> to kick off the project and align on the different compensation roles, manager types, customer pain points and benefits that we could solve for with this broad theme.\n2. *Prioritization* - I got some great feedback from <PERSON> and adjusted the spreadsheet accordingly. We can discuss the changes if <PERSON> and <PERSON><PERSON><PERSON><PERSON> have a chance to update the new columns before we meet (otherwise we can do this in the call or push the discussion to We<PERSON>)", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724723238.735399", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8", "U04DS2MBWP4", "U0690EB5JE5"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "kJdKy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for tomorrow", "style": {"bold": true}}, {"type": "text", "text": " (in addition to anything you all have)\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Manager enablement", "style": {"bold": true}}, {"type": "text", "text": " - I've drafted an interactive exercise for us to run through using Miro to kick off the project and align on the different compensation roles, manager types, customer pain points and benefits that we could solve for with this broad theme."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Prioritization", "style": {"bold": true}}, {"type": "text", "text": " - I got some great feedback from <PERSON> and adjusted the spreadsheet accordingly. We can discuss the changes if <PERSON> and <PERSON><PERSON><PERSON><PERSON> have a chance to update the new columns before we meet (otherwise we can do this in the call or push the discussion to We<PERSON>)"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1724709194.797069", "text": "<@U07EJ2LP44S>  we need to check with CWA on how they plan to distribute the COLA adjustments. From what I understand, not all employees may be eligible for COLA, as organizations often use criteria like employment status (full-time, part-time), tenure, and performance to determine eligibility. Currently, we have the capability to set eligibility based on performance-based recommendations, and adjustments can be manually modified if needed. Could you please get details on how CWA intends to approach this?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724709194.797069", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "f5NJX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "  we need to check with CWA on how they plan to distribute the COLA adjustments. From what I understand, not all employees may be eligible for COLA, as organizations often use criteria like employment status (full-time, part-time), tenure, and performance to determine eligibility. Currently, we have the capability to set eligibility based on performance-based recommendations, and adjustments can be manually modified if needed. Could you please get details on how CWA intends to approach this?"}]}]}]}, {"ts": "1724704642.503139", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> based on <PERSON>'s feedback from your earlier meeting, I made the following changes to the spreadsheet:\n\n1. Changed the first criteria from \"what portion of customers does this affect\" to *\"is this a universal need or a custom request\"*. At this stage it is less important whether it's 1, 2 or all customers requesting, and more important whether it's specific to their situation or fairly universal\n2. Added \"*Effort To Ship Estimate IN ADDITION to Dev\"* column - this will now be added to the initial dev estimate in calculating overall scope/priority.\n3. Separated the \"Level 1\" tab into two - the first one is items needed before this year's Sept/Nov cycles, the second one is items needed for the next set of cycles in Q1. You can just start with the first Nov tab for now.\nSo before the meeting tomorrow, if you could go through the 1st tab and select values for Column J, Universal Need vs Custom (<@U07EJ2LP44S>) and Column N - Effort to ship in addition to <PERSON> (<@U04DKEFP1K8>), then we can take a look at the new prioritization values tomorrow.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724704642.503139", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "3+<PERSON>qa", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " based on <PERSON>'s feedback from your earlier meeting, I made the following changes to the spreadsheet:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Changed the first criteria from \"what portion of customers does this affect\" to "}, {"type": "text", "text": "\"is this a universal need or a custom request\"", "style": {"bold": true}}, {"type": "text", "text": ". At this stage it is less important whether it's 1, 2 or all customers requesting, and more important whether it's specific to their situation or fairly universal"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Added \""}, {"type": "text", "text": "Effort To Ship Estimate IN ADDITION to Dev\" ", "style": {"bold": true}}, {"type": "text", "text": "column - this will now be added to the initial dev estimate in calculating overall scope/priority."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Separated the \"Level 1\" tab into two - the first one is items needed before this year's Sept/Nov cycles, the second one is items needed for the next set of cycles in Q1. You can just start with the first Nov tab for now."}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nSo before the meeting tomorrow, if you could go through the 1st tab and select values for Column J, Universal Need vs Custom ("}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": ") and Column N - Effort to ship in addition to Dev ("}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "), then we can take a look at the new prioritization values tomorrow."}]}]}]}, {"ts": "1724703603.733609", "text": "<@U04DS2MBWP4> can you fill $ amount in row 8 <https://docs.google.com/spreadsheets/d/1BlWJ2Jq1cmLwSCRekshFyAgVu9cdfhBMcDA5Yiew-Gs/edit?gid=0#gid=0>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724703603.733609", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "tEf81", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " can you fill $ amount in row 8 "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1BlWJ2Jq1cmLwSCRekshFyAgVu9cdfhBMcDA5Yiew-Gs/edit?gid=0#gid=0"}]}]}]}], "created_at": "2025-05-22T21:35:34.644651"}