{"date": "2025-01-20", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1737356731.853259", "text": "*After Sync:*", "user": "U0690EB5JE5", "type": "message", "files": [{"id": "F089CQZV17D", "created": 1737356729, "timestamp": 1737356729, "name": "Screenshot 2025-01-20 at 12.18.57 PM.png", "title": "Screenshot 2025-01-20 at 12.18.57 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 13560, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089CQZV17D/screenshot_2025-01-20_at_12.18.57___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089CQZV17D/download/screenshot_2025-01-20_at_12.18.57___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F089CQZV17D-c4506d481e/screenshot_2025-01-20_at_12.18.57___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F089CQZV17D-c4506d481e/screenshot_2025-01-20_at_12.18.57___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F089CQZV17D-c4506d481e/screenshot_2025-01-20_at_12.18.57___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 55, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F089CQZV17D-c4506d481e/screenshot_2025-01-20_at_12.18.57___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 73, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F089CQZV17D-c4506d481e/screenshot_2025-01-20_at_12.18.57___pm_160.png", "original_w": 588, "original_h": 90, "thumb_tiny": "AwAHADDRKhsZoKjHrTqKAG7QcZFBXI9adRQAm0e9IFw3A4p1FAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089CQZV17D/screenshot_2025-01-20_at_12.18.57___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089CQZV17D-ce993ef29a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "xtcOy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "After Sync:", "style": {"bold": true}}]}]}]}, {"ts": "1737356697.486389", "text": "<@U07EJ2LP44S> UPDATE on Tithley sync,\nThere are no other changes except for these two employees having salary updates, I am also not sure the salaries manually updated in prod post sync. Please confirm with customer on these two employees and We can update the salaries.\n\n\n*Before Sync*", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737356697.486389", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1737378775.000000"}, "files": [{"id": "F08A1N2LPH6", "created": 1737356685, "timestamp": 1737356685, "name": "Screenshot 2025-01-20 at 12.19.22 PM.png", "title": "Screenshot 2025-01-20 at 12.19.22 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 13264, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08A1N2LPH6/screenshot_2025-01-20_at_12.19.22___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08A1N2LPH6/download/screenshot_2025-01-20_at_12.19.22___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A1N2LPH6-f3ed1afa8f/screenshot_2025-01-20_at_12.19.22___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A1N2LPH6-f3ed1afa8f/screenshot_2025-01-20_at_12.19.22___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A1N2LPH6-f3ed1afa8f/screenshot_2025-01-20_at_12.19.22___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 54, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A1N2LPH6-f3ed1afa8f/screenshot_2025-01-20_at_12.19.22___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 72, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A1N2LPH6-f3ed1afa8f/screenshot_2025-01-20_at_12.19.22___pm_160.png", "original_w": 583, "original_h": 87, "thumb_tiny": "AwAHADDRKhsZoKjHrTqKAG7QcZFDKCPWnUUAJtGKQLhuBxTqKAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08A1N2LPH6/screenshot_2025-01-20_at_12.19.22___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08A1N2LPH6-1827859c0a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "k3m+n", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " UPDATE on <PERSON><PERSON><PERSON> sync,\nThere are no other changes except for these two employees having salary updates, I am also not sure the salaries manually updated in prod post sync. Please confirm with customer on these two employees and We can update the salaries.\n\n\n"}, {"type": "text", "text": "Before Sync", "style": {"bold": true}}]}]}]}, {"ts": "1737356580.721589", "text": "<@U07EJ2LP44S>\n• Div SSO is setup and replied them on the email thread.\n• HireDate is also added to comp planner view Friday", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737123368.746899", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "6fTmm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Div SSO is setup and replied them on the email thread."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "HireDate is also added to comp planner view Friday"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1737380584.425139", "text": "This will be done by Tuesday. Please note that this will be only for Salary components not bonus. Is <PERSON><PERSON><PERSON> is running this cycle?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737081886.337709", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1737382450.000000"}, "blocks": [{"type": "rich_text", "block_id": "WF/FT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This will be done by Tuesday. Please note that this will be only for Salary components not bonus. Is <PERSON><PERSON><PERSON> is running this cycle?"}]}]}]}, {"ts": "1737380507.023129", "text": "<@U07EJ2LP44S> All the issues reported so far and sync requests are completed. Please let us know if you face any issues. Please do share files to upload and things to take care if you are blocked by any issue, I will take care during my day. Create tickets if possible.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "KAatN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " All the issues reported so far and sync requests are completed. Please let us know if you face any issues. Please do share files to upload and things to take care if you are blocked by any issue, I will take care during my day. Create tickets if possible."}]}]}]}, {"ts": "1737379024.671709", "text": "<@U07EJ2LP44S> <http://stridedemo.stridehr.io|stridedemo.stridehr.io> is up and running. All the cycles deleted.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737121817.222299", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1737380119.000000"}, "blocks": [{"type": "rich_text", "block_id": "Z3+98", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://stridedemo.stridehr.io", "text": "stridedemo.stridehr.io"}, {"type": "text", "text": " is up and running. All the cycles deleted."}]}]}]}, {"ts": "1737378029.464949", "text": "<@U07EJ2LP44S> This issue is fixed as well.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737124353.756669", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "WQslt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This issue is fixed as well."}]}]}]}], "created_at": "2025-05-22T21:35:34.689973"}