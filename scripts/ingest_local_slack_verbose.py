#!/usr/bin/env python
import os, sys, django
sys.path.append('.'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local"); django.setup()
from apps.documents.services.ingestion_service import IngestionService
from apps.accounts.models import Tenant
from datetime import datetime

print("🚀 Starting Slack data ingestion...")
start_time = datetime.now()

tenant, _ = Tenant.objects.get_or_create(slug="default", defaults={"name": "Default"})
print(f"✅ Tenant: {tenant.name}")

service = IngestionService(tenant=tenant)
print("✅ Ingestion service initialized")

source = service.create_source("Local Slack", "local_slack", {
    "data_dir": "../data/", 
    "channel": "C065QSSNH8A", 
    "custom_days": 730
})
print(f"✅ Source created: {source.name}")

print("🔄 Starting processing...")
processed, failed = service.process_source(source, batch_size=50, days_back=730)

end_time = datetime.now()
duration = (end_time - start_time).total_seconds()

print(f"🎉 Completed in {duration:.1f} seconds!")
print(f"✅ Processed: {processed}, Failed: {failed}")
