{"thread_ts": "1733948742.699469", "channel_id": "C065QSSNH8A", "reply_count": 25, "replies": [{"ts": "1733949142.588009", "text": "so for now, there is nothing eng needs to do for this ticket?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "pF/PW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "so for now, there is nothing eng needs to do for this ticket?"}]}]}]}, {"ts": "1733949232.442589", "text": "No, we do need to fix it. We should not allow the cycle to be created with a hierarchy level missing from the recommenders list.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "yuuTU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No, we do need to fix it. We should not allow the cycle to be created with a hierarchy level missing from the recommenders list."}]}]}]}, {"ts": "1733949253.065309", "text": "It's still a bug, I just fixed this one.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "v71a/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It's still a bug, I just fixed this one."}]}]}]}, {"ts": "1733955885.810069", "text": "Ugh now I see <PERSON><PERSON><PERSON> is missing. He was definitely there before, b/c he is within the M8 group. There is something weird going on here.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ugh now I see <PERSON><PERSON><PERSON> is missing. He was definitely there before, b/c he is within the M8 group. There is something weird going on here."}]}]}]}, {"ts": "1733956092.990969", "text": "I added this as a comment to the ticket, can you look when you get in today, <@U0690EB5JE5>?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "92IKA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I added this as a comment to the ticket, can you look when you get in today, "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1733966331.779189", "text": "<@U07MH77PUBV> PTAL", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "fWBvh", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " PTAL"}]}]}]}, {"ts": "1734000080.999459", "text": "<@U07MH77PUBV> this is reassigned. You don't have to look into this.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733948742.699469", "reactions": [{"name": "+1", "users": ["U07MH77PUBV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "kR3mx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " this is reassigned. You don't have to look into this."}]}]}]}, {"ts": "1734022505.325689", "text": "Is there any update on this one? I saw a PR i think as a comment", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "sPykO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is there any update on this one? I saw a PR i think as a comment"}]}]}]}, {"ts": "1734022660.219289", "text": "Will need to review and deploy. I will do it tomorrow if it can’t wait till Monday.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "aYy0Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will need to review and deploy. I will do it tomorrow if it "}, {"type": "text", "text": "can’t"}, {"type": "text", "text": " wait till Monday."}]}]}]}, {"ts": "1734022677.396789", "text": "It's blocked us on testing", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "VBC8q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It's blocked us on testing"}]}]}]}, {"ts": "1734022711.624219", "text": "If I can manually override it or something to get it working again today I can do that", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "dvnzf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If I can manually override it or something to get it working again today I can do that"}]}]}]}, {"ts": "1734022797.546399", "text": "I haven’t tested it. I can deploy however. This will anyway impact diven for now ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "SgSul", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I "}, {"type": "text", "text": "haven’t"}, {"type": "text", "text": " tested it. I can deploy however. This will anyway impact diven for now "}]}]}]}, {"ts": "1734022817.876739", "text": "Give me like an hour ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "cDrkZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Give me like an hour "}]}]}]}, {"ts": "1734022820.926669", "text": "Ok thank you", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "aO5Qy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok thank you"}]}]}]}, {"ts": "1734024947.006869", "text": "<@U07EJ2LP44S> Fix deployed to Diven ENV. Please note I haven't got a chance to test the fix myself. Please let me know here if you still find issues.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "rsSuq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Fix deployed to Diven ENV. Please note I haven't got a chance to test the fix myself. Please let me know here if you still find issues."}]}]}]}, {"ts": "1734024958.317499", "text": "understood; thank you!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "oRUW4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "understood; thank you!"}]}]}]}, {"ts": "1734025124.476239", "text": "it did not work", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "fOdEq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "it did not work"}]}]}]}, {"ts": "1734025146.603839", "text": "let me try deleteing m8 and replacing it", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "qVYqc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "let me try deleteing m8 and replacing it"}]}]}]}, {"ts": "1734025301.084899", "text": "I can get it to look right inside the recommenders page in cycle builder, but as soon as i get to allocaiton page, it's all wrong again", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "WLx/A", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can get it to look right inside the recommenders page in cycle builder, but as soon as i get to allocaiton page, it's all wrong again"}]}]}]}, {"ts": "1734025506.451049", "text": "ok, I will get it fixed tomorrow <@U07EJ2LP44S>.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "GIz9W", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok, I will get it fixed tomorrow "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1734025543.797139", "text": "ok; i'll let cindy know we won't be able to test til at least them", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "WllaH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok; i'll let cindy know we won't be able to test til at least them"}]}]}]}, {"ts": "1734025583.417779", "text": "Sure.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "sxALT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure."}]}]}]}, {"ts": "1734105709.265499", "text": "<@U07EJ2LP44S> there was fix deployed  couple of hours ago. I couldn’t test though. Engineer confirmed it’s tested well. Please take a look once. I don’t have access to laptop, hoping the fix will work fine.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733948742.699469", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "DT9r8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " there was fix deployed  couple of hours ago"}, {"type": "text", "text": "."}, {"type": "text", "text": " I "}, {"type": "text", "text": "couldn’t"}, {"type": "text", "text": " test though. Engineer confirmed "}, {"type": "text", "text": "it’s"}, {"type": "text", "text": " tested well. Please take a look once. I "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " have access to laptop, hoping the fix will work fine."}]}]}]}, {"ts": "1734106249.883429", "text": "Thanks I'll look now!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "blocks": [{"type": "rich_text", "block_id": "gD7+1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks I'll look now!"}]}]}]}, {"ts": "1734106828.306739", "text": "It does look like it's working. I'll let you know if not!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "mPhKn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It does look like it's working. I'll let you know if not!"}]}]}]}], "created_at": "2025-05-22T22:00:49.316097"}