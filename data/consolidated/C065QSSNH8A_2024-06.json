{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2024-06", "message_count": 62, "messages": [{"ts": "1717534457.963979", "text": "Thinking about the challenge of finishing / releasing Merit 2.0 to customers around June 15 -- one thing I realize is that our new Merit 2.0 has email notifications built in, and we don't (yet) have a toggle for those. It could be extremely disruptive to a customer if their preliminary testing sends real emails to their managers and executives.\n\nWe should at the very least have a way to toggle the emails on/off. I don't think we have to get too clever about having emails rerouted to the HR admin or anything like that -- if they do want to preview them, they can test within the HR team. :wink:\n\nDo you think we can incorporate that within, or soon after, the June 15 timeline <@U0690EB5JE5>?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Cw4pi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thinking about the challenge of finishing / releasing Merit 2.0 to customers around June 15 -- one thing I realize is that our new Merit 2.0 has email notifications built in, and we don't (yet) have a toggle for those. It could be extremely disruptive to a customer if their preliminary testing sends real emails to their managers and executives.\n\nWe should at the very least have a way to toggle the emails on/off. I don't think we have to get too clever about having emails rerouted to the HR admin or anything like that -- if they do want to preview them, they can test within the HR team. "}, {"type": "emoji", "name": "wink", "unicode": "1f609"}, {"type": "text", "text": "\n\nDo you think we can incorporate that within, or soon after, the June 15 timeline "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1717547393.325189", "text": "<@U065H3M6WJV> that should be quick one. Will get that done today. One more thing came to my mind for flags. Should we give ability to toggle flagging at employee level?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1717547393.325189", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "2lzSk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " that should be quick one. Will get that done today. One more thing came to my mind for flags. Should we give ability to toggle flagging at employee level"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1717547427.657149", "text": "Will keep the email toggle in settings page?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1717547427.657149", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2hMwU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will keep the email toggle in settings page?"}]}]}]}, {"ts": "1717590287.015469", "text": "<@U04DKEFP1K8> <@U065H3M6WJV> I am working on optimizing cycle builder create/update API. There are some major issues due to the way currently the API updates/creates the merit rows.\nEvery time any changes made to cycle config. It deletes all the rows and creates new merit rows copying adjustments made and recalculating budgets/guidelines.  Issues because of this\n• There is bug due to which flags/comments are not carried over, even if carried over we are losing the timestamp info.\n• This is also would cause issues with submission statuses, since the old rows are deleted and created again, we need to handle the new merit view 2 statuses as well i.e. copy over\n• This also would mess up the audit log if implemented later\n•  And another issue is,  The delete/create is done at last step (screenshot below) of guidelines and the following publish buttons have no meaning. Basically after clicking the continue button in this step triggers create/update API which deletes and recreates merit rows and post that changes are irreversible. Also if there is any failure in between, all the past merit changes are also lost.\n• Another related issue <https://compiify.atlassian.net/browse/COM-3089>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1717590287.015469", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "1717591682.000000"}, "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F076RS4FP60", "mode": "tombstone"}], "blocks": [{"type": "rich_text", "block_id": "ZPGtw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I am working on optimizing cycle builder create/update API. There are some major issues due to the way currently the API updates/creates the merit rows.\nEvery time any changes made to cycle config. It deletes all the rows and creates new merit rows copying adjustments made and recalculating budgets/guidelines.  Issues because of this\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There is bug due to which flags/comments are not carried over, even if carried over we are losing the timestamp info."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "This is also would cause issues with submission statuses, since the old rows are deleted and created again, we need to handle the new merit view 2 statuses as well i.e. copy over"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "This also would mess up the audit log if implemented later"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": " And another issue is,  The delete/create is done at last step (screenshot below) of guidelines and the following publish buttons have no meaning. Basically after clicking the continue button in this step triggers create/update API which deletes and recreates merit rows and post that changes are irreversible. Also if there is any failure in between, all the past merit changes are also lost."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Another related issue "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3089"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1717590661.845819", "text": "I am thinking to rewrite this API to make it update only not delete and recreate and optimize along the way.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9CqQ8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am thinking to rewrite this API to make it update only not delete and recreate and optimize along the way."}]}]}]}, {"ts": "1717590689.963149", "text": "So Please note that, updating cycle may mess up the merit changes  w.r.t submission/approval statuses, flags/comments :slightly_smiling_face:", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1717590785.000000"}, "blocks": [{"type": "rich_text", "block_id": "PhvH5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So Please note that, updating cycle may mess up the merit changes  w.r.t submission/approval statuses, flags/comments "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1717605615.629659", "text": "<@U0690EB5JE5> So after this change, we may need to start a new cycle for more realistic testing?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/B02N", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " So after this change, we may need to start a new cycle for more realistic testing?"}]}]}]}, {"ts": "1717607153.179969", "text": "<@U065H3M6WJV> not really, the goal is to avoid creating new cycle because editing corrupts the data. Will discuss in next eng discussion.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vxnVc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " not really, the goal is to avoid creating new cycle because editing corrupts the data. Will discuss in next eng discussion"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1717607203.650539", "text": "I need to discuss with you on some scenarios if I have to be aware in case ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1717607203.650539", "reply_count": 2, "edited": {"user": "U0690EB5JE5", "ts": "1717611097.000000"}, "blocks": [{"type": "rich_text", "block_id": "0dXYX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I need to discuss with you on some scenarios if I have to be aware in case "}]}]}]}, {"ts": "1717672051.021099", "text": "<@U04DKEFP1K8> I have been making sure all the changes are synced to test ENV where we have customer sandboxes and new-meritview everyday. Its been a while we have not synced code changes to staging. Should I go ahead and keep doing that everyday going forward with staging as well. I think I stopped doing that due to people insights work.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1717672051.021099", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "1717673736.000000"}, "blocks": [{"type": "rich_text", "block_id": "DX+Gn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I have been making sure all the changes are synced to test ENV where we have customer sandboxes and new-meritview everyday. Its been a while we have not synced code changes to staging. Should I go ahead and keep doing that everyday going forward with staging as well. I think I stopped doing that due to people insights work."}]}]}]}, {"ts": "1717782076.160569", "text": "<@U065H3M6WJV> Here are details of <PERSON>'s 2nd environment\n\nDomain: <http://qa.compiify.com|qa.compiify.com> ( running with meritview 2.0 + people insights )\nemail: \"<mailto:<PERSON>@payright.com|<PERSON>@payright.com>\" ( superadmin)\npassword: Will DM you the password\n\nNotes:\n1. Login is enabled for following managers\n    a. <mailto:<PERSON>@payright.com|<PERSON>@payright.com>, <mailto:<PERSON>@payright.com|<PERSON>@payright.com>, <mailto:<EMAIL>|<PERSON><EMAIL>>, <mailto:<EMAIL>|<EMAIL>>, <mailto:<PERSON>@payright.com|<PERSON>@payright.com>, <mailto:<PERSON>@payright.com|<PERSON>@payright.com>, <mailto:<PERSON><PERSON>@payright.com|<PERSON><EMAIL>>\n2. Adjustment have been submitted for ( <PERSON>, <PERSON>, <PERSON> and <PERSON>)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1717782076.160569", "reply_count": 2, "edited": {"user": "U04DKEFP1K8", "ts": "1717782751.000000"}, "blocks": [{"type": "rich_text", "block_id": "V7Swc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Here are details of Delaney's 2nd environment\n\nDomain: "}, {"type": "link", "url": "http://qa.compiify.com", "text": "qa.compiify.com"}, {"type": "text", "text": " ( running with meritview 2.0 + people insights )\nemail: \""}, {"type": "link", "url": "mailto:<PERSON>@payright.com", "text": "<PERSON>@payright.com"}, {"type": "text", "text": "\" ( superadmin)\npassword: Will DM you the password\n\nNotes:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Login is enabled for following managers"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "mailto:<PERSON>@payright.com", "text": "<EMAIL>"}, {"type": "text", "text": ", "}, {"type": "link", "url": "mailto:<PERSON>@payright.com", "text": "<EMAIL>"}, {"type": "text", "text": ", "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ", "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ", "}, {"type": "link", "url": "mailto:<PERSON>@payright.com", "text": "<PERSON>@payright.com"}, {"type": "text", "text": ", "}, {"type": "link", "url": "mailto:<PERSON>@payright.com", "text": "<PERSON>@payright.com"}, {"type": "text", "text": ", "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}]}], "style": "ordered", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Adjustment have been submitted for ( <PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON>)"}]}], "style": "ordered", "indent": 0, "offset": 1, "border": 0}]}]}, {"ts": "1717797799.570369", "text": "<@U065H3M6WJV> There is a requirement for compliance where i need to provide how-to guides or reference materials for the service. What is the closest document we have currently?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1717797799.570369", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "G03VQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " There is a requirement for compliance where i need to provide how-to guides or reference materials for the service. What is the closest document we have currently?"}]}]}]}, {"ts": "1718055611.595939", "text": "<@U0690EB5JE5> were you storing serial number of all laptops in a document, if yes please share the location of the document", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eUnhO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " were you storing serial number of all laptops in a document, if yes please share the location of the document"}]}]}]}, {"ts": "1718057652.387599", "text": "<https://docs.google.com/spreadsheets/d/1uEzUEJOs3_REYoAwZ_CRuFn4LhgyJOUe1QG399kBeu8/edit#gid=0>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F077JCTF65A", "created": 1718057655, "timestamp": 1718057655, "name": "Asset assignment", "title": "Asset assignment", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 44454, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1uEzUEJOs3_REYoAwZ_CRuFn4LhgyJOUe1QG399kBeu8", "external_url": "https://docs.google.com/spreadsheets/d/1uEzUEJOs3_REYoAwZ_CRuFn4LhgyJOUe1QG399kBeu8/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1uEzUEJOs3_REYoAwZ_CRuFn4LhgyJOUe1QG399kBeu8/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F077JCTF65A-fc44c4e960/asset_assignment_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F077JCTF65A-fc44c4e960/asset_assignment_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F077JCTF65A-fc44c4e960/asset_assignment_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F077JCTF65A-fc44c4e960/asset_assignment_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F077JCTF65A-fc44c4e960/asset_assignment_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F077JCTF65A-fc44c4e960/asset_assignment_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F077JCTF65A-fc44c4e960/asset_assignment_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F077JCTF65A-fc44c4e960/asset_assignment_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F077JCTF65A-fc44c4e960/asset_assignment_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHSJO7FIWwcYpxAPakwPSgBFYknNOoAA6UUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F077JCTF65A/asset_assignment", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "J16Sh", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/spreadsheets/d/1uEzUEJOs3_REYoAwZ_CRuFn4LhgyJOUe1QG399kBeu8/edit#gid=0"}]}]}]}, {"ts": "1718061003.544889", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> Were any changes made to the `new-meritview` data set after ~11am Pacific today? The \"New Job Title\" dropdown suddenly started populating now when it wasn't before, and I wanted to know if that was related to a change in Salary Bands or something more unpredictable happening in Merit View.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1718061003.544889", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "Ee9TB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Were any changes made to the "}, {"type": "text", "text": "new-meritview", "style": {"code": true}}, {"type": "text", "text": " data set after ~11am Pacific today? The \"New Job Title\" dropdown suddenly started populating now when it wasn't before, and I wanted to know if that was related to a change in Salary Bands or something more unpredictable happening in Merit View."}]}]}]}, {"ts": "1718207899.352329", "text": "<@U0690EB5JE5> I saw <@U04DKEFP1K8>’s note that he's unavailable for the next hour. I see Merit 2.0 on the agenda, is that a topic we can cover without him?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "w8ApG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I saw "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "’s note that he's unavailable for the next hour. I see Merit 2.0 on the agenda, is that a topic we can cover without him?"}]}]}]}, {"ts": "1718208037.218089", "text": "Sure <@U065H3M6WJV> We can", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "cqM20", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " We can"}]}]}]}, {"ts": "1718216816.419039", "text": "<@U04DKEFP1K8> I'm still seeing some stray `NaN` values on some of the equity amounts in `new-meritview`, can you run the SQL update to see if that fixes it?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1718216816.419039", "reply_count": 2, "files": [{"id": "F078H4K4BJL", "created": 1718216813, "timestamp": 1718216813, "name": "Screenshot 2024-06-12 at 11.26.10 AM.png", "title": "Screenshot 2024-06-12 at 11.26.10 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 109228, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F078H4K4BJL/screenshot_2024-06-12_at_11.26.10___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F078H4K4BJL/download/screenshot_2024-06-12_at_11.26.10___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F078H4K4BJL-c1d52d9ba7/screenshot_2024-06-12_at_11.26.10___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F078H4K4BJL-c1d52d9ba7/screenshot_2024-06-12_at_11.26.10___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F078H4K4BJL-c1d52d9ba7/screenshot_2024-06-12_at_11.26.10___am_360.png", "thumb_360_w": 307, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F078H4K4BJL-c1d52d9ba7/screenshot_2024-06-12_at_11.26.10___am_480.png", "thumb_480_w": 410, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F078H4K4BJL-c1d52d9ba7/screenshot_2024-06-12_at_11.26.10___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F078H4K4BJL-c1d52d9ba7/screenshot_2024-06-12_at_11.26.10___am_720.png", "thumb_720_w": 614, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F078H4K4BJL-c1d52d9ba7/screenshot_2024-06-12_at_11.26.10___am_800.png", "thumb_800_w": 800, "thumb_800_h": 938, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F078H4K4BJL-c1d52d9ba7/screenshot_2024-06-12_at_11.26.10___am_960.png", "thumb_960_w": 819, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F078H4K4BJL-c1d52d9ba7/screenshot_2024-06-12_at_11.26.10___am_1024.png", "thumb_1024_w": 874, "thumb_1024_h": 1024, "original_w": 1128, "original_h": 1322, "thumb_tiny": "AwAwACjSPXrS0fhRQAgz60p6dcUgz6UpoAQfXNLQKKAE79aWiigBB9c0Hp1xSjPeg+1ACDHY5paBnvRQAh60tHNFACDHY0HpzSjPeg+1ACAjtS0DPeigD//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F078H4K4BJL/screenshot_2024-06-12_at_11.26.10___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F078H4K4BJL-64cb1d695f", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "55No6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I'm still seeing some stray "}, {"type": "text", "text": "NaN", "style": {"code": true}}, {"type": "text", "text": " values on some of the equity amounts in "}, {"type": "text", "text": "new-meritview", "style": {"code": true}}, {"type": "text", "text": ", can you run the SQL update to see if that fixes it?"}]}]}]}, {"ts": "1718302897.959479", "text": "I'm seeing some changes in the equity features in `new-meritview` but it's clear that not everything has been connected (Units toggle doesn't appear in all views, Equity conversion rate is not applied from Settings). Should I file bugs on this or wait if engineering is still working on it? <@U04DKEFP1K8> <@U0690EB5JE5>", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5X/Ub", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'm seeing some changes in the equity features in "}, {"type": "text", "text": "new-meritview", "style": {"code": true}}, {"type": "text", "text": " but it's clear that not everything has been connected (Units toggle doesn't appear in all views, Equity conversion rate is not applied from Settings). Should I file bugs on this or wait if engineering is still working on it? "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}]}]}]}, {"ts": "1718303027.547719", "text": "For setting equity conversion rate via UI please raise a jira", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "6V4DJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For setting equity conversion rate via UI please raise a jira"}]}]}]}, {"ts": "1718303116.744669", "text": "Work is already in progress to implement equity toggle ( eng will extend it to ensure it is implemented across diff views)", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "w4qjq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Work is already in progress to implement equity toggle ( eng will extend it to ensure it is implemented across diff views)"}]}]}]}, {"ts": "1718325945.387599", "text": "<@U04DKEFP1K8> <@U065H3M6WJV> I see that Equity toggle work seems more than I thought from the message above. And not sure if we will be to close this by your Friday morning. Will try our best.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1718325966.000000"}, "blocks": [{"type": "rich_text", "block_id": "WP0EF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I see that Equity toggle work seems more than I thought from the message above. And not sure if we will be to close this by your Friday morning. Will try our best."}]}]}]}, {"ts": "1718714062.440579", "text": "<@U065H3M6WJV> FYI... We have a lot of tickets in `In Review` state as of now as the changes are being reviewed and tested. Equity/currency conversion is not working properly and I am closely working with engineers to test as much as I can.\nHowever there is still some confusion on Equity feature which I will discuss with you. We will  have a good number of fixes pushed to new-meritview by your Wednesday morning. cc: <@U04DKEFP1K8>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1718714201.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "JLYoV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " FYI... We have a lot of tickets in "}, {"type": "text", "text": "In Review", "style": {"code": true}}, {"type": "text", "text": " state as of now as the changes are being reviewed and tested. Equity/currency conversion is not working properly and I am closely working with engineers to test as much as I can.\nHowever there is still some confusion on Equity feature which I will discuss with you. We will  have a good number of fixes pushed to new-meritview by your Wednesday morning. cc: "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1718754481.047989", "text": "<@U065H3M6WJV> do you remember in which document we had recorded application suport window details( i remember we has discussed official support time of 9am - 5pm PST 5 days a week)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1718754481.047989", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "cAm35", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " do you remember in which document we had recorded application suport window details( i remember we has discussed official support time of 9am - 5pm PST 5 days a week)"}]}]}]}, {"ts": "1718759440.286909", "text": "<@U04DKEFP1K8> I see that this ticket is set to highest priority\n<https://compiify.atlassian.net/browse/COM-3340>\nWhen is this expected? This seems like a big change, Will discuss with you if we can handle with current behaviour itself.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1718759440.286909", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "ZXyMB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I see that this ticket is set to highest priority\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3340"}, {"type": "text", "text": "\nWhen is this expected? This seems like a big change, Will discuss with you if we can handle with current behaviour itself."}]}]}]}, {"ts": "1718827465.105159", "text": "I tried to create a new cycle today in `new-meritview` env and it's not working. :confused:\n\n<@U04DKEFP1K8> is there a known issue that would prevent new cycles being launched? Or can you help me figure out why the \"Mid-Year Cycle\" that is set to \"Active\" status cannot be viewed? (I can't get the budget allocation page to load either)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1718827465.105159", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "QpR8r", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I tried to create a new cycle today in "}, {"type": "text", "text": "new-meritview", "style": {"code": true}}, {"type": "text", "text": " env and it's not working. "}, {"type": "emoji", "name": "confused", "unicode": "1f615"}, {"type": "text", "text": "\n\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " is there a known issue that would prevent new cycles being launched? Or can you help me figure out why the \"Mid-Year Cycle\" that is set to \"Active\" status cannot be viewed? (I can't get the budget allocation page to load either)"}]}]}]}, {"ts": "1718838957.047129", "text": "<@U065H3M6WJV> can you please review <https://compiify.atlassian.net/browse/COM-3340>. requirements added in this jira to allow granular performance rating in the product for CWA", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1718838957.047129", "reply_count": 2, "edited": {"user": "U04DKEFP1K8", "ts": "1718838991.000000"}, "reactions": [{"name": "muscle", "users": ["U04DS2MBWP4"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13401::e2c3e7c02e9111efab59e75b32d98389", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3340?atlOrigin=eyJpIjoiZTRlZTIyMTIzMjM0NGI0ZDgzYjQyZjE0OTY3YWQ3ODYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3340 Allow granular performance ratings for cainwatters environment>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13401::e2c40ed02e9111efab59e75b32d98389", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2a19465d953b8640b8f3558fd4f15da9?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13401::e2c3e7c12e9111efab59e75b32d98389", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13401\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13401\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3340", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "WXMxG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " can you please review "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3340"}, {"type": "text", "text": ". requirements added in this jira to allow granular performance rating in the product for CWA"}]}]}]}, {"ts": "1718925972.093439", "text": "<@U065H3M6WJV> what is latest update from candid? i see they have provided employee and compensation data long time back. Are we still tracking them?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1718925972.093439", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "ZRDej", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " what is latest update from candid? i see they have provided employee and compensation data long time back. Are we still tracking them?"}]}]}]}, {"ts": "1719255765.365529", "text": "<@U065H3M6WJV> Can you please make me the owner of eng discussion series starting tomorrow", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1719255765.365529", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "y6Bmy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Can you please make me the owner of eng discussion series starting tomorrow"}]}]}]}, {"ts": "1719261319.206199", "text": "<@U04DKEFP1K8> what's the Epic number for Merit 2.0 issues that <PERSON> has raised. Is it this one?\n<https://compiify.atlassian.net/browse/COM-2341>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719261319.206199", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12402::46656620326911ef9187bb4686506007", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2341?atlOrigin=eyJpIjoiOTY1MmU1MmQxMGRjNDVjMWFjZGI3YWYwZWJlNGJhMjciLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2341 Merit View V2 Feedback>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12402::46656622326911ef9187bb4686506007", "elements": [{"type": "mrkdwn", "text": "Status: *In Development*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/082e10750208b91e38660f50da682c58?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12402::46656621326911ef9187bb4686506007", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12402\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12402\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2341", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Zl1xK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what's the Epic number for Merit 2.0 issues that <PERSON> has raised. Is it this one?\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2341"}]}]}]}, {"ts": "1719261342.596699", "text": "<@U04DKEFP1K8> do you want to meet now for going over valgenesis env?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719261342.596699", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "HXyGZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " do you want to meet now for going over valgenesis env?"}]}]}]}, {"ts": "1719261551.194239", "text": "how do I login to test the Merit 2.0?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719261551.194239", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "OwN5f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "how do I login to test the Merit 2.0?"}]}]}]}, {"ts": "1719262331.158409", "text": "<@U0690EB5JE5> can we pls add equity in tomorrow eng discussion as well? I need to understand what changes we are making, what else needs to be done in order to meet <PERSON><PERSON>'s requrements. Thanks", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1719262383.000000"}, "blocks": [{"type": "rich_text", "block_id": "oRTkC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we pls add equity in tomorrow eng discussion as well? I need to understand what changes we are making, what else needs to be done in order to meet <PERSON><PERSON>'s requrements. Thanks"}]}]}]}, {"ts": "1719262679.493269", "text": "<@U065H3M6WJV> were previous calls with Valgenesis recorded? if so, can you share the recordings?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719262679.493269", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "jJNpl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " were previous calls with <PERSON><PERSON> recorded? if so, can you share the recordings?"}]}]}]}, {"ts": "1719263661.500879", "text": "<@U065H3M6WJV> Is valgenesis pushing performance ratings from CA to hibob?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719263661.500879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "+XyJc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Is valgenesis pushing performance ratings from CA to hibob?"}]}]}]}, {"ts": "1719327662.672459", "text": "<@U065H3M6WJV> last week you had mentioned that you have completed about 80% of the QA testing. What are the functionalities that are yet to be QA tested. I know equity is not tested yet. What else is remaining?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4HlXA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " last week you had mentioned that you have completed about 80% of the QA testing. What are the functionalities that are yet to be QA tested. I know equity is not tested yet. What else is remaining?"}]}]}]}, {"ts": "1719328694.740679", "text": "For the Jira tickets, that are listed as done in Jira, are they already QA  RE-TESTED by <PERSON> or do we still need to re-test them for accuracy?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719328694.740679", "reply_count": 2, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "pb7kH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For the Jira tickets, that are listed as done in Jira, are they already QA  RE-TESTED by <PERSON> or do we still need to re-test them for accuracy?"}]}]}]}, {"ts": "1719413211.192139", "text": "<@U04DKEFP1K8> I am not seeing the dry run invites for CWA and Nauto as we discussed in yesterday's eng call. I thought you said you were sending the invites during the meeting itself.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719413211.192139", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "iH2MF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I am not seeing the dry run invites for CWA and <PERSON><PERSON> as we discussed in yesterday's eng call. I thought you said you were sending the invites during the meeting itself."}]}]}]}, {"ts": "1719414440.742369", "text": "<@U04DKEFP1K8> where can I find the raw data set that is used for the new merit view?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719414440.742369", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "M8tON", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " where can I find the raw data set that is used for the new merit view?"}]}]}]}, {"ts": "1719414545.984939", "text": "also is there an option to see the hierarchy in the merit planning view? I can't seem to easily find it even though I am logged in as CEO", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "byMG8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "also is there an option to see the hierarchy in the merit planning view? I can't seem to easily find it even though I am logged in as CEO"}]}]}]}, {"ts": "1719414917.062759", "text": "<@U065H3M6WJV> <@U0690EB5JE5> when I view direct reports in merit view, I get a list of 17 people. When I apply manager filter in the org view to see my direct reports, I get a list of only 10 people. Any idea why this discrepancy?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719414917.062759", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "P9c/D", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " when I view direct reports in merit view, I get a list of 17 people. When I apply manager filter in the org view to see my direct reports, I get a list of only 10 people. Any idea why this discrepancy?"}]}]}]}, {"ts": "1719415073.284869", "text": "<@U04DS2MBWP4> I would recommend to explore and go over this in a meeting as there would be back and forth.  ", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1719415084.000000"}, "blocks": [{"type": "rich_text", "block_id": "cB8DA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I would recommend to explore and go over this in a meeting as there would be back and forth.  "}]}]}]}, {"ts": "1719415152.854569", "text": "Thats what I am finding out. We will have to do most of this live.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "HU2pY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thats what I am finding out. We will have to do most of this live."}]}]}]}, {"ts": "1719417313.711229", "text": "<@U065H3M6WJV> <@U04DKEFP1K8> How do we take care of pre-filling guidelines as in case of dynamic perf rating", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1719417313.711229", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "1719418548.000000"}, "files": [{"id": "F079W8VJVK4", "mode": "tombstone"}], "blocks": [{"type": "rich_text", "block_id": "1plVb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " How do we take care of pre-filling guidelines as in case of dynamic perf rating"}]}]}]}, {"ts": "1719417405.233059", "text": "Now everything is hardcoded, as we are working on supporting customer configurable perf ratings and those will be dynamic.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VXuM/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Now everything is hardcoded, as we are working on supporting customer configurable perf ratings and those will be dynamic."}]}]}]}, {"ts": "1719417451.508599", "text": "<@U0690EB5JE5> which country is AR? I did not see any employees in the org view under this country. will show in the eng call what I mean", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719417451.508599", "reply_count": 4, "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Y6WWX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " which country is AR? I did not see any employees in the org view under this country. will show in the eng call what I mean"}]}]}]}, {"ts": "1719419888.530169", "text": "<!here> FYI... There were quite a few fixes and performance improvements pushed to `<https://new-meritview.compiify.com/>` I am still validating some of the fixes pushed. Will do more testing tomorrow and be ready to merge changes to customer test ENVs.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "MgqUw", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " FYI... There were quite a few fixes and performance improvements pushed to "}, {"type": "link", "url": "https://new-meritview.compiify.com/", "style": {"code": true}}, {"type": "text", "text": " I am still validating some of the fixes pushed. Will do more testing tomorrow and be ready to merge changes to customer test ENVs."}]}]}]}, {"ts": "1719420699.987309", "text": "<@U065H3M6WJV> this is <@U071FN2589Y>’s doc on People insights calculation logic. Its not that structured. Please review and comment\n<https://docs.google.com/document/d/1RTbo30sJVdfbgDZxj8YyM5oOtj6-dfrIZe4htDzsrOw/edit?addon_store#heading=h.jw8oqqz90dqt>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1719420699.987309", "reply_count": 5, "files": [{"id": "F079WKSC1L2", "created": 1719420704, "timestamp": 1719420704, "name": "Data Validation", "title": "Data Validation", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 40613, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1RTbo30sJVdfbgDZxj8YyM5oOtj6-dfrIZe4htDzsrOw", "external_url": "https://docs.google.com/document/d/1RTbo30sJVdfbgDZxj8YyM5oOtj6-dfrIZe4htDzsrOw/edit?addon_store#heading=h.jw8oqqz90dqt", "url_private": "https://docs.google.com/document/d/1RTbo30sJVdfbgDZxj8YyM5oOtj6-dfrIZe4htDzsrOw/edit?addon_store#heading=h.jw8oqqz90dqt", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F079WKSC1L2-d00d859ceb/data_validation_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1035, "thumb_tiny": "AwAwACXTopCM0bR6UAGfY0tFFABRRRQAjfdNC/h+FKelIB70ALRRRQAUUUUAFAGKKKACiiigAooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F079WKSC1L2/data_validation", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "F/VSm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " this is "}, {"type": "user", "user_id": "U071FN2589Y"}, {"type": "text", "text": "’s doc on People insights calculation logic. Its not that structured. Please review and comment\n"}, {"type": "link", "url": "https://docs.google.com/document/d/1RTbo30sJVdfbgDZxj8YyM5oOtj6-dfrIZe4htDzsrOw/edit?addon_store#heading=h.jw8oqqz90dqt"}]}]}]}, {"ts": "1719428535.231639", "text": "For the tickets that were moved to \"In QA\" today, did you want me to try to verify those? <@U0690EB5JE5>\n\nIt might cause disruptions to other entries/statuses if <@U04DS2MBWP4> is also actively testing, so just want to make sure that's not gonna be an issue.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VqkfC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For the tickets that were moved to \"In QA\" today, did you want me to try to verify those? "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "\n\nIt might cause disruptions to other entries/statuses if "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " is also actively testing, so just want to make sure that's not gonna be an issue."}]}]}]}, {"ts": "1719431642.287849", "text": "<@U065H3M6WJV> I am not doing any new testing while you are here so please go ahead and verify those.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "QE3ZM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I am not doing any new testing while you are here so please go ahead and verify those."}]}]}]}, {"ts": "1719434956.080919", "text": "<@U065H3M6WJV> can you please confirm  expected behavior for following scenario if for an employee who is  in submitted state\n1. salary in local currency is modified \n2. performance rating recommendation is modified ( no flag raised)\n3. performance rating recommendation is modified and new recommendation raises a flag\n4. currency exchange rate is modified \n5. performance rating is modified", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1719434956.080919", "reply_count": 14, "edited": {"user": "U04DKEFP1K8", "ts": "1719435039.000000"}, "blocks": [{"type": "rich_text", "block_id": "XNW0J", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " can you please confirm  expected behavior for following scenario if for an employee who is  in submitted state\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "salary in local currency is modified "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "performance rating recommendation is modified ( no flag raised)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "performance rating recommendation is modified and new recommendation raises a flag"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "currency exchange rate is modified "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "performance rating is modified"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1719440020.580269", "text": "Cycle builder in `new-meritview` is now failing on the Bonus award section, may block some testing.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1719440020.580269", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "gr+yM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle builder in "}, {"type": "text", "text": "new-meritview", "style": {"code": true}}, {"type": "text", "text": " is now failing on the Bonus award section, may block some testing."}]}]}]}, {"ts": "1719446173.965319", "text": "<@U065H3M6WJV> <PERSON> (CEO) impersonating as <PERSON><PERSON>, a manager who reports to <PERSON>. <PERSON> is seeing the the 4 charts in org view for all departments and levels. I assume <PERSON> should not see these 4 charts for the whole org but only for her direct reports. Is that correct?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719446173.965319", "reply_count": 2, "files": [{"id": "F07ABBRTZ41", "created": 1719446118, "timestamp": 1719446118, "name": "Screenshot 2024-06-26 at 4.52.56 PM.png", "title": "Screenshot 2024-06-26 at 4.52.56 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 411357, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07ABBRTZ41/screenshot_2024-06-26_at_4.52.56___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07ABBRTZ41/download/screenshot_2024-06-26_at_4.52.56___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 189, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 252, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 379, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 421, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 505, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ABBRTZ41-9e1ffc084f/screenshot_2024-06-26_at_4.52.56___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 539, "original_w": 3282, "original_h": 1726, "thumb_tiny": "AwAZADDRPOOoxzTQgxjJ6YpzEjbjucUuRjORTAb5fOcnqDQygkc4xTgwJIHamvKqHBznGelACbBjG49AKds5zk9c1C87c7AAOMZqRJQxIPBzgc9aNQuPowPSjuKD2pAHHpRx6UHrR2oAMD0owPSkal70Af/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07ABBRTZ41/screenshot_2024-06-26_at_4.52.56___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07ABBRTZ41-09dfa2513c", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "vX1Q7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " <PERSON> (CEO) impersonating as <PERSON><PERSON>, a manager who reports to <PERSON>. <PERSON> is seeing the the 4 charts in org view for all departments and levels. I assume <PERSON> should not see these 4 charts for the whole org but only for her direct reports. Is that correct?"}]}]}]}, {"ts": "1719446412.051649", "text": "<PERSON> (CEO) impersonating as <PERSON><PERSON>, a manager who reports to <PERSON>. When <PERSON> submit a comments on behalf of <PERSON><PERSON>, the comment box is showing <PERSON><PERSON> as comment submitted. I'd think the submitted name should show as \"<PERSON> on behalf of <PERSON><PERSON>\". Thoughts?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719446412.051649", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "YDnTd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> (CEO) impersonating as <PERSON><PERSON>, a manager who reports to <PERSON>. When <PERSON> submit a comments on behalf of <PERSON><PERSON>, the comment box is showing <PERSON><PERSON> as comment submitted. I'd think the submitted name should show as \"<PERSON> on behalf of <PERSON><PERSON>\". Thoughts?"}]}]}]}, {"ts": "1719447674.802799", "text": "'", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MzIum", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "'"}]}]}]}, {"ts": "1719514878.412879", "text": "Hey folks <!here> - did anyone input these multi-million dollar values into the `new-meritview` environment in the last few days?\n\nAs far as I can tell, these were automatically increased to ridiculous amounts by the system, because these are not my inputs. I had seen something like this previously when doing bulk data uploads during an active cycle, but can't find a reliable way to reproduce the issue. I don't know if there's any other way to trace the source of the numbers?\n\nI've filed a bug for it, with 'Highest' priority: <https://compiify.atlassian.net/browse/COM-3371>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1719514878.412879", "reply_count": 7, "files": [{"id": "F07ASTH30BA", "created": 1719514852, "timestamp": 1719514852, "name": "Screenshot 2024-06-27 at 11.46.31 AM.png", "title": "Screenshot 2024-06-27 at 11.46.31 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 565748, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07ASTH30BA/screenshot_2024-06-27_at_11.46.31___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07ASTH30BA/download/screenshot_2024-06-27_at_11.46.31___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_360.png", "thumb_360_w": 360, "thumb_360_h": 197, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_480.png", "thumb_480_w": 480, "thumb_480_h": 263, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_720.png", "thumb_720_w": 720, "thumb_720_h": 395, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_800.png", "thumb_800_w": 800, "thumb_800_h": 439, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_960.png", "thumb_960_w": 960, "thumb_960_h": 527, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 562, "original_w": 3716, "original_h": 2038, "thumb_tiny": "AwAaADDSpaTijAoAWikwKCoNAC0neiigAPSiiloAO1JQaKACloooA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07ASTH30BA/screenshot_2024-06-27_at_11.46.31___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07ASTH30BA-9dc4f7bf8e", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F07A44CU1J6", "created": 1719514858, "timestamp": 1719514858, "name": "Screenshot 2024-06-27 at 11.46.58 AM.png", "title": "Screenshot 2024-06-27 at 11.46.58 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 576668, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07A44CU1J6/screenshot_2024-06-27_at_11.46.58___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07A44CU1J6/download/screenshot_2024-06-27_at_11.46.58___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_360.png", "thumb_360_w": 360, "thumb_360_h": 198, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_480.png", "thumb_480_w": 480, "thumb_480_h": 264, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_720.png", "thumb_720_w": 720, "thumb_720_h": 396, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_800.png", "thumb_800_w": 800, "thumb_800_h": 440, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_960.png", "thumb_960_w": 960, "thumb_960_h": 529, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 564, "original_w": 3716, "original_h": 2046, "thumb_tiny": "AwAaADDSpabgHrS7R6UALRSYFHFAC0nejiigAPSl7UlLQAUUUUAIaUUUUAf/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07A44CU1J6/screenshot_2024-06-27_at_11.46.58___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07A44CU1J6-5bbb574427", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13432::a3591ac034b711ef805489012a4dc1a4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3371?atlOrigin=eyJpIjoiMzA0YmFmZjJhNGNlNGI4OTkzN2RiZDk1Njk3NTRhODQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3371 Multi-million dollar increases are appearing for employees, possibl…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13432::a3591ac234b711ef805489012a4dc1a4", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13432::a3591ac134b711ef805489012a4dc1a4", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13432\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13432\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3371", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "q4mhf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey folks "}, {"type": "broadcast", "range": "here"}, {"type": "text", "text": " - did anyone input these multi-million dollar values into the "}, {"type": "text", "text": "new-meritview", "style": {"code": true}}, {"type": "text", "text": " environment in the last few days?\n\nAs far as I can tell, these were automatically increased to ridiculous amounts by the system, because these are not my inputs. I had seen something like this previously when doing bulk data uploads during an active cycle, but can't find a reliable way to reproduce the issue. I don't know if there's any other way to trace the source of the numbers?\n\nI've filed a bug for it, with 'Highest' priority: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3371"}]}]}]}, {"ts": "1719516243.521519", "text": "<@U04DKEFP1K8> what's the issue with Vercara? I am getting the same error", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "G0QaX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what's the issue with <PERSON><PERSON><PERSON><PERSON>? I am getting the same error"}]}]}]}, {"ts": "1719516318.941039", "text": "download is working for other reports except \" full employee data export\"", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "APlf0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "download is working for other reports except \" full employee data export\""}]}]}]}, {"ts": "1719527605.425239", "text": "<@U065H3M6WJV> does new meritview has flags enabled in case when a range is provided or target too is supported?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1719527605.425239", "reply_count": 7, "edited": {"user": "U04DKEFP1K8", "ts": "1719527669.000000"}, "blocks": [{"type": "rich_text", "block_id": "KYovd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " does new meritview has flags enabled in case when a range is provided or target too is supported?"}]}]}]}, {"ts": "1719527831.777659", "text": "<@U04DKEFP1K8> when can you send over the calculations doc for <PERSON>'s review if it is not done already?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719527831.777659", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "ip0Fe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " when can you send over the calculations doc for <PERSON>'s review if it is not done already?"}]}]}]}, {"ts": "1719528225.496369", "text": "<@U04DKEFP1K8> can you share the calculations doc that <PERSON><PERSON><PERSON> shared with the  <PERSON>'s comments on it?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719527831.777659", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "FESbr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you share the calculations doc that <PERSON><PERSON><PERSON> shared with the  <PERSON>'s comments on it?"}]}]}]}, {"ts": "1719530799.673839", "text": "<@U065H3M6WJV> can you please start the review for document\n<https://docs.google.com/spreadsheets/d/1T6QJx97GCP3N0okJasa0wg01HVdDhvwZmcP9eeog4pw/edit?gid=0#gid=0>\n\nI am left with Equity and stats section for merit view but rest of the data can be reviewed", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1719530799.673839", "reply_count": 3, "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "0fkCn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " can you please start the review for document\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1T6QJx97GCP3N0okJasa0wg01HVdDhvwZmcP9eeog4pw/edit?gid=0#gid=0"}, {"type": "text", "text": "\n\nI am left with Equity and stats section for merit view but rest of the data can be reviewed"}]}]}]}]}