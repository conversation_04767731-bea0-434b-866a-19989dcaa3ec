{"date": "2024-09-27", "channel_id": "C065QSSNH8A", "message_count": 12, "messages": [{"ts": "1727461693.126129", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> can we pls respond to Valgenesis by EOD today? Our goal is to respond to customers within 4 hours but not later than 24 hours especially when we are claiming white glove support to our early customers.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727461693.126129", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "/4gPq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can we pls respond to Valgenesis by EOD today? Our goal is to respond to customers within 4 hours but not later than 24 hours especially when we are claiming white glove support to our early customers."}]}]}]}, {"ts": "1727456732.095529", "text": "Just a quick update on me, I think the worst has passed, but we’re still under tornado watch and we have some severe storms running through. I don’t think I’m gonna be able to get anything done today with all the drama with the weather.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "SxFzJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just a quick update on me, I think the worst has passed, but we’re still under tornado watch and we have some severe storms running through. I don’t think I’m gonna be able to get anything done today with all the drama with the weather."}]}]}]}, {"ts": "1727453639.934719", "text": "Responsive\t599 (LI)\tCall #1 Scheduled\tw/ <PERSON><PERSON>\t                                        Call #1 - <PERSON><PERSON> 10/1 @ 2p\nAlfaTech\t        370 (LI)\tCall #1 Scheduled\tw/ <PERSON>\t                                Call #1 - <PERSON><PERSON><PERSON> 10/10 @ 3p\nFord Direct\t402 (LI)\tCall #1 Scheduled\tw/ <PERSON><PERSON><PERSON>, <PERSON><PERSON>\t        Call #1 - Mon 9/30 @ 11a\nFinanceIt\t318 (LI)\tCall #1 Scheduled\tw/ <PERSON><PERSON><PERSON>, <PERSON>\t        Call #1 - Fri 10/18 @ 2p", "user": "U07NBMXTL1E", "type": "message", "edited": {"user": "U07NBMXTL1E", "ts": "1727454525.000000"}, "blocks": [{"type": "rich_text", "block_id": "Vx+78", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Responsive\t599 (LI)\tCall #1 Scheduled\tw/ <PERSON><PERSON>\t                                        Call #1 - <PERSON><PERSON> 10/1 @ 2p\nAlfaTech\t        370 (LI)\tCall #1 Scheduled\tw/ <PERSON>\t                                Call #1 - <PERSON><PERSON><PERSON> 10/10 @ 3p\nFord Direct\t402 (LI)\tCall #1 Scheduled\tw/ <PERSON><PERSON><PERSON>, <PERSON><PERSON>\t        Call #1 - Mon 9/30 @ 11a\nFinanceIt\t318 (LI)\tCall #1 Scheduled\tw/ <PERSON><PERSON><PERSON>, <PERSON>\t        Call #1 - Fri 10/18 @ 2p"}]}]}]}, {"ts": "1727452856.321269", "text": "<@U04DKEFP1K8> Issues fixed today. all of them are issues raised by <PERSON>\n<https://compiify.atlassian.net/browse/COM-3649>,\n<https://compiify.atlassian.net/browse/COM-3654>,\n<https://compiify.atlassian.net/browse/COM-3653>,\n<https://compiify.atlassian.net/browse/COM-3651>\nAlso there was a fix pushed for Bands issue\nIts both data and code bugs. Please follow the instructions in <https://github.com/Compiify/Yellowstone/pull/1586|PR> to fix the issue", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727452856.321269", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "1727456782.000000"}, "blocks": [{"type": "rich_text", "block_id": "8lULl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Issues fixed today. all of them are issues raised by <PERSON>\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3649"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3654"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3653"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3651"}, {"type": "text", "text": "\nAlso there was a fix pushed for Bands issue\nIts both data and code bugs. Please follow the instructions in "}, {"type": "link", "url": "https://github.com/Compiify/Yellowstone/pull/1586", "text": "PR"}, {"type": "text", "text": " to fix the issue"}]}]}]}, {"ts": "1727452619.360889", "text": "stay safe and take care <@U07EJ2LP44S>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727404512.622239", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "3/O+y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "stay safe and take care "}, {"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1727452546.941169", "text": "<!here> Let's use this zoom link for the leadership meeting\n<https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZfBlD", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Let's use this zoom link for the leadership meeting\n"}, {"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1727452344.736629", "text": "<@U04DKEFP1K8> org view is still not showing charts. Do you know if <PERSON> was able to make the data changes (locations, name change for marketing) for the demo data?", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1727452432.000000"}, "blocks": [{"type": "rich_text", "block_id": "fvOPG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " org view is still not showing charts. Do you know if <PERSON> was able to make the data changes (locations, name change for marketing) for the demo data?"}]}]}]}, {"ts": "1727450876.106889", "text": "You may want to use a different link for the call in 30. I don’t think I’m going to be available yet.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727450876.106889", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "TGkTh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "You may want to use a different link for the call in 30. I don’t think I’m going to be available yet."}]}]}]}, {"ts": "1727407377.430219", "text": "<@U04DKEFP1K8> not sure if you were in the demo data meeting today when we we identified the issues around filters and shifting of columns, not persisting after refreshing the screen. Are you able to explain that to <PERSON><PERSON><PERSON> during your call tonight or let me know I can join the call for five minutes to explain those issues", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727407377.430219", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "ITzxR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " not sure if you were in the demo data meeting today when we we identified the issues around filters and shifting of columns, not persisting after refreshing the screen. Are you able to explain that to <PERSON><PERSON><PERSON> during your call tonight or let me know I can join the call for five minutes to explain those issues"}]}]}]}, {"ts": "1727404512.622239", "text": "Just a heads up to everyone, this hurricane is looking pretty bad and our area is expecting some pretty strong winds, tornadoes, and a lot of rain overnight and tomorrow morning. We will be fine, but we may lose power and I may have issues getting online tomorrow. It could be fine, but just a heads up. Importantly, though, we are fully prepared to brew coffee with no power. :rolling_on_the_floor_laughing: ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727404512.622239", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "LddVr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just a heads up to everyone, this hurricane is looking pretty bad and our area is expecting some pretty strong winds, tornadoes, and a lot of rain overnight and tomorrow morning. We will be fine, but we may lose power and I may have issues getting online tomorrow. It could be fine, but just a heads up. Importantly, though, we are fully prepared to brew coffee with no power. "}, {"type": "emoji", "name": "rolling_on_the_floor_laughing", "unicode": "1f923"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1727386788.693989", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> For some reasons, 3 of the charts are not showing up in demo env. Is it because of the data upload issues?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727386788.693989", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "sP+7q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " For some reasons, 3 of the charts are not showing up in demo env. Is it because of the data upload issues?"}]}]}]}, {"ts": "1727383120.772719", "text": "<@U07EJ2LP44S> It looks like <PERSON>gen<PERSON><PERSON><PERSON> has been waiting for a reply from you.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727383120.772719", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "RJYYU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " It looks like <PERSON><PERSON><PERSON><PERSON><PERSON> has been waiting for a reply from you."}]}]}]}], "created_at": "2025-05-22T21:35:34.654928"}