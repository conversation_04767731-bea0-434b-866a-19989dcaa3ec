{"date": "2024-02-17", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1708117100.406219", "text": "<@U04DKEFP1K8> For <https://drive.google.com/drive/u/0/folders/1iaKZEim_JEPRY0GkDeExi5t6hWpitKh_|Nauto>, we now have Employee data, Compensation data, and Salary bands. I think Equity will also be added, but let's start reviewing what they've provided and flag anything that will need special handling in upload. I already asked them to specify which \"tier\" to use for salary bands, separate from \"location\", so that may be similar to what we did for SDF.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708117100.406219", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "gcz1J", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For "}, {"type": "link", "url": "https://drive.google.com/drive/u/0/folders/1iaKZEim_JEPRY0GkDeExi5t6hWpitKh_", "text": "<PERSON><PERSON>"}, {"type": "text", "text": ", we now have Employee data, Compensation data, and Salary bands. I think Equity will also be added, but let's start reviewing what they've provided and flag anything that will need special handling in upload. I already asked them to specify which \"tier\" to use for salary bands, separate from \"location\", so that may be similar to what we did for SDF."}]}]}]}], "created_at": "2025-05-22T21:35:34.582068"}