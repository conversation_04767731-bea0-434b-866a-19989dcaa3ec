#!/usr/bin/env python3
"""
Quick script to set up user and tenant for testing.
"""

import os
import sys
import django

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant, UserProfile

def setup_user():
    """Set up user and tenant for testing."""
    print("🔧 Setting up user and tenant...")
    
    # Create or get user
    user, created = User.objects.get_or_create(
        username='mahesh',
        defaults={
            'email': '<EMAIL>',
            'is_superuser': True,
            'is_staff': True,
            'is_active': True
        }
    )
    
    # Always set the password to ensure it's correct
    user.set_password('admin123')
    user.save()
    
    if created:
        print('✅ Created user: mahesh')
    else:
        print('✅ Updated existing user: mahesh')
    
    # Create or get tenant
    tenant, created = Tenant.objects.get_or_create(
        slug='test-tenant',
        defaults={'name': 'Test Tenant', 'is_active': True}
    )
    
    if created:
        print('✅ Created tenant: test-tenant')
    else:
        print('✅ Tenant already exists: test-tenant')
    
    # Ensure user profile exists
    profile, created = UserProfile.objects.get_or_create(
        user=user,
        defaults={'tenant': tenant}
    )
    
    if created:
        print('✅ Created user profile')
    else:
        print('✅ User profile already exists')
    
    print('\n🎉 Setup complete!')
    print('📋 Login credentials:')
    print('   Username: mahesh')
    print('   Password: admin123')
    print('   Email: <EMAIL>')
    print('\n🌐 Access URLs:')
    print('   Login: http://127.0.0.1:8000/accounts/login/')
    print('   Search: http://127.0.0.1:8000/search/')

if __name__ == "__main__":
    setup_user()
