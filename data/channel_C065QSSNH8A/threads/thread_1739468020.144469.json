{"thread_ts": "**********.144469", "channel_id": "C065QSSNH8A", "reply_count": 14, "replies": [{"ts": "1739468626.657419", "text": "<@U07EJ2LP44S> Please share some more details on the new column. Also list of employees to be included in the bonus cycle ENV.\n\nAlso anything to be taken care.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.144469", "blocks": [{"type": "rich_text", "block_id": "7WwCp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please share some more details on the new column. Also list of employees to be included in the bonus cycle ENV.\n\nAlso anything to be taken care."}]}]}]}, {"ts": "**********.895159", "text": "If we could create a new env called curanahealthMIP that would be idea", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.144469", "files": [{"id": "F08D87KLTDG", "created": **********, "timestamp": **********, "name": "CuranaMIPcycle.xlsx", "title": "CuranaMIPcycle.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 62787, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08D87KLTDG/curanamipcycle.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08D87KLTDG/download/curanamipcycle.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D87KLTDG-ad7661524e/curanamipcycle_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D87KLTDG-ad7661524e/curanamipcycle_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08D87KLTDG/curanamipcycle.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08D87KLTDG-cea3e77914", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "IRFJ4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If we could create a new env called curanahealthMIP that would be idea"}]}]}]}, {"ts": "**********.880519", "text": "And I'm not sure what the best approach is but since we can't have the same email address in two accounts (I think?( we can append these email addresses with something like +MIP as well", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.144469", "blocks": [{"type": "rich_text", "block_id": "k4Z6Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And I'm not sure what the best approach is but since we can't have the same email address in two accounts (I think?( we can append these email addresses with something like +MIP as well"}]}]}]}, {"ts": "**********.865479", "text": "<@U07EJ2LP44S> what is MIP", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.144469", "blocks": [{"type": "rich_text", "block_id": "PPFve", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " what is MIP"}]}]}]}, {"ts": "**********.220849", "text": "Management incentive plan", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.144469", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "D+fFp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Management incentive plan"}]}]}]}, {"ts": "**********.488199", "text": "<@U07EJ2LP44S> I am working on this. Will have ENV ready by Monday. I can make the SSO work as well.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.144469", "blocks": [{"type": "rich_text", "block_id": "8Q4ce", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am working on this. Will have ENV ready by Monday. I can make the SSO work as well."}]}]}]}, {"ts": "1739536543.530779", "text": "<@U07EJ2LP44S> Actually I was able to bring up the ENV and upload the file above after adding a missing manager", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.144469", "files": [{"id": "F08E0UVS2AC", "created": 1739536534, "timestamp": 1739536534, "name": "Curana - <PERSON><PERSON><PERSON>_Employee_Data_Filtered_bonus.csv", "title": "Curana - <PERSON><PERSON><PERSON>_Employee_Data_Filtered_bonus.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": true, "size": 66300, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08E0UVS2AC/curana_-_curana_employee_data_filtered_bonus.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08E0UVS2AC/download/curana_-_curana_employee_data_filtered_bonus.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08E0UVS2AC/curana_-_curana_employee_data_filtered_bonus.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08E0UVS2AC-3f39f6c84e", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08E0UVS2AC/curana_-_curana_employee_data_filtered_bonus.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">NC</div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\">HEATHER</div><div class=\"cm-col\">HOWELLS</div><div class=\"cm-col\">HEATHER HOWELLS</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">03/17/14</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">2054</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">03/17/14</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">VA</div><div class=\"cm-col\">SVP, Partner Success</div><div class=\"cm-col\">Plan Management</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">216000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">25</div><div class=\"cm-col cm-num\">54000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">54000</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">54000</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">03/22/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">NC</div><div class=\"cm-col cm-num\">39</div><div class=\"cm-col\">ROBYN</div><div class=\"cm-col\">HOPKINS</div><div class=\"cm-col\">ROBYN HOPKINS</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">09/16/15</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">09/16/15</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">KS</div><div class=\"cm-col\">Executive Director</div><div class=\"cm-col\">Plan Management</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">94000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col cm-num\">14100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">14100</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">14100</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">03/22/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">NC</div><div class=\"cm-col cm-num\">67</div><div class=\"cm-col\">MONICA</div><div class=\"cm-col\">THOMASON</div><div class=\"cm-col\">MONICA THOMASON</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">04/11/16</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">04/11/16</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">GA</div><div class=\"cm-col\">Executive Director</div><div class=\"cm-col\">Plan Management</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">95000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col cm-num\">14250</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">14250</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">14250</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">03/22/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">NC</div><div class=\"cm-col cm-num\">8050</div><div class=\"cm-col\">JENNIFER</div><div class=\"cm-col\">ELAM</div><div class=\"cm-col\">JENNIFER ELAM</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">01/25/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">2054</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">01/25/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">TN</div><div class=\"cm-col\">Executive Director</div><div class=\"cm-col\">Plan Management</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">162225</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">20</div><div class=\"cm-col cm-num\">32445</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">32445</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">32445</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">03/22/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">NC</div><div class=\"cm-col cm-num\">16</div><div class=\"cm-col\">ASHLEY</div><div class=\"cm-col\">IVILL</div><div class=\"cm-col\">ASHLEY IVILL</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">01/01/15</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">3331</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">01/01/15</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">VA</div><div class=\"cm-col\">Director, Learning and Development</div><div class=\"cm-col\">Learning &amp; Development</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Shared Services</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">125725.6</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col cm-num\">18858.84</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">18858.84</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">18858.84</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">03/22/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 201, "lines_more": 200, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "8cM83", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Actually I was able to bring up the ENV and upload the file above after adding a missing manager"}]}]}]}, {"ts": "**********.021259", "text": "<https://curanahealthmip.stridehr.io/organization>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.144469", "attachments": [{"from_url": "https://curanahealthmip.stridehr.io/organization", "service_icon": "https://curanahealthmip.stridehr.io/apple-touch-icon.png", "id": 1, "original_url": "https://curanahealthmip.stridehr.io/organization", "fallback": "Stride", "text": "Web site created using create-react-app", "title": "Stride", "title_link": "https://curanahealthmip.stridehr.io/organization", "service_name": "curanahealthmip.stridehr.io"}], "blocks": [{"type": "rich_text", "block_id": "nJKzw", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://curanahealthmip.stridehr.io/organization"}]}]}]}, {"ts": "**********.253309", "text": "resharing: <https://curanahealthmip.stridehr.io/organization> is ready", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.144469", "subtype": "thread_broadcast", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "5N5yW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "resharing: "}, {"type": "link", "url": "https://curanahealthmip.stridehr.io/organization"}, {"type": "text", "text": " is ready"}]}]}]}, {"ts": "**********.942339", "text": "<@U0690EB5JE5> Is SSO already working or do I need to created a local login for their admin team? (<PERSON>, <PERSON>, <PERSON>)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.144469", "edited": {"user": "U07EJ2LP44S", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "RarPg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Is SSO already working or do I need to created a local login for their admin team? (<PERSON>, <PERSON>, <PERSON>)"}]}]}]}, {"ts": "**********.086789", "text": "we still need to create logins. Will do that for admins on Monday. Also who are recommenders? subset from the main one?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.144469", "blocks": [{"type": "rich_text", "block_id": "XjGYF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we still need to create logins. Will do that for admins on Monday. Also who are recommenders? subset from the main one?"}]}]}]}, {"ts": "**********.907069", "text": "They were able to get into the account, and they actually already set the recommenders list in there", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.144469", "blocks": [{"type": "rich_text", "block_id": "Ul9jU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They were able to get into the account, and they actually already set the recommenders list in there"}]}]}]}, {"ts": "**********.072869", "text": "Awesome ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.144469", "blocks": [{"type": "rich_text", "block_id": "uQScM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Awesome "}]}]}]}, {"ts": "**********.713809", "text": "<@U07EJ2LP44S> Recommenders should be able to SSO if <PERSON> was. Could you please confirm the same with them?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.144469", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "5qJ71", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Recommenders should be able to SSO if <PERSON> was. Could you please confirm the same with them?"}]}]}]}], "created_at": "2025-05-22T22:00:49.364660"}