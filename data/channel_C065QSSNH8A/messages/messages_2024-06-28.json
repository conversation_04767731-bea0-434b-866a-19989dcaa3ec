{"date": "2024-06-28", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1719530799.673839", "text": "<@U065H3M6WJV> can you please start the review for document\n<https://docs.google.com/spreadsheets/d/1T6QJx97GCP3N0okJasa0wg01HVdDhvwZmcP9eeog4pw/edit?gid=0#gid=0>\n\nI am left with Equity and stats section for merit view but rest of the data can be reviewed", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1719530799.673839", "reply_count": 3, "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "0fkCn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " can you please start the review for document\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1T6QJx97GCP3N0okJasa0wg01HVdDhvwZmcP9eeog4pw/edit?gid=0#gid=0"}, {"type": "text", "text": "\n\nI am left with Equity and stats section for merit view but rest of the data can be reviewed"}]}]}]}, {"ts": "1719528225.496369", "text": "<@U04DKEFP1K8> can you share the calculations doc that <PERSON><PERSON><PERSON> shared with the  <PERSON>'s comments on it?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719527831.777659", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "FESbr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you share the calculations doc that <PERSON><PERSON><PERSON> shared with the  <PERSON>'s comments on it?"}]}]}]}, {"ts": "1719527831.777659", "text": "<@U04DKEFP1K8> when can you send over the calculations doc for <PERSON>'s review if it is not done already?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1719527831.777659", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "ip0Fe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " when can you send over the calculations doc for <PERSON>'s review if it is not done already?"}]}]}]}, {"ts": "1719527605.425239", "text": "<@U065H3M6WJV> does new meritview has flags enabled in case when a range is provided or target too is supported?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1719527605.425239", "reply_count": 7, "edited": {"user": "U04DKEFP1K8", "ts": "1719527669.000000"}, "blocks": [{"type": "rich_text", "block_id": "KYovd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " does new meritview has flags enabled in case when a range is provided or target too is supported?"}]}]}]}, {"ts": "1719516318.941039", "text": "download is working for other reports except \" full employee data export\"", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "APlf0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "download is working for other reports except \" full employee data export\""}]}]}]}, {"ts": "1719516243.521519", "text": "<@U04DKEFP1K8> what's the issue with Vercara? I am getting the same error", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "G0QaX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what's the issue with <PERSON><PERSON><PERSON><PERSON>? I am getting the same error"}]}]}]}, {"ts": "1719514878.412879", "text": "Hey folks <!here> - did anyone input these multi-million dollar values into the `new-meritview` environment in the last few days?\n\nAs far as I can tell, these were automatically increased to ridiculous amounts by the system, because these are not my inputs. I had seen something like this previously when doing bulk data uploads during an active cycle, but can't find a reliable way to reproduce the issue. I don't know if there's any other way to trace the source of the numbers?\n\nI've filed a bug for it, with 'Highest' priority: <https://compiify.atlassian.net/browse/COM-3371>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1719514878.412879", "reply_count": 7, "files": [{"id": "F07ASTH30BA", "created": 1719514852, "timestamp": 1719514852, "name": "Screenshot 2024-06-27 at 11.46.31 AM.png", "title": "Screenshot 2024-06-27 at 11.46.31 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 565748, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07ASTH30BA/screenshot_2024-06-27_at_11.46.31___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07ASTH30BA/download/screenshot_2024-06-27_at_11.46.31___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_360.png", "thumb_360_w": 360, "thumb_360_h": 197, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_480.png", "thumb_480_w": 480, "thumb_480_h": 263, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_720.png", "thumb_720_w": 720, "thumb_720_h": 395, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_800.png", "thumb_800_w": 800, "thumb_800_h": 439, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_960.png", "thumb_960_w": 960, "thumb_960_h": 527, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07ASTH30BA-6d1be5c7c6/screenshot_2024-06-27_at_11.46.31___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 562, "original_w": 3716, "original_h": 2038, "thumb_tiny": "AwAaADDSpaTijAoAWikwKCoNAC0neiigAPSiiloAO1JQaKACloooA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07ASTH30BA/screenshot_2024-06-27_at_11.46.31___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07ASTH30BA-9dc4f7bf8e", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F07A44CU1J6", "created": 1719514858, "timestamp": 1719514858, "name": "Screenshot 2024-06-27 at 11.46.58 AM.png", "title": "Screenshot 2024-06-27 at 11.46.58 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 576668, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07A44CU1J6/screenshot_2024-06-27_at_11.46.58___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07A44CU1J6/download/screenshot_2024-06-27_at_11.46.58___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_360.png", "thumb_360_w": 360, "thumb_360_h": 198, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_480.png", "thumb_480_w": 480, "thumb_480_h": 264, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_720.png", "thumb_720_w": 720, "thumb_720_h": 396, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_800.png", "thumb_800_w": 800, "thumb_800_h": 440, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_960.png", "thumb_960_w": 960, "thumb_960_h": 529, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07A44CU1J6-9bb8009584/screenshot_2024-06-27_at_11.46.58___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 564, "original_w": 3716, "original_h": 2046, "thumb_tiny": "AwAaADDSpabgHrS7R6UALRSYFHFAC0nejiigAPSl7UlLQAUUUUAIaUUUUAf/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07A44CU1J6/screenshot_2024-06-27_at_11.46.58___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07A44CU1J6-5bbb574427", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13432::a3591ac034b711ef805489012a4dc1a4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3371?atlOrigin=eyJpIjoiMzA0YmFmZjJhNGNlNGI4OTkzN2RiZDk1Njk3NTRhODQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3371 Multi-million dollar increases are appearing for employees, possibl…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13432::a3591ac234b711ef805489012a4dc1a4", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13432::a3591ac134b711ef805489012a4dc1a4", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13432\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13432\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3371", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "q4mhf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey folks "}, {"type": "broadcast", "range": "here"}, {"type": "text", "text": " - did anyone input these multi-million dollar values into the "}, {"type": "text", "text": "new-meritview", "style": {"code": true}}, {"type": "text", "text": " environment in the last few days?\n\nAs far as I can tell, these were automatically increased to ridiculous amounts by the system, because these are not my inputs. I had seen something like this previously when doing bulk data uploads during an active cycle, but can't find a reliable way to reproduce the issue. I don't know if there's any other way to trace the source of the numbers?\n\nI've filed a bug for it, with 'Highest' priority: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3371"}]}]}]}], "created_at": "2025-05-22T21:35:34.622571"}