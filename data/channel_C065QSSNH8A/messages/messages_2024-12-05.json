{"date": "2024-12-05", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1733411336.131399", "text": "Will take a look. Could be data issue", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "4kvhd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take a look. Could be data issue"}]}]}]}, {"ts": "1733411180.688089", "text": "<@U0690EB5JE5> Will you look at this? I will put a bug in, but is there a step I'm missing along the way? I think everything should be working. <https://www.loom.com/share/fb147f06d1d546b78de8472535651494?sid=038ea0fb-9e17-4e20-b751-2457810f8ca7>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733411180.688089", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "4Mhhk", "video_url": "https://www.loom.com/embed/fb147f06d1d546b78de8472535651494?sid=038ea0fb-9e17-4e20-b751-2457810f8ca7&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/fb147f06d1d546b78de8472535651494-928dd9e9ad15f99b-4x3.jpg", "alt_text": "Troubleshooting Data Discrepancies 👩💻", "title": {"type": "plain_text", "text": "Troubleshooting Data Discrepancies 👩‍💻", "emoji": true}, "title_url": "https://www.loom.com/share/fb147f06d1d546b78de8472535651494", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 2 min  ", "emoji": true}}, {"type": "section", "block_id": "NRtpz", "text": {"type": "mrkdwn", "text": ":information_source: Hey there! I encountered an issue while impersonating <PERSON><PERSON><PERSON> in Diversified Energy. The data for employee count was missing initially, but...", "verbatim": false}}, {"type": "actions", "block_id": "nRBPZ", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/fb147f06d1d546b78de8472535651494?sid=038ea0fb-9e17-4e20-b751-2457810f8ca7"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"fb147f06d1d546b78de8472535651494\",\"videoName\":\"Troubleshooting Data Discrepancies 👩💻\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/fb147f06d1d546b78de8472535651494?sid=038ea0fb-9e17-4e20-b751-2457810f8ca7", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "OW1Tt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Will you look at this? I will put a bug in, but is there a step I'm missing along the way? I think everything should be working. "}, {"type": "link", "url": "https://www.loom.com/share/fb147f06d1d546b78de8472535651494?sid=038ea0fb-9e17-4e20-b751-2457810f8ca7"}]}]}]}, {"ts": "1733343938.091349", "text": "<@U0690EB5JE5> looking for some feedback on how to ingest the data we have for curana re: paybands for hourly employees. I need to confirm with <PERSON><PERSON>na how they want to handle but wanted your technical viewpoint first. <https://www.loom.com/share/e650daaf9daf4d29ba5de6ce2682b84e?sid=b2b92059-062f-4a99-9be0-fc4ec9b56560>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733343938.091349", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "KPVA0", "video_url": "https://www.loom.com/embed/e650daaf9daf4d29ba5de6ce2682b84e?sid=b2b92059-062f-4a99-9be0-fc4ec9b56560&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/e650daaf9daf4d29ba5de6ce2682b84e-f3d3fe3a771a74bd-4x3.jpg", "alt_text": "Understanding Quirana's Pay Bands", "title": {"type": "plain_text", "text": "Understanding Quirana's Pay Bands", "emoji": true}, "title_url": "https://www.loom.com/share/e650daaf9daf4d29ba5de6ce2682b84e", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 2 min  ", "emoji": true}}, {"type": "section", "block_id": "Jyddg", "text": {"type": "mrkdwn", "text": ":information_source: In this video, I discuss Quirana's pay bands, specifically focusing on the hourly and annual compensation data provided. I highlight how hourly...", "verbatim": false}}, {"type": "actions", "block_id": "6lM+q", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/e650daaf9daf4d29ba5de6ce2682b84e?sid=b2b92059-062f-4a99-9be0-fc4ec9b56560"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"e650daaf9daf4d29ba5de6ce2682b84e\",\"videoName\":\"Understanding Quirana's Pay Bands\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/e650daaf9daf4d29ba5de6ce2682b84e?sid=b2b92059-062f-4a99-9be0-fc4ec9b56560", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "92Dnp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " looking for some feedback on how to ingest the data we have for curana re: paybands for hourly employees. I need to confirm with <PERSON><PERSON><PERSON> how they want to handle but wanted your technical viewpoint first. "}, {"type": "link", "url": "https://www.loom.com/share/e650daaf9daf4d29ba5de6ce2682b84e?sid=b2b92059-062f-4a99-9be0-fc4ec9b56560"}]}]}]}, {"ts": "1733342217.835689", "text": "Bug in Alayacare - for compa ratio in the merit view, the system is taking the canadian salary amount (for US employees) and bumping it against the US band, so the compa ratio is incorrect. <https://compiify.atlassian.net/browse/COM-4016>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733342217.835689", "reply_count": 5, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14099::c70fc98cdc8a4b85a2d1b1c8826b61f8", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4016?atlOrigin=eyJpIjoiMDdjNTYwYTZjOTQxNDg1YWFjYzllZjQ1NmQxM2FiZmYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4016 Issue: Incorrect Currency Conversion Calculation for U.S. Employees…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14099::ab19c9b8c2804248847ebd92cfef4be5", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14099\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4016\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4016", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "n45Mc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bug in Alayacare - for compa ratio in the merit view, the system is taking the canadian salary amount (for US employees) and bumping it against the US band, so the compa ratio is incorrect. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4016"}]}]}]}, {"ts": "1733337807.903289", "text": "General question <@U0690EB5JE5>: can we still do promotion uploads in the cycle builder if the customer does not have pay bands? If so, can we leave the Band ID blank?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733337807.903289", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "General question "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": ": can we still do promotion uploads in the cycle builder if the customer does not have pay bands? If so, can we leave the Band ID blank?"}]}]}]}], "created_at": "2025-05-22T21:35:34.681854"}