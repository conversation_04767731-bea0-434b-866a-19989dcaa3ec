{"date": "2024-08-16", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1723832155.980879", "text": "<@U0690EB5JE5> we should a explore if there is a easy way to restrict access to environment during maintenance. just a thought", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1723832155.980879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "KhtvZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we should a explore if there is a easy way to restrict access to environment during maintenance. just a thought"}]}]}]}, {"ts": "1723770450.844349", "text": "<!here> We need to fix following issues asap for Nauto\n• <https://compiify.atlassian.net/browse/COM-3504> (Budget % (used) should exclude equity utilization) - this is an enhancement and not a bug \n• <https://compiify.atlassian.net/browse/COM-3505> (“*Do you have one combined budget for salary increase?*” is set to Yes and Market adjustment is chosen as one of the comp component then column for market adjustment is not visible in merit view table and no adjustment can be input.) - this is a bug", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1723770450.844349", "reply_count": 3, "edited": {"user": "U04DKEFP1K8", "ts": "1723770469.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13565::ea11fb505b6b11ef9b0229fa6cc801bd", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3504?atlOrigin=eyJpIjoiNjJlY2RhZjU1NWQ1NDQ5ZWIxMTkwYjc4MTRjMjI2YTEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3504 Budget % (used) should exclude equity utilization>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13565::ea11fb545b6b11ef9b0229fa6cc801bd", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13565::ea11fb515b6b11ef9b0229fa6cc801bd", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13565\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13565\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3504", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:13566::ea11fb525b6b11ef9b0229fa6cc801bd", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3505?atlOrigin=eyJpIjoiYjJmOTMwMDEwOWJlNDQ5Yjk2MDZmMWViYTNkOTQ2ODMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3505 Enhancement required for option [Do you have one combined budget fo…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13566::ea11fb555b6b11ef9b0229fa6cc801bd", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13566::ea11fb535b6b11ef9b0229fa6cc801bd", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13566\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13566\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3505", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "mT3WJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " We need to fix following issues asap for Nauto\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3504"}, {"type": "text", "text": " (Budget % (used) should exclude equity utilization) - this is an enhancement and not a bug "}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3505"}, {"type": "text", "text": " (“"}, {"type": "text", "text": "Do you have one combined budget for salary increase?", "style": {"bold": true}}, {"type": "text", "text": "” is set to Yes and Market adjustment is chosen as one of the comp component then column for market adjustment is not visible in merit view table and no adjustment can be input.) - this is a bug"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1723755763.335139", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> are we meeting?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1723755763.335139", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "5J6CX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " are we meeting?"}]}]}]}], "created_at": "2025-05-22T21:35:34.628663"}