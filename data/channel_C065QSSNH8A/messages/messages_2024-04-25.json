{"date": "2024-04-25", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1714028980.125589", "text": "<@U065H3M6WJV> <@U04DKEFP1K8> Can we discuss this today in the eng discussion?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1713309339.970349", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "X8ncf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can we discuss this today in the eng discussion?"}]}]}]}, {"ts": "1714008964.458409", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> We should plan to review customer implementation status / action items in addition to any project updates in the next Eng discussion meeting.\n\nSome things I know we have customers waiting on:\n• *Vercara* - TriNet integration option; we may need to move their walkthrough scheduled for 4/29 if their environment is unlikely to be ready by then. (My guess is they will not be able to provide the incremental data even if we get the initial sync by Friday)\n• *Practifi* - Data load. They uploaded CSV to the Google Drive 4/9, we have had 2 weeks and nothing shared back yet. \n• *PlayQ* - Google (social) login. If this is not likely to be ready by Friday morning (Pacific time zone), let's set up a username/password for <PERSON> for now. \n    ◦ Other items captured in <https://compiify.atlassian.net/browse/COM-2799|PlayQ UAT list>\n• *Nauto* - Equity data load; Namely sync. \n    ◦ Other items captured in <https://compiify.atlassian.net/browse/COM-2357|Nauto UAT list>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1714008964.458409", "reply_count": 45, "blocks": [{"type": "rich_text", "block_id": "xWzy0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We should plan to review customer implementation status / action items in addition to any project updates in the next Eng discussion meeting.\n\nSome things I know we have customers waiting on:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Vercar<PERSON>", "style": {"bold": true}}, {"type": "text", "text": " - TriNet integration option; we may need to move their walkthrough scheduled for 4/29 if their environment is unlikely to be ready by then. (My guess is they will not be able to provide the incremental data even if we get the initial sync by Friday)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON>", "style": {"bold": true}}, {"type": "text", "text": " - Data load. They uploaded CSV to the Google Drive 4/9, we have had 2 weeks and nothing shared back yet. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "PlayQ", "style": {"bold": true}}, {"type": "text", "text": " - Google (social) login. If this is not likely to be ready by Friday morning (Pacific time zone), let's set up a username/password for <PERSON> for now. "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Other items captured in "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2799", "text": "PlayQ UAT list"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON>", "style": {"bold": true}}, {"type": "text", "text": " - Equity data load; Namely sync. "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Other items captured in "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2357", "text": "Nauto UAT list"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}, {"ts": "1714008737.438029", "text": "This is a very big milestone for us. Our first integration going live. Congratulations everyone. :tada: :raised_hands: ", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1713994244.339869", "subtype": "thread_broadcast", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "OEn8+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is a very big milestone for us. Our first integration going live. Congratulations everyone. "}, {"type": "emoji", "name": "tada", "unicode": "1f389"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "raised_hands", "unicode": "1f64c"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1713994244.339869", "text": "Our first live customer integration! <PERSON> from Nauto went through the process to connect with <PERSON><PERSON> during today's call. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1713994244.339869", "reply_count": 9, "reactions": [{"name": "tada", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F070WLKKDUZ", "created": 1713994216, "timestamp": 1713994216, "name": "Screenshot 2024-04-24 at 2.10.33 PM.png", "title": "Screenshot 2024-04-24 at 2.10.33 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 472433, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F070WLKKDUZ/screenshot_2024-04-24_at_2.10.33___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F070WLKKDUZ/download/screenshot_2024-04-24_at_2.10.33___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 222, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 296, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 444, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 494, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 593, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F070WLKKDUZ-fd19a543b2/screenshot_2024-04-24_at_2.10.33___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 632, "original_w": 2258, "original_h": 1394, "thumb_tiny": "AwAdADCtS0UUAW7a3ikg3uGJzjipvskBBwrcepNFhxbf8CNTgkodwAPtQBj0UlLQAUUUUAaVh/x7/wDAjVhvun6VjbvlAIzj3IpQ4DAheR/tH/GgBlLRRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F070WLKKDUZ/screenshot_2024-04-24_at_2.10.33___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F070WLKKDUZ-b32dfe5053", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "yllpV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Our first live customer integration! <PERSON> from Nauto went through the process to connect with <PERSON><PERSON> during today's call. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1713985059.638749", "text": "<@U0690EB5JE5> <@U065H3M6WJV> Did you both received an email from Payroll Services &lt;<mailto:<EMAIL>|<EMAIL>>&gt; with subject line: TriNet Integration Center - Compiify?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1713985059.638749", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "nGyoR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Did you both received an email from Payroll Services <"}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": "> with subject line: TriNet Integration Center - Compiify?"}]}]}]}], "created_at": "2025-05-22T21:35:34.602750"}