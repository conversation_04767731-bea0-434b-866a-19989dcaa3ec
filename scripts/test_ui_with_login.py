#!/usr/bin/env python
"""
Test the UI endpoint with proper login.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.search.models import SearchResult

def test_ui_with_login():
    """Test the UI search with proper login."""
    print("🔍 Testing UI Search with Login")
    print("=" * 60)
    
    # Get the user
    try:
        user = User.objects.get(email="<EMAIL>")
        print(f"✅ Found user: {user.email}")
    except User.DoesNotExist:
        print("❌ User not found")
        return False
    
    # Create a test client
    client = Client()
    
    # Log in the user
    client.force_login(user)
    print("✅ User logged in")
    
    # Test the search endpoint
    search_data = {
        'query': 'List issues reported by <PERSON>',
        'sources': 'all',
        'output_format': 'text'
    }
    
    print(f"🔍 Testing search: '{search_data['query']}'")
    
    # Get the count of search results before
    before_count = SearchResult.objects.count()
    print(f"Search results before: {before_count}")
    
    # Make the search request
    try:
        response = client.post('/search/query/', search_data)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Search request successful")
            
            # Check if new search results were created
            after_count = SearchResult.objects.count()
            print(f"Search results after: {after_count}")
            
            if after_count > before_count:
                # Get the latest search result
                latest_result = SearchResult.objects.latest('timestamp')
                print(f"\n📊 Latest search result:")
                print(f"   ID: {latest_result.id}")
                print(f"   Query: {latest_result.search_query.query_text}")
                print(f"   User: {latest_result.user.email}")
                print(f"   Tenant: {latest_result.search_query.tenant.slug}")
                print(f"   Citations: {latest_result.citations.count()}")
                
                if latest_result.citations.count() > 0:
                    print(f"   🎉 CITATIONS WORKING!")
                    return True
                else:
                    print(f"   ❌ No citations found")
                    return False
            else:
                print("❌ No new search results created")
                return False
        
        elif response.status_code == 302:
            print(f"🔄 Redirect to: {response.url}")
            return False
        else:
            print(f"❌ Search request failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_ui_with_login()
    if success:
        print("\n🎉 UI SEARCH IS WORKING WITH CITATIONS!")
    else:
        print("\n❌ UI search is still broken!")
    exit(0 if success else 1)
