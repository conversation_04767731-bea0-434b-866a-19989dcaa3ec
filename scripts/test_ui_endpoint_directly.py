#!/usr/bin/env python
"""
Test the UI endpoint directly to confirm the dimension mismatch issue.
"""
import requests
import json

def test_ui_endpoint():
    """Test the UI search endpoint directly."""
    print("🔍 Testing UI Endpoint Directly")
    print("=" * 60)
    
    # Test data
    search_data = {
        'query': 'List issues reported by <PERSON>',
        'sources': 'all',
        'output_format': 'text'
    }
    
    # Login first (if needed)
    session = requests.Session()
    
    try:
        # Get CSRF token
        print("🔐 Getting CSRF token...")
        response = session.get('http://127.0.0.1:8000/search/')
        if response.status_code != 200:
            print(f"❌ Failed to get search page: {response.status_code}")
            return False
        
        # Extract CSRF token
        csrf_token = None
        for line in response.text.split('\n'):
            if 'csrfmiddlewaretoken' in line and 'value=' in line:
                start = line.find('value="') + 7
                end = line.find('"', start)
                csrf_token = line[start:end]
                break
        
        if not csrf_token:
            print("❌ Could not find CSRF token")
            return False
        
        print(f"✅ Got CSRF token: {csrf_token[:10]}...")
        
        # Add CSRF token to search data
        search_data['csrfmiddlewaretoken'] = csrf_token
        
        # Perform search
        print(f"🔍 Performing search: '{search_data['query']}'")
        response = session.post(
            'http://127.0.0.1:8000/search/query/',
            data=search_data,
            headers={
                'Referer': 'http://127.0.0.1:8000/search/',
                'X-CSRFToken': csrf_token
            }
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            # Check if it's a redirect response
            if 'search-results' in response.text:
                print("✅ Got search results page")
                
                # Check for error messages
                if 'error' in response.text.lower() or 'dimension error' in response.text.lower():
                    print("❌ Found error in response")
                    # Extract error message
                    lines = response.text.split('\n')
                    for line in lines:
                        if 'error' in line.lower() and ('dimension' in line.lower() or '400' in line):
                            print(f"   Error: {line.strip()}")
                    return False
                
                # Check for citations
                citation_indicators = ['citation', 'source', 'permalink']
                citation_found = any(indicator in response.text.lower() for indicator in citation_indicators)
                
                if citation_found:
                    print("✅ Citations appear to be present")
                    return True
                else:
                    print("❌ No citations found in response")
                    return False
            else:
                print("❌ Did not get search results page")
                # Check if it's an error page
                if 'error' in response.text.lower():
                    print("🚨 Error page detected")
                    # Try to extract error message
                    if 'dimension error' in response.text.lower():
                        print("🚨 DIMENSION MISMATCH ERROR CONFIRMED!")
                return False
        else:
            print(f"❌ Search request failed: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_ui_endpoint()
    if success:
        print("\n🎉 UI ENDPOINT IS WORKING!")
    else:
        print("\n❌ UI ENDPOINT IS BROKEN!")
    exit(0 if success else 1)
