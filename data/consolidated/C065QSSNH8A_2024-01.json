{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2024-01", "message_count": 99, "messages": [{"ts": "1704057016.345659", "text": "Today's update: I did some more testing on SDF's instance, to see if I could complete a manager's entire set of recommendations &amp; submission.\n\nI managed to get it into a state where neither the manager nor the admin can update a few employees, so the cycle cannot be completed. :upside_down_face:\n\nI logged the bugs I found along the way, including the ones at the root of this ^ blocking scenario. Updated bug counts are below.\n\n• <https://compiify.atlassian.net/browse/COM-2084|COM-2084: Showstoppers> : 4 issues\n• <https://compiify.atlassian.net/browse/COM-2085|COM-2085: Wave 1> : 24 issues\n• <https://compiify.atlassian.net/browse/COM-2086|COM-2086: Wave 2> : 26 issues\n• <https://compiify.atlassian.net/browse/COM-2087|COM-2087: Wave 3> : 24 issues\n• <https://compiify.atlassian.net/browse/COM-2088|COM-2088: Wave 4> : 29 issues\n", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WU001", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Today's update: I did some more testing on SDF's instance, to see if I could complete a manager's entire set of recommendations & submission.\n\nI managed to get it into a state where neither the manager nor the admin can update a few employees, so the cycle cannot be completed. "}, {"type": "emoji", "name": "upside_down_face", "unicode": "1f643"}, {"type": "text", "text": "\n\nI logged the bugs I found along the way, including the ones at the root of this ^ blocking scenario. Updated bug counts are below.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2084", "text": "COM-2084: Showstoppers"}, {"type": "text", "text": " : 4 issues"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2085", "text": "COM-2085: Wave 1"}, {"type": "text", "text": " : 24 issues"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2086", "text": "COM-2086: Wave 2"}, {"type": "text", "text": " : 26 issues"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "COM-2087: Wave 3"}, {"type": "text", "text": " : 24 issues"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2088", "text": "COM-2088: Wave 4"}, {"type": "text", "text": " : 29 issues"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1704057096.314619", "text": "Things I still haven't been able to test thoroughly:\n• Budget adherence when submitting changes\n• Reports / charts\n• Adjustment letters (I haven't really started looking at these yet)", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Y21Ip", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Things I still haven't been able to test thoroughly:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Budget adherence when submitting changes"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Reports / charts"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Adjustment letters (I haven't really started looking at these yet)"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1704057157.231289", "text": "Phew, this is making me real nervous. ", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1704057157.231289", "reply_count": 6, "reactions": [{"name": "aaaaaa", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "eLFY1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Phew, this is making me real nervous. "}]}]}]}, {"ts": "1704208181.693529", "text": "<@U04DS2MBWP4> we have a leadership meeting at 10am PST today, we will discuss today's update and then i need time to discuss remaining items with <PERSON>. I do not have any update related to OKR's , lets do that next week.", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "face_with_monocle", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "6wyRZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " we have a leadership meeting at 10am PST today, we will discuss today's update and then i need time to discuss remaining items with <PERSON>. I do not have any update related to OKR's , lets do that next week."}]}]}]}, {"ts": "1704231838.776749", "text": "<@U04DKEFP1K8> One thing we didn't discuss yet this week is our plan for Neuroflow. We need to figure out our approach here prior to the call on Friday.\n• Do we want to go ahead and load their data into a production instance prior to Friday?\n• Is there anything missing from their data that we need to ask them for this week?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1704231838.776749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "+TXG7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " One thing we didn't discuss yet this week is our plan for Neuroflow. We need to figure out our approach here prior to the call on Friday.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we want to go ahead and load their data into a production instance prior to Friday?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Is there anything missing from their data that we need to ask them for this week?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1704393126.288799", "text": "Had a check-in with DA this morning:\n• They had layoffs in December. :scream: \n    ◦ Affected about 25 employees. <PERSON> will update the EmployeeDataTemplate to mark these employees as \"Inactive.\" <@U04DKEFP1K8> Can you confirm that doing this will correctly prevent those employees from being shown when we re-load their data into production?\n• Comp cycle schedule hasn't changed. :spiral_calendar_pad: \n    ◦ Budget is supposed to be finalized next week on Jan 12\n• Captured some of their requirements/wish list that might not be implemented yet:\n    ◦ In addition to flagging individual employees outside of salary guideline ranges, they want a global flag for any employee with a salary increase over 9% and one for any employe with a bonus over 9%, because Emnet always reviews these cases individually.\n    ◦ Reports: Budget use &amp; which departments were under budget; reports to check for \"disparate impact\" by gender (we won't support \"age\" reports this cycle); discrepancies between performance ratings and increase %s", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1704393126.288799", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "XfeHn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Had a check-in with DA this morning:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They had layoffs in December. "}, {"type": "emoji", "name": "scream", "unicode": "1f631"}, {"type": "text", "text": " "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Affected about 25 employees. <PERSON> will update the EmployeeDataTemplate to mark these employees as \"Inactive.\" "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can you confirm that doing this will correctly prevent those employees from being shown when we re-load their data into production?"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Comp cycle schedule hasn't changed. "}, {"type": "emoji", "name": "spiral_calendar_pad", "unicode": "1f5d3-fe0f"}, {"type": "text", "text": " "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Budget is supposed to be finalized next week on Jan 12"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Captured some of their requirements/wish list that might not be implemented yet:"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In addition to flagging individual employees outside of salary guideline ranges, they want a global flag for any employee with a salary increase over 9% and one for any employe with a bonus over 9%, because Emnet always reviews these cases individually."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Reports: Budget use & which departments were under budget; reports to check for \"disparate impact\" by gender (we won't support \"age\" reports this cycle); discrepancies between performance ratings and increase %s"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}, {"ts": "**********.521809", "text": "Finally back in the states y'all", "user": "U0658EW4B8D", "type": "message", "thread_ts": "**********.521809", "reply_count": 3, "reactions": [{"name": "+1", "users": ["U065H3M6WJV", "U04DS2MBWP4", "U04DKEFP1K8"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "r4lv3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Finally back in the states y'all"}]}]}]}, {"ts": "**********.541549", "text": "<@U065H3M6WJV> Just FYI, I've shared the staging account login info with <PERSON>. Let me know if you've have any objections to it or anticipate any issues with him starting to use it.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.541549", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "oNvwQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Just FYI, I've shared the staging account login info with <PERSON>. Let me know if you've have any objections to it or anticipate any issues with him starting to use it."}]}]}]}, {"ts": "**********.694929", "text": "Could use some quick feedback on the different <https://docs.google.com/presentation/d/12_HxgsHlmGRtSmCMrmnx27mvao23S9GeLBKZgj5EayI/edit#slide=id.g2ac69e51c64_0_83|UX ideas> we could use for integrations. See options 1,2,3 at the end of the deck. I think I'm leaning toward something like \"Option 2\" but lmk what you think. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.694929", "reply_count": 2, "files": [{"id": "F06CM1TM1M3", "created": **********, "timestamp": **********, "name": "Integrations UX - examples", "title": "Integrations UX - examples", "mimetype": "application/vnd.google-apps.presentation", "filetype": "gpres", "pretty_type": "Google Slides", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 9592, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "12_HxgsHlmGRtSmCMrmnx27mvao23S9GeLBKZgj5EayI", "external_url": "https://docs.google.com/presentation/d/12_HxgsHlmGRtSmCMrmnx27mvao23S9GeLBKZgj5EayI/edit#slide=id.g2ac69e51c64_0_83", "url_private": "https://docs.google.com/presentation/d/12_HxgsHlmGRtSmCMrmnx27mvao23S9GeLBKZgj5EayI/edit#slide=id.g2ac69e51c64_0_83", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CM1TM1M3-d2be51c80a/integrations_ux_-_examples_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CM1TM1M3-d2be51c80a/integrations_ux_-_examples_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CM1TM1M3-d2be51c80a/integrations_ux_-_examples_360.png", "thumb_360_w": 360, "thumb_360_h": 203, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CM1TM1M3-d2be51c80a/integrations_ux_-_examples_480.png", "thumb_480_w": 480, "thumb_480_h": 270, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CM1TM1M3-d2be51c80a/integrations_ux_-_examples_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CM1TM1M3-d2be51c80a/integrations_ux_-_examples_720.png", "thumb_720_w": 720, "thumb_720_h": 405, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CM1TM1M3-d2be51c80a/integrations_ux_-_examples_800.png", "thumb_800_w": 800, "thumb_800_h": 450, "original_w": 800, "original_h": 450, "thumb_tiny": "AwAbADDTooprbv4QD9aAHUVHmX+6v50oMndV/OgB9FNXdzuA9qdQAUUUUAFFFFABRRRQB//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06CM1TM1M3/integrations_ux_-_examples", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "EPNBg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Could use some quick feedback on the different "}, {"type": "link", "url": "https://docs.google.com/presentation/d/12_HxgsHlmGRtSmCMrmnx27mvao23S9GeLBKZgj5EayI/edit#slide=id.g2ac69e51c64_0_83", "text": "UX ideas"}, {"type": "text", "text": " we could use for integrations. See options 1,2,3 at the end of the deck. I think I'm leaning toward something like \"Option 2\" but lmk what you think. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1704500210.967749", "text": "this is how it shows in Hubspot", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1704500210.967749", "reply_count": 9, "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F06CSJM8CJ0", "created": 1704500198, "timestamp": 1704500198, "name": "Screenshot 2024-01-05 at 4.15.49 PM.png", "title": "Screenshot 2024-01-05 at 4.15.49 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 446733, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06CSJM8CJ0/screenshot_2024-01-05_at_4.15.49___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06CSJM8CJ0/download/screenshot_2024-01-05_at_4.15.49___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CSJM8CJ0-426ea5cc9c/screenshot_2024-01-05_at_4.15.49___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CSJM8CJ0-426ea5cc9c/screenshot_2024-01-05_at_4.15.49___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CSJM8CJ0-426ea5cc9c/screenshot_2024-01-05_at_4.15.49___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 183, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CSJM8CJ0-426ea5cc9c/screenshot_2024-01-05_at_4.15.49___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 244, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CSJM8CJ0-426ea5cc9c/screenshot_2024-01-05_at_4.15.49___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CSJM8CJ0-426ea5cc9c/screenshot_2024-01-05_at_4.15.49___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 366, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CSJM8CJ0-426ea5cc9c/screenshot_2024-01-05_at_4.15.49___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 406, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CSJM8CJ0-426ea5cc9c/screenshot_2024-01-05_at_4.15.49___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 488, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CSJM8CJ0-426ea5cc9c/screenshot_2024-01-05_at_4.15.49___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 520, "original_w": 4352, "original_h": 2210, "thumb_tiny": "AwAYADC19jg/ufqaX7HB/c/U1MKdQBX+xW/9z9TR9it/7n6mrFFAFc2UGfu4/Gni2hAwEH481LiigBBS0gpaACiiigAooooA/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06CSJM8CJ0/screenshot_2024-01-05_at_4.15.49___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06CSJM8CJ0-3a3d0c2d8a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06CHFYL3LN", "created": 1704500202, "timestamp": 1704500202, "name": "Screenshot 2024-01-05 at 4.15.58 PM.png", "title": "Screenshot 2024-01-05 at 4.15.58 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 358147, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06CHFYL3LN/screenshot_2024-01-05_at_4.15.58___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06CHFYL3LN/download/screenshot_2024-01-05_at_4.15.58___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHFYL3LN-53390668f5/screenshot_2024-01-05_at_4.15.58___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHFYL3LN-53390668f5/screenshot_2024-01-05_at_4.15.58___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHFYL3LN-53390668f5/screenshot_2024-01-05_at_4.15.58___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 155, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHFYL3LN-53390668f5/screenshot_2024-01-05_at_4.15.58___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 206, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHFYL3LN-53390668f5/screenshot_2024-01-05_at_4.15.58___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHFYL3LN-53390668f5/screenshot_2024-01-05_at_4.15.58___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 309, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHFYL3LN-53390668f5/screenshot_2024-01-05_at_4.15.58___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 343, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHFYL3LN-53390668f5/screenshot_2024-01-05_at_4.15.58___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 412, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHFYL3LN-53390668f5/screenshot_2024-01-05_at_4.15.58___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 440, "original_w": 4304, "original_h": 1848, "thumb_tiny": "AwAUADDRHSl6Ugp1ABRRRQAhUE8iloooAQUtIKWgAooooAKKKKAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06CHFYL3LN/screenshot_2024-01-05_at_4.15.58___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06CHFYL3LN-e0fd7424e5", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "cBxX6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "this is how it shows in Hubspot"}]}]}]}, {"ts": "1704500516.824359", "text": "<@U04DKEFP1K8> we don't have google analytics for the website.. Is that right?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1704500516.824359", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "XDSTl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " we don't have google analytics for the website.. Is that right?"}]}]}]}, {"ts": "1704644945.441309", "text": "<@U065H3M6WJV> Here is the latest updates for Showstopper and Wave 1 jira's", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1704644945.441309", "reply_count": 1, "files": [{"id": "F06CC7PRXPH", "created": 1704644936, "timestamp": 1704644936, "name": "Wave1Jira.xlsx", "title": "Wave1Jira.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 19028, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06CC7PRXPH/wave1jira.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06CC7PRXPH/download/wave1jira.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CC7PRXPH-41768468bf/wave1jira_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CC7PRXPH-41768468bf/wave1jira_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06CC7PRXPH/wave1jira.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06CC7PRXPH-f561fe670b", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06CPRNDP5K", "created": 1704644940, "timestamp": 1704644940, "name": "ShowstopperJira.xlsx", "title": "ShowstopperJira.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 12872, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06CPRNDP5K/showstopperjira.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06CPRNDP5K/download/showstopperjira.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CPRNDP5K-ed2d1a3abf/showstopperjira_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CPRNDP5K-ed2d1a3abf/showstopperjira_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06CPRNDP5K/showstopperjira.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06CPRNDP5K-d0224babf7", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "5Dd5Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Here is the latest updates for Showstopper and Wave 1 jira's"}]}]}]}, {"ts": "1704729448.372889", "text": "Are we having daily R&amp;D sync this week?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1704729448.372889", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Ifept", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Are we having daily R&D sync this week?"}]}]}]}, {"ts": "1704752492.549389", "text": "<@U04DS2MBWP4> From our call last week for Google SSO setup, did you have a full Zoom recording or just the Otter transcript? (there are some screens that weren't captured in the Otter version)", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Fslnl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " From our call last week for Google SSO setup, did you have a full Zoom recording or just the Otter transcript? (there are some screens that weren't captured in the Otter version)"}]}]}]}, {"ts": "**********.208859", "text": "Just otter. But I can make you the account admin for our second domain if you want to capture the screenshots", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "2mVOZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just otter. But I can make you the account admin for our second domain if you want to capture the screenshots"}]}]}]}, {"ts": "**********.510609", "text": "Yeah, I was going to ask if I can get access to <http://cfy.app|cfy.app> to do my own walkthrough for screenshots :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.510609", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "xAq8t", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yeah, I was going to ask if I can get access to cfy.app to do my own walkthrough for screenshots "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "**********.881769", "text": "<@U04DS2MBWP4> Can we get a <mailto:<EMAIL>|<EMAIL>> alias set up? Something that distributes to me, <PERSON><PERSON><PERSON><PERSON>, you...?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "l7mZI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Can we get a "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " alias set up? Something that distributes to me, <PERSON><PERSON><PERSON><PERSON>, you...?"}]}]}]}, {"ts": "1704760532.809609", "text": "Yes will set one up. ", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1704760532.809609", "reply_count": 1, "edited": {"user": "U04DS2MBWP4", "ts": "1704760571.000000"}, "blocks": [{"type": "rich_text", "block_id": "itFQG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes will set one up. "}]}]}]}, {"ts": "1704782917.738409", "text": "<@U065H3M6WJV> nice work on putting together the documentation for Google Login Setup", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "meow_thx", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "zRlwY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " nice work on putting together the documentation for Google Login Setup"}]}]}]}, {"ts": "1704844865.784149", "text": "<@U04DS2MBWP4> When you have a minute... do you know what each of these reports was supposed to be for? (Just a short description?)", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F06CZA6V26S", "created": 1704844854, "timestamp": 1704844854, "name": "Screenshot 2024-01-09 at 4.00.44 PM.png", "title": "Screenshot 2024-01-09 at 4.00.44 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 25081, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06CZA6V26S/screenshot_2024-01-09_at_4.00.44___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06CZA6V26S/download/screenshot_2024-01-09_at_4.00.44___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CZA6V26S-44772ec463/screenshot_2024-01-09_at_4.00.44___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CZA6V26S-44772ec463/screenshot_2024-01-09_at_4.00.44___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CZA6V26S-44772ec463/screenshot_2024-01-09_at_4.00.44___pm_360.png", "thumb_360_w": 280, "thumb_360_h": 360, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CZA6V26S-44772ec463/screenshot_2024-01-09_at_4.00.44___pm_160.png", "original_w": 314, "original_h": 403, "thumb_tiny": "AwAwACXRIJP3iKNp/vH8hQwXPOM/Wm4j9R+dADwMDrmimAIeAQfxp2xfT9aAFooooAQkA9Cfwo3D+6fyoJbPAz+NGW/uj86AE3D+6fyp1Jlv7o/OlGccjFABRRRQAhDZ4IH4Uoz3OaQrk9SPoaNvu350ALRQBgdSfrS0AJRS0UAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06CZA6V26S/screenshot_2024-01-09_at_4.00.44___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06CZA6V26S-dc2bf7469a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "RwWYR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " When you have a minute... do you know what each of these reports was supposed to be for? (Just a short description?)"}]}]}]}, {"ts": "1704846041.377639", "text": "Report includes Average Salary Increase\nReport  includes Post Comp Cycle Closing Report that has all the updated data including the performance and potential ratings. Performnance ratings do not change from pre comp to post comp cycle\nReport includes old and new comp ratio for each employee along with all other standard fields\nReport includes post Comp Cycle Closing Report that has all the updated data.\nReport  includes Pre Comp Cycle data including the performance and potential  ratings.\nReport includes gender only (no race)\n\nHere is the excel file that has all the downloadable reports, if you have't already seen it", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1704846093.000000"}, "blocks": [{"type": "rich_text", "block_id": "Wu58H", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Report includes Average Salary Increase\nReport  includes Post Comp Cycle Closing Report that has all the updated data including the performance and potential ratings. Performnance ratings do not change from pre comp to post comp cycle\nReport includes old and new comp ratio for each employee along with all other standard fields\nReport includes post Comp Cycle Closing Report that has all the updated data.\nReport  includes Pre Comp Cycle data including the performance and potential  ratings.\nReport includes gender only (no race)\n\nHere is the excel file that has all the downloadable reports, if you have't already seen it"}]}]}]}, {"ts": "1704846149.011009", "text": "Here are some of the metrics that might be important for each persona", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F06D8F0MWLC", "created": 1704846145, "timestamp": 1704846145, "name": "Comp Process Personas and Indicators.xlsx", "title": "Comp Process Personas and Indicators.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 9150, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06D8F0MWLC/comp_process_personas_and_indicators.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06D8F0MWLC/download/comp_process_personas_and_indicators.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F06D8F0MWLC-de5120f53a/comp_process_personas_and_indicators_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F06D8F0MWLC-de5120f53a/comp_process_personas_and_indicators_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06D8F0MWLC/comp_process_personas_and_indicators.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06D8F0MWLC-208bfb491b", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "snpeb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here are some of the metrics that might be important for each persona"}]}]}]}, {"ts": "1704856594.663439", "text": "Thanks - this is helpful background. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0FG3E", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks - this is helpful background. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1704856639.471269", "text": "One question: So far I don't think any of the customers or advisors we've talked to have mentioned having a formal way to measure \"potential.\" Is that something we expect some customers to have as a structured value that can be used to compare employees?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fyo/c", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "One question: So far I don't think any of the customers or advisors we've talked to have mentioned having a formal way to measure \"potential.\" Is that something we expect some customers to have as a structured value that can be used to compare employees?"}]}]}]}, {"ts": "1704860247.135109", "text": "<@U04DS2MBWP4> <https://docs.google.com/spreadsheets/d/1dCu8W86ymgfJz8DWPKheim8UIEV_-Zw-LzYdOqHPpbg/edit#gid=82760926|Here's the list> from today's brainstorm about roadmap items. I added a short description for each except for \"Advanced analytics.\" Can you add that one, and review the others?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1704860247.135109", "reply_count": 1, "reactions": [{"name": "white_check_mark", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06D3PXJ42H", "created": 1704860249, "timestamp": 1704860249, "name": "Roadmap items", "title": "Roadmap items", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1dCu8W86ymgfJz8DWPKheim8UIEV_-Zw-LzYdOqHPpbg", "external_url": "https://docs.google.com/spreadsheets/d/1dCu8W86ymgfJz8DWPKheim8UIEV_-Zw-LzYdOqHPpbg/edit#gid=82760926", "url_private": "https://docs.google.com/spreadsheets/d/1dCu8W86ymgfJz8DWPKheim8UIEV_-Zw-LzYdOqHPpbg/edit#gid=82760926", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06D3PXJ42H-584b611c77/roadmap_items_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06D3PXJ42H-584b611c77/roadmap_items_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06D3PXJ42H-584b611c77/roadmap_items_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06D3PXJ42H-584b611c77/roadmap_items_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06D3PXJ42H-584b611c77/roadmap_items_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06D3PXJ42H-584b611c77/roadmap_items_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06D3PXJ42H-584b611c77/roadmap_items_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06D3PXJ42H-584b611c77/roadmap_items_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06D3PXJ42H-584b611c77/roadmap_items_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHT700kg9Kd3pjY3c9fpQA4kjFAbJ6UhGQMUKCOtADqKKKAE70x/vU/vTG+9xQA8DgUuKTnAxS80AFFFFACd6aVJOafRQADgUUUUAFFFFAH/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06D3PXJ42H/roadmap_items", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "80Tia", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1dCu8W86ymgfJz8DWPKheim8UIEV_-Zw-LzYdOqHPpbg/edit#gid=82760926", "text": "Here's the list"}, {"type": "text", "text": " from today's brainstorm about roadmap items. I added a short description for each except for \"Advanced analytics.\" Can you add that one, and review the others?"}]}]}]}, {"ts": "1705018235.123009", "text": "BTW, because I hadn't updated them here in a while:\n• <https://compiify.atlassian.net/browse/COM-2084|Showstoppers> - 100% Done! :white_check_mark: \n• <https://compiify.atlassian.net/browse/COM-2085|Wave 1 issues> - 96% Done (but really, 100% after we merge code today) :white_check_mark: \n• <https://compiify.atlassian.net/browse/COM-2086|Wave 2 issues> - 24% Done (and another 38% in progress) :chart_with_upwards_trend: \n• <https://compiify.atlassian.net/browse/COM-2087|Wave 3 issues> - 1 of 26 done (not really started yet)\n• <https://compiify.atlassian.net/browse/COM-2088|Wave 4 issues> - 0 of 30 done (not started yet)", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "rocket", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "heart", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ZSAEP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "BTW, because I hadn't updated them here in a while:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2084", "text": "Showstoppers"}, {"type": "text", "text": " - 100% Done! "}, {"type": "emoji", "name": "white_check_mark", "unicode": "2705"}, {"type": "text", "text": " "}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2085", "text": "Wave 1 issues"}, {"type": "text", "text": " - 96% Done (but really, 100% after we merge code today) "}, {"type": "emoji", "name": "white_check_mark", "unicode": "2705"}, {"type": "text", "text": " "}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2086", "text": "Wave 2 issues"}, {"type": "text", "text": " - 24% Done (and another 38% in progress) "}, {"type": "emoji", "name": "chart_with_upwards_trend", "unicode": "1f4c8"}, {"type": "text", "text": " "}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3 issues"}, {"type": "text", "text": " - 1 of 26 done (not really started yet)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2088", "text": "Wave 4 issues"}, {"type": "text", "text": " - 0 of 30 done (not started yet)"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1705078651.609009", "text": "FYI, I will be out from 9 to 12 but fully available via phone and slack. I just won't be in front of my computer", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "C/FXk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "FYI, I will be out from 9 to 12 but fully available via phone and slack. I just won't be in front of my computer"}]}]}]}, {"ts": "1705078753.931709", "text": "<!here> I will be unavailable on this Sunday", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Q69Xo", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I will be unavailable on this Sunday"}]}]}]}, {"ts": "1705099015.234569", "text": "<@U04DS2MBWP4> <PERSON><PERSON> has fe question about MSA and DPA", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XYK/5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " <PERSON><PERSON> has fe question about MSA and DPA"}]}]}]}, {"ts": "1705099065.553229", "text": "I am on my way to pick up is<PERSON>ka. I can join the call by phone if needed or we can note on the questions and I can respond.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vcK8Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am on my way to pick up is<PERSON>ka. I can join the call by phone if needed or we can note on the questions and I can respond."}]}]}]}, {"ts": "1705099106.490749", "text": "we will take the  question and you can respond later", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yNbKV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we will take the  question and you can respond later"}]}]}]}, {"ts": "1705099121.033939", "text": "Ok", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PfDzz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok"}]}]}]}, {"ts": "1705100598.296629", "text": "Felt like a great meeting with <PERSON><PERSON>. Got unblocked on the login question, and they have somewhat simplified what we need to do on the equity data (although that'll need some dev work still).", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705100598.296629", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "WuzBq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Felt like a great meeting with <PERSON><PERSON>. Got unblocked on the login question, and they have somewhat simplified what we need to do on the equity data (although that'll need some dev work still)."}]}]}]}, {"ts": "1705100619.127579", "text": "nice. love it. LFG!", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sWwZb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "nice. love it. LFG!"}]}]}]}, {"ts": "1705245639.176289", "text": "<@U065H3M6WJV> are we able to fully support the hourly employees? It looks like it's a gap in Assemble from the marketing audit report.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1705245639.176289", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "QByyr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " are we able to fully support the hourly employees? It looks like it's a gap in Assemble from the marketing audit report."}]}]}]}, {"ts": "1705246556.086259", "text": "<@U04DKEFP1K8> , currently both <https://compiify.com/> and <https://www.compiify.com/> are accessible and ranking separately from a marketing perspsective.. Can we implement a redirect for all traffic to use the www- address or the non-www address to avoid both confusion and potential duplicate content warnings from search engines? What's the ETA?", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"from_url": "https://compiify.com/", "id": 1, "original_url": "https://compiify.com/", "fallback": "Compiify", "text": "Compiify software", "title": "Compiify", "title_link": "https://compiify.com/", "service_name": "compiify.com"}, {"from_url": "https://www.compiify.com/", "id": 2, "original_url": "https://www.compiify.com/", "fallback": "Compiify", "text": "Compiify software", "title": "Compiify", "title_link": "https://www.compiify.com/", "service_name": "compiify.com"}], "blocks": [{"type": "rich_text", "block_id": "gYWvl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " , currently both "}, {"type": "link", "url": "https://compiify.com/"}, {"type": "text", "text": " and "}, {"type": "link", "url": "https://www.compiify.com/"}, {"type": "text", "text": " are accessible and ranking separately from a marketing perspsective.. Can we implement a redirect for all traffic to use the www- address or the non-www address to avoid both confusion and potential duplicate content warnings from search engines? What's the ETA?"}]}]}]}, {"ts": "1705247290.219539", "text": "<@U065H3M6WJV> Looks like <PERSON> is completing the 2nd comp survey at the end of this month. Now that we have total rewards portal, could we check if he is open to including us under the total rewards category as well esp now that <PERSON><PERSON> will be using our total rewards", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1705247290.219539", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "XiHcr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Looks like <PERSON> is completing the 2nd comp survey at the end of this month. Now that we have total rewards portal, could we check if he is open to including us under the total rewards category as well esp now that <PERSON><PERSON> will be using our total rewards"}]}]}]}, {"ts": "**********.171229", "text": "<@U04DKEFP1K8> We need to turn on DKIM for our secondtry domain <http://compiifyinc.com|compiifyinc.com> and part of the process includes updating DKIM key throu the domain provider. I am assuming it's AWS? can we do this tomorrow. Here are link to the steps we need to follow:\n<https://support.google.com/a/answer/180504>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.171229", "reply_count": 5, "attachments": [{"from_url": "https://support.google.com/a/answer/180504", "service_icon": "https://support.google.com/favicon.ico", "id": 1, "original_url": "https://support.google.com/a/answer/180504", "fallback": "Turn on DKIM for your domain - Google Workspace Admin Help", "text": "Protect against spoofing &amp; phishing, and help prevent messages from being marked as spamFollow the steps in this article to get your DomainKeys Identified Mail (DKIM) key, add the key to your domain p", "title": "Turn on DKIM for your domain - Google Workspace Admin Help", "title_link": "https://support.google.com/a/answer/180504", "service_name": "support.google.com"}], "blocks": [{"type": "rich_text", "block_id": "Kw8do", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " We need to turn on DKIM for our secondtry domain "}, {"type": "link", "url": "http://compiifyinc.com", "text": "compiifyinc.com"}, {"type": "text", "text": " and part of the process includes updating DKIM key throu the domain provider. I am assuming it's AWS? can we do this tomorrow. Here are link to the steps we need to follow:\n"}, {"type": "link", "url": "https://support.google.com/a/answer/180504"}]}]}]}, {"ts": "**********.584069", "text": "Doing some testing on <PERSON><PERSON><PERSON><PERSON>'s account today, and I've found <https://compiify.atlassian.net/browse/COM-2145|several bugs>. Anything where the math calculation is done incorrectly is a potential showstopper, IMO.\n\nThere are also some known bugs in the the downloadable reports that I've logged under <https://compiify.atlassian.net/browse/COM-1929|SDF testing>, but those would apply to Neuroflow as well.\n\n<@U04DKEFP1K8> Let me know when you have time to review these so we can agree on a plan for eng to tackle. If we want to give Jen &amp; <PERSON> their login info tomorrow, I need to know whether we also have to explain any known issues, and I'd prefer not to have obvious miscalculations as part of the \"known issues.\"\n\n(Also -- I haven't been able to test Adjustment letters on any customer instance yet, so the number of bugs there is still unknown.)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.584069", "reply_count": 5, "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "a/XTh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Doing some testing on <PERSON><PERSON><PERSON><PERSON>'s account today, and I've found "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2145", "text": "several bugs"}, {"type": "text", "text": ". Anything where the math calculation is done incorrectly is a potential showstopper, IMO.\n\nThere are also some known bugs in the the downloadable reports that I've logged under "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1929", "text": "SDF testing"}, {"type": "text", "text": ", but those would apply to Neuroflow as well.\n\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Let me know when you have time to review these so we can agree on a plan for eng to tackle. If we want to give <PERSON> & Megan their login info tomorrow, I need to know whether we also have to explain any known issues, and I'd prefer not to have obvious miscalculations as part of the \"known issues.\"\n\n(Also -- I haven't been able to test Adjustment letters on any customer instance yet, so the number of bugs there is still unknown.)"}]}]}]}, {"ts": "1705353745.301869", "text": "What's the grayed-out/inactive action icon in the middle? (it has no tooltip to explain it)\n\nIt looks to me like some kind of \"send\" or \"share\" action, but I don't know of any reason that would be an available action for a compensation cycle. :thinking_face:", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F06DK3RFXC7", "created": 1705353682, "timestamp": 1705353682, "name": "Screenshot 2024-01-15 at 1.20.37 PM.png", "title": "Screenshot 2024-01-15 at 1.20.37 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 25708, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06DK3RFXC7/screenshot_2024-01-15_at_1.20.37___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06DK3RFXC7/download/screenshot_2024-01-15_at_1.20.37___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DK3RFXC7-0e3da86482/screenshot_2024-01-15_at_1.20.37___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DK3RFXC7-0e3da86482/screenshot_2024-01-15_at_1.20.37___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DK3RFXC7-0e3da86482/screenshot_2024-01-15_at_1.20.37___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 221, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DK3RFXC7-0e3da86482/screenshot_2024-01-15_at_1.20.37___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 295, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DK3RFXC7-0e3da86482/screenshot_2024-01-15_at_1.20.37___pm_160.png", "original_w": 670, "original_h": 412, "thumb_tiny": "AwAdADDTqlPcTJM6r0HT5c5q5SEnP3SfxpoTKP2mfHX/AMco+0z+v/jhq/RTuhWfcofaZ/X/AMcNS208sk21+Vxn7uKtUtFx2CkycniloqRic8cUHp0paKAEyeOPrQM56UtFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06DK3RFXC7/screenshot_2024-01-15_at_1.20.37___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06DK3RFXC7-beac13c687", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "uGB4G", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "What's the grayed-out/inactive action icon in the middle? (it has no tooltip to explain it)\n\nIt looks to me like some kind of \"send\" or \"share\" action, but I don't know of any reason that would be an available action for a compensation cycle. "}, {"type": "emoji", "name": "thinking_face", "unicode": "1f914"}]}]}]}, {"ts": "1705356220.324799", "text": "<@U04DKEFP1K8> can we do DKIM set up now? <PERSON> is waiting on it since yesterday", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1705356220.324799", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "bf1Vv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can we do DKIM set up now? <PERSON> is waiting on it since yesterday"}]}]}]}, {"ts": "1705364999.212319", "text": "Neuroflow login details have been sent! Here's hoping they like what they see :crossed_fingers:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "heart", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "bOkVP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Neuroflow login details have been sent! Here's hoping they like what they see "}, {"type": "emoji", "name": "crossed_fingers", "unicode": "1f91e"}]}]}]}, {"ts": "1705371565.152969", "text": "Never ended up sending photos through so here’s a little photo dump: ", "user": "U0658EW4B8D", "type": "message", "thread_ts": "1705371565.152969", "reply_count": 4, "reactions": [{"name": "raised_hands", "users": ["U065H3M6WJV"], "count": 1}, {"name": "heart", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06ED1UBU8Z", "created": 1705371551, "timestamp": 1705371551, "name": "IMG_4623.jpg", "title": "IMG_4623", "mimetype": "image/jpeg", "filetype": "jpg", "pretty_type": "JPEG", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 806257, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06ED1UBU8Z/img_4623.jpg", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06ED1UBU8Z/download/img_4623.jpg", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UBU8Z-1dda1600bb/img_4623_64.jpg", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UBU8Z-1dda1600bb/img_4623_80.jpg", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UBU8Z-1dda1600bb/img_4623_360.jpg", "thumb_360_w": 270, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UBU8Z-1dda1600bb/img_4623_480.jpg", "thumb_480_w": 360, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UBU8Z-1dda1600bb/img_4623_160.jpg", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UBU8Z-1dda1600bb/img_4623_720.jpg", "thumb_720_w": 540, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UBU8Z-1dda1600bb/img_4623_800.jpg", "thumb_800_w": 800, "thumb_800_h": 1067, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UBU8Z-1dda1600bb/img_4623_960.jpg", "thumb_960_w": 720, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UBU8Z-1dda1600bb/img_4623_1024.jpg", "thumb_1024_w": 768, "thumb_1024_h": 1024, "original_w": 3024, "original_h": 4032, "thumb_tiny": "AwAwACSGJAu4AY/GrMMCSBjIM+lBXPGNpHapYXSOMgnvmoNLaEMkPkkAfdPShVz1qRpUkOW4A6ZpHjLHIPFKw0QNKFYjnj0FJ54/2vypwRF4JOaXCf5NMNRzSg7pB8oPTNQnkk8Ae1TeWVQZ+bIqMJg4fgHigEICPu498HvU8LMcqe1M24ABIOKWLiQkYx3oGDBAxzzSYj9qDHCWJLZJPc0eVB6/rSAskZGKrTSomA+eORike4CrnJqlITKpkY8k4FNIm9iVrhATt3mo2nduB8o6YFRgYoqrENti0UUUxH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06ED1UBU8Z/img_4623.jpg", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06ED1UBU8Z-75b55f14c8", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06DXEQ1MHT", "created": 1705371551, "timestamp": 1705371551, "name": "IMG_4622.jpg", "title": "IMG_4622", "mimetype": "image/jpeg", "filetype": "jpg", "pretty_type": "JPEG", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 1280460, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06DXEQ1MHT/img_4622.jpg", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06DXEQ1MHT/download/img_4622.jpg", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DXEQ1MHT-c518c90c36/img_4622_64.jpg", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DXEQ1MHT-c518c90c36/img_4622_80.jpg", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DXEQ1MHT-c518c90c36/img_4622_360.jpg", "thumb_360_w": 270, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DXEQ1MHT-c518c90c36/img_4622_480.jpg", "thumb_480_w": 360, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DXEQ1MHT-c518c90c36/img_4622_160.jpg", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DXEQ1MHT-c518c90c36/img_4622_720.jpg", "thumb_720_w": 540, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DXEQ1MHT-c518c90c36/img_4622_800.jpg", "thumb_800_w": 800, "thumb_800_h": 1067, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DXEQ1MHT-c518c90c36/img_4622_960.jpg", "thumb_960_w": 720, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DXEQ1MHT-c518c90c36/img_4622_1024.jpg", "thumb_1024_w": 768, "thumb_1024_h": 1024, "original_w": 3024, "original_h": 4032, "thumb_tiny": "AwAwACSJN2TkADHSgIj4I/PpUsqBJSB061EMhWwemazuapIeQ20Eu4GOzHmlWI9VVjnvinAb4IznowqyJChiQDg5B9qe4tit9jZuTgexo+wn1WrxWjaaZFzOeTzJCwBO48D2qKP75XtUn3CrDsadtUyOR0NTc1Jo8ABMdqcNscm9jwT37VECSAO/86LvmDnrkUITLXnp6mj7Qnqf0rHx7UYHpVXIsWzjHOMVCrqHwGBqs+DjBJ4703pyKSjYbmaoZRg1FLcIW2nJXviqfmswAzint85DAct2HrTUQcjTWCHaCEBB70vkQ/8APMVTjV1QAsfpnpT/AJv7x/OpA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06DXEQ1MHT/img_4622.jpg", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06DXEQ1MHT-a0fe19cf6e", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06EP635SQY", "created": 1705371551, "timestamp": 1705371551, "name": "IMG_4621.jpg", "title": "IMG_4621", "mimetype": "image/jpeg", "filetype": "jpg", "pretty_type": "JPEG", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 900271, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06EP635SQY/img_4621.jpg", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06EP635SQY/download/img_4621.jpg", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06EP635SQY-eae51f6907/img_4621_64.jpg", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06EP635SQY-eae51f6907/img_4621_80.jpg", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06EP635SQY-eae51f6907/img_4621_360.jpg", "thumb_360_w": 270, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06EP635SQY-eae51f6907/img_4621_480.jpg", "thumb_480_w": 360, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06EP635SQY-eae51f6907/img_4621_160.jpg", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06EP635SQY-eae51f6907/img_4621_720.jpg", "thumb_720_w": 540, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06EP635SQY-eae51f6907/img_4621_800.jpg", "thumb_800_w": 800, "thumb_800_h": 1067, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06EP635SQY-eae51f6907/img_4621_960.jpg", "thumb_960_w": 720, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06EP635SQY-eae51f6907/img_4621_1024.jpg", "thumb_1024_w": 768, "thumb_1024_h": 1024, "original_w": 3024, "original_h": 4032, "thumb_tiny": "AwAwACQnjlkADlflbC8Y/Wq6WzPIEPAB5IrRkQOvPBHINAAReOpqLlpFG9KqEiQfd5OO1VghMW8ZyDg1egt23OZCpJ54OeakML9EkKg9R1p3C12VIpAEAZwp9DT/ADk/56L+tK1i7NnzF/Km/wBnv/z0X8qNA1Lu7IqNmLE9SAaaHxTy568fSpKWgKSp5BHPNPbaWK8++KYZU4JOMU0yoWLLKyk+1OwNiMsOf9TIffBpNsP/ADwl/I1E8z7vllYj16U3zpf77fnVWJuh68d+aVnwKg81c4z0pjybvpSsTccWLHNGaj3ZoJqhEmaM1FuPpRuPpQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06EP635SQY/img_4621.jpg", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06EP635SQY-3c87721c24", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06E2RWNY12", "created": **********, "timestamp": **********, "name": "IMG_4558.jpg", "title": "IMG_4558", "mimetype": "image/jpeg", "filetype": "jpg", "pretty_type": "JPEG", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 3991561, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06E2RWNY12/img_4558.jpg", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06E2RWNY12/download/img_4558.jpg", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E2RWNY12-aeb47f1a67/img_4558_64.jpg", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E2RWNY12-aeb47f1a67/img_4558_80.jpg", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E2RWNY12-aeb47f1a67/img_4558_360.jpg", "thumb_360_w": 270, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E2RWNY12-aeb47f1a67/img_4558_480.jpg", "thumb_480_w": 360, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E2RWNY12-aeb47f1a67/img_4558_160.jpg", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E2RWNY12-aeb47f1a67/img_4558_720.jpg", "thumb_720_w": 540, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E2RWNY12-aeb47f1a67/img_4558_800.jpg", "thumb_800_w": 800, "thumb_800_h": 1067, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E2RWNY12-aeb47f1a67/img_4558_960.jpg", "thumb_960_w": 720, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E2RWNY12-aeb47f1a67/img_4558_1024.jpg", "thumb_1024_w": 768, "thumb_1024_h": 1024, "original_w": 4284, "original_h": 5712, "thumb_tiny": "AwAwACS/mjfWZHcypwGLD35p7XzA4CAeuaAL5fNRSyKgyevpTBeQ7M85PaoJp0mZQoI9yKBCTy/P1I46VH5p/vGlaUBiPK3Y7kUnnD/nh+lAxiZycmm7S8xUH8T2pYiG6Z6gc/jVnb8uFUnceTUjKrgscqPlxx9KekexclgWPUA5xUTsSxA4APAHYVP5bJndjnHSm9AuSebGvHl5/wCBUefH/wA8v1/+vUTW6KcFufak8mP+8aYhX8uJtoXGDVuAq0YIA5qHykY5bcx78cVOvyoAqYApDM6RAszqTnDfnVy5Zd2M4wOeKHhLsSwXmnSweYAGP5Cm3cQnmH0o8w+lM8jbwGP50eT/ALR/OpGf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06E2RWNY12/img_4558.jpg", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06E2RWNY12-ca56dda0c2", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06DTP5A4RL", "created": **********, "timestamp": **********, "name": "IMG_4552.jpg", "title": "IMG_4552", "mimetype": "image/jpeg", "filetype": "jpg", "pretty_type": "JPEG", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 2110164, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06DTP5A4RL/img_4552.jpg", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06DTP5A4RL/download/img_4552.jpg", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5A4RL-3acfa7b54e/img_4552_64.jpg", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5A4RL-3acfa7b54e/img_4552_80.jpg", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5A4RL-3acfa7b54e/img_4552_360.jpg", "thumb_360_w": 270, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5A4RL-3acfa7b54e/img_4552_480.jpg", "thumb_480_w": 360, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5A4RL-3acfa7b54e/img_4552_160.jpg", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5A4RL-3acfa7b54e/img_4552_720.jpg", "thumb_720_w": 540, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5A4RL-3acfa7b54e/img_4552_800.jpg", "thumb_800_w": 800, "thumb_800_h": 1067, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5A4RL-3acfa7b54e/img_4552_960.jpg", "thumb_960_w": 720, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5A4RL-3acfa7b54e/img_4552_1024.jpg", "thumb_1024_w": 768, "thumb_1024_h": 1024, "original_w": 4284, "original_h": 5712, "thumb_tiny": "AwAwACSWXa4znFRrweemcUucrxxQGGz5jznNZlkn7oY569s1G4D8BDgc1BK67eDnggGoIJmiZ8k5IxzTt1FchPU0lFFWSaMRDQoc9hSS8QnOM4PQ0y2JMK8ZAJFTXRLWqlVz8xGBUW1KKBbouMAGiQLhWHUk5FNPJp80bJtLDG4ZFWIiooooEX9OOYpV49eatRHMZGP4qoWL7ZGHqtXlDJGPeoe5SKKQbrjb0XeV/Kn36gCMZ+6MCnIhYEnKnzCQaZfHkUdQ6FRgAxA6UlB5NFWSf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06DTP5A4RL/img_4552.jpg", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06DTP5A4RL-1e92c9fb92", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06ED1UG75F", "created": **********, "timestamp": **********, "name": "IMG_4528.jpg", "title": "IMG_4528", "mimetype": "image/jpeg", "filetype": "jpg", "pretty_type": "JPEG", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 1577593, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06ED1UG75F/img_4528.jpg", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06ED1UG75F/download/img_4528.jpg", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UG75F-6f2636a113/img_4528_64.jpg", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UG75F-6f2636a113/img_4528_80.jpg", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UG75F-6f2636a113/img_4528_360.jpg", "thumb_360_w": 270, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UG75F-6f2636a113/img_4528_480.jpg", "thumb_480_w": 360, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UG75F-6f2636a113/img_4528_160.jpg", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UG75F-6f2636a113/img_4528_720.jpg", "thumb_720_w": 540, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UG75F-6f2636a113/img_4528_800.jpg", "thumb_800_w": 800, "thumb_800_h": 1067, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UG75F-6f2636a113/img_4528_960.jpg", "thumb_960_w": 720, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UG75F-6f2636a113/img_4528_1024.jpg", "thumb_1024_w": 768, "thumb_1024_h": 1024, "original_w": 4284, "original_h": 5712, "thumb_tiny": "AwAwACSnD9z8afTIfun60/vW0HoWhknb61J2qOTp+NP7U31Ar0YNPUjHOaXK+/51lckapK7setPzkAdsVGOppYz2PSqQyRGDZU9KbNkY5pYcLMvQ5pZQWbaB0z0p76CvcibAx9KSnNwcFSCODSf8BqbCJBGAc00jnhalpK0sjRoXbsZGUZIxnNKzAsTgc0nWmswXrRotQskGB6UmKYZGJ44pPMb1pcyJuj//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06ED1UG75F/img_4528.jpg", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06ED1UG75F-f50ff798cd", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06DTP5B7PG", "created": 1705371555, "timestamp": 1705371555, "name": "IMG_4508.jpg", "title": "IMG_4508", "mimetype": "image/jpeg", "filetype": "jpg", "pretty_type": "JPEG", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 4032721, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06DTP5B7PG/img_4508.jpg", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06DTP5B7PG/download/img_4508.jpg", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5B7PG-7f37ed68d9/img_4508_64.jpg", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5B7PG-7f37ed68d9/img_4508_80.jpg", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5B7PG-7f37ed68d9/img_4508_360.jpg", "thumb_360_w": 360, "thumb_360_h": 270, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5B7PG-7f37ed68d9/img_4508_480.jpg", "thumb_480_w": 480, "thumb_480_h": 360, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5B7PG-7f37ed68d9/img_4508_160.jpg", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5B7PG-7f37ed68d9/img_4508_720.jpg", "thumb_720_w": 720, "thumb_720_h": 540, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5B7PG-7f37ed68d9/img_4508_800.jpg", "thumb_800_w": 800, "thumb_800_h": 600, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5B7PG-7f37ed68d9/img_4508_960.jpg", "thumb_960_w": 960, "thumb_960_h": 720, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5B7PG-7f37ed68d9/img_4508_1024.jpg", "thumb_1024_w": 1024, "thumb_1024_h": 768, "original_w": 5712, "original_h": 4284, "thumb_tiny": "AwAkADCs+VZScdae6gJksBn2pk2WXgHj2qRpEKopIySMikMg43HnNG75APfn3qUoAQW/iOBxUbDkUrlcoMwbouDjFOiUseP1oXhfUnrT4JFjJLc5NANaD5YXjjJcjb2x60yEj5eAeM0XU0uSjtx1XA6/WnIAArE8kdfela+wrgGL5Q8Z4yKj2kTGNjwP1qcKqlmPK5GKbsAmB5PfmnYLkLRsp+YEfWmD5h/WrkwBbII6ciodu1gcZHqKbVkC10JCizGMP/exxUV0gjcomQoc4/IVPH96P/eqK9/1zf739BURHIh851AG7I96swncucc1TbpVu3/1f4VoiCTqOec0dRikH3RS1QH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06DTP5B7PG/img_4508.jpg", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06DTP5B7PG-d531609fdc", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06E0CLKBGA", "created": 1705371556, "timestamp": 1705371556, "name": "IMG_4494.jpg", "title": "IMG_4494", "mimetype": "image/jpeg", "filetype": "jpg", "pretty_type": "JPEG", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 2299883, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06E0CLKBGA/img_4494.jpg", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06E0CLKBGA/download/img_4494.jpg", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E0CLKBGA-895d1184d7/img_4494_64.jpg", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E0CLKBGA-895d1184d7/img_4494_80.jpg", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E0CLKBGA-895d1184d7/img_4494_360.jpg", "thumb_360_w": 270, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E0CLKBGA-895d1184d7/img_4494_480.jpg", "thumb_480_w": 360, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E0CLKBGA-895d1184d7/img_4494_160.jpg", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E0CLKBGA-895d1184d7/img_4494_720.jpg", "thumb_720_w": 540, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E0CLKBGA-895d1184d7/img_4494_800.jpg", "thumb_800_w": 800, "thumb_800_h": 1067, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E0CLKBGA-895d1184d7/img_4494_960.jpg", "thumb_960_w": 720, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06E0CLKBGA-895d1184d7/img_4494_1024.jpg", "thumb_1024_w": 768, "thumb_1024_h": 1024, "original_w": 4284, "original_h": 5712, "thumb_tiny": "AwAwACSJXUdxTxLH/eFUlXPSnYwcEYNSy0i6J4x/EKeLqMfxfpVYQnHTnrigKKm40i39rj9T+VH2uP1P5VW2ijaKOYfKV4TjOOtTJkypnHXiqysUPA5qZPM3qzodoPYUMakrWLbHDY9BkVCp6U53DPgHB24NATp86/nSSdhJpMKKXYf76/nRsP8AeX86LMfMiBSVJI4zTtx5BJ5ox+AoOO1aWRlcUHkH0GKMYpegFIM0CHUUCj86AP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06E0CLKBGA/img_4494.jpg", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06E0CLKBGA-fd5c4f7153", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06ED1UKUC9", "created": 1705371557, "timestamp": 1705371557, "name": "IMG_4501.jpg", "title": "IMG_4501", "mimetype": "image/jpeg", "filetype": "jpg", "pretty_type": "JPEG", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 2965198, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06ED1UKUC9/img_4501.jpg", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06ED1UKUC9/download/img_4501.jpg", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UKUC9-2858e7946d/img_4501_64.jpg", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UKUC9-2858e7946d/img_4501_80.jpg", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UKUC9-2858e7946d/img_4501_360.jpg", "thumb_360_w": 270, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UKUC9-2858e7946d/img_4501_480.jpg", "thumb_480_w": 360, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UKUC9-2858e7946d/img_4501_160.jpg", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UKUC9-2858e7946d/img_4501_720.jpg", "thumb_720_w": 540, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UKUC9-2858e7946d/img_4501_800.jpg", "thumb_800_w": 800, "thumb_800_h": 1067, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UKUC9-2858e7946d/img_4501_960.jpg", "thumb_960_w": 720, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ED1UKUC9-2858e7946d/img_4501_1024.jpg", "thumb_1024_w": 768, "thumb_1024_h": 1024, "original_w": 4284, "original_h": 5712, "thumb_tiny": "AwAwACRLUvJH98j2qRkcKSXbgetU4ZpRwoJyanLXB6lce9IBo5hGTzgmo4l3rkv83pinDIhVcdFIzSQAbujGi47aEot3PIk/Sj7NJ/z0/Spx3+cjn1pf+2h/OmIhXAOAKOWBIViB37VOYwFx2qu8jR9eR61LbKikQtI/QjH4UiS445/Kh2JPPWowrr0ppIG2ybz8dT+Yo8/6f981XYOT900m1vQ0yTZk+XNUJ33NjsKcbktGQc7getQdTSv1HbWw7+BW9OKUGmycAKD0py0o7FT3Cjilx7UfhVEH/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06ED1UKUC9/img_4501.jpg", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06ED1UKUC9-a2dbc988d5", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06DTP5GQCW", "created": 1705371559, "timestamp": 1705371559, "name": "IMG_4500.MOV", "title": "IMG_4500", "mimetype": "video/mp4", "filetype": "mp4", "pretty_type": "MPEG 4 Video", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 7049188, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "transcription": {"status": "complete", "locale": "en-US", "preview": {"content": "I was. Oh, that's right. You show some attitude. Do I understand? No, it's all good. I thought about it.", "has_more": false}}, "mp4": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5GQCW-85943a30b7/img_4500.mp4", "url_private": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5GQCW-85943a30b7/img_4500.mp4", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06DTP5GQCW/download/img_4500.mov", "vtt": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5GQCW-85943a30b7/file.vtt?_xcb=ae912", "hls": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5GQCW-85943a30b7/file.m3u8?_xcb=ae912", "hls_embed": "data:application/vnd.apple.mpegurl;base64,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", "mp4_low": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5GQCW-85943a30b7/img_4500_trans.mp4", "duration_ms": 15515, "media_display_type": "video", "thumb_video": "https://files.slack.com/files-tmb/T04DM97F1UM-F06DTP5GQCW-85943a30b7/img_4500_thumb_video.jpeg", "thumb_video_w": 720, "thumb_video_h": 1280, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06DTP5GQCW/img_4500.mov", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06DTP5GQCW-e9f4382682", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "FqUbx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Never ended up sending photos through so "}, {"type": "text", "text": "here’s"}, {"type": "text", "text": " a little photo dump: "}]}]}]}, {"ts": "1705423040.541819", "text": "<@U04DS2MBWP4> I am still under weather but certain i will be 100% tomorrow, can we move leadership meeting to Wednesday for this week?", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Xj1C+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I am still under weather but certain i will be 100% tomorrow, can we move leadership meeting to Wednesday for this week?"}]}]}]}, {"ts": "1705438879.384269", "text": "Who all is joining for <PERSON>eur<PERSON><PERSON>? <https://us06web.zoom.us/j/86095313675?pwd=4PSvrfaAP869AKfKuBGhLaRPJxN383.1>", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R06EUNP6U64", "block_id": "Wj60x", "api_decoration_available": false, "call": {"v1": {"id": "R06EUNP6U64", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1705438879, "active_participants": [], "all_participants": [], "display_id": "860-9531-3675", "join_url": "https://us06web.zoom.us/j/86095313675?pwd=4PSvrfaAP869AKfKuBGhLaRPJxN383.1", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1705525819, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/86095313675?pwd=4PSvrfaAP869AKfKuBGhLaRPJxN383.1", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "CK3Bg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Who all is joining for <PERSON>eur<PERSON><PERSON>? "}, {"type": "link", "url": "https://us06web.zoom.us/j/86095313675?pwd=4PSvrfaAP869AKfKuBGhLaRPJxN383.1"}]}]}]}, {"ts": "1705438905.187649", "text": "I have another meeting", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EG9VM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have another meeting"}]}]}]}, {"ts": "1705440386.361029", "text": "Had a good call with them. Immediate feedback was that the \"reviewed\" step feels like too much for a manager during planning while they try to see impact to the budget, so I let them know that it's already something we've identified to improve in the next version. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705440386.361029", "reply_count": 1, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "eyes", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "9tv9o", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Had a good call with them. Immediate feedback was that the \"reviewed\" step feels like too much for a manager during planning while they try to see impact to the budget, so I let them know that it's already something we've identified to improve in the next version. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1705440442.844299", "text": "<@U04DKEFP1K8> Let's get Clarity/Heap enabled there (wait about an hour so that we don't disrupt their immediate testing), and be ready if they ask to make specific changes to budget values. My guess is they won't be able to do it easily from the Allocate page and we might need to do that via a backend update.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705440442.844299", "reply_count": 1, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "tQoKw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Let's get Clarity/Heap enabled there (wait about an hour so that we don't disrupt their immediate testing), and be ready if they ask to make specific changes to budget values. My guess is they won't be able to do it easily from the Allocate page and we might need to do that via a backend update."}]}]}]}, {"ts": "1705447587.090079", "text": "<@U065H3M6WJV> <http://dev-app.compiify.com|dev-app.compiify.com> will be taken down later today. It will be replaced by a new environment immediately ( with a new url though)", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "QzXFo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://dev-app.compiify.com", "text": "dev-app.compiify.com"}, {"type": "text", "text": " will be taken down later today. It will be replaced by a new environment immediately ( with a new url though)"}]}]}]}, {"ts": "1705447648.244929", "text": "<@U065H3M6WJV> let me know when staging environment can be updated to latest build, it is now running a very old build ", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1705447648.244929", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "/prWM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " let me know when staging environment can be updated to latest build, it is now running a very old build "}]}]}]}, {"ts": "1705524653.133879", "text": "<PERSON><PERSON><PERSON><PERSON> has questions - trying to set up a call with them today (they didn't share specifics yet)", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "QxM5x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON> has questions - trying to set up a call with them today (they didn't share specifics yet)"}]}]}]}, {"ts": "1705525941.497079", "text": "<@U04DKEFP1K8> Neuroflow found bugs! I'll file these, and might need to re-listen to the call to capture it all, but:\n• Bonus amounts aren't saving properly\n• Some employee rows aren't allowing promotion to be selected at all\n• Some employee salaries are wrong / don't match the \"1.11\" spreadsheet data\n• At least one employee (<PERSON>) is reporting to the wrong manager, but I think that might've been wrong in the spreadsheet", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705525941.497079", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "c3UOm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " <PERSON><PERSON><PERSON><PERSON> found bugs! I'll file these, and might need to re-listen to the call to capture it all, but:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bonus amounts aren't saving properly"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Some employee rows aren't allowing promotion to be selected at all"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Some employee salaries are wrong / don't match the \"1.11\" spreadsheet data"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "At least one employee (<PERSON>) is reporting to the wrong manager, but I think that might've been wrong in the spreadsheet"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1705525966.538289", "text": "<@U04DS2MBWP4> How can I set up a shared Slack channel with external customers, do I just create a channel and add them, or is there any extra step?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705525966.538289", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "/mG3a", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " How can I set up a shared Slack channel with external customers, do I just create a channel and add them, or is there any extra step?"}]}]}]}, {"ts": "1705526203.186879", "text": "Just shared the Otter call with you both", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "H/325", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just shared the Otter call with you both"}]}]}]}, {"ts": "1705601396.971189", "text": "<@U065H3M6WJV> Any updates from <PERSON> on the data upload", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1705601396.971189", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "CmZwt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Any updates from <PERSON> on the data upload"}]}]}]}, {"ts": "1705608308.049779", "text": "<PERSON> and <PERSON><PERSON><PERSON> are still in another call, we'll start when <PERSON> is free to kick off his Zoom :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "73ypD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> and <PERSON><PERSON><PERSON> are still in another call, we'll start when <PERSON> is free to kick off his Zoom "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1705608331.064269", "text": "Oh kk no worries wanted to make sure I had the right link due to the last minute change!", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DEyKb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Oh kk no worries wanted to make sure I had the right link due to the last minute change!"}]}]}]}, {"ts": "1705608339.171119", "text": "Same! :sweat_smile:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "dqyHW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Same! "}, {"type": "emoji", "name": "sweat_smile", "unicode": "1f605"}]}]}]}, {"ts": "1705608548.925809", "text": "T minus 2 min :joy:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GmaDc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "T minus 2 min "}, {"type": "emoji", "name": "joy", "unicode": "1f602"}]}]}]}, {"ts": "1705609542.184059", "text": "<@U04DKEFP1K8> SDF rescheduled for tomorrow. Please make sure we get all those XLM updates in by then!", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705609542.184059", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "qeRng", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " SDF rescheduled for tomorrow. Please make sure we get all those XLM updates in by then!"}]}]}]}, {"ts": "1705696416.109339", "text": "Good call with SDF! They have a couple more feature requests, but know that we are working with a beta and I think we're in good shape. <PERSON><PERSON> also asked me to help with the manager training call on 1/29 and we're set up for that already. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705696416.109339", "reply_count": 7, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "AkeH2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Good call with SDF! They have a couple more feature requests, but know that we are working with a beta and I think we're in good shape. <PERSON><PERSON> also asked me to help with the manager training call on 1/29 and we're set up for that already. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1705942447.658669", "text": "<@U065H3M6WJV> I've put time on your cal for tomorrow for a feedback/working session on the reporting", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ZqCOO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I've put time on your cal for tomorrow for a feedback/working session on the reporting"}]}]}]}, {"ts": "1705944922.106619", "text": "<@U04DKEFP1K8> what's the status of update to demo environemnt?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NDifz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what's the status of update to demo environemnt?"}]}]}]}, {"ts": "1705944975.553659", "text": "Yesterday i had setup SDF prod, today i will setup DA prod and after that will update staging env", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1705944975.553659", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "X979Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yesterday i had setup SDF prod, today i will setup DA prod and after that will update staging env"}]}]}]}, {"ts": "1705951668.905349", "text": "Upcoming environment changes, in order of priority & sequence:\n• :white_check_mark: *SDF production* instance - will be shared with <PERSON> and <PERSON><PERSON> *today* (Monday) - _Done!_\n• :white_check_mark: *DA production* instance - data will be loaded *today* to share with <PERSON><PERSON><PERSON> and <PERSON> *tomorrow* (Tuesday) - _Done!_\n• :white_check_mark: *Staging* - will be updated later *today* (Monday, after 2pm) to get the latest features including table filters & various fixes - Done!\nThings we're *not* planning:\n• No urgent fixes identified for Neuroflow, so we'll keep that stable\n• No new data provided from Convene, Cipher, Cypher, or MX\n\n<@U04DS2MBWP4> Any others in the pipeline that we need to be planning as new beta environments?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705951668.905349", "reply_count": 6, "edited": {"user": "U065H3M6WJV", "ts": "1706036420.000000"}, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "v2PU+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Upcoming environment changes, in order of priority & sequence:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "white_check_mark", "unicode": "2705", "style": {"bold": true}}, {"type": "text", "text": " SDF production", "style": {"bold": true}}, {"type": "text", "text": " instance - will be shared with <PERSON> and <PERSON><PERSON> "}, {"type": "text", "text": "today", "style": {"bold": true}}, {"type": "text", "text": " (Monday) - "}, {"type": "text", "text": "Done!", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "white_check_mark", "unicode": "2705", "style": {"bold": true}}, {"type": "text", "text": " DA production ", "style": {"bold": true}}, {"type": "text", "text": "instance - data will be loaded"}, {"type": "text", "text": " today ", "style": {"bold": true}}, {"type": "text", "text": "to share with <PERSON><PERSON><PERSON> and <PERSON> "}, {"type": "text", "text": "tomorrow", "style": {"bold": true}}, {"type": "text", "text": " (Tuesday) - "}, {"type": "text", "text": "Done!", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "white_check_mark", "unicode": "2705", "style": {"bold": true}}, {"type": "text", "text": " Staging ", "style": {"bold": true}}, {"type": "text", "text": "- will be updated later "}, {"type": "text", "text": "today", "style": {"bold": true}}, {"type": "text", "text": " (Monday, after 2pm) to get the latest features including table filters & various fixes - Done!"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nThings we're "}, {"type": "text", "text": "not", "style": {"bold": true}}, {"type": "text", "text": " planning:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No urgent fixes identified for <PERSON><PERSON><PERSON><PERSON>, so we'll keep that stable"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "No new data provided from Convene, Cipher, Cypher, or MX"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n\n"}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Any others in the pipeline that we need to be planning as new beta environments?"}]}]}]}, {"ts": "1705952109.690369", "text": "<@U0658EW4B8D> For your testing this week:\n• Please use <https://qa.compiify.com> with \"<PERSON>\" login from <https://docs.google.com/document/d/1it4-9IEwBbS0izBRJKJp-h13bHAt_QJP2E7vUqeRAP4/edit|this doc>\n• Be sure to test a combo of merit, market adjustment, and bonus changes\n• Log any bugs you find in JIRA, as subtasks of the <https://compiify.atlassian.net/browse/COM-2145|Neuroflow UAT >ticket\n• When you've completed making adjustments, ping me and <@U04DKEFP1K8> for help generating adjustment letters for testing.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705952109.690369", "reply_count": 19, "edited": {"user": "U065H3M6WJV", "ts": "1705952112.000000"}, "reactions": [{"name": "+1", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "MLb+H", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " For your testing this week:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please use "}, {"type": "link", "url": "https://qa.compiify.com"}, {"type": "text", "text": " with \"<PERSON>\" login from "}, {"type": "link", "url": "https://docs.google.com/document/d/1it4-9IEwBbS0izBRJKJp-h13bHAt_QJP2E7vUqeRAP4/edit", "text": "this doc"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Be sure to test a combo of merit, market adjustment, and bonus changes"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Log any bugs you find in JIRA, as subtasks of the "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2145", "text": "Neuroflow UAT "}, {"type": "text", "text": "ticket"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "When you've completed making adjustments, ping me and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " for help generating adjustment letters for testing."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1705966153.255219", "text": "<!here> i have meeting from 8pm - 1130pm today ( will be logging off around 445pm)", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "SQxve", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " i have meeting from 8pm - 1130pm today ( will be logging off around 445pm)"}]}]}]}, {"ts": "1705972013.557359", "text": "Priorities for Eng for the next day:\n• DA: Production instance won't load a new cycle (<https://compiify.atlassian.net/browse/COM-2208|COM-2208>)\n• SDF: Allow blank prefill while still giving recommendations (<https://compiify.atlassian.net/browse/COM-2200|COM-2200> and <https://compiify.atlassian.net/browse/COM-2206|COM-2206>)\n• SDF: Correct the Monthly XLM values in the tooltip (<https://compiify.atlassian.net/browse/COM-2209|COM-2209>)\n• SDF: Load Stellar's custom performance rating scale (<https://compiify.atlassian.net/browse/COM-2020|COM-2020>)\n• Export / download support (<https://compiify.atlassian.net/browse/COM-2060|COM-2060>)\n• Various fixes for filters (<https://compiify.atlassian.net/browse/COM-2204|COM-2204>, <https://compiify.atlassian.net/browse/COM-2139|COM-2139>)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1705972013.557359", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "aRSfE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA: Production instance won't load a new cycle ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2208", "text": "COM-2208"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF: Allow blank prefill while still giving recommendations ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2200", "text": "COM-2200"}, {"type": "text", "text": " and "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2206", "text": "COM-2206"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF: Correct the Monthly XLM values in the tooltip ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2209", "text": "COM-2209"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF: Load Stellar's custom performance rating scale ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2020", "text": "COM-2020"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Export / download support ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2060", "text": "COM-2060"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Various fixes for filters ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2204", "text": "COM-2204"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2139", "text": "COM-2139"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1705978431.487359", "text": "<@U04DKEFP1K8> does aws offer a service to purchase a domain if the existing owner is willing to sell it?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1705978431.487359", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "lfob5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " does aws offer a service to purchase a domain if the existing owner is willing to sell it?"}]}]}]}, {"ts": "1706043950.613099", "text": "DA folks are able to login with their google login's :slightly_smiling_face:", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "meow_attention", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "CEyaV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA folks are able to login with their google login's "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1706050756.724549", "text": "Priorities for Eng for the next day:\n• :repeat: SDF: Allow blank prefill while still giving recommendations (<https://compiify.atlassian.net/browse/COM-2200|COM-2200> and <https://compiify.atlassian.net/browse/COM-2206|COM-2206>)\n• :repeat: SDF: Load Stellar's custom performance rating scale (<https://compiify.atlassian.net/browse/COM-2020|COM-2020>)\n• :repeat: Export / download support (<https://compiify.atlassian.net/browse/COM-2060|COM-2060>)\n• :repeat: Various fixes for filters (<https://compiify.atlassian.net/browse/COM-2204|COM-2204>, <https://compiify.atlassian.net/browse/COM-2139|COM-2139>)\n• DA: Flag for increases over 9% (<https://compiify.atlassian.net/browse/COM-2113|COM-2113>)\n• SDF: Allow inputting new titles for promotions (<https://compiify.atlassian.net/browse/COM-2198|COM-2198>)\n• Neuroflow / DA / SDF: Replace hardcoded cycle dates (<https://compiify.atlassian.net/browse/COM-1914|COM-1914>)\n• Various fixes for table sort (<https://compiify.atlassian.net/browse/COM-2205|COM-2205>, <https://compiify.atlassian.net/browse/COM-2054|COM-2054>, <https://compiify.atlassian.net/browse/COM-1990|COM-1990>)\nIf all these are addressed/assigned, next priorities are completing anything else in <https://compiify.atlassian.net/browse/COM-2086|Wave 2> and starting on <https://compiify.atlassian.net/browse/COM-2087|Wave 3> (top to bottom)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706050756.724549", "reply_count": 12, "edited": {"user": "U065H3M6WJV", "ts": "1706056718.000000"}, "blocks": [{"type": "rich_text", "block_id": "2mYV9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " SDF: Allow blank prefill while still giving recommendations ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2200", "text": "COM-2200"}, {"type": "text", "text": " and "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2206", "text": "COM-2206"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " SDF: Load Stellar's custom performance rating scale ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2020", "text": "COM-2020"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Export / download support ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2060", "text": "COM-2060"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Various fixes for filters ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2204", "text": "COM-2204"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2139", "text": "COM-2139"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "DA: Flag for increases over 9% ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2113", "text": "COM-2113"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF: Allow inputting new titles for promotions ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2198", "text": "COM-2198"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Neuroflow / DA / SDF: Replace hardcoded cycle dates ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1914", "text": "COM-1914"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Various fixes for table sort ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2205", "text": "COM-2205"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2054", "text": "COM-2054"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1990", "text": "COM-1990"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nIf all these are addressed/assigned, next priorities are completing anything else in "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2086", "text": "Wave 2"}, {"type": "text", "text": " and starting on "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " (top to bottom)"}]}]}]}, {"ts": "1706051197.903799", "text": "Next steps for DA production cycle:\n• Change to have blank prefills / no recommendations\n• Hide bonus &amp; pay band / compa ratio columns\n• Flatten the hierarchy for planning\n• Ensure we have the flag for &gt;9% bonus (COM-2113)\nWaiting on them:\n• Updated performance ratings\n• Updated ineligible-employee list\n• Final currency conversion values\n• Exact budget", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ngG12", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Next steps for DA production cycle:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Change to have blank prefills / no recommendations"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Hide bonus & pay band / compa ratio columns"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Flatten the hierarchy for planning"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Ensure we have the flag for >9% bonus (COM-2113)"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nWaiting on them:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Updated performance ratings"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Updated ineligible-employee list"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Final currency conversion values"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Exact budget"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1706119309.168149", "text": "<@U0658EW4B8D> How's your testing going - found any major issues for us yet? :wink:\n\nAnd <@U04DKEFP1K8>, will we need to have <PERSON> on a Zoom to walk through any of the adjustment letter process?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706119309.168149", "reply_count": 13, "blocks": [{"type": "rich_text", "block_id": "EP1Nm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " How's your testing going - found any major issues for us yet? "}, {"type": "emoji", "name": "wink", "unicode": "1f609"}, {"type": "text", "text": "\n\nAnd "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": ", will we need to have <PERSON> on a Zoom to walk through any of the adjustment letter process?"}]}]}]}, {"ts": "**********.122089", "text": "<@U068MM2H2QG> <@U04DS2MBWP4> We had 3 critical issue related to email health yesterday. 1/3 has been resolved. Per the devops engineer other 2 are not critical as long as we are able to send email from the domain. Please retry on your end again sending emails. Also can one of you send a test email to <mailto:<EMAIL>|<EMAIL>>\n\nThese are remaining list of error ( which are non critical per devops engineer)", "user": "U04DKEFP1K8", "type": "message", "files": [{"id": "F06G4BYHP4Y", "created": **********, "timestamp": **********, "name": "Screenshot 2024-01-24 at 11.30.31 AM.png", "title": "Screenshot 2024-01-24 at 11.30.31 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 148782, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06G4BYHP4Y/screenshot_2024-01-24_at_11.30.31___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06G4BYHP4Y/download/screenshot_2024-01-24_at_11.30.31___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_360.png", "thumb_360_w": 360, "thumb_360_h": 57, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_480.png", "thumb_480_w": 480, "thumb_480_h": 76, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_720.png", "thumb_720_w": 720, "thumb_720_h": 114, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_800.png", "thumb_800_w": 800, "thumb_800_h": 126, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_960.png", "thumb_960_w": 960, "thumb_960_h": 151, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G4BYHP4Y-2be5a262c7/screenshot_2024-01-24_at_11.30.31___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 161, "original_w": 3400, "original_h": 536, "thumb_tiny": "AwAHADDSwaMH1paKAEOcUc0tFACc0UtFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06G4BYHP4Y/screenshot_2024-01-24_at_11.30.31___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06G4BYHP4Y-09299e2168", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "wQkeg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U068MM2H2QG"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " We had 3 critical issue related to email health yesterday. 1/3 has been resolved. Per the devops engineer other 2 are not critical as long as we are able to send email from the domain. Please retry on your end again sending emails. Also can one of you send a test email to "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": "\n\nThese are remaining list of error ( which are non critical per devops engineer)"}]}]}]}, {"ts": "**********.781229", "text": "<@U04DKEFP1K8> pls move this conversation to revenue leadership as we<PERSON> and chris are not in this channel", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.781229", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "X8osM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " pls move this conversation to revenue leadership as we<PERSON> and chris are not in this channel"}]}]}]}, {"ts": "**********.946279", "text": "Priorities for Eng for the next day:\n• :repeat: SDF: Allow inputting new titles for promotions (<https://compiify.atlassian.net/browse/COM-2198|COM-2198>)\n• SDF: Export values for new XLM columns (<https://compiify.atlassian.net/browse/COM-2214|COM-2214>)\n• DA: Roll up Eric, <PERSON><PERSON><PERSON>, <PERSON><PERSON> under Emnet; Exclude <PERSON>'s and <PERSON>'s entire teams from the cycle (<https://compiify.atlassian.net/browse/COM-2221|COM-2221>)\n    ◦ _(I don't know if any Eng work is needed or just config changes from <PERSON><PERSON><PERSON><PERSON>, so putting it on the list just in case)_\n• Filter improvements (<https://compiify.atlassian.net/browse/COM-2139|COM-2139>, <https://compiify.atlassian.net/browse/COM-2220|COM-2220>)\n• Login email case sensitivity (<https://compiify.atlassian.net/browse/COM-2166|COM-2166>)\nAfter these we should have completed Wave 2 and can continue with <https://compiify.atlassian.net/browse/COM-2087|Wave 3>, starting from the top of the list.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.946279", "reply_count": 1, "edited": {"user": "U065H3M6WJV", "ts": "1706146443.000000"}, "blocks": [{"type": "rich_text", "block_id": "LnHMy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " SDF: Allow inputting new titles for promotions ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2198", "text": "COM-2198"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF: Export values for new XLM columns ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2214", "text": "COM-2214"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "DA: <PERSON> up <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> under Emnet; Exclude <PERSON>'s and <PERSON>'s entire teams from the cycle ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2221", "text": "COM-2221"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "(I don't know if any Eng work is needed or just config changes from <PERSON><PERSON><PERSON><PERSON>, so putting it on the list just in case)", "style": {"italic": true}}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Filter improvements ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2139", "text": "COM-2139"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2220", "text": "COM-2220"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Login email case sensitivity ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2166", "text": "COM-2166"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAfter these we should have completed Wave 2 and can continue with "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": ", starting from the top of the list."}]}]}]}, {"ts": "1706210817.601269", "text": "SDF To-do's\n• PROD: Remove existing performance ratings and ensure Merit view still loads (they'll give us a new upload Feb 15th)\n• PROD: Remove prefills and make sure the guidance tooltip still shows\n• PROD: Enable planners to enter a promotion title or select \"Other\" if the one they want isn't in our list\n• TEST: Load artificial data", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706210817.601269", "reply_count": 3, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "x+5XC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF To-do's\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "PROD: Remove existing performance ratings and ensure Merit view still loads (they'll give us a new upload Feb 15th)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "PROD: Remove prefills and make sure the guidance tooltip still shows"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "PROD: Enable planners to enter a promotion title or select \"Other\" if the one they want isn't in our list"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "TEST: Load artificial data"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1706232382.723659", "text": "<@U0658EW4B8D> Edits in progress - need another ~20 min at least, but I think we'll have a more readable version of the letter for you today! :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "4t0YN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " Edits in progress - need another ~20 min at least, but I think we'll have a more readable version of the letter for you today! "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1706232745.816849", "text": "Would love to have Table filters for org view :eyes:", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ogqPm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Would love to have Table filters for org view "}, {"type": "emoji", "name": "eyes", "unicode": "1f440"}]}]}]}, {"ts": "1706232772.819669", "text": "You mean \"Organization\" view outside of Merit view? Or the sub-tab of Merit View?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jpbur", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "You mean \"Organization\" view outside of Merit view? Or the sub-tab of Merit View?"}]}]}]}, {"ts": "1706232788.487399", "text": "Sub tab", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nYP6p", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sub tab"}]}]}]}, {"ts": "1706232817.717169", "text": "Filters are there - but you have to click the \"List view\" icon", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Vi8U1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Filters are there - but you have to click the \"List view\" icon"}]}]}]}, {"ts": "1706232826.188189", "text": "we don't filter in hierarchy view", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "z1Iia", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we don't filter in hierarchy view"}]}]}]}, {"ts": "1706232925.844119", "text": "That is exactly what I am looking for thank you", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "meow_attention", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "h488h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That is exactly what I am looking for thank you"}]}]}]}, {"ts": "1706236333.620429", "text": "<@U0658EW4B8D> I have uploaded <PERSON>'s templates on qa environment.( I already see few fields are not getting updated with letters generated via new templates, i will check them post dinner and revert back) Feel free to raise bugs in the meanwhile.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1706236333.620429", "reply_count": 6, "edited": {"user": "U04DKEFP1K8", "ts": "1706237085.000000"}, "blocks": [{"type": "rich_text", "block_id": "rMITk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " I have uploaded <PERSON>'s templates on qa environment.( I already see few fields are not getting updated with letters generated via new templates, i will check them post dinner and revert back) Feel free to raise bugs in the meanwhile."}]}]}]}, {"ts": "1706241193.582769", "text": "Priorities for Eng for the next day:\n• :repeat: SDF: Allow inputting new titles for promotions (<https://compiify.atlassian.net/browse/COM-2198|COM-2198>) - In development - *ETA 1/26*\n• Fix adjustment letter variables (<https://compiify.atlassian.net/browse/COM-2224|COM-2224>)\n• SDF / DA / Neuroflow: Date drift in cycle steps (<https://compiify.atlassian.net/browse/COM-2223|COM-2223>)\n• Currency conversion issues (<https://compiify.atlassian.net/browse/COM-1919|COM-1919> and <https://compiify.atlassian.net/browse/COM-2142|COM-2142>)\n• Enable comment (view/add) on \"Reviewed\" rows (<https://compiify.atlassian.net/browse/COM-2140|COM-2140>)\n• Promotion job-title display issue (<https://compiify.atlassian.net/browse/COM-2212|COM-2212>)\n• Export filename bug (<https://compiify.atlassian.net/browse/COM-2213|COM-2213>)\nAfter this continue with the rest of <https://compiify.atlassian.net/browse/COM-2087|Wave 3> from top to bottom.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706241193.582769", "reply_count": 1, "edited": {"user": "U065H3M6WJV", "ts": "1706242178.000000"}, "blocks": [{"type": "rich_text", "block_id": "Elej2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " SDF: Allow inputting new titles for promotions ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2198", "text": "COM-2198"}, {"type": "text", "text": ") - In development - "}, {"type": "text", "text": "ETA 1/26", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Fix adjustment letter variables ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2224", "text": "COM-2224"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF / DA / Neuroflow: Date drift in cycle steps ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2223", "text": "COM-2223"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Currency conversion issues ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1919", "text": "COM-1919"}, {"type": "text", "text": " and "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2142", "text": "COM-2142"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Enable comment (view/add) on \"Reviewed\" rows ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2140", "text": "COM-2140"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Promotion job-title display issue ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2212", "text": "COM-2212"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Export filename bug ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2213", "text": "COM-2213"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAfter this continue with the rest of "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " from top to bottom."}]}]}]}, {"ts": "1706290164.420289", "text": "<@U0658EW4B8D> <@U065H3M6WJV> Do you have any suggestions on how to help <PERSON>?\nHi <PERSON><PERSON><PERSON>,\nCurious what you all and other customers are using to compile market data to create compensation bands? With the acquisition of OPtion Impact by Pave the data in there seems really spotty, not to mention we can't see equity because we don't have a cap table and therefore we have no data to upload in exchange for seeing their data. And Radford is just so confusing, its hard to understand what the right data is to look at.\nLet me know if you have any advice.\nThanks!\nlisa\n\n<@U065H3M6WJV> can you pls also try to get <PERSON>'s feedback on this?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1706290164.420289", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "OjIcx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Do you have any suggestions on how to help <PERSON>?\nHi <PERSON>,\nCurious what you all and other customers are using to compile market data to create compensation bands? With the acquisition of OPtion Impact by Pave the data in there seems really spotty, not to mention we can't see equity because we don't have a cap table and therefore we have no data to upload in exchange for seeing their data. And <PERSON><PERSON> is just so confusing, its hard to understand what the right data is to look at.\nLet me know if you have any advice.\nThanks!\nlisa\n\n"}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " can you pls also try to get <PERSON>'s feedback on this?"}]}]}]}, {"ts": "1706297135.377289", "text": "<@U04DS2MBWP4> Any topics for <PERSON> specifically today? (I have my call with her at 1:30)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706297135.377289", "reply_count": 9, "edited": {"user": "U065H3M6WJV", "ts": "1706297151.000000"}, "blocks": [{"type": "rich_text", "block_id": "+sS5Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Any topics for <PERSON> specifically today? (I have my call with her at 1:30)"}]}]}]}, {"ts": "**********.477929", "text": "Jotting this thought down before I forget: I've noticed a few times lately where the current product isn't displaying well on smaller screens.\n• During Wonolo demo, I had it configured with 6 budget types and the HR Admin view couldn't adequately show the different departments\n• <PERSON> was trying to view her account on her laptop, and the collapsed navigation panel had the \"&gt;&gt;\" icon overlapping with the Total Rewards icon, so it wasn't clear how to open it up again\nWe'll (eventually?) need a better plan for making these pages flexible for smaller screens.", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F06FYHEBC92", "created": **********, "timestamp": **********, "name": "Screenshot 2024-01-26 at 4.36.33 PM.png", "title": "Screenshot 2024-01-26 at 4.36.33 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 592752, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06FYHEBC92/screenshot_2024-01-26_at_4.36.33___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06FYHEBC92/download/screenshot_2024-01-26_at_4.36.33___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 223, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 297, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 446, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 495, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 594, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06FYHEBC92-69937b82be/screenshot_2024-01-26_at_4.36.33___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 634, "original_w": 3022, "original_h": 1870, "thumb_tiny": "AwAdADDRI9MCkx64pR+PWlyfQ0AIAO/WjC0uT6UZPpQAmFoCg0uT6UZPpQAdqBnvR2o5oAKOaCSMUmTigB1FJzQCc0Af/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06FYHEBC92/screenshot_2024-01-26_at_4.36.33___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06FYHEBC92-24012a6eef", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "bfrp0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Jotting this thought down before I forget: I've noticed a few times lately where the current product isn't displaying well on smaller screens.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "During Wonolo demo, I had it configured with 6 budget types and the HR Admin view couldn't adequately show the different departments"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> was trying to view her account on her laptop, and the collapsed navigation panel had the \">>\" icon overlapping with the Total Rewards icon, so it wasn't clear how to open it up again"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nWe'll (eventually?) need a better plan for making these pages flexible for smaller screens."}]}]}]}, {"ts": "**********.876529", "text": "<@U0658EW4B8D> Whenever you have a chance to respond, a general question about adjustment letters:\n• Do you anticipate _any_ customer who wants different templates/language for a \"merit increase\" vs a \"market adjustment\"? (Would they ever tell an employee their raise is a \"market adjustment, or split that out separately?)\n• Would _any_ customer need to communicate both a \"bonus award\" _and_ \"one time bonus\" for the same cycle? To the same employee?\n• Would any customer need to specify the difference in \"refresh equity\" and \"promotion equity\" for the same employee?\n• Are OTE communications generally different from non-OTE? For example, do they (almost) always need to communicate \"new\" salary &amp; variable amount (or OTE) for any comp adjustment?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.876529", "reply_count": 1, "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ffQDl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " Whenever you have a chance to respond, a general question about adjustment letters:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you anticipate "}, {"type": "text", "text": "any", "style": {"italic": true}}, {"type": "text", "text": " customer who wants different templates/language for a \"merit increase\" vs a \"market adjustment\"? (Would they ever tell an employee their raise is a \"market adjustment, or split that out separately?)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Would "}, {"type": "text", "text": "any ", "style": {"italic": true}}, {"type": "text", "text": "customer need to communicate both a \"bonus award\" "}, {"type": "text", "text": "and", "style": {"italic": true}}, {"type": "text", "text": " \"one time bonus\" for the same cycle? To the same employee?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Would any customer need to specify the difference in \"refresh equity\" and \"promotion equity\" for the same employee?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Are OTE communications generally different from non-OTE? For example, do they (almost) always need to communicate \"new\" salary & variable amount (or OTE) for any comp adjustment?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1706497447.757859", "text": "Priorities for Eng for the next day:\n• For Neuroflow: Ensure we are able to generate adjustment letters from formatted HTML with images (logo &amp; signature will be included, <https://docs.google.com/document/d/1I9JL5r4vtGi8UFMxAaeaXHzGOjsoSjQO/edit?usp=sharing&amp;ouid=107994932584597228039&amp;rtpof=true&amp;sd=true|see example>)\n• :repeat: SDF: Allow inputting new titles for promotions (<https://compiify.atlassian.net/browse/COM-2198|COM-2198>) - In development - *ETA 1/26*\n• :repeat: Fix adjustment letter variables (<https://compiify.atlassian.net/browse/COM-2224|COM-2224>)\n• :repeat: SDF / DA / Neuroflow: Date drift in cycle steps (<https://compiify.atlassian.net/browse/COM-2223|COM-2223>)\n• :repeat: Currency conversion issues (<https://compiify.atlassian.net/browse/COM-1919|COM-1919> and <https://compiify.atlassian.net/browse/COM-2142|COM-2142>)\n• :repeat: Enable comment (view/add) on \"Reviewed\" rows (<https://compiify.atlassian.net/browse/COM-2140|COM-2140>)\n• :repeat: Promotion job-title display issue (<https://compiify.atlassian.net/browse/COM-2212|COM-2212>)\n• :repeat: Export filename bug (<https://compiify.atlassian.net/browse/COM-2213|COM-2213>)\nAfter this continue with the rest of <https://compiify.atlassian.net/browse/COM-2087|Wave 3> from top to bottom.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706497447.757859", "reply_count": 6, "edited": {"user": "U065H3M6WJV", "ts": "1706497457.000000"}, "blocks": [{"type": "rich_text", "block_id": "xYjF5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For Neuroflow: Ensure we are able to generate adjustment letters from formatted HTML with images (logo & signature will be included, "}, {"type": "link", "url": "https://docs.google.com/document/d/1I9JL5r4vtGi8UFMxAaeaXHzGOjsoSjQO/edit?usp=sharing&ouid=107994932584597228039&rtpof=true&sd=true", "text": "see example"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " SDF: Allow inputting new titles for promotions ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2198", "text": "COM-2198"}, {"type": "text", "text": ") - In development - "}, {"type": "text", "text": "ETA 1/26", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Fix adjustment letter variables ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2224", "text": "COM-2224"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " SDF / DA / Neuroflow: Date drift in cycle steps ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2223", "text": "COM-2223"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Currency conversion issues ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1919", "text": "COM-1919"}, {"type": "text", "text": " and "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2142", "text": "COM-2142"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Enable comment (view/add) on \"Reviewed\" rows ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2140", "text": "COM-2140"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Promotion job-title display issue ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2212", "text": "COM-2212"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Export filename bug ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2213", "text": "COM-2213"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAfter this continue with the rest of "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " from top to bottom."}]}]}]}, {"ts": "1706571404.806089", "text": "SDF training session is in the books! :books:\n• The repurposed sdf-test environment allowed us to demo SDF's customizations without exposing any real salaries\n• Their managers had the most questions about pay bands, mainly wanting reassurance that each employee's pay band would be the correct one for their location &amp; currency\n• <PERSON><PERSON> had compiled the main talking points she wanted to cover, so that made it easier to focus the training on just what the managers needed to know\nNext up:\n• We'll need to email login credentials when <PERSON><PERSON>/<PERSON> give us the green light (they want to do additional updates to the comp bands first)\n• We'll ask those managers to use <mailto:<EMAIL>|<EMAIL>> to send their questions &amp; feedback\n• Compiify team may need to make manual updates to promos once their committee approves/rejects promo nominations (around Feb 23)\n• Depending on how <PERSON>/<PERSON><PERSON> want to handle adjustment letters, we may need to do an additional training session with the same leaders at the end of cycle to show how to download and distribute those", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706571404.806089", "reply_count": 3, "reactions": [{"name": "heart", "users": ["U04DS2MBWP4", "U04DKEFP1K8", "U0658EW4B8D"], "count": 3}, {"name": "rocket", "users": ["U04DS2MBWP4", "U0658EW4B8D"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "ECFRJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF training session is in the books! "}, {"type": "emoji", "name": "books", "unicode": "1f4da"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The repurposed sdf-test environment allowed us to demo SDF's customizations without exposing any real salaries"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Their managers had the most questions about pay bands, mainly wanting reassurance that each employee's pay band would be the correct one for their location & currency"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON> had compiled the main talking points she wanted to cover, so that made it easier to focus the training on just what the managers needed to know"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nNext up:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We'll need to email login credentials when <PERSON><PERSON><PERSON> give us the green light (they want to do additional updates to the comp bands first)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "We'll ask those managers to use "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " to send their questions & feedback"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Compiify team may need to make manual updates to promos once their committee approves/rejects promo nominations (around Feb 23)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Depending on how <PERSON>/<PERSON><PERSON> want to handle adjustment letters, we may need to do an additional training session with the same leaders at the end of cycle to show how to download and distribute those"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1706580270.679249", "text": "<@U0658EW4B8D> The QA environment should be updated now with the corrected adjustment letters; will you have time to test tomorrow? I've changed our meeting time to 5p (because I'm on a demo at 4), is it possible to have some initial testing &amp; feedback ready before then?", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "NluA+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " The QA environment should be updated now with the corrected adjustment letters; will you have time to test tomorrow? I've changed our meeting time to 5p (because I'm on a demo at 4), is it possible to have some initial testing & feedback ready before then?"}]}]}]}, {"ts": "1706581245.786499", "text": "Priorities for Eng for the next day:\n• Password reset flow (<https://compiify.atlassian.net/browse/COM-2240|COM-2240>) - This will be more critical when we invite SDF managers into the tool!\n• Prevent \"Reviewed\" with empty fields (<https://compiify.atlassian.net/browse/COM-2144|COM-2144>) - This was introducing other anomalies with \"Reviewed\" employees, but I think we can fix just with this one so I increased its urgency within Wave 3\n• Allow clicking recommended values to input (<https://compiify.atlassian.net/browse/COM-2013|COM-2013>)\n• Enter key / Duplicate comment issue (<https://compiify.atlassian.net/browse/COM-2242|COM-2242>)\nAfter these, continue with <https://compiify.atlassian.net/browse/COM-2087|Wave 3> from the top of the list, especially the Reports-related items.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706581245.786499", "reply_count": 4, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Xcvnn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Password reset flow ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2240", "text": "COM-2240"}, {"type": "text", "text": ") - This will be more critical when we invite SDF managers into the tool!"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Prevent \"Reviewed\" with empty fields ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2144", "text": "COM-2144"}, {"type": "text", "text": ") - This was introducing other anomalies with \"Reviewed\" employees, but I think we can fix just with this one so I increased its urgency within Wave 3"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Allow clicking recommended values to input ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2013", "text": "COM-2013"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Enter key / Duplicate comment issue ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2242", "text": "COM-2242"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAfter these, continue with "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " from the top of the list, especially the Reports-related items."}]}]}]}, {"ts": "1706645960.188759", "text": "Doing some light testing on adjustment letters myself, a few things I noticed:\n• \"Deleting\" an adjustment letter doesn't seem to remove the icons for the previously generated letter, and clicking those will produce an error\n• At least one instance has a broken image for the logo\n• The formatting of the monetary values should include comma separators\n• The formatting of the dates might look better using \"Month DD, YYYY\" instead of \"MM-DD-YYYY\"\n• I can't tell how much of my template edits were used, because I see some of my changes and formatting applied, but the line breaks are still different from what I'd input.\nExamples attached -- <PERSON><PERSON> was working for one but not the other.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706645960.188759", "reply_count": 3, "files": [{"id": "F06G5MYBWMC", "created": 1706645944, "timestamp": 1706645944, "name": "salary_bonus_1706645582066.pdf", "title": "salary_bonus_1706645582066.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 38588, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06G5MYBWMC/salary_bonus_1706645582066.pdf", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06G5MYBWMC/download/salary_bonus_1706645582066.pdf", "media_display_type": "unknown", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F06G5MYBWMC-2c838d299a/salary_bonus_1706645582066_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06G5MYBWMC/salary_bonus_1706645582066.pdf", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06G5MYBWMC-bee0cc79a5", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06H14WJPQQ", "created": 1706645946, "timestamp": 1706645946, "name": "bonus_1706645716134.pdf", "title": "bonus_1706645716134.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 25724, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06H14WJPQQ/bonus_1706645716134.pdf", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06H14WJPQQ/download/bonus_1706645716134.pdf", "media_display_type": "unknown", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F06H14WJPQQ-2739e0a183/bonus_1706645716134_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06H14WJPQQ/bonus_1706645716134.pdf", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06H14WJPQQ-6e32d8e361", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ZOTmq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Doing some light testing on adjustment letters myself, a few things I noticed:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "\"Deleting\" an adjustment letter doesn't seem to remove the icons for the previously generated letter, and clicking those will produce an error"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "At least one instance has a broken image for the logo"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The formatting of the monetary values should include comma separators"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The formatting of the dates might look better using \"Month DD, YYYY\" instead of \"MM-DD-YYYY\""}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "I can't tell how much of my template edits were used, because I see some of my changes and formatting applied, but the line breaks are still different from what I'd input."}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nExamples attached -- <PERSON><PERSON> was working for one but not the other."}]}]}]}, {"ts": "1706648206.601819", "text": "<@U04DKEFP1K8> I think we'll end up needing to support some bulk actions for generating, releasing, and downloading adjustment letters. Was anything started along those lines, or do we need to spec it out from scratch?\n\nI also think it'll need filtering in the employee list (rather than only having search for a single employee) so that someone could generate/download letters for an entire team or department.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706648206.601819", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "vaL6W", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I think we'll end up needing to support some bulk actions for generating, releasing, and downloading adjustment letters. Was anything started along those lines, or do we need to spec it out from scratch?\n\nI also think it'll need filtering in the employee list (rather than only having search for a single employee) so that someone could generate/download letters for an entire team or department."}]}]}]}, {"ts": "1706662687.254479", "text": "Open to feedback: I've created a high-level \"<https://www.figma.com/file/HDEGIXOCpeSBahiqDzb0Vc/User-Flows%3A-Comp-Planning-Cycle?type=whiteboard&amp;node-id=0-1&amp;t=KpHOZkF1EwQnmUn6-0|User flow>\" to try and map how different roles in an organization are involved in a comp planning cycle. It may be a little simplified, but trying to capture some of the main tasks that each role needs to complete and how that fits into the context of what's happening around them with budgets, approvals, and deadlines.\n\nFeel free to add post-it note comments in the Figjam, or to just Slack me any feedback you have. I'm hoping this will help our UX designer ramp up more quickly on the concept of comp cycle workflows. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706662687.254479", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "OpJuI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Open to feedback: I've created a high-level \""}, {"type": "link", "url": "https://www.figma.com/file/HDEGIXOCpeSBahiqDzb0Vc/User-Flows%3A-Comp-Planning-Cycle?type=whiteboard&node-id=0-1&t=KpHOZkF1EwQnmUn6-0", "text": "User flow"}, {"type": "text", "text": "\" to try and map how different roles in an organization are involved in a comp planning cycle. It may be a little simplified, but trying to capture some of the main tasks that each role needs to complete and how that fits into the context of what's happening around them with budgets, approvals, and deadlines.\n\nFeel free to add post-it note comments in the Figjam, or to just Slack me any feedback you have. I'm hoping this will help our UX designer ramp up more quickly on the concept of comp cycle workflows. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1706666931.246259", "text": "Priorities for Eng for the next day:\n• :repeat: Password reset flow (<https://compiify.atlassian.net/browse/COM-2240|COM-2240>)\n• Adjustment Letter tickets in Neuroflow UAT (<https://compiify.atlassian.net/browse/COM-2145|COM-2145>) with the exception of <https://compiify.atlassian.net/browse/COM-2251|COM-2251> which does not have design yet\n• :repeat: Allow clicking recommended values to input (<https://compiify.atlassian.net/browse/COM-2013|COM-2013>)\nAfter these, continue with <https://compiify.atlassian.net/browse/COM-2087|Wave 3> from the top of the list, especially the Reports-related items.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4xjjS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Password reset flow ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2240", "text": "COM-2240"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Adjustment Letter tickets in Neuroflow UAT ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2145", "text": "COM-2145"}, {"type": "text", "text": ") with the exception of "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2251", "text": "COM-2251"}, {"type": "text", "text": " which does not have design yet"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Allow clicking recommended values to input ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2013", "text": "COM-2013"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAfter these, continue with "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " from the top of the list, especially the Reports-related items."}]}]}]}, {"ts": "1706666954.334779", "text": "<@U0658EW4B8D> Can you share your Adjustment letters test &amp; pass/fail spreadsheet here?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706666954.334779", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "TYKur", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " Can you share your Adjustment letters test & pass/fail spreadsheet here?"}]}]}]}]}