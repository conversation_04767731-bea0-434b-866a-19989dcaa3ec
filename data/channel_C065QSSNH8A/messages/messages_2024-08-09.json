{"date": "2024-08-09", "channel_id": "C065QSSNH8A", "message_count": 20, "messages": [{"ts": "**********.089519", "text": "<!here> my daughter has fractured her finger so I’m at the hospital right now. I’ll likely have to skip the meeting, but I’ll try to dial if I can through the phone and will be in the listening mode.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.089519", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "mJSlE", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " my daughter has fractured her finger so I’m at the hospital right now. I’ll likely have to skip the meeting, but I’ll try to dial if I can through the phone and will be in the listening mode."}]}]}]}, {"ts": "**********.248149", "text": "In the Compensation Data Template downloaded from an account, there is a duplicate Target Bonus Currency column.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.248149", "reply_count": 3, "files": [{"id": "F07GA44KEG2", "created": **********, "timestamp": **********, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 25159, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07GA44KEG2/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07GA44KEG2/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GA44KEG2-47c5802ad4/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GA44KEG2-47c5802ad4/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GA44KEG2-47c5802ad4/image_360.png", "thumb_360_w": 360, "thumb_360_h": 136, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GA44KEG2-47c5802ad4/image_480.png", "thumb_480_w": 480, "thumb_480_h": 181, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GA44KEG2-47c5802ad4/image_160.png", "original_w": 690, "original_h": 260, "thumb_tiny": "AwASADDQ+c8jGKMP6inL90UtADfn9vzoy3tTqQ9RQAnz/wCz+dGW74p1I3T8aAET7i/SnU2P/Vr9KdQAU0/eWnU1vvrQA6mv0/EU6mv938R/OgD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07GA44KEG2/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07GA44KEG2-68d68167c4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "01M/3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In the Compensation Data Template downloaded from an account, there is a duplicate Target Bonus Currency column."}]}]}]}, {"ts": "**********.569189", "text": "What is the intention of the 'Compensation Data Template' that has only 6 columns", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.569189", "reply_count": 1, "files": [{"id": "F07GC7WNA5A", "created": **********, "timestamp": **********, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 26243, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07GC7WNA5A/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07GC7WNA5A/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_360.png", "thumb_360_w": 360, "thumb_360_h": 33, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_480.png", "thumb_480_w": 480, "thumb_480_h": 44, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_720.png", "thumb_720_w": 720, "thumb_720_h": 66, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_800.png", "thumb_800_w": 800, "thumb_800_h": 73, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_960.png", "thumb_960_w": 960, "thumb_960_h": 87, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07GC7WNA5A-0e51e629ec/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 93, "original_w": 1110, "original_h": 101, "thumb_tiny": "AwAEADDRT7tAAJPA60J92lHf60ANx+8/CnADJPvSf8tD9KUd/rQAij5moAG48UL95qB1NAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07GC7WNA5A/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07GC7WNA5A-7f8559094d", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "wrFjX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "What is the intention of the 'Compensation Data Template' that has only 6 columns"}]}]}]}, {"ts": "1723200060.694409", "text": "<!here> Revoke feature after approval for more flexibility. This is released to <http://test.stridehr.io|test.stridehr.io>\n<https://www.loom.com/share/73de84896407492ea6b3b78f3cb251b3?sid=8613e12d-ebfa-413a-aa8e-19b1ccdef1c2>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723200060.694409", "reply_count": 2, "edited": {"user": "U0690EB5JE5", "ts": "1723200093.000000"}, "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "GVyDa", "video_url": "https://www.loom.com/embed/73de84896407492ea6b3b78f3cb251b3?sid=8613e12d-ebfa-413a-aa8e-19b1ccdef1c2&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/73de84896407492ea6b3b78f3cb251b3-6d687821564de0e9-4x3.jpg", "alt_text": "Merit View | Stride - 9 August 2024", "title": {"type": "plain_text", "text": "Merit View | Stride - 9 August 2024", "emoji": true}, "title_url": "https://www.loom.com/share/73de84896407492ea6b3b78f3cb251b3", "author_name": "<PERSON><PERSON><PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 1 min  ", "emoji": true}}, {"type": "actions", "block_id": "cH9yE", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/73de84896407492ea6b3b78f3cb251b3?sid=8613e12d-ebfa-413a-aa8e-19b1ccdef1c2"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"73de84896407492ea6b3b78f3cb251b3\",\"videoName\":\"Merit View | Stride - 9 August 2024\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/73de84896407492ea6b3b78f3cb251b3?sid=8613e12d-ebfa-413a-aa8e-19b1ccdef1c2", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "lLgVJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Revoke feature after approval for more flexibility. This is released to "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://www.loom.com/share/73de84896407492ea6b3b78f3cb251b3?sid=8613e12d-ebfa-413a-aa8e-19b1ccdef1c2"}]}]}]}, {"ts": "1723195841.274629", "text": "Also please note that we already have feature in place to automatically notify mangers when the manager down level submits but is disabled by default.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723195841.274629", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1723195855.000000"}, "files": [{"id": "F07G8F68AN6", "created": 1723195837, "timestamp": 1723195837, "name": "Screenshot 2024-08-09 at 2.59.52 PM.png", "title": "Screenshot 2024-08-09 at 2.59.52 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 169274, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07G8F68AN6/screenshot_2024-08-09_at_2.59.52___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07G8F68AN6/download/screenshot_2024-08-09_at_2.59.52___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 181, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 242, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 363, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 403, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 484, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07G8F68AN6-d73b58cc86/screenshot_2024-08-09_at_2.59.52___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 516, "original_w": 1710, "original_h": 862, "thumb_tiny": "AwAYADDRx0Pf6U6m9R0NOoAKKKOaACkYDB+lLSN90/SgANFFFABR36UUo6UAFI33T9KWkb7p+lAH/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07G8F68AN6/screenshot_2024-08-09_at_2.59.52___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07G8F68AN6-bd42823a7f", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "K7u5N", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also please note that we already have feature in place to automatically notify mangers when the manager down level submits but is disabled by default."}]}]}]}, {"ts": "1723195696.484049", "text": "<!here> We have minor enhancement which was not finished and dev complete now. Basically a feature to remind managers to complete their review. changes are in <http://test.stridehr.io|test.stridehr.io>\n<https://www.loom.com/share/b161a29a22d94e3b92eec1041af4f104?sid=ccfcbe60-add3-4d9b-aed9-1101744f3047>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723195696.484049", "reply_count": 3, "edited": {"user": "U0690EB5JE5", "ts": "1723200957.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "q7HbY", "video_url": "https://www.loom.com/embed/b161a29a22d94e3b92eec1041af4f104?sid=ccfcbe60-add3-4d9b-aed9-1101744f3047&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/b161a29a22d94e3b92eec1041af4f104-9cab8558dcc5828e-4x3.jpg", "alt_text": "Merit View | Stride - 9 August 2024", "title": {"type": "plain_text", "text": "Merit View | Stride - 9 August 2024", "emoji": true}, "title_url": "https://www.loom.com/share/b161a29a22d94e3b92eec1041af4f104", "author_name": "<PERSON><PERSON><PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 56 sec  ", "emoji": true}}, {"type": "actions", "block_id": "WNo/P", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/b161a29a22d94e3b92eec1041af4f104?sid=ccfcbe60-add3-4d9b-aed9-1101744f3047"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"b161a29a22d94e3b92eec1041af4f104\",\"videoName\":\"Merit View | Stride - 9 August 2024\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/b161a29a22d94e3b92eec1041af4f104?sid=ccfcbe60-add3-4d9b-aed9-1101744f3047", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "4WbRt", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " We have minor enhancement which was not finished and dev complete now. Basically a feature to remind managers to complete their review. changes are in "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://www.loom.com/share/b161a29a22d94e3b92eec1041af4f104?sid=ccfcbe60-add3-4d9b-aed9-1101744f3047"}]}]}]}, {"ts": "1723190831.756249", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> Do we have the file they were trying to upload?\n<https://stride-hr.slack.com/archives/C0702497X55/p1723154498825179>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723190831.756249", "reply_count": 4, "attachments": [{"from_url": "https://stride-hr.slack.com/archives/C0702497X55/p1723154498825179", "ts": "1723154498.825179", "author_id": "U06RSCZ3EFP", "channel_id": "C0702497X55", "channel_team": "T04DM97F1UM", "is_msg_unfurl": true, "is_thread_root_unfurl": true, "message_blocks": [{"team": "T04DM97F1UM", "channel": "C0702497X55", "ts": "1723154498.825179", "message": {"blocks": [{"type": "rich_text", "block_id": "8q8m7", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " this afternoon i've been attempting to upload updated data to the stride system yet every time it appears the upload is nearly complete i get an \"internal error\" message. might someone be able to help me with this?"}]}]}]}}], "id": 1, "original_url": "https://stride-hr.slack.com/archives/C0702497X55/p1723154498825179", "fallback": "[August 8th, 2024 3:01 PM] j<PERSON><PERSON><PERSON>: <!here> this afternoon i've been attempting to upload updated data to the stride system yet every time it appears the upload is nearly complete i get an \"internal error\" message. might someone be able to help me with this?", "text": "<!here> this afternoon i've been attempting to upload updated data to the stride system yet every time it appears the upload is nearly complete i get an \"internal error\" message. might someone be able to help me with this?", "author_name": "jennifer ho<PERSON> (she/her/hers)", "author_link": "https://stride-hr.slack.com/team/U06RSCZ3EFP", "author_icon": "https://avatars.slack-edge.com/2024-04-01/6892202092435_985e2599a3e466ccd31e_48.jpg", "author_subname": "jennifer ho<PERSON> (she/her/hers)", "mrkdwn_in": ["text"], "footer": "Thread in Slack Conversation"}], "blocks": [{"type": "rich_text", "block_id": "z7tVw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Do we have the file they were trying to upload?\n"}, {"type": "link", "url": "https://stride-hr.slack.com/archives/C0702497X55/p1723154498825179"}]}]}]}, {"ts": "1723183220.172729", "text": "Do we have any different expectation from Practifi/Valgenesis other than what is being implemented?\n<https://compiify.atlassian.net/browse/COM-3369>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1723186845.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13430::a9672aa0561411efae3a1b0f8cac9ce6", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3369?atlOrigin=eyJpIjoiYjU0ZTQxYjg5MGU5NDAyNWIzZWFiMGI4OTc2ZGU3MWMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3369 Manage OTE workflows for Practifi>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13430::a9672aa2561411efae3a1b0f8cac9ce6", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13430::a9672aa1561411efae3a1b0f8cac9ce6", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13430\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13430\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3369", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "LKFXC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we have any different expectation from Practifi/Valgenesis other than what is being implemented?\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3369"}]}]}]}, {"ts": "1723154506.605739", "text": "we can also check with her on the ETA for filled up templates if the files do not contain all the data", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BipCQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we can also check with her on the ETA for filled up templates if the files do not contain all the data"}]}]}]}, {"ts": "1723154465.917399", "text": "<@U07EJ2LP44S> can you please work with <PERSON><PERSON><PERSON><PERSON> on the Diversified?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "15Vr4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can you please work with <PERSON><PERSON><PERSON><PERSON> on the Diversified?"}]}]}]}, {"ts": "1723151686.379129", "text": "I think she's a payroll only person so I'm not really sure how to deal with her and she seems a bit snippy about this. I can try and remap the data and then tell her what we're missing, perhaps.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "UGDKw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think she's a payroll only person so I'm not really sure how to deal with her and she seems a bit snippy about this. I can try and remap the data and then tell her what we're missing, perhaps."}]}]}]}, {"ts": "1723151590.034019", "text": "The only question I have is <PERSON>/<PERSON>vers<PERSON> - she said she was expecting templates but then she said she sent everything over. From what I can tell her files don't contain all the info but I can look again and see if I'm wrong", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZxYvk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The only question I have is <PERSON>/<PERSON>vers<PERSON> - she said she was expecting templates but then she said she sent everything over. From what I can tell her files don't contain all the info but I can look again and see if I'm wrong"}]}]}]}, {"ts": "1723151504.292899", "text": "We have quite a list of to dos :slightly_smiling_face:", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oNM1+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We have quite a list of to dos "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1723151493.952239", "text": "do we need to connect Kapil?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sOy+1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "do we need to connect Kapil?"}]}]}]}, {"ts": "1723151479.943489", "text": "we just finished the call and i am running behind on updating na<PERSON>'s env so will use the time for it", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/qCJz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we just finished the call and i am running behind on updating na<PERSON>'s env so will use the time for it"}]}]}]}, {"ts": "1723151473.500709", "text": "We are ready (or at least I am!0", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XyEHk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We are ready (or at least I am!0"}]}]}]}, {"ts": "1723151047.124339", "text": "øk", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0H+eo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "øk"}]}]}]}, {"ts": "1723150830.129069", "text": "<@U04DS2MBWP4> <PERSON> and I are on a call to sort out practifi issue", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4EN6s", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " <PERSON> and I are on a call to sort out practifi issue"}]}]}]}, {"ts": "**********.009279", "text": "\"Job Title\"", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OU+Jh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "\"Job Title\""}]}]}]}, {"ts": "**********.372309", "text": "Job title does not appear to be a field in the CompensationBandsTemplate download from an account", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.372309", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "f5npJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Job title does not appear to be a field in the CompensationBandsTemplate download from an account"}]}]}]}], "created_at": "2025-05-22T21:35:34.630795"}