#!/usr/bin/env python
"""
Script to clean databases and ingest Slack data.
"""

import os
import sys
import json
import django
import logging
from datetime import datetime

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk, EmbeddingMetadata
from apps.core.utils.vectorstore import get_qdrant_client

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_database():
    """Clean the database by removing all documents, chunks, and related data."""
    logger.info("Starting database cleaning process...")

    # Delete all embedding metadata
    embedding_count = EmbeddingMetadata.objects.count()
    EmbeddingMetadata.objects.all().delete()
    logger.info(f"Deleted {embedding_count} embedding metadata records")

    # Delete all document chunks
    chunk_count = DocumentChunk.objects.count()
    DocumentChunk.objects.all().delete()
    logger.info(f"Deleted {chunk_count} document chunks")

    # Delete all raw documents
    doc_count = RawDocument.objects.count()
    RawDocument.objects.all().delete()
    logger.info(f"Deleted {doc_count} raw documents")

    # Delete all document sources
    source_count = DocumentSource.objects.count()
    DocumentSource.objects.all().delete()
    logger.info(f"Deleted {source_count} document sources")

    # Clean Qdrant vector database
    try:
        # Get Qdrant client
        client = get_qdrant_client()

        # Get all tenants
        tenants = Tenant.objects.all()

        # Delete collections for each tenant
        for tenant in tenants:
            collection_name = f"{tenant.slug}_chunks"
            try:
                if client.collection_exists(collection_name):
                    client.delete_collection(collection_name)
                    logger.info(f"Deleted vector collection: {collection_name}")
            except Exception as e:
                logger.error(f"Error deleting vector collection {collection_name}: {str(e)}")
    except Exception as e:
        logger.error(f"Error cleaning vector database: {str(e)}")

    logger.info("Database cleaning completed successfully")

def create_document_source(tenant):
    """Create a document source for the LocalSlackSourceInterface."""
    try:
        # Define the configuration for the LocalSlackSourceInterface
        config = {
            "data_dir": "data/",  # Path to the data directory
            "time_period": "custom",
            "custom_days": 730,  # Two years
            "enable_semantic_cross_refs": True,
            "quality_threshold": 0.3,
        }

        # Create the document source
        source = DocumentSource.objects.create(
            tenant=tenant,
            name="Local Slack Data",
            source_type="local_slack",
            config=config,
            is_active=True
        )

        logger.info(f"Created document source: {source.name} ({source.source_type})")
        return source

    except Exception as e:
        logger.error(f"Error creating document source: {str(e)}")
        return None

def create_slack_documents(tenant):
    """Create Slack documents directly from the message files."""
    logger.info("Creating Slack documents directly from message files...")

    # Path to the messages directory
    messages_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                               "data", "channel_C065QSSNH8A", "messages")

    if not os.path.exists(messages_dir):
        logger.error(f"Messages directory not found: {messages_dir}")
        return []

    # Get list of message files
    message_files = [f for f in os.listdir(messages_dir) if f.endswith(".json")]
    logger.info(f"Found {len(message_files)} message files")

    # Create documents
    documents = []
    for i, filename in enumerate(message_files):
        try:
            file_path = os.path.join(messages_dir, filename)
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Extract messages
            messages = data.get('messages', []) if isinstance(data, dict) else data

            # Create content from messages
            content = "\n\n".join([
                f"User: {msg.get('user', 'unknown')}\nTime: {msg.get('ts', '')}\nMessage: {msg.get('text', '')}"
                for msg in messages if msg.get('type') == 'message' and msg.get('text')
            ])

            # Create document
            if content.strip():
                doc = RawDocument.objects.create(
                    tenant=tenant,
                    title=f"Slack Messages - {filename.replace('.json', '')}",
                    content=content,
                    content_type="text/slack",
                    source_type="slack",
                    source_id=f"slack-{filename}",
                    metadata={
                        "channel": "C065QSSNH8A",
                        "date": filename.replace('messages_', '').replace('.json', ''),
                        "message_count": len(messages)
                    }
                )
                documents.append(doc)
                logger.info(f"Created document {i+1}/{len(message_files)}: {doc.title}")
        except Exception as e:
            logger.error(f"Error processing file {filename}: {str(e)}")

    logger.info(f"Created {len(documents)} documents")
    return documents

def create_chunks_from_document(document):
    """Create chunks from a document."""
    # Simple chunking strategy - split by paragraphs
    content = document.content
    chunks = []

    # Split content by paragraphs
    paragraphs = content.split("\n\n")

    # Create chunks
    for i, paragraph in enumerate(paragraphs):
        if paragraph.strip():
            chunk = DocumentChunk(
                tenant=document.tenant,
                document=document,
                content=paragraph,
                chunk_index=i,
                metadata={
                    "source": document.source_type,
                    "title": document.title,
                    "chunk_index": i,
                    "total_chunks": len(paragraphs)
                }
            )
            chunk.save()
            chunks.append(chunk)

    return chunks

def print_database_stats():
    """Print database statistics."""
    logger.info("Database Statistics:")
    logger.info(f"Raw Documents: {RawDocument.objects.count()}")
    logger.info(f"Document Chunks: {DocumentChunk.objects.count()}")
    logger.info(f"Document Sources: {DocumentSource.objects.count()}")
    logger.info(f"Embedding Metadata: {EmbeddingMetadata.objects.count()}")

def main():
    """Main function to clean databases and ingest Slack data."""
    logger.info("Starting database cleaning and Slack data ingestion...")

    # Clean the database first
    clean_database()

    # Get or create tenant
    tenant, created = Tenant.objects.get_or_create(
        slug="default",
        defaults={"name": "Default Tenant"}
    )

    # Print initial database stats
    logger.info("Initial database stats:")
    print_database_stats()

    # Create Slack documents
    documents = create_slack_documents(tenant)
    if not documents:
        logger.error("No documents created. Exiting.")
        return

    # Create chunks from documents
    for doc in documents:
        chunks = create_chunks_from_document(doc)
        logger.info(f"Created {len(chunks)} chunks for document: {doc.title}")

    # Print final database stats
    logger.info("Final database stats after ingestion:")
    print_database_stats()

    logger.info("Database cleaning and Slack data ingestion completed.")

if __name__ == "__main__":
    main()
