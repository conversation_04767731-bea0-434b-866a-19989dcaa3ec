{"date": "2023-12-31", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1704001019.115099", "text": "Update on bugs from UAT: I've created new epics to help organize these issues into \"waves\" so that it's easier to know which issues should come first, next, etc.\n\n• <https://compiify.atlassian.net/browse/COM-2084|COM-2084: Showstoppers> (currently 4 issues)\n• <https://compiify.atlassian.net/browse/COM-2085|COM-2085: Wave 1> (currently 21 issues)\n• <https://compiify.atlassian.net/browse/COM-2086|COM-2086: Wave 2> (currently 22 issues)\n• <https://compiify.atlassian.net/browse/COM-2087|COM-2087: Wave 3> (currently 23 issues)\n• <https://compiify.atlassian.net/browse/COM-2088|COM-2088: Wave 4> (currently 29 issues)\nIn order to get these done in time for the DA/SDF comp cycles, we'll need to target to complete each wave within 2 or 3 days, so hopefully <@U04DKEFP1K8> you're able to set that expectation with the team. It's a lot to get done but with some momentum, I'm sure we can make great progress. :meow_code:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1704001019.115099", "reply_count": 2, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4", "U04DKEFP1K8"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "xx6m8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Update on bugs from UAT: I've created new epics to help organize these issues into \"waves\" so that it's easier to know which issues should come first, next, etc.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2084", "text": "COM-2084: Showstoppers"}, {"type": "text", "text": " (currently 4 issues)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2085", "text": "COM-2085: Wave 1"}, {"type": "text", "text": " (currently 21 issues)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2086", "text": "COM-2086: Wave 2"}, {"type": "text", "text": " (currently 22 issues)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "COM-2087: Wave 3"}, {"type": "text", "text": " (currently 23 issues)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2088", "text": "COM-2088: Wave 4"}, {"type": "text", "text": " (currently 29 issues)"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nIn order to get these done in time for the DA/SDF comp cycles, we'll need to target to complete each wave within 2 or 3 days, so hopefully "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " you're able to set that expectation with the team. It's a lot to get done but with some momentum, I'm sure we can make great progress. "}, {"type": "emoji", "name": "meow_code"}]}]}]}], "created_at": "2025-05-22T21:35:34.595645"}