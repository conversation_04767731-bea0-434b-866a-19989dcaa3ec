{"date": "2023-12-18", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1702858833.061379", "text": "Some observations based on this exercise:\n• Unclear how Lumen Grants should be included in the total annual rewards chart -- once we have a Fair Market Value, I can redo the donut graph, but the vesting graph might still be a challenge\n• The benefits categories don't really match what's in the Compiify Figma or PRD docs; we need to decide how flexible we'll make it for customers to define their own categories of benefits for grouping, for example\n• Some of the benefits values felt low, so I played with hiding some specific values while still including them in the rolled-up total\n• The 2 employees I mocked up are in different US states, and I would expect the actual value of their benefits & perks to differ, but we didn't get this level of detail from Katya.\n• Our Salary band design doesn't currently include anything to distinguish location/tier that could affect the ranges for employees in the same Job level. ", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702858833.061379", "reply_count": 3, "edited": {"user": "U065H3M6WJV", "ts": "1702859164.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "1/kYc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Some observations based on this exercise:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Unclear how Lumen Grants should be included in the total annual rewards chart -- once we have a Fair Market Value, I can redo the donut graph, but the vesting graph might still be a challenge"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The benefits categories don't really match what's in the Compiify Figma or PRD docs; we need to decide how flexible we'll make it for customers to define their own categories of benefits for grouping, for example"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Some of the benefits values felt low, so I played with hiding some specific values while still including them in the rolled-up total"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The 2 employees I mocked up are in different US states, and I would expect the actual value of their benefits & perks to differ, but we didn't get this level of detail from <PERSON><PERSON>."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Our Salary band design doesn't currently include anything to distinguish location/tier that could affect the ranges for employees in the same Job level. "}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1702858701.026759", "text": "I'm trying to map Stellar's benefits and salary bands into our Total Rewards format, and decided to mock this up in a spreadsheet first before doing it in Figma. :slightly_smiling_face:\n\n<https://docs.google.com/spreadsheets/d/143Nqi24bq68Gr6stD-WcO2xGoMPPh3h_b3uJzX-vgDg/edit#gid=120147510|Have a look here>; I pulled a couple random employees and mapped their actual salaries, salary band positions, and comparatios. I also made an attempt to total up the Benefits &amp; Perks, but I think we'll need to have <PERSON><PERSON> &amp; <PERSON> react to the way these are presented.", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F06ABVBB75L", "created": 1702858704, "timestamp": 1702858704, "name": "Mockups of Total Rewards for Stellar", "title": "Mockups of Total Rewards for Stellar", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "143Nqi24bq68Gr6stD-WcO2xGoMPPh3h_b3uJzX-vgDg", "external_url": "https://docs.google.com/spreadsheets/d/143Nqi24bq68Gr6stD-WcO2xGoMPPh3h_b3uJzX-vgDg/edit#gid=120147510", "url_private": "https://docs.google.com/spreadsheets/d/143Nqi24bq68Gr6stD-WcO2xGoMPPh3h_b3uJzX-vgDg/edit#gid=120147510", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHQZmDYUD8aAz7sEL+dKfvijjcfWgB1FFFABRRRQA1gSaAOc0jgFuSfwOKAvJ5b/vqgB9FIBgYyfxNLQAUUUUANIy9J/ER604rk5oA5oAAD7flS0UUAFFFFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06ABVBB75L/mockups_of_total_rewards_for_stellar", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "1uF+6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'm trying to map Stellar's benefits and salary bands into our Total Rewards format, and decided to mock this up in a spreadsheet first before doing it in Figma. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": "\n\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/143Nqi24bq68Gr6stD-WcO2xGoMPPh3h_b3uJzX-vgDg/edit#gid=120147510", "text": "Have a look here"}, {"type": "text", "text": "; I pulled a couple random employees and mapped their actual salaries, salary band positions, and comparatios. I also made an attempt to total up the Benefits & Perks, but I think we'll need to have Katya & Lisa react to the way these are presented."}]}]}]}], "created_at": "2025-05-22T21:35:34.562660"}