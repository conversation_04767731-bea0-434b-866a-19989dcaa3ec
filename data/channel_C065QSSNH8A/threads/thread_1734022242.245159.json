{"thread_ts": "**********.245159", "channel_id": "C065QSSNH8A", "reply_count": 11, "replies": [{"ts": "**********.769249", "text": "<@U0690EB5JE5> is there anyway we can get this done tomorrow? They are looking to have the updated data so they can run some testing in their account on fresh data.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.245159", "blocks": [{"type": "rich_text", "block_id": "OW4f3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " is there anyway we can get this done tomorrow? They are looking to have the updated data so they can run some testing in their account on fresh data."}]}]}]}, {"ts": "**********.647159", "text": "I have some CTC refresh data to upload, but I want to do the Hibob refresh first", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.245159", "blocks": [{"type": "rich_text", "block_id": "wdOz7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have some CTC refresh data to upload, but I want to do the Hibob refresh first"}]}]}]}, {"ts": "**********.983049", "text": "Let me try and get back.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.245159", "blocks": [{"type": "rich_text", "block_id": "X66B6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me try and get back."}]}]}]}, {"ts": "**********.244629", "text": "<@U07EJ2LP44S> There was a sync error due to two employees having same employeeId\nEmployeeId 2333  <PERSON><PERSON>\nEmployeeId 2333  <PERSON><PERSON><PERSON> reddy", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.245159", "blocks": [{"type": "rich_text", "block_id": "BaJXO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " There was a sync error due to two employees having same employeeId\nEmployeeId 2333  <PERSON><PERSON> Kumar\nEmployeeId 2333  <PERSON><PERSON><PERSON> reddy"}]}]}]}, {"ts": "1734708803.059079", "text": "System expects unique business employeeId. Please clarify with customer on this. If this is true and then how to handle  duplicates?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.245159", "edited": {"user": "U0690EB5JE5", "ts": "1734708835.000000"}, "blocks": [{"type": "rich_text", "block_id": "LNsSx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "System expects unique business employeeId. Please clarify with customer on this. If this is true and then how to handle  duplicates?"}]}]}]}, {"ts": "1734716343.163459", "text": "I asked <PERSON>, she said <PERSON><PERSON><PERSON> is a deactivated employee and she doesn't know why he's showing up, so we can delete/ignore that one", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.245159", "blocks": [{"type": "rich_text", "block_id": "+tE+L", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I asked <PERSON>, she said <PERSON><PERSON><PERSON> is a deactivated employee and she doesn't know why he's showing up, so we can delete/ignore that one"}]}]}]}, {"ts": "1734716353.503309", "text": "<PERSON><PERSON> is correct", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.245159", "blocks": [{"type": "rich_text", "block_id": "CVTHt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON> is correct"}]}]}]}, {"ts": "1734916015.745999", "text": "Can we get this update complete by the time I get in tomorrow?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.245159", "blocks": [{"type": "rich_text", "block_id": "CiJTW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can we get this update complete by the time I get in tomorrow?"}]}]}]}, {"ts": "1734916029.538299", "text": "<@U0690EB5JE5> ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.245159", "blocks": [{"type": "rich_text", "block_id": "ld8xk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1734917697.657559", "text": "Yes will do ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.245159", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "YMWVK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes will do "}]}]}]}, {"ts": "1734958243.238199", "text": "I have synced the data. There seems to be issue with cycle. I am looking into it. We can create a new cylce to fix it worst case. Let me get back on that. Please spot check data meanwhile.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.245159", "edited": {"user": "U0690EB5JE5", "ts": "1734958668.000000"}, "blocks": [{"type": "rich_text", "block_id": "8PCBt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have synced the data. There seems to be issue with cycle. I am looking into it. We can create a new cylce to fix it worst case. Let me get back on that. Please spot check data meanwhile."}]}]}]}], "created_at": "2025-05-22T22:00:49.315398"}