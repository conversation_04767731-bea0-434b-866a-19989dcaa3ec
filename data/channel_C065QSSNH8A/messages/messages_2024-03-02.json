{"date": "2024-03-02", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1709398105.735089", "text": "<@U065H3M6WJV> when do you plan to be ready for regrouping to discuss the \"What\" of next 2 months?\n<@U04DKEFP1K8> <@U0690EB5JE5> Looking forward to hearing your thoughts on the action plan/next steps as discussed in \"tech regroup\" meeting last week. Are we still on for <PERSON><PERSON> for that?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1709398105.735089", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Cx3Ms", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " when do you plan to be ready for regrouping to discuss the \"What\" of next 2 months?\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Looking forward to hearing your thoughts on the action plan/next steps as discussed in \"tech regroup\" meeting last week. Are we still on for <PERSON><PERSON> for that?"}]}]}]}, {"ts": "1709341219.947659", "text": "After <PERSON> asked some questions about the charts on the Organization tab, I decided to do a deeper dive to figure out which charts seem to be using the live data and which ones don't match. In the Reports section, there's some obvious discrepancies in averages depending on the chart, so something doesn't fully align there.\n\nStarting to capture the details in <https://docs.google.com/spreadsheets/d/1XVqH5s7IeAhpUf7Nn9gwLIkLgFox4VgJRZeg0l7PVH8/edit#gid=1319426088|this spreadsheet>. (This should be less of an issue when we're ready with newer insights, but I think it'll help me understand which data sources seem to be working correctly vs. not.)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709341219.947659", "reply_count": 1, "reactions": [{"name": "rocket", "users": ["U04DS2MBWP4", "U0690EB5JE5"], "count": 2}], "files": [{"id": "F06MKS3LEQ2", "created": 1709341208, "timestamp": 1709341208, "name": "Screenshot 2024-03-01 at 4.56.22 PM.png", "title": "Screenshot 2024-03-01 at 4.56.22 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 453564, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06MKS3LEQ2/screenshot_2024-03-01_at_4.56.22___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06MKS3LEQ2/download/screenshot_2024-03-01_at_4.56.22___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 194, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 259, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 388, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 432, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 518, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MKS3LEQ2-12c722bf86/screenshot_2024-03-01_at_4.56.22___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 552, "original_w": 3700, "original_h": 1996, "thumb_tiny": "AwAZADDTpglUsRg4BxnHGadTfLXdu984zxmgB2KPypabtU9u+fxoAdmk70gRR0HfNKAB0oAKaC/OVX2wafRQA0b/ADDnG3HFBLDOADzTqKAG5b+739e1KCeMjH40UtAH/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06MKS3LEQ2/screenshot_2024-03-01_at_4.56.22___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06MKS3LEQ2-fea068c6d0", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06MGUE8QUD", "created": 1709341211, "timestamp": 1709341211, "name": "Screenshot 2024-03-01 at 4.56.35 PM.png", "title": "Screenshot 2024-03-01 at 4.56.35 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 390846, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06MGUE8QUD/screenshot_2024-03-01_at_4.56.35___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06MGUE8QUD/download/screenshot_2024-03-01_at_4.56.35___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 213, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 285, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 427, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 474, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 569, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MGUE8QUD-8ccd0d20d9/screenshot_2024-03-01_at_4.56.35___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 607, "original_w": 3674, "original_h": 2178, "thumb_tiny": "AwAcADDTqMSqWK4OAcZxxmn03yl3bueucZ4z60AOxRS00qDnI60AOopu0HPvS7R6d80AISFznPrQXAz149qWloAaGBOOeuKdRRQAUUUUAf/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06MGUE8QUD/screenshot_2024-03-01_at_4.56.35___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06MGUE8QUD-a3596144d5", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06MKNYASLT", "mode": "tombstone"}], "blocks": [{"type": "rich_text", "block_id": "znYOW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "After <PERSON> asked some questions about the charts on the Organization tab, I decided to do a deeper dive to figure out which charts seem to be using the live data and which ones don't match. In the Reports section, there's some obvious discrepancies in averages depending on the chart, so something doesn't fully align there.\n\nStarting to capture the details in "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1XVqH5s7IeAhpUf7Nn9gwLIkLgFox4VgJRZeg0l7PVH8/edit#gid=1319426088", "text": "this spreadsheet"}, {"type": "text", "text": ". (This should be less of an issue when we're ready with newer insights, but I think it'll help me understand which data sources seem to be working correctly vs. not.)"}]}]}]}, {"ts": "1709320430.161879", "text": "Sorry just saw, be right there", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "V+Fzw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sorry just saw, be right there"}]}]}]}, {"ts": "1709320182.013719", "text": "saurab<PERSON>.jain has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R06MLTKQRE0", "block_id": "OUXI1", "api_decoration_available": false, "call": {"v1": {"id": "R06MLTKQRE0", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1709320181, "active_participants": [], "all_participants": [{"slack_id": "U04DKEFP1K8"}, {"slack_id": "U065H3M6WJV"}], "display_id": "892-8873-6620", "join_url": "https://us06web.zoom.us/j/89288736620?pwd=ULCnoTQmfaqyemP1voq9Za3XfkQHRm.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEX2YyNTA0OTVmMmM4YjRlNjJiYzViZWU1MWNiMjdjMTcwJnVzcz1QdmxsdzVHcW0taEpya2tvLUstcFh2Z3FtaGpnRzFCSm1CM3ZNMHN6MExQLXlXSjI2Ukg1N1pIejFHWVMzcGZfQTJFbGxNSVlOTDJwUmxCM2p2Z3FvbEJzMm5EOWo1aGxkV0sxeFN3Llp5ZzB2ZWNKVk5pb1ZHNm8%3D&action=join&confno=89288736620&pwd=ULCnoTQmfaqyemP1voq9Za3XfkQHRm.1", "name": "Zoom meeting started by <PERSON><PERSON><PERSON><PERSON>", "created_by": "U05185RFCNT", "date_end": 1709321201, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "+lpGX", "text": {"type": "mrkdwn", "text": "Meeting passcode: ULCnoTQmfaqyemP1voq9Za3XfkQHRm.1", "verbatim": false}}]}, {"ts": "1709320173.650479", "text": "lets connect now in that case", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VRMae", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "lets connect now in that case"}]}]}]}, {"ts": "1709320127.703369", "text": "Ok. I have to leave around 11:30 but lmk if there’s anything to review before then", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "uujBH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. I have to leave around 11:30 but lmk if "}, {"type": "text", "text": "there’s"}, {"type": "text", "text": " anything to review before then"}]}]}]}, {"ts": "1709320015.731009", "text": "<@U065H3M6WJV> i am putting fixes to unblock DA now on qa and sdf-test now. Let's sync up when they are deployed", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "45B3Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " i am putting fixes to unblock DA now on qa and sdf-test now. Let's sync up when they are deployed"}]}]}]}], "created_at": "2025-05-22T21:35:34.613591"}