{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2023-12", "message_count": 179, "messages": [{"ts": "1701370310.848299", "text": "<@U0658EW4B8D> <https://docs.google.com/document/d/1ki-kXXrdjOX7XmzStYKS7Emz_NXii682/edit?usp=sharing&amp;ouid=107994932584597228039&amp;rtpof=true&amp;sd=true|Customer discovery doc>", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F068S275RL0", "created": 1701370313, "timestamp": 1701370313, "name": "Customer discovery document_v1.docx", "title": "Customer discovery document_v1.docx", "mimetype": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filetype": "docx", "pretty_type": "Word Document", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 110971, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1ki-kXXrdjOX7XmzStYKS7Emz_NXii682", "external_url": "https://docs.google.com/document/d/1ki-kXXrdjOX7XmzStYKS7Emz_NXii682/edit?usp=sharing&ouid=107994932584597228039&rtpof=true&sd=true", "url_private": "https://docs.google.com/document/d/1ki-kXXrdjOX7XmzStYKS7Emz_NXii682/edit?usp=sharing&ouid=107994932584597228039&rtpof=true&sd=true", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_360.png", "thumb_360_w": 255, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_480.png", "thumb_480_w": 340, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_800.png", "thumb_800_w": 800, "thumb_800_h": 1131, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_960.png", "thumb_960_w": 679, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F068S275RL0-658ddc8d51/customer_discovery_document_v1_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1131, "thumb_tiny": "AwAwACHSJwM03zF9acTikI3c7iKAE8xfWlBJ5HSgAD+In6mnZoAKKKKAGt24pOPSiQuMbEDfU4pm6X/nkv8A33QBJlfSlBGeKi3yf88l/wC+/wD61PRnJ+ZAo9mzQA+iiigAooOe1Jz7UALRSc+1Lz3FABRRRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F068S275RL0/customer_discovery_document_v1.docx", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "otIOm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://docs.google.com/document/d/1ki-kXXrdjOX7XmzStYKS7Emz_NXii682/edit?usp=sharing&ouid=107994932584597228039&rtpof=true&sd=true", "text": "Customer discovery doc"}]}]}]}, {"ts": "1701374715.147419", "text": "<@U04DKEFP1K8> One thing that would help me is a current list of what's actively being worked on, or next up on your list, for the Eng team. Can you share just a high-level bullet list of which items are already in progress?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701374715.147419", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "NbZPC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " One thing that would help me is a current list of what's actively being worked on, or next up on your list, for the Eng team. Can you share just a high-level bullet list of which items are already in progress?"}]}]}]}, {"ts": "1701386801.575719", "text": "I'd like to send the <https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0|project plan> over to DA by tomorrow.\n• Any more edits needed, or is this ready to go?\n• We don't have any further meetings on the calendar with them (yet), correct?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701386801.575719", "reply_count": 2, "edited": {"user": "U065H3M6WJV", "ts": "1701386810.000000"}, "blocks": [{"type": "rich_text", "block_id": "ziqIv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'd like to send the "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "text": "project plan"}, {"type": "text", "text": " over to DA by tomorrow.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Any more edits needed, or is this ready to go?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "We don't have any further meetings on the calendar with them (yet), correct?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1701394884.332609", "text": "For the call with <PERSON><PERSON>, since we already have their earlier responses to the discovery document, I think it'd be best to focus on questions specific to their upcoming comp cycle first. I've <https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit|drafted some questions here> with the goal of walking through it live and having them explain any nuances. Please feel free to suggest additions for any key components of a major comp cycle.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701394884.332609", "reply_count": 2, "files": [{"id": "F0687EKLWDA", "created": 1701394886, "timestamp": 1701394886, "name": "Cycle Config - Q1 2024 - Stellar Development Foundation", "title": "Cycle Config - Q1 2024 - Stellar Development Foundation", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw", "external_url": "https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit", "url_private": "https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSY49PxNJuP+z+dK34flSD2I/KgBct6D86Ue9Jz6j8qUZ70AFFFFACN05pAR70rDIpnHegCTP1oz9fypnFLketADqKAciigAJxUdPYZFMoAUYz/wDWpf8AgQpuKUcdv1oAeOnXNFA6UUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0687EKLWDA/cycle_config_-_q1_2024_-_stellar_development_foundation", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "egN4b", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For the call with <PERSON><PERSON>, since we already have their earlier responses to the discovery document, I think it'd be best to focus on questions specific to their upcoming comp cycle first. I've "}, {"type": "link", "url": "https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit", "text": "drafted some questions here"}, {"type": "text", "text": " with the goal of walking through it live and having them explain any nuances. Please feel free to suggest additions for any key components of a major comp cycle."}]}]}]}, {"ts": "1701454047.145049", "text": "Walking through <https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit|this questionnaire> -- feel free to co-edit", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F0687EKLWDA", "created": 1701394886, "timestamp": 1701394886, "name": "Cycle Config - Q1 2024 - Stellar Development Foundation", "title": "Cycle Config - Q1 2024 - Stellar Development Foundation", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw", "external_url": "https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit", "url_private": "https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0687EKLWDA-8fd2b7de30/cycle_config_-_q1_2024_-_stellar_development_foundation_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSY49PxNJuP+z+dK34flSD2I/KgBct6D86Ue9Jz6j8qUZ70AFFFFACN05pAR70rDIpnHegCTP1oz9fypnFLketADqKAciigAJxUdPYZFMoAUYz/wDWpf8AgQpuKUcdv1oAeOnXNFA6UUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0687EKLWDA/cycle_config_-_q1_2024_-_stellar_development_foundation", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "oOql/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Walking through "}, {"type": "link", "url": "https://docs.google.com/document/d/1fzRxdEkf9ZgLEZyuMwpOSOCpXDn70O2Z61DutY7b_jw/edit", "text": "this questionnaire"}, {"type": "text", "text": " -- feel free to co-edit"}]}]}]}, {"ts": "1701457779.207529", "text": "Team- It was a good call. Most of their requirements are fairly simples and it looks like we can address them with the exception of pay gap analytics within comp band and total rewards portal", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "2PC+O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Team- It was a good call. Most of their requirements are fairly simples and it looks like we can address them with the exception of pay gap analytics within comp band and total rewards portal"}]}]}]}, {"ts": "1701457822.823259", "text": "<@U065H3M6WJV> we should figure out the release plan for new pay bands and total rewards portal", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YGOdU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " we should figure out the release plan for new pay bands and total rewards portal"}]}]}]}, {"ts": "1701457836.523439", "text": "<!here> most requirements from the call with stellar seems attainable except we will not get performance ratings with the initial upload. Need to discuss with you all", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1701457836.523439", "reply_count": 3, "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4", "U065H3M6WJV"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "wbXhe", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " most requirements from the call with stellar seems attainable except we will not get performance ratings with the initial upload. Need to discuss with you all"}]}]}]}, {"ts": "1701467261.429009", "text": "Random: just added a <#C0683U8CHSA|> channel where I expect y’all to drop some photos of <PERSON>, <PERSON><PERSON>, and any other fuzzy friends. I went ahead and kicked it off with one of mine. :wink: ", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "it/GD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Random: just added a "}, {"type": "channel", "channel_id": "C0683U8CHSA"}, {"type": "text", "text": " channel where I expect y’all to drop some photos of <PERSON>, <PERSON><PERSON>, and any other fuzzy friends. I went ahead and kicked it off with one of mine. "}, {"type": "emoji", "name": "wink", "unicode": "1f609"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1701476381.011609", "text": "Here is Stellar timeline\n<https://docs.google.com/spreadsheets/d/1OzDi5j444dTOGX1XgwfQqGxL4p0io2tpd2kfHXFpU6M/edit#gid=0>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "meow_thx", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F068AUMHDDZ", "created": 1701476383, "timestamp": 1701476383, "name": "SDF Comp Timeline - Compiify", "title": "SDF Comp Timeline - Compiify", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 125149, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1OzDi5j444dTOGX1XgwfQqGxL4p0io2tpd2kfHXFpU6M", "external_url": "https://docs.google.com/spreadsheets/d/1OzDi5j444dTOGX1XgwfQqGxL4p0io2tpd2kfHXFpU6M/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1OzDi5j444dTOGX1XgwfQqGxL4p0io2tpd2kfHXFpU6M/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F068AUMHDDZ-564e9af9df/sdf_comp_timeline_-_compiify_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHSJOeKTLDPTikZgGwSBSnoaVgEDMT04p9RA5I/xqUdKLAFFFFMBjsobB60rdD9KZIshcFTx9acQcGgBi1MOlRqPbH4VIOlABRRRQAUUUUAFFFFABRRRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F068AUMHDDZ/sdf_comp_timeline_-_compiify", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "4xSdH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is Stellar timeline\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1OzDi5j444dTOGX1XgwfQqGxL4p0io2tpd2kfHXFpU6M/edit#gid=0"}]}]}]}, {"ts": "1701476438.817279", "text": "Do you all feel the need to create a separate slack channel for each customer or are we good here?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701476438.817279", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "g6agJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you all feel the need to create a separate slack channel for each customer or are we good here?"}]}]}]}, {"ts": "1701477467.228979", "text": "Can start here and breakout if needed", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eB9U/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can start here and breakout if needed"}]}]}]}, {"ts": "1701482835.042479", "text": "Looking at <PERSON><PERSON>'s timeline and their end of cycle is going to be TIGHT", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GeoRP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Looking at <PERSON><PERSON>'s timeline and their end of cycle is going to be TIGHT"}]}]}]}, {"ts": "1701484244.859709", "text": "<https://docs.google.com/spreadsheets/d/1QxlCmvkmAZGWnTaepnC8xPTVvggze9Jm4hBG7l_lJCc/edit#gid=0|Rough Stellar Project Plan here>", "user": "U0658EW4B8D", "type": "message", "files": [{"id": "F067WN3PDLP", "created": 1701484248, "timestamp": 1701484248, "name": "Stellar Implementation Project Plan", "title": "Stellar Implementation Project Plan", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 103185, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1QxlCmvkmAZGWnTaepnC8xPTVvggze9Jm4hBG7l_lJCc", "external_url": "https://docs.google.com/spreadsheets/d/1QxlCmvkmAZGWnTaepnC8xPTVvggze9Jm4hBG7l_lJCc/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1QxlCmvkmAZGWnTaepnC8xPTVvggze9Jm4hBG7l_lJCc/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F067WN3PDLP-f040147fae/stellar_implementation_project_plan_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHRbdnjH40gL+q/lTj1pAKVwEy+cZX8qUbs8kY+lHelouAtFFFMAooooAMc0UUUAFFFFABRRRQAUUUUAFFFFAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F067WN3PDLP/stellar_implementation_project_plan", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "h0Txj", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/spreadsheets/d/1QxlCmvkmAZGWnTaepnC8xPTVvggze9Jm4hBG7l_lJCc/edit#gid=0", "text": "Rough Stellar Project Plan here"}]}]}]}, {"ts": "1701489890.551529", "text": "Hey <@U065H3M6WJV> please share angel<PERSON>’s feedback for OTE front end in the merit view", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1701489890.551529", "reply_count": 6, "edited": {"user": "U04DKEFP1K8", "ts": "1701489913.000000"}, "blocks": [{"type": "rich_text", "block_id": "EGTZZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " please share <PERSON><PERSON><PERSON>s feedback for OTE front end in the merit view"}]}]}]}, {"ts": "1701490635.995679", "text": "<@U065H3M6WJV> let us know your thoughts if we should ask <PERSON> if there is a scope to start the planning cycle for managers after perf ratings are published ( asking since perf rating comes out around 2 feb and current request to open application is jan 22nd ) . I was also thinking that managers would need to view rating before they make any decisions. ( i will anyways plan development activities to accommodate scenario presented to us anyways)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1701490635.995679", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "qj0RV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " let us know your thoughts if we should ask <PERSON> if there is a scope to start the planning cycle for managers after perf ratings are published ( asking since perf rating comes out around 2 feb and current request to open application is jan 22nd ) . I was also thinking that managers would need to view rating before they make any decisions. ( i will anyways plan development activities to accommodate scenario presented to us anyways)"}]}]}]}, {"ts": "1701697328.432629", "text": "<@U065H3M6WJV> <@U04DS2MBWP4> I propose for this week meetings (starting with one we have with <PERSON> later in the day) with Advisors we focus on cycle closure related activities.", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}, {"name": "+1", "users": ["U065H3M6WJV", "U04DS2MBWP4"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "CSXr3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I propose for this week meetings (starting with one we have with <PERSON> later in the day) with Advisors we focus on cycle closure related activities."}]}]}]}, {"ts": "1701710455.303479", "text": "<@U04DKEFP1K8> what's the ETA for fixing total rewards equity chart? I prefer to set up call with Stellar only after that chart is fixed", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701710455.303479", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "fWRZT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what's the ETA for fixing total rewards equity chart? I prefer to set up call with Stellar only after that chart is fixed"}]}]}]}, {"ts": "1701713965.198129", "text": "<@U04DS2MBWP4> Did you happen to connect with <PERSON> last week about the topic of advisory sessions? I rescheduled using his calendar link and didn't get any note either way -- currently waiting on the Teams link. :confused:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NTdFq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Did you happen to connect with <PERSON> last week about the topic of advisory sessions? I rescheduled using his calendar link and didn't get any note either way -- currently waiting on the Teams link. "}, {"type": "emoji", "name": "confused", "unicode": "1f615"}]}]}]}, {"ts": "1701715087.643869", "text": "Do you mind sending me his scheduling link?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701715087.643869", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "DMKUf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you mind sending me his scheduling link?"}]}]}]}, {"ts": "1701717940.019399", "text": "We've just received the customer data from *Digital Asset* and I've moved the files to the <https://drive.google.com/drive/folders/1VqpbnBFyZo-Dp4xbQBsNq9FmaG_1gYOR|Google Drive folder>.\n\nI'll look for a time for our next sync up, ideally when <PERSON><PERSON><PERSON><PERSON> and <PERSON> are also available to join.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701717940.019399", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "sYsDf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We've just received the customer data from "}, {"type": "text", "text": "Digital Asset", "style": {"bold": true}}, {"type": "text", "text": " and I've moved the files to the "}, {"type": "link", "url": "https://drive.google.com/drive/folders/1VqpbnBFyZo-Dp4xbQBsNq9FmaG_1gYOR", "text": "Google Drive folder"}, {"type": "text", "text": ".\n\nI'll look for a time for our next sync up, ideally when <PERSON><PERSON><PERSON><PERSON> and <PERSON> are also available to join."}]}]}]}, {"ts": "1701724535.282289", "text": "Crap! I just realized the Thursday call with Digital Asset is a day when <@U04DKEFP1K8> is out. :picard-facepalm:\n\n_Edit: Moved to Friday, since I think <PERSON><PERSON><PERSON><PERSON> will be able to join then._ :+1: ", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701724535.282289", "reply_count": 9, "edited": {"user": "U065H3M6WJV", "ts": "1701742142.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "euzBr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Crap! I just realized the Thursday call with Digital Asset is a day when "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " is out. "}, {"type": "emoji", "name": "picard-facepalm"}, {"type": "text", "text": "\n\n"}, {"type": "text", "text": "Edit: Moved to Friday, since I think <PERSON><PERSON><PERSON><PERSON> will be able to join then. ", "style": {"italic": true}}, {"type": "emoji", "name": "+1", "unicode": "1f44d", "style": {"italic": true}}, {"type": "text", "text": " ", "style": {"italic": true}}]}]}]}, {"ts": "1701742170.438499", "text": "Do you need me to join the Merge call tomorrow <@U04DS2MBWP4>? I noticed it overlaps our design sync.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "drfWt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you need me to join the Merge call tomorrow "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": "? I noticed it overlaps our design sync."}]}]}]}, {"ts": "1701743441.872889", "text": "I think it's fine since <PERSON><PERSON><PERSON><PERSON> is joining it.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Pb3xN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think it's fine since <PERSON><PERSON><PERSON><PERSON> is joining it."}]}]}]}, {"ts": "1701793803.372499", "text": "<!here> I have set the total rewards implemenation planning for SDF on 12/11 at 10 am pst. <@U0658EW4B8D> would you be able to attend it?", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "4TSuj", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I have set the total rewards implemenation planning for SDF on 12/11 at 10 am pst. "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " would you be able to attend it?"}]}]}]}, {"ts": "1701794738.292899", "text": "<@U04DKEFP1K8> Is <PERSON><PERSON><PERSON> gonna be able to join the Merge call for sake of visibility?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5XNtv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Is <PERSON><PERSON><PERSON> gonna be able to join the Merge call for sake of visibility?"}]}]}]}, {"ts": "1701823929.293409", "text": "<@U065H3M6WJV> slides 13, 14, 15 and 18 highlights the beta program and implementation process. We will refine them as we gain more clarity on how we want to run the beta program and implementation\n<https://docs.google.com/presentation/d/1kCCTPk19VwJOVvq1I972HJwvXGY1LTz4/edit#slide=id.p15>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F0696HJ3URF", "created": 1701823932, "timestamp": 1701823932, "name": "Discovery Questions.pptx", "title": "Discovery Questions.pptx", "mimetype": "application/vnd.openxmlformats-officedocument.presentationml.presentation", "filetype": "pptx", "pretty_type": "PowerPoint Presentation", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 19625, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1kCCTPk19VwJOVvq1I972HJwvXGY1LTz4", "external_url": "https://docs.google.com/presentation/d/1kCCTPk19VwJOVvq1I972HJwvXGY1LTz4/edit#slide=id.p15", "url_private": "https://docs.google.com/presentation/d/1kCCTPk19VwJOVvq1I972HJwvXGY1LTz4/edit#slide=id.p15", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_360.png", "thumb_360_w": 360, "thumb_360_h": 203, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_480.png", "thumb_480_w": 480, "thumb_480_h": 270, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_720.png", "thumb_720_w": 720, "thumb_720_h": 405, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0696HJ3URF-31cc4c8d68/discovery_questions_800.png", "thumb_800_w": 800, "thumb_800_h": 450, "original_w": 800, "original_h": 450, "thumb_tiny": "AwAbADCnRSqAfvHH4Zp21P8Anp/47XWWIFyM5UfU0FcdwfoaXamfvn/vmjbHj/Wf+O0gGUU/an/PQ/8AfNNYAdDn8MUwEpcE9qSigBcH0pdrehptFAClSOopKKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0696HJ3URF/discovery_questions.pptx", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "agM7d", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " slides 13, 14, 15 and 18 highlights the beta program and implementation process. We will refine them as we gain more clarity on how we want to run the beta program and implementation\n"}, {"type": "link", "url": "https://docs.google.com/presentation/d/1kCCTPk19VwJOVvq1I972HJwvXGY1LTz4/edit#slide=id.p15"}]}]}]}, {"ts": "1701885733.024919", "text": "<@U0658EW4B8D> <PERSON> is running a little late for the meeting. She'll ping when when is ready", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hJGL3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " <PERSON> is running a little late for the meeting. She'll ping when when is ready"}]}]}]}, {"ts": "1701886062.108489", "text": "Online now <@U0658EW4B8D> <https://us06web.zoom.us/j/85136045601?pwd=fxFuBKVUaRaAxT4PcI1sUWUf1aHo4F.1>", "user": "U065H3M6WJV", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R068VQZ7N77", "block_id": "2VeAG", "api_decoration_available": false, "call": {"v1": {"id": "R068VQZ7N77", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1701886062, "active_participants": [], "all_participants": [], "display_id": "851-3604-5601", "join_url": "https://us06web.zoom.us/j/85136045601?pwd=fxFuBKVUaRaAxT4PcI1sUWUf1aHo4F.1", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1701976426, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/85136045601?pwd=fxFuBKVUaRaAxT4PcI1sUWUf1aHo4F.1", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "uQ0bf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Online now "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://us06web.zoom.us/j/85136045601?pwd=fxFuBKVUaRaAxT4PcI1sUWUf1aHo4F.1"}]}]}]}, {"ts": "1701905084.540399", "text": "I started a <https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=0|draft of UAT test cases> in a new spreadsheet because I'd like us to collaborate on these before sharing them with Digital Asset.\n\nI also created a tab to capture anything I noticed in the current Sandbox environment that could be a concern when the Digital Asset team tries to run their cycle with the existing UI.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701905084.540399", "reply_count": 6, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F068XFR96CD", "created": 1701905087, "timestamp": 1701905087, "name": "UAT test cases (draft)", "title": "UAT test cases (draft)", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU", "external_url": "https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSYkGlprdRTqACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F068XFR96CD/uat_test_cases__draft_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "HfNUT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I started a "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=0", "text": "draft of UAT test cases"}, {"type": "text", "text": " in a new spreadsheet because I'd like us to collaborate on these before sharing them with Digital Asset.\n\nI also created a tab to capture anything I noticed in the current Sandbox environment that could be a concern when the Digital Asset team tries to run their cycle with the existing UI."}]}]}]}, {"ts": "1701971088.617249", "text": "<@U04DKEFP1K8> we need a new domain to send sales emails because we don't want <http://compiify.com|compiify.com> emails to be listed as spam. Can we create domains such as <http://compiifyinc.com|compiifyinc.com>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701971088.617249", "reply_count": 5, "reactions": [{"name": "bulb", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "AgBMo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " we need a new domain to send sales emails because we don't want "}, {"type": "link", "url": "http://compiify.com", "text": "compiify.com"}, {"type": "text", "text": " emails to be listed as spam. Can we create domains such as "}, {"type": "link", "url": "http://compiifyinc.com", "text": "compiifyinc.com"}]}]}]}, {"ts": "1701976147.373879", "text": "<@U04DKEFP1K8> what's the status of fixing equity chart?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701976147.373879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "QEZqJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what's the status of fixing equity chart?"}]}]}]}, {"ts": "1701982827.181099", "text": "Agenda for tomorrow's call with *Digital Asset:*\n• Review the <https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0|Implementation Project Plan> and confirm key dates\n• Provide high-level info on planned UAT (don't need to review in detail) and get their commitment to do some testing once data is loaded\n• Walk through <https://docs.google.com/document/d/1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs/edit|cycle config questions> (verbally - not on screen) to capture any additional requirements for this cycle. I've prefilled this with what we know from their first call. \n    ◦ Specifically ask about what is lacking in HiBob and what they'll need in reports/analytics (noted at end of doc)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701982827.181099", "reply_count": 6, "reactions": [{"name": "moneybag", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F067S2J5BF0", "created": 1701322474, "timestamp": 1701322474, "name": "Digital Assets Implementation Project Plan", "title": "Digital Asset Implementation Project Plan", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE", "external_url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSbdnjH403L+q/lTj1popXAMv6r+VKN2eSMfSkHWnUXAWiiimAUUUUAGOaKKKACiiigAooooAKKKKACiiigD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F067S2J5BF0/digital_assets_implementation_project_plan", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}, {"id": "F069V9QT1DE", "created": 1701982829, "timestamp": 1701982829, "name": "DA - Cycle Config Questions", "title": "DA - Cycle Config Questions", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 126118, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs", "external_url": "https://docs.google.com/document/d/1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs/edit", "url_private": "https://docs.google.com/document/d/1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTSP1pB9TSscDpSAj0/WgBcH+8aUD3zRRQAUUUUAIwyKQKe+KHAPOKQLnsaAH4xRTQoHal2j0FAC0UUUAI31IpAD6n86G+poXk9TQAbT6n86cOlJj3NLQAUUUUAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F069V9QT1DE/da_-_cycle_config_questions", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "RZ6A0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for tomorrow's call with "}, {"type": "text", "text": "Digital Asset:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Review the "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "text": "Implementation Project Plan"}, {"type": "text", "text": " and confirm key dates"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Provide high-level info on planned UAT (don't need to review in detail) and get their commitment to do some testing once data is loaded"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Walk through "}, {"type": "link", "url": "https://docs.google.com/document/d/1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs/edit", "text": "cycle config questions"}, {"type": "text", "text": " (verbally - not on screen) to capture any additional requirements for this cycle. I've prefilled this with what we know from their first call. "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Specifically ask about what is lacking in HiBob and what they'll need in reports/analytics (noted at end of doc)"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}, {"ts": "1701992769.544389", "text": "<@U065H3M6WJV> <@U0658EW4B8D> We are going to use a quick survey in our sales outreach emails targeted towards HR leaders. Goal is to get some data on how they manage their comp cycles and if they do it either on spreadsheet or use a software, is it streamlined and effective.\n\nHere is a first take at the survey.\n\n*Which describes your situation?*\n• Respond \"1\" if your company plans merit cycles on spreadsheets.\n• Respond \"2\" if your company plans merit cycles on spreadsheets and you feel that your process is efficient, transparent and data-driven.\n• Respond \"3\" if your company plans merit cycle on spreadsheets and the processes is stressful or confusing for HR team and/or employees.\n\nAny thoughts on how to make it better or what questions we should actually be asking to ensure we are solving the right problem.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QHMXL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " We are going to use a quick survey in our sales outreach emails targeted towards HR leaders. Goal is to get some data on how they manage their comp cycles and if they do it either on spreadsheet or use a software, is it streamlined and effective.\n\nHere is a first take at the survey.\n\n"}, {"type": "text", "text": "Which describes your situation?", "style": {"bold": true}}, {"type": "text", "text": "\n• Respond \"1\" if your company plans merit cycles on spreadsheets.\n• Respond \"2\" if your company plans merit cycles on spreadsheets and you feel that your process is efficient, transparent and data-driven.\n• Respond \"3\" if your company plans merit cycle on spreadsheets and the processes is stressful or confusing for HR team and/or employees.\n\nAny thoughts on how to make it better or what questions we should actually be asking to ensure we are solving the right problem."}]}]}]}, {"ts": "1701992903.845849", "text": "I would maybe say:\n• Respond \"1\" if you're fully satisfied with your tools for planning merit cycles\n• Respond \"2\" if you have a system for planning merit cycles, but it's not ideal\n• Respond \"3\" if you're still using spreadsheets for merit planning", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "idsQV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I would maybe say:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Respond \"1\" if you're fully satisfied with your tools for planning merit cycles"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Respond \"2\" if you have a system for planning merit cycles, but it's not ideal"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Respond \"3\" if you're still using spreadsheets for merit planning"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1701993424.140749", "text": "good suggestion, I like how short and concise it is. Could we explore adding some specifics and/or more color on what it means to be satisfied or what's ideal? I also want to explicitly mention employee to make it look like it's a company-wide issue.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FNXRx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "good suggestion, I like how short and concise it is. Could we explore adding some specifics and/or more color on what it means to be satisfied or what's ideal? I also want to explicitly mention employee to make it look like it's a company-wide issue."}]}]}]}, {"ts": "1701993587.800259", "text": "• Respond \"1\" if you're fully satisfied with your tools for planning merit cycles\n• Respond \"2\" if merit planning is stressful or confusing for your managers and employees\n• Respond \"3\" if you're still doing your merit planning via spreadsheets", "user": "U065H3M6WJV", "type": "message", "edited": {"user": "U065H3M6WJV", "ts": "1701993601.000000"}, "blocks": [{"type": "rich_text", "block_id": "5cjkm", "elements": [{"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Respond \"1\" if you're fully satisfied with your tools for planning merit cycles"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Respond \"2\" if merit planning is stressful or confusing for your managers and employees"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Respond \"3\" if you're still doing your merit planning via spreadsheets"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1701993640.133009", "text": "sweet", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZADOs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "sweet"}]}]}]}, {"ts": "1702056810.489539", "text": "Main links / files for today's call with *Digital Asset*:\n• <https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0|Project plan> (shared with customer)\n• <https://docs.google.com/document/d/1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs/edit|Cycle config questionnaire> (not shared with customer)", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F067S2J5BF0", "created": 1701322474, "timestamp": 1701322474, "name": "Digital Assets Implementation Project Plan", "title": "Digital Asset Implementation Project Plan", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE", "external_url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSbdnjH403L+q/lTj1popXAMv6r+VKN2eSMfSkHWnUXAWiiimAUUUUAGOaKKKACiiigAooooAKKKKACiiigD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F067S2J5BF0/digital_assets_implementation_project_plan", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ky1jI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Main links / files for today's call with "}, {"type": "text", "text": "Digital Asset", "style": {"bold": true}}, {"type": "text", "text": ":\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "text": "Project plan"}, {"type": "text", "text": " (shared with customer)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/document/d/1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs/edit", "text": "Cycle config questionnaire"}, {"type": "text", "text": " (not shared with customer)"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1702057258.639349", "text": "<@U0658EW4B8D> We're going to drop the current session - see you on the call with DA at 10!", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xew3q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " We're going to drop the current session - see you on the call with DA at 10!"}]}]}]}, {"ts": "1702057600.763319", "text": "Sorry y'all. Office is packed. just found a room so I'm ready for the call with DA", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "aYoQv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sorry y'all. Office is packed. just found a room so I'm ready for the call with DA"}]}]}]}, {"ts": "1702062328.208599", "text": "This meeting was actually great. I think we're going to be able to slam this one out of the park. Sounds like we'll get some ranges and be able to really show really strong value to DA", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "excited", "users": ["U065H3M6WJV"], "count": 1}, {"name": "tada", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "iaZLh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This meeting was actually great. I think we're going to be able to slam this one out of the park. Sounds like we'll get some ranges and be able to really show really strong value to DA"}]}]}]}, {"ts": "1702063339.271179", "text": "Main action items from today's call &amp; next steps for us:\n• DA - provide templates for adjustment letters\n• DA - provide their existing salary range info for US employees\n• <@U0658EW4B8D> - Update project plan dates to reflect their cycle timeline\n• <@U04DKEFP1K8> - Load data to Compiify so we can start UAT", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702063339.271179", "reply_count": 4, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4", "U0658EW4B8D"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "BbjG5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Main action items from today's call & next steps for us:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA - provide templates for adjustment letters"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "DA - provide their existing salary range info for US employees"}]}, {"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " - Update project plan dates to reflect their cycle timeline"}]}, {"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " - Load data to Compiify so we can start UAT"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1702085923.231049", "text": "<@U04DKEFP1K8> SDF is asking for Compiify SLAs. I am assuming we need to establish them. Can you do this by <PERSON><PERSON>?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702085923.231049", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "G526y", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " SDF is asking for Compiify SLAs. I am assuming we need to establish them. Can you do this by <PERSON><PERSON>?"}]}]}]}, {"ts": "1702112280.952049", "text": "<!here> i am back from vacation and i see multiple threads i need to respond to. I will start responding to them now. ", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "gratitude-thank-you", "users": ["U065H3M6WJV", "U0658EW4B8D"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "fEObI", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " i am back from vacation and i see multiple threads i need to respond to. I will start responding to them now. "}]}]}]}, {"ts": "1702139512.001619", "text": "<@U04DKEFP1K8> what's the status of demo sandbox?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702139512.001619", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "XYQWY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what's the status of demo sandbox?"}]}]}]}, {"ts": "1702142129.704259", "text": "<@U04DKEFP1K8> how do I get access to AWS to do this. See screenshot", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702142129.704259", "reply_count": 4, "files": [{"id": "F0697RDRQ2J", "created": 1702142126, "timestamp": 1702142126, "name": "Screenshot 2023-12-09 at 9.13.50 AM.png", "title": "Screenshot 2023-12-09 at 9.13.50 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 379003, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F0697RDRQ2J/screenshot_2023-12-09_at_9.13.50___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F0697RDRQ2J/download/screenshot_2023-12-09_at_9.13.50___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_360.png", "thumb_360_w": 343, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_480.png", "thumb_480_w": 458, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_720.png", "thumb_720_w": 686, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_800.png", "thumb_800_w": 800, "thumb_800_h": 839, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_960.png", "thumb_960_w": 915, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_1024.png", "thumb_1024_w": 976, "thumb_1024_h": 1024, "original_w": 1792, "original_h": 1880, "thumb_tiny": "AwAwAC3RKg84yR05pvOfu/rT+1MDDuRQAoLDov60bn/u0deh/Q0uT6/+O0ALk+lFJk+v/jtKaAFFFAooARsjoQKBnvihulA5FAC0hpaQ0AA6Um8eho5o5oACwPY0owO9HPrRg+tAC5zSGjB9aTB9aAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0697RDRQ2J/screenshot_2023-12-09_at_9.13.50___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F0697RDRQ2J-4f78e10f40", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "h6ogx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " how do I get access to AWS to do this. See screenshot"}]}]}]}, {"ts": "1702218946.742719", "text": "Two new demo env has been setup\n\n<http://qa.compiify.com|qa.compiify.com> - primary ( 86 US only employee dataset - previously provided by stellar)\n<http://dev-app.compiify.com|dev-app.compiify.com> ( includes google auth with google) - secondary ( 334 5 countries employees dataset - internally prepared)\n\nI will share a document tomorrow with following details\n\n1. Auth details\n2. Currently available features\n3. Critical Issues\n4. Any known workaround for existing issues\n", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1702218946.742719", "reply_count": 2, "reactions": [{"name": "gratitude-thank-you", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "c07IT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Two new demo env has been setup\n\n"}, {"type": "link", "url": "http://qa.compiify.com", "text": "qa.compiify.com"}, {"type": "text", "text": " - primary ( 86 US only employee dataset - previously provided by stellar)\n"}, {"type": "link", "url": "http://dev-app.compiify.com", "text": "dev-app.compiify.com"}, {"type": "text", "text": " ( includes google auth with google) - secondary ( 334 5 countries employees dataset - internally prepared)\n\nI will share a document tomorrow with following details\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Auth details"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Currently available features"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Critical Issues"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Any known workaround for existing issues"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1702222442.717299", "text": "Engineering updates for the week of [Dec 4th - Dec 8th] have been posted here <https://docs.google.com/document/d/1d0AQGlECivhfgCCGEFJEjzTr0AeDR6fPrYAMBo9C1gE/edit>", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Gc3E3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Engineering updates for the week of [Dec 4th - Dec 8th] have been posted here "}, {"type": "link", "url": "https://docs.google.com/document/d/1d0AQGlECivhfgCCGEFJEjzTr0AeDR6fPrYAMBo9C1gE/edit"}]}]}]}, {"ts": "1702225192.861529", "text": "<@U04DS2MBWP4> Old staging environment (<https://staging-app.compiify.com/>) will be brought down Monday morning IST.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1702225192.861529", "reply_count": 3, "edited": {"user": "U04DKEFP1K8", "ts": "1702225218.000000"}, "attachments": [{"from_url": "https://staging-app.compiify.com/", "service_icon": "https://staging-app.compiify.com/apple-touch-icon.png", "id": 1, "original_url": "https://staging-app.compiify.com/", "fallback": "Compiify", "text": "Web site created using create-react-app", "title": "Compiify", "title_link": "https://staging-app.compiify.com/", "service_name": "staging-app.compiify.com"}], "blocks": [{"type": "rich_text", "block_id": "Ll2zy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Old staging environment ("}, {"type": "link", "url": "https://staging-app.compiify.com/"}, {"type": "text", "text": ") will be brought down Monday morning IST."}]}]}]}, {"ts": "1702225518.793529", "text": "<@U065H3M6WJV> I had provided data templates to <PERSON><PERSON> (SDF) last Tuesday. We have not heard back from her. Do you want to send a gentle reminder to her?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1702225518.793529", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "oRLEM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I had provided data templates to <PERSON><PERSON> (SDF) last Tuesday. We have not heard back from her. Do you want to send a gentle reminder to her?"}]}]}]}, {"ts": "1702297425.081069", "text": "<!here> Here is an update on implementing Digital Asset workflow within Compiify\nThere are 2 tasks that have been created\n1. Managing a cycle without pay bands information ( 4 issue already reported during today's testing <https://compiify.atlassian.net/browse/COM-1859>)\n2. Managing a cycle with partial pay bands information  ( Issue for this use case will be tracked here <https://compiify.atlassian.net/browse/COM-1866>)", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:11920::21622680982011ee96f59110f874eb6e", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-1859?atlOrigin=eyJpIjoiMDdlNjYxOTk2ZTlmNDFhMTgyZWZlOTk2ZDM3MTFmZWEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-1859 Manage a cycle without uploading salary ranges / pay bands data for…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:11920::21622684982011ee96f59110f874eb6e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:11920::21622681982011ee96f59110f874eb6e", "elements": [{"type": "button", "action_id": "{\"issueId\":\"11920\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"11920\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-1859", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:11927::21622682982011ee96f59110f874eb6e", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-1866?atlOrigin=eyJpIjoiNDczMmY2NjMzZmNlNDRmMzlkNTg1NjVlOTJmNTJhMTAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-1866 Manage a cycle with uploading partial salary ranges / pay bands dat…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:11927::21622685982011ee96f59110f874eb6e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:11927::21622683982011ee96f59110f874eb6e", "elements": [{"type": "button", "action_id": "{\"issueId\":\"11927\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"11927\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-1866", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "2qCCe", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Here is an update on implementing Digital Asset workflow within Compiify\nThere are 2 tasks that have been created\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Managing a cycle without pay bands information ( 4 issue already reported during today's testing "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1859"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Managing a cycle with partial pay bands information  ( Issue for this use case will be tracked here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1866"}, {"type": "text", "text": ")"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1702307869.362879", "text": "Do we have a timeline and plan of action for DA? When are we handing them over the pre-production environment?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702307869.362879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "c9dyA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we have a timeline and plan of action for DA? When are we handing them over the pre-production environment?"}]}]}]}, {"ts": "1702317642.870999", "text": "I saw <PERSON><PERSON>'s note about canceling / rescheduling -- I'm planning to reuse the time unless we still need to get together on that topic (just Compiify folks) ? <@U04DS2MBWP4>", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jg32f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I saw <PERSON><PERSON>'s note about canceling / rescheduling -- I'm planning to reuse the time unless we still need to get together on that topic (just Compiify folks) ? "}, {"type": "user", "user_id": "U04DS2MBWP4"}]}]}]}, {"ts": "1702317696.513439", "text": "yep responding to her. I think we all could use some time bacl", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nDwZX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yep responding to her. I think we all could use some time bacl"}]}]}]}, {"ts": "1702323100.763839", "text": "<@U04DS2MBWP4> Let us know when your current meeting ends so we can join the scheduled one at 1130am PST", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DoRfJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Let us know when your current meeting ends so we can join the scheduled one at 1130am PST"}]}]}]}, {"ts": "1702323114.269589", "text": "i am on zoom", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Hrl/K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i am on zoom"}]}]}]}, {"ts": "1702399932.174539", "text": "Staging environment update\n\n<https://staging.compiify.com/> is now available ( credentials <mailto:<EMAIL>|<EMAIL>> / compiify)\n\nFollowing features are pending activation\n1. Google Auth ETA Dec 14th\n2. Adjustment Letter and Reports : Dec 15th", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1702399932.174539", "reply_count": 4, "attachments": [{"from_url": "https://staging.compiify.com/", "service_icon": "https://staging.compiify.com/apple-touch-icon.png", "id": 1, "original_url": "https://staging.compiify.com/", "fallback": "Compiify", "text": "Web site created using create-react-app", "title": "Compiify", "title_link": "https://staging.compiify.com/", "service_name": "staging.compiify.com"}], "blocks": [{"type": "rich_text", "block_id": "jkoFa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Staging environment update\n\n"}, {"type": "link", "url": "https://staging.compiify.com/"}, {"type": "text", "text": " is now available ( credentials "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " / compiify)\n\nFollowing features are pending activation\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Google Auth ETA Dec 14th"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Adjustment Letter and Reports : Dec 15th"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1702400090.835189", "text": "<@U065H3M6WJV> Great job in finalizing the one-pager, it's looking pretty good. Here is the loom video\n<https://www.loom.com/share/210452a9973a494bae77b86ce0b50a5a?sid=0a4169ff-8f82-4da4-9931-71976fbe5a0a>", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"from_url": "https://www.loom.com/share/210452a9973a494bae77b86ce0b50a5a?sid=0a4169ff-8f82-4da4-9931-71976fbe5a0a", "thumb_url": "https://cdn.loom.com/sessions/thumbnails/210452a9973a494bae77b86ce0b50a5a-4x3.jpg", "thumb_width": 1280, "thumb_height": 960, "video_html": "<iframe src=\"https://www.loom.com/embed/210452a9973a494bae77b86ce0b50a5a?autoplay=1\" frameborder=\"0\" width=\"3840\" height=\"2880\" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>", "video_html_width": 3840, "video_html_height": 2880, "id": 1, "original_url": "https://www.loom.com/share/210452a9973a494bae77b86ce0b50a5a?sid=0a4169ff-8f82-4da4-9931-71976fbe5a0a", "fallback": "Loom Video: Follow-up on Advisory Board Opportunity", "text": "In this video, <PERSON>, <PERSON><PERSON><PERSON>, the CEO and co-founder of Compify, follow up on the email I sent earlier regarding an exciting opportunity to join our advisory board. I noticed your extensive experience in people operations, making you a perfect fit for our board. As a board member, you will play a crucial role in helping us build an innovative compensation product. This is not a sales pitch, but rather an invitation to join us and be compensated with equity stake in the company. If you're interested, let's schedule a time to discuss further. I look forward to hearing from you.", "title": "Follow-up on Advisory Board Opportunity", "title_link": "https://www.loom.com/share/210452a9973a494bae77b86ce0b50a5a?sid=0a4169ff-8f82-4da4-9931-71976fbe5a0a", "service_name": "Loom", "service_url": "https://www.loom.com/"}], "blocks": [{"type": "rich_text", "block_id": "LcLnu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Great job in finalizing the one-pager, it's looking pretty good. Here is the loom video\n"}, {"type": "link", "url": "https://www.loom.com/share/210452a9973a494bae77b86ce0b50a5a?sid=0a4169ff-8f82-4da4-9931-71976fbe5a0a"}]}]}]}, {"ts": "1702411989.520609", "text": "<@U065H3M6WJV> In the master data templates, can we mark the optional fields?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702411989.520609", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "cZEck", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " In the master data templates, can we mark the optional fields?"}]}]}]}, {"ts": "1702414521.517769", "text": "My quick notes from the call:\n\n• <PERSON><PERSON> grants\n    ◦ Aligns to prior grant. Additional grant spaced over the remaining time period\n    ◦ Looks at average amount over the previous month. Need to communicate \"based on value on [date]\"\n    ◦ Comp committee assigns a value on backend for planning cycle, but that's not announced to the employees.\n    ◦ Maybe updated quarterly in the tool\n• Perf ratings\n    ◦ Not numeric, different labels (Superstar, etc.)\n• Salary bands\n    ◦ Can share current data, may update in Jan\n• Total Rewards\n    ◦ Need - list of benefits &amp; perks with dollar values\n    ◦ Data is kept in Zenefits, employees can go to profile and look. But doesn't capture all the other areas.\n    ◦ Free lunches - but only for certain employees (locations)\n    ◦ Have eligibility spreadsheet. Some contractors given full-time treatment.\n    ◦ Most important, right benefits assigned to right person. Great to have visibility to the total of cost of perks &amp; benefits. \n    ◦ Medical - cost can range from $600 to $2000 monthly. Okay sharing a range with a caveat.\n    ◦ Differentiate ft / part time / contractor\n    ◦ Position in range - not sure if okay to share\n    ◦ Maybe put the maximum amount? On thinking, prefers average", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LbqVg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "My quick notes from the call:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Lumen grants"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Aligns to prior grant. Additional grant spaced over the remaining time period"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Looks at average amount over the previous month. Need to communicate \"based on value on [date]\""}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Comp committee assigns a value on backend for planning cycle, but that's not announced to the employees."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Maybe updated quarterly in the tool"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Perf ratings"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Not numeric, different labels (Superstar, etc.)"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Salary bands"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can share current data, may update in Jan"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Total Rewards"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Need - list of benefits & perks with dollar values"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Data is kept in Zenefits, employees can go to profile and look. But doesn't capture all the other areas."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Free lunches - but only for certain employees (locations)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Have eligibility spreadsheet. Some contractors given full-time treatment."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Most important, right benefits assigned to right person. Great to have visibility to the total of cost of perks & benefits. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Medical - cost can range from $600 to $2000 monthly. Okay sharing a range with a caveat."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Differentiate ft / part time / contractor"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Position in range - not sure if okay to share"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Maybe put the maximum amount? On thinking, prefers average"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}, {"ts": "**********.056629", "text": "<PERSON><PERSON> has already uploaded their <https://docs.google.com/spreadsheets/d/1fopvCam0v0gztD8wMj-vNJuo-luar8Yk4t2gtG6uMhA/edit#gid=0|benefits eligibility sheet> -- we'll still need a few of the averages for things like medical coverage, but this will help us think through Total Rewards requirements in more detail. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "moneybag", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06A8M2HBUZ", "created": **********, "timestamp": **********, "name": "SDF Benefits Eligibility", "title": "SDF Benefits Eligibility", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 60099, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1fopvCam0v0gztD8wMj-vNJuo-luar8Yk4t2gtG6uMhA", "external_url": "https://docs.google.com/spreadsheets/d/1fopvCam0v0gztD8wMj-vNJuo-luar8Yk4t2gtG6uMhA/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1fopvCam0v0gztD8wMj-vNJuo-luar8Yk4t2gtG6uMhA/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A8M2HBUZ-f5dce9b997/sdf_benefits_eligibility_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHRLHdgYpjSMD2/KnFSXzjtTXVieBQJieY3tTldiwBxTdjelKqsGBIoKRLRRRQIKKKKACiiigAooooAKKKKACiiigAooooA/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06A8M2HBUZ/sdf_benefits_eligibility", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "1onpq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON> has already uploaded their "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1fopvCam0v0gztD8wMj-vNJuo-luar8Yk4t2gtG6uMhA/edit#gid=0", "text": "benefits eligibility sheet"}, {"type": "text", "text": " -- we'll still need a few of the averages for things like medical coverage, but this will help us think through Total Rewards requirements in more detail. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "**********.752529", "text": "Hey y'all. For our working session, I would ideally like to try a test run through a merit planning workflow, and even to record that for later reference.\n• Maybe we can have <@U0658EW4B8D> be a guinea pig and see if he can complete merit &amp; bonus recommendations for \"direct reports\" without getting stuck\n• And, I want to see how a leader or executive with multiple managers would see the effects of someone else's recommendations, so maybe <@U04DKEFP1K8> and <@U04DS2MBWP4> can help with that walkthrough\nI'm finding that I get a little confused in the current UI, and our designer definitely does not understand these flows yet, so having a recorded walkthrough could be really useful for both of us. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oLYtw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey y'all. For our working session, I would ideally like to try a test run through a merit planning workflow, and even to record that for later reference.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Maybe we can have "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " be a guinea pig and see if he can complete merit & bonus recommendations for \"direct reports\" without getting stuck"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "And, I want to see how a leader or executive with multiple managers would see the effects of someone else's recommendations, so maybe "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " and "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " can help with that walkthrough"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nI'm finding that I get a little confused in the current UI, and our designer definitely does not understand these flows yet, so having a recorded walkthrough could be really useful for both of us. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1702490786.561259", "text": "Love it. I can do it", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "keanu_thanks", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "oz3kn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Love it. I can do it"}]}]}]}, {"ts": "1702491401.506729", "text": "Hey team, I have to hop at 11:30 because of a meeting that popped up at Affirm. any flexibility on the call today? Happy to do another half hour or more later on", "user": "U0658EW4B8D", "type": "message", "thread_ts": "1702491401.506729", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Ylz69", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey team, I have to hop at 11:30 because of a meeting that popped up at Affirm. any flexibility on the call today? Happy to do another half hour or more later on"}]}]}]}, {"ts": "1702498372.512989", "text": "Here's the recording of our \"usability session\" with <PERSON> :slightly_smiling_face:\n\n<https://us06web.zoom.us/rec/share/8XD3BwWDIo88YYFwWeIzn4pm7q9cCbh_-axjL_iPeXa_Ofp996VOhi_dvj4-L9wN.bXP-fxuc8d9qz6jh>\nPasscode: *y!x#1*D6*", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TIUgv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here's the recording of our \"usability session\" with <PERSON> "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": "\n\n"}, {"type": "link", "url": "https://us06web.zoom.us/rec/share/8XD3BwWDIo88YYFwWeIzn4pm7q9cCbh_-axjL_iPeXa_Ofp996VOhi_dvj4-L9wN.bXP-fxuc8d9qz6jh"}, {"type": "text", "text": "\nPasscode: "}, {"type": "text", "text": "y!x#1*D6", "style": {"bold": true}}]}]}]}, {"ts": "1702501168.903989", "text": "Unfortunately <PERSON> had a client meeting that ran way over so we didn't meet today - trying to reschedule for Friday", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "k5Fwt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Unfortunately <PERSON> had a client meeting that ran way over so we didn't meet today - trying to reschedule for Friday"}]}]}]}, {"ts": "1702501207.127109", "text": "Ah okk", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "uzjWT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ah okk"}]}]}]}, {"ts": "1702503252.586979", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> After our working session, I tried making some \"illustrative mockups\" with the current Compiify, just working with what I could adjust in the browser itself.\n\nHere's an example of what it could look like if recommended values were prefilled, but not automatically applied to the budget until the manager actually edits them. (See if you can tell the difference?)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702503252.586979", "reply_count": 1, "files": [{"id": "F06AQQTKP9N", "created": 1702503242, "timestamp": 1702503242, "name": "Recommendations_Prefill_Gray.png", "title": "Recommendations_Prefill_Gray.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 847266, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06AQQTKP9N/recommendations_prefill_gray.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06AQQTKP9N/download/recommendations_prefill_gray.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_360.png", "thumb_360_w": 360, "thumb_360_h": 235, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_480.png", "thumb_480_w": 480, "thumb_480_h": 313, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_720.png", "thumb_720_w": 720, "thumb_720_h": 470, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_800.png", "thumb_800_w": 800, "thumb_800_h": 522, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_960.png", "thumb_960_w": 960, "thumb_960_h": 626, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQTKP9N-788e1e4968/recommendations_prefill_gray_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 668, "original_w": 3736, "original_h": 2438, "thumb_tiny": "AwAfADDS65oxSClHSgA/GlpMUc+1AATgZ6/SgHPJFFFABigUUCgAxS4oooASlpKWgD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06AQQTKP9N/recommendations_prefill_gray.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06AQQTKP9N-f79ef17629", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06AQQU14QG", "created": 1702503246, "timestamp": 1702503246, "name": "Recommendations_Prefill_Gray_WithEdits.png", "title": "Recommendations_Prefill_Gray_WithEdits.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 835066, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06AQQU14QG/recommendations_prefill_gray_withedits.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06AQQU14QG/download/recommendations_prefill_gray_withedits.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_360.png", "thumb_360_w": 360, "thumb_360_h": 232, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_480.png", "thumb_480_w": 480, "thumb_480_h": 309, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_720.png", "thumb_720_w": 720, "thumb_720_h": 464, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_800.png", "thumb_800_w": 800, "thumb_800_h": 515, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_960.png", "thumb_960_w": 960, "thumb_960_h": 618, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AQQU14QG-6396eac20f/recommendations_prefill_gray_withedits_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 659, "original_w": 3728, "original_h": 2400, "thumb_tiny": "AwAeADDS9aAB2pBSjp1oAPxpfxpKPxFAB79aAc8kUUUAFAooFAC0UUUAIaWkNLQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06AQQU14QG/recommendations_prefill_gray_withedits.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06AQQU14QG-eeeba13790", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "wacyq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " After our working session, I tried making some \"illustrative mockups\" with the current Compiify, just working with what I could adjust in the browser itself.\n\nHere's an example of what it could look like if recommended values were prefilled, but not automatically applied to the budget until the manager actually edits them. (See if you can tell the difference?)"}]}]}]}, {"ts": "1702503428.913209", "text": "Here is another option I tried, in case the gray looked too much like an 'inactive' state. The purple color would ideally be a lighter shade; this was just what was available in the browser page. So again, just think of this as \"illustration\" and not entirely accurate. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F06A2296DGA", "created": 1702503385, "timestamp": 1702503385, "name": "Recommendations_Prefill_Purple.png", "title": "Recommendations_Prefill_Purple.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 846094, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06A2296DGA/recommendations_prefill_purple.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06A2296DGA/download/recommendations_prefill_purple.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_360.png", "thumb_360_w": 360, "thumb_360_h": 233, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_480.png", "thumb_480_w": 480, "thumb_480_h": 311, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_720.png", "thumb_720_w": 720, "thumb_720_h": 466, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_800.png", "thumb_800_w": 800, "thumb_800_h": 518, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_960.png", "thumb_960_w": 960, "thumb_960_h": 622, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A2296DGA-d5b89e4f02/recommendations_prefill_purple_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 663, "original_w": 3732, "original_h": 2418, "thumb_tiny": "AwAfADDS9aAB2pBSjpQAfjS0mKOfagAJxz1oBzyRRRQAUCigUAFLiiigBKKDS0Af/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06A2296DGA/recommendations_prefill_purple.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06A2296DGA-fd9d02f948", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F06A229KKHQ", "created": 1702503390, "timestamp": 1702503390, "name": "Recommendations_Prefill_Purple_WithEdits.png", "title": "Recommendations_Prefill_Purple_WithEdits.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 843536, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06A229KKHQ/recommendations_prefill_purple_withedits.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06A229KKHQ/download/recommendations_prefill_purple_withedits.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_360.png", "thumb_360_w": 360, "thumb_360_h": 233, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_480.png", "thumb_480_w": 480, "thumb_480_h": 310, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_720.png", "thumb_720_w": 720, "thumb_720_h": 466, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_800.png", "thumb_800_w": 800, "thumb_800_h": 517, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_960.png", "thumb_960_w": 960, "thumb_960_h": 621, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A229KKHQ-e884d7820d/recommendations_prefill_purple_withedits_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 662, "original_w": 3746, "original_h": 2422, "thumb_tiny": "AwAfADDS65oAHakHelHSgA/GlpMUc+1AATgZ6/SgHPJFFFABigUUCgAxS4oooASiiloA/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06A229KKHQ/recommendations_prefill_purple_withedits.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06A229KKHQ-7b1f68278c", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "8U3Fv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is another option I tried, in case the gray looked too much like an 'inactive' state. The purple color would ideally be a lighter shade; this was just what was available in the browser page. So again, just think of this as \"illustration\" and not entirely accurate. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1702504619.187999", "text": "could we see how the lighter shade of the purple color would look like the dark purple looks like the real values. The grey color option doesn’t look bad at all especially if the managers will see a pop-up on the first login that your HR has already pre-populated the recommended options for you to consider.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702504619.187999", "reply_count": 3, "reactions": [{"name": "thinking_face", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TTYm+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "could we see how the lighter shade of the purple color would look like the dark purple looks like the real values. The grey color option doesn’t look bad at all especially if the managers will see a pop-up on the first login that your HR has already pre-populated the recommended options for you to consider."}]}]}]}, {"ts": "1702504651.783109", "text": "I am wondering how our competitors are solving for this", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VBVK1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am wondering how our competitors are solving for this"}]}]}]}, {"ts": "1702504775.501879", "text": "I don't know if any competitor actually pre-fills values. My assumption is that they would only show company recommended ranges, at most. But let me see if we can find any examples in practice..", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0Di6O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I don't know if any competitor actually pre-fills values. My assumption is that they would only show company recommended ranges, at most. But let me see if we can find any examples in practice.."}]}]}]}, {"ts": "1702504798.130029", "text": "I think pave does that", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "g26/5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think pave does that"}]}]}]}, {"ts": "1702505211.270969", "text": "From this screen, you can see they have a recommendation above the field -- less clear whether it was also prefilled, but if so, it seems that it has been applied against the budget too :thinking_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702505211.270969", "reply_count": 2, "files": [{"id": "F069Z8QJ2NR", "mode": "tombstone"}], "blocks": [{"type": "rich_text", "block_id": "O0vFE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "From this screen, you can see they have a recommendation above the field -- less clear whether it was also prefilled, but if so, it seems that it has been applied against the budget too "}, {"type": "emoji", "name": "thinking_face", "unicode": "1f914"}]}]}]}, {"ts": "1702505435.216609", "text": "Another, probably older design, shows the concept of an \"empty\" field with an extra highlight. This would be different from entering $0.00 intentionally as the value, I think.", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F069Z98189K", "created": 1702505404, "timestamp": 1702505404, "name": "Pave_screenshot_older.png", "title": "Pave_screenshot_older.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 1513768, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F069Z98189K/pave_screenshot_older.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F069Z98189K/download/pave_screenshot_older.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_360.png", "thumb_360_w": 360, "thumb_360_h": 234, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_480.png", "thumb_480_w": 480, "thumb_480_h": 311, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_720.png", "thumb_720_w": 720, "thumb_720_h": 467, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_800.png", "thumb_800_w": 800, "thumb_800_h": 519, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_960.png", "thumb_960_w": 960, "thumb_960_h": 623, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F069Z98189K-10cc290dc7/pave_screenshot_older_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 664, "original_w": 2910, "original_h": 1888, "thumb_tiny": "AwAfADCeyKm2Unjk9qsgJ/smq+nH/RF57mrW4etN7gJhP9mj5P8AZoDe9LkUgGkIOcD8qMIey/lS7hS5HrQBRsoY5LVTIqk5PX61Y+yW/wDzyX8qj07/AI9F+pq3Te4iD7LBj/Vr+VJ9kt/+ea1P2oFIZD9kt/8AnmtH2S3/AOeS1PRQB//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F069Z98189K/pave_screenshot_older.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F069Z98189K-f9bd8f8145", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "/9x/t", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Another, probably older design, shows the concept of an \"empty\" field with an extra highlight. This would be different from entering $0.00 intentionally as the value, I think."}]}]}]}, {"ts": "1702507342.575709", "text": "This configurations page has as option to prefill recommendations\n<https://support.pave.com/hc/en-us/articles/6739129295895-Recommendation-Logic>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F069ZDADMV3", "created": 1702507314, "timestamp": 1702507314, "name": "Screenshot 2023-12-13 at 2.41.27 PM.png", "title": "Screenshot 2023-12-13 at 2.41.27 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 928718, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F069ZDADMV3/screenshot_2023-12-13_at_2.41.27___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F069ZDADMV3/download/screenshot_2023-12-13_at_2.41.27___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 214, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 285, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 427, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 475, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 570, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F069ZDADMV3-1536326170/screenshot_2023-12-13_at_2.41.27___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 608, "original_w": 3782, "original_h": 2244, "thumb_tiny": "AwAcADDTxQelFB6UAICaWkFLQAUUUUAFB6UUhoAWim8+po59TQA6imgn1oIDHnr6jrQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F069ZDADMV3/screenshot_2023-12-13_at_2.41.27___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F069ZDADMV3-a7da591c65", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "1oz7V", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This configurations page has as option to prefill recommendations\n"}, {"type": "link", "url": "https://support.pave.com/hc/en-us/articles/6739129295895-Recommendation-Logic"}]}]}]}, {"ts": "1702507468.425839", "text": "Interesting! In that case (guessing from the screenshots above) it seems like they are prefilled as \"real\" values and not _just_ suggestions.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PcBMf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Interesting! In that case (guessing from the screenshots above) it seems like they are prefilled as \"real\" values and not "}, {"type": "text", "text": "just ", "style": {"italic": true}}, {"type": "text", "text": "suggestions."}]}]}]}, {"ts": "1702507604.259159", "text": "probably. I think we can minimize this confusion with a tooltip and/or popup and and just show the real values for the MVP purposes. Once we have actual customers start using it, lets take it again then", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702507604.259159", "reply_count": 2, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ulL6s", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "probably. I think we can minimize this confusion with a tooltip and/or popup and and just show the real values for the MVP purposes. Once we have actual customers start using it, lets take it again then"}]}]}]}, {"ts": "1702507682.460809", "text": "this view shows prefilled with the recommended values as both the recommended and the filled numbers are same", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LczXe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "this view shows prefilled with the recommended values as both the recommended and the filled numbers are same"}]}]}]}, {"ts": "1702507701.270769", "text": "Yep, that's why I think their \"prefill\" setting is \"real\" values", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VhEjZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yep, that's why I think their \"prefill\" setting is \"real\" values"}]}]}]}, {"ts": "1702507716.678799", "text": "If the HR owner doesn't opt for \"prefill\", perhaps the \"recommendations\" still show above the field.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sN+Dd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If the HR owner doesn't opt for \"prefill\", perhaps the \"recommendations\" still show above the field."}]}]}]}, {"ts": "1702507725.758089", "text": "yes", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IB8ze", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes"}]}]}]}, {"ts": "1702507739.733679", "text": "Their format adds a lot of height, since it is there for each row.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9iQnz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Their format adds a lot of height, since it is there for each row."}]}]}]}, {"ts": "1702507784.627869", "text": "Although, I think ease-of-use is more important than compactness of data.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NYD4j", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Although, I think ease-of-use is more important than compactness of data."}]}]}]}, {"ts": "1702507880.385299", "text": "I think the ease of use is directly proportional to amount of data they can see to make it easier to make the decisions without having to scroll and multiple clicks.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3q7A1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think the ease of use is directly proportional to amount of data they can see to make it easier to make the decisions without having to scroll and multiple clicks."}]}]}]}, {"ts": "1702507934.156229", "text": "I do think they need to be able to see multiple rows and a good amount of data. You can see <PERSON><PERSON> has a way for the user to resize the budget area so that they can fit more rows on the screen, for example.", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "yP45Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I do think they need to be able to see multiple rows and a good amount of data. You can see <PERSON><PERSON> has a way for the user to resize the budget area so that they can fit more rows on the screen, for example."}]}]}]}, {"ts": "1702511356.825889", "text": "ChartHop's <https://www.youtube.com/watch?v=dV3C4eIQ-6w|marketing video >had a screenshot of their config options for guidelines &amp; prefill as well.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702511356.825889", "reply_count": 1, "files": [{"id": "F069VV1M3AA", "created": 1702511310, "timestamp": 1702511310, "name": "ChartHop_Planning_Configuration_Guidelines.png", "title": "ChartHop_Planning_Configuration_Guidelines.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 788020, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F069VV1M3AA/charthop_planning_configuration_guidelines.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F069VV1M3AA/download/charthop_planning_configuration_guidelines.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_360.png", "thumb_360_w": 360, "thumb_360_h": 212, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_480.png", "thumb_480_w": 480, "thumb_480_h": 283, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_720.png", "thumb_720_w": 720, "thumb_720_h": 425, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_800.png", "thumb_800_w": 800, "thumb_800_h": 472, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_960.png", "thumb_960_w": 960, "thumb_960_h": 566, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F069VV1M3AA-084ddbab60/charthop_planning_configuration_guidelines_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 604, "original_w": 2224, "original_h": 1312, "thumb_tiny": "AwAcADCbTuYGz/fP8hVvaB2qppp/cN/vn+lXKb3ATGegH5Uu36flRS0gEx7D8qY8SkbucgHvUlI/3G+lMClppxA3++atl6o2fyxED1/pU5JoYE276flS7z7VBk0ZNICffSO/yN9KhyaRidp+lAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F069VV1M3AA/charthop_planning_configuration_guidelines.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F069VV1M3AA-4394189595", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "attachments": [{"from_url": "https://www.youtube.com/watch?v=dV3C4eIQ-6w", "service_icon": "https://a.slack-edge.com/80588/img/unfurl_icons/youtube.png", "thumb_url": "https://i.ytimg.com/vi/dV3C4eIQ-6w/hqdefault.jpg", "thumb_width": 480, "thumb_height": 360, "video_html": "<iframe width=\"400\" height=\"225\" src=\"https://www.youtube.com/embed/dV3C4eIQ-6w?feature=oembed&autoplay=1&iv_load_policy=3\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" allowfullscreen title=\"Easy Way to Streamline Compensation Reviews with ChartHop\"></iframe>", "video_html_width": 400, "video_html_height": 225, "id": 1, "original_url": "https://www.youtube.com/watch?v=dV3C4eIQ-6w", "fallback": "YouTube Video: Easy Way to Streamline Compensation Reviews with ChartHop", "title": "Easy Way to Streamline Compensation Reviews with ChartHop", "title_link": "https://www.youtube.com/watch?v=dV3C4eIQ-6w", "author_name": "ChartHop", "author_link": "https://www.youtube.com/@ChartHop", "service_name": "YouTube", "service_url": "https://www.youtube.com/"}], "blocks": [{"type": "rich_text", "block_id": "cxnRK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ChartHop's "}, {"type": "link", "url": "https://www.youtube.com/watch?v=dV3C4eIQ-6w", "text": "marketing video "}, {"type": "text", "text": "had a screenshot of their config options for guidelines & prefill as well."}]}]}]}, {"ts": "1702512078.651579", "text": "Yes to everything and worth noting that you can manually load values as well, if you do not want to do modeled prefill as you see through the config", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4GnYj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes to everything and worth noting that you can manually load values as well, if you do not want to do modeled prefill as you see through the config"}]}]}]}, {"ts": "1702512114.375339", "text": "<@U0658EW4B8D> if there is nothing prefilled, are those fields then 'blank' rather than 'zero'?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "NHfms", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " if there is nothing prefilled, are those fields then 'blank' rather than 'zero'?"}]}]}]}, {"ts": "1702513907.482249", "text": "yes", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "IB8ze", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes"}]}]}]}, {"ts": "1702516954.943529", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> Here are <https://docs.google.com/presentation/d/1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0/edit#slide=id.p|my recommendations> for how we should adjust the behavior of recommended values &amp; updated calculations as managers are editing in Merit View. We can decide later whether to keep, revise, or remove the \"Reviewed\" state based on feedback from advisors.\n\nPlease let me know if you have any questions or have any trouble understanding these requirements. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702516954.943529", "reply_count": 4, "files": [{"id": "F069W8S9GEA", "created": 1702516957, "timestamp": 1702516957, "name": "Merit View changes, Dec 2023", "title": "Merit View changes, Dec 2023", "mimetype": "application/vnd.google-apps.presentation", "filetype": "gpres", "pretty_type": "Google Slides", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0", "external_url": "https://docs.google.com/presentation/d/1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0/edit#slide=id.p", "url_private": "https://docs.google.com/presentation/d/1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0/edit#slide=id.p", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_360.png", "thumb_360_w": 360, "thumb_360_h": 203, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_480.png", "thumb_480_w": 480, "thumb_480_h": 270, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_720.png", "thumb_720_w": 720, "thumb_720_h": 405, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F069W8S9GEA-5385953743/merit_view_changes__dec_2023_800.png", "thumb_800_w": 800, "thumb_800_h": 450, "thumb_tiny": "AwAbADDTooqPMvPC/nQBJRTCZAeAp/GgmTnAU/jQA+io8yZ+6MfWpKACiiigBCAeozQAB0AFLRQAUUUUAf/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F069W8S9GEA/merit_view_changes__dec_2023", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "a0L7j", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Here are "}, {"type": "link", "url": "https://docs.google.com/presentation/d/1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0/edit#slide=id.p", "text": "my recommendations"}, {"type": "text", "text": " for how we should adjust the behavior of recommended values & updated calculations as managers are editing in Merit View. We can decide later whether to keep, revise, or remove the \"Reviewed\" state based on feedback from advisors.\n\nPlease let me know if you have any questions or have any trouble understanding these requirements. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1702517079.270589", "text": "Team-  I have reorganized the Drive.\n<@U04DKEFP1K8> pls move technical folder to engineering. I am unable to move it.\n<https://drive.google.com/drive/u/0/folders/1P2E5KZS-vfovWHp3L_fLdZqmek1Q8guI>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702517079.270589", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "qKh4h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Team-  I have reorganized the Drive.\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " pls move technical folder to engineering. I am unable to move it.\n"}, {"type": "link", "url": "https://drive.google.com/drive/u/0/folders/1P2E5KZS-vfovWHp3L_fLdZqmek1Q8guI"}]}]}]}, {"ts": "1702522520.227789", "text": "<@U065H3M6WJV> I have added <PERSON> and <PERSON> to all advisory meetings during the week of Jan 2nd. What I would love to do is have each of our advisors role play as a customer while <PERSON> will role play as the sales rep to do the customer discovery and go through the sales pitch with them. It will be about 30- 45 min pitch and then we will leave last 15 mins for advisors to give feedback to <PERSON> on what parts of sales pitch and customer discovery resonated with them, so <PERSON> can fine tune his pitch accordingly. Plus it's a great way for <PERSON> to gain familiarity with our target buyers.\n\nCan you please run this by the advisors to make sure they are good with this role play exercise?", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1702522560.000000"}, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "W/Z0P", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I have added <PERSON> and <PERSON> to all advisory meetings during the week of Jan 2nd. What I would love to do is have each of our advisors role play as a customer while <PERSON> will role play as the sales rep to do the customer discovery and go through the sales pitch with them. It will be about 30- 45 min pitch and then we will leave last 15 mins for advisors to give feedback to <PERSON> on what parts of sales pitch and customer discovery resonated with them, so <PERSON> can fine tune his pitch accordingly. Plus it's a great way for <PERSON> to gain familiarity with our target buyers.\n\nCan you please run this by the advisors to make sure they are good with this role play exercise?"}]}]}]}, {"ts": "1702571342.490109", "text": "<@U065H3M6WJV> <@U04DKEFP1K8> I wiil be signing the contract with Merge today. I just put a time on your cal today to discuss the implementation plan. <@U04DKEFP1K8> Can <PERSON><PERSON><PERSON> join the call as well?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702571342.490109", "reply_count": 2, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "SaPAz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I wiil be signing the contract with Me<PERSON> today. I just put a time on your cal today to discuss the implementation plan. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can <PERSON><PERSON><PERSON> join the call as well?"}]}]}]}, {"ts": "1702573224.319629", "text": "What's the best way to keep a checklist of the customer-specific items? Maybe a separate \"UAT\" file for each one? (I don't think these should be shared directly with the customer though)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702573224.319629", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Nwl0i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "What's the best way to keep a checklist of the customer-specific items? Maybe a separate \"UAT\" file for each one? (I don't think these should be shared directly with the customer though)"}]}]}]}, {"ts": "1702573400.570549", "text": "<@U0658EW4B8D>  We have started writing blogs. <https://docs.google.com/document/d/1Vw1Rmk-kduGM07R6KF38QSKHCwHfvAfokjyvjBTA56c/edit#heading=h.kamnxapw2bvz|Here> is a link to the first blog. Can you please go through this and add your comments/suggestions to this doc? We will be writing about 3 blogs per month. Also let's discuss the blog/content strategy to make sure we are writing blogs on topics that are most meaningful to customers.\n<@U065H3M6WJV> I would love to get <PERSON>'s take on the the topics that we should be writing blogs on. I might join the call for the first 10 mins just for that", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702573400.570549", "reply_count": 15, "reactions": [{"name": "+1", "users": ["U0658EW4B8D"], "count": 1}], "files": [{"id": "F06A0FC2BLN", "created": 1702573402, "timestamp": 1702573402, "name": "What is Compensation Management?", "title": "What is Compensation Management?", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 154369, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1Vw1Rmk-kduGM07R6KF38QSKHCwHfvAfokjyvjBTA56c", "external_url": "https://docs.google.com/document/d/1Vw1Rmk-kduGM07R6KF38QSKHCwHfvAfokjyvjBTA56c/edit#heading=h.kamnxapw2bvz", "url_private": "https://docs.google.com/document/d/1Vw1Rmk-kduGM07R6KF38QSKHCwHfvAfokjyvjBTA56c/edit#heading=h.kamnxapw2bvz", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06A0FC2BLN-867228bb1b/what_is_compensation_management__1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1035, "thumb_tiny": "AwAwACXTJxSbhQx9aTj3oAdRSA47GjPsaAFooooARqT8aVqb+NAC59zRn3NHNKM5oAWiiigBCcUmTTiM0mKAEBpQfajFGBQAtFFFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06A0FC2BLN/what_is_compensation_management_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "2jCCw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": "  We have started writing blogs. "}, {"type": "link", "url": "https://docs.google.com/document/d/1Vw1Rmk-kduGM07R6KF38QSKHCwHfvAfokjyvjBTA56c/edit#heading=h.kamnxapw2bvz", "text": "Here"}, {"type": "text", "text": " is a link to the first blog. Can you please go through this and add your comments/suggestions to this doc? We will be writing about 3 blogs per month. Also let's discuss the blog/content strategy to make sure we are writing blogs on topics that are most meaningful to customers.\n"}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I would love to get <PERSON>'s take on the the topics that we should be writing blogs on. I might join the call for the first 10 mins just for that"}]}]}]}, {"ts": "1702580630.852039", "text": "<@U04DS2MBWP4> and <@U04DKEFP1K8> Are you able to join today's working session? I thought we could continue the exercise of seeing how the current Compiify tool works for a leader (<PERSON><PERSON> user) in the comp cycle.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ePWXm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Are you able to join today's working session? I thought we could continue the exercise of seeing how the current Compiify tool works for a leader (<PERSON><PERSON> user) in the comp cycle."}]}]}]}, {"ts": "1702580669.055109", "text": "sure", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "isGtt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "sure"}]}]}]}, {"ts": "1702584590.380809", "text": "Whoops I thought we were done then heard <PERSON> at the very end", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "d+BeZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Whoops I thought we were done then heard <PERSON> at the very end"}]}]}]}, {"ts": "1702584598.167129", "text": "If it was super important feel free to type it here!", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "aYdRq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If it was super important feel free to type it here!"}]}]}]}, {"ts": "1702584659.864909", "text": "My Zoom froze...", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "r2FxM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "My Zoom froze..."}]}]}]}, {"ts": "1702584685.888169", "text": "I dunno what happened to my internet there :disappointed:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "D4caC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I dunno what happened to my internet there "}, {"type": "emoji", "name": "disappointed", "unicode": "1f61e"}]}]}]}, {"ts": "1702584715.986249", "text": "Question for y'all: Is there any more from the Compiify tool that we should usability-test in tomorrow's session? Any use cases that we did not get to cover in the last 2 meetings?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GSsC2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Question for y'all: Is there any more from the Compiify tool that we should usability-test in tomorrow's session? Any use cases that we did not get to cover in the last 2 meetings?"}]}]}]}, {"ts": "1702584754.354699", "text": "admin view", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7NBPM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "admin view"}]}]}]}, {"ts": "1702584776.373589", "text": "admin dashboard, settings", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "a1Btn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "admin dashboard, settings"}]}]}]}, {"ts": "1702584848.750019", "text": "So our user would be an HR admin like <PERSON><PERSON> or <PERSON>?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "J/1tw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So our user would be an HR admin like <PERSON><PERSON> or <PERSON>?"}]}]}]}, {"ts": "1702584974.404669", "text": "yep", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HC6zQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yep"}]}]}]}, {"ts": "1702585147.558099", "text": "Do we need to walk through creating a cycle, or mainly focus on what an admin would do during a cycle / at end of cycle?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OFX2M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we need to walk through creating a cycle, or mainly focus on what an admin would do during a cycle / at end of cycle?"}]}]}]}, {"ts": "1702585336.854679", "text": "I think lets do it all", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "p4E2x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think lets do it all"}]}]}]}, {"ts": "1702585353.053019", "text": "that way we would be able to cover the whole product.", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1702585381.000000"}, "blocks": [{"type": "rich_text", "block_id": "Pz+cE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "that way we would be able to cover the whole product."}]}]}]}, {"ts": "1702590150.322039", "text": "<@U04DKEFP1K8> I've added some more of the live issues to the <https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=1695437663|list in this sheet>. Can you help by updating the status of those that are already fixed, and enter any JIRA links for those that have tickets already?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702590150.322039", "reply_count": 1, "files": [{"id": "F068XFR96CD", "created": 1701905087, "timestamp": 1701905087, "name": "UAT test cases (draft)", "title": "UAT test cases (draft)", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU", "external_url": "https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F068XFR96CD-017c646aa4/uat_test_cases__draft__1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSYkGlprdRTqACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F068XFR96CD/uat_test_cases__draft_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "x3g67", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I've added some more of the live issues to the "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/10s_nbfQH0Sz_PbTuwsrBl6d3J43UmDjll3i6d8nWqXU/edit#gid=1695437663", "text": "list in this sheet"}, {"type": "text", "text": ". Can you help by updating the status of those that are already fixed, and enter any JIRA links for those that have tickets already?"}]}]}]}, {"ts": "1702590338.812159", "text": "And here's the recording from today's session with \"<PERSON><PERSON>\" (thanks <@U0658EW4B8D> !)\n\n<https://us06web.zoom.us/rec/share/-ov8O4-RbNMwrEYBvpoOh1iBL2SVKtA-yeOjAVcVBk9Y4Lh_HoTwh4BXId4ATMS6.jl49z5gNUGUGCdDx>\nPasscode: *Szp+zh0=*", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D", "U04DS2MBWP4"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "U35Em", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And here's the recording from today's session with \"<PERSON><PERSON>\" (thanks "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " !)\n\n"}, {"type": "link", "url": "https://us06web.zoom.us/rec/share/-ov8O4-RbNMwrEYBvpoOh1iBL2SVKtA-yeOjAVcVBk9Y4Lh_HoTwh4BXId4ATMS6.jl49z5gNUGUGCdDx"}, {"type": "text", "text": "\nPasscode: "}, {"type": "text", "text": "Szp+zh0=", "style": {"bold": true}}]}]}]}, {"ts": "1702606093.224359", "text": "Hey <@U065H3M6WJV> we just got a second meeting set for the product advisory board. Our messaging is working and resonating with prospects.\nLet’s discuss the structure of the advisory board and how to position it to the prospect. I think it might be helpful for you to join the first few meetings. let me know if you want to join some of these meetings as you will be working very closely with these customers", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702606093.224359", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "tjbad", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " we just got a second meeting set for the product advisory board. Our messaging is working and resonating with prospects.\nLet’s discuss the structure of the advisory board and how to position it to the prospect. I think it might be helpful for you to join the first few meetings. let me know if you want to join some of these meetings as you will be working very closely with these customers"}]}]}]}, {"ts": "1702612703.129999", "text": "Also, related to our conversation earlier: <https://hbr.org/2021/10/research-cameras-on-or-off>\n\nA few snippets from this one piece of research published in the Journal of Applied Psychology:\n\n• \"Using the camera was positively correlated to daily feelings of fatigue; the number of hours that employees spent in virtual meetings were not. This indicates that keeping the camera consistently on during meetings is at the heart of the fatigue problem.\"\n• \"Even more interesting to us was our finding that fatigue reduced how engaged employees felt, as well as reducing their voice in meetings.\"\n•  \"To further complicate matters, when we examined our results along with the demographics of the employees, it also turned out that being on camera was more fatiguing for certain groups — specifically, women and employees newer to the organization.\"\nI always like bringing a research-focused and empirically-driven lens to these conversations and figured this would be interesting to do a deep dive on.", "user": "U0658EW4B8D", "type": "message", "thread_ts": "1702612703.129999", "reply_count": 4, "attachments": [{"from_url": "https://hbr.org/2021/10/research-cameras-on-or-off", "ts": 1635250533, "image_url": "https://hbr.org/resources/images/article_assets/2021/10/Oct21_26_5198264_1277308208.jpg", "image_width": 1200, "image_height": 675, "image_bytes": 169613, "service_icon": "https://hbr.org/resources/images/apple-touch-icon.png", "id": 1, "original_url": "https://hbr.org/2021/10/research-cameras-on-or-off", "fallback": "Harvard Business Review: Research: Cameras On or Off?", "text": "Managers looking to encourage engagement and inclusion in remote meetings have long encouraged team members to keep their cameras turned on. But researchers examining remote employees’ reactions to the constant video conference calls of the remote work era have found that keeping video on all day actually increases so-called “zoom fatigue.” That’s particularly true for women and new employees, groups that already may feel that they are under the microscope.", "title": "Research: Cameras On or Off?", "title_link": "https://hbr.org/2021/10/research-cameras-on-or-off", "service_name": "Harvard Business Review"}], "blocks": [{"type": "rich_text", "block_id": "NkCVl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also, related to our conversation earlier: "}, {"type": "link", "url": "https://hbr.org/2021/10/research-cameras-on-or-off"}, {"type": "text", "text": "\n\nA few snippets from this one piece of research published in the Journal of Applied Psychology:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "\"Using the camera was positively correlated to daily feelings of fatigue; the number of hours that employees spent in virtual meetings were not. This indicates that keeping the camera consistently on during meetings is at the heart of the fatigue problem.\""}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\"Even more interesting to us was our finding that fatigue reduced how engaged employees felt, as well as reducing their voice in meetings.\""}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": " \"To further complicate matters, when we examined our results along with the demographics of the employees, it also turned out that being on camera was more fatiguing for certain groups — specifically, women and employees newer to the organization.\""}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nI always like bringing a research-focused and empirically-driven lens to these conversations and figured this would be interesting to do a deep dive on."}]}]}]}, {"ts": "1702669205.152899", "text": "Here's today's \"usability\" session for our CHRO admin:\n\n<https://us06web.zoom.us/rec/share/ubGGykcvLStxsJqEoE6nTuZWnWLxRRl0ByrjbNwtu0UJS-WxhEcBOEqcRO02wPug.cfJ3u2pmOQcdUpue>\nPasscode: *i#y^87Hu*", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4", "U04DKEFP1K8"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "svVuM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here's today's \"usability\" session for our CHRO admin:\n\n"}, {"type": "link", "url": "https://us06web.zoom.us/rec/share/ubGGykcvLStxsJqEoE6nTuZWnWLxRRl0ByrjbNwtu0UJS-WxhEcBOEqcRO02wPug.cfJ3u2pmOQcdUpue"}, {"type": "text", "text": "\nPasscode:"}, {"type": "text", "text": " i#y^87Hu", "style": {"bold": true}}]}]}]}, {"ts": "1702736780.259119", "text": "<!here>\n\nHere is the parent jira to track issues for Digital Asset implementation\n\n<https://compiify.atlassian.net/browse/COM-1859>", "user": "U04DKEFP1K8", "type": "message", "edited": {"user": "U04DKEFP1K8", "ts": "1702737903.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:11920::15684be09c1f11ee97825747be94a3ef", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-1859?atlOrigin=eyJpIjoiMmIzNDExMzc5Mjk1NDA4NmI5N2MyNDYxZGQzM2RhMzgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-1859 Digital Asset UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:11920::15684be29c1f11ee97825747be94a3ef", "elements": [{"type": "mrkdwn", "text": "Status: *In Progress*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:11920::15684be19c1f11ee97825747be94a3ef", "elements": [{"type": "button", "action_id": "{\"issueId\":\"11920\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"11920\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-1859", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "3WI0Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": "\n\nHere is the parent jira to track issues for Digital Asset implementation\n\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1859"}]}]}]}, {"ts": "1702736865.491889", "text": "<!here> is parent jira <https://compiify.atlassian.net/browse/COM-1893> to track Merit View enhancement listed in the google slides <https://docs.google.com/presentation/d/1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0/edit#slide=id.g263cddf32b6_0_137>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:11954::4857fff09c1f11ee8489dbb8a9985de5", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-1893?atlOrigin=eyJpIjoiNmM1YWRmOWFjY2I2NGNjMThjZTQ3MDJjNDI5OGZlYWEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-1893 Merit View Enhancements - Direct Reports Tab ( Dec 2023)>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:11954::485827009c1f11ee8489dbb8a9985de5", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:11954::4857fff19c1f11ee8489dbb8a9985de5", "elements": [{"type": "button", "action_id": "{\"issueId\":\"11954\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"11954\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-1893", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "nX93K", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " is parent jira "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1893"}, {"type": "text", "text": " to track Merit View enhancement listed in the google slides "}, {"type": "link", "url": "https://docs.google.com/presentation/d/1Pz4lzDgz43IaPCF-viCUzjwLGaEZKPOuxwjl2a5U1Z0/edit#slide=id.g263cddf32b6_0_137"}]}]}]}, {"ts": "1702736906.071909", "text": "<!here> Here is parent jira which tracks all the items listed in <PERSON>'s UAT spreadsheet <https://compiify.atlassian.net/browse/COM-1905>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:11966::607455209c1f11ee9c6e455153c1a95a", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-1905?atlOrigin=eyJpIjoiMDU2Y2JjN2JiNWFiNDZjZDg0ODFiYWY4NTM4YWZhZmYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-1905 Dec 2023 Issues from Rachel's UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:11966::607455229c1f11ee9c6e455153c1a95a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:11966::607455219c1f11ee9c6e455153c1a95a", "elements": [{"type": "button", "action_id": "{\"issueId\":\"11966\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"11966\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-1905", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "HzDs9", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Here is parent jira which tracks all the items listed in <PERSON>'s UAT spreadsheet "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1905"}]}]}]}, {"ts": "1702858701.026759", "text": "I'm trying to map Stellar's benefits and salary bands into our Total Rewards format, and decided to mock this up in a spreadsheet first before doing it in Figma. :slightly_smiling_face:\n\n<https://docs.google.com/spreadsheets/d/143Nqi24bq68Gr6stD-WcO2xGoMPPh3h_b3uJzX-vgDg/edit#gid=120147510|Have a look here>; I pulled a couple random employees and mapped their actual salaries, salary band positions, and comparatios. I also made an attempt to total up the Benefits &amp; Perks, but I think we'll need to have <PERSON><PERSON> &amp; <PERSON> react to the way these are presented.", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F06ABVBB75L", "created": 1702858704, "timestamp": 1702858704, "name": "Mockups of Total Rewards for Stellar", "title": "Mockups of Total Rewards for Stellar", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "143Nqi24bq68Gr6stD-WcO2xGoMPPh3h_b3uJzX-vgDg", "external_url": "https://docs.google.com/spreadsheets/d/143Nqi24bq68Gr6stD-WcO2xGoMPPh3h_b3uJzX-vgDg/edit#gid=120147510", "url_private": "https://docs.google.com/spreadsheets/d/143Nqi24bq68Gr6stD-WcO2xGoMPPh3h_b3uJzX-vgDg/edit#gid=120147510", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06ABVBB75L-b7b4f3d08a/mockups_of_total_rewards_for_stellar_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHQZmDYUD8aAz7sEL+dKfvijjcfWgB1FFFABRRRQA1gSaAOc0jgFuSfwOKAvJ5b/vqgB9FIBgYyfxNLQAUUUUANIy9J/ER604rk5oA5oAAD7flS0UUAFFFFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06ABVBB75L/mockups_of_total_rewards_for_stellar", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "1uF+6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'm trying to map Stellar's benefits and salary bands into our Total Rewards format, and decided to mock this up in a spreadsheet first before doing it in Figma. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": "\n\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/143Nqi24bq68Gr6stD-WcO2xGoMPPh3h_b3uJzX-vgDg/edit#gid=120147510", "text": "Have a look here"}, {"type": "text", "text": "; I pulled a couple random employees and mapped their actual salaries, salary band positions, and comparatios. I also made an attempt to total up the Benefits & Perks, but I think we'll need to have Katya & Lisa react to the way these are presented."}]}]}]}, {"ts": "1702858833.061379", "text": "Some observations based on this exercise:\n• Unclear how Lumen Grants should be included in the total annual rewards chart -- once we have a Fair Market Value, I can redo the donut graph, but the vesting graph might still be a challenge\n• The benefits categories don't really match what's in the Compiify Figma or PRD docs; we need to decide how flexible we'll make it for customers to define their own categories of benefits for grouping, for example\n• Some of the benefits values felt low, so I played with hiding some specific values while still including them in the rolled-up total\n• The 2 employees I mocked up are in different US states, and I would expect the actual value of their benefits & perks to differ, but we didn't get this level of detail from Katya.\n• Our Salary band design doesn't currently include anything to distinguish location/tier that could affect the ranges for employees in the same Job level. ", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702858833.061379", "reply_count": 3, "edited": {"user": "U065H3M6WJV", "ts": "1702859164.000000"}, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "1/kYc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Some observations based on this exercise:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Unclear how Lumen Grants should be included in the total annual rewards chart -- once we have a Fair Market Value, I can redo the donut graph, but the vesting graph might still be a challenge"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The benefits categories don't really match what's in the Compiify Figma or PRD docs; we need to decide how flexible we'll make it for customers to define their own categories of benefits for grouping, for example"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Some of the benefits values felt low, so I played with hiding some specific values while still including them in the rolled-up total"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "The 2 employees I mocked up are in different US states, and I would expect the actual value of their benefits & perks to differ, but we didn't get this level of detail from <PERSON><PERSON>."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Our Salary band design doesn't currently include anything to distinguish location/tier that could affect the ranges for employees in the same Job level. "}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1702940080.709829", "text": "<@U065H3M6WJV> I am meeting with <PERSON> in few minutes. Do you have any questions for SDF that you need me to ask her since I am on the call anyway?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "uXU8s", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I am meeting with <PERSON> in few minutes. Do you have any questions for SDF that you need me to ask her since I am on the call anyway?"}]}]}]}, {"ts": "1702940135.069349", "text": "<@U04DS2MBWP4> main questions are about how they want to approach recommendations and prefill", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pNnYu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " main questions are about how they want to approach recommendations and prefill"}]}]}]}, {"ts": "1702940241.990929", "text": "ok", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1702940272.508179", "text": "Also happy to get feedback on Total Rewards mocks (spreadsheet), but that's lower priority and I can do that with <PERSON><PERSON> separately if needed", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wX7ob", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also happy to get feedback on Total Rewards mocks (spreadsheet), but that's lower priority and I can do that with <PERSON><PERSON> separately if needed"}]}]}]}, {"ts": "1702940448.656509", "text": "ok", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1702942582.217649", "text": "<PERSON><PERSON>, had a good call with <PERSON> this afternoon. I had him take a quick look at the <https://docs.google.com/document/d/12Uy1Kwh5NLLbGr3tnl30FZSE4SM_c04bsOazifc6F0c/edit|blog post ideas> and captured his reactions, then had him walk through the current QA environment and give some quick feedback on the HR Admin view and Merit view. Some <https://docs.google.com/document/d/1AisVWa1O8S5e6SjiWwAKecpPEKtm9F2SE0PVZf7lnWM/edit|notes here>.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702942582.217649", "reply_count": 1, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06AKSUDLBT", "created": 1702574493, "timestamp": 1702574493, "name": "Possible blog topics", "title": "Possible blog topics", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "12Uy1Kwh5NLLbGr3tnl30FZSE4SM_c04bsOazifc6F0c", "external_url": "https://docs.google.com/document/d/12Uy1Kwh5NLLbGr3tnl30FZSE4SM_c04bsOazifc6F0c/edit", "url_private": "https://docs.google.com/document/d/12Uy1Kwh5NLLbGr3tnl30FZSE4SM_c04bsOazifc6F0c/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AKSUDLBT-c7bc3321d5/possible_blog_topics_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACXTJxSZpSM9ajxjgD9KAJMj1oyKZ+f5UYPv+VAD6KKKAA9KjOKkqPjPegAwPWnDA/ipPl96UY7E0AOooooAKjJ/zipKTb70AMpQee1Ox7mlFABRRRQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06AKSUDLBT/possible_blog_topics", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}, {"id": "F06AT498FEG", "created": 1702942584, "timestamp": 1702942584, "name": "<PERSON> (Novo Insights)", "title": "<PERSON> (Novo Insights)", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1AisVWa1O8S5e6SjiWwAKecpPEKtm9F2SE0PVZf7lnWM", "external_url": "https://docs.google.com/document/d/1AisVWa1O8S5e6SjiWwAKecpPEKtm9F2SE0PVZf7lnWM/edit", "url_private": "https://docs.google.com/document/d/1AisVWa1O8S5e6SjiWwAKecpPEKtm9F2SE0PVZf7lnWM/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AT498FEG-f2a0caf9e8/paul_reiman__novo_insights__1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACXSJxRn6fnQaTaPQ0ALmlpAoFLgelABRRRQAhpAxpxqPHPNAD8mjJpuB6/rS4Ht+dADqKKKACmHk0+igBgX60uPanUUAFFFFAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06AT498FEG/paul_reiman__novo_insights_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "43l7N", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "BTW, had a good call with <PERSON> this afternoon. I had him take a quick look at the "}, {"type": "link", "url": "https://docs.google.com/document/d/12Uy1Kwh5NLLbGr3tnl30FZSE4SM_c04bsOazifc6F0c/edit", "text": "blog post ideas"}, {"type": "text", "text": " and captured his reactions, then had him walk through the current QA environment and give some quick feedback on the HR Admin view and Merit view. Some "}, {"type": "link", "url": "https://docs.google.com/document/d/1AisVWa1O8S5e6SjiWwAKecpPEKtm9F2SE0PVZf7lnWM/edit", "text": "notes here"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1702951078.017439", "text": "Update on SLAs\n\nAfter going back and forth with legal, we decided to now include SLA since are not including calculations and exceptions of availability and the credit based on availability among other terms. Given the stage of the company, we will just use the following language:\n\n*Support:* If Customer experiences any errors, bugs, or other issues in its use of the Services, Compiify will use commercially reasonable efforts to respond as soon as possible (“Support”) in order to resolve the issue or provide a suitable workaround. The fee for Support is included in the cost of the Subscription set forth on the Order Form. Customer will send any support requests to Compiify via email (to: <mailto:<EMAIL>|<EMAIL>>).", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}, {"name": "+1", "users": ["U065H3M6WJV", "U04DKEFP1K8"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "HMbFx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Update on SLAs\n\nAfter going back and forth with legal, we decided to now include SLA since are not including calculations and exceptions of availability and the credit based on availability among other terms. Given the stage of the company, we will just use the following language:\n\n"}, {"type": "text", "text": "Support:", "style": {"bold": true}}, {"type": "text", "text": " If Customer experiences any errors, bugs, or other issues in its use of the Services, Compiify will use commercially reasonable efforts to respond as soon as possible (“Support”) in order to resolve the issue or provide a suitable workaround. The fee for Support is included in the cost of the Subscription set forth on the Order Form. Customer will send any support requests to Compiify via email (to: "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ")."}]}]}]}, {"ts": "1702994688.159459", "text": "<!here> <http://qa.compiify.com|qa.compiify.com> and <https://dev-app.compiify.com/> will be under maintenance for next 3 hours. Access to the environment will be disrupted. I will send an update when the environments becomes accessible", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1702994688.159459", "reply_count": 5, "edited": {"user": "U04DKEFP1K8", "ts": "1702994742.000000"}, "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "attachments": [{"from_url": "https://dev-app.compiify.com/", "service_icon": "https://dev-app.compiify.com/apple-touch-icon.png", "id": 1, "original_url": "https://dev-app.compiify.com/", "fallback": "Compiify", "text": "Web site created using create-react-app", "title": "Compiify", "title_link": "https://dev-app.compiify.com/", "service_name": "dev-app.compiify.com"}], "blocks": [{"type": "rich_text", "block_id": "qan30", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://qa.compiify.com", "text": "qa.compiify.com"}, {"type": "text", "text": " and "}, {"type": "link", "url": "https://dev-app.compiify.com/"}, {"type": "text", "text": " will be under maintenance for next 3 hours. Access to the environment will be disrupted. I will send an update when the environments becomes accessible"}]}]}]}, {"ts": "1703017721.633069", "text": "Here is the first draft of company level OKRs. Let align all of your OKRs with this objective.\n\n Generate $500K in ARR and set revenue engine in motion\n    ◦ KR1: Achieve product market fit to set a solid foundation for long terms success (product)\n    ◦ KR2: Build a product that customer love and are willing to pay for (product)\n    ◦ KR3: Increase the delivery speed of new features with highest quality (eng)\n    ◦ KR4: Generate $500K in ARR (sales)\n    ◦ KR5: Establish compiify as a recognized and trusted brand (marketing)", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1703017733.000000"}, "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D", "U04DKEFP1K8"], "count": 2}, {"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "NIKS5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the first draft of company level OKRs. Let align all of your OKRs with this objective.\n\n Generate $500K in ARR and set revenue engine in motion\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "KR1: Achieve product market fit to set a solid foundation for long terms success (product)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "KR2: Build a product that customer love and are willing to pay for (product)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "KR3: Increase the delivery speed of new features with highest quality (eng)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "KR4: Generate $500K in ARR (sales)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "KR5: Establish compiify as a recognized and trusted brand (marketing)"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}, {"ts": "1703020624.667309", "text": "This morning's working session, with <PERSON> walking through the comp cycle builder:\n\n<https://us06web.zoom.us/rec/share/j5QcYRsqxC-7Q-8jZImAeC1pSxbKwCbgSIQNwCgd_ojQBymEIL56ZgK5qCxAhh1z.7jN22wdSBrHXeqaw>\nPasscode: *!CMnUG2&amp;*", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nBGAA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This morning's working session, with <PERSON> walking through the comp cycle builder:\n\n"}, {"type": "link", "url": "https://us06web.zoom.us/rec/share/j5QcYRsqxC-7Q-8jZImAeC1pSxbKwCbgSIQNwCgd_ojQBymEIL56ZgK5qCxAhh1z.7jN22wdSBrHXeqaw"}, {"type": "text", "text": "\nPasscode: "}, {"type": "text", "text": "!CMnUG2&", "style": {"bold": true}}]}]}]}, {"ts": "1703033964.481609", "text": "<@U04DS2MBWP4> I have the regularly scheduled call with <PERSON> tomorrow, any specific topics? Otherwise I will probably walk him through the comp builder and get some feedback, since last time I had him react to the merit views.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eYfMy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I have the regularly scheduled call with <PERSON> tomorrow, any specific topics? Otherwise I will probably walk him through the comp builder and get some feedback, since last time I had him react to the merit views."}]}]}]}, {"ts": "1703034947.692999", "text": "yep. I think we should do that for comp builder, admin dashboard, paybands, settings/permissions, offer letters. Then let's dive into reporting &amp; analytics followed by marketing pricing functionality.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2WEdh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yep. I think we should do that for comp builder, admin dashboard, paybands, settings/permissions, offer letters. Then let's dive into reporting & analytics followed by marketing pricing functionality."}]}]}]}, {"ts": "1703038407.310869", "text": "<@U0658EW4B8D> <@U065H3M6WJV> what's your take on if we build a functionality for customers to build comp programs including marketing pricing vs building advanced reporting &amp; analytics? Which is a bigger pain point?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1703038407.310869", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "tvF44", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " what's your take on if we build a functionality for customers to build comp programs including marketing pricing vs building advanced reporting & analytics? Which is a bigger pain point?"}]}]}]}, {"ts": "1703067617.762009", "text": "<!here> is an update on staging outage the day before\n\nOur RDS password has automatic password rotation every 7 days ( implemented after recent pen test)\nOn qa and dev environment code is deployed very frequently and password rotates with every deployment\nbut on staging env upgrade are not that frequent and hence it ran into this issue.\n\nWe will put a fix soon for env like staging or prod which do not get frequent updates and live on a build for more than 7 days", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1703067617.762009", "reply_count": 1, "edited": {"user": "U04DKEFP1K8", "ts": "1703079665.000000"}, "blocks": [{"type": "rich_text", "block_id": "TmQLI", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " is an update on staging outage the day before\n\nOur RDS password has automatic password rotation every 7 days ( implemented after recent pen test)\nOn qa and dev environment code is deployed very frequently and password rotates with every deployment\nbut on staging env upgrade are not that frequent and hence it ran into this issue.\n\nWe will put a fix soon for env like staging or prod which do not get frequent updates and live on a build for more than 7 days"}]}]}]}, {"ts": "1703088946.256579", "text": "<https://docs.google.com/document/d/1sfemzlADx-jKfMZKUjHKdQKvUGCLK7YbtNtTwYO8248/edit|Here> are my Q1 OKRs\nIt might be more efficient to insert your Q1 OKRs in this doc. <@U065H3M6WJV> can add her OKRs under Objective 1 and <@U04DKEFP1K8> can add his OKRs under Objective 2", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F069AP0957G", "created": 1702273069, "timestamp": 1702273069, "name": "OKRs_Kapil v1", "title": "Q1' 24 OKRs", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1sfemzlADx-jKfMZKUjHKdQKvUGCLK7YbtNtTwYO8248", "external_url": "https://docs.google.com/document/d/1sfemzlADx-jKfMZKUjHKdQKvUGCLK7YbtNtTwYO8248/edit", "url_private": "https://docs.google.com/document/d/1sfemzlADx-jKfMZKUjHKdQKvUGCLK7YbtNtTwYO8248/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACXSJxRuHrSnpTN6/wB79aTaW4Dtw9aNw9aQMp6E0ZHqaLoB1FFFMAPTmo9qH/8AVUlQupLfKxFJ6CY/Yvp+lKFUHgVHsfvIacFII+Y0k/ICSiiiqGFMYjPJxT6QoCckD8qAGZB6NTx75pNi+g/KnYFABRRRQB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F069AP0957G/okrs_kapil_v1", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "zplPn", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/document/d/1sfemzlADx-jKfMZKUjHKdQKvUGCLK7YbtNtTwYO8248/edit", "text": "Here"}, {"type": "text", "text": " are my Q1 OKRs\nIt might be more efficient to insert your Q1 OKRs in this doc. "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " can add her OKRs under Objective 1 and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can add his OKRs under Objective 2"}]}]}]}, {"ts": "1703095281.554799", "text": "We'll start the working session maybe ~5 min late ... need a break first :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rkc1O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We'll start the working session maybe ~5 min late ... need a break first "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1703096152.079709", "text": "<https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/ref=sr_1_2_sspa?hvadid=616859350494&amp;hvdev=c&amp;hvlocphy=9032306&amp;hvnetw=g&amp;hvqmt=e&amp;hvrand=2955047059185648970&amp;hvtargid=kwd-66599339680&amp;hydadcr=9407_13604228&amp;keywords=lifestraw%2Bwater%2Bbottle&amp;qid=1703096130&amp;sr=8-2-spons&amp;sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&amp;th=1|https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/[…]d=1703096130&amp;sr=8-2-spons&amp;sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&amp;th=1>", "user": "U0658EW4B8D", "type": "message", "attachments": [{"from_url": "https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/ref=sr_1_2_sspa?hvadid=616859350494&hvdev=c&hvlocphy=9032306&hvnetw=g&hvqmt=e&hvrand=2955047059185648970&hvtargid=kwd-66599339680&hydadcr=9407_13604228&keywords=lifestraw%2Bwater%2Bbottle&qid=1703096130&sr=8-2-spons&sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&th=1", "service_icon": "https://www.amazon.com/favicon.ico", "id": 1, "original_url": "https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/ref=sr_1_2_sspa?hvadid=616859350494&amp;hvdev=c&amp;hvlocphy=9032306&amp;hvnetw=g&amp;hvqmt=e&amp;hvrand=2955047059185648970&amp;hvtargid=kwd-66599339680&amp;hydadcr=9407_13604228&amp;keywords=lifestraw%2Bwater%2Bbottle&amp;qid=1703096130&amp;sr=8-2-spons&amp;sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&amp;th=1", "fallback": "Amazon.com : LifeStraw Go Series – Insulated Stainless Steel Water Filter Bottle for Travel and Everyday use removes Bacteria, parasites and microplastics, Improves Taste, 24oz Icelandic Blue : Sports &amp; Outdoors", "text": "<http://Amazon.com|Amazon.com> : LifeStraw Go Series – Insulated Stainless Steel Water Filter Bottle for Travel and Everyday use removes Bacteria, parasites and microplastics, Improves Taste, 24oz Icelandic Blue : Sports &amp; Outdoors", "title": "Amazon.com : LifeStraw Go Series – Insulated Stainless Steel Water Filter Bottle for Travel and Everyday use removes Bacteria, parasites and microplastics, Improves Taste, 24oz Icelandic Blue : Sports &amp; Outdoors", "title_link": "https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/ref=sr_1_2_sspa?hvadid=616859350494&hvdev=c&hvlocphy=9032306&hvnetw=g&hvqmt=e&hvrand=2955047059185648970&hvtargid=kwd-66599339680&hydadcr=9407_13604228&keywords=lifestraw%2Bwater%2Bbottle&qid=1703096130&sr=8-2-spons&sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&th=1", "service_name": "amazon.com"}], "blocks": [{"type": "rich_text", "block_id": "ltQz7", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/ref=sr_1_2_sspa?hvadid=616859350494&hvdev=c&hvlocphy=9032306&hvnetw=g&hvqmt=e&hvrand=2955047059185648970&hvtargid=kwd-66599339680&hydadcr=9407_13604228&keywords=lifestraw%2Bwater%2Bbottle&qid=1703096130&sr=8-2-spons&sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&th=1", "text": "https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/[…]d=1703096130&sr=8-2-spons&sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&th=1"}]}]}]}, {"ts": "1703098726.078779", "text": "next meeting 11:10", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "f41VS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "next meeting 11:10"}]}]}]}, {"ts": "1703107312.034799", "text": "For today's call with <PERSON>, I decided to use the time getting his <https://www.figma.com/file/2caUVa1XZJ7cuxSJ9HHK9v/Annual-patterns-in-Comp%2FHR?type=whiteboard&amp;node-id=0-1&amp;t=qh8OQNazM5yz6xkh-0|map of the comp \"calendar\"> from his previous role(s), because I'm noticing how our customers &amp; advisors are trying to juggle comp management with other time-sensitive priorities.\n\nIt also helps us understand how often different members of the org will be interacting with our tools depending on which \"jobs\" we are helping them get done; I captured those at a high level in the categories of <PERSON>'s <https://docs.google.com/presentation/d/1i1ULo4tA3vVK0h--k1MV2c_J7kmZwGhJpR5AX8JBEPM/edit#slide=id.g2a42f70c363_0_116|HR landscape here> (with his input).", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1703107312.034799", "reply_count": 4, "edited": {"user": "U065H3M6WJV", "ts": "1703107322.000000"}, "blocks": [{"type": "rich_text", "block_id": "/HTuA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For today's call with <PERSON>, I decided to use the time getting his "}, {"type": "link", "url": "https://www.figma.com/file/2caUVa1XZJ7cuxSJ9HHK9v/Annual-patterns-in-Comp%2FHR?type=whiteboard&node-id=0-1&t=qh8OQNazM5yz6xkh-0", "text": "map of the comp \"calendar\""}, {"type": "text", "text": " from his previous role(s), because I'm noticing how our customers & advisors are trying to juggle comp management with other time-sensitive priorities.\n\nIt also helps us understand how often different members of the org will be interacting with our tools depending on which \"jobs\" we are helping them get done; I captured those at a high level in the categories of <PERSON>'s "}, {"type": "link", "url": "https://docs.google.com/presentation/d/1i1ULo4tA3vVK0h--k1MV2c_J7kmZwGhJpR5AX8JBEPM/edit#slide=id.g2a42f70c363_0_116", "text": "HR landscape here"}, {"type": "text", "text": " (with his input)."}]}]}]}, {"ts": "1703108014.287159", "text": "BTW <@U04DS2MBWP4> if we want to try Zoom's AI auto-transcript/analysis as an alternative to Otter I think you have to enable a setting as the admin", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1703108014.287159", "reply_count": 19, "files": [{"id": "F06B2PYD335", "created": 1703108010, "timestamp": 1703108010, "name": "Screenshot 2023-12-20 at 1.32.13 PM.png", "title": "Screenshot 2023-12-20 at 1.32.13 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 430544, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06B2PYD335/screenshot_2023-12-20_at_1.32.13___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06B2PYD335/download/screenshot_2023-12-20_at_1.32.13___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 186, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 248, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 372, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 413, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 496, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B2PYD335-9644b98044/screenshot_2023-12-20_at_1.32.13___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 529, "original_w": 3210, "original_h": 1658, "thumb_tiny": "AwAYADDSxSD6U6k5oATj0pQAe1HPtS/WgAooooAKbg+tOooATn2ox60tFABRRRQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06B2PYD335/screenshot_2023-12-20_at_1.32.13___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06B2PYD335-aad1b45a26", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "e1khM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "BTW "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " if we want to try Zoom's AI auto-transcript/analysis as an alternative to O<PERSON> I think you have to enable a setting as the admin"}]}]}]}, {"ts": "1703111459.568209", "text": "<@U065H3M6WJV> In Meati's call today, they said there is no tool available for companies of their size (100 to 400 emp). A tool which is easier to implement and affordable. This could be a white space we need", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1703111459.568209", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "OKJPO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " In <PERSON><PERSON>'s call today, they said there is no tool available for companies of their size (100 to 400 emp). A tool which is easier to implement and affordable. This could be a white space we need"}]}]}]}, {"ts": "**********.599339", "text": "<@U0658EW4B8D> if we have access to Radford and Options Impact thru customer, how long would it take to create salary bands for them if they don't have salary bands to begin with?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.599339", "reply_count": 16, "blocks": [{"type": "rich_text", "block_id": "Q5GLe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " if we have access to Radford and Options Impact thru customer, how long would it take to create salary bands for them if they don't have salary bands to begin with?"}]}]}]}, {"ts": "**********.212759", "text": "<@U04DKEFP1K8> do we have google analytics account for the website?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.212759", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "sxcse", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " do we have google analytics account for the website?"}]}]}]}, {"ts": "**********.470439", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> For tomorrow's product demos with Neuroflow and Meati:\n1. The <http://qa.compiify.com|qa.compiify.com> environment is giving an internal error when I attempt to log in as <mailto:<EMAIL>|<EMAIL>>. \n2. I can log in to <http://staging.compiify.com|staging.compiify.com> with <mailto:<EMAIL>|<EMAIL>> and a dummy password, but \n    ◦ Can't demo Google login there\n    ◦ Can't create a new comp cycle (currently hitting an \"Unexpected Application Error\")\n3. I can log in to <http://dev-app.compiify.com|dev-app.compiify.com> using Google login for <mailto:<EMAIL>|<EMAIL>>. This one has an open cycle with a few recommendations already filled in.\n    ◦ Can we expect this to be stable tomorrow for 2 different demos?\n    ◦ Does anything need to be different in the Merit recommendations so that the demo shows what we want to highlight?\n    ◦ Is it okay if we do not demo a real \"cycle creation\"? (You can probably walk through some of the steps, but cannot create a new cycle without closing the old one, and that might not work smoothly.)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.470439", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "wyxVv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For tomorrow's product demos with Neuroflow and Meati:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The "}, {"type": "link", "url": "http://qa.compiify.com", "text": "qa.compiify.com"}, {"type": "text", "text": " environment is giving an internal error when I attempt to log in as "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ". "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "I can log in to "}, {"type": "link", "url": "http://staging.compiify.com", "text": "staging.compiify.com"}, {"type": "text", "text": " with "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " and a dummy password, but "}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can't demo Google login there"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Can't create a new comp cycle (currently hitting an \"Unexpected Application Error\")"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can log in to "}, {"type": "link", "url": "http://dev-app.compiify.com", "text": "dev-app.compiify.com"}, {"type": "text", "text": " using Google login for "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ". This one has an open cycle with a few recommendations already filled in."}]}], "style": "ordered", "indent": 0, "offset": 2, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can we expect this to be stable tomorrow for 2 different demos?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Does anything need to be different in the Merit recommendations so that the demo shows what we want to highlight?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Is it okay if we do not demo a real \"cycle creation\"? (You can probably walk through some of the steps, but cannot create a new cycle without closing the old one, and that might not work smoothly.)"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}, {"ts": "1703122684.522409", "text": "Update on DA UAT: I cannot successfully create a new cycle; I keep hitting a blocking error on the \"Bonus Planning\" step. :disappointed:\n\nI logged the ~10 issues that I saw in my testing in the <https://compiify.atlassian.net/browse/COM-1859|COM-1859> ticket for engineering.", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F06B0QMP0PP", "created": 1703122574, "timestamp": 1703122574, "name": "Screenshot 2023-12-20 at 5.35.22 PM.png", "title": "Screenshot 2023-12-20 at 5.35.22 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 347282, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06B0QMP0PP/screenshot_2023-12-20_at_5.35.22___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06B0QMP0PP/download/screenshot_2023-12-20_at_5.35.22___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 250, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 333, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 500, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 556, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 667, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B0QMP0PP-68fa459e97/screenshot_2023-12-20_at_5.35.22___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 711, "original_w": 3834, "original_h": 2663, "thumb_tiny": "AwAhADDRwDRgA0o5FLQAnFHHtS0UAJgelGKWigAFFFFABRRRQAUUUUAAooFFABRRRQAUUUUAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06B0QMP0PP/screenshot_2023-12-20_at_5.35.22___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06B0QMP0PP-56a38f8c96", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:11920::965c81a09fa111eea9aa2f40127b488a", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-1859?atlOrigin=eyJpIjoiZGIwMWNmNzE1YzZhNGU5M2E4ODQ1YjJiMWNmOTE2ZGMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-1859 Digital Asset UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:11920::965c81a29fa111eea9aa2f40127b488a", "elements": [{"type": "mrkdwn", "text": "Status: *In Progress*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:11920::965c81a19fa111eea9aa2f40127b488a", "elements": [{"type": "button", "action_id": "{\"issueId\":\"11920\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"11920\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-1859", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "t9ZlS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Update on DA UAT: I cannot successfully create a new cycle; I keep hitting a blocking error on the \"Bonus Planning\" step. "}, {"type": "emoji", "name": "disappointed", "unicode": "1f61e"}, {"type": "text", "text": "\n\nI logged the ~10 issues that I saw in my testing in the "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1859", "text": "COM-1859"}, {"type": "text", "text": " ticket for engineering."}]}]}]}, {"ts": "1703122752.191789", "text": "This is a big bummer. <@U04DKEFP1K8> this is your TOP priority", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wvAvc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is a big bummer. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " this is your TOP priority"}]}]}]}, {"ts": "1703123126.071739", "text": "<@U04DKEFP1K8> pls call me when you are up.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1703123126.071739", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "qRTqQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " pls call me when you are up."}]}]}]}, {"ts": "1703129055.408379", "text": "Looks like we had a partial cycle setup so I just \"launched\" it. May not get us the perfect setup but we can at least test <!here>", "user": "U0658EW4B8D", "type": "message", "thread_ts": "1703129055.408379", "reply_count": 4, "reactions": [{"name": "wow", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "VxrQw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Looks like we had a partial cycle setup so I just \"launched\" it. May not get us the perfect setup but we can at least test "}, {"type": "broadcast", "range": "here"}]}]}]}, {"ts": "1703130302.091569", "text": "<!here> guys i am available now. Can we jump on a call", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "tDhLN", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " guys i am available now. Can we jump on a call"}]}]}]}, {"ts": "1703130510.676649", "text": "<PERSON><PERSON><PERSON> has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R06BGNLFA8Z", "block_id": "8L1+Y", "api_decoration_available": false, "call": {"v1": {"id": "R06BGNLFA8Z", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1703130510, "active_participants": [], "all_participants": [{"slack_id": "U065H3M6WJV"}, {"slack_id": "U04DKEFP1K8"}, {"slack_id": "U04DS2MBWP4"}], "display_id": "813-1205-5458", "join_url": "https://us06web.zoom.us/j/81312055458?pwd=G3PG2TefdqhPq9w2TX6roantyDc5Bq.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEXzUwZmRmZjM4NTNkNjQzOGQ4YTI3OTk1YmE2ODliZjE3JnVzcz1xajRKMGl5aG9BNUE0QlRDZUhSZjFiYzVVMGhXT3JDSHJsdEV3OXAyM3pwN3lkaUZqRnYxR3p3LXZnUU5VbDVpa194YjZoNHpVY09Rd3Bib001MFlZMW9oT205VjZEMllVVTBUcFl3LnFGc21fOVlKLUh4bGFINUY%3D&action=join&confno=81312055458&pwd=G3PG2TefdqhPq9w2TX6roantyDc5Bq.1", "name": "Zoom meeting started by <PERSON>", "created_by": "U05185RFCNT", "date_end": 1703132910, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "m+hvO", "text": {"type": "mrkdwn", "text": "Meeting passcode: G3PG2TefdqhPq9w2TX6roantyDc5Bq.1", "verbatim": false}}]}, {"ts": "1703130514.951529", "text": "<@U04DKEFP1K8> ^", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qZ4AH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " ^"}]}]}]}, {"ts": "1703136218.854179", "text": "In my testing the biggest issue I've experienced that is more of a usability thing than anything (and perhaps my lack of understanding on the tool) is that it is very difficult for an admin, like <PERSON><PERSON><PERSON>, to see a single manager and their view. <PERSON><PERSON> doesn't work on the Organization page (logged in Ji<PERSON>) and Manager view / Direct Reports really doesn't address the issue at all. We can iterate the tool but long term I think this is a problem we need to solve. Additionally, from what I can tell, <PERSON><PERSON><PERSON> will be unable to modify budget for anyone but her own hierarchy which is incorrect IMO. I will do more testing in the AM but this is my biggest thought from what I reviewed tonight", "user": "U0658EW4B8D", "type": "message", "thread_ts": "1703136218.854179", "reply_count": 2, "reactions": [{"name": "face_with_monocle", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "aXeEe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In my testing the biggest issue I've experienced that is more of a usability thing than anything (and perhaps my lack of understanding on the tool) is that it is very difficult for an admin, like <PERSON><PERSON><PERSON>, to see a single manager and their view. <PERSON><PERSON> doesn't work on the Organization page (logged in Ji<PERSON>) and Manager view / Direct Reports really doesn't address the issue at all. We can iterate the tool but long term I think this is a problem we need to solve. Additionally, from what I can tell, <PERSON><PERSON><PERSON> will be unable to modify budget for anyone but her own hierarchy which is incorrect IMO. I will do more testing in the AM but this is my biggest thought from what I reviewed tonight"}]}]}]}, {"ts": "1703171268.542169", "text": "<!here> <http://staging.compiify.com|staging.compiify.com> is updated with latest build. <@U065H3M6WJV> <@U04DS2MBWP4> please check and confirm the environment looks good. <@U065H3M6WJV> google auth is enabled as well on the environment ( email: <mailto:<EMAIL>|<EMAIL>>)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "SEX/p", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://staging.compiify.com", "text": "staging.compiify.com"}, {"type": "text", "text": " is updated with latest build. "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " please check and confirm the environment looks good. "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " google auth is enabled as well on the environment ( email: "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ")"}]}]}]}, {"ts": "1703176277.489689", "text": "Moved our working session by 30min to allow for the compliance call y'all have, and I'll use those 30 min to sanity check the environments before we're on a group call", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hRtxY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Moved our working session by 30min to allow for the compliance call y'all have, and I'll use those 30 min to sanity check the environments before we're on a group call"}]}]}]}, {"ts": "1703176452.716649", "text": "Adding some subtasks in Jira this AM", "user": "U0658EW4B8D", "type": "message", "edited": {"user": "U0658EW4B8D", "ts": "1703176475.000000"}, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}, {"name": "meow_thx", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "dP37K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Adding some subtasks in Jira this AM"}]}]}]}, {"ts": "1703176601.797219", "text": "I think that security/compliance call can be pushed. I don't think there is any rush or time sensitive deadline we have for security/compliance", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1703176601.797219", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "xzhAC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think that security/compliance call can be pushed. I don't think there is any rush or time sensitive deadline we have for security/compliance"}]}]}]}, {"ts": "1703192428.507109", "text": "Who's got the Promotion template CSV/XLSX file?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EBCLX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Who's got the Promotion template CSV/XLSX file?"}]}]}]}, {"ts": "1703192620.326569", "text": "<@U04DS2MBWP4> Is <https://drive.google.com/file/d/1LO-5QRTntGFl7AxbL9CFhv1bpI0tETdo/view?usp=drive_link|this the correct file> to send customers as our SOC 2 report?", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F06B8M9KZ2P", "created": 1703192624, "timestamp": 1703192624, "name": "COMPIIFY SOC 2 Type 2 Report.pdf", "title": "COMPIIFY SOC 2 Type 2 Report.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 162683, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1LO-5QRTntGFl7AxbL9CFhv1bpI0tETdo", "external_url": "https://drive.google.com/file/d/1LO-5QRTntGFl7AxbL9CFhv1bpI0tETdo/view?usp=drive_link", "url_private": "https://drive.google.com/file/d/1LO-5QRTntGFl7AxbL9CFhv1bpI0tETdo/view?usp=drive_link", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_720.png", "thumb_720_w": 556, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1325, "thumb_tiny": "AwAwACXQMiAkFhkdaPNj/vr+dKQM9BRgeg/KjQADBhkHNLR+VFABRRRQA0rk9R+VG0eo/KkZcsfl/HNGz2H5mlYdxdo9R+VKAB6U0Jj+Ffxpw4HQA+1FguLRRRTEISc0ZpaKAEBpaKKACiijNAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06B8M9KZ2P/compiify_soc_2_type_2_report.pdf", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "esRyl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Is "}, {"type": "link", "url": "https://drive.google.com/file/d/1LO-5QRTntGFl7AxbL9CFhv1bpI0tETdo/view?usp=drive_link", "text": "this the correct file"}, {"type": "text", "text": " to send customers as our SOC 2 report?"}]}]}]}, {"ts": "1703192666.463899", "text": "yes", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IB8ze", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes"}]}]}]}, {"ts": "1703192701.427219", "text": "<@U04DKEFP1K8> has  the Promotion template CSV/XLSX file", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HNvIH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " has  the Promotion template CSV/XLSX file"}]}]}]}, {"ts": "1703195393.496849", "text": "<PERSON><PERSON> know if you have difficulty downloading", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RSBlf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON> know if you have difficulty downloading"}]}]}]}, {"ts": "1703195922.283379", "text": "Got it, thanks <@U0658EW4B8D>! I've added this to our standard templates and made a copy for our newest potential beta customer (Neuroflow) :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "DFVLy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Got it, thanks "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": "! I've added this to our standard templates and made a copy for our newest potential beta customer (Neuroflow) "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1703198679.212559", "text": "And now, because this morning's Frozen parody idea is still stuck in my head, I finished the verse :laughing:\n\n:musical_note: :snowflake: :snowman: :snowflake: :musical_note:\n_Do you want to build a budget?_\n_How much money will you need?_\n_You can upload your employee file,_\n_Then sit back a while,_ \n_Compiify will take the leeeeeead...._", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "grinning", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Zj32J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And now, because this morning's Frozen parody idea is still stuck in my head, I finished the verse "}, {"type": "emoji", "name": "laughing", "unicode": "1f606"}, {"type": "text", "text": "\n\n"}, {"type": "emoji", "name": "musical_note", "unicode": "1f3b5"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "snowflake", "unicode": "2744-fe0f"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "snowman", "unicode": "2603-fe0f"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "snowflake", "unicode": "2744-fe0f"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "musical_note", "unicode": "1f3b5"}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Do you want to build a budget?", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "How much money will you need?", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "You can upload your employee file,", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Then sit back a while, ", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Compiify will take the leeeeeead....", "style": {"italic": true}}]}]}]}, {"ts": "1703200870.212329", "text": "_Do you want to build a cycle?_\n_There is nothing that we lack_\n_We can plan for Salary_\n_And even Equity_\n_Compiify's got your baaaaaack!_", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}, {"name": "grinning", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "HhhKT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you want to build a cycle?", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "There is nothing that we lack", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "We can plan for Sal<PERSON>", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "And even Equity", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Compiify's got your baaaaaack!", "style": {"italic": true}}]}]}]}, {"ts": "1703201356.216549", "text": "Do you need to align pay and role?\nWith Compiify, you take full control.\nFrom salary to bonus to equity, it's a knack,\nCompiify's will get you on track, you're never looking back!", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "thinking_face", "users": ["U065H3M6WJV"], "count": 1}, {"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "9dAlT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you need to align pay and role?\nWith Compiify, you take full control.\nFrom salary to bonus to equity, it's a knack,\nCompiify's will get you on track, you're never looking back!"}]}]}]}, {"ts": "1703201957.358189", "text": "Trying to figure out whether <PERSON><PERSON><PERSON> has heard the Frozen soundtrack as much as the rest of us... :zany_face: :joy:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0EOkQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Trying to figure out whether <PERSON><PERSON><PERSON> has heard the Frozen soundtrack as much as the rest of us... "}, {"type": "emoji", "name": "zany_face", "unicode": "1f92a"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "joy", "unicode": "1f602"}]}]}]}, {"ts": "1703201980.900429", "text": "(It rhymes, but doesn't _quite_ fit the tune -- <PERSON> wins the matchup!)", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "joy", "users": ["U04DS2MBWP4", "U0658EW4B8D"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "QPJCb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "(It rhymes, but doesn't "}, {"type": "text", "text": "quite", "style": {"italic": true}}, {"type": "text", "text": " fit the tune -- <PERSON> wins the matchup!)"}]}]}]}, {"ts": "1703201989.123149", "text": "(That's 2 for <PERSON> today! :joy: )", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2BCEw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "(That's 2 for <PERSON> today! "}, {"type": "emoji", "name": "joy", "unicode": "1f602"}, {"type": "text", "text": " )"}]}]}]}, {"ts": "1703202980.256099", "text": "Ah, I have heard the track but did not realize that it had to fit the tune :joy: ", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1703203020.000000"}, "blocks": [{"type": "rich_text", "block_id": "jG0v+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ah, I have heard the track but did not realize that it had to fit the tune "}, {"type": "emoji", "name": "joy", "unicode": "1f602"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1703206346.448719", "text": "new analytics dashboard coming together", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "aaaaaa", "users": ["U065H3M6WJV"], "count": 1}, {"name": "rolling_on_the_floor_laughing", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F06BC1YL2BW", "created": 1703206335, "timestamp": 1703206335, "name": "Screenshot 2023-12-21 at 4.52.12 PM.png", "title": "Screenshot 2023-12-21 at 4.52.12 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 77901, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06BC1YL2BW/screenshot_2023-12-21_at_4.52.12_pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06BC1YL2BW/download/screenshot_2023-12-21_at_4.52.12_pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_360.png", "thumb_360_w": 360, "thumb_360_h": 224, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_480.png", "thumb_480_w": 480, "thumb_480_h": 298, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_720.png", "thumb_720_w": 720, "thumb_720_h": 448, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_800.png", "thumb_800_w": 800, "thumb_800_h": 497, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_960.png", "thumb_960_w": 960, "thumb_960_h": 597, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 637, "original_w": 1264, "original_h": 786, "thumb_tiny": "AwAdADDRY4ANKCD0oYZU1HgADrmmBIpzn6012wcCo1cqTj9aUkE+/enYCUcjNLTYz8uPSnVICYzSBQPWlooAhwKMe5qXaPSjaPSncCMZHQmnKSW5NO2j0owOwouB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06BC1YL2BW/screenshot_2023-12-21_at_4.52.12_pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06BC1YL2BW-1d23a2e252", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "7fsvc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "new analytics dashboard coming together"}]}]}]}, {"ts": "1703206366.131979", "text": "<PERSON><PERSON>", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Mu001", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON>"}]}]}]}, {"ts": "1703262892.979399", "text": "<@U065H3M6WJV> we can use this document for auth discussion later in the day <https://docs.google.com/document/d/13k3u2jMsN7nUZ_-4kKir_wcu6Ac-0_NKv5mEWUZDYJU/edit>", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "gratitude-thank-you", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "b7A17", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " we can use this document for auth discussion later in the day "}, {"type": "link", "url": "https://docs.google.com/document/d/13k3u2jMsN7nUZ_-4kKir_wcu6Ac-0_NKv5mEWUZDYJU/edit"}]}]}]}, {"ts": "1703285449.814759", "text": "Zoom AI's understanding of \"work-related topics\" has evolved... This could be dangerous. :joy:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1703285449.814759", "reply_count": 2, "files": [{"id": "F06AZFFNY07", "created": 1703285320, "timestamp": 1703285320, "name": "Screenshot 2023-12-22 at 2.46.59 PM.png", "title": "Screenshot 2023-12-22 at 2.46.59 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 154948, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06AZFFNY07/screenshot_2023-12-22_at_2.46.59___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06AZFFNY07/download/screenshot_2023-12-22_at_2.46.59___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AZFFNY07-0b75afd5bf/screenshot_2023-12-22_at_2.46.59___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AZFFNY07-0b75afd5bf/screenshot_2023-12-22_at_2.46.59___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AZFFNY07-0b75afd5bf/screenshot_2023-12-22_at_2.46.59___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 188, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AZFFNY07-0b75afd5bf/screenshot_2023-12-22_at_2.46.59___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 250, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AZFFNY07-0b75afd5bf/screenshot_2023-12-22_at_2.46.59___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AZFFNY07-0b75afd5bf/screenshot_2023-12-22_at_2.46.59___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 375, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AZFFNY07-0b75afd5bf/screenshot_2023-12-22_at_2.46.59___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 417, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AZFFNY07-0b75afd5bf/screenshot_2023-12-22_at_2.46.59___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 501, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06AZFFNY07-0b75afd5bf/screenshot_2023-12-22_at_2.46.59___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 534, "original_w": 1120, "original_h": 584, "thumb_tiny": "AwAZADC+UbJIP6mjY2eD+ppD948D8qXcQODxQAbHzw36mnBOOSc/Wm7j60bm9aAHBcHq34mnVHub1pykk9aAGH7xoAJ6VLRQBHtPpRtPpUlFAEe1vSnICBzTqKAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06AZFFNY07/screenshot_2023-12-22_at_2.46.59___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06AZFFNY07-1c001b9ea7", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "0B/Qi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Zoom AI's understanding of \"work-related topics\" has evolved... This could be dangerous. "}, {"type": "emoji", "name": "joy", "unicode": "1f602"}]}]}]}, {"ts": "1703693255.102529", "text": "<!here> Team dev-app, qa and de-test environment are going under maintenance for next 1.5 hours", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1703693255.102529", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ANKPG", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Team dev-app, qa and de-test environment are going under maintenance for next 1.5 hours"}]}]}]}, {"ts": "1703695160.626749", "text": "<@U04DKEFP1K8> how is the SDF data upload coming along?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1703695160.626749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Fdaw2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " how is the SDF data upload coming along?"}]}]}]}, {"ts": "1703702627.800449", "text": "All the maintenance on dev-app, qa, da-test environment is now complete. Additional a new env sdf-test is now available with sdf's data", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1703693255.102529", "subtype": "thread_broadcast", "reactions": [{"name": "catjam", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "N3EKO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All the maintenance on dev-app, qa, da-test environment is now complete. Additional a new env sdf-test is now available with sdf's data"}]}]}]}, {"ts": "1703792909.711279", "text": "<!here> DA (<https://da-test.compiify.com>) and SDF(<https://sdf-test.compiify.com>) environment are now only accessible via google auth login. Please find the details below to login\n\n<https://docs.google.com/document/d/1it4-9IEwBbS0izBRJKJp-h13bHAt_QJP2E7vUqeRAP4/edit>\n\nNote: It is recommended to clear cookies and site data for <http://sdf-test.compiify.com|sdf-test.compiify.com> and <http://da-test.compiify.com|da-test.compiify.com> in case you notice login page taking longer to load the first time", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1703792909.711279", "reply_count": 5, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}, {"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F06CHSELR5E", "created": 1703792881, "timestamp": 1703792881, "name": "Screenshot 2023-12-29 at 1.15.34 AM.png", "title": "Screenshot 2023-12-29 at 1.15.34 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 196339, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06CHSELR5E/screenshot_2023-12-29_at_1.15.34_am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06CHSELR5E/download/screenshot_2023-12-29_at_1.15.34_am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHSELR5E-2e29aa2b61/screenshot_2023-12-29_at_1.15.34_am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHSELR5E-2e29aa2b61/screenshot_2023-12-29_at_1.15.34_am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHSELR5E-2e29aa2b61/screenshot_2023-12-29_at_1.15.34_am_360.png", "thumb_360_w": 360, "thumb_360_h": 253, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHSELR5E-2e29aa2b61/screenshot_2023-12-29_at_1.15.34_am_480.png", "thumb_480_w": 480, "thumb_480_h": 337, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHSELR5E-2e29aa2b61/screenshot_2023-12-29_at_1.15.34_am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHSELR5E-2e29aa2b61/screenshot_2023-12-29_at_1.15.34_am_720.png", "thumb_720_w": 720, "thumb_720_h": 505, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06CHSELR5E-2e29aa2b61/screenshot_2023-12-29_at_1.15.34_am_800.png", "thumb_800_w": 800, "thumb_800_h": 561, "original_w": 918, "original_h": 644, "thumb_tiny": "AwAhADDSpG6feIxzS0h79OnegBvH/PT9aOP+en604k56rRnnqtAAFPrmjHvTqQ0AFBzzjGcd6KD70AIQc9FpQOOQM03HP3j+tKOO/wCdADqQ5oB9f5UcZ4zQAUUUUAFFFFABRRRQB//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06CHSELR5E/screenshot_2023-12-29_at_1.15.34_am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06CHSELR5E-9830c04182", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "attachments": [{"from_url": "https://da-test.compiify.com/", "service_icon": "https://da-test.compiify.com/apple-touch-icon.png", "id": 1, "original_url": "https://da-test.compiify.com", "fallback": "Compiify", "text": "Web site created using create-react-app", "title": "Compiify", "title_link": "https://da-test.compiify.com/", "service_name": "da-test.compiify.com"}, {"from_url": "https://sdf-test.compiify.com/", "service_icon": "https://sdf-test.compiify.com/apple-touch-icon.png", "id": 2, "original_url": "https://sdf-test.compiify.com", "fallback": "Compiify", "text": "Web site created using create-react-app", "title": "Compiify", "title_link": "https://sdf-test.compiify.com/", "service_name": "sdf-test.compiify.com"}], "blocks": [{"type": "rich_text", "block_id": "SKrwt", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " DA ("}, {"type": "link", "url": "https://da-test.compiify.com"}, {"type": "text", "text": ") and SDF("}, {"type": "link", "url": "https://sdf-test.compiify.com"}, {"type": "text", "text": ") environment are now only accessible via google auth login. Please find the details below to login\n\n"}, {"type": "link", "url": "https://docs.google.com/document/d/1it4-9IEwBbS0izBRJKJp-h13bHAt_QJP2E7vUqeRAP4/edit"}, {"type": "text", "text": "\n\nNote: It is recommended to clear cookies and site data for "}, {"type": "link", "url": "http://sdf-test.compiify.com", "text": "sdf-test.compiify.com"}, {"type": "text", "text": " and "}, {"type": "link", "url": "http://da-test.compiify.com", "text": "da-test.compiify.com"}, {"type": "text", "text": " in case you notice login page taking longer to load the first time"}]}]}]}, {"ts": "1704001019.115099", "text": "Update on bugs from UAT: I've created new epics to help organize these issues into \"waves\" so that it's easier to know which issues should come first, next, etc.\n\n• <https://compiify.atlassian.net/browse/COM-2084|COM-2084: Showstoppers> (currently 4 issues)\n• <https://compiify.atlassian.net/browse/COM-2085|COM-2085: Wave 1> (currently 21 issues)\n• <https://compiify.atlassian.net/browse/COM-2086|COM-2086: Wave 2> (currently 22 issues)\n• <https://compiify.atlassian.net/browse/COM-2087|COM-2087: Wave 3> (currently 23 issues)\n• <https://compiify.atlassian.net/browse/COM-2088|COM-2088: Wave 4> (currently 29 issues)\nIn order to get these done in time for the DA/SDF comp cycles, we'll need to target to complete each wave within 2 or 3 days, so hopefully <@U04DKEFP1K8> you're able to set that expectation with the team. It's a lot to get done but with some momentum, I'm sure we can make great progress. :meow_code:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1704001019.115099", "reply_count": 2, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4", "U04DKEFP1K8"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "xx6m8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Update on bugs from UAT: I've created new epics to help organize these issues into \"waves\" so that it's easier to know which issues should come first, next, etc.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2084", "text": "COM-2084: Showstoppers"}, {"type": "text", "text": " (currently 4 issues)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2085", "text": "COM-2085: Wave 1"}, {"type": "text", "text": " (currently 21 issues)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2086", "text": "COM-2086: Wave 2"}, {"type": "text", "text": " (currently 22 issues)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "COM-2087: Wave 3"}, {"type": "text", "text": " (currently 23 issues)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2088", "text": "COM-2088: Wave 4"}, {"type": "text", "text": " (currently 29 issues)"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nIn order to get these done in time for the DA/SDF comp cycles, we'll need to target to complete each wave within 2 or 3 days, so hopefully "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " you're able to set that expectation with the team. It's a lot to get done but with some momentum, I'm sure we can make great progress. "}, {"type": "emoji", "name": "meow_code"}]}]}]}]}