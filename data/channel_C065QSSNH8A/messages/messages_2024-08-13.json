{"date": "2024-08-13", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1723571061.721049", "text": "We need to establish the Rippling integration. PlayQ would like to leverage it, but there is a specific process through Merge: <https://help.merge.dev/en/articles/9265388-how-can-i-access-the-rippling-integration> Who should own this?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723571061.721049", "reply_count": 1, "attachments": [{"image_url": "https://downloads.intercomcdn.com/i/o/379082/107841514fe57a3f0b2a855a/065e08fde79f8a1ee25eb6318655bdc9.jpg", "image_width": 1200, "image_height": 628, "image_bytes": 74161, "from_url": "https://help.merge.dev/en/articles/9265388-how-can-i-access-the-rippling-integration", "id": 1, "original_url": "https://help.merge.dev/en/articles/9265388-how-can-i-access-the-rippling-integration", "fallback": "How can I access the Rippling integration? | Merge Help Center", "text": "An overview of <PERSON><PERSON>’s partnership with R<PERSON><PERSON> and instructions on how to access the integration", "title": "How can I access the Rippling integration? | Merge Help Center", "title_link": "https://help.merge.dev/en/articles/9265388-how-can-i-access-the-rippling-integration", "service_name": "help.merge.dev"}], "blocks": [{"type": "rich_text", "block_id": "m0GJG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We need to establish the Rippling integration. PlayQ would like to leverage it, but there is a specific process through Merge: "}, {"type": "link", "url": "https://help.merge.dev/en/articles/9265388-how-can-i-access-the-rippling-integration"}, {"type": "text", "text": " Who should own this?"}]}]}]}, {"ts": "1723551930.264039", "text": "Production deployment done. We also have pushed a minor improvement i.e. the rows in Org View and Merit view table will be sorted by Employee names in alphabetical order by default.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723497573.287809", "subtype": "thread_broadcast", "reactions": [{"name": "heart", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "7Pfxb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Production deployment done. We also have pushed a minor improvement i.e. the rows in Org View and Merit view table will be sorted by Employee names in alphabetical order by default."}]}]}]}, {"ts": "1723512814.417909", "text": "Is All good with <PERSON><PERSON> for now?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723512814.417909", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "jP9Fy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is All good with <PERSON><PERSON> for now?"}]}]}]}, {"ts": "1723497573.287809", "text": "<!here> upload issue for Nauto, Practifi and PlayQ have been resolved. These will be deployed in Nauto's production environment at the end of the day. For playq and practifi these are already available in their environment", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1723497573.287809", "reply_count": 1, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "partyparrot", "users": ["U04DS2MBWP4", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "EWUoG", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " upload issue for Nauto, Practifi and PlayQ have been resolved. These will be deployed in Nauto's production environment at the end of the day. For playq and practifi these are already available in their environment"}]}]}]}], "created_at": "2025-05-22T21:35:34.629862"}