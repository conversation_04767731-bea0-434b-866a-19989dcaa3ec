{"date": "2023-11-29", "channel_id": "C065QSSNH8A", "message_count": 10, "messages": [{"ts": "1701275432.794879", "text": "<!here> I will be on vacation on Dec 7th and 8th ( will be available on call  / text / slack if there anything urgent. Here is my cell <tel:5082808112|************>)", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "o49h+", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I will be on vacation on Dec 7th and 8th ( will be available on call  / text / slack if there anything urgent. Here is my cell "}, {"type": "link", "url": "tel:5082808112", "text": "************"}, {"type": "text", "text": ")"}]}]}]}, {"ts": "1701264164.689749", "text": "Hey <@U0658EW4B8D> and <@U065H3M6WJV> daily recurring working session is currently scheduled for start at 530am IST. Can we find another schedule between 9am pst - 1pm pst instead?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1701264164.689749", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "nj4s2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " and "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " daily recurring working session is currently scheduled for start at 530am IST. Can we find another schedule between 9am pst - 1pm pst instead?"}]}]}]}, {"ts": "1701235535.612679", "text": "Nice <@U0658EW4B8D> ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Fj1av", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Nice "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1701231198.746489", "text": "<https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0|Project Plan> coming together", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "CgKLg", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "text": "Project Plan"}, {"type": "text", "text": " coming together"}]}]}]}, {"ts": "1701226641.616159", "text": "<@U0658EW4B8D> would be good to hear your feedback too. :slightly_smiling_face: And holler at me if the Google macro breaks. :zany_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701226641.616159", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "sjMXH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " would be good to hear your feedback too. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " And holler at me if the Google macro breaks. "}, {"type": "emoji", "name": "zany_face", "unicode": "1f92a"}]}]}]}, {"ts": "1701226526.071019", "text": "<@U04DKEFP1K8> When you have a chance -- I did a mockup of what we discussed with <PERSON><PERSON> this morning for OTE comp planning values, using <https://docs.google.com/spreadsheets/d/14A5nElJkzocHtMbcvnC_Mf-nm9ckhmSq0J1-QHUwKh8/edit#gid=0|this Google Sheet>. Take a look and let me know whether it matches what you were expecting? We could also share this with <PERSON><PERSON> to get her feedback before we lock in the requirements. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701226526.071019", "reply_count": 1, "reactions": [{"name": "rocket", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F068E8NKTQQ", "created": 1701226528, "timestamp": 1701226528, "name": "Compiify mockup: OTE adjustments", "title": "Compiify mockup: OTE adjustments", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "14A5nElJkzocHtMbcvnC_Mf-nm9ckhmSq0J1-QHUwKh8", "external_url": "https://docs.google.com/spreadsheets/d/14A5nElJkzocHtMbcvnC_Mf-nm9ckhmSq0J1-QHUwKh8/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/14A5nElJkzocHtMbcvnC_Mf-nm9ckhmSq0J1-QHUwKh8/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F068E8NKTQQ-be11c398bc/compiify_mockup__ote_adjustments_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHT5pOfalooATnvS0UUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F068E8NKTQQ/compiify_mockup__ote_adjustments", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "w3JBG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " When you have a chance -- I did a mockup of what we discussed with <PERSON><PERSON> this morning for OTE comp planning values, using "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/14A5nElJkzocHtMbcvnC_Mf-nm9ckhmSq0J1-QHUwKh8/edit#gid=0", "text": "this Google Sheet"}, {"type": "text", "text": ". Take a look and let me know whether it matches what you were expecting? We could also share this with <PERSON><PERSON> to get her feedback before we lock in the requirements. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1701222207.030239", "text": "<@U0658EW4B8D> We can use this channel for updates on PRDs and the customer implementation plans :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4", "U0658EW4B8D"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "CmTUp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " We can use this channel for updates on PRDs and the customer implementation plans "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1701205427.351099", "text": "I thought that's what <PERSON><PERSON><PERSON><PERSON> was doing- creating two separate environments. I'll wait for <PERSON><PERSON><PERSON><PERSON> to respond.\nWe really need to figure this out now as it has been a consistent issue on the live demo calls.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701205427.351099", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "aHFYY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I thought that's what <PERSON><PERSON><PERSON><PERSON> was doing- creating two separate environments. I'll wait for <PERSON><PERSON><PERSON><PERSON> to respond.\nWe really need to figure this out now as it has been a consistent issue on the live demo calls."}]}]}]}, {"ts": "1701205237.897429", "text": "If it's underlying data shifts (like changing which username works), this could be solvable by creating at least 2 different organizations and keeping one stable for demo.\n\nBut if it's more about code pushes breaking the sandbox ... you would end up needing separate sandboxes. :thinking_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8sLh5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If it's underlying data shifts (like changing which username works), this could be solvable by creating at least 2 different organizations and keeping one stable for demo.\n\nBut if it's more about code pushes breaking the sandbox ... you would end up needing separate sandboxes. "}, {"type": "emoji", "name": "thinking_face", "unicode": "1f914"}]}]}]}, {"ts": "1701205114.444339", "text": "<@U04DKEFP1K8> I was just giving a product demo to sales candidate. The organization tab of merit view appears to be broken in the demo environment. Can you fix it?\nWe really need to fix breaking of demos in the calls.\n<@U065H3M6WJV> any suggestions on how we can silo the demo environment to prevent such breakdowns?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701205114.444339", "reply_count": 2, "edited": {"user": "U04DS2MBWP4", "ts": "1701205134.000000"}, "blocks": [{"type": "rich_text", "block_id": "t5Htd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I was just giving a product demo to sales candidate. The organization tab of merit view appears to be broken in the demo environment. Can you fix it?\nWe really need to fix breaking of demos in the calls.\n"}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " any suggestions on how we can silo the demo environment to prevent such breakdowns?"}]}]}]}], "created_at": "2025-05-22T21:35:34.575166"}