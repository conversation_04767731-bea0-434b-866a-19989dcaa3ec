{"date": "2024-11-19", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1732037238.391159", "text": "<https://comptool.com/>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"from_url": "https://comptool.com/", "image_url": "http://comptool.com/wp-content/uploads/2022/11/sunburst-chart-trace.png", "image_width": 1080, "image_height": 1080, "image_bytes": 27225, "service_icon": "https://comptool.com/wp-content/uploads/2022/11/Site-Icon-300x300.png", "id": 1, "original_url": "https://comptool.com/", "fallback": "CompTool: Compensation Management Tools | CompTool", "text": "Seeking efficient compensation management solutions? We provide helpful compensation benchmarking tools! Elevate with our streamlined compensation processes.", "title": "Compensation Management Tools | CompTool", "title_link": "https://comptool.com/", "service_name": "CompTool"}], "blocks": [{"type": "rich_text", "block_id": "iNsDE", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://comptool.com/"}]}]}]}, {"ts": "1732032214.565229", "text": "My laptop is starting joining in a minute", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "cOSn9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "My laptop is starting joining in a minute"}]}]}]}, {"ts": "1732026543.101329", "text": "<@U0690EB5JE5> I'm sorry to ask but can you tell me what configuration was set for paybands for Curana on the backend? I think Its maybe job family and title but I'm not sure.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732026543.101329", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "YcQKS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I'm sorry to ask but can you tell me what configuration was set for paybands for Curana on the backend? I think Its maybe job family and title but I'm not sure."}]}]}]}, {"ts": "1732010515.326639", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> Fixes/changes pushed to Prod ENVs today\n• Promotion title dropdown issue, show full amount in PIB tooltip - DegenKolb\n• Audit Log - All ENVs\n• Include employee bug in cycle eligibility\n• Spell check `Equity` ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1732010515.326639", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1732010641.000000"}, "blocks": [{"type": "rich_text", "block_id": "AYo5n", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Fixes/changes pushed to Prod ENVs today\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Promotion title dropdown issue, show full amount in PIB tooltip - DegenKolb"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Audit Log - All ENVs"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Include employee bug in cycle eligibility"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Spell check "}, {"type": "text", "text": "Equity", "style": {"code": true}}, {"type": "text", "text": " "}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1731989581.575079", "text": "<@U0690EB5JE5> can we please fix the typo \"Equity\" in data upload in settings, and \"performance\" in editing form on the org page?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1731989581.575079", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "J0LmR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we please fix the typo \"Equity\" in data upload in settings, and \"performance\" in editing form on the org page?"}]}]}]}, {"ts": "1731959833.652149", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1731959446.931559", "text": "Just FYI, apparently <PERSON><PERSON> isn't able to join our shared channel without them upgrading their own Slack plan. So the channel is gone and won't come back. I do have <PERSON> on DM on Slack but the channel is no longer working.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "7hFLX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just FYI, apparently <PERSON><PERSON> isn't able to join our shared channel without them upgrading their own Slack plan. So the channel is gone and won't come back. I do have <PERSON> on DM on Slack but the channel is no longer working."}]}]}]}], "created_at": "2025-05-22T21:35:34.687862"}