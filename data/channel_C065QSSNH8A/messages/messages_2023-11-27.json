{"date": "2023-11-27", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1701109530.836359", "text": "<PERSON><PERSON><PERSON><PERSON>- we can't hear you", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "w3rxm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON>- we can't hear you"}]}]}]}, {"ts": "1701108088.910069", "text": "it works. Thanks Rachel", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "x4p6q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "it works. Thanks Rachel"}]}]}]}, {"ts": "1701107587.362409", "text": "He changed the login <@U04DS2MBWP4> :slightly_smiling_face:\n\nTry <mailto:<EMAIL>|<EMAIL>> instead as the username", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DTHg2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "He changed the login "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": "\n\nTry "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " instead as the username"}]}]}]}, {"ts": "1701107002.193579", "text": "<@U04DKEFP1K8> devapp (<https://dev-app.compiify.com/login>) is not working for me", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"from_url": "https://dev-app.compiify.com/login", "service_icon": "https://dev-app.compiify.com/apple-touch-icon.png", "id": 1, "original_url": "https://dev-app.compiify.com/login", "fallback": "Compiify", "text": "Web site created using create-react-app", "title": "Compiify", "title_link": "https://dev-app.compiify.com/login", "service_name": "dev-app.compiify.com"}], "blocks": [{"type": "rich_text", "block_id": "hkcSR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " devapp ("}, {"type": "link", "url": "https://dev-app.compiify.com/login"}, {"type": "text", "text": ") is not working for me"}]}]}]}, {"ts": "1701102550.688149", "text": "<@U04DKEFP1K8> <@U065H3M6WJV> I set up a time for us to chat at 10 am pst to align on the agenda and objectives for the call with digital assets.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV", "U04DKEFP1K8"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "M/dDI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I set up a time for us to chat at 10 am pst to align on the agenda and objectives for the call with digital assets."}]}]}]}, {"ts": "1701102502.123969", "text": "Welcome aboard <@U065H3M6WJV> :tada:", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "partying_face", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "AEWfh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Welcome aboard "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "tada", "unicode": "1f389"}]}]}]}], "created_at": "2025-05-22T21:35:34.576632"}