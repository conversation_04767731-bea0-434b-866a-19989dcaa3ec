#!/usr/bin/env python3
"""
Debug citation data consistency between vector DB and PostgreSQL.
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

import logging
from apps.documents.models import DocumentChunk
from apps.accounts.models import Tenant
from apps.core.utils.collection_manager import get_collection_name, get_qdrant_client

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_citation_consistency():
    """Debug the citation data consistency issue."""
    print("🔍 Debugging Citation Data Consistency")
    print("=" * 60)
    
    # Get stride tenant
    tenant = Tenant.objects.get(slug='stride')
    
    # Get the problematic node IDs from logs
    problematic_node_ids = [
        'b682eb0b-a168-4e20-8e14-06ceaff7bc2e',
        '5a3df087-7ebe-4540-9ccc-736202b54775',
        '7d54f5e6-b189-4c2d-b5eb-c0645f3ab7ad',
        'd66a7e98-61ab-46f7-ba89-a4571a2196f6',
        'db626e4f-a1f1-44b6-8131-afde1a0e5afa',
        '82c54dce-0701-465a-86b3-d62702f3caa3'
    ]
    
    print(f"🔍 Checking {len(problematic_node_ids)} problematic node IDs...")
    
    # Check each node ID in PostgreSQL
    for node_id in problematic_node_ids:
        print(f"\n📋 Checking node ID: {node_id}")
        
        # Check if chunk exists in PostgreSQL
        chunk = DocumentChunk.objects.filter(
            tenant=tenant,
            id=node_id
        ).first()
        
        if chunk:
            print(f"  ✅ Found in PostgreSQL: {chunk.id}")
            print(f"     Document: {chunk.document.title if chunk.document else 'No document'}")
            print(f"     Text preview: {chunk.text[:100]}...")
        else:
            print(f"  ❌ NOT found in PostgreSQL")
            
            # Check if it exists with different tenant
            chunk_other_tenant = DocumentChunk.objects.filter(id=node_id).first()
            if chunk_other_tenant:
                print(f"     ⚠️  Found in different tenant: {chunk_other_tenant.tenant.slug}")
            else:
                print(f"     ❌ Not found in any tenant")
    
    # Check vector database
    print(f"\n🔍 Checking vector database...")
    collection_name = get_collection_name(tenant.slug, intent="conversation")
    client = get_qdrant_client()
    
    try:
        # Get collection info
        collection_info = client.get_collection(collection_name)
        print(f"  ✅ Collection exists: {collection_name}")
        print(f"     Points count: {collection_info.points_count}")
        print(f"     Vectors count: {collection_info.vectors_count}")
        
        # Check if the problematic points exist in vector DB
        for node_id in problematic_node_ids[:3]:  # Check first 3
            try:
                point = client.retrieve(
                    collection_name=collection_name,
                    ids=[node_id]
                )
                if point:
                    print(f"  ✅ Node {node_id} exists in vector DB")
                else:
                    print(f"  ❌ Node {node_id} NOT in vector DB")
            except Exception as e:
                print(f"  ❌ Error checking node {node_id}: {e}")
                
    except Exception as e:
        print(f"  ❌ Error accessing vector DB: {e}")
    
    # Check all curana chunks in PostgreSQL
    print(f"\n📊 Checking all curana chunks in PostgreSQL...")
    curana_chunks = DocumentChunk.objects.filter(
        tenant=tenant,
        text__icontains='curana'
    )
    
    print(f"  📊 Found {curana_chunks.count()} curana chunks in PostgreSQL")
    
    # Show first few chunk IDs
    for i, chunk in enumerate(curana_chunks[:10]):
        print(f"    {i+1}. {chunk.id} - {chunk.text[:50]}...")
    
    # Check if any of the PostgreSQL chunk IDs match the problematic ones
    postgres_ids = set(str(chunk.id) for chunk in curana_chunks)
    problematic_set = set(problematic_node_ids)
    
    intersection = postgres_ids.intersection(problematic_set)
    print(f"\n🔍 Intersection between PostgreSQL and problematic IDs:")
    print(f"  📊 Matching IDs: {len(intersection)}")
    for match_id in intersection:
        print(f"    ✅ {match_id}")
    
    missing_in_postgres = problematic_set - postgres_ids
    print(f"  📊 Missing in PostgreSQL: {len(missing_in_postgres)}")
    for missing_id in missing_in_postgres:
        print(f"    ❌ {missing_id}")
    
    # Check tenant consistency
    print(f"\n🏢 Checking tenant consistency...")
    default_tenant = Tenant.objects.filter(slug='default').first()
    if default_tenant:
        default_curana_chunks = DocumentChunk.objects.filter(
            tenant=default_tenant,
            text__icontains='curana'
        )
        print(f"  📊 Curana chunks in 'default' tenant: {default_curana_chunks.count()}")
        
        # Check if problematic IDs are in default tenant
        for node_id in problematic_node_ids[:3]:
            chunk = DocumentChunk.objects.filter(
                tenant=default_tenant,
                id=node_id
            ).first()
            if chunk:
                print(f"    ✅ {node_id} found in 'default' tenant")
            else:
                print(f"    ❌ {node_id} not in 'default' tenant")
    
    return problematic_node_ids, curana_chunks

def fix_citation_consistency():
    """Fix the citation consistency issue."""
    print("\n🔧 Fixing Citation Consistency Issue")
    print("=" * 50)
    
    # Get stride tenant
    tenant = Tenant.objects.get(slug='stride')
    
    # Check if we're using the wrong tenant in search
    print("🔍 Checking tenant configuration...")
    
    # The issue might be that search is using 'default' tenant but data is in 'stride'
    # Let's check the search configuration
    
    from apps.search.services.rag_service import RAGService
    from apps.accounts.models import User
    
    user = User.objects.filter(email='<EMAIL>').first()
    if not user:
        print("❌ User not found")
        return
    
    # Check what tenant the RAG service is using
    rag_service = RAGService(user=user)
    print(f"🏢 RAG Service tenant: {rag_service.tenant_slug}")
    
    # The logs show it's using 'tenant_stride_default' collection
    # But the search might be looking for chunks in wrong tenant
    
    return True

if __name__ == "__main__":
    problematic_ids, curana_chunks = debug_citation_consistency()
    fix_citation_consistency()
