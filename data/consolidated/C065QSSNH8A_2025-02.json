{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2025-02", "message_count": 276, "messages": [{"ts": "1738349964.826349", "text": "<@U07EJ2LP44S> Do we have the final numbers? should I just take whatever is there in cycle now?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "eh0f8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Do we have the final numbers? should I just take whatever is there in cycle now?"}]}]}]}, {"ts": "1738587857.326209", "text": "<@U07EJ2LP44S> I have an <https://github.com/Compiify/Yellowstone/pull/2086|API> ready to distribute the delta based on the current distribution. I am just waiting for <PERSON>'s confirmation and validate with you.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1738587981.000000"}, "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}, {"name": "tada", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "0l5Fe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have an "}, {"type": "link", "url": "https://github.com/Compiify/Yellowstone/pull/2086", "text": "API"}, {"type": "text", "text": " ready to distribute the delta based on the current distribution. I am just waiting for <PERSON>'s confirmation and validate with you."}]}]}]}, {"ts": "1738587870.092229", "text": "<@U07EJ2LP44S> This is fixed.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738342580.211129", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "hG5Os", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is fixed."}]}]}]}, {"ts": "1738608250.582429", "text": "Valgenesis is LIVE!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738608250.582429", "reply_count": 3, "reactions": [{"name": "tada", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}, {"name": "partying_face", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "gMWbn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis is LIVE!"}]}]}]}, {"ts": "1738689869.849069", "text": "<@U0690EB5JE5> Can you investigate what's happening with Diversified? <PERSON> is a recommender, has a manager role, and has 7 direct reports. However his team won't load in the merit view, and I cannot impersonate him. I think this is also happening with at least one other manager, <PERSON>. They both report to <PERSON> --&gt; <PERSON> --&gt; <PERSON><PERSON><PERSON>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738689869.849069", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "Hp+k8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you investigate what's happening with Di<PERSON><PERSON>? <PERSON> is a recommender, has a manager role, and has 7 direct reports. However his team won't load in the merit view, and I cannot impersonate him. I think this is also happening with at least one other manager, <PERSON>. They both report to <PERSON> --> <PERSON> --> <PERSON><PERSON><PERSON>"}]}]}]}, {"ts": "1738694547.267379", "text": "<@U0690EB5JE5> There's a weird laggy thing happening in Curana: <https://www.loom.com/share/acabd6c7a091476a82d8e87ecf1f99ac?sid=0c3ef763-66c1-4a56-bfec-c454d33b1462>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738694547.267379", "reply_count": 3, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "6/F6y", "video_url": "https://www.loom.com/embed/acabd6c7a091476a82d8e87ecf1f99ac?sid=0c3ef763-66c1-4a56-bfec-c454d33b1462&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/acabd6c7a091476a82d8e87ecf1f99ac-480bca168ab832c8-4x3.jpg", "alt_text": "Performance Ratings Lag Issue 🐢", "title": {"type": "plain_text", "text": "Performance Ratings Lag Issue 🐢", "emoji": true}, "title_url": "https://www.loom.com/share/acabd6c7a091476a82d8e87ecf1f99ac", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 1 min  ", "emoji": true}}, {"type": "section", "block_id": "2sUvz", "text": {"type": "mrkdwn", "text": ":information_source: In this video, I’m discussing the lag issues we’re experiencing in Qurana while trying to access the performance ratings. I’ve noticed that it takes...", "verbatim": false}}, {"type": "actions", "block_id": "kyjRH", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/acabd6c7a091476a82d8e87ecf1f99ac?sid=0c3ef763-66c1-4a56-bfec-c454d33b1462"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"acabd6c7a091476a82d8e87ecf1f99ac\",\"videoName\":\"Performance Ratings Lag Issue 🐢\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://www.loom.com/share/acabd6c7a091476a82d8e87ecf1f99ac?sid=0c3ef763-66c1-4a56-bfec-c454d33b1462", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "Cnz7S", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " There's a weird laggy thing happening in Curana: "}, {"type": "link", "url": "https://www.loom.com/share/acabd6c7a091476a82d8e87ecf1f99ac?sid=0c3ef763-66c1-4a56-bfec-c454d33b1462"}]}]}]}, {"ts": "1738736483.459779", "text": "<@U07EJ2LP44S> This is done. Please check and let me know", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738271308.186779", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "m1YME", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is done. Please check and let me know"}]}]}]}, {"ts": "1738768999.028589", "text": "<@U0690EB5JE5> can you upload this to curana? I keep getting an error about the file format. It's additional paybands.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738768999.028589", "reply_count": 1, "files": [{"id": "F08C9LDC2SV", "created": 1738768997, "timestamp": 1738768997, "name": "Curana_PaybandsUpload.csv", "title": "Curana_PaybandsUpload.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1988, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08C9LDC2SV/curana_paybandsupload.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08C9LDC2SV/download/curana_paybandsupload.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08C9LDC2SV/curana_paybandsupload.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08C9LDC2SV-2802122761", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08C9LDC2SV/curana_paybandsupload.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Band Id,Effective Start Date,Effective End Date,Compensation Grade,Location (Country),Location (Region),Team,Department,Job Family,Job Subfamily,Description,Tier,Job Category,Job Level,Job Title,Frequency,Salary Currency,Salary Min Amount,Salary Target Amount,Salary Max Amount,Variable Pay Currency,Variable Pay Target (%),Variable Pay Target Amount,Equity Currency,Equity Target Amount,Equity Target (unit),Equity Target (%),Min Equity (unit),Max Equity (unit),Equity Participation Rate (%),OTE Min Amount,OTE Target Amount,OTE Max Amount,Pay Mix\r\nA,350,1/1/21,,N/A,US,N/A,N/A,Finance & Accounting,Shared Services,N/A,N/A,0,M,6,VP of Finance,Annual,USD,159400.00,262500.00,400100.00,USD,0,0,USD,0,0,0,0,0,0,0,0,0,\r\nA,351,1/1/21,,N/A,US,N/A,N/A,Appeals & Grievances,Healthplan Admin,N/A,N/A,0,IC,3,Appeals and Grievances Specialist,Annual,USD,40200.00,56100.00,81000.00,USD,0,0,USD,0,0,0,0,0,0,0,0,0,\r\nA,352,1/1/21,,N/A,US,N/A,N/A,Risk Adjustment,Healthplan Admin,N/A,N/A,0,IC,5,Senior Technical Pr...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Band Id</div><div class=\"cm-col\">Effective Start Date</div><div class=\"cm-col\">Effective End Date</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Location (Country)</div><div class=\"cm-col\">Location (Region)</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">Job Subfamily</div><div class=\"cm-col\">Description</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Frequency</div><div class=\"cm-col\">Salary Currency</div><div class=\"cm-col\">Salary Min Amount</div><div class=\"cm-col\">Salary Target Amount</div><div class=\"cm-col\">Salary Max Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay Target (%)</div><div class=\"cm-col\">Variable Pay Target Amount</div><div class=\"cm-col\">Equity Currency</div><div class=\"cm-col\">Equity Target Amount</div><div class=\"cm-col\">Equity Target (unit)</div><div class=\"cm-col\">Equity Target (%)</div><div class=\"cm-col\">Min Equity (unit)</div><div class=\"cm-col\">Max Equity (unit)</div><div class=\"cm-col\">Equity Participation Rate (%)</div><div class=\"cm-col\">OTE Min Amount</div><div class=\"cm-col\">OTE Target Amount</div><div class=\"cm-col\">OTE Max Amount</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">350</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">US</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Finance &amp; Accounting</div><div class=\"cm-col\">Shared Services</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">VP of Finance</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">159400.00</div><div class=\"cm-col cm-num\">262500.00</div><div class=\"cm-col cm-num\">400100.00</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">351</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">US</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Appeals &amp; Grievances</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Appeals and Grievances Specialist</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">40200.00</div><div class=\"cm-col cm-num\">56100.00</div><div class=\"cm-col cm-num\">81000.00</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">352</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">US</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Risk Adjustment</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Senior Technical Project Manager</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">66500.00</div><div class=\"cm-col cm-num\">90900.00</div><div class=\"cm-col cm-num\">121900.00</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">353</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">US</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Utilization Mgmt</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Director of Utilization Management</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">78500.00</div><div class=\"cm-col cm-num\">100200.00</div><div class=\"cm-col cm-num\">120900.00</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">354</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">US</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Systems</div><div class=\"cm-col\">Shared Services</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Principal Cloud Architect</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">123800.00</div><div class=\"cm-col cm-num\">152400.00</div><div class=\"cm-col cm-num\">181600.00</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">355</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">US</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Finance &amp; Accounting</div><div class=\"cm-col\">Shared Services</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Corporate Controller</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">99000.00</div><div class=\"cm-col cm-num\">167500.00</div><div class=\"cm-col cm-num\">259800.00</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">356</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">US</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Credentialing</div><div class=\"cm-col\">Med Grp Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Lead Medical Credentialing Coordinator</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">38300.00</div><div class=\"cm-col cm-num\">51900.00</div><div class=\"cm-col cm-num\">65800.00</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">357</div><div class=\"cm-col\">1/1/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">US</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Clinical Operations</div><div class=\"cm-col\">Med Grp Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Regional Sales Manager</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">80000.00</div><div class=\"cm-col cm-num\">112200.00</div><div class=\"cm-col cm-num\">157800.00</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 9, "lines_more": 5, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Oibrk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you upload this to curana? I keep getting an error about the file format. It's additional paybands."}]}]}]}, {"ts": "1738769042.426039", "text": "Will do it in an hour ", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "coDnv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will do it in an hour "}]}]}]}, {"ts": "1738769400.381659", "text": "<@U0690EB5JE5> Also when you have a moment (before tomorrow please) can you boost up <http://test.stridehr.io|test.stridehr.io> so it's fast for the Diversified training? There's a session tomorrow and also one on Friday.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738769400.381659", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "x9XP9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Also when you have a moment (before tomorrow please) can you boost up "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": " so it's fast for the Diversified training? There's a session tomorrow and also one on Friday."}]}]}]}, {"ts": "1738852884.947509", "text": "I have an 11am meeting with <PERSON><PERSON><PERSON> (including <PERSON>) so I may not make it to the call. I don't know that it would go that long but just FYI.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5", "U07M6QKHUC9"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "DlsgJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have an 11am meeting with <PERSON><PERSON><PERSON> (including <PERSON>) so I may not make it to the call. I don't know that it would go that long but just FYI."}]}]}]}, {"ts": "1738854851.150619", "text": "<@U0690EB5JE5> From <PERSON> at Valgenesis:Quick flag before our call\n[10:07 AM] <PERSON>\n\nHaving some comments from managers the comp planner isn't working right ( not updating the comp field when a% is added and merit not updating at the top\n[10:08 AM] <PERSON>\n\nAlso the HRBP's can't impersonate people the settings and Reporting view has gone ? but we didn't change anything", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "aeDI6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " From <PERSON> at Valgenesis:Quick flag before our call\n[10:07 AM] <PERSON>\n\nHaving some comments from managers the comp planner isn't working right ( not updating the comp field when a% is added and merit not updating at the top\n[10:08 AM] <PERSON>\n\nAlso the HRBP's can't impersonate people the settings and Reporting view has gone ? but we didn't change anything"}]}]}]}, {"ts": "1738854976.656029", "text": "1. Merit not working. I think they have to press enter which is not obvious", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+yK8x", "elements": [{"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Merit not working. I think they have to press enter which is not obvious"}]}], "style": "ordered", "indent": 0, "offset": 0, "border": 0}]}]}, {"ts": "1738854989.377069", "text": "HRBP will check and get back ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "KllHz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "HRBP will check and get back "}]}]}]}, {"ts": "1738855015.964619", "text": "I have not pushed any changes today ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ArFQp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have not pushed any changes today "}]}]}]}, {"ts": "1738855032.112419", "text": "I just told her about the enter thing. How quickly can we address that?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "euGKJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I just told her about the enter thing. How quickly can we address that?"}]}]}]}, {"ts": "1738855050.398429", "text": "Enter thing will address tomorrow ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jUIYy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Enter thing will address tomorrow "}]}]}]}, {"ts": "1738855063.102669", "text": "Work in progress ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "06eRS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Work in progress "}]}]}]}, {"ts": "1738855122.381909", "text": "Ok. That is highest priority for me since we have an active cycle, and one starting on Monday. We didn't train people to press enter because that wasn't the behavior before so there's a sense of urgency there.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738855122.381909", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "iKcT9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. That is highest priority for me since we have an active cycle, and one starting on Monday. We didn't train people to press enter because that wasn't the behavior before so there's a sense of urgency there."}]}]}]}, {"ts": "1738855159.111399", "text": "Yes, We are on top of it. <@U06HN8XDC5A> FYI...", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "j48mC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, We are on top of it. "}, {"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " FYI..."}]}]}]}, {"ts": "1738855174.910829", "text": "<@U07EJ2LP44S> HRBP I am able to impersonate, whats the issue exactly", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4OCsm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " HRBP I am able to impersonate, whats the issue exactly"}]}]}]}, {"ts": "1738855180.562519", "text": "do you have an example?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "dFomM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "do you have an example?"}]}]}]}, {"ts": "1738855219.655709", "text": "Impersonation of an HRBP in VG", "user": "U0690EB5JE5", "type": "message", "files": [{"id": "F08C3KSLFGT", "created": 1738855209, "timestamp": 1738855209, "name": "Screenshot 2025-02-06 at 8.50.04 PM.png", "title": "Screenshot 2025-02-06 at 8.50.04 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 259276, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08C3KSLFGT/screenshot_2025-02-06_at_8.50.04___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08C3KSLFGT/download/screenshot_2025-02-06_at_8.50.04___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 195, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 260, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 389, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 433, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 519, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C3KSLFGT-780be87462/screenshot_2025-02-06_at_8.50.04___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 554, "original_w": 1916, "original_h": 1036, "thumb_tiny": "AwAZADDR69qPwoxjGKU0AGKSlo9KAE/Cl/CiloAQ0UoooASilpBQAUtFFAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08C3KSLFGT/screenshot_2025-02-06_at_8.50.04___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08C3KSLFGT-2b3c840fb8", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "KioDG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Impersonation of an HRBP in VG"}]}]}]}, {"ts": "1738855442.691999", "text": "I think they are saying the HRBP themselves isn't able to impersonate a manager they work with", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "smonC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think they are saying the HRBP themselves isn't able to impersonate a manager they work with"}]}]}]}, {"ts": "1738855460.501149", "text": "Whereas before they could go in and see what the manager saw", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LRyku", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Whereas before they could go in and see what the manager saw"}]}]}]}, {"ts": "1738855544.054639", "text": "I don’t think we ever supported that.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "C/NAL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " think we ever supported that."}]}]}]}, {"ts": "1738855618.431929", "text": "Also Impersonation doesn’t makes sense VG HRBP as they can only see employees assigned.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0hNml", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also Impersonation "}, {"type": "text", "text": "doesn’t"}, {"type": "text", "text": " makes sense VG HRBP as they can only see employees assigned."}]}]}]}, {"ts": "1738855735.853249", "text": "Ok; I didn't test that ever so I don't know. Is that the same for reporting? Did HRBPs ever have access to reports?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "f/1Xc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok; I didn't test that ever so I don't know. Is that the same for reporting? Did HRBPs ever have access to reports?"}]}]}]}, {"ts": "1738855765.873809", "text": "<PERSON> said she is positive HRBPs could impersonate before", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EXzUA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> said she is positive HRBPs could impersonate before"}]}]}]}, {"ts": "1738855829.991279", "text": "It makes sense though that they wouldn't be able to - since we modeled HRBP after managers IIRC?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oOKxF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It makes sense though that they wouldn't be able to - since we modeled HRBP after managers IIRC?"}]}]}]}, {"ts": "1738855932.644119", "text": "Yes, no reports just table ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "e095v", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, no reports just table "}]}]}]}, {"ts": "1738855939.091269", "text": "And export ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "R5Hd7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And export "}]}]}]}, {"ts": "1738855968.816769", "text": "Ok I think I'm with you on this but she SWEARS it was possible before. I have a call with her at noon to talk anyway so we'll see what she says then", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0G/Dt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok I think I'm with you on this but she SWEARS it was possible before. I have a call with her at noon to talk anyway so we'll see what she says then"}]}]}]}, {"ts": "1738856032.912339", "text": "I don’t think so, let me check. Impersonation was allowed only for admins ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vUBiP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " think so, let me check. Impersonation was allowed only for admins "}]}]}]}, {"ts": "1738857070.683319", "text": "I thought HRBP were able to impersonate their managers and I remember seeing it", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jQdVh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I thought HRBP were able to impersonate their managers and I remember seeing it"}]}]}]}, {"ts": "1738857684.937499", "text": "<@U07EJ2LP44S> <@U0690EB5JE5> do we need to meet after <PERSON>'s diversified call or should we skip the meeting?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vf123", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " do we need to meet after <PERSON>'s diversified call or should we skip the meeting?"}]}]}]}, {"ts": "1738857716.918369", "text": "I am fine to skip", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738857716.918369", "reply_count": 10, "blocks": [{"type": "rich_text", "block_id": "ezU6h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am fine to skip"}]}]}]}, {"ts": "1738857790.586429", "text": "<@U0690EB5JE5> you were going to show the total rewards today?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "SZFpJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " you were going to show the total rewards today?"}]}]}]}, {"ts": "1738857818.297889", "text": "Yes, I can", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wjwGe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, I can"}]}]}]}, {"ts": "1738857833.708679", "text": "It’s ready", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fpK1t", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It’s"}, {"type": "text", "text": " ready"}]}]}]}, {"ts": "1738857843.692319", "text": "I am available if you want to meet ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "JTlF2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am available if you want to meet "}]}]}]}, {"ts": "1738858072.111179", "text": "ok lets do that", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8mBzF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok lets do that"}]}]}]}, {"ts": "1738863898.973629", "text": "<@U0690EB5JE5> In the calculated budget section of the cycle builder, the calclulated budget is reflecting INR but the budget is reflecting USD. i don't think this is causing any functional issues but FYI.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738863898.973629", "reply_count": 7, "files": [{"id": "F08C1T921B7", "created": 1738863894, "timestamp": 1738863894, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 54299, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08C1T921B7/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08C1T921B7/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_360.png", "thumb_360_w": 360, "thumb_360_h": 212, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_480.png", "thumb_480_w": 480, "thumb_480_h": 283, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_720.png", "thumb_720_w": 720, "thumb_720_h": 425, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C1T921B7-aed7726e9b/image_800.png", "thumb_800_w": 800, "thumb_800_h": 472, "original_w": 912, "original_h": 538, "thumb_tiny": "AwAcADDSPPcikx/tGlFLQA3HuaMf7TUtH4UAJj3NLkUH6UUAJj2oA56Uv4UZoAKBRmgUAFLRRQB//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08C1T921B7/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08C1T921B7-d10ace4c6a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Jn6Hw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " In the calculated budget section of the cycle builder, the calclulated budget is reflecting INR but the budget is reflecting USD. i don't think this is causing any functional issues but FYI."}]}]}]}, {"ts": "1738866297.361909", "text": "<@U0690EB5JE5> <PERSON> cannot see full data (she does see some data) in her view right now. The filters aren't working, and when I impersonate I see nothing. I removed and readded her role and it did not help. This is <PERSON><PERSON><PERSON>, <PERSON> Super Admin. Same for <PERSON><PERSON>.", "user": "U07EJ2LP44S", "type": "message", "edited": {"user": "U07EJ2LP44S", "ts": "1738866383.000000"}, "blocks": [{"type": "rich_text", "block_id": "UXA/u", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON> cannot see full data (she does see some data) in her view right now. The filters aren't working, and when I impersonate I see nothing. I removed and readded her role and it did not help. This is <PERSON><PERSON><PERSON>, <PERSON> Super Admin. Same for <PERSON><PERSON>."}]}]}]}, {"ts": "**********.773559", "text": "<@U0690EB5JE5> There is also an issue with the overall data, employees are not showing a status or their performance ratings. I explained the best I could here: <https://www.loom.com/share/7ccedf644cfb4c00b552d22aefe64878?sid=b88f140d-c2c7-4f40-98ea-d631fe6c38b6>\n\nAlso I had data in the filters at the beginning of the call and by the end the filters were showing blank.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.773559", "reply_count": 9, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "mSFu0", "video_url": "https://www.loom.com/embed/7ccedf644cfb4c00b552d22aefe64878?sid=b88f140d-c2c7-4f40-98ea-d631fe6c38b6&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/7ccedf644cfb4c00b552d22aefe64878-0758ca4d4e03f48e-4x3.jpg", "alt_text": "Tidally Account Data Issues 🔍", "title": {"type": "plain_text", "text": "Tidally Account Data Issues 🔍", "emoji": true}, "title_url": "https://www.loom.com/share/7ccedf644cfb4c00b552d22aefe64878", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 4 min  ", "emoji": true}}, {"type": "section", "block_id": "ACBCo", "text": {"type": "mrkdwn", "text": ":information_source: In this video, I discuss the recent findings regarding <PERSON><PERSON><PERSON>'s account data while we were supposed to focus on training. We discovered that the...", "verbatim": false}}, {"type": "actions", "block_id": "SXezv", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/7ccedf644cfb4c00b552d22aefe64878?sid=b88f140d-c2c7-4f40-98ea-d631fe6c38b6"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"7ccedf644cfb4c00b552d22aefe64878\",\"videoName\":\"Tidally Account Data Issues 🔍\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://www.loom.com/share/7ccedf644cfb4c00b552d22aefe64878?sid=b88f140d-c2c7-4f40-98ea-d631fe6c38b6", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "0UqDu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " There is also an issue with the overall data, employees are not showing a status or their performance ratings. I explained the best I could here: "}, {"type": "link", "url": "https://www.loom.com/share/7ccedf644cfb4c00b552d22aefe64878?sid=b88f140d-c2c7-4f40-98ea-d631fe6c38b6"}, {"type": "text", "text": "\n\nAlso I had data in the filters at the beginning of the call and by the end the filters were showing blank."}]}]}]}, {"ts": "1738944405.568679", "text": "Would we be able to change the continue with azure ad icon to continue with microsoft sso instead?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738944405.568679", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "8aCes", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Would we be able to change the continue with azure ad icon to continue with microsoft sso instead?"}]}]}]}, {"ts": "1738949005.630699", "text": "<@U0690EB5JE5> can you re-run diversifieds budget with this new number: 14514944.4", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738949005.630699", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "MOusa", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you re-run diversifieds budget with this new number: 14514944.4"}]}]}]}, {"ts": "1738949029.584709", "text": "and please add <PERSON>, <mailto:<PERSON><PERSON><PERSON><PERSON>@dgoc.com|<PERSON><PERSON><PERSON><PERSON>@dgoc.com> to SSO", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738949029.584709", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "U3hbo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "and please add <PERSON>, "}, {"type": "link", "url": "mailto:<PERSON><PERSON><EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " to SSO"}]}]}]}, {"ts": "1738949320.475859", "text": "<@U0690EB5JE5> BUG (I think? maybe due to the budget thing) - Added <PERSON> to recommenders list. Confirmed he's there. He's not in the budget allocation. After publishing, he's not in the merit view. He is assigned a manager role. How do we get him into the merit cycle as a recommender?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738949320.475859", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "1xlhC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " BUG (I think? maybe due to the budget thing) - Added <PERSON> to recommenders list. Confirmed he's there. He's not in the budget allocation. After publishing, he's not in the merit view. He is assigned a manager role. How do we get him into the merit cycle as a recommender?"}]}]}]}, {"ts": "1738950116.671609", "text": "<@U0690EB5JE5> Can you please upload this for tithely. it is just performance data changes - performance period start/end and score. I am getting a format error as per usual. I left all the other columns exactly as they were since sometimes it seems to want a random column.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738950116.671609", "reply_count": 1, "files": [{"id": "F08C4L7QWCE", "created": 1738950086, "timestamp": 1738950086, "name": "Tithely_PerformanceScores.csv", "title": "Tithely_PerformanceScores.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 45024, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08C4L7QWCE/tithely_performancescores.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08C4L7QWCE/download/tithely_performancescores.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08C4L7QWCE/tithely_performancescores.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08C4L7QWCE-cf3214e822", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08C4L7QWCE/tithely_performancescores.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">234</div><div class=\"cm-col\">David</div><div class=\"cm-col\">Wickstrom</div><div class=\"cm-col\">David Wickstrom</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">10/16/17</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">266</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">10/16/17</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">FL</div><div class=\"cm-col\">Engineer II</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">91416</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/25/24</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">326</div><div class=\"cm-col\">Nicholas</div><div class=\"cm-col\">White</div><div class=\"cm-col\">Nicholas White</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">10/9/23</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">26</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">10/9/23</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">MA</div><div class=\"cm-col\">Product Manager</div><div class=\"cm-col\">Product Management</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">125000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">9/13/23</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">167</div><div class=\"cm-col\">Bradley</div><div class=\"cm-col\">White</div><div class=\"cm-col\">Bradley White</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">4/13/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">218</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">4/13/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">TN</div><div class=\"cm-col\">Engineer 2</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">90537</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/25/24</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">172</div><div class=\"cm-col\">Cory</div><div class=\"cm-col\">Walz</div><div class=\"cm-col\">Cory Walz</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">5/17/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">26</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">5/17/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">OR</div><div class=\"cm-col\">Technical Product Manager</div><div class=\"cm-col\">Product Management</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">95000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">4/8/24</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">57</div><div class=\"cm-col\">Cory</div><div class=\"cm-col\">Wadstrom</div><div class=\"cm-col\">Cory Wadstrom</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">2/4/19</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">18</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">2/4/19</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">AR</div><div class=\"cm-col\">Sales Operations Manager</div><div class=\"cm-col\">Sales</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">135000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">4/1/22</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">13</div><div class=\"cm-col\">Tyler</div><div class=\"cm-col\">Viers</div><div class=\"cm-col\">Tyler Viers</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">9/25/17</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">199</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">9/25/17</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">TN</div><div class=\"cm-col\">Sr. Customer Support Advocate</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Part Time</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">50960</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">9/23/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">24.5</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 174, "lines_more": 173, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "EEzMR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you please upload this for tithely. it is just performance data changes - performance period start/end and score. I am getting a format error as per usual. I left all the other columns exactly as they were since sometimes it seems to want a random column."}]}]}]}, {"ts": "1738951027.888099", "text": "<@U0690EB5JE5> We also need to turn on the email notifications for Diversified as of EOD Monday", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738951027.888099", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "4yqst", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We also need to turn on the email notifications for Diversified as of EOD Monday"}]}]}]}, {"ts": "1738955301.322879", "text": "BUG: Valgenesis. For teams that have international currency, the system seems to be calculating the budgets incorrectly and not allowing submission (because we have enabled the functionality to keep them within budget):", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738955301.322879", "reply_count": 2, "files": [{"id": "F08C55G2Q6S", "created": 1738955299, "timestamp": 1738955299, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 125691, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08C55G2Q6S/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08C55G2Q6S/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C55G2Q6S-c5e8f4b307/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C55G2Q6S-c5e8f4b307/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C55G2Q6S-c5e8f4b307/image_360.png", "thumb_360_w": 360, "thumb_360_h": 203, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C55G2Q6S-c5e8f4b307/image_480.png", "thumb_480_w": 480, "thumb_480_h": 270, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C55G2Q6S-c5e8f4b307/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08C55G2Q6S-c5e8f4b307/image_720.png", "thumb_720_w": 720, "thumb_720_h": 405, "original_w": 720, "original_h": 405, "thumb_tiny": "AwAbADB7WkWf4vxNJ9kj/wBr86sv1H0pv+etAEP2OP0f86T7HH6P+dXd4A+9+FL5ilevOKAKP2SL/a/Ok+yw57/nViigB0nUfQUlJb/PAjNySOpqO7JQjaSOe30oAloqgZZMn5z+dL5sm4/OfzoAvUHpVAzSc/OevrSGaTH3z+dAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08C55G2Q6S/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08C55G2Q6S-9a3247235c", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "n59ih", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "BUG: Valgenesis. For teams that have international currency, the system seems to be calculating the budgets incorrectly and not allowing submission (because we have enabled the functionality to keep them within budget):"}]}]}]}, {"ts": "1738969290.920799", "text": "<@U07EJ2LP44S> will fix all these issues by Monday.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ecYMW", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " will fix all these issues by Monday."}]}]}]}, {"ts": "1739193201.600729", "text": "<@U07EJ2LP44S> All the issues/requests posted on Friday are addressed. Please do let me know if anything missed or any new issues.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Bu1Rn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " All the issues/requests posted on Friday are addressed. Please do let me know if anything missed or any new issues."}]}]}]}, {"ts": "**********.039499", "text": "<!here> I am sick today and my responses will be slower. ", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.039499", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Klusq", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I am sick today and my responses will be slower. "}]}]}]}, {"ts": "**********.544439", "text": "<@U0690EB5JE5> we got the confirmation from Alaya Care to deactivate their account. We will reactivate it whenever they need to login.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.544439", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "XFv77", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we got the confirmation from Alaya Care to deactivate their account. We will reactivate it whenever they need to login."}]}]}]}, {"ts": "**********.653009", "text": "<@U0690EB5JE5> Thank you for all those fixes. Can you upload this file ASAP? It's the Diversified final ratings, and we need to have them in immediately to do a sanity before tomorrow. Once again I got a format error.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.653009", "reply_count": 20, "files": [{"id": "F08D9PDG4QY", "created": **********, "timestamp": **********, "name": "Diversified_CornerstoneUpdate.csv", "title": "Diversified_CornerstoneUpdate.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 146073, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08D9PDG4QY/diversified_cornerstoneupdate.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08D9PDG4QY/download/diversified_cornerstoneupdate.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08D9PDG4QY/diversified_cornerstoneupdate.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08D9PDG4QY-ffe2a543d7", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08D9PDG4QY/diversified_cornerstoneupdate.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30882</div><div class=\"cm-col\">Sabrina</div><div class=\"cm-col\">Smith</div><div class=\"cm-col\">Sabrina Smith</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">8/26/24</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">25190</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">8/26/24</div><div class=\"cm-col\"></div><div class=\"cm-col\">TWO_OR_MORE_RACES</div><div class=\"cm-col\">AL</div><div class=\"cm-col\">ANALYST</div><div class=\"cm-col\">Measurement:Bham</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">55000.14</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">5500.01</div><div class=\"cm-col cm-num\">2200</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">3300.01</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">2200</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30905</div><div class=\"cm-col\">Caren</div><div class=\"cm-col\">Haga</div><div class=\"cm-col\">Caren Haga</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">8/16/24</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">26481</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">8/16/24</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">TX</div><div class=\"cm-col\">PRODUCTION ASSISTANT</div><div class=\"cm-col\">ADMIN:DEW</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">62400</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">7.5</div><div class=\"cm-col cm-num\">4680</div><div class=\"cm-col cm-num\">1872</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">2808</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">1872</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30904</div><div class=\"cm-col\">Carlina</div><div class=\"cm-col\">Favors</div><div class=\"cm-col\">Carlina Favors</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">8/16/24</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">26481</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">8/16/24</div><div class=\"cm-col\"></div><div class=\"cm-col\">HISPANIC_OR_LATINO</div><div class=\"cm-col\">TX</div><div class=\"cm-col\">PRODUCTION ASSISTANT</div><div class=\"cm-col\">ADMIN:DEW</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">58240</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">7.5</div><div class=\"cm-col cm-num\">4368</div><div class=\"cm-col cm-num\">1747.2</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">2620.8</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">1747.2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30903</div><div class=\"cm-col\">Kimberly</div><div class=\"cm-col\">Chaney</div><div class=\"cm-col\">Kimberly Chaney</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">8/16/24</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">16602</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">8/16/24</div><div class=\"cm-col\"></div><div class=\"cm-col\">HISPANIC_OR_LATINO</div><div class=\"cm-col\">TX</div><div class=\"cm-col\">PRODUCTION ASSISTANT</div><div class=\"cm-col\">ADMIN:DEW MID</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">54080</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">7.5</div><div class=\"cm-col cm-num\">4056</div><div class=\"cm-col cm-num\">1622.4</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">2433.6</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">1622.4</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30906</div><div class=\"cm-col\">Ryan</div><div class=\"cm-col\">Jones</div><div class=\"cm-col\">Ryan Jones</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">8/16/24</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">29497</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">8/16/24</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">TX</div><div class=\"cm-col\">SPECIALIST</div><div class=\"cm-col\">Production:DEW UP</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">102670.62</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">10267.06</div><div class=\"cm-col cm-num\">4106.82</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">6160.24</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">4106.82</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30897</div><div class=\"cm-col\">David</div><div class=\"cm-col\">Lewis</div><div class=\"cm-col\">David Lewis</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">8/16/24</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">23929</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">8/16/24</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">TX</div><div class=\"cm-col\">FOREMAN SR</div><div class=\"cm-col\">Compression:DEW MID</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">134373.98</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col cm-num\">20156.1</div><div class=\"cm-col cm-num\">8062.44</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">12093.66</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">8062.44</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 548, "lines_more": 547, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "1H4Zm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Thank you for all those fixes. Can you upload this file ASAP? It's the Diversified final ratings, and we need to have them in immediately to do a sanity before tomorrow. Once again I got a format error."}]}]}]}, {"ts": "1739207347.294419", "text": "<@U0690EB5JE5> Small bug: <https://www.loom.com/share/2920bc9486644657a77bf4b97e271d00?sid=7d5c552c-7f3b-48c2-ba32-b56e8f514c7b>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739207347.294419", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "50CFa", "video_url": "https://www.loom.com/embed/2920bc9486644657a77bf4b97e271d00?sid=7d5c552c-7f3b-48c2-ba32-b56e8f514c7b&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/2920bc9486644657a77bf4b97e271d00-00ea523dbdd977ac-4x3.jpg", "alt_text": "Issues with Diversified Bonus Award Sorting", "title": {"type": "plain_text", "text": "Issues with Diversified Bonus Award Sorting", "emoji": true}, "title_url": "https://www.loom.com/share/2920bc9486644657a77bf4b97e271d00", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 1 min  ", "emoji": true}}, {"type": "section", "block_id": "BkxQV", "text": {"type": "mrkdwn", "text": ":information_source: In this video, I’m sharing my experience while testing the Diversified system, specifically focusing on the bonus award sorting feature. I noticed...", "verbatim": false}}, {"type": "actions", "block_id": "pjuRi", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/2920bc9486644657a77bf4b97e271d00?sid=7d5c552c-7f3b-48c2-ba32-b56e8f514c7b"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"2920bc9486644657a77bf4b97e271d00\",\"videoName\":\"Issues with Diversified Bonus Award Sorting\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://www.loom.com/share/2920bc9486644657a77bf4b97e271d00?sid=7d5c552c-7f3b-48c2-ba32-b56e8f514c7b", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "w2HJw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Small bug: "}, {"type": "link", "url": "https://www.loom.com/share/2920bc9486644657a77bf4b97e271d00?sid=7d5c552c-7f3b-48c2-ba32-b56e8f514c7b"}]}]}]}, {"ts": "**********.438369", "text": "From tithely, have not tried to reproduce yet. Cannot move past this section in comp builder.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.438369", "reply_count": 5, "files": [{"id": "F08CN1J4L2E", "file_access": "access_denied", "created": 0, "timestamp": 0, "user": "U08QENR4TNY", "filetype": "webm"}], "blocks": [{"type": "rich_text", "block_id": "B1q13", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "From tithely, have not tried to reproduce yet. Cannot move past this section in comp builder."}]}]}]}, {"ts": "1739266875.844119", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> I have disabled all ENVs except these.\n• <http://test.stridehr.io|test.stridehr.io>\n• <http://curana.stridhr.io|curana.stridhr.io>\n• <http://div-energy.stridehr.io|div-energy.stridehr.io>\n• <http://tithely.stridehr.io|tithely.stridehr.io>\n• <http://valgenesis.stridehr.io|valgenesis.stridehr.io>\nPlease let me know if any inactive customers need to login (DGOC/Alayacare). Also please let me know if I can downgrade <http://test.stridehr.io|test.stridehr.io>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1739268649.000000"}, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8tabf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I have disabled all ENVs except these.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://curana.stridhr.io", "text": "curana.stridhr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://div-energy.stridehr.io", "text": "div-energy.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://tithely.stridehr.io", "text": "tithely.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "http://valgenesis.stridehr.io", "text": "valgenesis.stridehr.io"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nPlease let me know if any inactive customers need to login (DGOC/Alayacare). Also please let me know if I can downgrade "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}]}]}]}, {"ts": "1739280601.304249", "text": "I still need the customer demo one please", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "h25Q+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I still need the customer demo one please"}]}]}]}, {"ts": "1739280633.857759", "text": "I will be creating the training environments for both <PERSON> and <PERSON><PERSON> simultaneously", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739280633.857759", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "bGe8W", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will be creating the training environments for both <PERSON> and <PERSON><PERSON> simultaneously"}]}]}]}, {"ts": "1739280732.846839", "text": "Ok I will renable demo in couple of hours ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QgejN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok I will renable demo in couple of hours "}]}]}]}, {"ts": "1739295222.824489", "text": "<@U07EJ2LP44S> <https://stridedemo.stridehr.io/> is back online", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ds1B4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://stridedemo.stridehr.io/"}, {"type": "text", "text": " is back online"}]}]}]}, {"ts": "1739296532.147379", "text": "<@U0690EB5JE5> From Tithely, I don't know the answer to this:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739296532.147379", "reply_count": 12, "files": [{"id": "F08CM410DML", "created": 1739296530, "timestamp": 1739296530, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 58381, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08CM410DML/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08CM410DML/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_360.png", "thumb_360_w": 360, "thumb_360_h": 35, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_480.png", "thumb_480_w": 480, "thumb_480_h": 47, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_720.png", "thumb_720_w": 720, "thumb_720_h": 70, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_800.png", "thumb_800_w": 800, "thumb_800_h": 78, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_960.png", "thumb_960_w": 960, "thumb_960_h": 93, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CM410DML-1c8462ee6f/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 99, "original_w": 1608, "original_h": 156, "thumb_tiny": "AwAEADDQ2AHjvzR+Jpx60lABz6mlA46mkpw6UAGPrRj60UUAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08CM410DML/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08CM410DML-f4429a63e7", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ygcxk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " From Tithely, I don't know the answer to this:"}]}]}]}, {"ts": "1739296602.076429", "text": "<@U0690EB5JE5> also check the comment re: total rewards in the tithely channel", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739296602.076429", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "ZKYSo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " also check the comment re: total rewards in the tithely channel"}]}]}]}, {"ts": "1739297019.126179", "text": "<@U07EJ2LP44S> Super admin can switch between personal view and admin view.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1739297398.000000"}, "files": [{"id": "F08CQV1TCSZ", "created": 1739297015, "timestamp": 1739297015, "name": "Screenshot 2025-02-11 at 11.31.51 PM.png", "title": "Screenshot 2025-02-11 at 11.31.51 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 168508, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08CQV1TCSZ/screenshot_2025-02-11_at_11.31.51___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08CQV1TCSZ/download/screenshot_2025-02-11_at_11.31.51___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 180, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 239, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 359, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 399, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 479, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08CQV1TCSZ-dff21f8cea/screenshot_2025-02-11_at_11.31.51___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 511, "original_w": 1917, "original_h": 956, "thumb_tiny": "AwAXADDRIyP/AK1DEgDFKBgcUhoAXP1pRzSdKWgApuOnFOpKACg470UjUAO60UDoKKACiiigD//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08CQV1TCSZ/screenshot_2025-02-11_at_11.31.51___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08CQV1TCSZ-9c601617ab", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "kRTeT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Super admin can switch between personal view and admin view."}]}]}]}, {"ts": "**********.798149", "text": "This is in my local with <PERSON>'s login when switched to her Employee Account view. And regarding click issue let me check tomorrow and get back.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "d/Klw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is in my local with <PERSON>'s login when switched to her Employee Account view. And regarding click issue let me check tomorrow and get back."}]}]}]}, {"ts": "**********.462759", "text": "<@U07EJ2LP44S> I cannot reproduce this issue locally. Please ask user to delete cookies and try again.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.438369", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "AizKw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I cannot reproduce this issue locally. Please ask user to delete cookies and try again."}]}]}]}, {"ts": "**********.263239", "text": "<@U0690EB5JE5> are you still around?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "deHa/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " are you still around?"}]}]}]}, {"ts": "**********.740229", "text": "I had a call with curana and they have a request I want to run by you (and <PERSON><PERSON><PERSON>)", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "p3+by", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I had a call with curana and they have a request I want to run by you (and <PERSON><PERSON><PERSON>)"}]}]}]}, {"ts": "1739298383.273299", "text": "I am avaiable", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/dwfp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am avaiable"}]}]}]}, {"ts": "1739298677.299989", "text": "<PERSON>- we can meet and I can run it by <PERSON><PERSON><PERSON> in the evening", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Z8See", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>- we can meet and I can run it by <PERSON><PERSON><PERSON> in the evening"}]}]}]}, {"ts": "**********.323949", "text": "I'll summarize here - they are changing their bonus payout date, so they want to run the bonus cycle separately so they can do that immediately. the merit would still happen on their normal timeline, paid out a month later. To do this we'd need to do a couple things:\n\n1. create a second account and run a bonus-only cycle in that account. we'd only put the bonus-eligible employees in there, no paybands etc, and we'd run a fast 2 week cycle to get bonuses completed. assuming we'd need to do +1@email addresses, and local logins for 150 people \n2. remove bonus from the primary account, but ideally create a new column where we could display the bonus amount that was received from the bonus cycle. read only, just a data point. ", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1vypP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'll summarize here - they are changing their bonus payout date, so they want to run the bonus cycle separately so they can do that immediately. the merit would still happen on their normal timeline, paid out a month later. To do this we'd need to do a couple things:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "create a second account and run a bonus-only cycle in that account. we'd only put the bonus-eligible employees in there, no paybands etc, and we'd run a fast 2 week cycle to get bonuses completed. assuming we'd need to do +1@email addresses, and local logins for 150 people "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "remove bonus from the primary account, but ideally create a new column where we could display the bonus amount that was received from the bonus cycle. read only, just a data point. "}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "**********.122619", "text": "also he said he'd be more than happy to pay an invoice for the cost of the second enivorment to get it done", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0wjKG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "also he said he'd be more than happy to pay an invoice for the cost of the second enivorment to get it done"}]}]}]}, {"ts": "**********.826859", "text": "will they be open to paying the cost of additional engineering work for the 2nd item? I can check with <PERSON><PERSON><PERSON> what would be", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yB9Zz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will they be open to paying the cost of additional engineering work for the 2nd item? I can check with <PERSON><PERSON><PERSON> what would be"}]}]}]}, {"ts": "1739298844.903549", "text": "It should not be more than $3K", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Os7CP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It should not be more than $3K"}]}]}]}, {"ts": "1739298883.350189", "text": "Given the size of their company, <PERSON> should not have any problem in paying this amount", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "A9pnk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Given the size of their company, <PERSON> should not have any problem in paying this amount"}]}]}]}, {"ts": "1739298892.303119", "text": "i don't know about that", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eOMXN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i don't know about that"}]}]}]}, {"ts": "1739298913.330019", "text": "ok let me work with <PERSON><PERSON><PERSON> in the evening and get back to you the estimated cost of engineering work", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/CWnZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok let me work with <PERSON><PERSON><PERSON> in the evening and get back to you the estimated cost of engineering work"}]}]}]}, {"ts": "1739298915.778199", "text": "i would think that would be harder to justify but i don't know", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rWGb+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i would think that would be harder to justify but i don't know"}]}]}]}, {"ts": "**********.316949", "text": "it's just adding a column so i'm thinking he's not going to love that", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "no0Mb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "it's just adding a column so i'm thinking he's not going to love that"}]}]}]}, {"ts": "**********.749879", "text": "it was more the $400 a month we were talking about, just the hosting fee of an account", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4TDVK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "it was more the $400 a month we were talking about, just the hosting fee of an account"}]}]}]}, {"ts": "**********.517369", "text": "yes", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IB8ze", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes"}]}]}]}, {"ts": "**********.292179", "text": "would they need the new env  to say active only for a say 2 months. or they want to keep it active as long as they have a contract with us", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "tO9D8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "would they need the new env  to say active only for a say 2 months. or they want to keep it active as long as they have a contract with us"}]}]}]}, {"ts": "**********.954519", "text": "i think one month", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OGvTg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i think one month"}]}]}]}, {"ts": "1739299086.871169", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1739299089.694299", "text": "spin it up and turn it off", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Tv0tN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "spin it up and turn it off"}]}]}]}, {"ts": "**********.794059", "text": "2 week cycle plus a buffer to get data out", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FT5g4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "2 week cycle plus a buffer to get data out"}]}]}]}, {"ts": "**********.837169", "text": "since two cycles are not possible in one account this is the only workaround", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VQ1v9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "since two cycles are not possible in one account this is the only workaround"}]}]}]}, {"ts": "**********.889149", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "**********.674049", "text": "unless mahesh has a way to run a bonus cycle concurrently with a merit cycle in the same account", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+CbBu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "unless mahesh has a way to run a bonus cycle concurrently with a merit cycle in the same account"}]}]}]}, {"ts": "**********.286129", "text": "which i'm thinking is not posible", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6Y4NJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "which i'm thinking is not posible"}]}]}]}, {"ts": "**********.079709", "text": "correct", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "j+ZXP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "correct"}]}]}]}, {"ts": "**********.332869", "text": "ok lets see how much of an eng effort it is to add a new column", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9PZcY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok lets see how much of an eng effort it is to add a new column"}]}]}]}, {"ts": "**********.463509", "text": "we will have an answer by tomorrow", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "v176H", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we will have an answer by tomorrow"}]}]}]}, {"ts": "**********.628259", "text": "he's checking with his CEO on this solution as well", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "UCk5K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "he's checking with his CEO on this solution as well"}]}]}]}, {"ts": "**********.045269", "text": "he needs to get approval since we can't do it in one cycle/account", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gV7X0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "he needs to get approval since we can't do it in one cycle/account"}]}]}]}, {"ts": "**********.891439", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "**********.880109", "text": "overall it shouldn't be too much work to manage since the second account is only ~150 people and only bonus", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "6TE5M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "overall it shouldn't be too much work to manage since the second account is only ~150 people and only bonus"}]}]}]}, {"ts": "**********.709379", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "**********.993139", "text": "i dont' know what work is involved in local logins though, if we can just run a script for that or what", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pYhtC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i dont' know what work is involved in local logins though, if we can just run a script for that or what"}]}]}]}, {"ts": "**********.225959", "text": "they'll use SSO in their main account", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XO2XC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "they'll use SSO in their main account"}]}]}]}, {"ts": "**********.896629", "text": "It's gonna need some manual effort to set up local logins but I think we can deal with that", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qVshM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It's gonna need some manual effort to set up local logins but I think we can deal with that"}]}]}]}, {"ts": "**********.892899", "text": "we'd basically kick of both cycles more or less at the same time - two weeks from now", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TR7qI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we'd basically kick of both cycles more or less at the same time - two weeks from now"}]}]}]}, {"ts": "**********.586419", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> running two cycles in parallel is not possible at this point and needs work and a lot of testing. We can go ahead with the solution proposed above but lets wait for them to come back with their CEO's approval. Regarding new column, need details what exactly this should contain, is it same as `Bonus Award` column that we have today but just read only?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.586419", "reply_count": 8, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "zvOaM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " running two cycles in parallel is not possible at this point and needs work and a lot of testing. We can go ahead with the solution proposed above but lets wait for them to come back with their CEO's approval. Regarding new column, need details what exactly this should contain, is it same as "}, {"type": "text", "text": "Bonus Award", "style": {"code": true}}, {"type": "text", "text": " column that we have today but just read only?"}]}]}]}, {"ts": "1739323370.853989", "text": "We can discuss in detail in our next meeting.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "AF4IA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can discuss in detail in our next meeting."}]}]}]}, {"ts": "1739323748.268069", "text": "<@U07EJ2LP44S>\nWe have two fields \"Employment Type\" and \"Compensation Type\".  We can hide comp type on all the views and  can use \"Employment Type\" or \"Worker Type\" to display as they like as these fields don't have any impact on the functionality. However, Its also confusing to me from the message above when they say they would do adjustments on annualized numbers. Do they mean they want all the employees to be treated like Salaried employees? If its just about \"Compensation Type\" being displayed as `Part Time` we can address that quickly by changing it to `Hourly` in the UI. Let me know.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739296532.147379", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1739323827.000000"}, "blocks": [{"type": "rich_text", "block_id": "il/Gx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "\nWe have two fields \"Employment Type\" and \"Compensation Type\".  We can hide comp type on all the views and  can use \"Employment Type\" or \"Worker Type\" to display as they like as these fields don't have any impact on the functionality. However, Its also confusing to me from the message above when they say they would do adjustments on annualized numbers. Do they mean they want all the employees to be treated like Salaried employees? If its just about \"Compensation Type\" being displayed as "}, {"type": "text", "text": "Part Time", "style": {"code": true}}, {"type": "text", "text": " we can address that quickly by changing it to "}, {"type": "text", "text": "Hourly", "style": {"code": true}}, {"type": "text", "text": " in the UI. Let me know."}]}]}]}, {"ts": "1739370057.908689", "text": "<@U0690EB5JE5> Valgenesis is looking to have the letters by 3/1. They may have a change to the Portugal one - if so when do you need that change (obviously asap but what is the date you have to have it)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739370057.908689", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "rgON0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Valgenesis is looking to have the letters by 3/1. They may have a change to the Portugal one - if so when do you need that change (obviously asap but what is the date you have to have it)"}]}]}]}, {"ts": "1739376020.375069", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> Curana’s bonus prefill issue is not actually an issue. Basically when cycle was first created with prefill set to true the adjustments were pre-filled, but after setting prefill flag to false later the already filled values will not be overwritten as expected. Currently there is no way for system to know if the values were manually input or pre-filled by system. I have reset the pre-filled values and cycle should be good now.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9", "U07EJ2LP44S"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "fwy/x", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "text", "text": "<PERSON><PERSON><PERSON>’s "}, {"type": "text", "text": "bonus "}, {"type": "text", "text": "prefill issue is not actually an issue. Basically when cycle was first created with prefill set to true the adjustments were pre-filled, but after setting prefill flag to false later the already filled values will not be overwritten as expected. Currently there is no way for system to know if the values were manually input or pre-filled by system."}, {"type": "text", "text": " I have reset the pre-filled values and cycle should be good now."}]}]}]}, {"ts": "1739392066.630219", "text": "Is this possible? Essentially the low recommendation would be the same across several ratings", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739392066.630219", "reply_count": 5, "files": [{"id": "F08D40URA3W", "created": 1739392062, "timestamp": 1739392062, "name": "IMG_4908.png", "title": "IMG_4908", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 163705, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08D40URA3W/img_4908.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08D40URA3W/download/img_4908.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_360.png", "thumb_360_w": 166, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_480.png", "thumb_480_w": 221, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_720.png", "thumb_720_w": 332, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_800.png", "thumb_800_w": 800, "thumb_800_h": 1734, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_960.png", "thumb_960_w": 443, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08D40URA3W-fd4d899d2a/img_4908_1024.png", "thumb_1024_w": 472, "thumb_1024_h": 1024, "original_w": 1179, "original_h": 2556, "thumb_tiny": "AwAwABbMooooAKKKKANNdNjLYLP+dP8A7Li/vv8AnTV1KENkq/5Cn/2pB/ck/IUAJ/ZcX99/zFH9lw/33/MUv9qQf3JPyFH9qQf3JPyFAGRRRS7WH8J/KgBKKKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08D40URA3W/img_4908.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08D40URA3W-a5b858b06b", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Mh5DW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is this possible? Essentially the low recommendation would be the same across several ratings"}]}]}]}, {"ts": "1739442529.073429", "text": "<@U07EJ2LP44S> This is Done. We changed the wording `Part Time` to `Hourly` in table views and reports.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739296532.147379", "subtype": "thread_broadcast", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S", "U07M6QKHUC9"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "FGog2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is Done. We changed the wording "}, {"type": "text", "text": "Part Time", "style": {"code": true}}, {"type": "text", "text": " to "}, {"type": "text", "text": "Hourly", "style": {"code": true}}, {"type": "text", "text": " in table views and reports."}]}]}]}, {"ts": "1739456845.337299", "text": "I am likely going to miss the meeting today. I have an urgent care visit scheduled for my daughter at 11 and it includes an xray so I'm doubtful i'll be done in time", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5", "U07M6QKHUC9"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "lnz6z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am likely going to miss the meeting today. I have an urgent care visit scheduled for my daughter at 11 and it includes an xray so I'm doubtful i'll be done in time"}]}]}]}, {"ts": "1739462237.719569", "text": "<@U0690EB5JE5> if you’re available, we can still meet", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "F06Ve", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " if you’re available, we can still meet"}]}]}]}, {"ts": "1739462439.581609", "text": "Yes we can ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rjqNn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes we can "}]}]}]}, {"ts": "1739466311.409719", "text": "For Valgenesis Letters, they would like the % change to show the merit increase change %, not the overall comp (including bonuses, for example)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739466311.409719", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "bdlci", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For Valgenesis Letters, they would like the % change to show the merit increase change %, not the overall comp (including bonuses, for example)"}]}]}]}, {"ts": "1739467326.619579", "text": "Can we provide a zip file for their letters with the letters grouped by team, like we did for Degenkolb? They will be importing them into their HRIS (HiBob)", "user": "U07EJ2LP44S", "type": "message", "edited": {"user": "U07EJ2LP44S", "ts": "1739467429.000000"}, "blocks": [{"type": "rich_text", "block_id": "L6c3X", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can we provide a zip file for their letters with the letters grouped by team, like we did for Degenkolb? They will be importing them into their HRIS (HiBob)"}]}]}]}, {"ts": "1739467906.586919", "text": "<@U07EJ2LP44S> yes thats the plan, Zip file as per hierarchy.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "17MZW", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " yes thats the plan, Zip file as per hierarchy."}]}]}]}, {"ts": "**********.144469", "text": "<@U0690EB5JE5> <@U07M6QKHUC9> <PERSON><PERSON><PERSON> wants to move forward with the separate budget cycle", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.144469", "reply_count": 14, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "YfVqQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> wants to move forward with the separate budget cycle"}]}]}]}, {"ts": "1739474866.025449", "text": "Just talked to <PERSON><PERSON><PERSON>. A few things.\n\n1. I updated ratings in the cycle builder and once I changed numbers I could not proceed. *The next button doesn't do anything now that the data is updated.* We are trying to put in the ratings I shared yesterday - I will also need this to work in the stridedemo environment.  (Also their ratings are all going to be the same regardless of location - do we still have to copy the ratings to every single country?) \n2. A few comments for TotalRewards - 1) they asked about a download, I confirmed it was coming. 2) *they asked to remove the compensation band info* 3) *they asked if we can add a percentage increase along with the $ increase* for the salary change info 3) they would like to add a ''Total increase over time' section, where it would display the total $/% increase since hire date. We thought putting this in the current comp band area would look nice. 4) they would like to add a couple more things to their benefits, like 401k matching. Also we discussed HSA/FSA in conversation and they have no match for that so its not a benefit.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739474866.025449", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "tR4Jj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just talked to <PERSON><PERSON><PERSON>. A few things.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I updated ratings in the cycle builder and once I changed numbers I could not proceed. "}, {"type": "text", "text": "The next button doesn't do anything now that the data is updated.", "style": {"bold": true}}, {"type": "text", "text": " We are trying to put in the ratings I shared yesterday - I will also need this to work in the stridedemo environment.  (Also their ratings are all going to be the same regardless of location - do we still have to copy the ratings to every single country?) "}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n2. A few comments for TotalRewards - 1) they asked about a download, I confirmed it was coming. 2) "}, {"type": "text", "text": "they asked to remove the compensation band info ", "style": {"bold": true}}, {"type": "text", "text": "3) "}, {"type": "text", "text": "they asked if we can add a percentage increase along with the $ increase", "style": {"bold": true}}, {"type": "text", "text": " for the salary change info 3) they would like to add a ''Total increase over time' section, where it would display the total $/% increase since hire date. We thought putting this in the current comp band area would look nice. 4) they would like to add a couple more things to their benefits, like 401k matching. Also we discussed HSA/FSA in conversation and they have no match for that so its not a benefit."}]}]}]}, {"ts": "**********.253309", "text": "resharing: <https://curanahealthmip.stridehr.io/organization> is ready", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.144469", "subtype": "thread_broadcast", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "5N5yW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "resharing: "}, {"type": "link", "url": "https://curanahealthmip.stridehr.io/organization"}, {"type": "text", "text": " is ready"}]}]}]}, {"ts": "**********.624009", "text": "Diversified is looking for a better way to view flagged employees, and their bonus award. So they would want to see the percentage being awarded inside the flagged employees report. Is this a change we can make?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.624009", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "Z34iv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified is looking for a better way to view flagged employees, and their bonus award. So they would want to see the percentage being awarded inside the flagged employees report. Is this a change we can make?"}]}]}]}, {"ts": "**********.866509", "text": "<@U07EJ2LP44S> We are targeting to wrap up the Total Rewards thing by end of this week. One question I have is, After incorporating their feedback, This pretty much covers their adjustment letters. Could you please check if they are still expecting adjustment letters separately? which I feel is redundant. Can we convince them to use Total rewards instead of adjustment letters.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739474866.025449", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1739766493.000000"}, "blocks": [{"type": "rich_text", "block_id": "3oIqH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We are targeting to wrap up the Total Rewards thing by end of this week. One question I have is, After incorporating their feedback, This pretty much covers their adjustment letters. Could you please check if they are still expecting adjustment letters separately? which I feel is redundant. Can we convince them to use Total rewards instead of adjustment letters."}]}]}]}, {"ts": "1739766422.713809", "text": "<@U07EJ2LP44S> Recommenders should be able to SSO if <PERSON> was. Could you please confirm the same with them?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.144469", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "5qJ71", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Recommenders should be able to SSO if <PERSON> was. Could you please confirm the same with them?"}]}]}]}, {"ts": "1739766717.917229", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> Dgoc is back online. I will not stop the ENV going forward. Just curious, Why are they still logging in after cycle is closed? I am just wondering about the use case.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1739766727.000000"}, "blocks": [{"type": "rich_text", "block_id": "BNlVN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Dgoc is back online. I will not stop the ENV going forward. Just curious, Why are they still logging in after cycle is closed? I am just wondering about the use case."}]}]}]}, {"ts": "1739767062.393409", "text": "I think they want to check something they will log in. the only reason we are disabling environments is because we want to save on the cloud costs, but it is not a standard practice to do so", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1739767062.393409", "reply_count": 2, "edited": {"user": "U07M6QKHUC9", "ts": "1739767074.000000"}, "blocks": [{"type": "rich_text", "block_id": "lI1PL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think they want to check something they will log in. the only reason we are disabling environments is because we want to save on the cloud costs, but it is not a standard "}, {"type": "text", "text": "practice "}, {"type": "text", "text": "to do so"}]}]}]}, {"ts": "1739806308.164669", "text": "<@U0690EB5JE5> Can you delete the existing cycle in stridedemo?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739806308.164669", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "rOo6R", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you delete the existing cycle in stridedemo?"}]}]}]}, {"ts": "1739810189.626809", "text": "<@U0690EB5JE5> <PERSON> at Diversified is unable to view his own team in the tasks page. He is a SuperAdmin and a recommender with 15 directs. He is also the final approver.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739810189.626809", "reply_count": 3, "edited": {"user": "U07EJ2LP44S", "ts": "1739810235.000000"}, "files": [{"id": "F08DP82V5QT", "created": 1739810187, "timestamp": 1739810187, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 26724, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DP82V5QT/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DP82V5QT/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_360.png", "thumb_360_w": 360, "thumb_360_h": 68, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_480.png", "thumb_480_w": 480, "thumb_480_h": 91, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_720.png", "thumb_720_w": 720, "thumb_720_h": 137, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_800.png", "thumb_800_w": 800, "thumb_800_h": 152, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DP82V5QT-e3e5fc4a3a/image_960.png", "thumb_960_w": 960, "thumb_960_h": 182, "original_w": 1001, "original_h": 190, "thumb_tiny": "AwAJADDROKOMdKdRQAgxjigHNLRQAUUUUAf/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DP82V5QT/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DP82V5QT-36a65ab44d", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "oB6Lb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON> at Diversified is unable to view his own team in the tasks page. He is a SuperAdmin and a recommender with 15 directs. He is also the final approver."}]}]}]}, {"ts": "1739811374.839069", "text": "Diversified: this employee's earned % had to be added after the cycle started and the numbers are not adding up:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739811374.839069", "reply_count": 8, "files": [{"id": "F08DPATEMLK", "created": 1739811372, "timestamp": 1739811372, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 34731, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DPATEMLK/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DPATEMLK/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_360.png", "thumb_360_w": 360, "thumb_360_h": 22, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_480.png", "thumb_480_w": 480, "thumb_480_h": 30, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_720.png", "thumb_720_w": 720, "thumb_720_h": 45, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_800.png", "thumb_800_w": 800, "thumb_800_h": 49, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_960.png", "thumb_960_w": 960, "thumb_960_h": 59, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DPATEMLK-033ff06697/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 63, "original_w": 2134, "original_h": 132, "thumb_tiny": "AwACADDRHWl7GkHU0djQAvajtR2o7UAHajvR2pO9AH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DPATEMLK/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DPATEMLK-faac02d4ad", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "CNFLl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified: this employee's earned % had to be added after the cycle started and the numbers are not adding up:"}]}]}]}, {"ts": "**********.879759", "text": "<@U0690EB5JE5> Can the <http://test.striderhr.io|test.striderhr.io> and stridedemo accounts currency be switched to USD? they are defaulted to canada and it doesn't look like i can edit that in the settings.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.879759", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "/66N4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can the "}, {"type": "link", "url": "http://test.striderhr.io", "text": "test.striderhr.io"}, {"type": "text", "text": " and stridedemo accounts currency be switched to USD? they are defaulted to canada and it doesn't look like i can edit that in the settings."}]}]}]}, {"ts": "**********.151789", "text": "<@U0690EB5JE5> Flags have disappeared overnight in <PERSON><PERSON><PERSON>'s account. Over 40 people who were flagged are not any longer. <PERSON> is freaking out. I sent her email to you that has the flags from yesterday. Can you address asap?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.151789", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "3GNOi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Flags have disappeared overnight in <PERSON><PERSON><PERSON>'s account. Over 40 people who were flagged are not any longer. <PERSON> is freaking out. I sent her email to you that has the flags from yesterday. Can you address asap?"}]}]}]}, {"ts": "**********.743249", "text": "<@U0690EB5JE5> Also they need another employee changed, and it's not adding up correctly: <PERSON> bonus I changed his total percentage to 15 percent yesterday but the individual performance bucket is not accurate.  His total should be 13779.05", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.743249", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "ZYsZR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Also they need another employee changed, and it's not adding up correctly: <PERSON> bonus I changed his total percentage to 15 percent yesterday but the individual performance bucket is not accurate.  His total should be 13779.05"}]}]}]}, {"ts": "**********.641669", "text": "<@U07EJ2LP44S> I am looking into the issue. Need an hour to root cause. will keep you update every hour or whenever I have an update.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.641669", "reply_count": 6, "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "rGERK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am looking into the issue. Need an hour to root cause. will keep you update every hour or whenever I have an update."}]}]}]}, {"ts": "1739894430.968549", "text": "<@U0690EB5JE5> <@U07EJ2LP44S> should we use the meeting time to let <PERSON><PERSON><PERSON> fix the bugs? I am fine either way", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XPO2N", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " should we use the meeting time to let <PERSON><PERSON><PERSON> fix the bugs? I am fine either way"}]}]}]}, {"ts": "1739894455.961349", "text": "I think I need the time to fix the issue.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "REdVX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think I need the time to fix the issue."}]}]}]}, {"ts": "1739894464.548189", "text": "<@U07M6QKHUC9>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lAMKj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}]}]}]}, {"ts": "1739894468.720909", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "**********.412569", "text": "Yes that's fine. I have another one", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Ca3Yt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes that's fine. I have another one"}]}]}]}, {"ts": "**********.655379", "text": "In it seems like all the accounts, the comp view is showing the rating number not the name. I double checked how it was set up in settings and it's right. Do we need to change something on the back end to have it show the name vs the number?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.655379", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "HtJIG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In it seems like all the accounts, the comp view is showing the rating number not the name. I double checked how it was set up in settings and it's right. Do we need to change something on the back end to have it show the name vs the number?"}]}]}]}, {"ts": "**********.180759", "text": "<@U0690EB5JE5> I am not able to log into <http://demo.stridehr.io|demo.stridehr.io>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.180759", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "AMvOv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am not able to log into "}, {"type": "link", "url": "http://demo.stridehr.io", "text": "demo.stridehr.io"}]}]}]}, {"ts": "**********.514149", "text": "<@U07EJ2LP44S> I just checked valgenesis. It is showing performance rating name", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/Mo8+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I just checked valgenesis. It is showing performance rating name"}]}]}]}, {"ts": "1739895514.577149", "text": "Thats good; <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and test. are all showing the number vs the name", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "O0hqV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thats good; <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and test. are all showing the number vs the name"}]}]}]}, {"ts": "1739895575.567639", "text": "Is it because we only upload the performance rating number and not the name? or it is a bug?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1739895575.567639", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "tYiS5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is it because we only upload the performance rating number and not the name? or it is a bug?"}]}]}]}, {"ts": "1739896033.103619", "text": "Getting errors. Can you upload this into stridedemo. Tomorrow is fine. One has all the data and the one with a 2 has only the rating column. I can't get either to upload. (I will need to do the same for T<PERSON><PERSON> demo in test.)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739896033.103619", "reply_count": 2, "edited": {"user": "U07EJ2LP44S", "ts": "1739896056.000000"}, "files": [{"id": "F08DSDVH9EZ", "created": 1739896008, "timestamp": 1739896008, "name": "CuranaDemoRatings.csv", "title": "CuranaDemoRatings.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 49297, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DSDVH9EZ/curanademoratings.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DSDVH9EZ/download/curanademoratings.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DSDVH9EZ/curanademoratings.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DSDVH9EZ-c69385d620", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DSDVH9EZ/curanademoratings.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">37</div><div class=\"cm-col\">Khoa</div><div class=\"cm-col\">Pham</div><div class=\"cm-col\">Khoa Pham</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">9/28/20</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">42</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">9/28/20</div><div class=\"cm-col\">9/28/20</div><div class=\"cm-col\">Vietnamese</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Partner Engineering Manager</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">Manager</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col\">Needs Improvement</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">133326.24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">13071</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">130712</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Raj</div><div class=\"cm-col\">Patel</div><div class=\"cm-col\">Raj Patel</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">4/6/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">206</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">4/6/21</div><div class=\"cm-col\">4/6/21</div><div class=\"cm-col\">Indian</div><div class=\"cm-col\">San Francisco</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\">Platform</div><div class=\"cm-col cm-num\">1</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">138409.95</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">13182</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">131819</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\">Juan</div><div class=\"cm-col\">Rodriguez</div><div class=\"cm-col\">Juan Rodriguez</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">5/24/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">194</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">5/24/21</div><div class=\"cm-col\">5/24/21</div><div class=\"cm-col\">Hispanic</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col\">Needs Improvement</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">155710</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">14571</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">155710</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30</div><div class=\"cm-col\">Hans</div><div class=\"cm-col\">Wagner</div><div class=\"cm-col\">Hans Wagner</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">9/6/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">206</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">9/6/22</div><div class=\"cm-col\">9/6/22</div><div class=\"cm-col\">German</div><div class=\"cm-col\">Remote-Payzone2</div><div class=\"cm-col\">Senior Software Engineer</div><div class=\"cm-col\">Platform</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">205837.32</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">13195</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">131947</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">126</div><div class=\"cm-col\">Elena</div><div class=\"cm-col\">Lopez</div><div class=\"cm-col\">Elena Lopez</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">11/1/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">12</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">11/1/21</div><div class=\"cm-col\">11/1/21</div><div class=\"cm-col\">Hispanic</div><div class=\"cm-col\">Remote-Payzone3</div><div class=\"cm-col\">Sr. Engineering Manager</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">Manager</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">FTE</div><div class=\"cm-col\"></div><div class=\"cm-col\">x</div><div class=\"cm-col\">x</div><div class=\"cm-col\">P02</div><div class=\"cm-col\">ENCR</div><div class=\"cm-col\">NA</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Amalia Berg</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\">1/1/23</div><div class=\"cm-col\">12/31/23</div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">153991.9</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">13391</div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">11/29/24</div><div class=\"cm-col cm-num\">133906</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 138, "lines_more": 137, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F08DSDVMPFF", "created": 1739896009, "timestamp": 1739896009, "name": "CuranaDemoRatings2.csv", "title": "CuranaDemoRatings2.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 3751, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DSDVMPFF/curanademoratings2.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DSDVMPFF/download/curanademoratings2.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DSDVMPFF/curanademoratings2.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DSDVMPFF-11bb469b0d", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DSDVMPFF/curanademoratings2.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,Performance Rating,Performance Comment\r\nU,37,Needs Improvement,\r\nU,6,Exceeds Expectations,\r\nU,10,Needs Improvement,\r\nU,30,Exceeds Expectations,\r\nU,126,Meets Expectations,\r\nU,168,Meets Expectations,\r\nU,231,Exceeds Expectations,\r\nU,206,Meets Expectations,\r\nU,19,Exceeds Expectations,\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">37</div><div class=\"cm-col\">Needs Improvement</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\">Needs Improvement</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">30</div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">126</div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">168</div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">231</div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">206</div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">19</div><div class=\"cm-col\">Exceeds Expectations</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 138, "lines_more": 128, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "owYsJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Getting errors. Can you upload this into stridedemo. Tomorrow is fine. One has all the data and the one with a 2 has only the rating column. I can't get either to upload. (I will need to do the same for T<PERSON><PERSON> demo in test.)"}]}]}]}, {"ts": "1739898701.301759", "text": "<@U07EJ2LP44S> I have the fixe ready. I am testing with latest diversified db in local. Will keep you posted.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.641669", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "A36/d", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have the fixe ready. I am testing with latest diversified db in local. Will keep you posted."}]}]}]}, {"ts": "1739898897.554389", "text": "<@U07EJ2LP44S> how do you feel about canceling the leadership calls and make them as-needed going forward. It looks like most of the issues can be handled asyc between you and <PERSON><PERSON><PERSON>.\n<PERSON><PERSON><PERSON> and I can use this time to meet start making progress on the other ideas.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YXEGu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " how do you feel about canceling the leadership calls and make them as-needed going forward. It looks like most of the issues can be handled asyc between you and <PERSON><PERSON><PERSON>.\n<PERSON><PERSON><PERSON> and I can use this time to meet start making progress on the other ideas."}]}]}]}, {"ts": "1739899666.845589", "text": "Sure that’s fine", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8KkO3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure "}, {"type": "text", "text": "that’s"}, {"type": "text", "text": " fine"}]}]}]}, {"ts": "1739904174.386769", "text": "Just sent an email about Curana SSO - need to investigate", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EOMzt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just sent an email about Curana SSO - need to investigate"}]}]}]}, {"ts": "1739904428.253229", "text": "Also, Curana just set their recommender level to ALL MANAGERS in the cycle - so this will be a really large cycle. If there's anything we need to do to prep for that, please lets do it!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739904428.253229", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "RfRze", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also, Curana just set their recommender level to ALL MANAGERS in the cycle - so this will be a really large cycle. If there's anything we need to do to prep for that, please lets do it!"}]}]}]}, {"ts": "1739906378.494079", "text": "<@U07EJ2LP44S> UPDATE: There few more issues we found while testing. Fixes are being deployed. Will update here once done.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.641669", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "pemIK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " UPDATE: There few more issues we found while testing. Fixes are being deployed. Will update here once done."}]}]}]}, {"ts": "1739906838.647289", "text": "Thank you; she's asking if there's any other way to filter by people who have had their attainment changed, but I think the answer is no. Unless I'm missing something?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Zv4v9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you; she's asking if there's any other way to filter by people who have had their attainment changed, but I think the answer is no. Unless I'm missing something?"}]}]}]}, {"ts": "1739906988.377399", "text": "We can comparing with org view i.e. employee report", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "t8F3S", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can comparing with org view i.e. employee report"}]}]}]}, {"ts": "1739907089.543489", "text": "not sure if we have those columns added in org view. I can generate one tomorrow or day after.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739907089.543489", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "fatpB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "not sure if we have those columns added in org view. I can generate one tomorrow or day after."}]}]}]}, {"ts": "1739907114.128789", "text": "let me know what details are required.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "p1dR/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "let me know what details are required."}]}]}]}, {"ts": "1739907484.270799", "text": "<@U07EJ2LP44S> The flags issue is fixed and published the cycle as well. There are 63 flags now.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "z+zoI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " The flags issue is fixed and published the cycle as well. There are 63 flags now."}]}]}]}, {"ts": "1739907780.694879", "text": "Will upload tomorrow", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739896033.103619", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "w4ltn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will upload tomorrow"}]}]}]}, {"ts": "1739907863.464999", "text": "<@U07EJ2LP44S> There is some UX issue in org edit which is causing this issue. Please do click on percent inputs for UI to auto calculate the numbers correctly.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.743249", "subtype": "thread_broadcast", "files": [{"id": "F08DFTABRHD", "created": 1739907859, "timestamp": 1739907859, "name": "Screenshot 2025-02-19 at 1.12.35 AM.png", "title": "Screenshot 2025-02-19 at 1.12.35 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 91985, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DFTABRHD/screenshot_2025-02-19_at_1.12.35___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DFTABRHD/download/screenshot_2025-02-19_at_1.12.35___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_360.png", "thumb_360_w": 290, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_480.png", "thumb_480_w": 387, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_720.png", "thumb_720_w": 580, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DFTABRHD-d1bdd311ca/screenshot_2025-02-19_at_1.12.35___am_800.png", "thumb_800_w": 644, "thumb_800_h": 800, "original_w": 691, "original_h": 858, "thumb_tiny": "AwAwACbRbnuRSqCO+aB1/wDrUtABRSEZ60bR6CgBaKBxRQAnUn/GlpO9LQAjf55pR05pDQOlAC0UDmigBp6//WpRRgelA+mKAA0dqZK5UjEbP9KZ5z4/1D07MCYUtRxyFiQYmT3NSUgP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DFTABRHD/screenshot_2025-02-19_at_1.12.35___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DFTABRHD-bad800f4b4", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "oas0h", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " There is some UX issue in org edit which is causing this issue. Please do click on percent inputs for UI to auto calculate the numbers correctly."}]}]}]}, {"ts": "1739924573.115709", "text": "<@U0690EB5JE5> Based on the amount of budget used (adding up total bonus awards), the system is showing an incorrect amount remaining. The budget is  240,261.57, the usage of the budget is 233651.00. That should leave 6610 but the system is showing only 3273. There a handful of prorated employees, but there's no scenario where I can come up with the right amount remaining. He should have double what he has left.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739924573.115709", "reply_count": 4, "files": [{"id": "F08EAAA4SAD", "created": 1739924354, "timestamp": 1739924354, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 113792, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08EAAA4SAD/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08EAAA4SAD/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_360.png", "thumb_360_w": 360, "thumb_360_h": 82, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_480.png", "thumb_480_w": 480, "thumb_480_h": 109, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_720.png", "thumb_720_w": 720, "thumb_720_h": 163, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_800.png", "thumb_800_w": 800, "thumb_800_h": 182, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_960.png", "thumb_960_w": 960, "thumb_960_h": 218, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EAAA4SAD-6a5788ba30/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 232, "original_w": 2220, "original_h": 504, "thumb_tiny": "AwAKADDR4z3/ACo496SlzTAOPej8/wAqSigAOcfL19waUZ4znPsKSgUAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08EAAA4SAD/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08EAAA4SAD-5627b73181", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "/2IgM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Based on the amount of budget used (adding up total bonus awards), the system is showing an incorrect amount remaining. The budget is  240,261.57, the usage of the budget is 233651.00. That should leave 6610 but the system is showing only 3273. There a handful of prorated employees, but there's no scenario where I can come up with the right amount remaining. He should have double what he has left."}]}]}]}, {"ts": "1739965673.114909", "text": "<@U07EJ2LP44S> uploaded", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739896033.103619", "subtype": "thread_broadcast", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "d<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " uploaded"}]}]}]}, {"ts": "1739974725.994419", "text": "<@U0690EB5JE5> you replied to <PERSON>‘s email from back in January but it’s <PERSON><PERSON><PERSON> who had the issue with SSO. Is it the configuration in Curana that you updated?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739974725.994419", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Yfxh9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " you replied to <PERSON>‘s email from back in January but it’s <PERSON><PERSON><PERSON> who had the issue with SSO. Is it the configuration in Curana that you updated?"}]}]}]}, {"ts": "**********.472849", "text": "<@U0690EB5JE5> I have a demo tomorrow. Can we enable <http://demo.stridehr.io|demo.stridehr.io>?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.472849", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "RI0Ba", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I have a demo tomorrow. Can we enable "}, {"type": "link", "url": "http://demo.stridehr.io", "text": "demo.stridehr.io"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1739976796.338019", "text": "It's a one off demo to an investor", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "WnrLC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It's a one off demo to an investor"}]}]}]}, {"ts": "1739977277.099479", "text": "<@U0690EB5JE5> Diversified seems to be down. <PERSON> cannot get in, it says to create a new cycle, and I get this:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739977277.099479", "reply_count": 16, "files": [{"id": "F08DUMG60TG", "created": 1739977272, "timestamp": 1739977272, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 89537, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DUMG60TG/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DUMG60TG/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_360.png", "thumb_360_w": 360, "thumb_360_h": 216, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_480.png", "thumb_480_w": 480, "thumb_480_h": 288, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_720.png", "thumb_720_w": 720, "thumb_720_h": 433, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_800.png", "thumb_800_w": 800, "thumb_800_h": 481, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_960.png", "thumb_960_w": 960, "thumb_960_h": 577, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DUMG60TG-241cf9d81d/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 615, "original_w": 1754, "original_h": 1054, "thumb_tiny": "AwAcADDSpKWigBOlLmkpCRnmgB2aM0gOaWgAooooAKayKxyygn3p1FACABRgDApaKKAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DUMG60TG/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DUMG60TG-75b0cd046f", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "lZhgB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Diversified seems to be down. <PERSON> cannot get in, it says to create a new cycle, and I get this:"}]}]}]}, {"ts": "1739977333.453979", "text": "Let me check some depoyment issue probably ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ka4Op", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me check some depoyment "}, {"type": "text", "text": "issue probably "}]}]}]}, {"ts": "1739977343.954059", "text": "30 mnts will check ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "prCfU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "30 mnts will check "}]}]}]}, {"ts": "1739980228.234569", "text": "<@U0690EB5JE5> can you turn on another environment for me to run a demo in? I now need a bonus-only cycle for the Curana demo.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739980228.234569", "reply_count": 17, "blocks": [{"type": "rich_text", "block_id": "BBwe+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you turn on another environment for me to run a demo in? I now need a bonus-only cycle for the Curana demo."}]}]}]}, {"ts": "1739980263.587899", "text": "<@U0690EB5JE5> Tith<PERSON> says it's fine to just use total rewards as long as it's downloadable and with those changes they asked for", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739980263.587899", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "G0zPd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> says it's fine to just use total rewards as long as it's downloadable and with those changes they asked for"}]}]}]}, {"ts": "1739980795.218829", "text": "<@U0690EB5JE5> Diversified down again", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IkxY+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Diversified down again"}]}]}]}, {"ts": "1739980817.526249", "text": "She was trying to export the merit table while impersonating but it gave her errors then it crashed", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "kgxP3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She was trying to export the merit table while impersonating but it gave her errors then it crashed"}]}]}]}, {"ts": "1739980857.765099", "text": "I think the comments column change we made today is causing the issue. Let me rollback the change for time being.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rW1XP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think the comments column change we made today is causing the issue. Let me rollback the change for time being."}]}]}]}, {"ts": "1739981523.477589", "text": "<@U07EJ2LP44S> Can you please check with her who she was impersonating?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PhJG4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Can you please check with her who she was impersonating?"}]}]}]}, {"ts": "1739981533.721819", "text": "I am unable to reproduce the issue locally", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YWl3i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am unable to reproduce the issue locally"}]}]}]}, {"ts": "1739981697.286449", "text": "She was impersonating <PERSON>, I also didn't try to reproduce. She just said she's been impersonating then downloading and it was workign for everyone until this morning, so probably the comments thing?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HoqIf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She was impersonating <PERSON>, I also didn't try to reproduce. She just said she's been impersonating then downloading and it was workign for everyone until this morning, so probably the comments thing?"}]}]}]}, {"ts": "1739981722.339159", "text": "Everything seems to be ok now so if we can not touch it that would be great", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LCx8R", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Everything seems to be ok now so if we can not touch it that would be great"}]}]}]}, {"ts": "1739981760.854149", "text": "Ok I am rolling back the comments thing just in case. I will dig in deeper tomorrow on the comments part.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739981760.854149", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "zi8Tq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok I am rolling back the comments thing just in case. I will dig in deeper tomorrow on the comments part."}]}]}]}, {"ts": "1739981785.522929", "text": "I am getting the Curana performance ratings ready for upload; I'll give it a shot but if it doesn't work i'll send to you", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "RfqX2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am getting the Curana performance ratings ready for upload; I'll give it a shot but if it doesn't work i'll send to you"}]}]}]}, {"ts": "**********.559549", "text": "This is to the main curanahealth account, the only thing I changed was update type and the performance rating name", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8Eo5I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is to the main curanahealth account, the only thing I changed was update type and the performance rating name"}]}]}]}, {"ts": "**********.931099", "text": "<@U07EJ2LP44S> Rolled back the comments column change. Things should be in the same state as yesterday.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3KP0g", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Rolled back the comments column change. Things should be in the same state as yesterday."}]}]}]}, {"ts": "**********.538559", "text": "<@U07M6QKHUC9> <http://demo.stridehr.io|demo.stridehr.io>  is up now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.472849", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "RrSx2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://demo.stridehr.io", "text": "demo.stridehr.io"}, {"type": "text", "text": "  is up now."}]}]}]}, {"ts": "1739984435.437539", "text": "<@U0690EB5JE5> If we use the range functionality for recommendations, is it taking the middle value to estimate budgets? Or the high value?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739984435.437539", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "Nr3rv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " If we use the range functionality for recommendations, is it taking the middle value to estimate budgets? Or the high value?"}]}]}]}, {"ts": "1739986529.116449", "text": "<@U0690EB5JE5> There's a bug/use case issue with the market adjustment for Curana. If you put in a market adjustment and it pushes the employee beyond the threshold (so for example from .57 to .61), the market adjustment then becomes uneditable. We should be able to edit the market adjustment until submission.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739986529.116449", "reply_count": 3, "files": [{"id": "F08DMTNV7ST", "created": 1739986526, "timestamp": 1739986526, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 26570, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DMTNV7ST/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DMTNV7ST/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_360.png", "thumb_360_w": 360, "thumb_360_h": 103, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_480.png", "thumb_480_w": 480, "thumb_480_h": 137, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_720.png", "thumb_720_w": 720, "thumb_720_h": 206, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DMTNV7ST-547550c394/image_800.png", "thumb_800_w": 800, "thumb_800_h": 229, "original_w": 916, "original_h": 262, "thumb_tiny": "AwANADDSP1NGP9o0EZpMD3oAX8aWkxRigBaKTHNGKAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DMTNV7ST/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DMTNV7ST-7a517721ec", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "WHfCq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " There's a bug/use case issue with the market adjustment for Curana. If you put in a market adjustment and it pushes the employee beyond the threshold (so for example from .57 to .61), the market adjustment then becomes uneditable. We should be able to edit the market adjustment until submission."}]}]}]}, {"ts": "**********.382919", "text": "<@U0690EB5JE5> the other thing we need to ensure is that SSO will work for both accounts for Kia. They have a different set of recommenders. But I believe all the recommenders in the MIP account are also recommenders in the main account. It’s just more limited. Also, since they opened up the main cycle to all managers, do we need to re-assess the list of SSO folks in the primary account?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eEdKy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " the other thing we need to ensure is that SSO will work for both accounts for Kia. They have a different set of recommenders. But I believe all the recommenders in the MIP account are also recommenders in the main account. It’s just more limited. Also, since they opened up the main cycle to all managers, do we need to re-assess the list of SSO folks in the primary account?"}]}]}]}, {"ts": "**********.230549", "text": "I think SSO should work for all recommenders", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GJOGc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think SSO should work for all recommenders"}]}]}]}, {"ts": "**********.454379", "text": "<@U0690EB5JE5> Everything appears to be down right now:", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F08E8DKDD0B", "created": **********, "timestamp": **********, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 155235, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08E8DKDD0B/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08E8DKDD0B/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_360.png", "thumb_360_w": 360, "thumb_360_h": 193, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_480.png", "thumb_480_w": 480, "thumb_480_h": 257, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_720.png", "thumb_720_w": 720, "thumb_720_h": 386, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_800.png", "thumb_800_w": 800, "thumb_800_h": 429, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_960.png", "thumb_960_w": 960, "thumb_960_h": 514, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E8DKDD0B-07f48a1dde/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 549, "original_w": 1202, "original_h": 644, "thumb_tiny": "AwAZADDSppJzwRj6UN16UnHpQAuT6j8qMn1H5UlFACgnPJH5U6mcelKvXpQApGaTbTqKAG7TRtNOooAbtpQMUtFAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08E8DKDD0B/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08E8DKDD0B-65e91195ba", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Grm+I", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Everything appears to be down right now:"}]}]}]}, {"ts": "1740065959.235469", "text": "Which ENV is this?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DErKs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Which ENV is this?"}]}]}]}, {"ts": "1740065981.581669", "text": "All of them", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BRPCd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All of them"}]}]}]}, {"ts": "1740065996.885049", "text": "I have tried test, curana, tithely, valgenesis. No reports fro customers yet but I cannot get in in Safari or Chrome", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3THVL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have tried test, curana, tithely, valgenesis. No reports fro customers yet but I cannot get in in Safari or Chrome"}]}]}]}, {"ts": "1740066014.520519", "text": "Let me check ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FMP3J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me check "}]}]}]}, {"ts": "1740066022.470419", "text": "This is the chrome error", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F08E1TZ652S", "created": 1740066019, "timestamp": 1740066019, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 39897, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08E1TZ652S/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08E1TZ652S/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_360.png", "thumb_360_w": 360, "thumb_360_h": 236, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_480.png", "thumb_480_w": 480, "thumb_480_h": 315, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_720.png", "thumb_720_w": 720, "thumb_720_h": 473, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08E1TZ652S-984283cc88/image_800.png", "thumb_800_w": 800, "thumb_800_h": 525, "original_w": 810, "original_h": 532, "thumb_tiny": "AwAfADDTPAzTVbd2INOpFUL0oAWiiigAooooAKj2Jz8rVJSY9zQAzYnHytx9aNif3W5+tPx7mjHuaAI/LT+635mpaKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08E1TZ652S/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08E1TZ652S-d1e4ee93d5", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "1orET", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is the chrome error"}]}]}]}, {"ts": "1740066084.748199", "text": "I can login. Can you check your internet once?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WcQFY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can login. Can you check your internet once?"}]}]}]}, {"ts": "1740066158.233659", "text": "everythign else is working", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "USPcF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "everythign else is working"}]}]}]}, {"ts": "1740066168.539789", "text": "i'll clear cookies", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "z+XTd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i'll clear cookies"}]}]}]}, {"ts": "1740066222.436579", "text": "Nothing. I even tried incognito", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "swdzW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Nothing. I even tried incognito"}]}]}]}, {"ts": "1740066243.962259", "text": "It doesn't sit and churn or anything it just goes straight to a server not found", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fey4F", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It doesn't sit and churn or anything it just goes straight to a server not found"}]}]}]}, {"ts": "1740066396.609459", "text": "interesting. Try switching wifi if you have more than one at home.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Hz7qG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "interesting. Try switching wifi if you have more than one at home."}]}]}]}, {"ts": "1740066424.571259", "text": "restart laptop once", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xpUuC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "restart laptop once"}]}]}]}, {"ts": "1740066479.824519", "text": "i switched wifi, nothing. i am able to stream video and open literally any page but ours", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qgjdR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i switched wifi, nothing. i am able to stream video and open literally any page but ours"}]}]}]}, {"ts": "1740066492.437069", "text": "I will restart, gimme 5.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/K/cs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will restart, gimme 5."}]}]}]}, {"ts": "1740067251.515329", "text": "Ok I'm back on, and I've been able to get most things to load", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "54wH5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok I'm back on, and I've been able to get most things to load"}]}]}]}, {"ts": "1740067308.539259", "text": "I cannot get <PERSON><PERSON><PERSON> to load on <PERSON><PERSON> (it just takes me back to the login screen over and over again) but I can get every other environment", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "a1B6r", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I cannot get <PERSON><PERSON><PERSON> to load on <PERSON><PERSON> (it just takes me back to the login screen over and over again) but I can get every other environment"}]}]}]}, {"ts": "1740067320.976799", "text": "But I DID get stridedemo on chrome so I will use that for the training today", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MncbQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "But I DID get stridedemo on chrome so I will use that for the training today"}]}]}]}, {"ts": "1740067458.209509", "text": "Ok cool.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "w+VhM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok cool."}]}]}]}, {"ts": "1740067806.998869", "text": "It stopped working again but my husband just told me he's been having network problems so clearly its us. :confused:", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IjwyG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It stopped working again but my husband just told me he's been having network problems so clearly its us. "}, {"type": "emoji", "name": "confused", "unicode": "1f615"}]}]}]}, {"ts": "1740067904.655569", "text": "yes I thought so.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sVC4N", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes I thought so."}]}]}]}, {"ts": "1740068217.369589", "text": "Yes it would have been <PERSON><PERSON><PERSON> if he would have told me :face_with_rolling_eyes:", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "CM24Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes it would have been GRE<PERSON> if he would have told me "}, {"type": "emoji", "name": "face_with_rolling_eyes", "unicode": "1f644"}]}]}]}, {"ts": "1740068240.920249", "text": "He doesn't usually work from here and he started futzing with things and didn't bother to say so", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VaBJh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "He doesn't usually work from here and he started futzing with things and didn't bother to say so"}]}]}]}, {"ts": "1740068271.323419", "text": "He reset the routers and it messed up all the DNS stuff", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5s2SE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "He reset the routers and it messed up all the DNS stuff"}]}]}]}, {"ts": "1740068298.847859", "text": "Why everything but our Stride environments loaded is a mystery to me though", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ckiut", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Why everything but our Stride environments loaded is a mystery to me though"}]}]}]}, {"ts": "1740068640.389279", "text": "Thats depends on how AWS network works.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yu/Mm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thats depends on how AWS network works."}]}]}]}, {"ts": "**********.973219", "text": "We are hosted on  Amazon AWS cloud. I don't have much insights into that.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ktL8k", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We are hosted on  Amazon AWS cloud. I don't have much insights into that."}]}]}]}, {"ts": "**********.514879", "text": "<@U0690EB5JE5> We need to upload the Curana performance ratings into the MIP account as well.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.514879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "xLTRA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We need to upload the Curana performance ratings into the MIP account as well."}]}]}]}, {"ts": "**********.256489", "text": "Do you want me to prep the document to only include people with a bonus to upload, or can you do that as you upload the doc?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "tgp+P", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you want me to prep the document to only include people with a bonus to upload, or can you do that as you upload the doc?"}]}]}]}, {"ts": "**********.289779", "text": "I can take care of that. Can it wait till tomorrow?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gliFJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can take care of that. Can it wait till tomorrow?"}]}]}]}, {"ts": "**********.605649", "text": "Yes, can it be done by the time I get in?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "wSBxO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, can it be done by the time I get in?"}]}]}]}, {"ts": "1740071114.144469", "text": "We need time to adjust the recommendations/budget info", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jUM+Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We need time to adjust the recommendations/budget info"}]}]}]}, {"ts": "1740071115.841569", "text": "yes sure, Will first thing my morning.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "A+19M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes sure, Will first thing my morning."}]}]}]}, {"ts": "1740110258.720519", "text": "<@U07EJ2LP44S> This is done. Please let me know if anything missed. There are few employees missing ratings.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.514879", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "xZAir", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is done. Please let me know if anything missed. There are few employees missing ratings."}]}]}]}, {"ts": "1740110871.824349", "text": "<@U07EJ2LP44S> We will be wrapping up all the work by mid of next week (delayed by few days). Please follow up with Tithely for updated benefits data.\ncc: <@U07MH77PUBV>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739980263.587899", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "keV85", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We will be wrapping up all the work by mid of next week (delayed by few days). Please follow up with <PERSON><PERSON><PERSON> for updated benefits data.\ncc: "}, {"type": "user", "user_id": "U07MH77PUBV"}]}]}]}, {"ts": "1740152174.247849", "text": "<@U07EJ2LP44S> Please find adjustment letters for Val<PERSON> with current cycle state. Please let me know if any issues.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739980228.234569", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "xMptv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please find adjustment letters for <PERSON><PERSON> with current cycle state. Please let me know if any issues."}]}]}]}, {"ts": "1740152251.965019", "text": "Question: Should we generate letters for promotion cases as the increment adjustments are not upload?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "awOYj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Question: Should we generate letters for promotion cases as the increment adjustments are not upload?"}]}]}]}, {"ts": "1740156409.872589", "text": "Just FYI, I met with the ELT at Curana, and they decided NOT to use the market adjustment field after all. However, <PERSON><PERSON><PERSON> wants to use it (with the compa ratio feature) so it was still worth doing. :slightly_smiling_face:", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "u9eLq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just FYI, I met with the ELT at Curana, and they decided NOT to use the market adjustment field after all. However, <PERSON><PERSON><PERSON> wants to use it (with the compa ratio feature) so it was still worth doing. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1740156469.924829", "text": "Val<PERSON> seems to be pretty active but haven’t reported any issues. It feels too good to be true :smile: ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "UA+64", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Val<PERSON> seems to be pretty active but "}, {"type": "text", "text": "haven’t"}, {"type": "text", "text": " reported any issues. It feels too good to be true "}, {"type": "emoji", "name": "smile", "unicode": "1f604"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1740156574.295109", "text": "SHHHHHH MAHESH", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "smile", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "GY0LP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SHHHHHH MAHESH"}]}]}]}, {"ts": "**********.614159", "text": "Curana starts Monday with their main cycle, and Tuesday with their MIP cycle. They may add a few more recommenders to the MIP; will we need to do anything for SSO? Anything else we need to do for SSO in the main account? There are so many recommenders now.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zmiAz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Curana starts Monday with their main cycle, and Tuesday with their MIP cycle. They may add a few more recommenders to the MIP; will we need to do anything for SSO? Anything else we need to do for SSO in the main account? There are so many recommenders now."}]}]}]}, {"ts": "**********.869339", "text": "SSO works if role is assigned and manager role is assigned automatically when cycle is published. So we should be good there.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "LF2sx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SSO works if role is assigned and manager role is assigned automatically when cycle is published. So we should be good there."}]}]}]}, {"ts": "**********.662949", "text": "Ok! I'm trying to anticipate what could blow up our days on Monday and Tuesday :see_no_evil:", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WjNqH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok! I'm trying to anticipate what could blow up our days on Monday and Tuesday "}, {"type": "emoji", "name": "see_no_evil", "unicode": "1f648"}]}]}]}, {"ts": "**********.607869", "text": "If Valgenesis doing well then Curana would be in the same state.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "iPLGQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If Valgenesis doing well then Curana would be in the same state."}]}]}]}, {"ts": "**********.117169", "text": "at least the main account. Bonus is extensively used this time unlike last year where we might expect some bugs.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Lc36R", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "at least the main account. Bonus is extensively used this time unlike last year where we might expect some bugs."}]}]}]}, {"ts": "**********.427929", "text": "Diversified is doing fine with bonus for the most part so hopefully we caught and fixed any bugs with that one already.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "f8bT7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified is doing fine with bonus for the most part so hopefully we caught and fixed any bugs with that one already."}]}]}]}, {"ts": "**********.843209", "text": "yes hopefully", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "aAga7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes hopefully"}]}]}]}, {"ts": "**********.101529", "text": "Valgenesis:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.101529", "reply_count": 15, "files": [{"id": "F08EDM3DHRB", "created": 1740160084, "timestamp": 1740160084, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 569402, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08EDM3DHRB/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08EDM3DHRB/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_360.png", "thumb_360_w": 360, "thumb_360_h": 247, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_480.png", "thumb_480_w": 480, "thumb_480_h": 330, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_720.png", "thumb_720_w": 720, "thumb_720_h": 494, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_800.png", "thumb_800_w": 800, "thumb_800_h": 549, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_960.png", "thumb_960_w": 960, "thumb_960_h": 659, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDM3DHRB-a38b46a7b2/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 703, "original_w": 1806, "original_h": 1240, "thumb_tiny": "AwAgADDQ2e5p2D60EZPU0bfc0AGD60Y96McdTRj3NABj3NLijHuaKAEIyen60mPb9aGOD3pM/WgBcYHIpePSk6jnNKFFABgelLgelGMUUAf/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08EDM3DHRB/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08EDM3DHRB-673d50f774", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "GxYJn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis:"}]}]}]}, {"ts": "**********.657039", "text": "<@U0690EB5JE5> Can you update the scores in both Curana accounts with this file? They had some additions and updates for ratings.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.657039", "reply_count": 2, "files": [{"id": "F08FAKQ969W", "created": **********, "timestamp": **********, "name": "CuranaUpdatedScores.csv", "title": "CuranaUpdatedScores.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 9467, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FAKQ969W/curanaupdatedscores.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FAKQ969W/download/curanaupdatedscores.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FAKQ969W/curanaupdatedscores.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FAKQ969W-ecc3194a70", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FAKQ969W/curanaupdatedscores.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">531</div><div class=\"cm-col\">JOSHUA</div><div class=\"cm-col\">DUIMSTRA</div><div class=\"cm-col\">JOSHUA DUIMSTRA</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">7/27/20</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">71</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">7/27/20</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">VA</div><div class=\"cm-col\">Account Manager</div><div class=\"cm-col\">ASC Administration</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">70000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/22/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">1012</div><div class=\"cm-col\">STEVEN</div><div class=\"cm-col\">HALS</div><div class=\"cm-col\">STEVEN HALS</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">10/4/21</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">2726</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">10/4/21</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">MN</div><div class=\"cm-col\">FP&amp;A Manager</div><div class=\"cm-col\">Finance &amp; Accounting</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Shared Services</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">150000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col cm-num\">22500</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">22500</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">22500</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">10/18/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">1406</div><div class=\"cm-col\">RUJUTA</div><div class=\"cm-col\">PARIKH</div><div class=\"cm-col\">RUJUTA PARIKH</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">1/3/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">3907</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/3/22</div><div class=\"cm-col\"></div><div class=\"cm-col\">Asian</div><div class=\"cm-col\">VA</div><div class=\"cm-col\">AP Accountant</div><div class=\"cm-col\">Finance &amp; Accounting</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Shared Services</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">58916</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/22/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">1500</div><div class=\"cm-col\">ERIK</div><div class=\"cm-col\">WITTBOLD</div><div class=\"cm-col\">ERIK WITTBOLD</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">1/3/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">1002</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/3/22</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">MI</div><div class=\"cm-col\">Chief Development Officer</div><div class=\"cm-col\">Corporate Development</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Med Grp Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">234000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">32.05</div><div class=\"cm-col cm-num\">75000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">75000</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">75000</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/22/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">1661</div><div class=\"cm-col\">SHERRIKA</div><div class=\"cm-col\">EVANS</div><div class=\"cm-col\">SHERRIKA EVANS</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">9/5/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">1999</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">12/10/18</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">TN</div><div class=\"cm-col\">Director of Clinical Operations</div><div class=\"cm-col\">Clinical Operations</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">APP</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">135850</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">25</div><div class=\"cm-col cm-num\">33962.5</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">33962.5</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">33962.5</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/22/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 26, "lines_more": 25, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "j1wMX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you update the scores in both Curana accounts with this file? They had some additions and updates for ratings."}]}]}]}, {"ts": "**********.471169", "text": "<@U0690EB5JE5> brian at curana getting this when trying to login", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.471169", "reply_count": 5, "files": [{"id": "F08ETTV2EKC", "created": **********, "timestamp": **********, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 16196, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08ETTV2EKC/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08ETTV2EKC/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ETTV2EKC-fe1015a23c/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ETTV2EKC-fe1015a23c/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ETTV2EKC-fe1015a23c/image_360.png", "thumb_360_w": 360, "thumb_360_h": 117, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ETTV2EKC-fe1015a23c/image_480.png", "thumb_480_w": 480, "thumb_480_h": 156, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ETTV2EKC-fe1015a23c/image_160.png", "original_w": 684, "original_h": 222, "thumb_tiny": "AwAPADClTlUtnkDHqcUvlP8A3f1pQki9F/lTATyzj7y/99Cjyz/eT/vqnbZfT+VG2X0/lQAxlKjOVP0OabUhSQjBX+VJ5T/3f1oA/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08ETTV2EKC/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08ETTV2EKC-46ebfe6453", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ri47V", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " brian at curana getting this when trying to login"}]}]}]}, {"ts": "1740431930.726509", "text": "Tithely updated all their benefits data - they have decided to reflect the next years numbers vs the previous years, so the data has changed. Attached.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740431930.726509", "reply_count": 18, "files": [{"id": "F08EDEXD4SK", "created": 1740431927, "timestamp": 1740431927, "name": "Stride Total Rewards 2.25.xlsx", "title": "Stride Total Rewards 2.25.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 39601, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08EDEXD4SK/stride_total_rewards_2.25.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08EDEXD4SK/download/stride_total_rewards_2.25.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDEXD4SK-dea481c717/stride_total_rewards_2.25_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDEXD4SK-dea481c717/stride_total_rewards_2.25_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08EDEXD4SK/stride_total_rewards_2.25.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08EDEXD4SK-28568d94a3", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "BVJ6g", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Tithely updated all their benefits data - they have decided to reflect the next years numbers vs the previous years, so the data has changed. Attached."}]}]}]}, {"ts": "**********.181109", "text": "<@U07EJ2LP44S> what's the expected completion date for DE and Curana?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.181109", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "U4/KX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " what's the expected completion date for DE and Curana?"}]}]}]}, {"ts": "**********.356239", "text": "<@U0690EB5JE5> <PERSON><PERSON><PERSON> is going to make a few more data changes - in the MIP account they are changing salaries (they have a whole proration thing so they're hacking the system a bit), and they have a few more ratings to add. If there aren't too many ratings I can update them in the org view (though I'm not sure that will work for bonus cycle?). But expect there may be a few files coming I will likely need help with an ideally uploaded today. I told them there's some urgency to get them in in the next hour or so.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.356239", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "SDdMj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> is going to make a few more data changes - in the MIP account they are changing salaries (they have a whole proration thing so they're hacking the system a bit), and they have a few more ratings to add. If there aren't too many ratings I can update them in the org view (though I'm not sure that will work for bonus cycle?). But expect there may be a few files coming I will likely need help with an ideally uploaded today. I told them there's some urgency to get them in in the next hour or so."}]}]}]}, {"ts": "**********.519829", "text": "<@U0690EB5JE5> Tithely Terminations (still getting formatting error on everything I upload). Can you upload?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.519829", "reply_count": 6, "files": [{"id": "F08F0U47X8S", "created": **********, "timestamp": **********, "name": "TithelyTerms.csv", "title": "TithelyTerms.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 2504, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F0U47X8S/tithelyterms.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F0U47X8S/download/tithelyterms.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F0U47X8S/tithelyterms.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F0U47X8S-aabf979586", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F0U47X8S/tithelyterms.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">57</div><div class=\"cm-col\">Cory</div><div class=\"cm-col\">Wadstrom</div><div class=\"cm-col\">Cory Wadstrom</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">2/4/19</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">18</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">2/4/19</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">AR</div><div class=\"cm-col\">Sales Operations Manager</div><div class=\"cm-col\">Sales</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">135000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">4/1/22</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">321</div><div class=\"cm-col\">John</div><div class=\"cm-col\">Powers</div><div class=\"cm-col\">John Powers</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">4/24/23</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">31</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">4/24/23</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">CA</div><div class=\"cm-col\">CFO</div><div class=\"cm-col\">Executive</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">322400</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/25/24</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">129</div><div class=\"cm-col\">Paulina</div><div class=\"cm-col\">Arias</div><div class=\"cm-col\">Paulina Arias</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">8/3/20</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">26</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">8/3/20</div><div class=\"cm-col\"></div><div class=\"cm-col\">BLACK_OR_AFRICAN_AMERICAN</div><div class=\"cm-col\">NY</div><div class=\"cm-col\">Lead UI / UX Designer</div><div class=\"cm-col\">Product Management</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">100000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">7/1/24</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">306</div><div class=\"cm-col\">Rudolph</div><div class=\"cm-col\">Abrot</div><div class=\"cm-col\">Rudolph Abrot</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">12/5/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">137</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">12/5/22</div><div class=\"cm-col\"></div><div class=\"cm-col\">Asian</div><div class=\"cm-col\">CA</div><div class=\"cm-col\">Sr. Web Design &amp; Developer</div><div class=\"cm-col\">Marketing</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">153000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/25/24</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">358</div><div class=\"cm-col\">Maame</div><div class=\"cm-col\">Richardson</div><div class=\"cm-col\">Maame Richardson</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">U</div><div class=\"cm-col\">1/13/25</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">200</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/13/25</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">CA</div><div class=\"cm-col\">Operations Specialist</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\">No</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Part Time</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">56160</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">1/2/25</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">27</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 6, "lines_more": 5, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "DVF43", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Tithely Terminations (still getting formatting error on everything I upload). Can you upload?"}]}]}]}, {"ts": "**********.947339", "text": "We need to remove the last raise date for <PERSON><PERSON> in the primary Curana account. Can you upload this <@U0690EB5JE5>  It doesn't look like I can change it in the application.", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F08F2DYKCBU", "created": **********, "timestamp": **********, "name": "PlowmanDeleteLastRaiseDate.csv", "title": "PlowmanDeleteLastRaiseDate.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1550, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F2DYKCBU/plowmandeletelastraisedate.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F2DYKCBU/download/plowmandeletelastraisedate.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F2DYKCBU/plowmandeletelastraisedate.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F2DYKCBU-d3c9a43f98", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F2DYKCBU/plowmandeletelastraisedate.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U </div><div class=\"cm-col cm-num\">2994</div><div class=\"cm-col\">KAYLEE</div><div class=\"cm-col\">PLOWMAN</div><div class=\"cm-col\">KAYLEE PLOWMAN</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">1/30/23</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">8122</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/30/23</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">TN</div><div class=\"cm-col\">Marketing Coordinator</div><div class=\"cm-col\">Marketing</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Shared Services</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">50000.08</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 2, "lines_more": 1, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "yLGwI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We need to remove the last raise date for <PERSON><PERSON> in the primary Curana account. Can you upload this "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "  It doesn't look like I can change it in the application."}]}]}]}, {"ts": "**********.383749", "text": "<@U0690EB5JE5> Here's an updated file for Curana MIP only. I'm attaching directly what he sent me; I'm not sure what rows are updated but I know it was 50+ people getting their salary and bonus $ changed.", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F08F4HD160L", "created": **********, "timestamp": **********, "name": "Curana Health_EmployeeDataTemplate_202502251506_utc.xlsx", "title": "Curana Health_EmployeeDataTemplate_202502251506_utc.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 112398, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F4HD160L/curana_health_employeedatatemplate_202502251506_utc.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F4HD160L/download/curana_health_employeedatatemplate_202502251506_utc.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4HD160L-914c1925e3/curana_health_employeedatatemplate_202502251506_utc_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4HD160L-914c1925e3/curana_health_employeedatatemplate_202502251506_utc_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F4HD160L/curana_health_employeedatatemplate_202502251506_utc.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F4HD160L-eb3378b1d6", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "a/52+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Here's an updated file for Curana MIP only. I'm attaching directly what he sent me; I'm not sure what rows are updated but I know it was 50+ people getting their salary and bonus $ changed."}]}]}]}, {"ts": "**********.494759", "text": "Sure <@U07EJ2LP44S> will take care of all the data updates today.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "d2tnE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " will take care of all the data updates today."}]}]}]}, {"ts": "**********.455499", "text": "One more - <PERSON><PERSON><PERSON> just sent me this file. These are employees that need to be added to the account. The ones in red we can skip as they aren't elibile. I just got this and cannot format tonight. Sending over for your help! This is the main account.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.455499", "reply_count": 1, "files": [{"id": "F08F36DPZR8", "created": **********, "timestamp": **********, "name": "Add_Employees_Curana-Stride 01.23 (1).xlsx", "title": "Add_Employees_Curana-Stride 01.23 (1).xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 1513730, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F36DPZR8/add_employees_curana-stride_01.23__1_.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F36DPZR8/download/add_employees_curana-stride_01.23__1_.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F36DPZR8-d2997504fe/add_employees_curana-stride_01.23__1__converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F36DPZR8-d2997504fe/add_employees_curana-stride_01.23__1__thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F36DPZR8/add_employees_curana-stride_01.23__1_.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F36DPZR8-758683ddd8", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ooMVU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "One more - <PERSON><PERSON><PERSON> just sent me this file. These are employees that need to be added to the account. The ones in red we can skip as they aren't elibile. I just got this and cannot format tonight. Sending over for your help! This is the main account."}]}]}]}, {"ts": "**********.185639", "text": "Will take care ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EjPOt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take care "}]}]}]}, {"ts": "**********.260599", "text": "All the employees except \"*<PERSON>\" deleted. This employee is a manager and has two reportees. System will allow only after update manager of the reporting employees.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.519829", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "PGDjZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "All the employees except \""}, {"type": "text", "text": "<PERSON>", "style": {"bold": true}}, {"type": "text", "text": "\" deleted. This employee is a manager and has two reportees. System will allow only after update manager of the reporting employees."}]}]}]}, {"ts": "1740547180.129609", "text": "<@U07EJ2LP44S> Couple of questions with the data\n• We have only employer values. Should we show only employer contributions?\n• And the amounts are \"monthly\"/ \"weekly\"/ Half yearly  (looks like annual though just need confirmation? Do we need to show annual numbers or as is from the file?\n• Also should we include internet stipend as \"Other Cateogry?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "/K6j+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Couple of questions with the data\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We have only employer values. Should we show only employer contributions?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "And the amounts are \"monthly\"/ \"weekly\"/ Half yearly  (looks like annual though just need confirmation? Do we need to show annual numbers or as is from the file?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Also should we include internet stipend as \"Other Cateogry?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1740547477.180219", "text": "cc: <@U07MH77PUBV>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "j4uwO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "cc: "}, {"type": "user", "user_id": "U07MH77PUBV"}]}]}]}, {"ts": "1740565946.104409", "text": "<@U07EJ2LP44S> All data updates are taken care except for <PERSON><PERSON><PERSON>'s one employee termination.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lI8GC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " All data updates are taken care except for <PERSON><PERSON><PERSON>'s one employee termination."}]}]}]}, {"ts": "1740565974.454709", "text": "Please confirm on Curana updates.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "s2cBE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please confirm on Curana updates."}]}]}]}, {"ts": "1740568321.488139", "text": "<@U07EJ2LP44S> We have addressed the feedback and updated the ENV with data shared above. There are some minor nitpicks we are addressing but should be good to share with customer for initial feedback. We may have to update the data gain based on the answers to the questions above.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "subtype": "thread_broadcast", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "KyIvM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We have addressed the feedback and updated the ENV with data shared above. There are some minor nitpicks we are addressing but should be good to share with customer for initial feedback. We may have to update the data gain based on the answers to the questions above."}]}]}]}, {"ts": "1740580446.530429", "text": "<@U0690EB5JE5> Valgenesis is ready for letters! If those changes are done from their feedback we should be good.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740580446.530429", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "PZ5uq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Valgenesis is ready for letters! If those changes are done from their feedback we should be good."}]}]}]}, {"ts": "1740580479.217569", "text": "<PERSON> will share the letters tomorrow with the feedback addressed", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "kZYyp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> will share the letters tomorrow with the feedback addressed"}]}]}]}, {"ts": "**********.713999", "text": "<@U0690EB5JE5> we have one person at curana unable to login via sso. He keeps refreshing back to a blank screen, which is strange. <PERSON>. I did notice his email in our system is timothy.puri, and his email he's sending from is tim.puri, so I asked them to check that. But is there anything else on our end?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.713999", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "NWIfZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we have one person at curana unable to login via sso. He keeps refreshing back to a blank screen, which is strange. <PERSON>. I did notice his email in our system is timothy.puri, and his email he's sending from is tim.puri, so I asked them to check that. But is there anything else on our end?"}]}]}]}, {"ts": "1740580799.092289", "text": "No it’s definitely email issue then", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "2CQ2M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No "}, {"type": "text", "text": "it’s"}, {"type": "text", "text": " definitely email issue then"}]}]}]}, {"ts": "1740580835.414759", "text": "I will have update the email in case the one in the system is not the correct one ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GhNuV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will have update the email in case the one in the system is not the correct one "}]}]}]}, {"ts": "1740583665.116679", "text": "<@U0690EB5JE5> I copied you into the email thread. Can you help? this is the SSO thing with <PERSON>", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XvsEo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I copied you into the email thread. Can you help? this is the SSO thing with <PERSON>"}]}]}]}, {"ts": "**********.587619", "text": "<@U0690EB5JE5> We need to remove another last raise date for <PERSON> in the Curana main account. I thought about just manually overriding it by including her in the cycle manually, but I'm a little concerned that republishing will rock the boat. Can you remove her last raise date completely?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.587619", "reply_count": 15, "blocks": [{"type": "rich_text", "block_id": "YfP8/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We need to remove another last raise date for <PERSON> in the Curana main account. I thought about just manually overriding it by including her in the cycle manually, but I'm a little concerned that republishing will rock the boat. Can you remove her last raise date completely?"}]}]}]}, {"ts": "**********.330099", "text": "<@U07EJ2LP44S> This is done.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.519829", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "NpIa6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This is done."}]}]}]}, {"ts": "**********.170459", "text": "<@U07EJ2LP44S> Looks like user was able to login and We haven't made any changes that would impact login. Could you please ask user to clear cookies and try with the actual url `<https://curanahealthmip.stridehr.io/>` `<https://curanahealth.stridehr.io/>`", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.713999", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "FjsH9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Looks like user was able to login and We haven't made any changes that would impact login. Could you please ask user to clear cookies and try with the actual url "}, {"type": "link", "url": "https://curanahealthmip.stridehr.io/", "style": {"code": true}}, {"type": "text", "text": " "}, {"type": "link", "url": "https://curanahealth.stridehr.io/", "style": {"code": true}}]}]}]}, {"ts": "**********.150179", "text": "I guess we did UI deployment and that would sometimes cause this issue. clearing the cookies or reloading it multiple times would solve the problem.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QC3qU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I guess we did UI deployment and that would sometimes cause this issue. clearing the cookies or reloading it multiple times would solve the problem."}]}]}]}, {"ts": "**********.743189", "text": "<@U0690EB5JE5> <PERSON> is trying to adjust individual performance $ for the executive team so that the total bonus amount is in whole dollars. However, the system won't you let input a number with decimals so she can't get what she needs. For example, <PERSON>'s FINAL bonus award should be 573750.00 But she can't adjust the individual amount to get that number to be round. How can we address this?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.743189", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "9uCGj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON> is trying to adjust individual performance $ for the executive team so that the total bonus amount is in whole dollars. However, the system won't you let input a number with decimals so she can't get what she needs. For example, <PERSON>'s FINAL bonus award should be 573750.00 But she can't adjust the individual amount to get that number to be round. How can we address this?"}]}]}]}, {"ts": "1740656074.781259", "text": "<@U07EJ2LP44S> Letters post addressing feedback.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740580446.530429", "subtype": "thread_broadcast", "files": [{"id": "F08F3F30UMC", "created": 1740656066, "timestamp": 1740656066, "name": "VG_Adjustment_letters.zip", "title": "VG_Adjustment_letters.zip", "mimetype": "application/zip", "filetype": "zip", "pretty_type": "Zip", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 27315908, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F3F30UMC/vg_adjustment_letters.zip", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F3F30UMC/download/vg_adjustment_letters.zip", "media_display_type": "unknown", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F3F30UMC/vg_adjustment_letters.zip", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F3F30UMC-1f9234efa3", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Letters post addressing feedback."}]}]}]}, {"ts": "1740672852.661089", "text": "<@U0690EB5JE5> Can you upload these band changes to Tithely", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740672852.661089", "reply_count": 7, "files": [{"id": "F08FQ0L64SD", "created": 1740672850, "timestamp": 1740672850, "name": "TithelyBandsUpdate.csv", "title": "TithelyBandsUpdate.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 4443, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FQ0L64SD/tithelybandsupdate.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FQ0L64SD/download/tithelybandsupdate.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FQ0L64SD/tithelybandsupdate.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FQ0L64SD-942041cb2b", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FQ0L64SD/tithelybandsupdate.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Band Id,Effective Start Date,Effective End Date,Compensation Grade,Location (Country),Location (Region),Team,Department,Job Family,Job Subfamily,Description,Tier,Job Category,Job Level,Job Title,Frequency,Salary Currency,Salary Min Amount,Salary Target Amount,Salary Max Amount,Variable Pay Currency,Variable Pay Target (%),Variable Pay Target Amount,Equity Currency,Equity Target Amount,Equity Target (unit),Equity Target (%),Min Equity (unit),Max Equity (unit),Equity Participation Rate (%),OTE Min Amount,OTE Target Amount,OTE Max Amount,Pay Mix\r\nU,32,01/01/2021,,,US,,,Engineering,Engineering,,,0,Mgr,0,Director of Engineering & Product Dev Operations,Annual,USD,163000,211900,244500,,0,0,,0,0,0,0,0,0,0,0,0,\r\nU,33,01/01/2021,,,US,,,Engineering,Engineering,,,0,Mgr,0,Engineering Manager I,Annual,USD,125154,162700,187731,,0,0,,0,0,0,0,0,0,0,0,0,\r\nU,34,01/01/2021,,,US,,,Engineering,Engineering,,,0,Mgr,0,Engineering Manager II,Annual,USD,129692,168600,194538,,0,0,,0,0,0,0,0,0,0,0,0,\r\nU,35,01/01...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Band Id</div><div class=\"cm-col\">Effective Start Date</div><div class=\"cm-col\">Effective End Date</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Location (Country)</div><div class=\"cm-col\">Location (Region)</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">Job Subfamily</div><div class=\"cm-col\">Description</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Frequency</div><div class=\"cm-col\">Salary Currency</div><div class=\"cm-col\">Salary Min Amount</div><div class=\"cm-col\">Salary Target Amount</div><div class=\"cm-col\">Salary Max Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay Target (%)</div><div class=\"cm-col\">Variable Pay Target Amount</div><div class=\"cm-col\">Equity Currency</div><div class=\"cm-col\">Equity Target Amount</div><div class=\"cm-col\">Equity Target (unit)</div><div class=\"cm-col\">Equity Target (%)</div><div class=\"cm-col\">Min Equity (unit)</div><div class=\"cm-col\">Max Equity (unit)</div><div class=\"cm-col\">Equity Participation Rate (%)</div><div class=\"cm-col\">OTE Min Amount</div><div class=\"cm-col\">OTE Target Amount</div><div class=\"cm-col\">OTE Max Amount</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">32</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Mgr</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Director of Engineering &amp; Product Dev Operations</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">163000</div><div class=\"cm-col cm-num\">211900</div><div class=\"cm-col cm-num\">244500</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">33</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Mgr</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Engineering Manager I</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">125154</div><div class=\"cm-col cm-num\">162700</div><div class=\"cm-col cm-num\">187731</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">34</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Mgr</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Engineering Manager II</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">129692</div><div class=\"cm-col cm-num\">168600</div><div class=\"cm-col cm-num\">194538</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">35</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Jr. QA Engineer</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">52846</div><div class=\"cm-col cm-num\">68700</div><div class=\"cm-col cm-num\">79269</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">36</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Principal Software Engineer</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">137077</div><div class=\"cm-col cm-num\">178200</div><div class=\"cm-col cm-num\">205615</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">37</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">QA Engineer</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">70462</div><div class=\"cm-col cm-num\">91600</div><div class=\"cm-col cm-num\">105692</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">38</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Mgr</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Quality Assurance Testing Manager</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">114154</div><div class=\"cm-col cm-num\">148400</div><div class=\"cm-col cm-num\">171231</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">39</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Senior Software Engineer I</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">113154</div><div class=\"cm-col cm-num\">147100</div><div class=\"cm-col cm-num\">169731</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Senior Software Engineer II</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">131615</div><div class=\"cm-col cm-num\">171100</div><div class=\"cm-col cm-num\">197423</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 29, "lines_more": 24, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Q//ZL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you upload these band changes to Tithely"}]}]}]}, {"ts": "1740673162.054169", "text": "<@U07EJ2LP44S> All the feedback is taken care.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "2u3dj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " All the feedback is taken care."}]}]}]}, {"ts": "1740673749.648159", "text": "Thank you!", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ye0AT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you!"}]}]}]}, {"ts": "1740676302.597679", "text": "<@U0690EB5JE5> Is it possible to re-export the Valgenesis letters with the EEID in the name of the file? So for example 'EE ID - Name - Merit FY2025'? They are going to be uploading these into HiBob and they anticipated an issue with the names only.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740676302.597679", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "gj1cd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Is it possible to re-export the Valgenesis letters with the EEID in the name of the file? So for example 'EE ID - Name - Merit FY2025'? They are going to be uploading these into HiBob and they anticipated an issue with the names only."}]}]}]}, {"ts": "**********.886039", "text": "<@U0690EB5JE5> Can you add this employee to the Curana main account?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.886039", "reply_count": 1, "files": [{"id": "F08FQDKNWD7", "created": **********, "timestamp": **********, "name": "AddTrevorRipley.csv", "title": "AddTrevorRipley.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 663, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FQDKNWD7/addtrevorripley.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FQDKNWD7/download/addtrevorripley.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FQDKNWD7/addtrevorripley.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FQDKNWD7-e89e5398bc", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FQDKNWD7/addtrevorripley.csv/edit", "preview": "Employee_Code,Legal_Firstname,Legal_Lastname,Name,Work_Email,Gender,Most_Recent_Hire_Date,Supervisor_Primary_Code,Employee_Status,Hire_Date,EEO1_Ethnicity,Lives-in_State,Position,Department_Desc,Manager_Level,Manager_Level,DOL_Status,Scheduled_Pay_Period_Hours,Emp._Type(1099?),Group_Desc,Pay_Type,Annual_Salary,Annual_Benefits_Base_Rate,TargetBonus,MIPAmount,MIPPercent,Last_Pay_Change,Rate_1\r\n1472,TREVOR,RIPLEY,TREVOR RIPLEY,<EMAIL>,Male,12/20/2021,0248,On Leave,12/20/2021,Black or African American,AZ,Member Services Specialist,Call Center,XXX,None,Full-Time,80,W2,<PERSON><PERSON><PERSON>,<PERSON>ly,\"42,556.80\",\"42,556.80\",,,,03/23/2024,20.46", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Employee_Code</div><div class=\"cm-col\">Legal_Firstname</div><div class=\"cm-col\">Legal_Lastname</div><div class=\"cm-col\">Name</div><div class=\"cm-col\">Work_Email</div><div class=\"cm-col\">Gender</div><div class=\"cm-col\">Most_Recent_Hire_Date</div><div class=\"cm-col\">Supervisor_Primary_Code</div><div class=\"cm-col\">Employee_Status</div><div class=\"cm-col\">Hire_Date</div><div class=\"cm-col\">EEO1_Ethnicity</div><div class=\"cm-col\">Lives-in_State</div><div class=\"cm-col\">Position</div><div class=\"cm-col\">Department_Desc</div><div class=\"cm-col\">Manager_Level</div><div class=\"cm-col\">Manager_Level</div><div class=\"cm-col\">DOL_Status</div><div class=\"cm-col\">Scheduled_Pay_Period_Hours</div><div class=\"cm-col\">Emp._Type(1099?)</div><div class=\"cm-col\">Group_Desc</div><div class=\"cm-col\">Pay_Type</div><div class=\"cm-col\">Annual_Salary</div><div class=\"cm-col\">Annual_Benefits_Base_Rate</div><div class=\"cm-col\">TargetBonus</div><div class=\"cm-col\">MIPAmount</div><div class=\"cm-col\">MIPPercent</div><div class=\"cm-col\">Last_Pay_Change</div><div class=\"cm-col\">Rate_1</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">1472</div><div class=\"cm-col\">TREVOR</div><div class=\"cm-col\">RIPLEY</div><div class=\"cm-col\">TREVOR RIPLEY</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">Male</div><div class=\"cm-col\">12/20/2021</div><div class=\"cm-col cm-num\">0248</div><div class=\"cm-col\">On Leave</div><div class=\"cm-col\">12/20/2021</div><div class=\"cm-col\">Black or African American</div><div class=\"cm-col\">AZ</div><div class=\"cm-col\">Member Services Specialist</div><div class=\"cm-col\">Call Center</div><div class=\"cm-col\">XXX</div><div class=\"cm-col\">None</div><div class=\"cm-col\">Full-Time</div><div class=\"cm-col cm-num\">80</div><div class=\"cm-col\">W2</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">Hourly</div><div class=\"cm-col\">42,556.80</div><div class=\"cm-col\">42,556.80</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">03/23/2024</div><div class=\"cm-col cm-num\">20.46</div></div></div>\n</div>\n", "lines": 2, "lines_more": 0, "preview_is_truncated": false, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "OQl9Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you add this employee to the Curana main account?"}]}]}]}, {"ts": "**********.711599", "text": "<@U0690EB5JE5> These are updated scores from Curana for both accounts. Can you upload them? I am not able to get all of this done in PT hours, the customers are very active", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.711599", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "lBA46", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " These are updated scores from Curana for both accounts. Can you upload them? I am not able to get all of this done in PT hours, the customers are very active"}]}]}]}, {"ts": "**********.134039", "text": "<@U0690EB5JE5> For the total rewards, the download is not showing the entire salary history. It's only showing what's in the main box before scrolling. This needs to be fixed so it shows the entire salary history, including the data that only shows after the scroll.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.134039", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "OFuIE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " For the total rewards, the download is not showing the entire salary history. It's only showing what's in the main box before scrolling. This needs to be fixed so it shows the entire salary history, including the data that only shows after the scroll."}]}]}]}, {"ts": "**********.031909", "text": "<@U0690EB5JE5> Again for total rewards, can we change the 401k language to 'can we change the “retirement 401k” line item to “Retirement Match”. The international folks don't have a 401k", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.031909", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "6d3kS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Again for total rewards, can we change the 401k language to 'can we change the “retirement 401k” line item to “Retirement Match”. The international folks don't have a 401k"}]}]}]}, {"ts": "1740682896.228889", "text": "<@U0690EB5JE5> for Tithely, it looks like anyone who is hourly has no option for a merit increase. These people should behave the same as the salary people. But for hourly (mostly in Customer Success) they merit box is grayed out.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740682896.228889", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "ozxVg", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " for Tithely, it looks like anyone who is hourly has no option for a merit increase. These people should behave the same as the salary people. But for hourly (mostly in Customer Success) they merit box is grayed out."}]}]}]}, {"ts": "1740683606.282089", "text": "<@U0690EB5JE5> We need to pivot for the matrix performance/compa ratio in Tithely. B/c they are looking at it from range penetration and we from compa ratio the numbers are wrong.\n\nCan we change the ranges in the matrix to\n\n<.80\n.80-1\n1.1-1.20\ngreater than 1.2\n\nThey would like to keep the same ratings (which were set to US but still need to be applied to all the international matrixes - just the same numbers). We just need to change the compa ratio categories", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740683606.282089", "reply_count": 2, "edited": {"user": "U07EJ2LP44S", "ts": "1740684348.000000"}, "blocks": [{"type": "rich_text", "block_id": "bUnZ0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We need to pivot for the matrix performance/compa ratio in Tithely. B/c they are looking at it from range penetration and we from compa ratio the numbers are wrong.\n\nCan we change the ranges in the matrix to\n\n<.80\n.80-1\n1.1-1.20\ngreater than 1.2\n\nThey would like to keep the same ratings (which were set to US but still need to be applied to all the international matrixes - just the same numbers). We just need to change the compa ratio categories"}]}]}]}, {"ts": "1740707361.628499", "text": "Will take a look into all the reported stuff above.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jfFN6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take a look into all the reported stuff above."}]}]}]}, {"ts": "1740721189.675179", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> Can I stop all the non-prod ENVs? I assume training is done (may be Tithely would still not done?)", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740721189.675179", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "1740721203.000000"}, "blocks": [{"type": "rich_text", "block_id": "4eHOU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Can I stop all the non-prod ENVs? I assume training is done (may be Tith<PERSON> would still not done?)"}]}]}]}, {"ts": "**********.210139", "text": "<@U0690EB5JE5> One more update for Curana (well at least for now). Employee additions for the MIP account.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.210139", "reply_count": 2, "files": [{"id": "F08G7LBRZKJ", "created": **********, "timestamp": **********, "name": "Curana Health_EmployeeDataTemplate_MIP additions.xlsx", "title": "Curana Health_EmployeeDataTemplate_MIP additions.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 24995, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08G7LBRZKJ/curana_health_employeedatatemplate_mip_additions.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08G7LBRZKJ/download/curana_health_employeedatatemplate_mip_additions.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G7LBRZKJ-6562a6b853/curana_health_employeedatatemplate_mip_additions_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08G7LBRZKJ-6562a6b853/curana_health_employeedatatemplate_mip_additions_thumb_pdf.png", "thumb_pdf_w": 935, "thumb_pdf_h": 1210, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08G7LBRZKJ/curana_health_employeedatatemplate_mip_additions.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08G7LBRZKJ-c1d8539c6d", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "WkUM/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " One more update for Curana (well at least for now). Employee additions for the MIP account."}]}]}]}, {"ts": "**********.044429", "text": "Ok will add them in an hour away right now.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "05FLQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok will add them in an hour away right"}, {"type": "text", "text": " "}, {"type": "text", "text": "now."}]}]}]}, {"ts": "**********.670959", "text": "<@U07EJ2LP44S> Done.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.210139", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "Ts22O", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Done."}]}]}]}, {"ts": "**********.801109", "text": "<@U0690EB5JE5> Can we create a custom report for Diversified energy based on the attached? We would need one row for <PERSON><PERSON>'s direct reports, and one row for each M8. Calculations are in the sheet.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.801109", "reply_count": 39, "files": [{"id": "F08G8G816BA", "created": 1740763821, "timestamp": 1740763821, "name": "Summary Report Request .csv", "title": "Summary Report Request .csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1632, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08G8G816BA/summary_report_request_.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08G8G816BA/download/summary_report_request_.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08G8G816BA/summary_report_request_.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08G8G816BA-e7cb8bb982", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08G8G816BA/summary_report_request_.csv/edit", "preview": ",,,,,,,,,,,,\r\n,No. EE,Salaries $,Bonus Eligibility $,Bonus El % of $,Bonus Paid,Bonus/EE,% Eligibility,,,,,\r\n<PERSON><PERSON>ons Directs,4,\" $2,024,999.86 \",\" $3,143,749.77 \",155.2%,\" $2,573,725.62 \",\" $643,431.40 \",81.9%,,,,,\r\nRigg,3,\" $625,977.30 \",\" $160,613.47 \",25.7%,\" $160,613.47 \",\" $53,537.82 \",100.0%,,,,,\r\nRidgeway,22,\" $3,317,626.00 \",\" $819,644.83 \",24.7%,\" $819,644.83 \",\" $37,256.58 \",100.0%,,,,,\r\nNLE,8,\" $1,215,461.10 \",\" $176,051.37 \",14.5%,\" $176,051.37 \",\" $22,006.42 \",100.0%,,,,,\r\n<PERSON>,58,\" $6,516,384.68 \",\" $1,181,382.23 \",18.1%,\" $1,195,085.95 \",\" $20,604.93 \",101.2%,,<PERSON><PERSON><PERSON>s ,,,6\r\n<PERSON>,84,\" $10,426,770.90 \",\" $1,713,905.83 \",16.4%,\" $1,688,247.65 \",\" $20,098.19 \",98.5%,,M8 Teams,,,\r\nGray,88,\" $8,749,046.54 \",\" $1,674,646.02 \",19.1%,\" $1,631,478.72 \",\" $18,539.53 \",97.4%,,,,,\r\nBentley,200,\" $21,820,980.74 \",\" $3,616,337.30 \",16.6%,\" $3,581,678.25 \",\" $17,908.39 \",99.0%,,,,,\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\"></div><div class=\"cm-col\">No. EE</div><div class=\"cm-col\">Salaries $</div><div class=\"cm-col\">Bonus Eligibility $</div><div class=\"cm-col\">Bonus El % of $</div><div class=\"cm-col\">Bonus Paid</div><div class=\"cm-col\">Bonus/EE</div><div class=\"cm-col\">% Eligibility</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Hutsons Directs</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\"> $2,024,999.86 </div><div class=\"cm-col\"> $3,143,749.77 </div><div class=\"cm-col\">155.2%</div><div class=\"cm-col\"> $2,573,725.62 </div><div class=\"cm-col\"> $643,431.40 </div><div class=\"cm-col\">81.9%</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Rigg</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\"> $625,977.30 </div><div class=\"cm-col\"> $160,613.47 </div><div class=\"cm-col\">25.7%</div><div class=\"cm-col\"> $160,613.47 </div><div class=\"cm-col\"> $53,537.82 </div><div class=\"cm-col\">100.0%</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Ridgeway</div><div class=\"cm-col cm-num\">22</div><div class=\"cm-col\"> $3,317,626.00 </div><div class=\"cm-col\"> $819,644.83 </div><div class=\"cm-col\">24.7%</div><div class=\"cm-col\"> $819,644.83 </div><div class=\"cm-col\"> $37,256.58 </div><div class=\"cm-col\">100.0%</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">NLE</div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\"> $1,215,461.10 </div><div class=\"cm-col\"> $176,051.37 </div><div class=\"cm-col\">14.5%</div><div class=\"cm-col\"> $176,051.37 </div><div class=\"cm-col\"> $22,006.42 </div><div class=\"cm-col\">100.0%</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Sullivan</div><div class=\"cm-col cm-num\">58</div><div class=\"cm-col\"> $6,516,384.68 </div><div class=\"cm-col\"> $1,181,382.23 </div><div class=\"cm-col\">18.1%</div><div class=\"cm-col\"> $1,195,085.95 </div><div class=\"cm-col\"> $20,604.93 </div><div class=\"cm-col\">101.2%</div><div class=\"cm-col\"></div><div class=\"cm-col\">Hutsons Directs </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">6</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Myers</div><div class=\"cm-col cm-num\">84</div><div class=\"cm-col\"> $10,426,770.90 </div><div class=\"cm-col\"> $1,713,905.83 </div><div class=\"cm-col\">16.4%</div><div class=\"cm-col\"> $1,688,247.65 </div><div class=\"cm-col\"> $20,098.19 </div><div class=\"cm-col\">98.5%</div><div class=\"cm-col\"></div><div class=\"cm-col\">M8 Teams</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Gray</div><div class=\"cm-col cm-num\">88</div><div class=\"cm-col\"> $8,749,046.54 </div><div class=\"cm-col\"> $1,674,646.02 </div><div class=\"cm-col\">19.1%</div><div class=\"cm-col\"> $1,631,478.72 </div><div class=\"cm-col\"> $18,539.53 </div><div class=\"cm-col\">97.4%</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Bentley</div><div class=\"cm-col cm-num\">200</div><div class=\"cm-col\"> $21,820,980.74 </div><div class=\"cm-col\"> $3,616,337.30 </div><div class=\"cm-col\">16.6%</div><div class=\"cm-col\"> $3,581,678.25 </div><div class=\"cm-col\"> $17,908.39 </div><div class=\"cm-col\">99.0%</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 28, "lines_more": 18, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "GVbTP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can we create a custom report for Diversified energy based on the attached? We would need one row for <PERSON><PERSON>'s direct reports, and one row for each M8. Calculations are in the sheet."}]}]}]}]}