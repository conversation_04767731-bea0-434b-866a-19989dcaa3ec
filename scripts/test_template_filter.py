#!/usr/bin/env python3
"""
Test script to verify the markdown template filter is working correctly.
"""

import os
import sys
import django

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.template import Template, Context
from django.template.loader import get_template

def test_markdown_filter():
    """Test the markdown filter in a Django template context."""
    
    print("🧪 Testing Markdown Template Filter")
    print("=" * 50)
    
    # Test markdown content
    test_content = """## Latest Updates on Curana

### Most Recent Updates
- **March 4, 2024**: <PERSON> reported new employee additions
- **February 26, 2024**: <PERSON> provided feedback on total rewards

### Key People
- **Amanda**: Product manager
- **<PERSON><PERSON>h**: Engineering lead

This is a **bold** statement with *italic* text."""

    # Create a simple template
    template_string = """
    {% load search_extras %}
    <div class="response-container">
        {{ content|markdown_to_html }}
    </div>
    """
    
    try:
        template = Template(template_string)
        context = Context({'content': test_content})
        rendered = template.render(context)
        
        print("✅ Template rendered successfully!")
        print("\n📝 Rendered HTML:")
        print("-" * 30)
        print(rendered)
        
        # Check for expected elements
        checks = [
            ('<h2 class="response-heading">', 'H2 headers'),
            ('<h3 class="response-heading">', 'H3 headers'),
            ('<ul class="response-list">', 'Lists'),
            ('<strong>', 'Bold text'),
            ('<em>', 'Italic text'),
        ]
        
        print("\n🔍 Element Checks:")
        print("-" * 30)
        all_passed = True
        for element, description in checks:
            if element in rendered:
                print(f"✅ {description}: Found")
            else:
                print(f"❌ {description}: Missing")
                all_passed = False
        
        if all_passed:
            print("\n🎉 ALL CHECKS PASSED - Markdown filter is working!")
        else:
            print("\n⚠️  Some checks failed - filter may not be working correctly")
            
        return all_passed
        
    except Exception as e:
        print(f"❌ Template rendering failed: {e}")
        return False

def test_markdown_availability():
    """Test if markdown library is available."""
    
    print("\n📦 Testing Markdown Library Availability")
    print("=" * 50)
    
    try:
        import markdown
        print(f"✅ Markdown library available: v{markdown.__version__}")
        
        # Test basic markdown conversion
        md = markdown.Markdown()
        test_md = "## Test Header\n\n- List item\n- **Bold item**"
        html = md.convert(test_md)
        print(f"✅ Basic conversion works: {html[:50]}...")
        
        return True
    except ImportError:
        print("❌ Markdown library not available")
        return False

if __name__ == "__main__":
    print("🚀 Template Filter Test Suite")
    print("=" * 50)
    
    # Test markdown availability
    markdown_available = test_markdown_availability()
    
    # Test template filter
    if markdown_available:
        filter_working = test_markdown_filter()
        
        print("\n📊 Final Results:")
        print("=" * 50)
        if filter_working:
            print("🎉 SUCCESS: Markdown template filter is working correctly!")
            print("✅ The UI should now display formatted content")
            print("✅ Headers, lists, and text formatting will be applied")
        else:
            print("⚠️  ISSUE: Template filter has problems")
    else:
        print("\n❌ FAILED: Markdown library not available")
        print("💡 Make sure to run Django with: poetry run python manage.py runserver")
