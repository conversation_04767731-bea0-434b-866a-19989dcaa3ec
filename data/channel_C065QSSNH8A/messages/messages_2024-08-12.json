{"date": "2024-08-12", "channel_id": "C065QSSNH8A", "message_count": 12, "messages": [{"ts": "1723484586.497869", "text": "<@U07EJ2LP44S> For playQ updates and deletes have been processed successfully after recent code changes were merged. <PERSON><PERSON> mentioned She wants to add new employees too Row no 90-93 I do not see these rows in the csv she had provided. lets confirm that in the meeting at 11", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1723484586.497869", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "gHYTx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " For playQ updates and deletes have been processed successfully after recent code changes were merged. <PERSON><PERSON> mentioned She wants to add new employees too Row no 90-93 I do not see these rows in the csv she had provided. lets confirm that in the meeting at 11"}]}]}]}, {"ts": "1723479592.043299", "text": "<!here> As just discussed in the eng call, let's use this <https://docs.google.com/spreadsheets/d/1YZOTN_AAVx_-a3faQfQM2IseXRo-qUDfXlLgmp6T_ws/edit?gid=891744166#gid=891744166|sheet> to manage customer roadmap and their timelines", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F07EKKX5954", "created": 1722369064, "timestamp": 1722369064, "name": "Customer Tracker", "title": "Customer Tracker - No Longer Updating", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1YZOTN_AAVx_-a3faQfQM2IseXRo-qUDfXlLgmp6T_ws", "external_url": "https://docs.google.com/spreadsheets/d/1YZOTN_AAVx_-a3faQfQM2IseXRo-qUDfXlLgmp6T_ws/edit?usp=sharing", "url_private": "https://docs.google.com/spreadsheets/d/1YZOTN_AAVx_-a3faQfQM2IseXRo-qUDfXlLgmp6T_ws/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EKKX5954-97bdfb28e2/customer_tracker_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSJoyaWigBMmloooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07EKKX5954/customer_tracker", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Kz1li", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " As just discussed in the eng call, let's use this "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1YZOTN_AAVx_-a3faQfQM2IseXRo-qUDfXlLgmp6T_ws/edit?gid=891744166#gid=891744166", "text": "sheet"}, {"type": "text", "text": " to manage customer roadmap and their timelines"}]}]}]}, {"ts": "1723478243.434799", "text": "<!here> I am very tired and won't be able to join eng discussion call. updates from my end\n\n• OTE merit planning dev work is complete and ready for QA\n• Fixes for upload issues are deployed to TEST ENV. Requested <PERSON><PERSON>bh to verify and deploy to production\n• Need to finalize the scope of work for supporting international currency in budgetting. \n• Please let me know if there are any other new requirements from customers that needs to prioritized over other known work.\n• work for improving data upload user experience is in progress and ETA is by EOW worst case", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723478243.434799", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1723478774.000000"}, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "RbK1U", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I am very tired and won't be able to join eng discussion call. updates from my end\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OTE merit planning dev work is complete and ready for QA"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Fixes for upload issues are deployed to TEST ENV. Requested <PERSON><PERSON>bh to verify and deploy to production"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Need to finalize the scope of work for supporting international currency in budgetting. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Please let me know if there are any other new requirements from customers that needs to prioritized over other known work."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "work for improving data upload user experience is in progress and ETA is by EOW worst case"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1723477962.358459", "text": "<@U04DKEFP1K8> Fixes for the three issues discussed last night PST are deployed to TEST ENV (`developmet`/`dev`). I didn't get a chance to test those fixes. Please do verify and merge the changes to PROD.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723428063.401379", "subtype": "thread_broadcast", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "BwAtO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Fixes for the three issues discussed last night PST are deployed to TEST ENV ("}, {"type": "text", "text": "developmet", "style": {"code": true}}, {"type": "text", "text": "/"}, {"type": "text", "text": "dev", "style": {"code": true}}, {"type": "text", "text": "). I didn't get a chance to test those fixes. Please do verify and merge the changes to PROD."}]}]}]}, {"ts": "1723428569.409729", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> Can you please help me with slack threads/tickets related upload issues? there were too many slack threads and I am unable to track those issues clearly.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723428063.401379", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1723428643.000000"}, "blocks": [{"type": "rich_text", "block_id": "BVvMX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can you please help me with slack threads/tickets related upload issues? there were too many slack threads and I am unable to track those issues clearly."}]}]}]}, {"ts": "1723428063.401379", "text": "<@U0690EB5JE5> please let us know the status of fixing date upload issues ", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1723428063.401379", "reply_count": 13, "blocks": [{"type": "rich_text", "block_id": "Tjhq4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " please let us know the status of fixing date upload issues "}]}]}]}, {"ts": "1723428018.486789", "text": "<@U04DKEFP1K8> FYI... I have synced all the changes from BE-dev till Production/Demo env. There were some fixes and minor merit planning enhancements i.e. nudge and revoke features.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1723428037.000000"}, "blocks": [{"type": "rich_text", "block_id": "+7FTQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " FYI... I have synced all the changes from BE-dev till Production/Demo env. There were some fixes and minor merit planning enhancements i.e. nudge and revoke features."}]}]}]}, {"ts": "1723427534.125269", "text": "Sure sounds good.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MKL8q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure sounds good."}]}]}]}, {"ts": "1723427511.467129", "text": "So far it looks like only Alayacare has a need for Canadian dollars for the best currency. We can discuss this more during the eng call", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "GnBq2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So far it looks like only Alayacare has a need for Canadian dollars for the best currency. We can discuss this more during the eng call"}]}]}]}, {"ts": "1723427418.101019", "text": "I think for now we will keep only the US dollars for paybands", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Wkq8L", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think for now we will keep only the US dollars for paybands"}]}]}]}, {"ts": "1723427356.029129", "text": "Also how about Pay bands as well? Do we need to introduce toggle there as well?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Fei<PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also how about Pay bands as well? Do we need to introduce toggle there as well?"}]}]}]}, {"ts": "1723427290.892939", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> Would like understand the scope of supporting international currency in budgetting, Is this just limited to introducing currency toggle work for budget info as well? if yes then we can try finishing this early next month. So the base currency still remains US but they can use the toggle. Can we try convincing customers for this scope? We can work on the larger scope of giving flexibility to keep any currency as base currency across product if we get more such requests.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1723427369.000000"}, "blocks": [{"type": "rich_text", "block_id": "JPhfK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Would like understand the scope of supporting international currency in budgetting, Is this just limited to introducing currency toggle work for budget info as well? if yes then we can try finishing this early next month. So the base currency still remains US but they can use the toggle. Can we try convincing customers for this scope? We can work on the larger scope of giving flexibility to keep any currency as base currency across product if we get more such requests."}]}]}]}], "created_at": "2025-05-22T21:35:34.630086"}