{"date": "2024-10-30", "channel_id": "C065QSSNH8A", "message_count": 8, "messages": [{"ts": "1730303012.914649", "text": "<!here> running 10 minutes late", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "aVNUI", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " running 10 minutes late"}]}]}]}, {"ts": "1730301277.919129", "text": "We need to have one session on Comp Cycle builder. Somehow I feel the budget planning flow is bit confusing. And Also allocation is very crude as of now.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "100", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "lur13", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We need to have one session on Comp Cycle builder. Somehow I feel the budget planning flow is bit confusing. And Also allocation is very crude as of now."}]}]}]}, {"ts": "1730299073.947939", "text": "Agenda today:\n• Plan for eng support during active customer cycles and during testing phase leading to cycle launch dates.\n• Realistic product expectations for the Nov cycles\n• Eng plan to ensure Jan cycles are all stable and minimal bugs and errors.\n• Current bugs, issues, customers etc. ", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730299073.947939", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "oCFyz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda today:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Plan for eng support during active customer cycles and during testing phase leading to cycle launch dates."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Realistic product expectations for the Nov cycles"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Eng plan to ensure Jan cycles are all stable and minimal bugs and errors."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Current bugs, issues, customers etc. "}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "**********.774889", "text": "<@U0690EB5JE5> will you look at the Alayacare account - <PERSON> - the CEO - isn't showing up in the org view. They need to assign him an HRBP. I removed his role (Super Admin) and reassigned a different role (Manager), but it didn't do anything. I assume this is the root employee.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.774889", "reply_count": 18, "blocks": [{"type": "rich_text", "block_id": "/FqFM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " will you look at the Alayacare account - <PERSON> - the CEO - isn't showing up in the org view. They need to assign him an HRBP. I removed his role (Super Admin) and reassigned a different role (Manager), but it didn't do anything. I assume this is the root employee."}]}]}]}, {"ts": "**********.621399", "text": "<@U0690EB5JE5> and team , here are the key data issues\n• Shipstopper: <https://compiify.atlassian.net/browse/COM-3935>\n• <https://compiify.atlassian.net/browse/COM-3936>\n• <https://compiify.atlassian.net/browse/COM-3930> ", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14018::b0ca8c54e1ec4d0799e609aff82a2830", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3935?atlOrigin=eyJpIjoiODZmOWVjMDJhZDFiNDc0ZDg1Y2Y3OGM0YjA3MmIyYWUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3935 Issue: Incorrect Budget Roll-up on Allocate Page>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14018::0726fd71caed45f79afecac5131bbd8a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14018\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3935\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3935", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:14019::fc64e14d299344149b317126eb0c27ac", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3936?atlOrigin=eyJpIjoiNmY3NzYzODhhNDY2NDBjNjgxNTBiYTBhZTQ3MmRhMjQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3936 Issue: Inconsistent Decimal Rounding for Hourly Employees>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14019::ac628d48bd5e41e58c310ec5c639ca77", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14019\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3936\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3936", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 3, "blocks": [{"type": "section", "block_id": "uf:ih:14013::b15a93bb3cad49db92aa7009860314c4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3930?atlOrigin=eyJpIjoiYmJmZmU1M2JkMDkzNDIyOTgzMjVkMWMwNmM5MjYzYjEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3930 Issue: Investigate Total Target Cash Calculation Discrepancy for ho…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14013::4a86dfffbfa0437f8cf32560031e69c7", "elements": [{"type": "mrkdwn", "text": "Status: *In Development*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/3ba050d9f25e3d5164f213ae816bb449?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14013\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3930\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3930", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "KMFDr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " and team , here are the key data issues\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Shipstopper: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3935"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3936"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3930"}, {"type": "text", "text": " "}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "**********.908719", "text": "<@U0690EB5JE5> In the Tithely account, I am seeing two employees \"Compiify Service Account\" and \"Swagup Service Account\". Is these garbage data that should be deleted?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.908719", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "7kYwL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " In the Tithely account, I am seeing two employees \"Compiify Service Account\" and \"Swagup Service Account\". Is these garbage data that should be deleted?"}]}]}]}, {"ts": "**********.177939", "text": "<!here> Just FYI, we are working with NovoInsights to be included their vendor list for the comp category. This is what the process would look like.\n\nWe will be doing a product demo of their requested use cases in late November. <@U0690EB5JE5> can we make sure we have new bonus workflows are built and tested by then.\n\n\n*Here is the overview of their process.* \n\nThank you for being willing to engage in our new research as we seek to provide the market with even greater clarity on their CompTech choices. Our clients have asked us for a more in-depth analysis of the Cycle Management category, which we define as the *technology used to facilitate the cyclical pay decisions (salary, bonus, and/or equity) within established guidelines and budget.*\n\nWe’re ready to start the first step of the process, which requires your company to complete the attached questionnaire workbook.\n\nIn this workbook, we are asking you to provide three things:\n1. A profile of your firm, including your CompTech footprint and target market\n2. Indicative pricing for your Cycle Management solution for a few defined scenarios (your pricing will not be shared directly or attributed to you)\n3. Self-evaluation of key features/capabilities in this space\n*We would like for you to return your completed workbook by November 12. Upon completion, please send to <mailto:<EMAIL>|<EMAIL>>.*\n\nIf you have questions as you review and complete your workbook, contact me or <PERSON> (<mailto:<EMAIL>|<EMAIL>>) as needed.\n\nAfter we receive your completed workbook, the remaining steps will be:\n1. A discussion between your team and the Novo Insights team to clarify any areas of your workbook (45 minutes, to be scheduled once the workbook is received)\n2. Your team will deliver a demonstration of your solution based on specific identified case requirements (~90 minutes, to be scheduled in late November once Novo Insights provides case requirements)\n3. Your review of your profile page for factual updates and comment-only review of our intended editorial comments (expected in January)\nAs a reminder, after our research, we expect to publish a report with the following:\n• *Category Thesis and Case for Impact:* Why compensation teams should explore this technology and how it helps drive value/ROI\n• *Planning Considerations:* Indicative budgets and timelines to help the market understand the likely investment of resources to deploy\n• *Landscape and Players:* Our view of the category participants and any segments within\n• *Capability Overview:* What features matter and drive value within the category\n• *Evaluation Process:* how we conducted our research\n• *Vendor Profiles:* Specific profile of each participating vendor, including:\n    ◦ Who you are\n    ◦ Who you target\n    ◦ What else you provide (e.g., other CompTech categories or services)\n    ◦ Capability evaluation (summary scores by area)\n    ◦ What we love - distinct features that are easy, high impact, and market leading\n    ◦ What we want - what we hear or see that we hope can evolve\n", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1gipG", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Just FYI, we are working with NovoInsights to be included their vendor list for the comp category. This is what the process would look like.\n\nWe will be doing a product demo of their requested use cases in late November. "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we make sure we have new bonus workflows are built and tested by then.\n\n\n"}, {"type": "text", "text": "Here is the overview of their process. ", "style": {"bold": true}}, {"type": "text", "text": "\n\nThank you for being willing to engage in our new research as we seek to provide the market with even greater clarity on their CompTech choices. Our clients have asked us for a more in-depth analysis of the Cycle Management category, which we define as the "}, {"type": "text", "text": "technology used to facilitate the cyclical pay decisions (salary, bonus, and/or equity) within established guidelines and budget.", "style": {"bold": true}}, {"type": "text", "text": "\n\nWe’re ready to start the first step of the process, which requires your company to complete the attached questionnaire workbook.\n\nIn this workbook, we are asking you to provide three things:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "A profile of your firm, including your CompTech footprint and target market"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Indicative pricing for your Cycle Management solution for a few defined scenarios (your pricing will not be shared directly or attributed to you)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Self-evaluation of key features/capabilities in this space"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "text", "text": "We would like for you to return your completed workbook by November 12. Upon completion, please send to ", "style": {"bold": true}}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>", "style": {"bold": true}}, {"type": "text", "text": ".", "style": {"bold": true}}, {"type": "text", "text": "\n\nIf you have questions as you review and complete your workbook, contact me or <PERSON> ("}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ") as needed.\n\nAfter we receive your completed workbook, the remaining steps will be:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "A discussion between your team and the Novo Insights team to clarify any areas of your workbook (45 minutes, to be scheduled once the workbook is received)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Your team will deliver a demonstration of your solution based on specific identified case requirements (~90 minutes, to be scheduled in late November once Novo Insights provides case requirements)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Your review of your profile page for factual updates and comment-only review of our intended editorial comments (expected in January)"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAs a reminder, after our research, we expect to publish a report with the following:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Category Thesis and Case for Impact:", "style": {"bold": true}}, {"type": "text", "text": " Why compensation teams should explore this technology and how it helps drive value/ROI"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Planning Considerations:", "style": {"bold": true}}, {"type": "text", "text": " Indicative budgets and timelines to help the market understand the likely investment of resources to deploy"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Landscape and Players:", "style": {"bold": true}}, {"type": "text", "text": " Our view of the category participants and any segments within"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Capability Overview:", "style": {"bold": true}}, {"type": "text", "text": " What features matter and drive value within the category"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Evaluation Process:", "style": {"bold": true}}, {"type": "text", "text": " how we conducted our research"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Vendor Profiles:", "style": {"bold": true}}, {"type": "text", "text": " Specific profile of each participating vendor, including:"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Who you are"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Who you target"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "What else you provide (e.g., other CompTech categories or services)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Capability evaluation (summary scores by area)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "What we love - distinct features that are easy, high impact, and market leading"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "What we want - what we hear or see that we hope can evolve"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1730232352.248069", "text": "did that bug for allocations get into jira? do i need to add it if not?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1730232352.248069", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "KWMs+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "did that bug for allocations get into jira? do i need to add it if not?"}]}]}]}], "created_at": "2025-05-22T21:35:34.658421"}