{"date": "2024-03-08", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1709917540.724929", "text": "<@U0690EB5JE5> For building the new Stride website, how important would it be for it to be built with next.js specifically, vs. just react.js ?\n\nDo you see any concerns about our frontend engineers' ability to maintain it long term if it's not specifically built with next.js?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709917540.724929", "reply_count": 18, "blocks": [{"type": "rich_text", "block_id": "Djb1N", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " For building the new Stride website, how important would it be for it to be built with next.js specifically, vs. just react.js ?\n\nDo you see any concerns about our frontend engineers' ability to maintain it long term if it's not specifically built with next.js?"}]}]}]}, {"ts": "1709873102.591849", "text": "In addition to the new People Insights with compelling charts &amp; graphics, I'm proposing a <https://docs.google.com/document/d/1tEsOQuOXvL5y2CzgJDXz5kG3cI_jfWGwP6jsuBow4hA/edit|refresh of our downloadable reports> based on the types of requests we got from beta customers so far.\n\n<@U04DKEFP1K8> If you have some time to chime in on this, my main questions for you are\n• How do the current available roles relate to access to reports - should we make any assumptions about restricting this to Admins only?\n• Are there any fields in the current upload templates that we should consider removing? For example, if no customer is uploading \"cost center\" or \"job family group\", how important is it to include these in standard downloads?\n<@U0690EB5JE5> This is the PRD I mentioned that could correspond to our data consistency exercise.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709873102.591849", "reply_count": 9, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "files": [{"id": "F06NJHPGE4T", "created": 1709873105, "timestamp": 1709873105, "name": "Reports Refresh PRD", "title": "Reports Refresh PRD", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1tEsOQuOXvL5y2CzgJDXz5kG3cI_jfWGwP6jsuBow4hA", "external_url": "https://docs.google.com/document/d/1tEsOQuOXvL5y2CzgJDXz5kG3cI_jfWGwP6jsuBow4hA/edit", "url_private": "https://docs.google.com/document/d/1tEsOQuOXvL5y2CzgJDXz5kG3cI_jfWGwP6jsuBow4hA/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NJHPGE4T-f29a684415/reports_refresh_prd_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSOcjDAfUUmG/vD8qVhn60mG9aAHUU3B9aXBoAWiiigBr4yM4pFKjuKc2RSA8dKAF3D1oyKM+1GfagBRzRRRQAjU38vypxyOlAJ9RQAnP+RR/npTqM0AA6UUUUAf/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06NJHPGE4T/reports_refresh_prd", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "pwooU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In addition to the new People Insights with compelling charts & graphics, I'm proposing a "}, {"type": "link", "url": "https://docs.google.com/document/d/1tEsOQuOXvL5y2CzgJDXz5kG3cI_jfWGwP6jsuBow4hA/edit", "text": "refresh of our downloadable reports"}, {"type": "text", "text": " based on the types of requests we got from beta customers so far.\n\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " If you have some time to chime in on this, my main questions for you are\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "How do the current available roles relate to access to reports - should we make any assumptions about restricting this to Admins only?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Are there any fields in the current upload templates that we should consider removing? For example, if no customer is uploading \"cost center\" or \"job family group\", how important is it to include these in standard downloads?"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " This is the PRD I mentioned that could correspond to our data consistency exercise."}]}]}]}], "created_at": "2025-05-22T21:35:34.611423"}