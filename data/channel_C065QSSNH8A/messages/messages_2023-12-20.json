{"date": "2023-12-20", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1703096152.079709", "text": "<https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/ref=sr_1_2_sspa?hvadid=616859350494&amp;hvdev=c&amp;hvlocphy=9032306&amp;hvnetw=g&amp;hvqmt=e&amp;hvrand=2955047059185648970&amp;hvtargid=kwd-66599339680&amp;hydadcr=9407_13604228&amp;keywords=lifestraw%2Bwater%2Bbottle&amp;qid=1703096130&amp;sr=8-2-spons&amp;sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&amp;th=1|https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/[…]d=1703096130&amp;sr=8-2-spons&amp;sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&amp;th=1>", "user": "U0658EW4B8D", "type": "message", "attachments": [{"from_url": "https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/ref=sr_1_2_sspa?hvadid=616859350494&hvdev=c&hvlocphy=9032306&hvnetw=g&hvqmt=e&hvrand=2955047059185648970&hvtargid=kwd-66599339680&hydadcr=9407_13604228&keywords=lifestraw%2Bwater%2Bbottle&qid=1703096130&sr=8-2-spons&sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&th=1", "service_icon": "https://www.amazon.com/favicon.ico", "id": 1, "original_url": "https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/ref=sr_1_2_sspa?hvadid=616859350494&amp;hvdev=c&amp;hvlocphy=9032306&amp;hvnetw=g&amp;hvqmt=e&amp;hvrand=2955047059185648970&amp;hvtargid=kwd-66599339680&amp;hydadcr=9407_13604228&amp;keywords=lifestraw%2Bwater%2Bbottle&amp;qid=1703096130&amp;sr=8-2-spons&amp;sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&amp;th=1", "fallback": "Amazon.com : LifeStraw Go Series – Insulated Stainless Steel Water Filter Bottle for Travel and Everyday use removes Bacteria, parasites and microplastics, Improves Taste, 24oz Icelandic Blue : Sports &amp; Outdoors", "text": "<http://Amazon.com|Amazon.com> : LifeStraw Go Series – Insulated Stainless Steel Water Filter Bottle for Travel and Everyday use removes Bacteria, parasites and microplastics, Improves Taste, 24oz Icelandic Blue : Sports &amp; Outdoors", "title": "Amazon.com : LifeStraw Go Series – Insulated Stainless Steel Water Filter Bottle for Travel and Everyday use removes Bacteria, parasites and microplastics, Improves Taste, 24oz Icelandic Blue : Sports &amp; Outdoors", "title_link": "https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/ref=sr_1_2_sspa?hvadid=616859350494&hvdev=c&hvlocphy=9032306&hvnetw=g&hvqmt=e&hvrand=2955047059185648970&hvtargid=kwd-66599339680&hydadcr=9407_13604228&keywords=lifestraw%2Bwater%2Bbottle&qid=1703096130&sr=8-2-spons&sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&th=1", "service_name": "amazon.com"}], "blocks": [{"type": "rich_text", "block_id": "ltQz7", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/ref=sr_1_2_sspa?hvadid=616859350494&hvdev=c&hvlocphy=9032306&hvnetw=g&hvqmt=e&hvrand=2955047059185648970&hvtargid=kwd-66599339680&hydadcr=9407_13604228&keywords=lifestraw%2Bwater%2Bbottle&qid=1703096130&sr=8-2-spons&sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&th=1", "text": "https://www.amazon.com/LifeStraw-Go-Insulated-Stainless-microplastics/dp/B0BY3B1KP9/[…]d=1703096130&sr=8-2-spons&sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&th=1"}]}]}]}, {"ts": "1703095281.554799", "text": "We'll start the working session maybe ~5 min late ... need a break first :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rkc1O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We'll start the working session maybe ~5 min late ... need a break first "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1703088946.256579", "text": "<https://docs.google.com/document/d/1sfemzlADx-jKfMZKUjHKdQKvUGCLK7YbtNtTwYO8248/edit|Here> are my Q1 OKRs\nIt might be more efficient to insert your Q1 OKRs in this doc. <@U065H3M6WJV> can add her OKRs under Objective 1 and <@U04DKEFP1K8> can add his OKRs under Objective 2", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F069AP0957G", "created": 1702273069, "timestamp": 1702273069, "name": "OKRs_Kapil v1", "title": "Q1' 24 OKRs", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1sfemzlADx-jKfMZKUjHKdQKvUGCLK7YbtNtTwYO8248", "external_url": "https://docs.google.com/document/d/1sfemzlADx-jKfMZKUjHKdQKvUGCLK7YbtNtTwYO8248/edit", "url_private": "https://docs.google.com/document/d/1sfemzlADx-jKfMZKUjHKdQKvUGCLK7YbtNtTwYO8248/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F069AP0957G-e1d3309c5f/okrs_kapil_v1_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACXSJxRuHrSnpTN6/wB79aTaW4Dtw9aNw9aQMp6E0ZHqaLoB1FFFMAPTmo9qH/8AVUlQupLfKxFJ6CY/Yvp+lKFUHgVHsfvIacFII+Y0k/ICSiiiqGFMYjPJxT6QoCckD8qAGZB6NTx75pNi+g/KnYFABRRRQB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F069AP0957G/okrs_kapil_v1", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "zplPn", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/document/d/1sfemzlADx-jKfMZKUjHKdQKvUGCLK7YbtNtTwYO8248/edit", "text": "Here"}, {"type": "text", "text": " are my Q1 OKRs\nIt might be more efficient to insert your Q1 OKRs in this doc. "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " can add her OKRs under Objective 1 and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can add his OKRs under Objective 2"}]}]}]}, {"ts": "1703067617.762009", "text": "<!here> is an update on staging outage the day before\n\nOur RDS password has automatic password rotation every 7 days ( implemented after recent pen test)\nOn qa and dev environment code is deployed very frequently and password rotates with every deployment\nbut on staging env upgrade are not that frequent and hence it ran into this issue.\n\nWe will put a fix soon for env like staging or prod which do not get frequent updates and live on a build for more than 7 days", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1703067617.762009", "reply_count": 1, "edited": {"user": "U04DKEFP1K8", "ts": "1703079665.000000"}, "blocks": [{"type": "rich_text", "block_id": "TmQLI", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " is an update on staging outage the day before\n\nOur RDS password has automatic password rotation every 7 days ( implemented after recent pen test)\nOn qa and dev environment code is deployed very frequently and password rotates with every deployment\nbut on staging env upgrade are not that frequent and hence it ran into this issue.\n\nWe will put a fix soon for env like staging or prod which do not get frequent updates and live on a build for more than 7 days"}]}]}]}, {"ts": "1703038407.310869", "text": "<@U0658EW4B8D> <@U065H3M6WJV> what's your take on if we build a functionality for customers to build comp programs including marketing pricing vs building advanced reporting &amp; analytics? Which is a bigger pain point?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1703038407.310869", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "tvF44", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " what's your take on if we build a functionality for customers to build comp programs including marketing pricing vs building advanced reporting & analytics? Which is a bigger pain point?"}]}]}]}, {"ts": "1703034947.692999", "text": "yep. I think we should do that for comp builder, admin dashboard, paybands, settings/permissions, offer letters. Then let's dive into reporting &amp; analytics followed by marketing pricing functionality.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2WEdh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yep. I think we should do that for comp builder, admin dashboard, paybands, settings/permissions, offer letters. Then let's dive into reporting & analytics followed by marketing pricing functionality."}]}]}]}, {"ts": "1703033964.481609", "text": "<@U04DS2MBWP4> I have the regularly scheduled call with <PERSON> tomorrow, any specific topics? Otherwise I will probably walk him through the comp builder and get some feedback, since last time I had him react to the merit views.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eYfMy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I have the regularly scheduled call with <PERSON> tomorrow, any specific topics? Otherwise I will probably walk him through the comp builder and get some feedback, since last time I had him react to the merit views."}]}]}]}, {"ts": "1703020624.667309", "text": "This morning's working session, with <PERSON> walking through the comp cycle builder:\n\n<https://us06web.zoom.us/rec/share/j5QcYRsqxC-7Q-8jZImAeC1pSxbKwCbgSIQNwCgd_ojQBymEIL56ZgK5qCxAhh1z.7jN22wdSBrHXeqaw>\nPasscode: *!CMnUG2&amp;*", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nBGAA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This morning's working session, with <PERSON> walking through the comp cycle builder:\n\n"}, {"type": "link", "url": "https://us06web.zoom.us/rec/share/j5QcYRsqxC-7Q-8jZImAeC1pSxbKwCbgSIQNwCgd_ojQBymEIL56ZgK5qCxAhh1z.7jN22wdSBrHXeqaw"}, {"type": "text", "text": "\nPasscode: "}, {"type": "text", "text": "!CMnUG2&", "style": {"bold": true}}]}]}]}, {"ts": "1703017721.633069", "text": "Here is the first draft of company level OKRs. Let align all of your OKRs with this objective.\n\n Generate $500K in ARR and set revenue engine in motion\n    ◦ KR1: Achieve product market fit to set a solid foundation for long terms success (product)\n    ◦ KR2: Build a product that customer love and are willing to pay for (product)\n    ◦ KR3: Increase the delivery speed of new features with highest quality (eng)\n    ◦ KR4: Generate $500K in ARR (sales)\n    ◦ KR5: Establish compiify as a recognized and trusted brand (marketing)", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1703017733.000000"}, "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D", "U04DKEFP1K8"], "count": 2}, {"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "NIKS5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the first draft of company level OKRs. Let align all of your OKRs with this objective.\n\n Generate $500K in ARR and set revenue engine in motion\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "KR1: Achieve product market fit to set a solid foundation for long terms success (product)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "KR2: Build a product that customer love and are willing to pay for (product)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "KR3: Increase the delivery speed of new features with highest quality (eng)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "KR4: Generate $500K in ARR (sales)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "KR5: Establish compiify as a recognized and trusted brand (marketing)"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.560465"}