{"thread_ts": "1740583878.587619", "channel_id": "C065QSSNH8A", "reply_count": 15, "replies": [{"ts": "1740584198.041249", "text": "<@U07EJ2LP44S> just updating last Raise date won't help, We need to publish the cycle. I had published in the morning and should be fine if we publish again.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "Tnv1f", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " just updating last Raise date won't help, We need to publish the cycle. I had published in the morning and should be fine if we publish again."}]}]}]}, {"ts": "1740584326.834119", "text": "Oh, boo. Ok, shoudl I just include her then, and republish?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "HOHBC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Oh, boo. Ok, shoudl I just include her then, and republish?"}]}]}]}, {"ts": "1740584358.071619", "text": "Ideally we would still remove the last raise date to avoid confusion though", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "i3/8l", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ideally we would still remove the last raise date to avoid confusion though"}]}]}]}, {"ts": "1740584374.514299", "text": "Apparently these are 'change dates' they provided, and she actually took a paycut to move roles.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "2VNIj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Apparently these are 'change dates' they provided, and she actually took a paycut to move roles."}]}]}]}, {"ts": "1740584442.813339", "text": "yes, you can", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "XWBN0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes, you can"}]}]}]}, {"ts": "1740584451.708189", "text": "yes will remove", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "zMKDM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes will remove"}]}]}]}, {"ts": "1740585436.318609", "text": "<@U07EJ2LP44S> removed lastRaiseDate.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "nPsjA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " removed lastRaiseDate."}]}]}]}, {"ts": "1740587309.304699", "text": "I see it gone in the org view, and I went through and republished the cycle, but it's still there and she's still ineligible:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740583878.587619", "files": [{"id": "F08F7C33WFL", "created": 1740587301, "timestamp": 1740587301, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 59890, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F7C33WFL/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F7C33WFL/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F7C33WFL-6162c4ba63/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F7C33WFL-6162c4ba63/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F7C33WFL-6162c4ba63/image_360.png", "thumb_360_w": 360, "thumb_360_h": 51, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F7C33WFL-6162c4ba63/image_480.png", "thumb_480_w": 480, "thumb_480_h": 68, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F7C33WFL-6162c4ba63/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F7C33WFL-6162c4ba63/image_720.png", "thumb_720_w": 720, "thumb_720_h": 101, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F7C33WFL-6162c4ba63/image_800.png", "thumb_800_w": 800, "thumb_800_h": 113, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F7C33WFL-6162c4ba63/image_960.png", "thumb_960_w": 960, "thumb_960_h": 135, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F7C33WFL-6162c4ba63/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 144, "original_w": 2202, "original_h": 310, "thumb_tiny": "AwAGADDS57Yo5opaAExRS0UAJRS0UAf/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F7C33WFL/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F7C33WFL-a4d12c539e", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "RqYAh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I see it gone in the org view, and I went through and republished the cycle, but it's still there and she's still ineligible:"}]}]}]}, {"ts": "1740588187.552799", "text": "I went through to republish again, this time adding her as an included employee, but stopped here b/c this number is different. It was 3.7. I don't know why this is happening but I'm not going to republish b/c even if I change it back I'm worried it will mess up the numbers.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740583878.587619", "files": [{"id": "F08F4B876ER", "created": 1740588184, "timestamp": 1740588184, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 68688, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F4B876ER/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F4B876ER/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4B876ER-b7c60eca85/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4B876ER-b7c60eca85/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4B876ER-b7c60eca85/image_360.png", "thumb_360_w": 360, "thumb_360_h": 142, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4B876ER-b7c60eca85/image_480.png", "thumb_480_w": 480, "thumb_480_h": 190, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4B876ER-b7c60eca85/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4B876ER-b7c60eca85/image_720.png", "thumb_720_w": 720, "thumb_720_h": 285, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4B876ER-b7c60eca85/image_800.png", "thumb_800_w": 800, "thumb_800_h": 316, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4B876ER-b7c60eca85/image_960.png", "thumb_960_w": 960, "thumb_960_h": 379, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F4B876ER-b7c60eca85/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 405, "original_w": 1432, "original_h": 566, "thumb_tiny": "AwASADDSOfUUc+oqOaZYsbt3PpTPtceM4f8AKnYVyfn1FLTIpBKm5c496dj3NIYuRRSY96WgAooooAKKKKACiiigD//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F4B876ER/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F4B876ER-f28d96bf2e", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "8qNR0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I went through to republish again, this time adding her as an included employee, but stopped here b/c this number is different. It was 3.7. I don't know why this is happening but I'm not going to republish b/c even if I change it back I'm worried it will mess up the numbers."}]}]}]}, {"ts": "1740588310.179859", "text": "it would rounding off issue.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "N/har", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "it would rounding off issue."}]}]}]}, {"ts": "1740588338.521369", "text": "I am looking into the lastRaiseDate thing. I am still not sure why its showing.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "WJJvb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am looking into the lastRaiseDate thing. I am still not sure why its showing."}]}]}]}, {"ts": "1740588657.706809", "text": "<@U07EJ2LP44S> removed the lastRaiseDate all places now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "W5M1y", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " removed the lastRaiseDate all places now."}]}]}]}, {"ts": "1740588933.725589", "text": "Will you republish since i'm scared", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "MTzWn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will you republish since i'm scared"}]}]}]}, {"ts": "1740589436.989989", "text": "Ok On it", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "3jT3x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok On it"}]}]}]}, {"ts": "1740589587.202099", "text": "<@U07EJ2LP44S> Done.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740583878.587619", "blocks": [{"type": "rich_text", "block_id": "Ts22O", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Done."}]}]}]}], "created_at": "2025-05-22T22:00:49.357884"}