# RAGSearch Commands Guide

This document provides comprehensive instructions for using the Django shell scripts to manage databases and ingest data in the RAGSearch application.

## Overview

The RAGSearch application provides three main scripts for database management and data ingestion:

1. **`clean_database.py`** - Clean PostgreSQL and Qdrant vector databases
2. **`ingest_data.py`** - Ingest data from various sources
3. **`clean_and_ingest.py`** - Combined clean and ingest operation

All scripts are located in the `multi_source_rag/scripts/` directory and should be run from the Django project root.

## Prerequisites

Before running any scripts, ensure:

1. **Django Environment**: The Django environment is properly set up
2. **Database Access**: PostgreSQL and Qdrant are running and accessible
3. **Data Directory**: Your data files are organized in the expected structure
4. **Permissions**: You have appropriate permissions to read data files and write to databases

## Script 1: Database Cleaning (`clean_database.py`)

### Purpose
Cleans the PostgreSQL database and Qdrant vector database by removing all documents, chunks, embeddings, and related metadata.

### Basic Usage

```bash
# Clean all data for all tenants
python scripts/clean_database.py --confirm

# Show what would be cleaned without actually cleaning
python scripts/clean_database.py --stats-only

# Clean data for a specific tenant
python scripts/clean_database.py --tenant default --confirm

# Clean only PostgreSQL database
python scripts/clean_database.py --postgres-only --confirm

# Clean only vector database
python scripts/clean_database.py --vector-only --confirm
```

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--tenant SLUG` | Clean data for specific tenant only | All tenants |
| `--confirm` | Skip confirmation prompt | False |
| `--postgres-only` | Clean only PostgreSQL database | False |
| `--vector-only` | Clean only vector database | False |
| `--stats-only` | Show statistics without cleaning | False |

### Examples

```bash
# Interactive cleaning with confirmation
python scripts/clean_database.py

# Quick clean for development
python scripts/clean_database.py --confirm

# Clean specific tenant's data
python scripts/clean_database.py --tenant my-company --confirm

# Check current database statistics
python scripts/clean_database.py --stats-only
```

## Script 2: Data Ingestion (`ingest_data.py`)

### Purpose
Ingests data from various sources (Slack, files) into the RAGSearch system using LlamaIndex-based processing.

### Basic Usage

```bash
# Auto-detect source type and ingest
python scripts/ingest_data.py --data-dir /path/to/data

# Ingest Slack data specifically
python scripts/ingest_data.py --data-dir /path/to/slack/data --source-type slack

# Ingest with custom configuration
python scripts/ingest_data.py --data-dir /path/to/data --config-file config.json

# Ingest limited number of documents for testing
python scripts/ingest_data.py --data-dir /path/to/data --limit 100
```

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--data-dir PATH` | Path to data directory | Required |
| `--tenant SLUG` | Tenant slug for ingestion | default |
| `--user-email EMAIL` | User email for attribution | <EMAIL> |
| `--source-type TYPE` | Source type: slack, file, auto | auto |
| `--batch-size SIZE` | Batch size for processing | 50 |
| `--limit COUNT` | Limit documents to process | None |
| `--config-file PATH` | JSON configuration file | None |
| `--stats-only` | Show statistics without ingesting | False |

### Data Directory Structure

#### For Slack Data
```
data/
├── channel_C065QSSNH8A/
│   ├── messages/
│   │   ├── messages_2024-01-01.json
│   │   └── ...
│   ├── threads/
│   │   ├── thread_1234567890.json
│   │   └── ...
│   └── users/
│       └── users.json
└── consolidated/
    ├── C065QSSNH8A_2024-01.json
    └── ...
```

#### For File Data
```
data/
├── documents/
│   ├── file1.txt
│   ├── file2.md
│   └── subfolder/
│       └── file3.pdf
└── ...
```

### Configuration File Format

Create a JSON configuration file to customize ingestion behavior:

```json
{
  "time_period": "custom",
  "custom_days": 365,
  "chunking_strategy": "monthly",
  "include_threads": true,
  "filter_bots": true,
  "min_message_length": 10,
  "max_chunk_size": 8000,
  "overlap_size": 200,
  "quality_threshold": 0.3,
  "enable_semantic_cross_refs": true
}
```

### Examples

```bash
# Ingest Slack data from the data folder
python scripts/ingest_data.py --data-dir ../data

# Ingest with specific tenant and user
python scripts/ingest_data.py \
  --data-dir ../data \
  --tenant my-company \
  --user-email <EMAIL>

# Ingest with custom batch size and limit
python scripts/ingest_data.py \
  --data-dir ../data \
  --batch-size 25 \
  --limit 500

# Ingest with custom configuration
python scripts/ingest_data.py \
  --data-dir ../data \
  --config-file slack_config.json

# Check ingestion statistics
python scripts/ingest_data.py --stats-only
```

## Script 3: Combined Clean and Ingest (`clean_and_ingest.py`)

### Purpose
Provides a one-command solution to clean the database and ingest fresh data with safety checks and rollback capabilities.

### Basic Usage

```bash
# Clean and ingest with confirmation
python scripts/clean_and_ingest.py --data-dir /path/to/data

# Clean and ingest without confirmation (for automation)
python scripts/clean_and_ingest.py --data-dir /path/to/data --confirm

# Skip cleaning and only ingest
python scripts/clean_and_ingest.py --data-dir /path/to/data --skip-cleaning
```

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--data-dir PATH` | Path to data directory | Required |
| `--tenant SLUG` | Tenant slug | default |
| `--user-email EMAIL` | User email for attribution | <EMAIL> |
| `--source-type TYPE` | Source type: slack, file, auto | auto |
| `--batch-size SIZE` | Batch size for processing | 50 |
| `--limit COUNT` | Limit documents to process | None |
| `--config-file PATH` | JSON configuration file | None |
| `--postgres-only` | Clean only PostgreSQL database | False |
| `--vector-only` | Clean only vector database | False |
| `--skip-cleaning` | Skip database cleaning phase | False |
| `--no-backup` | Disable backup creation | False |
| `--confirm` | Skip confirmation prompts | False |

### Examples

```bash
# Full clean and ingest with confirmation
python scripts/clean_and_ingest.py --data-dir ../data

# Automated clean and ingest for CI/CD
python scripts/clean_and_ingest.py \
  --data-dir ../data \
  --confirm \
  --no-backup

# Clean and ingest specific tenant
python scripts/clean_and_ingest.py \
  --data-dir ../data \
  --tenant production \
  --user-email <EMAIL> \
  --confirm

# Only clean PostgreSQL and ingest
python scripts/clean_and_ingest.py \
  --data-dir ../data \
  --postgres-only \
  --confirm

# Skip cleaning and only ingest new data
python scripts/clean_and_ingest.py \
  --data-dir ../data \
  --skip-cleaning
```

## Django Shell Usage

You can also run these operations directly in the Django shell for more control:

```python
# Start Django shell
python manage.py shell

# Import and use the scripts
from scripts.clean_database import DatabaseCleaner
from scripts.ingest_data import DataIngester

# Clean database
cleaner = DatabaseCleaner(tenant_slug="default")
cleaner.clean_all()

# Ingest data
ingester = DataIngester(tenant_slug="default")
processed, failed = ingester.ingest_slack_data("../data")
```

## Common Workflows

### Development Workflow
```bash
# 1. Clean everything and start fresh
python scripts/clean_database.py --confirm

# 2. Ingest test data
python scripts/ingest_data.py --data-dir ../data --limit 100

# 3. Test the application
python manage.py runserver
```

### Production Data Refresh
```bash
# 1. Clean and ingest in one command with backup
python scripts/clean_and_ingest.py \
  --data-dir /production/data \
  --tenant production \
  --confirm

# 2. Verify ingestion
python scripts/ingest_data.py --stats-only
```

### Testing New Configuration
```bash
# 1. Test with limited data first
python scripts/clean_and_ingest.py \
  --data-dir ../data \
  --config-file test_config.json \
  --limit 50 \
  --confirm

# 2. If successful, run full ingestion
python scripts/clean_and_ingest.py \
  --data-dir ../data \
  --config-file test_config.json \
  --confirm
```

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure you have read access to data files and write access to databases
2. **Database Connection**: Verify PostgreSQL and Qdrant are running
3. **Memory Issues**: Reduce batch size for large datasets
4. **Data Format**: Ensure data files are in the expected JSON format for Slack data

### Error Recovery

If an operation fails:

1. Check the error logs for specific issues
2. Use `--stats-only` to check current state
3. Clean partially ingested data if needed
4. Fix the underlying issue and retry

### Performance Tips

1. Use appropriate batch sizes (25-100 for most cases)
2. Limit initial ingestion for testing
3. Monitor memory usage during large ingestions
4. Use `--postgres-only` or `--vector-only` for targeted cleaning
