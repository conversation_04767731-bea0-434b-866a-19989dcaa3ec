"""
GitHub Discussions interface for document source.
"""

import hashlib
import json
import logging
import re
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlparse

from django.utils import timezone
from github import Github, GithubException, RateLimitExceededException
from github.Repository import Repository

from .base import DocumentSourceInterface
from .github import RateLimitHandler

# Set up logging
logger = logging.getLogger(__name__)


class GitHubDiscussionsSourceInterface(DocumentSourceInterface):
    """
    Interface for GitHub Discussions document source.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the GitHub Discussions source interface.

        Args:
            config: Configuration for the GitHub Discussions source
        """
        super().__init__(config)
        self.token = config.get("token")
        self.owner = config.get("owner")
        self.repo = config.get("repo")
        self.client = None
        self.rate_limit_handler = RateLimitHandler()
        self.initialize_client()

    def initialize_client(self) -> None:
        """
        Initialize the GitHub client.
        """
        if not self.token:
            raise ValueError("GitHub token is required")

        try:
            self.client = Github(self.token, per_page=100)
        except Exception as e:
            raise ValueError(f"Failed to initialize GitHub client: {str(e)}")

    def validate_config(self) -> bool:
        """
        Validate the configuration.

        Returns:
            bool: True if the configuration is valid, False otherwise
        """
        required_fields = ["token", "owner", "repo"]
        for field in required_fields:
            if not self.config.get(field):
                return False

        return True

    def get_source_type(self) -> str:
        """
        Get the type of the source.

        Returns:
            str: Type of the source
        """
        return "github_discussions"

    def fetch_documents(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch documents from GitHub Discussions.

        Args:
            **kwargs: Additional arguments for fetching documents
                - days: Number of days to fetch (default: 30)
                - categories: List of discussion categories to fetch (default: all)
                - max_discussions: Maximum number of discussions to fetch (default: 100)

        Returns:
            List[Dict[str, Any]]: List of documents
        """
        if not self.client:
            self.initialize_client()

        # Parse arguments
        days = kwargs.get("days", 30)
        categories = kwargs.get("categories", [])
        max_discussions = kwargs.get("max_discussions", 100)

        documents = []

        try:
            # Get repository
            repo = self.client.get_repo(f"{self.owner}/{self.repo}")

            # Check if discussions are enabled
            if not repo.has_discussions:
                logger.warning(
                    f"Discussions are not enabled for repository {self.owner}/{self.repo}"
                )
                return documents

            # Use GraphQL to fetch discussions
            # Note: The GitHub API v3 doesn't support discussions, so we need to use GraphQL
            discussions = self._fetch_discussions_graphql(
                days, categories, max_discussions
            )

            # Process discussions
            for discussion in discussions:
                try:
                    # Create document for the discussion
                    discussion_doc = self._process_discussion(discussion)
                    documents.append(discussion_doc)

                    # Process comments
                    comments = discussion.get("comments", [])
                    for comment in comments:
                        comment_doc = self._process_discussion_comment(
                            discussion, comment
                        )
                        documents.append(comment_doc)
                except Exception as e:
                    logger.error(
                        f"Error processing discussion {discussion.get('number')}: {str(e)}"
                    )

            return documents

        except RateLimitExceededException:
            # Handle rate limit
            if self.rate_limit_handler.handle_rate_limit(self.client):
                # Retry after waiting
                return self.fetch_documents(**kwargs)
            else:
                raise ValueError(
                    "GitHub API rate limit exceeded and could not be handled"
                )

        except GithubException as e:
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            raise ValueError(
                f"Error fetching documents from GitHub Discussions: {str(e)}"
            )

    def get_document(self, document_id: str, **kwargs) -> Dict[str, Any]:
        """
        Get a specific document from GitHub Discussions.

        Args:
            document_id: ID of the document to get
            **kwargs: Additional arguments for getting the document

        Returns:
            Dict[str, Any]: Document
        """
        if not self.client:
            self.initialize_client()

        try:
            # Parse document ID
            parts = document_id.split("/")
            if len(parts) < 2:
                raise ValueError(
                    f"Invalid document ID: {document_id}. Expected format: 'discussion/number' or 'comment/number'"
                )

            doc_type, number = parts[0], parts[1]

            if doc_type == "discussion":
                # Fetch discussion
                discussion = self._fetch_discussion_graphql(number)
                return self._process_discussion(discussion)
            elif doc_type == "comment":
                # Fetch comment
                if len(parts) < 3:
                    raise ValueError(
                        f"Invalid comment ID: {document_id}. Expected format: 'comment/discussion_number/comment_id'"
                    )

                discussion_number, comment_id = parts[1], parts[2]
                discussion = self._fetch_discussion_graphql(discussion_number)

                # Find the comment
                for comment in discussion.get("comments", []):
                    if comment.get("id") == comment_id:
                        return self._process_discussion_comment(discussion, comment)

                raise ValueError(
                    f"Comment {comment_id} not found in discussion {discussion_number}"
                )
            else:
                raise ValueError(f"Unsupported document type: {doc_type}")

        except RateLimitExceededException:
            # Handle rate limit
            if self.rate_limit_handler.handle_rate_limit(self.client):
                # Retry after waiting
                return self.get_document(document_id, **kwargs)
            else:
                raise ValueError(
                    "GitHub API rate limit exceeded and could not be handled"
                )

        except GithubException as e:
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            raise ValueError(
                f"Error getting document from GitHub Discussions: {str(e)}"
            )

    def search_documents(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for documents in GitHub Discussions.

        Args:
            query: Query to search for
            **kwargs: Additional arguments for searching documents
                - max_results: Maximum number of results to return (default: 10)

        Returns:
            List[Dict[str, Any]]: List of documents
        """
        if not self.client:
            self.initialize_client()

        # Parse arguments
        max_results = kwargs.get("max_results", 10)

        documents = []

        try:
            # Use GraphQL to search discussions
            discussions = self._search_discussions_graphql(query, max_results)

            # Process discussions
            for discussion in discussions:
                try:
                    # Create document for the discussion
                    discussion_doc = self._process_discussion(discussion)
                    documents.append(discussion_doc)
                except Exception as e:
                    logger.error(
                        f"Error processing discussion {discussion.get('number')}: {str(e)}"
                    )

            return documents

        except RateLimitExceededException:
            # Handle rate limit
            if self.rate_limit_handler.handle_rate_limit(self.client):
                # Retry after waiting
                return self.search_documents(query, **kwargs)
            else:
                raise ValueError(
                    "GitHub API rate limit exceeded and could not be handled"
                )

        except GithubException as e:
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            raise ValueError(
                f"Error searching documents in GitHub Discussions: {str(e)}"
            )

    def _fetch_discussions_graphql(
        self, days: int, categories: List[str], max_discussions: int
    ) -> List[Dict[str, Any]]:
        """
        Fetch discussions using GraphQL.

        Args:
            days: Number of days to fetch
            categories: List of discussion categories to fetch
            max_discussions: Maximum number of discussions to fetch

        Returns:
            List[Dict[str, Any]]: List of discussions
        """
        # Note: This is a simplified implementation
        # In a production system, you would use the GitHub GraphQL API to fetch discussions
        # For this example, we'll return a mock response

        # Mock discussions
        discussions = [
            {
                "id": "D_1",
                "number": 1,
                "title": "Welcome to Discussions",
                "body": "This is the first discussion in this repository.",
                "category": {"name": "General", "emoji": "💬"},
                "author": {"login": "octocat", "url": "https://github.com/octocat"},
                "createdAt": (datetime.now() - timedelta(days=5)).isoformat(),
                "updatedAt": (datetime.now() - timedelta(days=2)).isoformat(),
                "url": f"https://github.com/{self.owner}/{self.repo}/discussions/1",
                "comments": [
                    {
                        "id": "DC_1_1",
                        "body": "This is a comment on the first discussion.",
                        "author": {"login": "user1", "url": "https://github.com/user1"},
                        "createdAt": (datetime.now() - timedelta(days=4)).isoformat(),
                        "url": f"https://github.com/{self.owner}/{self.repo}/discussions/1#discussioncomment-1",
                    }
                ],
            }
        ]

        return discussions

    def _fetch_discussion_graphql(self, number: str) -> Dict[str, Any]:
        """
        Fetch a specific discussion using GraphQL.

        Args:
            number: Discussion number

        Returns:
            Dict[str, Any]: Discussion
        """
        # Mock discussion
        discussion = {
            "id": f"D_{number}",
            "number": int(number),
            "title": f"Discussion {number}",
            "body": f"This is discussion {number}.",
            "category": {"name": "General", "emoji": "💬"},
            "author": {"login": "octocat", "url": "https://github.com/octocat"},
            "createdAt": (datetime.now() - timedelta(days=5)).isoformat(),
            "updatedAt": (datetime.now() - timedelta(days=2)).isoformat(),
            "url": f"https://github.com/{self.owner}/{self.repo}/discussions/{number}",
            "comments": [
                {
                    "id": f"DC_{number}_1",
                    "body": f"This is a comment on discussion {number}.",
                    "author": {"login": "user1", "url": "https://github.com/user1"},
                    "createdAt": (datetime.now() - timedelta(days=4)).isoformat(),
                    "url": f"https://github.com/{self.owner}/{self.repo}/discussions/{number}#discussioncomment-1",
                }
            ],
        }

        return discussion

    def _search_discussions_graphql(
        self, query: str, max_results: int
    ) -> List[Dict[str, Any]]:
        """
        Search discussions using GraphQL.

        Args:
            query: Query to search for
            max_results: Maximum number of results to return

        Returns:
            List[Dict[str, Any]]: List of discussions
        """
        # Mock search results
        discussions = [
            {
                "id": "D_1",
                "number": 1,
                "title": f"Discussion about {query}",
                "body": f"This discussion mentions {query} in the body.",
                "category": {"name": "General", "emoji": "💬"},
                "author": {"login": "octocat", "url": "https://github.com/octocat"},
                "createdAt": (datetime.now() - timedelta(days=5)).isoformat(),
                "updatedAt": (datetime.now() - timedelta(days=2)).isoformat(),
                "url": f"https://github.com/{self.owner}/{self.repo}/discussions/1",
            }
        ]

        return discussions[:max_results]

    def _process_discussion(self, discussion: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a discussion into a document.

        Args:
            discussion: GitHub discussion

        Returns:
            Dict[str, Any]: Processed document
        """
        # Create document ID
        document_id = f"discussion/{discussion['number']}"

        # Parse dates
        created_at = datetime.fromisoformat(
            discussion["createdAt"].replace("Z", "+00:00")
        )
        updated_at = datetime.fromisoformat(
            discussion["updatedAt"].replace("Z", "+00:00")
        )

        # Create document
        document = {
            "id": document_id,
            "external_id": str(discussion["number"]),
            "title": discussion["title"],
            "content": discussion["body"],
            "content_type": "markdown",
            "metadata": {
                "source_type": "github_discussions",
                "repo": f"{self.owner}/{self.repo}",
                "discussion_id": discussion["id"],
                "discussion_number": discussion["number"],
                "category": discussion["category"]["name"],
                "category_emoji": discussion["category"]["emoji"],
                "author": discussion["author"]["login"],
                "author_url": discussion["author"]["url"],
                "comment_count": len(discussion.get("comments", [])),
                "html_url": discussion["url"],
            },
            "created_at": created_at,
            "updated_at": updated_at,
        }

        return document

    def _process_discussion_comment(
        self, discussion: Dict[str, Any], comment: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process a discussion comment into a document.

        Args:
            discussion: GitHub discussion
            comment: Discussion comment

        Returns:
            Dict[str, Any]: Processed document
        """
        # Create document ID
        document_id = f"comment/{discussion['number']}/{comment['id']}"

        # Parse dates
        created_at = datetime.fromisoformat(comment["createdAt"].replace("Z", "+00:00"))

        # Create document
        document = {
            "id": document_id,
            "external_id": comment["id"],
            "title": f"Comment on '{discussion['title']}'",
            "content": comment["body"],
            "content_type": "markdown",
            "metadata": {
                "source_type": "github_discussions",
                "repo": f"{self.owner}/{self.repo}",
                "discussion_id": discussion["id"],
                "discussion_number": discussion["number"],
                "discussion_title": discussion["title"],
                "comment_id": comment["id"],
                "author": comment["author"]["login"],
                "author_url": comment["author"]["url"],
                "html_url": comment["url"],
                "is_comment": True,
            },
            "created_at": created_at,
            "updated_at": created_at,
        }

        return document
