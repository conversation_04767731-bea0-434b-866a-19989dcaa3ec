{"date": "2024-09-10", "channel_id": "C065QSSNH8A", "message_count": 19, "messages": [{"ts": "1725991931.966479", "text": "<@U0690EB5JE5> Do you have an update on Diversified's Paycor data?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1725991931.966479", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "RDEFr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Do you have an update on Diversified's Paycor data?"}]}]}]}, {"ts": "1725990403.546429", "text": "<@U04DKEFP1K8> Is this correct for instructions? I know we need to change the url <https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1725990403.546429", "reply_count": 6, "files": [{"id": "F07M69YTJ3T", "created": 1725990406, "timestamp": 1725990406, "name": "Setting up Azure AD as SAML enterprise connection", "title": "Setting up Azure AD as SAML enterprise connection", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 124649, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50", "external_url": "https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit", "url_private": "https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07M69YTJ3T-094c03ba52/setting_up_azure_ad_as_saml_enterprise_connection_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1035, "thumb_tiny": "AwAwACXTJxSZ9v1ob6ZpAD7UALk+lLSc+1LzQAUUUUAI30puDTmpvHqaAF+alyfSjj1pfxoAKKKKAEP1pu0HvTyM03YKADb70uBSbBSgAdKAFooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07M69YTJ3T/setting_up_azure_ad_as_saml_enterprise_connection", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "wDUhM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Is this correct for instructions? I know we need to change the url "}, {"type": "link", "url": "https://docs.google.com/document/d/1NT9_yXuSQUON4cVqK8g6X1v88W7Jz6ZNnl2ewHzdr50/edit"}]}]}]}, {"ts": "1725989667.113099", "text": "Do you have the emails for the other two guys, ka<PERSON><PERSON>? I only have brians'", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1725989667.113099", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "P4b3h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you have the emails for the other two guys, ka<PERSON><PERSON>? I only have brians'"}]}]}]}, {"ts": "1725989645.483239", "text": "Good call with <PERSON><PERSON><PERSON>. Doesn't look any thing complex with them so far", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "AmVbA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Good call with <PERSON><PERSON><PERSON>. Doesn't look any thing complex with them so far"}]}]}]}, {"ts": "1725983170.393499", "text": "OK, we can discuss the companywide and customer success care okrs today. We will move engineering  okrs to tomorrow", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1725983170.393499", "reply_count": 1, "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5", "U07EJ2LP44S"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "jbkZd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OK, we can discuss the companywide and customer success care okrs today. We will move engineering  okrs to tomorrow"}]}]}]}, {"ts": "1725983104.815369", "text": "will work with <@U04DKEFP1K8> on this my morning.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "po4T7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will work with "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " on this my morning."}]}]}]}, {"ts": "1725983057.509019", "text": "<@U04DS2MBWP4> Sorry, I was deep into integrations work and haven't got a chance to look into OKR stuff.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yv7jf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Sorry, I was deep into integrations work and haven't got a chance to look into OKR stuff."}]}]}]}, {"ts": "1725983018.011719", "text": "Also I am not aware if there are any new requirements to prioritize. Its been a while we discussed it feels like.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "iZhD3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also I am not aware if there are any new requirements to prioritize. Its been a while we discussed it feels like."}]}]}]}, {"ts": "1725983014.817369", "text": "The agenda for today’s meeting is to go over the OKRs", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mhoVL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The agenda for today’s meeting is to go over the OKRs"}]}]}]}, {"ts": "1725982964.218159", "text": "I am fine to skip meeting if needed.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5SG2y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am fine to skip meeting if needed."}]}]}]}, {"ts": "1725982948.076399", "text": "We can do share updates offline also.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yl1OQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can do share updates offline also."}]}]}]}, {"ts": "1725982933.232139", "text": "• Enable three performance rating columns - DegenKolb\n• Justification requirements for promotions and pay increases - DegenKolb\n• Certain pay bands have an extended maximum value, with an additional amount available beyond the standard maximum - DegenKolb\n• Managing visibility of pay ranges available to reviewers - DegenKolb", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1725982933.232139", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "PrGCG", "elements": [{"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Enable three performance rating columns - DegenKolb"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Justification requirements for promotions and pay increases - DegenKolb"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Certain pay bands have an extended maximum value, with an additional amount available beyond the standard maximum - DegenKolb"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Managing visibility of pay ranges available to reviewers - DegenKolb"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1725982887.258209", "text": "<@U07EJ2LP44S> I am waiting on clarifications/confirmation on recent new customer requirements. Needed an update. Other stuff can wait till tomorrow.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oI4aH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am waiting on clarifications/confirmation on recent new customer requirements. Needed an update. Other stuff can wait till tomorrow."}]}]}]}, {"ts": "1725982816.468099", "text": "<@U0690EB5JE5> since we only have 30 minutes today, do you have any customer questions for me? All I have for you right now is an update on Diversified's paycor data", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QAE9w", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " since we only have 30 minutes today, do you have any customer questions for me? All I have for you right now is an update on Diversified's paycor data"}]}]}]}, {"ts": "1725982763.600529", "text": "<!here> i have a last minute errand came up at home, will be joining curana call at 930am", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "AuPTF", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " i have a last minute errand came up at home, will be joining curana call at 930am"}]}]}]}, {"ts": "1725980180.466539", "text": "Sure <@U04DS2MBWP4> end of this week worst case.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "prTq1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " end of this week worst case."}]}]}]}, {"ts": "1725979907.992259", "text": "we have an imp demo next week. can we push audit log feature to demo env? <@U0690EB5JE5> <@U04DKEFP1K8>", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1ogwA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we have an imp demo next week. can we push audit log feature to demo env? "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1725965748.059959", "text": "<@U04DKEFP1K8> Following changes are deployed to <http://test.stridehr.io|test.stridehr.io>\n• Issue while creating compensation cycle - <https://compiify.atlassian.net/browse/COM-3541>\n• Unable to control viewing recommendation for individual merit component - <https://compiify.atlassian.net/browse/COM-3541>\n• `Sync` buttons on integration page are available only for Stride admins\n• diversified energy data sync via integration tested and fixed some bugs and optimized the slowness in sync with large dataset\n• Audit Log feature is now available only in <http://qa.stridehr.io|qa.stridehr.io>. We will stress test the feature in this ENV and deploy to regular ENVs", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1725966939.000000"}, "blocks": [{"type": "rich_text", "block_id": "BBoJE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Following changes are deployed to "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Issue while creating compensation cycle - "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3541"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Unable to control viewing recommendation for individual merit component - "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3541"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Sync", "style": {"code": true}}, {"type": "text", "text": " buttons on integration page are available only for Stride admins"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "diversified energy data sync via integration tested and fixed some bugs and optimized the slowness in sync with large dataset"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Audit Log feature is now available only in "}, {"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}, {"type": "text", "text": ". We will stress test the feature in this ENV and deploy to regular ENVs"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1725958501.695519", "text": "Agenda for today:\n• Review open questions on customer requirements\n• Cycle Builder Usability enhancements", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1725965049.000000"}, "blocks": [{"type": "rich_text", "block_id": "8p49s", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Review open questions on customer requirements"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle Builder Usability enhancements"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.640950"}