#!/usr/bin/env python
"""
Comprehensive headless browser UAT for the RAG Search system.
"""
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def setup_headless_browser():
    """Setup headless Chrome browser."""
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    driver = webdriver.Chrome(options=chrome_options)
    return driver

def test_search_functionality(driver, base_url="http://127.0.0.1:8000"):
    """Test the search functionality comprehensively."""
    print("🔍 COMPREHENSIVE HEADLESS BROWSER UAT")
    print("=" * 80)
    
    test_results = {
        "total_tests": 0,
        "passed_tests": 0,
        "failed_tests": 0,
        "test_details": []
    }
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "Basic Search - Issue Tracking",
            "query": "List issues reported by Amanda",
            "expected_elements": ["search-results", "citations"],
            "timeout": 30
        },
        {
            "name": "Technical Query - Bug Reports", 
            "query": "Show me all bug reports",
            "expected_elements": ["search-results", "citations"],
            "timeout": 30
        },
        {
            "name": "Project Status Query",
            "query": "What's the latest on Curana project?",
            "expected_elements": ["search-results", "citations"],
            "timeout": 30
        },
        {
            "name": "General Discussion Query",
            "query": "What is discussed in the engineering channel?",
            "expected_elements": ["search-results", "citations"],
            "timeout": 30
        },
        {
            "name": "Person-Specific Query",
            "query": "What problems did Rachel mention?",
            "expected_elements": ["search-results", "citations"],
            "timeout": 30
        }
    ]
    
    try:
        # Navigate to search page
        print(f"🌐 Navigating to {base_url}/search/")
        driver.get(f"{base_url}/search/")
        
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        print("✅ Search page loaded successfully")
        
        # Check if user is logged in
        try:
            login_required = driver.find_element(By.CLASS_NAME, "login-required")
            print("❌ User not logged in - login required")
            return test_results
        except NoSuchElementException:
            print("✅ User is logged in")
        
        # Run test scenarios
        for i, scenario in enumerate(test_scenarios):
            print(f"\n📋 Test {i+1}: {scenario['name']}")
            print(f"🔍 Query: '{scenario['query']}'")
            
            test_results["total_tests"] += 1
            test_detail = {
                "name": scenario["name"],
                "query": scenario["query"],
                "status": "FAILED",
                "error": None,
                "citations_found": 0,
                "response_time": 0
            }
            
            try:
                # Find search input
                search_input = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.NAME, "query"))
                )
                
                # Clear and enter query
                search_input.clear()
                search_input.send_keys(scenario["query"])
                print("   ✅ Query entered")
                
                # Find and click search button
                search_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
                
                # Record start time
                start_time = time.time()
                search_button.click()
                print("   ✅ Search submitted")
                
                # Wait for results or error
                try:
                    # Check for error message first
                    error_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.CLASS_NAME, "error-message"))
                    )
                    error_text = error_element.text
                    print(f"   ❌ Error found: {error_text}")
                    test_detail["error"] = error_text
                    test_detail["status"] = "ERROR"
                    
                    # Check if it's the dimension error
                    if "dimension error" in error_text.lower() or "400" in error_text:
                        print("   🚨 DIMENSION MISMATCH ERROR CONFIRMED!")
                        test_detail["error"] = "Embedding dimension mismatch (384d vs 768d)"
                    
                except TimeoutException:
                    # No error found, check for results
                    try:
                        # Wait for search results
                        results_container = WebDriverWait(driver, scenario["timeout"]).until(
                            EC.presence_of_element_located((By.CLASS_NAME, "search-results"))
                        )
                        
                        response_time = time.time() - start_time
                        test_detail["response_time"] = response_time
                        print(f"   ✅ Results loaded in {response_time:.2f}s")
                        
                        # Check for citations
                        try:
                            citations = driver.find_elements(By.CSS_SELECTOR, ".citation, .source-citation, [class*='citation']")
                            citation_count = len(citations)
                            test_detail["citations_found"] = citation_count
                            
                            if citation_count > 0:
                                print(f"   ✅ Found {citation_count} citations")
                                test_detail["status"] = "PASSED"
                                test_results["passed_tests"] += 1
                            else:
                                print(f"   ❌ No citations found")
                                test_detail["status"] = "NO_CITATIONS"
                                test_results["failed_tests"] += 1
                                
                        except Exception as citation_error:
                            print(f"   ❌ Error checking citations: {citation_error}")
                            test_detail["error"] = f"Citation check failed: {citation_error}"
                            test_results["failed_tests"] += 1
                            
                    except TimeoutException:
                        response_time = time.time() - start_time
                        test_detail["response_time"] = response_time
                        print(f"   ❌ Search timed out after {response_time:.2f}s")
                        test_detail["error"] = f"Search timeout after {response_time:.2f}s"
                        test_results["failed_tests"] += 1
                
            except Exception as e:
                print(f"   ❌ Test failed: {e}")
                test_detail["error"] = str(e)
                test_results["failed_tests"] += 1
            
            test_results["test_details"].append(test_detail)
            
            # Wait between tests
            time.sleep(2)
        
    except Exception as e:
        print(f"❌ UAT setup failed: {e}")
        test_results["test_details"].append({
            "name": "UAT Setup",
            "status": "FAILED",
            "error": str(e)
        })
    
    return test_results

def main():
    """Run the comprehensive UAT."""
    driver = None
    try:
        driver = setup_headless_browser()
        results = test_search_functionality(driver)
        
        # Print summary
        print(f"\n📊 UAT SUMMARY")
        print("=" * 80)
        print(f"Total tests: {results['total_tests']}")
        print(f"Passed: {results['passed_tests']}")
        print(f"Failed: {results['failed_tests']}")
        print(f"Success rate: {(results['passed_tests']/results['total_tests'])*100:.1f}%" if results['total_tests'] > 0 else "0%")
        
        # Detailed results
        print(f"\n📋 DETAILED RESULTS:")
        for test in results["test_details"]:
            status_icon = "✅" if test["status"] == "PASSED" else "❌"
            print(f"{status_icon} {test['name']}: {test['status']}")
            if test.get("error"):
                print(f"   Error: {test['error']}")
            if test.get("citations_found", 0) > 0:
                print(f"   Citations: {test['citations_found']}")
            if test.get("response_time", 0) > 0:
                print(f"   Response time: {test['response_time']:.2f}s")
        
        # Overall assessment
        if results["passed_tests"] == results["total_tests"] and results["total_tests"] > 0:
            print(f"\n🎉 ALL TESTS PASSED - SYSTEM IS PRODUCTION READY!")
            return True
        elif results["passed_tests"] > 0:
            print(f"\n⚠️  PARTIAL SUCCESS - {results['passed_tests']}/{results['total_tests']} tests passed")
            return False
        else:
            print(f"\n❌ ALL TESTS FAILED - SYSTEM IS BROKEN")
            return False
            
    except Exception as e:
        print(f"❌ UAT failed: {e}")
        return False
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
