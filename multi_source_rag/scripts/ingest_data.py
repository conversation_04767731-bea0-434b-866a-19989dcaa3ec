#!/usr/bin/env python
"""
Enhanced Data Ingestion Script for RAGSearch

This script provides comprehensive data ingestion functionality using the
LlamaIndex-based ingestion services. It supports various data sources and
provides detailed progress tracking and error handling.

Usage:
    python scripts/ingest_data.py [--tenant TENANT_SLUG] [--source-type SOURCE_TYPE] [--data-dir DATA_DIR] [--batch-size BATCH_SIZE] [--limit LIMIT]
"""

import argparse
import os
import sys
import django
import logging
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, DocumentProcessingJob
from apps.documents.services.llama_ingestion_service_unified import UnifiedLlamaIndexIngestionService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DataIngester:
    """Enhanced data ingester with comprehensive functionality."""

    def __init__(self, tenant_slug: str = "default", user_email: str = "<EMAIL>"):
        """
        Initialize the data ingester.

        Args:
            tenant_slug: Tenant slug for data ingestion
            user_email: User email for processing attribution
        """
        self.tenant_slug = tenant_slug
        self.user_email = user_email
        self.tenant = None
        self.user = None
        self.ingestion_service = None
        self.stats = {
            'start_time': None,
            'end_time': None,
            'documents_processed': 0,
            'documents_failed': 0,
            'sources_created': 0,
            'processing_jobs': 0
        }

        self._setup_tenant_and_user()
        self._setup_ingestion_service()

    def _setup_tenant_and_user(self) -> None:
        """Set up tenant and user for ingestion."""
        # Get or create tenant
        self.tenant, created = Tenant.objects.get_or_create(
            slug=self.tenant_slug,
            defaults={"name": f"Tenant {self.tenant_slug.title()}"}
        )
        if created:
            logger.info(f"Created new tenant: {self.tenant.name}")
        else:
            logger.info(f"Using existing tenant: {self.tenant.name}")

        # Get or create user
        try:
            self.user = User.objects.get(email=self.user_email)
            logger.info(f"Using existing user: {self.user.email}")
        except User.DoesNotExist:
            # Create a default user if none exists
            self.user = User.objects.create_user(
                username=self.user_email.split('@')[0],
                email=self.user_email,
                password='defaultpassword123'
            )
            logger.info(f"Created new user: {self.user.email}")

    def _setup_ingestion_service(self) -> None:
        """Set up the LlamaIndex ingestion service."""
        self.ingestion_service = UnifiedLlamaIndexIngestionService(
            tenant=self.tenant,
            user=self.user
        )
        logger.info("Initialized LlamaIndex ingestion service")

    def create_slack_source(self, data_dir: str, config: Optional[Dict[str, Any]] = None) -> DocumentSource:
        """
        Create a Slack document source.

        Args:
            data_dir: Path to the data directory
            config: Optional configuration overrides

        Returns:
            DocumentSource: Created document source
        """
        default_config = {
            "data_dir": data_dir,
            "time_period": "custom",
            "custom_days": 730,  # Two years
            "enable_semantic_cross_refs": True,
            "quality_threshold": 0.3,
            "chunking_strategy": "monthly",
            "include_threads": True,
            "filter_bots": True,
            "min_message_length": 10,
            "max_chunk_size": 8000,
            "overlap_size": 200
        }

        if config:
            default_config.update(config)

        source = DocumentSource.objects.create(
            tenant=self.tenant,
            name=f"Local Slack Data - {Path(data_dir).name}",
            source_type="local_slack",
            config=default_config,
            is_active=True
        )

        self.stats['sources_created'] += 1
        logger.info(f"Created Slack document source: {source.name}")
        return source

    def create_file_source(self, data_dir: str, config: Optional[Dict[str, Any]] = None) -> DocumentSource:
        """
        Create a file document source.

        Args:
            data_dir: Path to the data directory
            config: Optional configuration overrides

        Returns:
            DocumentSource: Created document source
        """
        default_config = {
            "data_dir": data_dir,
            "file_extensions": [".txt", ".md", ".pdf", ".docx"],
            "recursive": True,
            "max_file_size": 50 * 1024 * 1024,  # 50MB
            "encoding": "utf-8"
        }

        if config:
            default_config.update(config)

        source = DocumentSource.objects.create(
            tenant=self.tenant,
            name=f"File Source - {Path(data_dir).name}",
            source_type="file",
            config=default_config,
            is_active=True
        )

        self.stats['sources_created'] += 1
        logger.info(f"Created file document source: {source.name}")
        return source

    def ingest_source(
        self,
        source: DocumentSource,
        batch_size: int = 50,
        limit: Optional[int] = None
    ) -> tuple[int, int]:
        """
        Ingest data from a document source.

        Args:
            source: Document source to ingest
            batch_size: Number of documents to process in a batch
            limit: Optional limit on number of documents to process

        Returns:
            Tuple of (processed_count, failed_count)
        """
        logger.info(f"Starting ingestion for source: {source.name}")
        self.stats['start_time'] = datetime.now()

        # Create processing job
        job = DocumentProcessingJob.objects.create(
            tenant=self.tenant,
            source=source,
            status="pending",
            created_by=self.user
        )
        self.stats['processing_jobs'] += 1

        try:
            # Process the source
            processed, failed = self.ingestion_service.process_source(
                source=source,
                job=job,
                batch_size=batch_size,
                limit=limit
            )

            self.stats['documents_processed'] += processed
            self.stats['documents_failed'] += failed

            logger.info(f"Ingestion completed: {processed} processed, {failed} failed")
            return processed, failed

        except Exception as e:
            logger.error(f"Error during ingestion: {str(e)}")
            job.status = "failed"
            job.error_message = str(e)
            job.save()
            raise

    def ingest_slack_data(
        self,
        data_dir: str,
        batch_size: int = 50,
        limit: Optional[int] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> tuple[int, int]:
        """
        Ingest Slack data from a directory.

        Args:
            data_dir: Path to the Slack data directory
            batch_size: Number of documents to process in a batch
            limit: Optional limit on number of documents to process
            config: Optional configuration overrides

        Returns:
            Tuple of (processed_count, failed_count)
        """
        # Validate data directory
        if not os.path.exists(data_dir):
            raise ValueError(f"Data directory does not exist: {data_dir}")

        # Create source
        source = self.create_slack_source(data_dir, config)

        # Ingest data
        return self.ingest_source(source, batch_size, limit)

    def ingest_file_data(
        self,
        data_dir: str,
        batch_size: int = 50,
        limit: Optional[int] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> tuple[int, int]:
        """
        Ingest file data from a directory.

        Args:
            data_dir: Path to the file data directory
            batch_size: Number of documents to process in a batch
            limit: Optional limit on number of documents to process
            config: Optional configuration overrides

        Returns:
            Tuple of (processed_count, failed_count)
        """
        # Validate data directory
        if not os.path.exists(data_dir):
            raise ValueError(f"Data directory does not exist: {data_dir}")

        # Create source
        source = self.create_file_source(data_dir, config)

        # Ingest data
        return self.ingest_source(source, batch_size, limit)

    def print_summary(self) -> None:
        """Print ingestion summary."""
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            logger.info(f"Ingestion completed in {duration.total_seconds():.2f} seconds")

        logger.info("=== Ingestion Summary ===")
        logger.info(f"Sources Created: {self.stats['sources_created']}")
        logger.info(f"Processing Jobs: {self.stats['processing_jobs']}")
        logger.info(f"Documents Processed: {self.stats['documents_processed']}")
        logger.info(f"Documents Failed: {self.stats['documents_failed']}")

        if self.stats['documents_processed'] > 0:
            success_rate = (self.stats['documents_processed'] /
                          (self.stats['documents_processed'] + self.stats['documents_failed'])) * 100
            logger.info(f"Success Rate: {success_rate:.2f}%")

        logger.info("=========================")

    def get_ingestion_stats(self) -> Dict[str, Any]:
        """Get detailed ingestion statistics."""
        if self.ingestion_service:
            service_stats = self.ingestion_service.get_processing_stats()
            return {**self.stats, **service_stats}
        return self.stats


def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(description="Ingest data into RAGSearch")
    parser.add_argument("--tenant", default="default", help="Tenant slug (default: default)")
    parser.add_argument("--user-email", default="<EMAIL>", help="User email for processing attribution")
    parser.add_argument("--source-type", choices=["slack", "file", "auto"], default="auto",
                       help="Type of data source (default: auto-detect)")
    parser.add_argument("--data-dir", required=True, help="Path to data directory")
    parser.add_argument("--batch-size", type=int, default=50, help="Batch size for processing (default: 50)")
    parser.add_argument("--limit", type=int, help="Limit number of documents to process")
    parser.add_argument("--config-file", help="Path to JSON configuration file")
    parser.add_argument("--stats-only", action="store_true", help="Show current statistics without ingesting")

    args = parser.parse_args()

    # Validate data directory
    if not os.path.exists(args.data_dir):
        logger.error(f"Data directory does not exist: {args.data_dir}")
        sys.exit(1)

    # Load configuration from file if provided
    config = {}
    if args.config_file:
        if os.path.exists(args.config_file):
            with open(args.config_file, 'r') as f:
                config = json.load(f)
            logger.info(f"Loaded configuration from {args.config_file}")
        else:
            logger.error(f"Configuration file does not exist: {args.config_file}")
            sys.exit(1)

    # Create ingester instance
    try:
        ingester = DataIngester(tenant_slug=args.tenant, user_email=args.user_email)
    except Exception as e:
        logger.error(f"Failed to initialize ingester: {str(e)}")
        sys.exit(1)

    # Show statistics only
    if args.stats_only:
        stats = ingester.get_ingestion_stats()
        logger.info("=== Current Ingestion Statistics ===")
        for key, value in stats.items():
            logger.info(f"{key.replace('_', ' ').title()}: {value}")
        logger.info("====================================")
        return

    # Auto-detect source type if needed
    source_type = args.source_type
    if source_type == "auto":
        data_path = Path(args.data_dir)
        if any(f.name.startswith("channel_") for f in data_path.iterdir() if f.is_dir()):
            source_type = "slack"
            logger.info("Auto-detected source type: slack")
        else:
            source_type = "file"
            logger.info("Auto-detected source type: file")

    # Perform ingestion
    try:
        logger.info(f"Starting {source_type} data ingestion from {args.data_dir}")

        if source_type == "slack":
            processed, failed = ingester.ingest_slack_data(
                data_dir=args.data_dir,
                batch_size=args.batch_size,
                limit=args.limit,
                config=config
            )
        elif source_type == "file":
            processed, failed = ingester.ingest_file_data(
                data_dir=args.data_dir,
                batch_size=args.batch_size,
                limit=args.limit,
                config=config
            )
        else:
            logger.error(f"Unsupported source type: {source_type}")
            sys.exit(1)

        ingester.stats['end_time'] = datetime.now()
        ingester.print_summary()

        if failed > 0:
            logger.warning(f"Ingestion completed with {failed} failures")
            sys.exit(1)
        else:
            logger.info("Ingestion completed successfully!")

    except Exception as e:
        logger.error(f"Error during ingestion: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
