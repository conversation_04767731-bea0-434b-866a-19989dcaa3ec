{"thread_ts": "1735837307.452099", "channel_id": "C065QSSNH8A", "reply_count": 6, "replies": [{"ts": "1735837729.396299", "text": "<@U07M6QKHUC9> how about `<https://stridedemo.stridehr.io/>` ?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1735837307.452099", "blocks": [{"type": "rich_text", "block_id": "FOgIo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " how about "}, {"type": "link", "url": "https://stridedemo.stridehr.io/", "style": {"code": true}}, {"type": "text", "text": " ?"}]}]}]}, {"ts": "1735837764.503769", "text": "I am stopping it until a customer requests for a demo ENV.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1735837307.452099", "blocks": [{"type": "rich_text", "block_id": "vyngz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am stopping it until a customer requests for a demo ENV."}]}]}]}, {"ts": "1735837810.088189", "text": "yes lets do that", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1735837307.452099", "blocks": [{"type": "rich_text", "block_id": "rGxnn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes lets do that"}]}]}]}, {"ts": "1735838154.418189", "text": "Done disabled all except listed above.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1735837307.452099", "blocks": [{"type": "rich_text", "block_id": "uhIGT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Done disabled all except listed above."}]}]}]}, {"ts": "1735838199.565219", "text": "thank you", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1735837307.452099", "blocks": [{"type": "rich_text", "block_id": "oPGX0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "thank you"}]}]}]}, {"ts": "1735838290.825939", "text": "I will look further if there are any other services we need to stop tomorrow after syncing up with <PERSON><PERSON> guy.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1735837307.452099", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "hZWDI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will look further if there are any other services we need to stop tomorrow after syncing up with <PERSON><PERSON> guy."}]}]}]}], "created_at": "2025-05-22T22:00:49.337374"}