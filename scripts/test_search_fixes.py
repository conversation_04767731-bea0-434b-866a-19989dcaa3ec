#!/usr/bin/env python3
"""
Test Search Fixes - Validate all improvements to search thresholds, query classification, and vector search
"""

import os
import sys
import django
from datetime import datetime

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def test_query_classification_fixes():
    """Test that query classification improvements work correctly."""
    
    print("🔍 Testing Query Classification Fixes")
    print("=" * 60)
    
    from apps.core.utils.query_classifier import classify_query
    
    # Test cases that were previously misclassified
    test_cases = [
        {
            "query": "What did Amanda say about the proration date picker bug?",
            "expected_type": "factual",  # Was classified as "code"
            "reason": "Should be factual since it asks what someone said"
        },
        {
            "query": "Who is assigned to the UI/UX changes ticket COM-4036?",
            "expected_type": "factual",
            "reason": "Should be factual since it asks who is assigned"
        },
        {
            "query": "What is Kapil's role in the company?",
            "expected_type": "factual",
            "reason": "Should be factual since it asks about someone's role"
        },
        {
            "query": "Who reported the proration bug and who is fixing it?",
            "expected_type": "factual",  # Was classified as "code"
            "reason": "Should be factual since it asks who reported/fixing"
        },
        {
            "query": "What was the issue with Position In Band for Alayacare?",
            "expected_type": "factual",
            "reason": "Should be factual since it asks what the issue was"
        },
        {
            "query": "Something about compensation builder crashing",
            "expected_type": "list_issues",  # Was classified as "procedural"
            "reason": "Should be list_issues since it mentions crashing/issues"
        }
    ]
    
    results = {}
    correct_classifications = 0
    
    for test_case in test_cases:
        query = test_case["query"]
        expected = test_case["expected_type"]
        reason = test_case["reason"]
        
        classification = classify_query(query)
        actual = classification["type"]
        confidence = classification["confidence"]
        
        is_correct = actual == expected
        if is_correct:
            correct_classifications += 1
        
        status = "✅" if is_correct else "❌"
        print(f"{status} '{query[:50]}...'")
        print(f"   Expected: {expected} | Actual: {actual} | Confidence: {confidence:.2f}")
        print(f"   Reason: {reason}")
        
        if not is_correct:
            print(f"   ⚠️  Classification needs improvement")
        print()
        
        results[query] = {
            "expected": expected,
            "actual": actual,
            "confidence": confidence,
            "correct": is_correct
        }
    
    accuracy = (correct_classifications / len(test_cases)) * 100
    print(f"📊 Classification Accuracy: {correct_classifications}/{len(test_cases)} ({accuracy:.0f}%)")
    
    return results, accuracy

def test_search_threshold_fixes():
    """Test that search threshold improvements work correctly."""
    
    print("\n🎯 Testing Search Threshold Fixes")
    print("=" * 60)
    
    from apps.search.services.rag_service import RAGService
    from django.contrib.auth.models import User
    
    # Test queries that previously returned 0 citations
    test_queries = [
        "What did Amanda say about the proration date picker bug?",
        "Who is assigned to the UI/UX changes ticket COM-4036?",
        "What was the issue with Position In Band for Alayacare?",
        "Any discussion about email spam issues?",
        "How should we handle unverified contacts in email sequences?",
    ]
    
    user = User.objects.first()
    rag_service = RAGService(user=user, tenant_slug='stride')
    
    results = {}
    queries_with_citations = 0
    
    for query in test_queries:
        print(f"🔍 Testing: '{query[:50]}...'")
        
        try:
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=20,
                min_relevance_score=0.15,  # Using new default threshold
                use_hybrid_search=True,
                use_context_aware=True
            )
            
            citations_count = search_result.citations.count()
            response_length = len(search_result.generated_answer)
            confidence = search_result.llm_confidence_score
            
            has_citations = citations_count > 0
            if has_citations:
                queries_with_citations += 1
            
            status = "✅" if has_citations else "❌"
            print(f"   {status} Citations: {citations_count} | Response: {response_length} chars | Confidence: {confidence:.2f}")
            
            if has_citations:
                # Show top citation score
                top_citation = search_result.citations.first()
                if top_citation:
                    print(f"   📚 Top citation score: {top_citation.relevance_score:.3f}")
            else:
                print(f"   ⚠️  No citations found - threshold may still be too high")
            
            results[query] = {
                "citations": citations_count,
                "response_length": response_length,
                "confidence": confidence,
                "has_citations": has_citations
            }
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results[query] = {"error": str(e)}
        
        print()
    
    citation_success_rate = (queries_with_citations / len(test_queries)) * 100
    print(f"📊 Citation Success Rate: {queries_with_citations}/{len(test_queries)} ({citation_success_rate:.0f}%)")
    
    return results, citation_success_rate

def test_cross_source_search():
    """Test that search works across different sources (Slack, Google Docs, etc.)."""
    
    print("\n🌐 Testing Cross-Source Search Capability")
    print("=" * 60)
    
    from apps.documents.models import DocumentSource
    from apps.search.services.rag_service import RAGService
    from django.contrib.auth.models import User
    
    # Check available sources
    sources = DocumentSource.objects.all()
    print(f"📊 Available Sources: {sources.count()}")
    for source in sources:
        doc_count = source.documents.count()
        print(f"   - {source.name} ({source.source_type}): {doc_count} documents")
    
    if sources.count() == 0:
        print("⚠️  No sources found - cannot test cross-source search")
        return {}, 0
    
    # Test queries that should work across sources
    cross_source_queries = [
        "Find any mentions of Curana across all sources",
        "Show me discussions about compensation from any source",
        "What information do we have about Amanda across all documents?",
    ]
    
    user = User.objects.first()
    rag_service = RAGService(user=user, tenant_slug='stride')
    
    results = {}
    successful_searches = 0
    
    for query in cross_source_queries:
        print(f"🔍 Testing: '{query}'")
        
        try:
            # Search without source filter to test cross-source capability
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=20,
                metadata_filter=None,  # No source filter
                min_relevance_score=0.10,  # Low threshold for comprehensive search
                use_hybrid_search=True,
                use_context_aware=True
            )
            
            citations_count = search_result.citations.count()
            
            # Check if citations come from multiple sources
            citation_sources = set()
            for citation in search_result.citations.all():
                source_name = citation.document_chunk.document.source.name
                citation_sources.add(source_name)
            
            has_results = citations_count > 0
            is_cross_source = len(citation_sources) > 1
            
            if has_results:
                successful_searches += 1
            
            status = "✅" if has_results else "❌"
            cross_status = "🌐" if is_cross_source else "📄"
            
            print(f"   {status} Citations: {citations_count}")
            print(f"   {cross_status} Sources: {len(citation_sources)} ({', '.join(citation_sources)})")
            
            results[query] = {
                "citations": citations_count,
                "sources": list(citation_sources),
                "is_cross_source": is_cross_source,
                "has_results": has_results
            }
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results[query] = {"error": str(e)}
        
        print()
    
    success_rate = (successful_searches / len(cross_source_queries)) * 100
    print(f"📊 Cross-Source Search Success Rate: {successful_searches}/{len(cross_source_queries)} ({success_rate:.0f}%)")
    
    return results, success_rate

def main():
    """Run all search fix tests."""
    
    print("🚀 Search Fixes Validation Suite")
    print("=" * 80)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all tests
    classification_results, classification_accuracy = test_query_classification_fixes()
    threshold_results, citation_success_rate = test_search_threshold_fixes()
    cross_source_results, cross_source_success_rate = test_cross_source_search()
    
    # Generate comprehensive report
    print("\n📊 COMPREHENSIVE VALIDATION RESULTS")
    print("=" * 80)
    
    print(f"🔍 Query Classification Accuracy: {classification_accuracy:.0f}%")
    print(f"🎯 Citation Success Rate: {citation_success_rate:.0f}%")
    print(f"🌐 Cross-Source Search Success: {cross_source_success_rate:.0f}%")
    
    # Overall assessment
    overall_score = (classification_accuracy + citation_success_rate + cross_source_success_rate) / 3
    print(f"\n🏆 Overall Search Quality Score: {overall_score:.0f}%")
    
    if overall_score >= 80:
        print("\n🎉 EXCELLENT: Search fixes are working well!")
        print("✅ Ready for production use")
        print("✅ Significant improvements achieved")
    elif overall_score >= 60:
        print("\n✅ GOOD: Search fixes show improvement")
        print("💡 Some areas may need further optimization")
    else:
        print("\n⚠️  NEEDS MORE WORK: Search fixes need additional improvements")
        print("💡 Review thresholds and classification patterns")
    
    # Specific recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if classification_accuracy < 80:
        print("1. 🔧 Improve query classification patterns and keywords")
    if citation_success_rate < 80:
        print("2. 🎯 Further lower search thresholds or improve vector search")
    if cross_source_success_rate < 80:
        print("3. 🌐 Verify cross-source indexing and metadata handling")
    
    print("\n🌐 Next Step: Test these improvements in the browser UI")
    print("   Open http://127.0.0.1:8000/search/ and test the problematic queries")
    
    return {
        "classification": classification_results,
        "thresholds": threshold_results,
        "cross_source": cross_source_results,
        "overall_score": overall_score
    }

if __name__ == "__main__":
    main()
