{"date": "2023-11-16", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1700154380.449959", "text": "they have already done the control panel", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gxcAQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "they have already done the control panel"}]}]}]}, {"ts": "1700154371.268189", "text": "This is how pave is configuring the total rewards and offer lettters\n<https://support.pave.com/hc/en-us/articles/5829282609687-Configuring-Benefits-that-are-displayed-in-Total-Rewards-Visual-Offer-Letter>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Vklhp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is how pave is configuring the total rewards and offer lettters\n"}, {"type": "link", "url": "https://support.pave.com/hc/en-us/articles/5829282609687-Configuring-Benefits-that-are-displayed-in-Total-Rewards-Visual-Offer-Letter"}]}]}]}, {"ts": "1700098090.186989", "text": "<@U065H3M6WJV>, <PERSON><PERSON><PERSON>, and I get together with <PERSON> from Tuesday to Friday in the evening, between 4 pm and 6 pm (the meeting usually lasts for about an hour, but it can be a bit longer). We are primarily discussing new features / answers for some core comp specific questions. If you would like, I can add you in this meeting right away. It's completely optional though. Please let me know.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1700098090.186989", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "OXQP8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": ", <PERSON><PERSON><PERSON>, and I get together with <PERSON> from Tuesday to Friday in the evening, between 4 pm and 6 pm (the meeting usually lasts for about an hour, but it can be a bit longer). We are primarily discussing new features / answers for some core comp specific questions. If you would like, I can add you in this meeting right away. It's completely optional though. Please let me know."}]}]}]}, {"ts": "**********.336059", "text": "Good afternoon -- I spent a little time reviewing the Merge API docs and comparing their available fields to the data structure in Compiify's upload formats: <https://docs.google.com/document/d/1zV3XvRMvcCE7cFHkKYaZWkG_LZQVuaoc7lDFbnyCuzE/edit|Data mapping table>\n\nI think the most concerning gaps would be the inability to identify categories that map to compensation ranges, for example:\n• Job Category\n• Job Level \n• Job Family\n• Job Family Group\n• Compensation Grade\nSo, we would need to know if some of these are supported as pass-through or custom data fields from the set of HRIS providers we need to support.\n\nThis is in addition to the question of whether they could provide new integrations to capture equity data at an employee level.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.336059", "reply_count": 6, "files": [{"id": "F065HJUC895", "created": **********, "timestamp": **********, "name": "Data mapping - Merge API", "title": "Data mapping - Merge API", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1zV3XvRMvcCE7cFHkKYaZWkG_LZQVuaoc7lDFbnyCuzE", "external_url": "https://docs.google.com/document/d/1zV3XvRMvcCE7cFHkKYaZWkG_LZQVuaoc7lDFbnyCuzE/edit", "url_private": "https://docs.google.com/document/d/1zV3XvRMvcCE7cFHkKYaZWkG_LZQVuaoc7lDFbnyCuzE/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F065HJUC895-032e00db62/data_mapping_-_merge_api_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSOc9cUfjSMMnrSbRng0AOx70YPrQR/tU05B6k0APopmT70ZPoaAFbqKCOaU9aPzoATFBODS01utAC7vajd7UmDRg0AOPWkobrQetAC0hxnnNLSHr0zQAYHvRge9H4UfhQB//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F065HJUC895/data_mapping_-_merge_api", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "8bWJv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Good afternoon -- I spent a little time reviewing the Merge API docs and comparing their available fields to the data structure in Compiify's upload formats: "}, {"type": "link", "url": "https://docs.google.com/document/d/1zV3XvRMvcCE7cFHkKYaZWkG_LZQVuaoc7lDFbnyCuzE/edit", "text": "Data mapping table"}, {"type": "text", "text": "\n\nI think the most concerning gaps would be the inability to identify categories that map to compensation ranges, for example:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Category"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Level "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Family"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Job Family Group"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Compensation Grade"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nSo, we would need to know if some of these are supported as pass-through or custom data fields from the set of HRIS providers we need to support.\n\nThis is in addition to the question of whether they could provide new integrations to capture equity data at an employee level."}]}]}]}], "created_at": "2025-05-22T21:35:34.577368"}