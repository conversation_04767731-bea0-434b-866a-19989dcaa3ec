{"date": "2024-12-11", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1733863332.234359", "text": "I'll ask <PERSON><PERSON><PERSON>; I want to be respectful of their capacity.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XzPlN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'll ask <PERSON><PERSON><PERSON>; I want to be respectful of their capacity."}]}]}]}, {"ts": "1733861973.350069", "text": "also can you pls invite <PERSON> to the next call with <PERSON><PERSON>?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lynq4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "also can you pls invite <PERSON> to the next call with <PERSON><PERSON>?"}]}]}]}, {"ts": "1733861819.861239", "text": "hey <@U07EJ2LP44S> When can we set up a paybands discovery session with <PERSON><PERSON><PERSON>? Would they be open to meeting this week?\n\nAlso can we do the same for C<PERSON><PERSON>? or make it an agenda items for the next weekly call with them?", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1733861951.000000"}, "blocks": [{"type": "rich_text", "block_id": "xbCQ/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "hey "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " When can we set up a paybands discovery session with <PERSON><PERSON><PERSON>? Would they be open to meeting this week?\n\nAlso can we do the same for <PERSON><PERSON><PERSON>? or make it an agenda items for the next weekly call with them?"}]}]}]}, {"ts": "1733860443.290419", "text": "<@U0690EB5JE5> When we are in test mode, are we triggering an email without realizing it? As in, if <PERSON> tests the approval chain, could these people who have not yet been introduced to Stride be getting an email? What are the trigger points for emails right now in the system?\n\nShe's concerned that if she goes in an submit's on behalf of a manager, it'll send an email to THEIR manager telling them they have something to approve. I haven't had this question before and it didn't even occur to me to ask.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733860443.290419", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "cy+gU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " When we are in test mode, are we triggering an email without realizing it? As in, if <PERSON> tests the approval chain, could these people who have not yet been introduced to Stride be getting an email? What are the trigger points for emails right now in the system?\n\nShe's concerned that if she goes in an submit's on behalf of a manager, it'll send an email to THEIR manager telling them they have something to approve. I haven't had this question before and it didn't even occur to me to ask."}]}]}]}, {"ts": "1733855891.499739", "text": "Curana market adjustment/compa ratio request here: <https://compiify.atlassian.net/browse/COM-4027>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14110::afe1de6d3d1344bd853555e4329cf402", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4027?atlOrigin=eyJpIjoiNWRjNjA2MWRiMzgxNDZlZGEwZjA2ZGJjZWIyODQxNmIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4027 Feature Request: Curana's Market Adjustment workflow>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14110::053c9432e34e4f7d9d8904cec04273a5", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14110\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4027\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4027", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "YrN5W", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Curana market adjustment/compa ratio request here: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4027"}]}]}]}], "created_at": "2025-05-22T21:35:34.679270"}