{"date": "2024-11-02", "channel_id": "C065QSSNH8A", "message_count": 11, "messages": [{"ts": "1730564005.668499", "text": "<https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "2HwXU", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1730563227.472019", "text": "For SDF,\nbroken merit page is fixed\nBudget discrepency is fixed.\nFor decimals, It is cosmetic. the app should show 45677.00 if it is a whole number.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730563227.472019", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "RkVjt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For SDF,\nbroken merit page is fixed\nBudget discrepency is fixed.\nFor decimals, It is cosmetic. the app should show 45677.00 if it is a whole number."}]}]}]}, {"ts": "1730562828.313779", "text": "start date and the end date is not fixed. I changed the dates in the comp builder and published the cycle but the app is not reflecting the new dates in the first page and in the \"select ket dates\" section", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730562828.313779", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "HhGKy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "start date and the end date is not fixed. I changed the dates in the comp builder and published the cycle but the app is not reflecting the new dates in the first page and in the \"select ket dates\" section"}]}]}]}, {"ts": "1730562733.957249", "text": "<@U0690EB5JE5> for the SDF, in the allocation page, for the root emp and <PERSON>, we are showing only 1 decimal digit for their budget. Can we please fix it so through out the product, it as 2 decimal places?\n\nSDF will notice these inconsistency", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730562733.957249", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "/8x9s", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " for the SDF, in the allocation page, for the root emp and <PERSON>, we are showing only 1 decimal digit for their budget. Can we please fix it so through out the product, it as 2 decimal places?\n\nSDF will notice these inconsistency"}]}]}]}, {"ts": "1730551467.113379", "text": "<!here> Following fixes are deployed in test ENVs\n• *Alayacare* - in <http://qa.stridehr.io|qa.stridehr.io> - database is also upto date with prod\n    ◦ *<https://compiify.atlassian.net/browse/COM-3951?atlOrigin=eyJpIjoiM2VhM2UwOWRiMTkxNGVhMWFhZmIyNTRjODQ3YTlmMDUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3951 Missing Next Position in Band Allocation during Promotion Upload>* - Please retry promotion upload and let me know. I couldn't reproduce locally and have put a potential fix\n    ◦ *<https://compiify.atlassian.net/browse/COM-3940?atlOrigin=eyJpIjoiYTEzZjdiNDMwMGMxNDgzNmE4YWVhZjFiYTI5NGEzY2EiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3940 Inconsistent Flagging Issue for Proration Factor>* - This is fixed. need to test bit more for bonus. I am not clear how this should work for bonus. Right now percent is calculated based on prorated salary\n• *SDF:*  Deployed to SDF-test\n    ◦ *Broken merit page issue*\n    ◦ *Budget discrepancy* - This root employee is matching as we now store upto 2 decimal places. if we still see differences we need to increase that. <@U07M6QKHUC9> Please verify and let me know, i will look into this further tomorrow\n    ◦ *Incorrect dates on cycle page -* I have put the fix. But need someone to test from US timezone and confirm. if issue still persists I will take further look into this tomorrow", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730551467.113379", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "Cddi2", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Following fixes are deployed in test ENVs\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Alayacare", "style": {"bold": true}}, {"type": "text", "text": " - in "}, {"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}, {"type": "text", "text": " - database is also upto date with prod"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3951?atlOrigin=eyJpIjoiM2VhM2UwOWRiMTkxNGVhMWFhZmIyNTRjODQ3YTlmMDUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ", "text": "COM-3951 Missing Next Position in Band Allocation during Promotion Upload", "style": {"bold": true}}, {"type": "text", "text": " - Please retry promotion upload and let me know. I couldn't reproduce locally and have put a potential fix"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3940?atlOrigin=eyJpIjoiYTEzZjdiNDMwMGMxNDgzNmE4YWVhZjFiYTI5NGEzY2EiLCJwIjoiamlyYS1zbGFjay1pbnQifQ", "text": "COM-3940 Inconsistent Flagging Issue for Proration Factor", "style": {"bold": true}}, {"type": "text", "text": " - This is fixed. need to test bit more for bonus. I am not clear how this should work for bonus. Right now percent is calculated based on prorated salary"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF:  ", "style": {"bold": true}}, {"type": "text", "text": "Deployed to SDF-test"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Broken merit page issue", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Budget discrepancy ", "style": {"bold": true}}, {"type": "text", "text": "- This root employee is matching as we now store upto 2 decimal places. if we still see differences we need to increase that. "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Please verify and let me know, i will look into this further tomorrow"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Incorrect dates on cycle page - ", "style": {"bold": true}}, {"type": "text", "text": "I have put the fix. But need someone to test from US timezone and confirm. if issue still persists I will take further look into this tomorrow"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}, {"ts": "1730516265.457159", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> I am unable to reproduce this issue. I created a file for particular employee and uploaded promotion again. I see the new position in band. Could you please share the file the <PERSON> uploaded?\nin my local with file\n<https://compiify.atlassian.net/browse/COM-3951>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730483051.144899", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1730516344.000000"}, "files": [{"id": "F07UN6Y5544", "created": 1730516233, "timestamp": 1730516233, "name": "Screenshot 2024-11-02 at 8.23.54 AM.png", "title": "Screenshot 2024-11-02 at 8.23.54 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 207658, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07UN6Y5544/screenshot_2024-11-02_at_8.23.54___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07UN6Y5544/download/screenshot_2024-11-02_at_8.23.54___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UN6Y5544-0757bf68af/screenshot_2024-11-02_at_8.23.54___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UN6Y5544-0757bf68af/screenshot_2024-11-02_at_8.23.54___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UN6Y5544-0757bf68af/screenshot_2024-11-02_at_8.23.54___am_360.png", "thumb_360_w": 360, "thumb_360_h": 161, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UN6Y5544-0757bf68af/screenshot_2024-11-02_at_8.23.54___am_480.png", "thumb_480_w": 480, "thumb_480_h": 215, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UN6Y5544-0757bf68af/screenshot_2024-11-02_at_8.23.54___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UN6Y5544-0757bf68af/screenshot_2024-11-02_at_8.23.54___am_720.png", "thumb_720_w": 720, "thumb_720_h": 323, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UN6Y5544-0757bf68af/screenshot_2024-11-02_at_8.23.54___am_800.png", "thumb_800_w": 800, "thumb_800_h": 359, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UN6Y5544-0757bf68af/screenshot_2024-11-02_at_8.23.54___am_960.png", "thumb_960_w": 960, "thumb_960_h": 431, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UN6Y5544-0757bf68af/screenshot_2024-11-02_at_8.23.54___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 459, "original_w": 1708, "original_h": 766, "thumb_tiny": "AwAVADDS5oo7UuKACikIB6ikIxjAoAdTd3zdD+VAJPWloARuBTgeKa3SlHSgAFI/3aBSSfdpoAQ8U+mJ0pw70MD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07UN6Y5544/screenshot_2024-11-02_at_8.23.54___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07UN6Y5544-2cc69a5ebd", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F07U55J6M6K", "created": 1730516246, "timestamp": 1730516246, "name": "Updated - Promotion.csv", "title": "Updated - Promotion.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": true, "size": 91, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07U55J6M6K/updated_-_promotion.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07U55J6M6K/download/updated_-_promotion.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07U55J6M6K/updated_-_promotion.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07U55J6M6K-212c042a5f", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F07U55J6M6K/updated_-_promotion.csv/edit", "preview": "Employee Id,Is Eligible for Promotion,Job Title,Band Id\r\n705,Yes,Senior Product Manager,343", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Employee Id</div><div class=\"cm-col\">Is Eligible for Promotion</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Band Id</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">705</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">Senior Product Manager</div><div class=\"cm-col cm-num\">343</div></div></div>\n</div>\n", "lines": 2, "lines_more": 0, "preview_is_truncated": false, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14034::58588610ceb146e7b000abc7b8b7b476", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3951?atlOrigin=eyJpIjoiM2VhM2UwOWRiMTkxNGVhMWFhZmIyNTRjODQ3YTlmMDUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3951 Missing Next Position in Band Allocation during Promotion Upload>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14034::ea860a0fac1741e0931c969ab4f6f3b6", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14034\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3951\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3951", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "SfqAT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am unable to reproduce this issue. I created a file for particular employee and uploaded promotion again. I see the new position in band. Could you please share the file the <PERSON> uploaded?\nin my local with file\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3951"}]}]}]}, {"ts": "1730511378.060089", "text": "<@U0690EB5JE5> <https://compiify.atlassian.net/browse/COM-3940?atlOrigin=eyJpIjoiMjczZDMzMmU3YmQ1NDVlODk4ODM1Y2E4YmFkMWViNmQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|https://compiify.atlassian.net/browse/COM-3940?atlOrigin=eyJpIjoiMjczZDMzMmU3YmQ1NDVlODk4ODM1Y2E4YmFkMWViNmQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ> is the only remaining pressing issue for alayacare at this moment", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1730511378.060089", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14023::4443a8d4655644c5b1abff0ca93132aa", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3940?atlOrigin=eyJpIjoiYTEzZjdiNDMwMGMxNDgzNmE4YWVhZjFiYTI5NGEzY2EiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3940 Inconsistent Flagging Issue for Proration Factor>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14023::342cac3339594554adcc6306727c9201", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/712020:7d833b3a-e9d6-423f-93b8-d525378819e9/af9dff77-7442-4e64-9882-eb63aaa8f5a1/48?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14023\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3940\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3940?atlOrigin=eyJpIjoiMjczZDMzMmU3YmQ1NDVlODk4ODM1Y2E4YmFkMWViNmQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "inNia", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3940?atlOrigin=eyJpIjoiMjczZDMzMmU3YmQ1NDVlODk4ODM1Y2E4YmFkMWViNmQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ", "text": "https://compiify.atlassian.net/browse/COM-3940?atlOrigin=eyJpIjoiMjczZDMzMmU3YmQ1NDVlODk4ODM1Y2E4YmFkMWViNmQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ"}, {"type": "text", "text": " is the only remaining pressing issue for alayacare at this moment"}]}]}]}, {"ts": "1730501912.888239", "text": "Nothing is more exciting than starting the weekend with positive words from Customer. Great job <@U04DKEFP1K8> <@U0690EB5JE5> <@U06HN8XDC5A> <@U071FN2589Y> on working tirelessly over the past couple of weeks to fix their bugs and answers their queries promptly. And to <@U07EJ2LP44S> for handling them and putting them at ease during this time. :rocket::fire::tada:", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XBvSJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Nothing is more exciting than starting the weekend with positive words from Customer. Great job "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U071FN2589Y"}, {"type": "text", "text": " on working tirelessly over the past couple of weeks to fix their bugs and answers their queries promptly. And to "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " for handling them and putting them at ease during this time. "}, {"type": "emoji", "name": "rocket", "unicode": "1f680"}, {"type": "emoji", "name": "fire", "unicode": "1f525"}, {"type": "emoji", "name": "tada", "unicode": "1f389"}]}]}]}, {"ts": "1730501665.054289", "text": "<@U04DKEFP1K8> do we have a test environment for AlayaCare so we can reproduce bugs they will see during the cycle? I am assuming we won't be able to play around within their prod env anymore if any issue comes up. or do we just want to use the QA env for that?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730501665.054289", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "irZqQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " do we have a test environment for AlayaCare so we can reproduce bugs they will see during the cycle? I am assuming we won't be able to play around within their prod env anymore if any issue comes up. or do we just want to use the QA env for that?"}]}]}]}, {"ts": "1730490404.512319", "text": "Also I put time on your cal on Monday to meet with a new vendor we are using to scale our outbound campaigns? They need someone on the call with a access to DNS settings?", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nrPfW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also I put time on your cal on Monday to meet with a new vendor we are using to scale our outbound campaigns? They need someone on the call with a access to DNS settings?"}]}]}]}, {"ts": "1730490347.869209", "text": "<@U04DKEFP1K8> Is <PERSON><PERSON><PERSON> going to be handling the  DNS hosting settings for email deliverabilty going forward or is it something I can manage?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730490347.869209", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "JyDjR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Is <PERSON><PERSON><PERSON> going to be handling the  DNS hosting settings for email deliverabilty going forward or is it something I can manage?"}]}]}]}], "created_at": "2025-05-22T21:35:34.675190"}