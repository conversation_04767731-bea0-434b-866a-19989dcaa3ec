#!/usr/bin/env python
"""
Production Slack Data Ingestion Script

This script ingests all Slack data from the data/ folder using the production-ready
UnifiedLlamaIndexIngestionService with proper DocumentSource setup.

Features:
- Uses production DocumentSource pattern
- Processes all available Slack data
- Comprehensive error handling and statistics
- Production-ready logging and monitoring
- Proper tenant isolation

Usage:
    cd multi_source_rag
    python ../scripts/production_slack_ingestion.py
"""

import os
import sys
import django
from datetime import datetime
from typing import Dict, Any, List

# Set up Django
sys.path.append('.')
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from django.db import transaction
from apps.documents.services.llama_ingestion_service_unified import UnifiedLlamaIndexIngestionService
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk
from apps.accounts.models import Tenant
from apps.documents.interfaces.local_slack import LocalSlackSourceInterface

def print_banner():
    """Print startup banner."""
    print("🚀 PRODUCTION SLACK DATA INGESTION")
    print("=" * 50)
    print("📊 Processing all Slack data from data/ folder")
    print("🔧 Using UnifiedLlamaIndexIngestionService")
    print("🏢 Tenant: stride")
    print("=" * 50)

def get_or_create_tenant() -> Tenant:
    """Get or create the stride tenant."""
    tenant, created = Tenant.objects.get_or_create(
        slug="stride",
        defaults={"name": "Stride Technologies"}
    )
    if created:
        print("✅ Created stride tenant")
    else:
        print("✅ Using existing stride tenant")
    return tenant

def create_document_source(tenant: Tenant) -> DocumentSource:
    """Create or get the document source for local Slack data."""
    source, created = DocumentSource.objects.get_or_create(
        tenant=tenant,
        name="Local Slack - C065QSSNH8A",
        source_type="local_slack",
        defaults={
            "config": {
                "data_dir": "../data/",
                "channel": "C065QSSNH8A",
                "time_period": "monthly",
                "max_tokens": 500,
                "overlap_tokens": 50,
                "enable_summary": False,
                "quality_threshold": 0.3
            }
        }
    )
    
    if created:
        print(f"✅ Created document source: {source.name}")
    else:
        print(f"📋 Using existing document source: {source.name}")
    
    return source

def get_data_info(slack_interface: LocalSlackSourceInterface) -> Dict[str, Any]:
    """Get information about available data."""
    try:
        info = slack_interface.get_staged_data_info()
        print(f"📊 Data Summary:")
        print(f"   Total messages: {info.total_messages}")
        print(f"   Available files: {len(info.available_dates)}")
        print(f"   Date range: {info.date_range}")
        return info
    except Exception as e:
        print(f"⚠️  Could not get data info: {e}")
        return {}

def fetch_all_documents(slack_interface: LocalSlackSourceInterface) -> List[Dict[str, Any]]:
    """Fetch all available documents from Slack data."""
    print("📄 Fetching all documents from Slack data...")
    
    try:
        # Fetch all documents (no limit for production)
        documents = slack_interface.fetch_documents(
            days_back=730,  # 2 years of data
            include_threads=True,
            filter_bots=True,
            time_period="monthly"
        )
        
        print(f"✅ Fetched {len(documents)} documents")
        
        if documents:
            # Show sample of documents
            print("📋 Sample documents:")
            for i, doc in enumerate(documents[:3]):
                title = doc.get("title", "Untitled")
                content_length = len(doc.get("content", ""))
                metadata = doc.get("metadata", {})
                message_count = metadata.get("message_count", 0)
                print(f"   {i+1}. {title} ({content_length} chars, {message_count} messages)")
            
            if len(documents) > 3:
                print(f"   ... and {len(documents) - 3} more documents")
        
        return documents
        
    except Exception as e:
        print(f"❌ Error fetching documents: {e}")
        return []

def ingest_documents_production(
    ingestion_service: UnifiedLlamaIndexIngestionService,
    source: DocumentSource,
    documents: List[Dict[str, Any]]
) -> Dict[str, int]:
    """Ingest documents using production patterns."""
    print(f"🚀 Starting production ingestion of {len(documents)} documents...")
    
    stats = {
        "processed": 0,
        "failed": 0,
        "chunks_created": 0,
        "start_time": datetime.now()
    }
    
    # Process documents in batches for better memory management
    batch_size = 25
    total_batches = (len(documents) + batch_size - 1) // batch_size
    
    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min(start_idx + batch_size, len(documents))
        batch_docs = documents[start_idx:end_idx]
        
        print(f"📦 Processing batch {batch_num + 1}/{total_batches} ({len(batch_docs)} documents)")
        
        for i, document in enumerate(batch_docs):
            try:
                with transaction.atomic():
                    # Process single document using the production service
                    raw_doc = ingestion_service._process_single_document(source, document)
                    
                    if raw_doc:
                        stats["processed"] += 1
                        
                        # Count chunks created
                        chunk_count = DocumentChunk.objects.filter(document=raw_doc).count()
                        stats["chunks_created"] += chunk_count
                        
                        if (stats["processed"]) % 10 == 0:
                            print(f"   ✅ Processed {stats['processed']} documents...")
                    else:
                        print(f"   ⚠️  No result for document: {document.get('id', 'unknown')}")
                        
            except Exception as e:
                stats["failed"] += 1
                doc_id = document.get("id", "unknown")
                print(f"   ❌ Failed to process document {doc_id}: {e}")
                continue
        
        print(f"✅ Completed batch {batch_num + 1}/{total_batches}")
    
    stats["end_time"] = datetime.now()
    return stats

def print_final_statistics(stats: Dict[str, Any], tenant: Tenant):
    """Print comprehensive final statistics."""
    duration = stats["end_time"] - stats["start_time"]
    
    print("\n" + "=" * 60)
    print("📊 PRODUCTION INGESTION STATISTICS")
    print("=" * 60)
    print(f"⏱️  Total Duration: {duration}")
    print(f"📄 Documents Processed: {stats['processed']}")
    print(f"❌ Documents Failed: {stats['failed']}")
    print(f"🧩 Chunks Created: {stats['chunks_created']}")
    
    if stats["processed"] > 0:
        avg_time = duration.total_seconds() / stats["processed"]
        print(f"⚡ Average Time per Document: {avg_time:.2f}s")
        
        # Get database counts
        total_docs = RawDocument.objects.filter(tenant=tenant).count()
        total_chunks = DocumentChunk.objects.filter(tenant=tenant).count()
        
        print(f"🗄️  Total Documents in DB: {total_docs}")
        print(f"🗄️  Total Chunks in DB: {total_chunks}")
    
    success_rate = (stats["processed"] / (stats["processed"] + stats["failed"]) * 100) if (stats["processed"] + stats["failed"]) > 0 else 0
    print(f"✅ Success Rate: {success_rate:.1f}%")
    print("=" * 60)

def main():
    """Main ingestion function."""
    print_banner()
    
    try:
        # 1. Setup tenant
        tenant = get_or_create_tenant()
        
        # 2. Create document source
        source = create_document_source(tenant)
        
        # 3. Initialize LocalSlackSourceInterface
        print("📡 Initializing LocalSlackSourceInterface...")
        slack_interface = LocalSlackSourceInterface({
            "data_dir": "../data/",
            "channel": "C065QSSNH8A",
            "time_period": "monthly",
            "max_tokens": 500,
            "overlap_tokens": 50,
            "enable_summary": False,
            "quality_threshold": 0.3
        })
        print("✅ LocalSlackSourceInterface initialized")
        
        # 4. Get data information
        data_info = get_data_info(slack_interface)
        
        # 5. Fetch all documents
        documents = fetch_all_documents(slack_interface)
        
        if not documents:
            print("⚠️  No documents to process. Check data directory.")
            return 1
        
        # 6. Initialize ingestion service
        print("🔧 Initializing UnifiedLlamaIndexIngestionService...")
        ingestion_service = UnifiedLlamaIndexIngestionService(tenant)
        print("✅ Ingestion service initialized")
        
        # 7. Perform production ingestion
        stats = ingest_documents_production(ingestion_service, source, documents)
        
        # 8. Print final statistics
        print_final_statistics(stats, tenant)
        
        if stats["processed"] > 0:
            print("\n🎉 PRODUCTION SLACK INGESTION COMPLETED SUCCESSFULLY!")
            print("🔍 Ready for RAG search testing with real data")
            return 0
        else:
            print("\n❌ NO DOCUMENTS WERE PROCESSED")
            return 1
            
    except Exception as e:
        print(f"\n❌ FATAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
