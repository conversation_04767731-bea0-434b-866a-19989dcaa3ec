{"date": "2025-03-03", "channel_id": "C065QSSNH8A", "message_count": 23, "messages": [{"ts": "1741025680.953409", "text": "ah okay.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "dnTsL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ah okay."}]}]}]}, {"ts": "1741025657.418149", "text": "He is in tithely training that <PERSON> is doing now", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LtbSX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "He is in tithely training that <PERSON> is doing now"}]}]}]}, {"ts": "1741025595.728489", "text": "<@U07M6QKHUC9>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lAMKj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}]}]}]}, {"ts": "1741025591.189269", "text": "sounds great. Did we demo this to him?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RvB0J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "sounds great. Did we demo this to him?"}]}]}]}, {"ts": "1741025260.662519", "text": "Great job <@U0690EB5JE5> in building a product that’s praised by customers. <PERSON> was <PERSON> and <PERSON>’s manager at 15five", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1741025268.000000"}, "blocks": [{"type": "rich_text", "block_id": "23o+f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Great job "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " in building a product that’s praised by customers. <PERSON> was <PERSON> and <PERSON>’s manager "}, {"type": "text", "text": "at"}, {"type": "text", "text": " 15five"}]}]}]}, {"ts": "1741025158.888799", "text": "<@U07EJ2LP44S> brad just texted me “I’m on the Stride Manager kickoff call! So cool!”", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ntz98", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " brad just texted me “I’m on the Stride Manager kickoff call! So cool!”"}]}]}]}, {"ts": "1741022274.442609", "text": ":+1:", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qF+xl", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "+1", "unicode": "1f44d"}]}]}]}, {"ts": "1741022222.570209", "text": "I don't know why it let me do this one and not others, but I was able to update all their hourly employees over to Regular/FT employees", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "D6tRT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I don't know why it let me do this one and not others, but I was able to update all their hourly employees over to Regular/FT employees"}]}]}]}, {"ts": "1741021956.844309", "text": "will do in sometime.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "23TTZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will do in sometime."}]}]}]}, {"ts": "1741021820.496829", "text": "<@U0690EB5JE5> Tithely Payband Additions:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741021820.496829", "reply_count": 12, "files": [{"id": "F08FU9LPQJZ", "created": 1741021819, "timestamp": 1741021819, "name": "TithelyNewBandsMarch3.csv", "title": "TithelyNewBandsMarch3.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1266, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08FU9LPQJZ/tithelynewbandsmarch3.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08FU9LPQJZ/download/tithelynewbandsmarch3.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FU9LPQJZ/tithelynewbandsmarch3.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08FU9LPQJZ-13c4f5d2f9", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08FU9LPQJZ/tithelynewbandsmarch3.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Band Id,Effective Start Date,Effective End Date,Compensation Grade,Location (Country),Location (Region),Team,Department,Job Family,Job Subfamily,Description,Tier,Job Category,Job Level,Job Title,Frequency,Salary Currency,Salary Min Amount,Salary Target Amount,Salary Max Amount,Variable Pay Currency,Variable Pay Target (%),Variable Pay Target Amount,Equity Currency,Equity Target Amount,Equity Target (unit),Equity Target (%),Min Equity (unit),Max Equity (unit),Equity Participation Rate (%),OTE Min Amount,OTE Target Amount,OTE Max Amount,Pay Mix\r\nA,112,01/01/2021,,,US,,,Marketing,,,,,IC,,Content Manager,Annual,USD,\"$89,680.00 \",\"112,100.00\",\"$134,520.00 \",,,,,,,,,,,,,,\r\nA,113,01/01/2021,,,US,,,Product Development,,,,,M,,Director of Product,Annual,USD,\"$158,800.00 \",\"198,500.00\",\"$238,200.00 \",,,,,,,,,,,,,,\r\nA,114,01/01/2021,,,AUS,,,Engineering,,,,,IC,,Senior Software Engineer II,Annual,AUD,\"$111,384.62 \",\"144,800.00\",\"$167,076.92 \",,,,,,,,,,,,,,\r\nA,115,01/01/2021,,,AUS,,,Engineering,,,,,...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Band Id</div><div class=\"cm-col\">Effective Start Date</div><div class=\"cm-col\">Effective End Date</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Location (Country)</div><div class=\"cm-col\">Location (Region)</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">Job Subfamily</div><div class=\"cm-col\">Description</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Frequency</div><div class=\"cm-col\">Salary Currency</div><div class=\"cm-col\">Salary Min Amount</div><div class=\"cm-col\">Salary Target Amount</div><div class=\"cm-col\">Salary Max Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay Target (%)</div><div class=\"cm-col\">Variable Pay Target Amount</div><div class=\"cm-col\">Equity Currency</div><div class=\"cm-col\">Equity Target Amount</div><div class=\"cm-col\">Equity Target (unit)</div><div class=\"cm-col\">Equity Target (%)</div><div class=\"cm-col\">Min Equity (unit)</div><div class=\"cm-col\">Max Equity (unit)</div><div class=\"cm-col\">Equity Participation Rate (%)</div><div class=\"cm-col\">OTE Min Amount</div><div class=\"cm-col\">OTE Target Amount</div><div class=\"cm-col\">OTE Max Amount</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">112</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Marketing</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col\"></div><div class=\"cm-col\">Content Manager</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">$89,680.00 </div><div class=\"cm-col\">112,100.00</div><div class=\"cm-col\">$134,520.00 </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">113</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Product Development</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col\"></div><div class=\"cm-col\">Director of Product</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">$158,800.00 </div><div class=\"cm-col\">198,500.00</div><div class=\"cm-col\">$238,200.00 </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">114</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">AUS</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col\"></div><div class=\"cm-col\">Senior Software Engineer II</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">AUD</div><div class=\"cm-col\">$111,384.62 </div><div class=\"cm-col\">144,800.00</div><div class=\"cm-col\">$167,076.92 </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">115</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">AUS</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col\"></div><div class=\"cm-col\">Software Engineer III</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">AUD</div><div class=\"cm-col\">$86,538.46 </div><div class=\"cm-col\">112,500.00</div><div class=\"cm-col\">$129,807.69 </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">A</div><div class=\"cm-col cm-num\">116</div><div class=\"cm-col\">01/01/2021</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">AUS</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col\"></div><div class=\"cm-col\">Sr. Staff Software Engineer (PT)</div><div class=\"cm-col\">Annual</div><div class=\"cm-col\">AUD</div><div class=\"cm-col\">$72,269.23 </div><div class=\"cm-col\">$93,950.00 </div><div class=\"cm-col\">$112,740.00 </div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 6, "lines_more": 1, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Nnrz3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Tithely Payband Additions:"}]}]}]}, {"ts": "1741017206.424199", "text": "ok", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1741017190.945549", "text": "I have a call with curana, diversified, and a training with tithely, which i'm sure will all bring up additional things", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "T/Ffy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have a call with curana, diversified, and a training with tithely, which i'm sure will all bring up additional things"}]}]}]}, {"ts": "1741017158.162279", "text": "Ok, thank you. I am not going to be able to get through today on my own, they are all coming at me", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ylup3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok, thank you. I am not going to be able to get through today on my own, they are all coming at me"}]}]}]}, {"ts": "1741016929.133129", "text": "Ok. I will be available mostly after one or two hours if there is anything urgent.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sHl53", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. I will be available mostly after one or two hours if there is anything urgent."}]}]}]}, {"ts": "**********.365679", "text": "<@U0690EB5JE5> I'm not sure what it is right now, but <PERSON><PERSON><PERSON> just scheduled a call with me in 15 to talk about a bug in the MIP account", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FCzJm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I'm not sure what it is right now, but <PERSON><PERSON><PERSON> just scheduled a call with me in 15 to talk about a bug in the MIP account"}]}]}]}, {"ts": "**********.980389", "text": "Also I have a couple paybands to load for them, and they want to change all their hourly people to annual. I will work on those sheets", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HN2te", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also I have a couple paybands to load for them, and they want to change all their hourly people to annual. I will work on those sheets"}]}]}]}, {"ts": "**********.479669", "text": "I have one final request from <PERSON><PERSON><PERSON> on Total rewards:\n\nFor the summary at the top of the salaries table, they would like to see the first salary, previous salary (coming into this cycle), and current salary (after this cycle). Can we list those three items above or below the Total % Increase?\n\nThey would like to keep the salary history just to the summary of 5 (or however many are showing there). They do not like the full list view.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.479669", "reply_count": 6, "edited": {"user": "U07EJ2LP44S", "ts": "1741016723.000000"}, "blocks": [{"type": "rich_text", "block_id": "ghRJo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have one final request from <PERSON><PERSON><PERSON> on Total rewards:\n\nFor the summary at the top of the salaries table, they would like to see the first salary, previous salary (coming into this cycle), and current salary (after this cycle). Can we list those three items above or below the Total % Increase?\n\nThey would like to keep the salary history just to the summary of 5 (or however many are showing there). They do not like the full list view."}]}]}]}, {"ts": "1741016436.899319", "text": "I am OOO from Wednesday until end of the week, But will make sure you have the same level of support.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1741016444.000000"}, "blocks": [{"type": "rich_text", "block_id": "b0A2t", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am OOO from Wednesday until end of the week, But will make sure you have the same level of support."}]}]}]}, {"ts": "1741016402.109749", "text": "<@U07EJ2LP44S> FYI... I will be in a family wedding from My Tuesday evening and I won't be able to connect from laptop at least Wednesday and Thursday. I will come back on how to work around the support for those two days.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741016402.109749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "NbTqw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " FYI... I will be in a family wedding from My Tuesday evening and I won't be able to connect from laptop at least Wednesday and Thursday. I will come back on how to work around the support for those two days."}]}]}]}, {"ts": "1741004525.922969", "text": "<@U07EJ2LP44S> couple of questions:\n• So we need to generate report with sum of all salaries bottom up and then bonus paid. Could you please help me with mapping to merit table columns to the report above. \n• Need little more clarity on the second table", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740763822.801109", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1741004713.000000"}, "blocks": [{"type": "rich_text", "block_id": "PxCie", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " couple of questions:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So we need to generate report with sum of all salaries bottom up and then bonus paid. Could you please help me with mapping to merit table columns to the report above. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Need little more clarity on the second table"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1740965371.065529", "text": "<@U07EJ2LP44S> Can I ?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740721189.675179", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "a3nAF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Can I ?"}]}]}]}, {"ts": "**********.294799", "text": "Will take care", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QnCVU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take care"}]}]}]}, {"ts": "**********.658649", "text": "<@U0690EB5JE5> I am not in front of my computer now so I can’t upload it to slack, but I forwarded you an email from Curana. They would like the file that is attached to be uploaded into their MIP account. ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.658649", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "4xLj8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am not in front of my computer now so I can’t upload it to slack, but I forwarded you an email from Curana. They would like the file that is attached to be uploaded into their MIP account. "}]}]}]}], "created_at": "2025-05-22T21:35:34.707971"}