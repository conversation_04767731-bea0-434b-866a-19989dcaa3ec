{"date": "2024-11-01", "channel_id": "C065QSSNH8A", "message_count": 27, "messages": [{"ts": "1730485450.334899", "text": "Here is the file i used", "user": "U04DKEFP1K8", "type": "message", "files": [{"id": "F07UJ9DTYKC", "created": 1730485448, "timestamp": 1730485448, "name": "CURANACOMPALL.csv", "title": "CURANACOMPALL.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": true, "size": 83743, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07UJ9DTYKC/curanacompall.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07UJ9DTYKC/download/curanacompall.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07UJ9DTYKC/curanacompall.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07UJ9DTYKC-77ae093e2e", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F07UJ9DTYKC/curanacompall.csv/edit", "preview": "Employee Id,Annual Salary Currency,Annual Salary (Currency),Annual Salary-Other (Currency),Variable Pay Currency,Variable Pay (%),Variable Pay (Currency),Target Bonus Currency,Target Bonus (%),Target Bonus (Currency),Last Raise Date,Previous Year Salary,Annual Salary OTE (Currency),Pay Mix,Hourly Rate,\"Update Type (NC,A,D,U)\"\r\n0002,USD,298784.98,0,USD,0,0,USD,0,0,3/23/24,0,0,0,0,U\r\n0008,USD,216000,0,USD,0,0,USD,0,0,3/23/24,0,0,0,0,U\r\n0015,USD,85573,0,USD,0,0,USD,0,0,3/23/24,0,0,0,0,U\r\n0016,USD,125725.6,0,USD,0,0,USD,0,0,3/23/24,0,0,0,0,U\r\n0027,USD,228999.94,0,USD,0,0,USD,0,0,3/23/24,0,0,0,0,U\r\n0034,USD,130000,0,USD,0,0,USD,0,0,5/15/22,0,0,0,125,U\r\n0039,USD,94000,0,USD,0,0,USD,0,0,3/23/24,0,0,0,0,U\r\n0059,USD,96871.5,0,USD,0,0,USD,0,0,3/23/24,0,0,0,0,U\r\n0067,USD,95000,0,USD,0,0,USD,0,0,3/23/24,0,0,0,0,U\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Employee Id</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary (Currency)</div><div class=\"cm-col\">Annual Salary-Other (Currency)</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay (Currency)</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus (Currency)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE (Currency)</div><div class=\"cm-col\">Pay Mix</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Update Type (NC,A,D,U)</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">0002</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">298784.98</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">3/23/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">0008</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">216000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">3/23/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">0015</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">85573</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">3/23/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">0016</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">125725.6</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">3/23/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">0027</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">228999.94</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">3/23/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">0034</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">130000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">5/15/22</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">125</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">0039</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">94000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">3/23/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">0059</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">96871.5</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">3/23/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">0067</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">95000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">3/23/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div></div>\n</div>\n", "lines": 1862, "lines_more": 1852, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "O04sE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the file i used"}]}]}]}, {"ts": "1730485433.757349", "text": "<@U07EJ2LP44S> curanacomp data is uploaded. There was formatting issue for value in the column \"Employee Id\" in file you had shared. I just copied formatted employee id from employee data in your file and upload went fine.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1730485433.757349", "reply_count": 15, "blocks": [{"type": "rich_text", "block_id": "mutJj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " curanacomp data is uploaded. There was formatting issue for value in the column \"Employee Id\" in file you had shared. I just copied formatted employee id from employee data in your file and upload went fine."}]}]}]}, {"ts": "1730483303.880089", "text": "<@U04DKEFP1K8> can you pls add the SSO instructions that we should to customers in this folder so I don't have to trace it down everytime I need it?\n<https://drive.google.com/drive/u/0/folders/1jWG6RWHuqyDH4rB-iy4v7TTz_GqXHRVe>", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "T/baC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you pls add the SSO instructions that we should to customers in this folder so I don't have to trace it down everytime I need it?\n"}, {"type": "link", "url": "https://drive.google.com/drive/u/0/folders/1jWG6RWHuqyDH4rB-iy4v7TTz_GqXHRVe"}]}]}]}, {"ts": "1730483051.144899", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> re chery<PERSON>'s question, I am not seeing the list of job titles for promoting employees. All I see is custom role. Is that how it is supposed to be for them>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730483051.144899", "reply_count": 38, "edited": {"user": "U07M6QKHUC9", "ts": "1730483380.000000"}, "blocks": [{"type": "rich_text", "block_id": "TC7ZN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " re cheryl's question, I am not seeing the list of job titles for promoting employees. All I see is custom role. Is that how it is supposed to be for them>"}]}]}]}, {"ts": "1730482403.754979", "text": "<@U0690EB5JE5> any idea why the salary currency is showing up as USD even for the international employees for Tithely from the integration sync? Per tithely, they have local currencies for international employees so it looks like a bug or data sync error. Can you pls double check?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730482403.754979", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "YJzY6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " any idea why the salary currency is showing up as USD even for the international employees for Tithely from the integration sync? Per tithely, they have local currencies for international employees so it looks like a bug or data sync error. Can you pls double check?"}]}]}]}, {"ts": "1730482241.530419", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> I deleted 3 emp for Tithely yesterday. But when I download the employee data template from the app today, those 3 deleted emp are showing as inactive in the downloaded temp. I would think they should not even show up in the downloaded temp anymore. What's the reason for showing them as inactive? Since they are not deleted, they also show up in the comp data template", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "T7ktV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I deleted 3 emp for Tithely yesterday. But when I download the employee data template from the app today, those 3 deleted emp are showing as inactive in the downloaded temp. I would think they should not even show up in the downloaded temp anymore. What's the reason for showing them as inactive? Since they are not deleted, they also show up in the comp data template"}]}]}]}, {"ts": "1730481782.626449", "text": "<!here> back online", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "klCm7", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " back online"}]}]}]}, {"ts": "1730473982.185959", "text": "sounds good thanks man", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Mm7Hj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "sounds good thanks man"}]}]}]}, {"ts": "1730473639.490539", "text": "Round off issue is bit more hard to fix in general. I mean even after my fixes there could be scenarios in future where we would see 1 cent difference . I will discuss this in detail on Monday ", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1730473842.000000"}, "blocks": [{"type": "rich_text", "block_id": "Bd4Tw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Round off issue is bit more hard to fix in general"}, {"type": "text", "text": ". I mean even after my fixes there could be scenarios in future where we would see 1 cent difference "}, {"type": "text", "text": ". I will discuss this in detail on Monday "}]}]}]}, {"ts": "1730473557.886079", "text": "<@U07M6QKHUC9> we made some good progress on the issues reported yesterday . I am working on SDF issues related to dates and round off issues in FE. I will push the fixes to SDF test your late evening. If you could test and let me know if any more issues by your Sunday evening. I can get those fixed by Monday PST.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1730473765.000000"}, "blocks": [{"type": "rich_text", "block_id": "qflOj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " we made some good progress on the issues reported yesterday . I am working on SDF issues related to dates and round off issues in FE. I will push the fixes to SDF test your late evening. If you could test and let me know if any more issues by your Sunday evening. I can get those fixed by Monday PST."}]}]}]}, {"ts": "1730423521.661469", "text": "<@U04DKEFP1K8> Were you able to test cycle recreation alayacare?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730423521.661469", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "dgRnp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Were you able to test cycle recreation alayacare?"}]}]}]}, {"ts": "1730423229.812099", "text": "SDF. I will check but we should not rush. Probably wait for another day regress and push fixes.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pzcTW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF. I will check but we should not rush. Probably wait for another day regress and push fixes."}]}]}]}, {"ts": "1730423166.981579", "text": "recreating cycle alaya care was tested yesterday and <PERSON> mentioned looks good. But will check with him again what level of testing he did.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "dVj+M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "recreating cycle alaya care was tested yesterday and <PERSON> mentioned looks good. But will check with him again what level of testing he did."}]}]}]}, {"ts": "1730423080.936839", "text": "I’m OK to wait until Monday for tithely", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8gPds", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I’m OK to wait until Monday for tithely"}]}]}]}, {"ts": "1730423057.980799", "text": "Fixing the SDF issue and ensure there is no issue with re-creating the cycle for alaya care are time sensitive ", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "mZr0H", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Fixing the SDF issue and ensure there is no issue with re-creating the cycle for alaya care are time sensitive "}]}]}]}, {"ts": "1730422992.376889", "text": "<@U07M6QKHUC9> there are quite a few issues. We will fix and regress. Hope these can wait till Monday.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zJNsG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " there are quite a few issues. We will fix and regress. Hope these can wait till Monday."}]}]}]}, {"ts": "1730412419.724469", "text": "<@U0690EB5JE5> another data inconistency <https://compiify.atlassian.net/browse/COM-3950>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14033::dcf0f10cbe9442e6a1ebbd117ea5b8e3", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3950?atlOrigin=eyJpIjoiZTUzNjIyNDU2MDBkNDRhZTgzZmM1NjA5Nzg1YjkwYzYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3950 Data Inconsistency Issue in Employee Count>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14033::ca334b43063b4fe3a18eb25fea915f7d", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14033\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3950\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3950", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "6e7U0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " another data inconistency "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3950"}]}]}]}, {"ts": "**********.537559", "text": "<@U04DKEFP1K8> Is there an easy way to delete the emp record other than marking them as D thru template upload? we have 3 emp in Tithely account who needs to be deleted", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.537559", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "B4w4e", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Is there an easy way to delete the emp record other than marking them as D thru template upload? we have 3 emp in Tithely account who needs to be deleted"}]}]}]}, {"ts": "**********.212019", "text": "<@U0690EB5JE5> filtering issue happening again  <https://compiify.atlassian.net/browse/COM-3949>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14032::f5d2fbd6101e4c699b7eb39986aed3cd", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3949?atlOrigin=eyJpIjoiMDc2MmZhYWY3ODkyNDE4YTg0NGViZWY5NjYyODBlYTgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3949 Issue: Inconsistent data display when filtering by Department NA>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14032::3654459509d546a89e5e17ab76626ed4", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14032\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3949\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3949", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "00KJp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " filtering issue happening again  "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3949"}]}]}]}, {"ts": "**********.719299", "text": "I hope our slack account is not being hacked", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.719299", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "t72kS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I hope our slack account is not being hacked"}]}]}]}, {"ts": "**********.222769", "text": "<!here> do we know who this person is?", "user": "U07M6QKHUC9", "type": "message", "files": [{"id": "F07UDPUAKB4", "created": **********, "timestamp": **********, "name": "Screenshot 2024-10-31 at 1.57.06 PM.png", "title": "Screenshot 2024-10-31 at 1.57.06 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 161123, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07UDPUAKB4/screenshot_2024-10-31_at_1.57.06___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07UDPUAKB4/download/screenshot_2024-10-31_at_1.57.06___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UDPUAKB4-6ca1b8e0da/screenshot_2024-10-31_at_1.57.06___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UDPUAKB4-6ca1b8e0da/screenshot_2024-10-31_at_1.57.06___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UDPUAKB4-6ca1b8e0da/screenshot_2024-10-31_at_1.57.06___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 148, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UDPUAKB4-6ca1b8e0da/screenshot_2024-10-31_at_1.57.06___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 197, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UDPUAKB4-6ca1b8e0da/screenshot_2024-10-31_at_1.57.06___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UDPUAKB4-6ca1b8e0da/screenshot_2024-10-31_at_1.57.06___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 296, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UDPUAKB4-6ca1b8e0da/screenshot_2024-10-31_at_1.57.06___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 328, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UDPUAKB4-6ca1b8e0da/screenshot_2024-10-31_at_1.57.06___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 394, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07UDPUAKB4-6ca1b8e0da/screenshot_2024-10-31_at_1.57.06___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 420, "original_w": 1364, "original_h": 560, "thumb_tiny": "AwATADCayYrAAFBBcg84xxVoEnt+tU7MSG3HllR8xzmp8T/3l/WmDJ8+xoz7GoP9I/vR/kaX/SP70f5UWETZ9jRn2NNTdtG4898U7n1NIZV04Ztjn+8at1U07/j3P+8at0AwooooAKKKKAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07UDPUAKB4/screenshot_2024-10-31_at_1.57.06___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07UDPUAKB4-a94ca6e617", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ooGHk", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " do we know who this person is?"}]}]}]}, {"ts": "1730408087.705949", "text": "<!here> Happy Halloween Folks:jack_o_lantern:\nWe all are crazily busy and swamped in Stride's haunted house but let's not forget to take time out to have some Halloween fun :partying_face:", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "04pmf", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Happy Halloween Folks"}, {"type": "emoji", "name": "jack_o_lantern", "unicode": "1f383"}, {"type": "text", "text": "\nWe all are crazily busy and swamped in Stride's haunted house but let's not forget to take time out to have some Halloween fun "}, {"type": "emoji", "name": "partying_face", "unicode": "1f973"}]}]}]}, {"ts": "1730405723.780839", "text": "<!here> i have a doctor appointment between 8-10am tomorrow and will not be available for standup", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "U/UrC", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " i have a doctor appointment between 8-10am tomorrow and will not be available for standup"}]}]}]}, {"ts": "1730403061.249839", "text": "<https://compiify.atlassian.net/browse/COM-3948>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14031::1b4b246517fb4d658e7128208bfd8294", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3948?atlOrigin=eyJpIjoiOWU2Y2ViODAzZTU3NGZkY2EwZWY2OThlYTY5NmJjYjAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3948 Unable to Add Currency for Localization in Admin View>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14031::0399bcb0f9a34592ae0cbf17ce3f2491", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14031\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3948\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3948", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "njx3k", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3948"}]}]}]}, {"ts": "1730402773.028079", "text": "<@U0690EB5JE5> here is a bug in org view for Tithely\n<https://compiify.atlassian.net/browse/COM-3947>", "user": "U07M6QKHUC9", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14030::8db8ca1ea8074d9daa7bcd81086ac4eb", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3947?atlOrigin=eyJpIjoiNjM3MjFlOTQ1Mjc4NGM2YzkyMThjZWUzODUwNzVhNGEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3947 Issue: Incorrect Currency Display for Employee Annual Salary>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14030::eb207507e97144a7bc942b75aa0788f0", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14030\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3947\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3947", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "4tC9i", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " here is a bug in org view for Tithely\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3947"}]}]}]}, {"ts": "1730401427.600219", "text": "<@U0690EB5JE5> Have we enabled SSO for Tithely yet? If not, what are the instructions I need to send them to enable SSO via Google. <@U04DKEFP1K8> can you send me the link to google doc that has customer facing SSO instructions?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730401427.600219", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "a6B2R", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Have we enabled SSO for Tithely yet? If not, what are the instructions I need to send them to enable SSO via Google. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you send me the link to google doc that has customer facing SSO instructions?"}]}]}]}, {"ts": "1730400960.707599", "text": "<@U07EJ2LP44S> Can we pls make sure that all of the customer's data files are in their corresponding folders in google drive?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1730400960.707599", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "UeGX0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Can we pls make sure that all of the customer's data files are in their corresponding folders in google drive?"}]}]}]}], "created_at": "2025-05-22T21:35:34.676040"}