{"thread_ts": "1737124353.756669", "channel_id": "C065QSSNH8A", "reply_count": 9, "replies": [{"ts": "1737125997.974719", "text": "<@U07EJ2LP44S> This will be fixed by Monday.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737124353.756669", "blocks": [{"type": "rich_text", "block_id": "hKtbw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This will be fixed by Monday."}]}]}]}, {"ts": "1737126548.603269", "text": "Ok; this is a bug then, I can't just correct the data?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737124353.756669", "blocks": [{"type": "rich_text", "block_id": "IAwKd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok; this is a bug then, I can't just correct the data?"}]}]}]}, {"ts": "1737126553.211949", "text": "She's panicking a bit as you know", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737124353.756669", "blocks": [{"type": "rich_text", "block_id": "PO0TJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She's panicking a bit as you know"}]}]}]}, {"ts": "1737126668.446289", "text": "This is being fixed as we speak but can't promise today. If I don't come back in an hour then it will be Monday.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737124353.756669", "blocks": [{"type": "rich_text", "block_id": "1FsQ6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is being fixed as we speak but can't promise today. If I don't come back in an hour then it will be Monday."}]}]}]}, {"ts": "1737126715.993989", "text": "ok, thank you", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737124353.756669", "blocks": [{"type": "rich_text", "block_id": "mj4kE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok, thank you"}]}]}]}, {"ts": "1737129394.329539", "text": "Just as an aside, we will need to be able to upload the individual attainment numbers for employees. This was part of the original design, but I want to make sure it will be functional. We will have EEID and the % achievement an employee reached. That needs to be uploaded into the employee earned section. I am not sure I can do that just by uploading employee data (I don't THIN<PERSON> so?) or if it needs to be done by ENG, but we'll need to upload some test data Monday to ensure everything is working.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737124353.756669", "blocks": [{"type": "rich_text", "block_id": "mOxi6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just as an aside, we will need to be able to upload the individual attainment numbers for employees. This was part of the original design, but I want to make sure it will be functional. We will have EEID and the % achievement an employee reached. That needs to be uploaded into the employee earned section. I am not sure I can do that just by uploading employee data (I don't THIN<PERSON> so?) or if it needs to be done by ENG, but we'll need to upload some test data Monday to ensure everything is working."}]}]}]}, {"ts": "1737129559.315539", "text": "<@U07EJ2LP44S> please create a ticket with a loom video and data file. I will make sure it’s in good shape by Monday morning.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737124353.756669", "edited": {"user": "U0690EB5JE5", "ts": "1737129570.000000"}, "blocks": [{"type": "rich_text", "block_id": "shGpB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " please create a ticket with a loo"}, {"type": "text", "text": "m"}, {"type": "text", "text": " video and data file. I will make sure it’s in good shape by Monday morning."}]}]}]}, {"ts": "1737131428.217209", "text": "<https://compiify.atlassian.net/browse/COM-4068>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737124353.756669", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14151::bd8f393576004860b0df8e83ad069344", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4068?atlOrigin=eyJpIjoiZjkzODBkYTkwMTg4NGFhNzljZmRjYWQ5ZDI1YzE1ZjgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4068 Update Individual Performance Percentage and Upload Earned Performa…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14151::40ef24d5682f4f70a6e5175b20bc3fc9", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14151\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4068\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4068", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "yN3iR", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4068"}]}]}]}, {"ts": "1737378029.464949", "text": "<@U07EJ2LP44S> This issue is fixed as well.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737124353.756669", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "WQslt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This issue is fixed as well."}]}]}]}], "created_at": "2025-05-22T22:00:49.330193"}