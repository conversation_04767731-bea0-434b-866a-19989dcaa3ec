#!/usr/bin/env python
"""
Debug script to understand LocalSlackSourceInterface behavior
"""

import os
import sys
import django
import logging
from pathlib import Path

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from apps.documents.interfaces.local_slack import LocalSlackSourceInterface

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def debug_local_slack():
    """Debug the LocalSlackSourceInterface configuration."""
    print("🔍 Debugging LocalSlackSourceInterface...")

    # Test configuration
    config = {
        "data_dir": "../data",
        "channel": "C065QSSNH8A",
        "aggregation_period": "monthly",
        "include_threads": True,
        "filter_bots": True,
        "max_documents_per_channel": 100,
        "custom_days": 30
    }

    print(f"📁 Config: {config}")

    # Create interface
    interface = LocalSlackSourceInterface(config)

    # Debug paths
    print(f"📂 Data dir: {interface.data_dir}")
    print(f"📂 Base data dir: {interface.base_data_dir}")
    print(f"📂 Channel ID: {interface.channel_id}")
    print(f"📂 Channel dir: {interface.channel_dir}")

    if hasattr(interface, 'messages_dir'):
        print(f"📂 Messages dir: {interface.messages_dir}")
        print(f"📂 Messages dir exists: {interface.messages_dir.exists() if interface.messages_dir else False}")

        if interface.messages_dir and interface.messages_dir.exists():
            message_files = list(interface.messages_dir.glob("messages_*.json"))
            print(f"📄 Found {len(message_files)} message files")
            if message_files:
                print(f"📄 Sample files: {[f.name for f in message_files[:5]]}")

    # Test data availability
    print("\n🔍 Testing data availability...")
    staged_info = interface.get_staged_data_info()
    print(f"📊 Staged data info:")
    print(f"   - Channel ID: {staged_info.channel_id}")
    print(f"   - Data dir: {staged_info.data_dir}")
    print(f"   - Available dates: {len(staged_info.available_dates)}")
    print(f"   - Total messages: {staged_info.total_messages}")
    print(f"   - Thread count: {staged_info.thread_count}")
    print(f"   - User count: {staged_info.user_count}")

    if staged_info.available_dates:
        print(f"   - Sample dates: {staged_info.available_dates[:5]}")

    # Test fetching messages first
    print("\n🔍 Testing message fetching...")
    try:
        messages = interface.fetch_channel_messages(days_back=365, include_threads=True, filter_bots=True)
        print(f"📄 Fetched {len(messages)} messages")

        if messages:
            msg = messages[0]
            print(f"📄 Sample message:")
            print(f"   - Text: {msg.text[:100]}...")
            print(f"   - User: {msg.user}")
            print(f"   - User name: {msg.user_name}")
            print(f"   - Timestamp: {msg.timestamp}")
            print(f"   - Channel: {msg.channel}")
        else:
            print("❌ No messages returned from fetch_channel_messages")

            # Debug the message loading process
            print("\n🔍 Debugging message loading...")
            dates_to_load = interface._determine_dates_to_load(30, None)
            print(f"📅 Dates to load: {len(dates_to_load)}")
            if dates_to_load:
                print(f"📅 Sample dates: {dates_to_load[:5]}")

                # Try loading messages for the first date
                sample_date = dates_to_load[0]
                print(f"\n🔍 Loading messages for date: {sample_date}")
                date_messages = interface._load_messages_for_date(sample_date, True)
                print(f"📄 Loaded {len(date_messages)} messages for {sample_date}")

                if date_messages:
                    msg = date_messages[0]
                    print(f"📄 Sample message from date:")
                    print(f"   - Text: {msg.text[:100]}...")
                    print(f"   - User: {msg.user}")
                    print(f"   - User name: {msg.user_name}")
                    print(f"   - Timestamp: {msg.timestamp}")

    except Exception as e:
        print(f"❌ Error fetching messages: {e}")
        import traceback
        traceback.print_exc()

    # Test fetching documents
    print("\n🔍 Testing document fetching...")
    try:
        # Test the document creation process step by step
        print("🔍 Testing document creation process...")

        # First get messages
        messages = interface.fetch_channel_messages(days_back=365, include_threads=True, filter_bots=True)
        print(f"📄 Got {len(messages)} messages for document creation")

        if messages:
            # Test grouping messages by time period
            grouped_messages = interface._group_messages_by_time_period(messages, "monthly", 30)
            print(f"📅 Grouped into {len(grouped_messages)} time periods")

            if grouped_messages:
                print(f"📅 Time periods: {list(grouped_messages.keys())[:5]}")

                # Check the first group
                first_period = list(grouped_messages.keys())[0]
                first_group_messages = grouped_messages[first_period]
                print(f"📄 First period '{first_period}' has {len(first_group_messages)} messages")

                # Test creating documents from grouped messages
                documents = interface._create_time_based_documents(messages, "monthly", 30)
                print(f"📄 Created {len(documents)} documents from grouped messages")

                if documents:
                    doc = documents[0]
                    print(f"📄 Sample document:")
                    print(f"   - ID: {doc.get('id', 'N/A')}")
                    print(f"   - Title: {doc.get('title', 'N/A')}")
                    print(f"   - Content length: {len(doc.get('content', ''))}")
                    print(f"   - Metadata keys: {list(doc.get('metadata', {}).keys())}")

        # Now test the full fetch_documents method
        documents = interface.fetch_documents(limit=5, days_back=365)
        print(f"📄 Full fetch_documents returned {len(documents)} documents")

        if documents:
            doc = documents[0]
            print(f"📄 Sample document from fetch_documents:")
            print(f"   - ID: {doc.get('id', 'N/A')}")
            print(f"   - Title: {doc.get('title', 'N/A')}")
            print(f"   - Content length: {len(doc.get('content', ''))}")
            print(f"   - Metadata keys: {list(doc.get('metadata', {}).keys())}")

    except Exception as e:
        print(f"❌ Error fetching documents: {e}")
        import traceback
        traceback.print_exc()

    # Check actual directory structure
    print("\n🔍 Checking actual directory structure...")
    data_path = Path("../data")
    print(f"📂 Data path exists: {data_path.exists()}")

    if data_path.exists():
        print(f"📂 Contents of {data_path}:")
        for item in data_path.iterdir():
            print(f"   - {item.name} ({'dir' if item.is_dir() else 'file'})")

            if item.is_dir() and item.name.startswith("channel_"):
                print(f"     📂 Channel dir: {item}")
                messages_dir = item / "messages"
                if messages_dir.exists():
                    message_count = len(list(messages_dir.glob("*.json")))
                    print(f"       📄 Messages: {message_count} files")
                else:
                    print(f"       ❌ No messages directory")

if __name__ == "__main__":
    debug_local_slack()
