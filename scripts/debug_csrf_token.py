#!/usr/bin/env python3
"""
Debug script to check CSRF token handling in detail.
"""

import requests
from bs4 import BeautifulSoup
import time

def debug_csrf_flow():
    """Debug the CSRF token flow step by step."""
    base_url = "http://localhost:8000"
    session = requests.Session()
    
    print("🔍 Debugging CSRF Token Flow...")
    
    # Step 1: Login
    print("\n1. Login Process...")
    login_response = session.get(f"{base_url}/accounts/login/")
    soup = BeautifulSoup(login_response.content, 'html.parser')
    csrf_token = soup.find('input', {'name': 'csrfmiddlewaretoken'}).get('value')
    
    login_data = {
        'username': 'testuser',
        'password': 'testpass123',
        'csrfmiddlewaretoken': csrf_token
    }
    
    login_result = session.post(f"{base_url}/accounts/login/", data=login_data)
    print(f"   Login status: {login_result.status_code}")
    
    # Step 2: Get search page and examine CSRF token
    print("\n2. Search Page CSRF Token Analysis...")
    search_response = session.get(f"{base_url}/search/")
    print(f"   Search page status: {search_response.status_code}")
    
    soup = BeautifulSoup(search_response.content, 'html.parser')
    
    # Find all CSRF tokens
    csrf_tokens = soup.find_all('input', {'name': 'csrfmiddlewaretoken'})
    print(f"   Found {len(csrf_tokens)} CSRF token(s)")
    
    for i, token in enumerate(csrf_tokens):
        token_value = token.get('value')
        print(f"   Token {i+1}: {token_value[:20]}... (length: {len(token_value)})")
        print(f"   Token {i+1} parent form: {token.find_parent('form').get('id', 'no-id')}")
    
    # Step 3: Test form submission with explicit token
    if csrf_tokens:
        main_token = csrf_tokens[0].get('value')
        print(f"\n3. Testing form submission with token: {main_token[:20]}...")
        
        # Test with form data exactly as browser would send
        form_data = {
            'query': 'test search query',
            'csrfmiddlewaretoken': main_token,
            'use_hybrid_search': 'true',
            'use_context_aware': 'true'
        }
        
        print("   Form data being sent:")
        for key, value in form_data.items():
            print(f"     {key}: {value}")
        
        # Submit the form
        search_result = session.post(
            f"{base_url}/search/query/",
            data=form_data,
            headers={
                'Referer': f"{base_url}/search/",
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        )
        
        print(f"   Search result status: {search_result.status_code}")
        
        if search_result.status_code == 403:
            print("   ❌ CSRF verification still failed")
            # Check response content for clues
            if "CSRF token missing" in search_result.text:
                print("   Error: CSRF token missing")
            elif "CSRF token incorrect" in search_result.text:
                print("   Error: CSRF token incorrect")
            else:
                print("   Error: Other CSRF issue")
        elif search_result.status_code == 200:
            print("   ✅ Search successful!")
        elif search_result.status_code == 302:
            print(f"   ✅ Redirected to: {search_result.headers.get('Location')}")
        else:
            print(f"   ⚠️  Unexpected status: {search_result.status_code}")
    
    # Step 4: Check cookies and session
    print("\n4. Session and Cookie Analysis...")
    print(f"   Session cookies: {list(session.cookies.keys())}")
    for cookie in session.cookies:
        if 'csrf' in cookie.name.lower():
            print(f"   CSRF cookie: {cookie.name} = {cookie.value[:20]}...")

if __name__ == "__main__":
    debug_csrf_flow()
