{"date": "2024-10-21", "channel_id": "C065QSSNH8A", "message_count": 25, "messages": [{"ts": "1729533665.957639", "text": "ah just wrapped up. will call him back so I am good and joining", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "c7HO1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ah just wrapped up. will call him back so I am good and joining"}]}]}]}, {"ts": "1729533551.539949", "text": "Ok we will get started", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "AlDCa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok we will get started"}]}]}]}, {"ts": "1729533528.356139", "text": "<@U07EJ2LP44S> I am on a call with one of our investors, will join few min late for the SDF demo", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "stfEV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am on a call with one of our investors, will join few min late for the SDF demo"}]}]}]}, {"ts": "1729532943.762609", "text": "<!here> going forward, sales will provide daily updates on all things sales in <#C07SEC04BP1|> channel. If you have any questions, need clarifications etc on sales related items, please use that channel", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qSHt9", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " going forward, sales will provide daily updates on all things sales in "}, {"type": "channel", "channel_id": "C07SEC04BP1"}, {"type": "text", "text": " channel. If you have any questions, need clarifications etc on sales related items, please use that channel"}]}]}]}, {"ts": "1729528626.066889", "text": "<http://qa.stridehr.io|qa.stridehr.io>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "KGV/f", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}]}]}]}, {"ts": "1729526340.473479", "text": "i think it was deleted.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729526340.473479", "reply_count": 2, "reactions": [{"name": "upside_down_face", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "J7CX6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i think it was deleted."}]}]}]}, {"ts": "1729526191.614519", "text": "<@U07EJ2LP44S> vercara came back from being dormant. can you pls respond to them once we reactivate their env. <@U04DKEFP1K8> FYA", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hxw0s", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " vercara came back from being dormant. can you pls respond to them once we reactivate their env. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " FYA"}]}]}]}, {"ts": "1729525448.687569", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> ^^this failed for me with the same 'internal error' I got with stride<PERSON><PERSON>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729525448.687569", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "1+Phm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " ^^this failed for me with the same 'internal error' I got with <PERSON><PERSON><PERSON>"}]}]}]}, {"ts": "1729525098.913229", "text": "can you please create local login for cheryl for AlayaCare? But it would be for qa.strideghr environment as we are using AlayaCare data for that env?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "CllzQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "can you please create local login for cheryl for AlayaCare? But it would be for qa.strideghr environment as we are using AlayaCare data for that env?"}]}]}]}, {"ts": "1729524995.906439", "text": "<@U07EJ2LP44S> do you know how to create local logins or is this something needs to be done by <PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON><PERSON>?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729524995.906439", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "y67Qd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " do you know how to create local logins or is this something needs to be done by <PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON><PERSON>?"}]}]}]}, {"ts": "1729524726.094469", "text": "<@U07EJ2LP44S> Are you done with your testing on SDF on Org View, Paybands, Insights and Reporting? If so, Can I start my round of testing?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729524726.094469", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "nOUSX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Are you done with your testing on SDF on Org View, Paybands, Insights and Reporting? If so, Can I start my round of testing?"}]}]}]}, {"ts": "1729524103.028379", "text": "<@U07EJ2LP44S> The link for these tickets is not working", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729453404.206219", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "z9eCw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " The link for these tickets is not working"}]}]}]}, {"ts": "1729453850.717929", "text": "<!here> team, given all of the issues we are having with the QA testing, IMO if any aspect of testing is not documented going forward, then let’s assume it was not tested. if anyone has any objections to this, please reach out to me or feel free to propose a better solution in the leadership standup", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1729454349.000000"}, "blocks": [{"type": "rich_text", "block_id": "rTLqO", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " team, given all of the issues we are having with the QA testing, "}, {"type": "text", "text": "IMO"}, {"type": "text", "text": " if any aspect of testing is not documented going forward, then let’s assume it was not tested. if anyone has any objections to this, please reach out to me or feel free to propose a better solution in the leadership standup"}]}]}]}, {"ts": "1729453429.245959", "text": "For the reporting you don’t need to specifically look for calculations. As long as the data and the format in the reports matches what we have in the org view and And cycle insights then we should be good", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729453429.245959", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "pTIgi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For the reporting you don’t need to specifically look for calculations. As long as the data and the format in the reports matches what we have in the org view and And cycle insights then we should be good"}]}]}]}, {"ts": "1729453404.206219", "text": "i'm not sure if this filter works for you but these are all the tickets i've been putting in: <https://compiify.atlassian.net/issues/?filter=10022>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729453404.206219", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "oqT49", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i'm not sure if this filter works for you but these are all the tickets i've been putting in: "}, {"type": "link", "url": "https://compiify.atlassian.net/issues/?filter=10022"}]}]}]}, {"ts": "1729453357.712489", "text": "I documented all the steps in your format. I put the tickets directly in jira. do you want to me to put a comment where i entered a bug?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4y6bY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I documented all the steps in your format. I put the tickets directly in jira. do you want to me to put a comment where i entered a bug?"}]}]}]}, {"ts": "**********.952899", "text": "For that one I don't know the calcs behind the data, but I spot checked that it looked accurate, everything was downloading, and consistent with the account/merit view", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0e+nP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For that one I don't know the calcs behind the data, but I spot checked that it looked accurate, everything was downloading, and consistent with the account/merit view"}]}]}]}, {"ts": "**********.614669", "text": "I will check out the tickets that you have raised. Can I go ahead and start testing the org view, and insights and paybands? all the things that you have tested so far, are they documented in the QA spreadsheet?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sdd52", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will check out the tickets that you have raised. Can I go ahead and start testing the org view, and insights and paybands? all the things that you have tested so far, are they documented in the QA spreadsheet?"}]}]}]}, {"ts": "**********.042479", "text": "oh that one too sorry", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6zrpL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "oh that one too sorry"}]}]}]}, {"ts": "**********.519399", "text": "Thanks <PERSON>. How are we doing on the reporting?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HvHVa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks <PERSON>. How are we doing on the reporting?"}]}]}]}, {"ts": "1729453058.302709", "text": "<@U07M6QKHUC9> I'm mostly done with the org view/insights/paybands in sdf. I did find a few bugs (paybands is off) and a few enhancements that are in Jira now.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WfvWT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I'm mostly done with the org view/insights/paybands in sdf. I did find a few bugs (paybands is off) and a few enhancements that are in Jira now."}]}]}]}, {"ts": "1729451067.469449", "text": "<@U07NBMXTL1E> we are going to cancel sales update until sales start making good progress on outbound.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xa7KV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " we are going to cancel sales update until sales start making good progress on outbound."}]}]}]}, {"ts": "1729450994.907849", "text": "Agenda for tomorrow's standup:\nQA testing on SDF and update on Jira tickets raised\nSDF Demo and handling over test env to them\nQA testing on AlayaCare\nQA testing on CainWatters", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "J6Nta", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for tomorrow's standup:\nQA testing on SDF and update on Jira tickets raised\nSDF Demo and handling over test env to them\nQA testing on AlayaCare\nQA testing on CainWatters"}]}]}]}, {"ts": "1729450270.864989", "text": "resurfacing it <@U0690EB5JE5>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729365127.200369", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "b3V9Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "resurfacing it "}, {"type": "user", "user_id": "U0690EB5JE5"}]}]}]}, {"ts": "1729450238.537399", "text": "<@U0690EB5JE5> in QA test env, for Alaya Care,  budgets are also not consistent in my tasks vs merit. I did not even check cycle insights", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729365127.200369", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "GKwhX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " in QA test env, for Alaya Care,  budgets are also not consistent in my tasks vs merit. I did not even check cycle insights"}]}]}]}], "created_at": "2025-05-22T21:35:34.665455"}