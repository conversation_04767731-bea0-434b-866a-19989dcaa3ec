#!/usr/bin/env python3
"""
Test script for enhanced prompt templates with detailed, date-specific responses.
"""

import os
import sys
import time
import django

# Add the project root to the Python path
sys.path.insert(0, '/Users/<USER>/Desktop/RAGSearch/multi_source_rag')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.unified_rag_service import UnifiedRAGService
from apps.accounts.models import Tenant, User
from apps.core.utils.query_classifier import classify_query

def test_enhanced_prompts():
    """Test the enhanced prompt templates for detailed responses."""
    print("🧪 Testing Enhanced Prompt Templates for Detailed Responses")
    print("=" * 70)

    # Get tenant and user
    try:
        tenant = Tenant.objects.first()
        user = User.objects.first()
        if not tenant or not user:
            print("❌ Error: No tenant or user found")
            return
        print(f"✅ Using tenant: {tenant.name}")
        print(f"✅ Using user: {user.email}")
    except Exception as e:
        print(f"❌ Error: {e}")
        return

    # Initialize service
    service = UnifiedRAGService(tenant=tenant.slug, user=user)

    # Test queries for different types
    test_queries = [
        {
            "query": "whats latest on curana?",
            "description": "Latest updates query - should trigger detailed chronological response"
        },
        {
            "query": "summarize issues about curana",
            "description": "Issue summary query - should provide detailed pointwise summary"
        },
        {
            "query": "what did Amanda say about testing?",
            "description": "Factual query - should include specific dates and quotes"
        }
    ]

    for i, test_case in enumerate(test_queries, 1):
        query = test_case["query"]
        description = test_case["description"]

        print(f"\n🔍 Test {i}: {description}")
        print(f"📝 Query: '{query}'")
        print("-" * 50)

        # Classify the query first
        classification = classify_query(query)
        print(f"🏷️  Query Type: {classification['type']} (confidence: {classification['confidence']:.2f})")

        try:
            start_time = time.time()
            search_result, retrieved_docs = service.search(
                query_text=query,
                top_k=12,
                min_relevance_score=0.08,
                use_hybrid_search=True
            )
            end_time = time.time()

            print(f"⏱️  Duration: {end_time - start_time:.2f} seconds")
            print(f"📊 Retrieved Documents: {len(retrieved_docs)}")
            print(f"🔗 Citations: {search_result.citations.count()}")

            # Show the response
            print(f"\n📋 Response:")
            print("=" * 40)
            response_text = getattr(search_result, 'response_text', '') or getattr(search_result, 'response', '') or 'No response available'
            print(response_text)
            print("=" * 40)

            # Show citations with metadata
            print(f"\n📚 Citations ({search_result.citations.count()}):")
            for j, citation in enumerate(search_result.citations.all()[:5], 1):
                chunk = citation.document_chunk
                print(f"   {j}. Score: {citation.relevance_score:.3f}")
                print(f"      📅 Date: {chunk.created_at.strftime('%Y-%m-%d %H:%M')}")

                if chunk.profile:
                    print(f"      👤 Profile: {chunk.profile.display_name}")

                # Show content preview
                content = getattr(chunk, 'content', '') or getattr(chunk, 'text', '') or 'No content available'
                content_preview = content[:150].replace('\n', ' ')
                print(f"      📝 Content: {content_preview}...")

                if chunk.metadata:
                    print(f"      🏷️  Metadata: {chunk.metadata}")
                print()

        except Exception as e:
            print(f"❌ Error during search: {str(e)}")
            import traceback
            traceback.print_exc()

        print("\n" + "="*70)

    print("\n✅ Enhanced prompt template testing completed!")
    print("\n📊 Key Improvements Expected:")
    print("   • Specific dates in responses")
    print("   • Pointwise format with bullet points")
    print("   • Names of people and systems")
    print("   • Detailed context and quotes")
    print("   • Chronological organization")
    print("   • Structured sections (Timeline, People, Status)")

if __name__ == "__main__":
    test_enhanced_prompts()
