{"date": "2024-11-26", "channel_id": "C065QSSNH8A", "message_count": 11, "messages": [{"ts": "1732638372.904319", "text": "<https://support.pave.com/hc/en-us/articles/4689346817559-Intro-to-Pave>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "udOzr", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://support.pave.com/hc/en-us/articles/4689346817559-Intro-to-Pave"}]}]}]}, {"ts": "1732633721.019079", "text": "Proration date picker bug: <https://compiify.atlassian.net/browse/COM-4002>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732633721.019079", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14085::4cf07cea4449444ba2a41f6931fd09db", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4002?atlOrigin=eyJpIjoiZjQ3ZTkwODU3Y2UwNGE1ZDhkYTg1YTg1MjliOTBlZTMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4002 Issue: Proration date selection bug>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14085::00225a98b68f45bc9e981ae25ce79050", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14085\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4002\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4002", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "jIEP5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Proration date picker bug: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4002"}]}]}]}, {"ts": "1732617446.096869", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S>\n*Agenda for today's call:*\n• Alayacare Cycle\n• DegenKolb Report change\n• Bonus change clarification\n• Regression Test Automation progress and timelines", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qIMYX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Agenda for today's call:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Alayacare Cycle"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "DegenKolb Report change"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Bonus change clarification"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Regression Test Automation progress and timelines"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1732615882.845119", "text": "<@U07EJ2LP44S> We have the code changes ready for Summary row in cycle changes report for DegenKolb. Please review the report attached here and let us know if any further changes required. Please comment on the ticket itself\n<https://compiify.atlassian.net/browse/COM-3733>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1732615882.845119", "reply_count": 3, "edited": {"user": "U0690EB5JE5", "ts": "1732616432.000000"}, "files": [{"id": "F082H6W9BJN", "created": 1732615852, "timestamp": 1732615852, "name": "Merit Cycle Changes - 2024-11-26.csv", "title": "Merit Cycle Changes - 2024-11-26.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": true, "size": 257776, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F082H6W9BJN/merit_cycle_changes_-_2024-11-26.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F082H6W9BJN/download/merit_cycle_changes_-_2024-11-26.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F082H6W9BJN/merit_cycle_changes_-_2024-11-26.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F082H6W9BJN-a4609acf13", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F082H6W9BJN/merit_cycle_changes_-_2024-11-26.csv/edit", "preview": "Employee_ID,First_Name,Last_Name,Hire_Date,Total_Tenure,Performance_Rating,Location_Country,Location_Region,Salary_Tier,Department,Team,Job_Title,Job_Category,Job_Level,Job_Family,FTE%,Weekly_Hours,Worker_Type,HR_Business_Partner,Employee_Local_Currency,Compensation_Type,Reporting_Manager_ID,Reporting_Manager_Name,Old_Annual_Salary_Local,Old_Annual_Salary_USD,Old_Compa_Ratio,Old_Position_In_Band_Percent,Old_Salary_Band_ID,Old_Salary_Band_Min,Old_Salary_Band_Mid,Old_Salary_Band_Max,Old_Pay_Mix,Old_Base_Pay,Old_Variable_Pay,Promotion_This_Cycle,Promotion_Increase_Percent,Promotion_Increase_Amount_Local,Promotion_Increase_Amount_USD,New_Job_Title,New_Job_Category,New_Job_Level,New_Bonus_Target,New_Bonus_Target_Percent,Merit_Increase_Percent,Proration_Multiplier,Merit_Increase_Amount_Local,Merit_Increase_Amount_USD,Market_Adjustment_Percent,Market_Adjustment_Amount_Local,Market_Adjustment_Amount_USD,COLA_Percent,COLA_Amount_Local,COLA_Amount_USD,Hourly_Rate_Local,Hourly_Rate_USD,Hourly_Promotion_Increase_Local,Ho...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Employee_ID</div><div class=\"cm-col\">First_Name</div><div class=\"cm-col\">Last_Name</div><div class=\"cm-col\">Hire_Date</div><div class=\"cm-col\">Total_Tenure</div><div class=\"cm-col\">Performance_Rating</div><div class=\"cm-col\">Location_Country</div><div class=\"cm-col\">Location_Region</div><div class=\"cm-col\">Salary_Tier</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Job_Title</div><div class=\"cm-col\">Job_Category</div><div class=\"cm-col\">Job_Level</div><div class=\"cm-col\">Job_Family</div><div class=\"cm-col\">FTE%</div><div class=\"cm-col\">Weekly_Hours</div><div class=\"cm-col\">Worker_Type</div><div class=\"cm-col\">HR_Business_Partner</div><div class=\"cm-col\">Employee_Local_Currency</div><div class=\"cm-col\">Compensation_Type</div><div class=\"cm-col\">Reporting_Manager_ID</div><div class=\"cm-col\">Reporting_Manager_Name</div><div class=\"cm-col\">Old_Annual_Salary_Local</div><div class=\"cm-col\">Old_Annual_Salary_USD</div><div class=\"cm-col\">Old_Compa_Ratio</div><div class=\"cm-col\">Old_Position_In_Band_Percent</div><div class=\"cm-col\">Old_Salary_Band_ID</div><div class=\"cm-col\">Old_Salary_Band_Min</div><div class=\"cm-col\">Old_Salary_Band_Mid</div><div class=\"cm-col\">Old_Salary_Band_Max</div><div class=\"cm-col\">Old_Pay_Mix</div><div class=\"cm-col\">Old_Base_Pay</div><div class=\"cm-col\">Old_Variable_Pay</div><div class=\"cm-col\">Promotion_This_Cycle</div><div class=\"cm-col\">Promotion_Increase_Percent</div><div class=\"cm-col\">Promotion_Increase_Amount_Local</div><div class=\"cm-col\">Promotion_Increase_Amount_USD</div><div class=\"cm-col\">New_Job_Title</div><div class=\"cm-col\">New_Job_Category</div><div class=\"cm-col\">New_Job_Level</div><div class=\"cm-col\">New_Bonus_Target</div><div class=\"cm-col\">New_Bonus_Target_Percent</div><div class=\"cm-col\">Merit_Increase_Percent</div><div class=\"cm-col\">Proration_Multiplier</div><div class=\"cm-col\">Merit_Increase_Amount_Local</div><div class=\"cm-col\">Merit_Increase_Amount_USD</div><div class=\"cm-col\">Market_Adjustment_Percent</div><div class=\"cm-col\">Market_Adjustment_Amount_Local</div><div class=\"cm-col\">Market_Adjustment_Amount_USD</div><div class=\"cm-col\">COLA_Percent</div><div class=\"cm-col\">COLA_Amount_Local</div><div class=\"cm-col\">COLA_Amount_USD</div><div class=\"cm-col\">Hourly_Rate_Local</div><div class=\"cm-col\">Hourly_Rate_USD</div><div class=\"cm-col\">Hourly_Promotion_Increase_Local</div><div class=\"cm-col\">Hourly_Promotion_Increase_Percentage</div><div class=\"cm-col\">Hourly_Promotion_Increase_USD</div><div class=\"cm-col\">Hourly_Salary_Increase_Local</div><div class=\"cm-col\">Hourly_Salary_Increase_Percentage</div><div class=\"cm-col\">Hourly_Salary_Increase_USD</div><div class=\"cm-col\">New_Annual_Salary_Local</div><div class=\"cm-col\">New_Hourly_Rate_Local</div><div class=\"cm-col\">New_Hourly_Rate_USD</div><div class=\"cm-col\">New_Compa_Ratio</div><div class=\"cm-col\">New_Position_In_Band_Percent</div><div class=\"cm-col\">New_Salary_Band_ID</div><div class=\"cm-col\">New_Salary_Band_Min</div><div class=\"cm-col\">New_Salary_Band_Mid</div><div class=\"cm-col\">New_Salary_Band_Max</div><div class=\"cm-col\">New_Pay_Mix</div><div class=\"cm-col\">New_Base_Pay</div><div class=\"cm-col\">New_Variable_Pay</div><div class=\"cm-col\">Bonus_Target_Percent</div><div class=\"cm-col\">Bonus_Award_Percent</div><div class=\"cm-col\">Bonus_Award_Amount_Local</div><div class=\"cm-col\">Bonus_Award_Amount_USD</div><div class=\"cm-col\">One_Time_Bonus_Local</div><div class=\"cm-col\">One_Time_Bonus_USD</div><div class=\"cm-col\">Old_Equity_Total</div><div class=\"cm-col\">Old_Equity_Vested_Percent</div><div class=\"cm-col\">Old_Equity_Unvested_Amount</div><div class=\"cm-col\">Old_Equity_Band_Min</div><div class=\"cm-col\">Old_Equity_Band_Mid</div><div class=\"cm-col\">Old_Equity_Band_Max</div><div class=\"cm-col\">Refresh_Equity_Amount</div><div class=\"cm-col\">Refresh_Equity_Units</div><div class=\"cm-col\">Promotion_Equity_Amount</div><div class=\"cm-col\">Promotion_Equity_Units</div><div class=\"cm-col\">New_Equity_Total</div><div class=\"cm-col\">New_Equity_Vested_Percent</div><div class=\"cm-col\">New_Equity_Unvested_Amount</div><div class=\"cm-col\">New_Equity_Band_Min</div><div class=\"cm-col\">New_Equity_Band_Mid</div><div class=\"cm-col\">New_Equity_Band_Max</div><div class=\"cm-col\">Total_Target_Cash_Old</div><div class=\"cm-col\">Total_Target_Cash_New</div><div class=\"cm-col\">Review_Status</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">106</div><div class=\"cm-col\">James</div><div class=\"cm-col\">Malley</div><div class=\"cm-col\">2014-01-01</div><div class=\"cm-col cm-num\">10.8</div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\">San Francisco</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Senior Principal</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">9</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\">Employee</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">USD</div><div class=\"cm-col\">Regular</div><div class=\"cm-col cm-num\">545</div><div class=\"cm-col\">Stacy Bartoletti</div><div class=\"cm-col\">336,000.00</div><div class=\"cm-col\">336,000.00</div><div class=\"cm-col cm-num\">1.29</div><div class=\"cm-col cm-num\">297.96</div><div class=\"cm-col cm-num\">624</div><div class=\"cm-col\">244,851.60</div><div class=\"cm-col\">260,147.10</div><div class=\"cm-col\">275,442.60</div><div class=\"cm-col\">100/0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">False</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\">Senior Principal</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">9</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">1.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\"></div><div class=\"cm-col\">336,000.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">1.29</div><div class=\"cm-col cm-num\">297.96</div><div class=\"cm-col cm-num\">624</div><div class=\"cm-col\">244,851.60</div><div class=\"cm-col\">260,147.10</div><div class=\"cm-col\">275,442.60</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\">336,000.00</div><div class=\"cm-col\">336,000.00</div><div class=\"cm-col\">Pending Submission</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">106A</div><div class=\"cm-col\">James</div><div class=\"cm-col\">Malley-A</div><div class=\"cm-col\">1983-01-10</div><div class=\"cm-col cm-num\">41.8</div><div class=\"cm-col\"></div><div class=\"cm-col\">US</div><div class=\"cm-col\">San Francisco</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">Engineering</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Working Group Leader</div><div class=\"cm-col\">Manager</div><div class=\"cm-col cm-num\">7</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\">Employee</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col cm-num\">545</div><div class=\"cm-col\">Stacy Bartoletti</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">617</div><div class=\"cm-col\">9,720.00</div><div class=\"cm-col\">25,290.00</div><div class=\"cm-col\">40,860.00</div><div class=\"cm-col\">100/0</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">False</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\">Working Group Leader</div><div class=\"cm-col\">Manager</div><div class=\"cm-col cm-num\">7</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">1.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">617</div><div class=\"cm-col\">9,720.00</div><div class=\"cm-col\">25,290.00</div><div class=\"cm-col\">40,860.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\">Pending Submission</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">106Summary</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">336,000.00</div><div class=\"cm-col\">336,000.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col cm-num\">0.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">336,000.00</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">336,000.00</div><div class=\"cm-col\">336,000.00</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 475, "lines_more": 474, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13794::f23b4aa5b74e4f77b2fe174099fb432d", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3733?atlOrigin=eyJpIjoiZmE4NTBhN2Q3Y2U3NDVlODlhMGM2ZmMzOGZkOGNlMTUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3733 Support for an additional row to show total compensation structure …>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13794::b749fc5e9f0240fda945f3ebc7321b51", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/3ba050d9f25e3d5164f213ae816bb449?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13794\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3733\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3733", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "ard4R", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We have the code changes ready for Summary row in cycle changes report for DegenKolb. Please review the report attached here and let us know if any further changes required. Please comment on the ticket itself\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3733"}]}]}]}, {"ts": "1732605598.295629", "text": "Also Position In Band for Alaycare hourly employees is fixed across the product. PTAL and let me know if any issues.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "K7YOO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also Position In Band for Alaycare hourly employees is fixed across the product. PTAL and let me know if any issues."}]}]}]}, {"ts": "1732605391.925989", "text": "<@U07EJ2LP44S> Alaycare Cycle,  one of HRBPs has input `New Target Bonus` Could you please check if this is intended? I suspect they intended to edit `Bonus Award`", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1732616650.000000"}, "files": [{"id": "F0829PYKSVC", "created": 1732605331, "timestamp": 1732605331, "name": "Screenshot 2024-11-26 at 12.45.23 PM.png", "title": "Screenshot 2024-11-26 at 12.45.23 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 365276, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F0829PYKSVC/screenshot_2024-11-26_at_12.45.23___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F0829PYKSVC/download/screenshot_2024-11-26_at_12.45.23___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0829PYKSVC-96b10c14d2/screenshot_2024-11-26_at_12.45.23___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0829PYKSVC-96b10c14d2/screenshot_2024-11-26_at_12.45.23___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0829PYKSVC-96b10c14d2/screenshot_2024-11-26_at_12.45.23___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 182, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0829PYKSVC-96b10c14d2/screenshot_2024-11-26_at_12.45.23___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 243, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0829PYKSVC-96b10c14d2/screenshot_2024-11-26_at_12.45.23___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0829PYKSVC-96b10c14d2/screenshot_2024-11-26_at_12.45.23___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 364, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0829PYKSVC-96b10c14d2/screenshot_2024-11-26_at_12.45.23___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 404, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0829PYKSVC-96b10c14d2/screenshot_2024-11-26_at_12.45.23___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 485, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0829PYKSVC-96b10c14d2/screenshot_2024-11-26_at_12.45.23___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 517, "original_w": 3392, "original_h": 1714, "thumb_tiny": "AwAYADCvRT4ADPGCMjcK1PKj/wCeSflQBkUVr+TH/wA80/75FAhj/wCeaf8AfIoAyKTv0rY8qPP+qT8qypABK4A6MaAGgkHIODT/ADZP+ej/AJmmUUAP82T/AJ6P+Zo82T/no/5mmUUAP82T/no/5mmHk5NFFAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0829PYKSVC/screenshot_2024-11-26_at_12.45.23___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F0829PYKSVC-94f7767fd2", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "mOzf/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Alaycare Cycle,  one of HRBPs has input "}, {"type": "text", "text": "New Target Bonus", "style": {"code": true}}, {"type": "text", "text": " Could you please check if this is intended? I suspect they intended to edit "}, {"type": "text", "text": "Bonus Award", "style": {"code": true}}]}]}]}, {"ts": "1732590693.132159", "text": "<@U07EJ2LP44S> Its easy if any such requests above converted to Jira. With slack its difficult to track these threads. Will take care of the above for this time.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1732604581.000000"}, "blocks": [{"type": "rich_text", "block_id": "JgDDv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Its easy if any such requests above converted to Jira. With slack its difficult to track these threads. Will take care of the above for this time."}]}]}]}, {"ts": "1732571618.883809", "text": "<@U0690EB5JE5> are these visual changes able to be made to the diversified environment for the purposes of testing? I know the column hiding/moving will be available in Jan but if we could hide in this environment it would help her test.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732571618.883809", "reply_count": 6, "files": [{"id": "F082043LHCP", "created": 1732571597, "timestamp": 1732571597, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 173907, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F082043LHCP/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F082043LHCP/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F082043LHCP-8ee600defb/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F082043LHCP-8ee600defb/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F082043LHCP-8ee600defb/image_360.png", "thumb_360_w": 360, "thumb_360_h": 272, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F082043LHCP-8ee600defb/image_480.png", "thumb_480_w": 480, "thumb_480_h": 362, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F082043LHCP-8ee600defb/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F082043LHCP-8ee600defb/image_720.png", "thumb_720_w": 720, "thumb_720_h": 543, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F082043LHCP-8ee600defb/image_800.png", "thumb_800_w": 800, "thumb_800_h": 604, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F082043LHCP-8ee600defb/image_960.png", "thumb_960_w": 960, "thumb_960_h": 724, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F082043LHCP-8ee600defb/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 773, "original_w": 1508, "original_h": 1138, "thumb_tiny": "AwAkADDRKkk80BfUmkJIY80/PvQAm33NG33NLketFACbfc0BcdzS0UANYjoCAaNp9R+VB69aTJ9f1oAXafanU3I/vUo56NQAtFJj3NLj3NACY+tGPc/nS0UAJj3P50Y9z+dLRQAmPc/nS0UUAf/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F082043LHCP/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F082043LHCP-1beac057c3", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "+9nua", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " are these visual changes able to be made to the diversified environment for the purposes of testing? I know the column hiding/moving will be available in Jan but if we could hide in this environment it would help her test."}]}]}]}, {"ts": "1732567510.332999", "text": "<!here> had a good call with <PERSON> today for the benchmarking data. We are meeting again next week to discuss the next steps on understanding the scope of partnership.", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07NBMXTL1E", "U0690EB5JE5"], "count": 2}, {"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "AcdD3", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " had a good call with <PERSON> today for the benchmarking data. We are meeting again next week to discuss the next steps on understanding the scope of partnership."}]}]}]}, {"ts": "1732567334.178639", "text": "Feedback on naming. Agreed that 'merit cycle' is too generalized for the variety of cycles a customer may run (bonus, merit, maybe a separate equity cycle eventually, etc)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732567334.178639", "reply_count": 3, "files": [{"id": "F082BEVMUCV", "created": 1732567275, "timestamp": 1732567275, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 158875, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F082BEVMUCV/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F082BEVMUCV/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F082BEVMUCV-1ea32e8103/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F082BEVMUCV-1ea32e8103/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F082BEVMUCV-1ea32e8103/image_360.png", "thumb_360_w": 360, "thumb_360_h": 167, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F082BEVMUCV-1ea32e8103/image_480.png", "thumb_480_w": 480, "thumb_480_h": 223, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F082BEVMUCV-1ea32e8103/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F082BEVMUCV-1ea32e8103/image_720.png", "thumb_720_w": 720, "thumb_720_h": 334, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F082BEVMUCV-1ea32e8103/image_800.png", "thumb_800_w": 800, "thumb_800_h": 371, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F082BEVMUCV-1ea32e8103/image_960.png", "thumb_960_w": 960, "thumb_960_h": 446, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F082BEVMUCV-1ea32e8103/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 475, "original_w": 1534, "original_h": 712, "thumb_tiny": "AwAWADDROc9/zo/A/nSnOcY4x1zSYPtQAfgfzpfwP50oz3ooATH1/OgfQ/nS0UAJzu7YxRg0d6WgBOaWiigAooooA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F082BEVMUCV/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F082BEVMUCV-4fd8cd3ba0", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Krnn3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Feedback on naming. Agreed that 'merit cycle' is too generalized for the variety of cycles a customer may run (bonus, merit, maybe a separate equity cycle eventually, etc)"}]}]}]}, {"ts": "**********.849459", "text": "<@U0690EB5JE5> how difficult would it be to put a link to a google form in the application? I am thinking a little link under their account info in the upper right of the page (beneath their title) for 'provide feedback' or similar. Could link to a Google form we create to collect user feedback.\n\nI'd also like to put NPS in the application, but I think that's going to require buying a service so maybe not right now", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.849459", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "fBckn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " how difficult would it be to put a link to a google form in the application? I am thinking a little link under their account info in the upper right of the page (beneath their title) for 'provide feedback' or similar. Could link to a Google form we create to collect user feedback.\n\nI'd also like to put NPS in the application, but I think that's going to require buying a service so maybe not right now"}]}]}]}], "created_at": "2025-05-22T21:35:34.684219"}