{"date": "2024-02-23", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1708710516.475199", "text": "<PERSON> is finally logging in this morning &amp; using the tool, so keeping eyes &amp; ears open for any feedback from DA... :eyes:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "partying_face", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "OQoVO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> is finally logging in this morning & using the tool, so keeping eyes & ears open for any feedback from DA... "}, {"type": "emoji", "name": "eyes", "unicode": "1f440"}]}]}]}, {"ts": "1708707482.086579", "text": "<PERSON><PERSON><PERSON> has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R06LCKHA0MQ", "block_id": "09/IW", "api_decoration_available": false, "call": {"v1": {"id": "R06LCKHA0MQ", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1708707481, "active_participants": [], "all_participants": [{"slack_id": "U065H3M6WJV"}, {"external_id": "33555456", "avatar_url": "", "display_name": "<PERSON><PERSON><PERSON> V"}, {"slack_id": "U04DS2MBWP4"}, {"slack_id": "U04DKEFP1K8"}], "display_id": "819-9232-0030", "join_url": "https://us06web.zoom.us/j/81992320030?pwd=SpkSaEC1JXr3R4a3JB20awO2WGSBF1.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEXzZkYjdkYmIzZTUzZDQ1YjNiNTk2NjZlZjZmZjU1Mjk0JnVzcz1qZ1phOWUwa2dLOXZBWGx0dGdTbk1SWDJmSTJBdmRtRHdkdlVUOEIwZUxFaTRKTzNIUlBHZlExajdsb2w3MUVTWERNRGw3QXAzWElfVFgzU1B5SWRoNlcyclFjNnlMNTVtTUdUT2Z3LjEyZ3FWdFR6TWxQWFBqR2c%3D&action=join&confno=81992320030&pwd=SpkSaEC1JXr3R4a3JB20awO2WGSBF1.1", "name": "Zoom meeting started by <PERSON>", "created_by": "U05185RFCNT", "date_end": 1708707961, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "4WWkc", "text": {"type": "mrkdwn", "text": "Meeting passcode: SpkSaEC1JXr3R4a3JB20awO2WGSBF1.1", "verbatim": false}}]}, {"ts": "1708701933.083029", "text": "<@U065H3M6WJV> <@U04DKEFP1K8> let me know if there are any progressupdate that I can communicate to <PERSON> on real time updates to the budgets", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1708701933.083029", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "ntgPG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " let me know if there are any progressupdate that I can communicate to <PERSON> on real time updates to the budgets"}]}]}]}, {"ts": "1708636322.709469", "text": "<@U065H3M6WJV> can we ask our future customers to find one manager from their team to participate and give us early feedback ( i am getting a feeling we will always get feedback from superadmins but it gets too late when the cycle has started and manager request updates to product at that moment)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1708636322.709469", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "s9r5y", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " can we ask our future customers to find one manager from their team to participate and give us early feedback ( i am getting a feeling we will always get feedback from superadmins but it gets too late when the cycle has started and manager request updates to product at that moment)"}]}]}]}], "created_at": "2025-05-22T21:35:34.580061"}