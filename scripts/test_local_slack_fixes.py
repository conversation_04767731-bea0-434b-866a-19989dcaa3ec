#!/usr/bin/env python
"""
Test script to validate the LocalSlackSourceInterface fixes and improvements.

This script tests the 500-token chunking strategy implementation and validates
that all bugs have been fixed.

Usage:
    python scripts/test_local_slack_fixes.py
"""

import os
import sys
import django
import logging
from datetime import datetime
from typing import Dict, Any, List

# Set up Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from apps.documents.interfaces.local_slack import LocalSlackSourceInterface, SlackMessage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_interface_initialization():
    """Test that the interface initializes correctly with various configurations."""
    print("\n🧪 TESTING INTERFACE INITIALIZATION")
    print("=" * 50)

    # Test 1: Valid configuration
    config = {
        "data_dir": "data/slack/",
        "channel": "C065QSSNH8A",
        "time_period": "monthly",
        "max_tokens": 500,
        "overlap_tokens": 50
    }

    try:
        interface = LocalSlackSourceInterface(config)
        print("✅ Interface initialized successfully with valid config")

        # Check that all required attributes are initialized
        required_attrs = [
            'data_dir', 'channel_id', 'user_cache', 'metadata',
            '_embedding_cache', 'cross_ref_min_similarity'
        ]

        for attr in required_attrs:
            if hasattr(interface, attr):
                print(f"✅ Attribute '{attr}' initialized")
            else:
                print(f"❌ Missing attribute '{attr}'")

    except Exception as e:
        print(f"❌ Interface initialization failed: {e}")
        return False

    # Test 2: Configuration with missing channel directory
    config_no_channel = {
        "data_dir": "nonexistent/path/",
        "channel": "C123456789"
    }

    try:
        interface = LocalSlackSourceInterface(config_no_channel)
        print("✅ Interface handles missing channel directory gracefully")
    except Exception as e:
        print(f"❌ Interface failed with missing directory: {e}")
        return False

    return True


def test_token_estimation():
    """Test the improved token estimation method."""
    print("\n🧪 TESTING TOKEN ESTIMATION")
    print("=" * 50)

    config = {"data_dir": "data/slack/"}
    interface = LocalSlackSourceInterface(config)

    test_cases = [
        ("", 0),  # Empty string
        ("Hello world", 2),  # Simple text
        ("Hello, world! How are you?", 6),  # With punctuation
        ("<@U123456> check this out: https://example.com", 8),  # With mentions and URLs
        ("```python\nprint('hello')\n```", 5),  # With code blocks
        ("This is a longer message with multiple sentences. It should have more tokens.", 14),
    ]

    for text, expected_min in test_cases:
        tokens = interface._estimate_tokens(text)
        print(f"Text: '{text[:30]}...' -> {tokens} tokens")

        if tokens >= expected_min:
            print(f"✅ Token estimation reasonable (>= {expected_min})")
        else:
            print(f"❌ Token estimation too low (< {expected_min})")

    return True


def test_overlap_content_creation():
    """Test the overlap content creation with edge cases."""
    print("\n🧪 TESTING OVERLAP CONTENT CREATION")
    print("=" * 50)

    config = {"data_dir": "data/slack/"}
    interface = LocalSlackSourceInterface(config)

    # Create test messages
    messages = [
        SlackMessage(
            text=f"Message {i}: This is test message number {i}",
            user=f"U{i:06d}",
            timestamp=str(1640000000 + i * 60),  # 1 minute apart
            user_name=f"User{i}"
        )
        for i in range(5)
    ]

    # Test normal overlap
    overlap = interface._create_overlap_content(messages, 50)
    print(f"✅ Normal overlap created: {len(overlap)} characters")

    # Test with zero overlap tokens
    overlap_zero = interface._create_overlap_content(messages, 0)
    print(f"✅ Zero overlap handled: '{overlap_zero}'")

    # Test with empty messages
    overlap_empty = interface._create_overlap_content([], 50)
    print(f"✅ Empty messages handled: '{overlap_empty}'")

    # Test with very large overlap request
    overlap_large = interface._create_overlap_content(messages, 10000)
    print(f"✅ Large overlap request handled: {len(overlap_large)} characters")

    return True


def test_document_creation_validation():
    """Test document creation with various edge cases."""
    print("\n🧪 TESTING DOCUMENT CREATION VALIDATION")
    print("=" * 50)

    config = {"data_dir": "data/slack/"}
    interface = LocalSlackSourceInterface(config)

    # Test 1: Empty messages list
    docs = interface._create_token_based_documents([], 500, 50)
    print(f"✅ Empty messages handled: {len(docs)} documents")

    # Test 2: Invalid token parameters
    test_messages = [
        SlackMessage(
            text="Test message",
            user="U123456",
            timestamp="1640000000",
            user_name="TestUser"
        )
    ]

    # Invalid max_tokens
    docs = interface._create_token_based_documents(test_messages, -100, 50)
    print(f"✅ Invalid max_tokens handled: {len(docs)} documents")

    # Invalid overlap_tokens
    docs = interface._create_token_based_documents(test_messages, 500, -50)
    print(f"✅ Invalid overlap_tokens handled: {len(docs)} documents")

    # Overlap >= max_tokens
    docs = interface._create_token_based_documents(test_messages, 100, 150)
    print(f"✅ Overlap >= max_tokens handled: {len(docs)} documents")

    return True


def test_configuration_validation():
    """Test the improved configuration validation."""
    print("\n🧪 TESTING CONFIGURATION VALIDATION")
    print("=" * 50)

    # Test with valid data directory (if exists)
    if os.path.exists("data/slack/"):
        config = {"data_dir": "data/slack/", "channel": "C065QSSNH8A"}
        interface = LocalSlackSourceInterface(config)

        is_valid = interface.validate_config()
        print(f"✅ Configuration validation result: {is_valid}")
    else:
        print("⚠️  No data directory found, skipping validation test")

    # Test with invalid data directory
    config_invalid = {"data_dir": "nonexistent/path/"}
    interface_invalid = LocalSlackSourceInterface(config_invalid)

    is_valid = interface_invalid.validate_config()
    print(f"✅ Invalid config handled: {is_valid}")

    return True


def test_error_handling():
    """Test error handling in various scenarios."""
    print("\n🧪 TESTING ERROR HANDLING")
    print("=" * 50)

    config = {"data_dir": "data/slack/"}
    interface = LocalSlackSourceInterface(config)

    # Test with malformed message (invalid timestamp)
    malformed_message = SlackMessage(
        text="Test message",
        user="U123456",
        timestamp="invalid_timestamp",
        user_name="TestUser"
    )

    try:
        docs = interface._create_token_based_documents([malformed_message], 500, 50)
        print(f"✅ Malformed message handled: {len(docs)} documents")
    except Exception as e:
        print(f"❌ Error handling malformed message: {e}")
        return False

    # Test message formatting with None values
    message_with_nones = SlackMessage(
        text=None,
        user=None,
        timestamp="1640000000",
        user_name=None
    )

    try:
        formatted = interface._format_message_with_context(message_with_nones)
        print(f"✅ None values handled in formatting")
    except Exception as e:
        print(f"❌ Error handling None values: {e}")
        return False

    return True


def main():
    """Run all tests."""
    print("🚀 TESTING LOCAL SLACK INTERFACE FIXES")
    print("=" * 70)

    tests = [
        test_interface_initialization,
        test_token_estimation,
        test_overlap_content_creation,
        test_document_creation_validation,
        test_configuration_validation,
        test_error_handling
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} failed")
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")

    print(f"\n📊 TEST RESULTS")
    print("=" * 30)
    print(f"Passed: {passed}/{total}")
    print(f"Success rate: {(passed/total)*100:.1f}%")

    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        return True
    else:
        print("⚠️  Some tests failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
