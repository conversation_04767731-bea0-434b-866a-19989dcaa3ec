{"date": "2024-07-18", "channel_id": "C065QSSNH8A", "message_count": 12, "messages": [{"ts": "1721325346.633659", "text": "<@U04DS2MBWP4> I will take a look at the data issues in demo ENV tomorrow. Looks like some data backfill is required. Would be more involved effort.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "mf+qx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I will take a look at the data issues in demo ENV tomorrow. Looks like some data backfill is required. Would be more involved effort."}]}]}]}, {"ts": "1721325246.446509", "text": "sequence of performance rating scale is not correct in the demo env", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721325246.446509", "reply_count": 1, "files": [{"id": "F07D2KXKVEX", "created": 1721325244, "timestamp": 1721325244, "name": "Screenshot 2024-07-18 at 10.53.15 AM.png", "title": "Screenshot 2024-07-18 at 10.53.15 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 96967, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07D2KXKVEX/screenshot_2024-07-18_at_10.53.15___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07D2KXKVEX/download/screenshot_2024-07-18_at_10.53.15___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_360.png", "thumb_360_w": 360, "thumb_360_h": 208, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_480.png", "thumb_480_w": 480, "thumb_480_h": 278, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_720.png", "thumb_720_w": 720, "thumb_720_h": 417, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_800.png", "thumb_800_w": 800, "thumb_800_h": 463, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_960.png", "thumb_960_w": 960, "thumb_960_h": 556, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07D2KXKVEX-c76ac910d0/screenshot_2024-07-18_at_10.53.15___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 593, "original_w": 1496, "original_h": 866, "thumb_tiny": "AwAbADDS2j0oCgdBQOp+tRXDEIMMRz2oQE2aKo73x95vzo8xv77fnVco7F7IoqiHb+8x/GjzG/vt+dHKFi6OCc9zTJV3rgYPNSUVIir5WODtz9acsJB5AqxgUYFVzAQGNR1C0vlD+6KmwKMClcD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07D2KXKVEX/screenshot_2024-07-18_at_10.53.15___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07D2KXKVEX-0accfc8160", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "5Aki7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "sequence of performance rating scale is not correct in the demo env"}]}]}]}, {"ts": "1721324812.418579", "text": "Also the flags shown on the org insights are not correct", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721324812.418579", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "uDcAj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also the flags shown on the org insights are not correct"}]}]}]}, {"ts": "1721324357.223289", "text": "prepping for demo with KornFerry. I am encountering bugs:\nFilters are not always consistent and show errors depending on the filtering sequence in org view\nIn Merit view, I can not filter by performance ratings.\nwill provide more update after the call", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1721324357.223289", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "FnJ9i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "prepping for demo with KornFerry. I am encountering bugs:\nFilters are not always consistent and show errors depending on the filtering sequence in org view\nIn Merit view, I can not filter by performance ratings.\nwill provide more update after the call"}]}]}]}, {"ts": "1721317669.598749", "text": "I have meeting at 9.30am PST. Will have to drop after first 30mnts of eng discussion.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "koS5L", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have meeting at 9.30am PST. Will have to drop after first 30mnts of eng discussion."}]}]}]}, {"ts": "1721301702.274019", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> UPDATE: Org Insights will be hidden in Production ENVs but not test ENVs", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721214828.916949", "subtype": "thread_broadcast", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "VSfwN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " UPDATE: Org Insights will be hidden in Production ENVs but not test ENVs"}]}]}]}, {"ts": "1721300623.625159", "text": "<@U04DKEFP1K8> <http://nauto.stridehr.io|nauto.stridehr.io> is ready", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721300623.625159", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "a01Qp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://nauto.stridehr.io", "text": "nauto.stridehr.io"}, {"type": "text", "text": " is ready"}]}]}]}, {"ts": "1721276112.898199", "text": "<@U0690EB5JE5> demo envrionment is the old staging env correct?", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BBA3u", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " demo envrionment is the old staging env correct?"}]}]}]}, {"ts": "1721255949.737039", "text": "Leta use demo environment ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VVQJ1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Leta use demo environment "}]}]}]}, {"ts": "1721255930.814579", "text": "which environment are we using for tomorrow's kornferry demo?", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "odUpB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "which environment are we using for tomorrow's kornferry demo?"}]}]}]}, {"ts": "1721255847.123569", "text": "we should cancel tomorrow's biweekly happy hour", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Qvbpx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we should cancel tomorrow's biweekly happy hour"}]}]}]}, {"ts": "1721246840.813619", "text": "saurab<PERSON>.jain has started a meeting", "user": "", "type": "message", "subtype": "bot_message", "blocks": [{"type": "call", "call_id": "R07CU7X1KN1", "block_id": "FyqBw", "api_decoration_available": false, "call": {"v1": {"id": "R07CU7X1KN1", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1721246840, "active_participants": [], "all_participants": [{"slack_id": "U04DKEFP1K8"}, {"slack_id": "U04DS2MBWP4"}], "display_id": "885-7086-2795", "join_url": "https://us06web.zoom.us/j/88570862795?pwd=tvAz6JiWB4iTzfYVgkLOIsVG9zJ4bk.1", "desktop_app_join_url": "zoommtg://us06web.zoom.us/join?confid=dXRpZD1VVElEXzg4Zjg0YWNlYzY0OTQ4YmViZmM1YWFlYjNhMTRkOTlhJnVzcz1yU3A1Y0cxdm9PUWdqOHZRYlpSYW5iR1F0UXVxMWxUQm5sU2xyYXBqZmNLa3VkZjgxSW5ERU80OTI2RDdwd0JXbVhkX1BTdU92WjJwTkNubXpOQ1BuQVFpMHFzNTRoWkM1RXVKdkNsSE12SzlCR3JLYk92UnFfcW54Mm5KZDNjYXozRU4xTzUzQTVGR1haRGlTQS43RC1HNEtGQW1IMjlBaVRw&action=join&confno=88570862795&pwd=tvAz6JiWB4iTzfYVgkLOIsVG9zJ4bk.1", "name": "Zoom meeting started by <PERSON><PERSON><PERSON><PERSON>", "created_by": "U05185RFCNT", "date_end": 1721247500, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}, {"type": "section", "block_id": "WUZQx", "text": {"type": "mrkdwn", "text": "Meeting passcode: tvAz6JiWB4iTzfYVgkLOIsVG9zJ4bk.1", "verbatim": false}}]}], "created_at": "2025-05-22T21:35:34.618490"}