{"date": "2024-03-14", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1710431447.090069", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> need a couple min break before we start the next call", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1Y+k0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " need a couple min break before we start the next call"}]}]}]}, {"ts": "1710371618.231909", "text": "I'm trying to test some things on sdf-test and ran into a bug where real-time budget updates are no longer working. <@U04DKEFP1K8> <@U0690EB5JE5> could this be related to any recent push to prod? <https://www.loom.com/share/fdcafcaa231846f2b37d97b12417a850?sid=0b9f81d0-8587-4928-942e-79cb6490888d>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710371618.231909", "reply_count": 10, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "D046G", "video_url": "https://www.loom.com/embed/fdcafcaa231846f2b37d97b12417a850?sid=0b9f81d0-8587-4928-942e-79cb6490888d&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/fdcafcaa231846f2b37d97b12417a850-4x3.jpg", "alt_text": "Bug - Budget error / real-time updates", "title": {"type": "plain_text", "text": "Bug - Budget error / real-time updates", "emoji": true}, "title_url": "https://www.loom.com/share/fdcafcaa231846f2b37d97b12417a850", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 49 sec  ", "emoji": true}}, {"type": "actions", "block_id": "OZLb5", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/fdcafcaa231846f2b37d97b12417a850?sid=0b9f81d0-8587-4928-942e-79cb6490888d"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"fdcafcaa231846f2b37d97b12417a850\",\"videoName\":\"Bug - Budget error / real-time updates\",\"sendWatchLaterReminderWeekdaysOnly\":true,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/fdcafcaa231846f2b37d97b12417a850?sid=0b9f81d0-8587-4928-942e-79cb6490888d", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "Pv05X", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'm trying to test some things on sdf-test and ran into a bug where real-time budget updates are no longer working. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " could this be related to any recent push to prod? "}, {"type": "link", "url": "https://www.loom.com/share/fdcafcaa231846f2b37d97b12417a850?sid=0b9f81d0-8587-4928-942e-79cb6490888d"}]}]}]}, {"ts": "1710366792.217609", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I've added a <https://docs.google.com/document/d/1sRICN3PCrHRh4Wgk2O1Qw5CF8tCYgBnwe5S_WBBgNQI/edit|suggested agenda> for our next eng discussion:\n• Updates on this week’s deliverables:\n    ◦ PR queue deployment\n    ◦ Project plan, including estimates for size of effort for top priorities\n• Discuss possible approach to balance different types of investment:\n    ◦ Engineering quality &amp; infra _[Ex: test framework; multi-tenancy]_\n    ◦ Core comp cycle work for beta customers _[Ex: Reports refresh, Cycle config, Merit view v2, People Insights]_\n    ◦ New features _[Ex: Salary Bands completion, Total Rewards]_\n• Customer enablement - current progress, demo?\nLet me know if there's anything else in addition or more urgent that we need to discuss.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710366792.217609", "reply_count": 7, "files": [{"id": "F06NT7KNA0N", "created": 1710190164, "timestamp": 1710190164, "name": "Engineering discussions", "title": "Engineering discussions", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1sRICN3PCrHRh4Wgk2O1Qw5CF8tCYgBnwe5S_WBBgNQI", "external_url": "https://docs.google.com/document/d/1sRICN3PCrHRh4Wgk2O1Qw5CF8tCYgBnwe5S_WBBgNQI/edit", "url_private": "https://docs.google.com/document/d/1sRICN3PCrHRh4Wgk2O1Qw5CF8tCYgBnwe5S_WBBgNQI/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06NT7KNA0N-1f7e564eef/engineering_discussions_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACXSOe1AJ9DSkZpnQ45/OgB+fajPtSAA9z+dLigAooooAM00jnNOPSo/woAkFFNGPSnZoAKKKKAEaj/PWloxQAmfp+dLzRiigAooooA//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06NT7KNA0N/engineering_discussions", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "gGrZ8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I've added a "}, {"type": "link", "url": "https://docs.google.com/document/d/1sRICN3PCrHRh4Wgk2O1Qw5CF8tCYgBnwe5S_WBBgNQI/edit", "text": "suggested agenda"}, {"type": "text", "text": " for our next eng discussion:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Updates on this week’s deliverables:"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "PR queue deployment"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Project plan, including estimates for size of effort for top priorities"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Discuss possible approach to balance different types of investment:"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Engineering quality & infra"}, {"type": "text", "text": " [Ex: test framework; multi-tenancy]", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Core comp cycle work for beta customers "}, {"type": "text", "text": "[Ex: Reports refresh, Cycle config, Merit view v2, People Insights]", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "New features "}, {"type": "text", "text": "[Ex: Salary Bands completion, Total Rewards]", "style": {"italic": true}}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Customer enablement - current progress, demo?"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nLet me know if there's anything else in addition or more urgent that we need to discuss."}]}]}]}, {"ts": "1710362404.100539", "text": "<@U065H3M6WJV> We need to add a cookie consent manager to our live website <http://compiify.com|compiify.com> I have put together a jira with initial functional requirements. Please review and provide your feedback. <@U04DS2MBWP4> i am assuming new website already has been designed to include this if not please include , this is a must have.\n\n<https://compiify.atlassian.net/browse/COM-2532>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710362404.100539", "reply_count": 12, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12593::df8323b0e17911ee970425a73efabb2c", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2532?atlOrigin=eyJpIjoiY2E1Y2EwYTAyMWMxNDFhZWFkZjg2NWVjMWFlNDAwYWMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2532 Cookie consent manager>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12593::df8323b2e17911ee970425a73efabb2c", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12593::df8323b1e17911ee970425a73efabb2c", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12593\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12593\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2532", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "/e7EG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " We need to add a cookie consent manager to our live website "}, {"type": "link", "url": "http://compiify.com", "text": "compiify.com"}, {"type": "text", "text": " I have put together a jira with initial functional requirements. Please review and provide your feedback. "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " i am assuming new website already has been designed to include this if not please include , this is a must have.\n\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2532"}]}]}]}], "created_at": "2025-05-22T21:35:34.609093"}