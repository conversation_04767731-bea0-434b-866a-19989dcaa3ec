{"thread_ts": "1739392066.630219", "channel_id": "C065QSSNH8A", "reply_count": 5, "replies": [{"ts": "1739413921.609239", "text": "Not sure. This only customer can confirm. ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739392066.630219", "blocks": [{"type": "rich_text", "block_id": "PqXKc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Not sure. This only customer can confirm. "}]}]}]}, {"ts": "1739414028.217789", "text": "Are you saying we need to test it to see if it’ll work? I’m asking a functional question about if the system will allow multiple ratings to have the same recommendation.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739392066.630219", "blocks": [{"type": "rich_text", "block_id": "EwYT9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Are you saying we need to test it to see if it’ll work? I’m asking a functional question about if the system will allow multiple ratings to have the same recommendation."}]}]}]}, {"ts": "1739414092.932639", "text": "Yeah it should, I don’t think system does any validation or restrict.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739392066.630219", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "/RjRm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yeah it should, I "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " think system does any validation or restrict."}]}]}]}, {"ts": "1739414108.688439", "text": "You can put anything ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739392066.630219", "blocks": [{"type": "rich_text", "block_id": "BTV6i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "You can put anything "}]}]}]}, {"ts": "1739417177.282489", "text": "Yes, it should work. We never had any restrictions around recommendations matrix", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1739392066.630219", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "CxuJM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, it should work. We never had any restrictions around recommendations matrix"}]}]}]}], "created_at": "2025-05-22T22:00:49.365171"}