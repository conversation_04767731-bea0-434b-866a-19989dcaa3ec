{"date": "2024-09-25", "channel_id": "C065QSSNH8A", "message_count": 26, "messages": [{"ts": "1727285599.335099", "text": "already done", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Z559a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "already done"}]}]}]}, {"ts": "1727285594.502109", "text": "<@U07EJ2LP44S> can we add their HR director Sara <mailto:<EMAIL>|<EMAIL>> to the call as well", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7kVk+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can we add their HR director Sara "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " to the call as well"}]}]}]}, {"ts": "1727285553.840359", "text": "<!here> <PERSON> reponded and wants to meet tomorrow", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "I8Gfi", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " <PERSON> reponded and wants to meet tomorrow"}]}]}]}, {"ts": "1727285254.254679", "text": "<@U07EJ2LP44S> test for scenario  NO.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727285254.254679", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "x4ocS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " test for scenario  NO."}]}]}]}, {"ts": "1727285218.275969", "text": "This is what we were seeing earlier, it is still broken (cannot proceed through to next step on proration page)", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07NLUYFR8F", "created": 1727285194, "timestamp": 1727285194, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 215928, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NLUYFR8F/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NLUYFR8F/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_360.png", "thumb_360_w": 360, "thumb_360_h": 263, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_480.png", "thumb_480_w": 480, "thumb_480_h": 351, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_720.png", "thumb_720_w": 720, "thumb_720_h": 526, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_800.png", "thumb_800_w": 800, "thumb_800_h": 584, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_960.png", "thumb_960_w": 960, "thumb_960_h": 701, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NLUYFR8F-386b0cfed1/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 748, "original_w": 2056, "original_h": 1502, "thumb_tiny": "AwAjADDSOc96AOetHeloAKKTHNLQAUUmKWgBKMCmsyg4LKD7mnUAHTtRmjFIAc9KAFzS5pMc0gGKAEaKNzlkUn1Ip+AOlFFABRRRQAUUUUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NLUYFR8F/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NLUYFR8F-e8b33e9e38", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "aGEPV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is what we were seeing earlier, it is still broken (cannot proceed through to next step on proration page)"}]}]}]}, {"ts": "1727285213.901829", "text": "<@U07EJ2LP44S> Please do  keep adding  ticket  under same epic.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727285213.901829", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "2GbFe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please do  keep adding  ticket  under same epic."}]}]}]}, {"ts": "1727285140.152719", "text": "These do not persist upon editing (should i put this feedback elsewhere?)", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07P40Q7L3E", "created": 1727285129, "timestamp": 1727285129, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 37677, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07P40Q7L3E/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07P40Q7L3E/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_360.png", "thumb_360_w": 360, "thumb_360_h": 101, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_480.png", "thumb_480_w": 480, "thumb_480_h": 134, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_720.png", "thumb_720_w": 720, "thumb_720_h": 202, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_800.png", "thumb_800_w": 800, "thumb_800_h": 224, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_960.png", "thumb_960_w": 960, "thumb_960_h": 269, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P40Q7L3E-b81234f08c/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 287, "original_w": 1186, "original_h": 332, "thumb_tiny": "AwANADDSOc//AFqTn1/SlPWjmgAH1oz9aKMUAGfrRS0lAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07P40Q7L3E/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07P40Q7L3E-6993ddfb68", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "7ZEXI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "These do not persist upon editing (should i put this feedback elsewhere?)"}]}]}]}, {"ts": "1727285032.167949", "text": "Does this also need to be hidden?", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07NUS9V45U", "created": 1727285025, "timestamp": 1727285025, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 101412, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NUS9V45U/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NUS9V45U/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_360.png", "thumb_360_w": 360, "thumb_360_h": 290, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_480.png", "thumb_480_w": 480, "thumb_480_h": 386, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_720.png", "thumb_720_w": 720, "thumb_720_h": 579, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_800.png", "thumb_800_w": 800, "thumb_800_h": 643, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_960.png", "thumb_960_w": 960, "thumb_960_h": 772, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NUS9V45U-2b05691cf0/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 824, "original_w": 1124, "original_h": 904, "thumb_tiny": "AwAmADDSOccH9M0mT6/+O0ppOf7woATJ9T/3zRk+v/jtOz7/AK0fjQADpS03/gQp2R60AIaTHsPypaWgBCPQD8qMD0FLTdv+c0ALgegpcD0pAMGloAjmi82PbuxzmoBYgY/eH8qt0U7sVkVXsg7s3mEZJPSrKjaoHoMUtFFwsFFFFIZ//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NUS9V45U/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NUS9V45U-b86296dcab", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "m5ob3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Does this also need to be hidden?"}]}]}]}, {"ts": "1727284926.245469", "text": "The one data thing (I think it's a data thing) we should fix in test &amp; demo is the country, AR still showing everything as 0 because no employees there, I think?", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "EwMsd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The one data thing (I think it's a data thing) we should fix in test & demo is the country, AR still showing everything as 0 because no employees there, I think?"}]}]}]}, {"ts": "1727284811.404999", "text": "Okay i ll check", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "E3MGE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Okay i ll check"}]}]}]}, {"ts": "1727284648.198849", "text": "<@U04DKEFP1K8> FYI... band assignment might be not done I think as the payzone identifiers were missing", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1E2v5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " FYI... band assignment might be not done I think as the payzone identifiers were missing"}]}]}]}, {"ts": "1727279429.617149", "text": "Running 15 min late to the standup", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0qOG5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Running 15 min late to the standup"}]}]}]}, {"ts": "1727278713.617619", "text": "I am trying few things ENV will be intermittently down for two hours.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1727278713.617619", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "8HCjs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am trying few things ENV will be intermittently down for two hours."}]}]}]}, {"ts": "1727278672.723239", "text": "<@U07EJ2LP44S> I fixed the cycle create issue on SDF-test but there is weird infra issue which I am looking into as of now.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "74hio", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I fixed the cycle create issue on SDF-test but there is weird infra issue which I am looking into as of now."}]}]}]}, {"ts": "**********.504509", "text": "<@U04DKEFP1K8> Can we pls replace <PERSON><PERSON>'s email (zoyaat stridehr) for test account with <mailto:zoya<PERSON><EMAIL>|<EMAIL>>, and <PERSON>'s email (vicky@stridehr) with <mailto:<EMAIL>|<EMAIL>>?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.504509", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "9hsos", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can we pls replace <PERSON><PERSON>'s email (zoyaat stridehr) for test account with "}, {"type": "link", "url": "mailto:z<PERSON><PERSON><PERSON>@gmail.com", "text": "<EMAIL>"}, {"type": "text", "text": ", and <PERSON>'s email (vicky@stridehr) with "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "**********.783259", "text": "<!here> Just FYI, we have to delete emails of <PERSON>, <PERSON><PERSON> and <PERSON> for SOC2 compliance purposes as they are external to the company. We will create external slack channels with them using their business/personal  emails for any communication/collaboration going forward.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "i97tm", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Just FYI, we have to delete emails of <PERSON>, <PERSON><PERSON> and <PERSON> for SOC2 compliance purposes as they are external to the company. We will create external slack channels with them using their business/personal  emails for any communication/collaboration going forward."}]}]}]}, {"ts": "1727213722.167339", "text": "Allocated budget page not loading:", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07NW3SV43C", "created": 1727213717, "timestamp": 1727213717, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 101420, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NW3SV43C/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NW3SV43C/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_360.png", "thumb_360_w": 360, "thumb_360_h": 153, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_480.png", "thumb_480_w": 480, "thumb_480_h": 204, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_720.png", "thumb_720_w": 720, "thumb_720_h": 306, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_800.png", "thumb_800_w": 800, "thumb_800_h": 340, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_960.png", "thumb_960_w": 960, "thumb_960_h": 408, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NW3SV43C-82d0ccd791/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 435, "original_w": 2770, "original_h": 1178, "thumb_tiny": "AwAUADDS5paKKADFFFFABRRRQAUUUUAFFFFABRRRQB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NW3SV43C/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NW3SV43C-02bf009d25", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "KKrtA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Allocated budget page not loading:"}]}]}]}, {"ts": "1727213570.127989", "text": "Also we have two equity pieces, and they behave slightly differently from each other. The second category showed no rating matrix until I chose which factor. The first one did not, it defaulted to showing the matrix.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "b/yvZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also we have two equity pieces, and they behave slightly differently from each other. The second category showed no rating matrix until I chose which factor. The first one did not, it defaulted to showing the matrix."}]}]}]}, {"ts": "1727213497.790369", "text": "Equity not allowing changes to the recommended % (defaults back to %0 if changed)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727213497.790369", "reply_count": 3, "files": [{"id": "F07NYHVCM0C", "created": 1727213494, "timestamp": 1727213494, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 151976, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NYHVCM0C/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NYHVCM0C/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_360.png", "thumb_360_w": 304, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_480.png", "thumb_480_w": 406, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_720.png", "thumb_720_w": 609, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_800.png", "thumb_800_w": 800, "thumb_800_h": 946, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_960.png", "thumb_960_w": 811, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYHVCM0C-ec7de6166a/image_1024.png", "thumb_1024_w": 866, "thumb_1024_h": 1024, "original_w": 1114, "original_h": 1318, "thumb_tiny": "AwAwACi/K5QAhXbn+GmxyM7YKSLxnJqUj2zSY/2T+dMQc/7VKBjuT9aNo9KTkHgUhjqKBRQAhpAPX+dKcd6AR2NACAeufzpcD3/Og0mB/k0AKAB6/nS0gpaAENLSGigAooooAUUUCigD/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NYHVCM0C/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NYHVCM0C-6d9f54b779", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "0ULDd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Equity not allowing changes to the recommended % (defaults back to %0 if changed)"}]}]}]}, {"ts": "1727213275.604379", "text": "I went back to beginnning and ran through again and it looks normal", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F07PJS27AF2", "created": 1727213273, "timestamp": 1727213273, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 117992, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07PJS27AF2/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07PJS27AF2/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_360.png", "thumb_360_w": 360, "thumb_360_h": 140, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_480.png", "thumb_480_w": 480, "thumb_480_h": 187, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_720.png", "thumb_720_w": 720, "thumb_720_h": 280, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_800.png", "thumb_800_w": 800, "thumb_800_h": 311, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_960.png", "thumb_960_w": 960, "thumb_960_h": 373, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PJS27AF2-c5e1e948ef/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 398, "original_w": 2006, "original_h": 780, "thumb_tiny": "AwASADDSxRgelRfaIQSN4zT0dZFyhBHSnYLjunajNGKAuKQBk+lLSYoAxQAYHoKXGOlFFABRRRQAUUUUAf/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07PJS27AF2/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07PJS27AF2-aa21147faf", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Lssl8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I went back to beginnning and ran through again and it looks normal"}]}]}]}, {"ts": "1727213190.201969", "text": "After selecting 'no' for combined budgets", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07NYH54THS", "created": 1727213182, "timestamp": 1727213182, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 52700, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NYH54THS/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NYH54THS/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_360.png", "thumb_360_w": 360, "thumb_360_h": 229, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_480.png", "thumb_480_w": 480, "thumb_480_h": 306, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_720.png", "thumb_720_w": 720, "thumb_720_h": 458, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_800.png", "thumb_800_w": 800, "thumb_800_h": 509, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NYH54THS-2b872fe82b/image_960.png", "thumb_960_w": 960, "thumb_960_h": 611, "original_w": 996, "original_h": 634, "thumb_tiny": "AwAeADDS59f0pCcDO7j6UvNHPegBOT/F+lLg+v6UUE4FAAAc9f0paZuPr+lPoAaT74pR9c1XmuvKkCbM8DnOKYt9k48vsT96nZiui5SVTF98wHl9f9qrtDVgTuJS0UUhn//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NYH54THS/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NYH54THS-e178646658", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Is3Yn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "After selecting 'no' for combined budgets"}]}]}]}, {"ts": "1727212965.916419", "text": "Looks ok but flashed up an error", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qfx2X", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Looks ok but flashed up an error"}]}]}]}, {"ts": "1727212960.308939", "text": "<@U04DKEFP1K8> Eligibility Rule page", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727212960.308939", "reply_count": 2, "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F07NFF8RFJB", "created": 1727212957, "timestamp": 1727212957, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 37426, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07NFF8RFJB/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07NFF8RFJB/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NFF8RFJB-d6db756e4d/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NFF8RFJB-d6db756e4d/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NFF8RFJB-d6db756e4d/image_360.png", "thumb_360_w": 360, "thumb_360_h": 138, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NFF8RFJB-d6db756e4d/image_480.png", "thumb_480_w": 480, "thumb_480_h": 184, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NFF8RFJB-d6db756e4d/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07NFF8RFJB-d6db756e4d/image_720.png", "thumb_720_w": 720, "thumb_720_h": 276, "original_w": 798, "original_h": 306, "thumb_tiny": "AwASADDSbdg7QCfekJOe/wCVOooAaN3TJ/Klwf736UtFAB2prfdNOprnCE4oAdRRRQAUUUUAFI33TS0jfdNAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07NFF8RFJB/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07NFF8RFJB-4904e006a6", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Hjs2F", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eligibility Rule page"}]}]}]}, {"ts": "1727212371.953819", "text": "Do I need to use the old one?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727212371.953819", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "N6JQB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do I need to use the old one?"}]}]}]}, {"ts": "1727212351.675919", "text": "For purposes of demo", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ddHFm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For purposes of demo"}]}]}]}, {"ts": "1727212344.870069", "text": "I am trying to create a new test cycle in SDF test, and it will not allow it.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1727212344.870069", "reply_count": 4, "files": [{"id": "F07P8LP40SD", "created": 1727212343, "timestamp": 1727212343, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 95252, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07P8LP40SD/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07P8LP40SD/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_360.png", "thumb_360_w": 360, "thumb_360_h": 168, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_480.png", "thumb_480_w": 480, "thumb_480_h": 224, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_720.png", "thumb_720_w": 720, "thumb_720_h": 335, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07P8LP40SD-e560b1e382/image_800.png", "thumb_800_w": 800, "thumb_800_h": 373, "original_w": 906, "original_h": 422, "thumb_tiny": "AwAWADDSJPGBnnn2pMn/ACKUkdzUE8LyPlJdoxjGTQgZOMn0/Kjn2qp9mm/57fqf8aPs03/Pb9T/AI07IV2W8c9aaqrzwKighkjk3PJuGMYyamQDrjmkxoUgHGaAoHSl7iigBKO9FHcUAHGaRKXvSJQB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07P8LP40SD/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07P8LP40SD-3ef91f244b", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "KcM7x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am trying to create a new test cycle in SDF test, and it will not allow it."}]}]}]}], "created_at": "2025-05-22T21:35:34.635586"}