#!/usr/bin/env python3
"""
Analyze search issues and data quality for specific queries.
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.documents.models import DocumentChunk, RawDocument, EmbeddingMetadata
from apps.accounts.models import UserPlatformProfile
from apps.search.models import SearchResult, ResultCitation

def analyze_amanda_data():
    """Analyze data related to <PERSON>."""
    print("🔍 Analyzing Amanda-related Data")
    print("=" * 50)

    # Check for Amanda in platform profiles
    amanda_profiles = UserPlatformProfile.objects.filter(
        display_name__icontains='amanda'
    )
    print(f"📊 Amanda platform profiles: {amanda_profiles.count()}")
    for profile in amanda_profiles:
        print(f"   - {profile.display_name} ({profile.platform}) - ID: {profile.platform_user_id}")

    # Check for Amanda in document chunks
    amanda_chunks = DocumentChunk.objects.filter(
        text__icontains='amanda'
    )
    print(f"📊 Chunks mentioning Amanda: {amanda_chunks.count()}")

    # Show sample Amanda chunks
    for i, chunk in enumerate(amanda_chunks[:5], 1):
        print(f"\n📄 Amanda Chunk {i}:")
        print(f"   Document: {chunk.document.title}")
        print(f"   Source: {chunk.document.source.source_type}")
        print(f"   Profile: {chunk.profile.display_name if chunk.profile else 'None'}")
        print(f"   Text preview: {chunk.text[:200]}...")

        # Check if this chunk has embedding metadata
        embedding = EmbeddingMetadata.objects.filter(chunk=chunk).first()
        if embedding:
            print(f"   ✅ Has embedding: {embedding.vector_id}")
        else:
            print(f"   ❌ No embedding found")

def analyze_curana_data():
    """Analyze data related to Curana."""
    print("\n🔍 Analyzing Curana-related Data")
    print("=" * 50)

    # Check for Curana in document chunks
    curana_chunks = DocumentChunk.objects.filter(
        text__icontains='curana'
    )
    print(f"📊 Chunks mentioning Curana: {curana_chunks.count()}")

    # Group by source type
    source_types = {}
    for chunk in curana_chunks:
        source_type = chunk.document.source.source_type
        if source_type not in source_types:
            source_types[source_type] = 0
        source_types[source_type] += 1

    print(f"📊 Curana chunks by source type:")
    for source_type, count in source_types.items():
        print(f"   - {source_type}: {count} chunks")

    # Show sample Curana chunks with issues
    print(f"\n📄 Sample Curana chunks with 'issue' or 'problem':")
    issue_chunks = curana_chunks.filter(
        text__iregex=r'(issue|problem|bug|error|fail)'
    )
    print(f"📊 Curana issue-related chunks: {issue_chunks.count()}")

    for i, chunk in enumerate(issue_chunks[:3], 1):
        print(f"\n📄 Curana Issue Chunk {i}:")
        print(f"   Document: {chunk.document.title}")
        print(f"   Profile: {chunk.profile.display_name if chunk.profile else 'None'}")
        print(f"   Text preview: {chunk.text[:300]}...")

def analyze_citation_data():
    """Analyze citation data and permalinks."""
    print("\n🔍 Analyzing Citation Data")
    print("=" * 50)

    # Check recent search results
    recent_results = SearchResult.objects.order_by('-timestamp')[:5]
    print(f"📊 Recent search results: {recent_results.count()}")

    for i, result in enumerate(recent_results, 1):
        print(f"\n🔍 Search Result {i}:")
        print(f"   Query: {result.search_query.query_text}")
        print(f"   Citations: {result.citations.count()}")

        citations = result.citations.all()
        for j, citation in enumerate(citations, 1):
            chunk = citation.document_chunk
            document = chunk.document
            print(f"   Citation {j}:")
            print(f"     - Chunk ID: {chunk.id}")
            print(f"     - Document: {document.title}")
            print(f"     - Source: {document.source.source_type}")
            print(f"     - Permalink: {document.permalink}")
            print(f"     - Profile: {chunk.profile.display_name if chunk.profile else 'None'}")

def test_specific_queries():
    """Test specific problematic queries."""
    print("\n🧪 Testing Specific Queries")
    print("=" * 50)

    from apps.accounts.models import User
    from apps.search.services.rag_service import RAGService

    # Get user
    user = User.objects.filter(email='<EMAIL>').first()
    if not user:
        print("❌ User not found")
        return

    # Initialize RAG service
    rag_service = RAGService(user=user, tenant_slug='stride')

    test_queries = [
        "List issues reported by Amanda",
        "Summarize issues reported about curana",
        "What problems has Amanda mentioned?"
    ]

    for query in test_queries:
        print(f"\n🔍 Testing query: '{query}'")
        try:
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=10,
                min_relevance_score=0.2  # Lower threshold for testing
            )

            print(f"   ✅ Search completed")
            print(f"   Results: {len(retrieved_docs)} documents")
            print(f"   Citations: {search_result.citations.count()}")
            print(f"   Answer length: {len(search_result.generated_answer)}")

            # Show citations
            citations = search_result.citations.all()
            for i, citation in enumerate(citations[:3], 1):
                chunk = citation.document_chunk
                print(f"   Citation {i}: {chunk.document.title} (score: {citation.relevance_score})")

        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    analyze_amanda_data()
    analyze_curana_data()
    analyze_citation_data()
    test_specific_queries()
