{"date": "2024-03-13", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "1710347819.462509", "text": "<@U04DS2MBWP4> *SDF* was originally supposed to close their cycle 2/27, they officially delayed it to 3/1, but unofficially were still making changes in the last week. <PERSON> indicated that managers were sharing raises with employees this week (probably because pay effective date was still 3/1 and they wanted to inform before payroll shows up). They opted not to use our adjustment letters, and I'm waiting to see what else <PERSON> will need to close everything out.\n\n*DA* may still be waiting on their comp committee, they originally had a deadline of 3/15 for that and their pay effective date is 4/1.", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TnLSZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "text", "text": "SDF ", "style": {"bold": true}}, {"type": "text", "text": "was originally supposed to close their cycle 2/27, they officially delayed it to 3/1, but unofficially were still making changes in the last week. <PERSON> indicated that managers were sharing raises with employees this week (probably because pay effective date was still 3/1 and they wanted to inform before payroll shows up). They opted not to use our adjustment letters, and I'm waiting to see what else <PERSON> will need to close everything out.\n\n"}, {"type": "text", "text": "DA ", "style": {"bold": true}}, {"type": "text", "text": "may still be waiting on their comp committee, they originally had a deadline of 3/15 for that and their pay effective date is 4/1."}]}]}]}, {"ts": "1710347234.037989", "text": "and for DA", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9nUMj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "and for DA"}]}]}]}, {"ts": "1710347224.867799", "text": "<@U065H3M6WJV> what's the expected cycle closing date for SDF?", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "72hhd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " what's the expected cycle closing date for SDF?"}]}]}]}, {"ts": "1710287300.356659", "text": "<@U065H3M6WJV>\n1. Fixes for <https://compiify.atlassian.net/browse/COM-2509> is deployed on sdf-test and are ready for deployment on sdf prod. Okay to deploy fixes later tonight on production?\n2. Started documenting managing precision digits for usd conversion and storing decimal digits here <https://compiify.atlassian.net/browse/COM-2520>\n3. No updates today on setting up DA's templates on <http://qa.compiify.com|qa.compiify.com>, will target this activity for tomorrow", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710287300.356659", "reply_count": 4, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12570::020d19e0e0cb11eeba9c5bc0aa480443", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2509?atlOrigin=eyJpIjoiNWRhZGFmZDMwOWMyNDQwYzg1ZDEzNDUzYWYyYzkwNWQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2509 Approving a leader hierarchy is not allowed if an ineligible employ…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12570::020d19e4e0cb11eeba9c5bc0aa480443", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/79ad6fec1eb23766c2c2c46cb17078ab?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12570::020d19e1e0cb11eeba9c5bc0aa480443", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12570\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12570\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2509", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:12581::020d19e2e0cb11eeba9c5bc0aa480443", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2520?atlOrigin=eyJpIjoiMTNjYmE3M2Q2YzdjNDU4N2E0NzYwNTVmN2YzOWIxNDMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2520 Managing currency conversion and decimal digits in the product>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12581::020d19e5e0cb11eeba9c5bc0aa480443", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12581::020d19e3e0cb11eeba9c5bc0aa480443", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12581\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12581\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2520", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "i15P9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Fixes for "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2509"}, {"type": "text", "text": " is deployed on sdf-test and are ready for deployment on sdf prod. Okay to deploy fixes later tonight on production?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Started documenting managing precision digits for usd conversion and storing decimal digits here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2520"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "No updates today on setting up DA's templates on "}, {"type": "link", "url": "http://qa.compiify.com", "text": "qa.compiify.com"}, {"type": "text", "text": ", will target this activity for tomorrow"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1710285886.251609", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> Will the <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities board> get updated after the PR queue is completed / merged? I'm having a hard time keeping track of what's done vs pending using the current view.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710285886.251609", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "ECgYS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Will the "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities board"}, {"type": "text", "text": " get updated after the PR queue is completed / merged? I'm having a hard time keeping track of what's done vs pending using the current view."}]}]}]}, {"ts": "1710283066.854589", "text": "<@U04DS2MBWP4> Instead of manually provided link to soc2 document, <PERSON><PERSON> provides a trust center, link to it can be provided on compiify's website (needs configuration to make it publicly accessible) <https://app.vanta.com/trust-center/view> ( you can preview it here with your google login currently ). I will bring it up in next meeting with Agency and provide remaining details.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1710283066.854589", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "/ZC6+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Instead of manually provided link to soc2 document, <PERSON><PERSON> provides a trust center, link to it can be provided on compiify's website (needs configuration to make it publicly accessible) "}, {"type": "link", "url": "https://app.vanta.com/trust-center/view"}, {"type": "text", "text": " ( you can preview it here with your google login currently ). I will bring it up in next meeting with Agency and provide remaining details."}]}]}]}, {"ts": "1710268843.321769", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> Starting a doc for the <https://docs.google.com/document/d/1Mk7miV5SiW6S3IQ5nAM188oT5oqjW9JYcV1klrjVEi4/edit?usp=sharing|Social login requirements here>.\n\n<PERSON><PERSON><PERSON><PERSON> - I will need some help from you understanding how we currently set up the initial Compiify admin and/or customer admin access, and how that might need to change (or not).", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710268843.321769", "reply_count": 4, "files": [{"id": "F06P6829GMQ", "created": 1710268845, "timestamp": 1710268845, "name": "Product Requirements for \"Social\" login", "title": "Product Requirements for \"Social\" login", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1Mk7miV5SiW6S3IQ5nAM188oT5oqjW9JYcV1klrjVEi4", "external_url": "https://docs.google.com/document/d/1Mk7miV5SiW6S3IQ5nAM188oT5oqjW9JYcV1klrjVEi4/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1Mk7miV5SiW6S3IQ5nAM188oT5oqjW9JYcV1klrjVEi4/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06P6829GMQ-52253774b6/product_requirements_for__social__login_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSYkdAPzpA3rSsMim4YUAO3CjcKTa3rRtPr+tADqKB0ooARvqR9KQEDuT+FKwzSBfWgB1FJtFG0elAC0UUUANYcjkUmPcU5vofwpAM+ooAAp9aXB9f0owKMCgBaKKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06P6829GMQ/product_requirements_for__social__login", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Tkrjp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Starting a doc for the "}, {"type": "link", "url": "https://docs.google.com/document/d/1Mk7miV5SiW6S3IQ5nAM188oT5oqjW9JYcV1klrjVEi4/edit?usp=sharing", "text": "Social login requirements here"}, {"type": "text", "text": ".\n\n<PERSON><PERSON><PERSON><PERSON> - I will need some help from you understanding how we currently set up the initial Compiify admin and/or customer admin access, and how that might need to change (or not)."}]}]}]}], "created_at": "2025-05-22T21:35:34.609843"}