{"date": "2024-03-25", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "**********.238129", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> PlayQ data: See the 2 \"Updated\" xlsx files in <https://drive.google.com/drive/u/0/folders/1CDVw5sS2KRsy9sULCIpY1Oss-Qu47ywR|this folder>.\n\n• We will still need to fill in placeholder data for employee names (for example, using \"Employee [IDNumber]\" format)\n• Employee &amp; salary band data should now use consistent categories for regions\nLet me know if we're able to load their account with these updated files! :pray:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.238129", "reply_count": 9, "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "g+QvM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " PlayQ data: See the 2 \"Updated\" xlsx files in "}, {"type": "link", "url": "https://drive.google.com/drive/u/0/folders/1CDVw5sS2KRsy9sULCIpY1Oss-Qu47ywR", "text": "this folder"}, {"type": "text", "text": ".\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We will still need to fill in placeholder data for employee names (for example, using \"Employee [IDNumber]\" format)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Employee & salary band data should now use consistent categories for regions"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nLet me know if we're able to load their account with these updated files! "}, {"type": "emoji", "name": "pray", "unicode": "1f64f"}]}]}]}, {"ts": "**********.028219", "text": "Hey <@U04DKEFP1K8> <@U0690EB5JE5>, I'm still seeing some examples where engineers are using customer data sets in their environments/demos. <https://www.loom.com/share/a9d968b2a3044c7b917671e6cea37f91|Here's an example> where the visible data is SDF's employees.\n\nCan we make sure all engineers are working off of artificial data sets on their local environments? (I don't think this example came from production, right?)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.028219", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Ps7If", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": ", I'm still seeing some examples where engineers are using customer data sets in their environments/demos. "}, {"type": "link", "url": "https://www.loom.com/share/a9d968b2a3044c7b917671e6cea37f91", "text": "Here's an example"}, {"type": "text", "text": " where the visible data is SDF's employees.\n\nCan we make sure all engineers are working off of artificial data sets on their local environments? (I don't think this example came from production, right?)"}]}]}]}], "created_at": "2025-05-22T21:35:34.606573"}