{"date": "2024-05-03", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1714740610.381389", "text": "<@U065H3M6WJV> <@U04DS2MBWP4>\n\nHere are links to loom video prepared by engineering team for work completed on two features\n1. Revised Salary Bands:  <https://www.loom.com/share/a6504552a03b40d4a88702f7d7dc8b03>\n2. Enable data edit: <https://www.loom.com/share/bf4cd223646f4fc2947ace93227ec918>\nBoth of these features are deployed and available to view on all sandboxes for upcoming customers as well <http://sdf-test.compiify.com|sdf-test.compiify.com> and <http://test.compiify.com|test.compiify.com>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1714740610.381389", "reply_count": 14, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "iWoI9", "video_url": "https://www.loom.com/embed/bf4cd223646f4fc2947ace93227ec918?unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/bf4cd223646f4fc2947ace93227ec918-4x3.jpg", "alt_text": "Loom Message - 3 May 2024", "title": {"type": "plain_text", "text": "Loom Message - 3 May 2024", "emoji": true}, "title_url": "https://www.loom.com/share/bf4cd223646f4fc2947ace93227ec918", "author_name": "<PERSON><PERSON><PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 2 min  👀 2 views  ", "emoji": true}}, {"type": "actions", "block_id": "0WQzg", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/bf4cd223646f4fc2947ace93227ec918"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"bf4cd223646f4fc2947ace93227ec918\",\"videoName\":\"Loom Message - 3 May 2024\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/bf4cd223646f4fc2947ace93227ec918", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "A/75b", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": "\n\nHere are links to loom video prepared by engineering team for work completed on two features\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Revised Salary Bands:  "}, {"type": "link", "url": "https://www.loom.com/share/a6504552a03b40d4a88702f7d7dc8b03"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Enable data edit: "}, {"type": "link", "url": "https://www.loom.com/share/bf4cd223646f4fc2947ace93227ec918"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Both of these features are deployed and available to view on all sandboxes for upcoming customers as well "}, {"type": "link", "url": "http://sdf-test.compiify.com", "text": "sdf-test.compiify.com"}, {"type": "text", "text": " and "}, {"type": "link", "url": "http://test.compiify.com", "text": "test.compiify.com"}]}]}]}], "created_at": "2025-05-22T21:35:34.601368"}