#!/usr/bin/env python3
"""
Test script to simulate the exact UI flow and validate what users actually see.
"""

import os
import sys
import django
import time
from django.test import Client
from django.contrib.auth.models import User

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()


def test_ui_search_flow():
    """Test the exact UI search flow that users experience."""
    print("🌐 Testing UI Search Flow - Simulating Real User Experience")
    print("=" * 70)
    
    # Create a test client (simulates browser requests)
    client = Client()
    
    # Step 1: Get the user
    try:
        user = User.objects.get(email='<EMAIL>')
        print(f"✅ Found user: {user.email}")
    except User.DoesNotExist:
        print("❌ User not found")
        return False
    
    # Step 2: Login (simulate UI login)
    login_response = client.login(username=user.username, password='admin123')
    if not login_response:
        print("❌ Login failed")
        return False
    print("✅ Login successful")
    
    # Step 3: Get the search page (simulate navigating to search)
    search_page_response = client.get('/search/')
    if search_page_response.status_code != 200:
        print(f"❌ Search page failed: {search_page_response.status_code}")
        return False
    print("✅ Search page loaded")
    
    # Step 4: Submit search query (simulate form submission)
    query = "list issues reported on Curana"
    search_data = {
        'query': query,
        'use_hybrid_search': 'true',
        'use_context_aware': 'true',  # This should use our enhanced prompt templates
        'use_query_expansion': 'false',
        'use_multi_step_reasoning': 'false'
    }
    
    print(f"🔍 Submitting query: {query}")
    print(f"📋 Search options: {search_data}")
    
    start_time = time.time()
    search_response = client.post('/search/query/', data=search_data)
    processing_time = time.time() - start_time
    
    print(f"⏱️  Processing time: {processing_time:.2f}s")
    print(f"📊 Response status: {search_response.status_code}")
    
    if search_response.status_code != 200:
        print(f"❌ Search failed with status {search_response.status_code}")
        print(f"Response content: {search_response.content.decode()[:500]}")
        return False
    
    # Step 5: Analyze the actual HTML response that users see
    response_html = search_response.content.decode()
    
    # Extract the response content from the HTML
    import re
    
    # Look for the response content in the HTML
    response_pattern = r'<div class="response-content">(.*?)</div>'
    response_match = re.search(response_pattern, response_html, re.DOTALL)
    
    if not response_match:
        # Try alternative patterns
        response_pattern = r'<div[^>]*class="[^"]*response-content[^"]*"[^>]*>(.*?)</div>'
        response_match = re.search(response_pattern, response_html, re.DOTALL)
    
    if not response_match:
        # Try to find any response content
        response_pattern = r'<div[^>]*id="response-text"[^>]*>(.*?)</div>'
        response_match = re.search(response_pattern, response_html, re.DOTALL)
    
    if response_match:
        actual_response = response_match.group(1).strip()
        print("✅ Found response content in HTML")
    else:
        print("❌ Could not find response content in HTML")
        # Let's see what's in the HTML
        print("🔍 HTML content preview:")
        print(response_html[:1000])
        return False
    
    # Step 6: Analyze the response quality
    print(f"\n📝 Actual UI Response Analysis:")
    print(f"📊 Response length: {len(actual_response)} characters")
    
    # Check for detailed formatting indicators
    has_bullets = '•' in actual_response or '<li>' in actual_response or '&bull;' in actual_response
    has_bold_dates = '<strong' in actual_response and ('February' in actual_response or 'March' in actual_response)
    has_structure = 'Issues reported by' in actual_response or 'Issues Found:' in actual_response
    has_detailed_descriptions = len(actual_response) > 1000
    has_citations = '[' in actual_response and ']' in actual_response
    
    print(f"\n🎯 Quality Indicators:")
    print(f"✅ Has bullet points: {has_bullets}")
    print(f"✅ Has bold dates: {has_bold_dates}")
    print(f"✅ Has structure: {has_structure}")
    print(f"✅ Has detailed descriptions: {has_detailed_descriptions}")
    print(f"✅ Has citations: {has_citations}")
    
    # Calculate quality score
    quality_indicators = [has_bullets, has_bold_dates, has_structure, has_detailed_descriptions, has_citations]
    quality_score = (sum(quality_indicators) / len(quality_indicators)) * 100
    
    print(f"\n📈 Overall Quality Score: {quality_score:.0f}%")
    
    # Show actual response preview
    print(f"\n📄 Actual UI Response Preview (first 800 chars):")
    print("=" * 60)
    print(actual_response[:800])
    print("=" * 60)
    
    # Check for citations in the HTML
    citation_pattern = r'<div[^>]*class="[^"]*source-card[^"]*"'
    citations = re.findall(citation_pattern, response_html)
    print(f"\n🔗 Citations found: {len(citations)}")
    
    # Overall assessment
    if quality_score >= 80:
        print("\n🎉 EXCELLENT: UI is showing detailed, comprehensive responses!")
        return True
    elif quality_score >= 60:
        print("\n👍 GOOD: UI shows improved responses but could be better")
        return True
    else:
        print("\n⚠️  PROBLEM: UI is still showing basic/poor quality responses")
        print("🔧 The backend improvements are not reaching the UI properly")
        return False


def main():
    """Main test function."""
    print("🚀 UI Flow Validation Test")
    print("Testing what users actually see in the web interface")
    print()
    
    success = test_ui_search_flow()
    
    if success:
        print(f"\n✅ UI Test PASSED!")
        print(f"🎯 Users should now see detailed, comprehensive responses")
    else:
        print(f"\n❌ UI Test FAILED!")
        print(f"🔧 There's still a disconnect between backend and UI")
        print(f"💡 Need to investigate further...")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
