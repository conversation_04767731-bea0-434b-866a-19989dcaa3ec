{"date": "2024-12-19", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1734631941.726549", "text": "<@U07EJ2LP44S> Dan is actually available today to meet. I added you to the invite at 2 pm pst today", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "M9Efp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Dan is actually available today to meet. I added you to the invite at 2 pm pst today"}]}]}]}, {"ts": "1734617693.076909", "text": "I actually think that verbiage makes total sense. Ideally, we would have this be sortable and filterable.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734617693.076909", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "XnWLf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I actually think that verbiage makes total sense. Ideally, we would have this be sortable and filterable."}]}]}]}, {"ts": "1734606627.678479", "text": "<@U07EJ2LP44S> Need some help on finalizing the verbiage for <https://compiify.atlassian.net/browse/COM-4027|COM-4027>:\nAlso, for <https://compiify.atlassian.net/browse/COM-4021|COM-4021>, I was thinking that sorting this column can serve our purpose. Or do we only want filtering?\n\nCC: <@U0690EB5JE5>", "user": "U07MH77PUBV", "type": "message", "files": [{"id": "F085U21Q599", "created": 1734606481, "timestamp": 1734606481, "name": "Screenshot 2024-12-19 at 1.15.23 PM.png", "title": "Screenshot 2024-12-19 at 1.15.23 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07MH77PUBV", "user_team": "T04DM97F1UM", "editable": false, "size": 134591, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F085U21Q599/screenshot_2024-12-19_at_1.15.23___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F085U21Q599/download/screenshot_2024-12-19_at_1.15.23___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 297, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 396, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 595, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 661, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 793, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 846, "original_w": 1166, "original_h": 963, "thumb_tiny": "AwAnADDRxkfMB1+tAVfSgDCnnPNKDQAg2g8D9KdTTwf/AK9Nd1Xhsj86AJKKRcEDGaNvufzoAB0pcUCigBD1pMH2pT1oGPQUAAB9aWjiigAFFAooAacZo+X0FB60lA7C/L6CnCmU5elAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F085U21Q599/screenshot_2024-12-19_at_1.15.23___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F085U21Q599-7607a47d81", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "KvSSc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Need some help on finalizing the verbiage for "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4027", "text": "COM-4027"}, {"type": "text", "text": ":\nAlso, for "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4021", "text": "COM-4021"}, {"type": "text", "text": ", I was thinking that sorting this column can serve our purpose. Or do we only want filtering?\n\nCC: "}, {"type": "user", "user_id": "U0690EB5JE5"}]}]}]}], "created_at": "2025-05-22T21:35:34.677084"}