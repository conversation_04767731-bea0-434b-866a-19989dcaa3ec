{"date": "2024-06-13", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1718303116.744669", "text": "Work is already in progress to implement equity toggle ( eng will extend it to ensure it is implemented across diff views)", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "w4qjq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Work is already in progress to implement equity toggle ( eng will extend it to ensure it is implemented across diff views)"}]}]}]}, {"ts": "1718303027.547719", "text": "For setting equity conversion rate via UI please raise a jira", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "6V4DJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For setting equity conversion rate via UI please raise a jira"}]}]}]}, {"ts": "1718302897.959479", "text": "I'm seeing some changes in the equity features in `new-meritview` but it's clear that not everything has been connected (Units toggle doesn't appear in all views, Equity conversion rate is not applied from Settings). Should I file bugs on this or wait if engineering is still working on it? <@U04DKEFP1K8> <@U0690EB5JE5>", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5X/Ub", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'm seeing some changes in the equity features in "}, {"type": "text", "text": "new-meritview", "style": {"code": true}}, {"type": "text", "text": " but it's clear that not everything has been connected (Units toggle doesn't appear in all views, Equity conversion rate is not applied from Settings). Should I file bugs on this or wait if engineering is still working on it? "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}]}]}]}], "created_at": "2025-05-22T21:35:34.625104"}