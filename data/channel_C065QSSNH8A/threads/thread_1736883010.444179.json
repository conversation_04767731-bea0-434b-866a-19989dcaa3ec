{"thread_ts": "**********.444179", "channel_id": "C065QSSNH8A", "reply_count": 5, "replies": [{"ts": "1736883063.930749", "text": "Same for what defaults in the comp planner:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.444179", "files": [{"id": "F088F819KML", "created": 1736883059, "timestamp": 1736883059, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 228715, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F088F819KML/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F088F819KML/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F088F819KML-7e61f0f94b/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F088F819KML-7e61f0f94b/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F088F819KML-7e61f0f94b/image_360.png", "thumb_360_w": 360, "thumb_360_h": 177, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F088F819KML-7e61f0f94b/image_480.png", "thumb_480_w": 480, "thumb_480_h": 236, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F088F819KML-7e61f0f94b/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F088F819KML-7e61f0f94b/image_720.png", "thumb_720_w": 720, "thumb_720_h": 353, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F088F819KML-7e61f0f94b/image_800.png", "thumb_800_w": 800, "thumb_800_h": 393, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F088F819KML-7e61f0f94b/image_960.png", "thumb_960_w": 960, "thumb_960_h": 471, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F088F819KML-7e61f0f94b/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 502, "original_w": 2454, "original_h": 1204, "thumb_tiny": "AwAXADC/5ak/xfmaXyl/2v8Avo0uOTTqdwGeUv8Atf8AfRpvlr/tf99GpaTFF2A3yl/2v++jSeUo9f8Avo1JRRdgNHWnUnelpAFFFFABRRRQB//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F088F819KML/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F088F819KML-8b7b0af764", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ancr3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Same for what defaults in the comp planner:"}]}]}]}, {"ts": "**********.016489", "text": "Actually I just talked to her and maybe she did this? It wasn't clear. But I do know the account was looking weird when she tried logging in.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.444179", "blocks": [{"type": "rich_text", "block_id": "MF0xW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Actually I just talked to her and maybe she did this? It wasn't clear. But I do know the account was looking weird when she tried logging in."}]}]}]}, {"ts": "**********.507179", "text": "A bug, I think - is that employee name is not defaulted on, and we can't seem to add it.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.444179", "blocks": [{"type": "rich_text", "block_id": "oqc1i", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "A bug, I think - is that employee name is not defaulted on, and we can't seem to add it."}]}]}]}, {"ts": "**********.803809", "text": "will take a look.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.444179", "blocks": [{"type": "rich_text", "block_id": "PR6Jz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will take a look."}]}]}]}, {"ts": "**********.316869", "text": "<@U07EJ2LP44S> We have to click reset default initially for the first time setup post deployment. I had done it for other customers. It should be good now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.444179", "blocks": [{"type": "rich_text", "block_id": "KWm/v", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We have to click reset default initially for the first time setup post deployment. I had done it for other customers. It should be good now."}]}]}]}], "created_at": "2025-05-22T22:00:49.333855"}