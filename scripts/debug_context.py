#!/usr/bin/env python3
"""
Debug script to check what context is being passed to the LLM.
"""

import os, sys, django
sys.path.append('.'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local"); django.setup()

from apps.search.services.rag_service import RAGService
from apps.accounts.models import Tenant
from django.contrib.auth.models import User

def debug_context():
    """Debug what context is being retrieved and passed to the LLM."""
    
    # Get tenant and user
    tenant = Tenant.objects.get(slug="stride")
    user = User.objects.get(email="<EMAIL>")
    
    # Create RAG service
    rag_service = RAGService(tenant_slug="stride", user=user)
    
    # Test query
    query = "whats latest on curana?"
    
    print(f"🔍 Debugging context for query: '{query}'")
    print("=" * 60)
    
    # Get the unified service to access retrieval directly
    unified_service = rag_service.unified_service
    
    # Get the citation engine
    citation_engine = unified_service.citation_engine
    
    # Perform retrieval to see what context is being found
    print("🔎 Retrieving context...")
    
    # Get the retriever from the citation engine
    retriever = citation_engine.retriever
    
    # Retrieve nodes
    nodes = retriever.retrieve(query)
    
    print(f"📄 Retrieved {len(nodes)} context nodes:")
    print("-" * 40)
    
    for i, node in enumerate(nodes, 1):
        print(f"\nNode {i}:")
        print(f"  Score: {node.score:.4f}")
        print(f"  Node ID: {node.node_id}")
        print(f"  Content: {node.text[:200]}...")
        
        # Check if we can get metadata
        if hasattr(node, 'metadata') and node.metadata:
            print(f"  Metadata: {node.metadata}")
    
    print("\n" + "=" * 60)
    print("🧪 Now testing full search to see final response...")
    
    # Perform full search
    search_result, retrieved_docs = rag_service.search(
        query_text=query,
        top_k=10,
        use_hybrid_search=True,
        use_context_aware=True,
        output_format="text",
    )
    
    print(f"\n📝 Final Response:")
    print("-" * 20)
    print(search_result.generated_answer)
    
    print(f"\n📚 Citations:")
    print("-" * 20)
    citations = search_result.citations.all().order_by('rank')
    for citation in citations:
        chunk = citation.document_chunk
        print(f"  {citation.rank}. {chunk.document.title}")
        print(f"     Score: {citation.relevance_score:.4f}")
        print(f"     Content: {chunk.text[:100]}...")
        print(f"     Created: {chunk.document.created_at}")
        print()

if __name__ == "__main__":
    debug_context()
