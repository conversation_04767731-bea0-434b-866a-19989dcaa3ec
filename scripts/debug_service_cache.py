#!/usr/bin/env python
"""
Debug the service cache to see what's cached and clear it if needed.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from apps.core.utils.service_cache import ServiceCacheManager, clear_service_cache, _service_cache
from django.contrib.auth.models import User

def debug_service_cache():
    """Debug the service cache."""
    print("🔍 Debugging Service Cache")
    print("=" * 50)
    
    # Get cache stats
    stats = ServiceCacheManager.get_cache_stats()
    print(f"📊 Cache Statistics:")
    print(f"   Total entries: {stats['total_entries']}")
    print(f"   Valid entries: {stats['valid_entries']}")
    print(f"   Expired entries: {stats['expired_entries']}")
    print(f"   Max capacity: {stats['max_capacity']}")
    
    # Show cached services
    print(f"\n📋 Cached Services:")
    if _service_cache:
        for cache_key, (service, timestamp) in _service_cache.items():
            print(f"   {cache_key}:")
            print(f"      Service type: {type(service).__name__}")
            if hasattr(service, 'tenant_slug'):
                print(f"      Tenant slug: {service.tenant_slug}")
            if hasattr(service, 'tenant'):
                print(f"      Tenant: {service.tenant.slug if service.tenant else 'None'}")
            print(f"      Cached at: {timestamp}")
    else:
        print("   No cached services")
    
    # Test getting a service for the user
    user = User.objects.get(email="<EMAIL>")
    print(f"\n🔧 Testing service retrieval for user: {user.email}")
    
    # Test with stride tenant
    print(f"\n   Testing with 'stride' tenant:")
    cached_service = ServiceCacheManager.get_cached_service("unified_rag", "stride", user.id)
    if cached_service:
        print(f"      ✅ Found cached service")
        print(f"      Service tenant: {cached_service.tenant.slug if cached_service.tenant else 'None'}")
    else:
        print(f"      ❌ No cached service found")
    
    # Test with default tenant
    print(f"\n   Testing with 'default' tenant:")
    cached_service = ServiceCacheManager.get_cached_service("unified_rag", "default", user.id)
    if cached_service:
        print(f"      ✅ Found cached service")
        print(f"      Service tenant: {cached_service.tenant.slug if cached_service.tenant else 'None'}")
    else:
        print(f"      ❌ No cached service found")
    
    # Clear the cache
    print(f"\n🧹 Clearing service cache...")
    clear_service_cache()
    
    # Verify cache is cleared
    stats_after = ServiceCacheManager.get_cache_stats()
    print(f"📊 Cache Statistics After Clear:")
    print(f"   Total entries: {stats_after['total_entries']}")
    print(f"   Valid entries: {stats_after['valid_entries']}")
    
    print(f"\n✅ Service cache debugging completed!")

if __name__ == "__main__":
    debug_service_cache()
