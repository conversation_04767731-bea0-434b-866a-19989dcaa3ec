{"date": "2024-06-06", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1717672051.021099", "text": "<@U04DKEFP1K8> I have been making sure all the changes are synced to test ENV where we have customer sandboxes and new-meritview everyday. Its been a while we have not synced code changes to staging. Should I go ahead and keep doing that everyday going forward with staging as well. I think I stopped doing that due to people insights work.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1717672051.021099", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "1717673736.000000"}, "blocks": [{"type": "rich_text", "block_id": "DX+Gn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I have been making sure all the changes are synced to test ENV where we have customer sandboxes and new-meritview everyday. Its been a while we have not synced code changes to staging. Should I go ahead and keep doing that everyday going forward with staging as well. I think I stopped doing that due to people insights work."}]}]}]}], "created_at": "2025-05-22T21:35:34.597861"}