{"thread_ts": "**********.302109", "channel_id": "C065QSSNH8A", "reply_count": 3, "replies": [{"ts": "1736397124.622499", "text": "Thank You <@U07EJ2LP44S>. I will take a look and we can discuss in stand up on this today and set expectations.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.302109", "blocks": [{"type": "rich_text", "block_id": "h7XMF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank You "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": ". I will take a look and we can discuss in stand up on this today and set expectations."}]}]}]}, {"ts": "**********.917829", "text": "<@U07EJ2LP44S> what is the \"Hierarchy issue\" in Curana health mentioned in the doc? is there any action item for me.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.302109", "blocks": [{"type": "rich_text", "block_id": "rnzb0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " what is the \"Hierarchy issue\" in Curana health mentioned in the doc? is there any action item for me."}]}]}]}, {"ts": "**********.030149", "text": "Their hierarchy is not currently correct, but it is most likely because they made some reporting changes last quarter. We need to refresh the data.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.302109", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "s3eIK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Their hierarchy is not currently correct, but it is most likely because they made some reporting changes last quarter. We need to refresh the data."}]}]}]}], "created_at": "2025-05-22T22:00:49.336085"}