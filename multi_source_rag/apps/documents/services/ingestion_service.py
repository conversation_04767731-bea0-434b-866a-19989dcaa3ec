"""
LlamaIndex-based Ingestion Service - Production Ready

This service replaces all custom logic with LlamaIndex end-to-end implementation.
It imports and uses the UnifiedLlamaIndexIngestionService for all operations.
"""

import logging
from typing import Any, Dict, Optional, Tuple, Union

from django.contrib.auth.models import User

from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, DocumentProcessingJob
from apps.documents.services.llama_ingestion_service_unified import UnifiedLlamaIndexIngestionService

# Set up logging
logger = logging.getLogger(__name__)


class IngestionService:
    """
    LlamaIndex-based Ingestion Service Wrapper.

    This class provides backward compatibility while using the unified
    LlamaIndex ingestion service under the hood.
    """

    def __init__(self, tenant: Tenant = None, user: Optional[User] = None):
        """
        Initialize the ingestion service.

        Args:
            tenant: Tenant to ingest documents for
            user: User performing the ingestion
        """
        self.tenant = tenant
        self.user = user
        self.pagination_cursor = None

        # Initialize the unified LlamaIndex service
        if tenant:
            self.unified_service = UnifiedLlamaIndexIngestionService(tenant, user)
        else:
            self.unified_service = None

    def get_pagination_cursor(self) -> Optional[str]:
        """
        Get the current pagination cursor.

        Returns:
            Optional[str]: Current pagination cursor or None if not available
        """
        return self.pagination_cursor

    def create_source(
        self, name: str, source_type: str, config: Dict[str, Any]
    ) -> DocumentSource:
        """
        Create a document source.

        Args:
            name: Name of the source
            source_type: Type of source
            config: Configuration for the source

        Returns:
            DocumentSource: Created document source
        """
        # Validate source type
        if source_type not in ["slack", "local_slack", "github", "confluence", "file", "web", "other"]:
            raise ValueError(f"Unsupported source type: {source_type}")

        # Create source
        source = DocumentSource.objects.create(
            tenant=self.tenant, name=name, source_type=source_type, config=config
        )

        return source

    def create_processing_job(self, source: DocumentSource) -> DocumentProcessingJob:
        """
        Create a document processing job.

        Args:
            source: Document source to process

        Returns:
            DocumentProcessingJob: Created processing job
        """
        job = DocumentProcessingJob.objects.create(
            tenant=self.tenant, source=source, status="pending", created_by=self.user
        )

        return job

    def process_source(
        self,
        source: DocumentSource,
        job: Optional[DocumentProcessingJob] = None,
        batch_size: int = 100,
        **kwargs,
    ) -> Tuple[int, int]:
        """
        Process a document source using the unified LlamaIndex service.

        Args:
            source: Document source to process
            job: Processing job to update
            batch_size: Number of documents to process in a batch
            **kwargs: Additional arguments for processing

        Returns:
            Tuple[int, int]: Number of documents processed and failed
        """
        if not self.unified_service:
            raise ValueError("Unified service not initialized. Tenant is required.")

        # Delegate to the unified LlamaIndex service
        return self.unified_service.process_source(source, job, batch_size, **kwargs)

    def sync_source_from_last_update(
        self,
        source_id: Union[int, str],
        tenant_id: Union[int, str] = None,
        batch_size: int = 100,
        days_if_no_sync: int = 7,
        **kwargs,
    ) -> Tuple[int, int]:
        """
        Sync a document source from the last sync timestamp.

        Args:
            source_id: ID of the document source
            tenant_id: ID of the tenant (optional if service already has tenant)
            batch_size: Number of documents to process in a batch
            days_if_no_sync: Number of days to fetch if no previous sync
            **kwargs: Additional parameters for the source interface

        Returns:
            Tuple of (processed_count, failed_count)
        """
        if not self.unified_service:
            raise ValueError("Unified service not initialized. Tenant is required.")

        # Get source
        source = DocumentSource.objects.get(id=source_id)

        # Create processing job
        job = DocumentProcessingJob.objects.create(
            tenant=self.tenant,
            source=source,
            status="pending",
            created_by=self.user
        )

        # Delegate to unified service
        return self.unified_service.process_source(source, job, batch_size, **kwargs)

    def get_processing_stats(self) -> Dict[str, Any]:
        """
        Get processing statistics from the unified service.

        Returns:
            Dictionary of processing statistics
        """
        if not self.unified_service:
            return {}

        return self.unified_service.get_processing_stats()
