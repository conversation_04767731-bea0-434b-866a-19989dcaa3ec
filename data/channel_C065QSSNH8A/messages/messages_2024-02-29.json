{"date": "2024-02-29", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1709229747.645129", "text": "<@U04DS2MBWP4> Have you connected with <PERSON> directly in the last week? Wonder what's the best way to gauge how things are going, especially since her last questions were more about the \"beta\" unfinished features like the charts.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709229747.645129", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "NbAZH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Have you connected with <PERSON> directly in the last week? Wonder what's the best way to gauge how things are going, especially since her last questions were more about the \"beta\" unfinished features like the charts."}]}]}]}, {"ts": "1709187368.088919", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> How are we managing which tickets get picked up each day? The <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|Eng priorities> board seems to have gone stale, and I know we had a \"drop everything\" while unblocking SDF but we shouldn't still be picking every ticket from <https://compiify.atlassian.net/browse/COM-1929|SDF UAT> (some of these were lower priority, like \"stray checkbox\" or even the label for \"ineligible\" employees).", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709187368.088919", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "A1zkK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " How are we managing which tickets get picked up each day? The "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "Eng priorities"}, {"type": "text", "text": " board seems to have gone stale, and I know we had a \"drop everything\" while unblocking SDF but we shouldn't still be picking every ticket from "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1929", "text": "SDF UAT"}, {"type": "text", "text": " (some of these were lower priority, like \"stray checkbox\" or even the label for \"ineligible\" employees)."}]}]}]}, {"ts": "1709160384.985419", "text": "<@U065H3M6WJV> i know the issue lisa is reporting, we need to make an update department budget after we did update manager budget but for that we need actual numbers by department. we can tell lisa , we will update it and make it available later tonight or tomorrow morning", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1709160384.985419", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "lcbwK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " i know the issue lisa is reporting, we need to make an update department budget after we did update manager budget but for that we need actual numbers by department. we can tell lisa , we will update it and make it available later tonight or tomorrow morning"}]}]}]}, {"ts": "1709158216.483669", "text": "<@U04DKEFP1K8> Checking in on the customer work - do we know how soon we will be able to get PlayQ's data uploaded? And what else is potentially taking higher priority? Ex:\n• SDF code fix review/merge\n• SDF data changes\n• DA issue root-cause\n• Nauto data changes\n• PlayQ environment creation / data upload\n...anything else higher/lower on this list? :zany_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709158216.483669", "reply_count": 15, "reactions": [{"name": "muscle", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "+smRo", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Checking in on the customer work - do we know how soon we will be able to get PlayQ's data uploaded? And what else is potentially taking higher priority? Ex:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF code fix review/merge"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF data changes"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "DA issue root-cause"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Nauto data changes"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "PlayQ environment creation / data upload"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n...anything else higher/lower on this list? "}, {"type": "emoji", "name": "zany_face", "unicode": "1f92a"}]}]}]}], "created_at": "2025-05-22T21:35:34.614326"}