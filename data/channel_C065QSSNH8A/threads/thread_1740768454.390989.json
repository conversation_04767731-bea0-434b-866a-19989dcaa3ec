{"thread_ts": "1740768454.390989", "channel_id": "C065QSSNH8A", "reply_count": 8, "replies": [{"ts": "1740768648.750749", "text": "We can update through CSV upload and republish the cycle.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740768454.390989", "blocks": [{"type": "rich_text", "block_id": "znYMh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can update through CSV upload and republish the cycle."}]}]}]}, {"ts": "1740768670.612869", "text": "Please help me with CSV, I will take care.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740768454.390989", "blocks": [{"type": "rich_text", "block_id": "08cgC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please help me with CSV, I will take care."}]}]}]}, {"ts": "1740768942.311089", "text": "Can I just include those three people? I think so but let me know if I need to include more of the org", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740768454.390989", "blocks": [{"type": "rich_text", "block_id": "olKf1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can I just include those three people? I think so but let me know if I need to include more of the org"}]}]}]}, {"ts": "1740768991.741019", "text": "whoever is impacted should be good enough.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740768454.390989", "blocks": [{"type": "rich_text", "block_id": "0iSAT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "whoever is impacted should be good enough."}]}]}]}, {"ts": "1740769020.341709", "text": "whatever data correction is required I meant.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740768454.390989", "edited": {"user": "U0690EB5JE5", "ts": "1740769034.000000"}, "blocks": [{"type": "rich_text", "block_id": "dcL7C", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "whatever data correction is required I meant."}]}]}]}, {"ts": "1740769080.790759", "text": "I think you only need the two directs, but I put the manager on here as well.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740768454.390989", "files": [{"id": "F08F5LL1K1V", "created": 1740769066, "timestamp": 1740769066, "name": "ManagerChanges.csv", "title": "ManagerChanges.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 2195, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F5LL1K1V/managerchanges.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F5LL1K1V/download/managerchanges.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F5LL1K1V/managerchanges.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F5LL1K1V-ceea1d891f", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F5LL1K1V/managerchanges.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">NC</div><div class=\"cm-col cm-num\">3107</div><div class=\"cm-col\">DENISE</div><div class=\"cm-col\">DIXON</div><div class=\"cm-col\">DENISE DIXON</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">3/20/23</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">2659</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">3/20/23</div><div class=\"cm-col\"></div><div class=\"cm-col\">Black or African American</div><div class=\"cm-col\">CA</div><div class=\"cm-col\">Executive Director</div><div class=\"cm-col\">ASC Administration</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">3</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">140000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col cm-num\">21000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">21000</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">21000</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">6/23/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">2859</div><div class=\"cm-col\">LAURA</div><div class=\"cm-col\">ROBLES</div><div class=\"cm-col\">LAURA ROBLES</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">12/19/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">3107</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">12/19/22</div><div class=\"cm-col\"></div><div class=\"cm-col\">Hispanic or Latino</div><div class=\"cm-col\">CA</div><div class=\"cm-col\">Market Sales Manager</div><div class=\"cm-col\">Sales</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">80000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">10/4/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">2303</div><div class=\"cm-col\">LISA</div><div class=\"cm-col\">MUKANOS</div><div class=\"cm-col\">LISA MUKANOS</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">6/6/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">3107</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">6/6/22</div><div class=\"cm-col\"></div><div class=\"cm-col\">White</div><div class=\"cm-col\">CA</div><div class=\"cm-col\">Market Sales Manager</div><div class=\"cm-col\">Sales</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Healthplan Admin</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">Regular</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Meets Expectations</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">78540</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/22/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 4, "lines_more": 3, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "AF+25", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think you only need the two directs, but I put the manager on here as well."}]}]}]}, {"ts": "1740769681.070919", "text": "Done <@U07EJ2LP44S>. If there are any more issues, Will take a look on my Monday.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740768454.390989", "blocks": [{"type": "rich_text", "block_id": "JYTMc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Done "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": ". If there are any more issues, Will take a look on my Monday."}]}]}]}, {"ts": "1740769690.836409", "text": "Logging off.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740768454.390989", "reactions": [{"name": "zzz", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "wn6Z7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Logging off."}]}]}]}], "created_at": "2025-05-22T22:00:49.354540"}