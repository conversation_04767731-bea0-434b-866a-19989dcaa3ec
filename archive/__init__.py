"""
Archive of Previous RAG Services

This folder contains the archived RAG services that have been replaced by the new
consolidated RAGService. These files are kept for reference and potential rollback.

Archived Services:
- rag_service_old.py: Original RAGService (wrapper around UnifiedRAGService)
- unified_rag_service.py: Core LlamaIndex RAG implementation
- enhanced_rag_service.py: Advanced features (query expansion, multi-step reasoning)

Migration Date: 2025-01-30
Reason: Consolidated into single RAGService to eliminate code duplication

The new RAGService combines all functionality from these three services into a single
service with feature flags, reducing code from ~1,500 lines to ~750 lines while
maintaining 100% functional equivalence.
"""
