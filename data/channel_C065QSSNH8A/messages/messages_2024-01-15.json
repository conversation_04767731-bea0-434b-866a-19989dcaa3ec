{"date": "2024-01-15", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "**********.584069", "text": "Doing some testing on <PERSON><PERSON><PERSON><PERSON>'s account today, and I've found <https://compiify.atlassian.net/browse/COM-2145|several bugs>. Anything where the math calculation is done incorrectly is a potential showstopper, IMO.\n\nThere are also some known bugs in the the downloadable reports that I've logged under <https://compiify.atlassian.net/browse/COM-1929|SDF testing>, but those would apply to Neuroflow as well.\n\n<@U04DKEFP1K8> Let me know when you have time to review these so we can agree on a plan for eng to tackle. If we want to give Jen &amp; <PERSON> their login info tomorrow, I need to know whether we also have to explain any known issues, and I'd prefer not to have obvious miscalculations as part of the \"known issues.\"\n\n(Also -- I haven't been able to test Adjustment letters on any customer instance yet, so the number of bugs there is still unknown.)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "**********.584069", "reply_count": 5, "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "a/XTh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Doing some testing on <PERSON><PERSON><PERSON><PERSON>'s account today, and I've found "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2145", "text": "several bugs"}, {"type": "text", "text": ". Anything where the math calculation is done incorrectly is a potential showstopper, IMO.\n\nThere are also some known bugs in the the downloadable reports that I've logged under "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1929", "text": "SDF testing"}, {"type": "text", "text": ", but those would apply to Neuroflow as well.\n\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Let me know when you have time to review these so we can agree on a plan for eng to tackle. If we want to give <PERSON> & Megan their login info tomorrow, I need to know whether we also have to explain any known issues, and I'd prefer not to have obvious miscalculations as part of the \"known issues.\"\n\n(Also -- I haven't been able to test Adjustment letters on any customer instance yet, so the number of bugs there is still unknown.)"}]}]}]}], "created_at": "2025-05-22T21:35:34.592370"}