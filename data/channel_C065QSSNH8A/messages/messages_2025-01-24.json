{"date": "2025-01-24", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1737660532.566279", "text": "Bug, maybe? in <http://test.stridehr.io|test.stridehr.io> -  <https://www.loom.com/share/f37e02a25c074122844d9d8415938738?sid=b76984e3-c4d3-49fc-bbc1-7ebd3f60b21c>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737660532.566279", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "bEq2V", "video_url": "https://www.loom.com/embed/f37e02a25c074122844d9d8415938738?sid=b76984e3-c4d3-49fc-bbc1-7ebd3f60b21c&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/f37e02a25c074122844d9d8415938738-7fba98905564c15a-4x3.jpg", "alt_text": "Troubleshooting Demo User Issues", "title": {"type": "plain_text", "text": "Troubleshooting Demo User Issues", "emoji": true}, "title_url": "https://www.loom.com/share/f37e02a25c074122844d9d8415938738", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 1 min  ", "emoji": true}}, {"type": "section", "block_id": "16WFC", "text": {"type": "mrkdwn", "text": ":information_source: In this video, I'm addressing some issues I'm encountering while using a demo user account for Diversified's anonymized training. Specifically, I'm...", "verbatim": false}}, {"type": "actions", "block_id": "srYJ7", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/f37e02a25c074122844d9d8415938738?sid=b76984e3-c4d3-49fc-bbc1-7ebd3f60b21c"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"f37e02a25c074122844d9d8415938738\",\"videoName\":\"Troubleshooting Demo User Issues\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://www.loom.com/share/f37e02a25c074122844d9d8415938738?sid=b76984e3-c4d3-49fc-bbc1-7ebd3f60b21c", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "3BBHr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bug, maybe? in "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": " -  "}, {"type": "link", "url": "https://www.loom.com/share/f37e02a25c074122844d9d8415938738?sid=b76984e3-c4d3-49fc-bbc1-7ebd3f60b21c"}]}]}]}, {"ts": "**********.807219", "text": "<@U0690EB5JE5> I have noticed that the root employee is no longer showing in the org view. They are not there in the hierarchy or as an individual. This only changed after these recent pushes. I just noticed it in the <http://test.stridehr.io|test.stridehr.io> account so it's not just weird data for a customer, it seems to be by design?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.807219", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "U0OdF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I have noticed that the root employee is no longer showing in the org view. They are not there in the hierarchy or as an individual. This only changed after these recent pushes. I just noticed it in the "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": " account so it's not just weird data for a customer, it seems to be by design?"}]}]}]}, {"ts": "**********.291089", "text": "<@U07EJ2LP44S> I am logging off now. Do you need my help with anything in like next half an hour?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.291089", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "8N/wV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am logging off now. Do you need my help with anything in like next half an hour?"}]}]}]}, {"ts": "**********.869849", "text": "<@U0690EB5JE5> trying to upload this to <http://test.stridehr.io|test.stridehr.io>.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.869849", "reply_count": 6, "files": [{"id": "F08A39ZVDAQ", "created": **********, "timestamp": **********, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 105097, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08A39ZVDAQ/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08A39ZVDAQ/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A39ZVDAQ-d325efe8b7/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A39ZVDAQ-d325efe8b7/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A39ZVDAQ-d325efe8b7/image_360.png", "thumb_360_w": 360, "thumb_360_h": 183, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A39ZVDAQ-d325efe8b7/image_480.png", "thumb_480_w": 480, "thumb_480_h": 244, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A39ZVDAQ-d325efe8b7/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A39ZVDAQ-d325efe8b7/image_720.png", "thumb_720_w": 720, "thumb_720_h": 366, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A39ZVDAQ-d325efe8b7/image_800.png", "thumb_800_w": 800, "thumb_800_h": 406, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A39ZVDAQ-d325efe8b7/image_960.png", "thumb_960_w": 960, "thumb_960_h": 488, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08A39ZVDAQ-d325efe8b7/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 520, "original_w": 1878, "original_h": 954, "thumb_tiny": "AwAYADDTpvGcf1p1J/FQAYFG0UtFACbRS0UUAFIPvdKWigAooooAKKKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08A39ZVDAQ/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08A39ZVDAQ-91e3d56585", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F089U5WNMC6", "created": 1737657159, "timestamp": 1737657159, "name": "PayrightBonuses.csv", "title": "PayrightBonuses.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 1439, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089U5WNMC6/payrightbonuses.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089U5WNMC6/download/payrightbonuses.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089U5WNMC6/payrightbonuses.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089U5WNMC6-ce750add1f", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F089U5WNMC6/payrightbonuses.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,Target Bonus (%),Target Company Performance (%),Target Individual Performance (%),Earned Company Performance (%),Earned Individual Performance (%),\r\nU,37,10,40,60,100,71,\r\nU,10,10,40,60,100,72,\r\nU,126,10,40,60,100,73,\r\nU,168,10,40,60,100,74,\r\nU,231,10,40,60,100,75,\r\nU,19,10,40,60,100,76,\r\nU,24,10,40,60,100,77,\r\nU,157,10,40,60,100,78,\r\nU,193,10,40,60,100,79,\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">37</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">71</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">72</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">126</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">73</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">168</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">74</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">231</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">75</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">19</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">76</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">24</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">77</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">157</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">78</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">193</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">79</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 54, "lines_more": 44, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "BlfEB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " trying to upload this to "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": "."}]}]}]}], "created_at": "2025-05-22T21:35:34.704999"}