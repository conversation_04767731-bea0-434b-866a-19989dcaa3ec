#!/usr/bin/env python
"""
Quick Slack Data Ingestion Script

A simplified script to quickly ingest Slack data from the data/ folder
using LocalSlackInterface and UnifiedIngestionService.

Usage:
    python scripts/quick_slack_ingest.py
"""

import os
import sys
import django
import logging
from pathlib import Path
from datetime import datetime

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from django.db import transaction
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource
from apps.documents.interfaces.local_slack import LocalSlackSourceInterface
from apps.documents.services.llama_ingestion_service_unified import UnifiedIngestionService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def main():
    """Main ingestion function."""
    print("🚀 Starting Quick Slack Data Ingestion")
    print("=" * 50)
    
    # Configuration
    data_dir = "data"
    tenant_slug = "default"
    channel_id = "C065QSSNH8A"
    time_period = "monthly"
    days_back = 90  # Last 3 months
    
    try:
        # 1. Get or create tenant
        print("📋 Setting up tenant...")
        tenant, created = Tenant.objects.get_or_create(
            slug=tenant_slug,
            defaults={"name": f"Tenant {tenant_slug}"}
        )
        if created:
            print(f"✅ Created new tenant: {tenant_slug}")
        else:
            print(f"📋 Using existing tenant: {tenant_slug}")
        
        # 2. Create or get document source
        print("📁 Setting up document source...")
        source, created = DocumentSource.objects.get_or_create(
            tenant=tenant,
            name=f"Local Slack - {channel_id}",
            source_type="local_slack",
            defaults={
                "config": {
                    "channel_id": channel_id,
                    "data_dir": data_dir,
                    "time_period": time_period
                }
            }
        )
        if created:
            print(f"✅ Created document source: {source.name}")
        else:
            print(f"📋 Using existing document source: {source.name}")
        
        # 3. Initialize LocalSlackInterface
        print("🔧 Initializing LocalSlackInterface...")
        config = {
            "channel_id": channel_id,
            "data_dir": data_dir,
            "time_period": time_period,
            "enable_summary": False,
            "quality_threshold": 0.3
        }
        
        interface = LocalSlackSourceInterface(config)
        print(f"✅ LocalSlackInterface initialized for channel {channel_id}")
        
        # 4. Check data availability
        print("📊 Checking data availability...")
        data_info = interface.get_staged_data_info()
        print(f"   Available dates: {len(data_info.available_dates)}")
        print(f"   Total messages: {data_info.total_messages}")
        print(f"   Thread count: {data_info.thread_count}")
        print(f"   User count: {data_info.user_count}")
        
        if data_info.total_messages == 0:
            print("❌ No messages found in data directory")
            return 1
        
        # 5. Fetch documents
        print(f"📥 Fetching documents (last {days_back} days, {time_period} aggregation)...")
        documents = interface.fetch_documents(
            channel_id=channel_id,
            days_back=days_back,
            include_threads=True,
            filter_bots=True,
            time_period=time_period
        )
        
        print(f"✅ Fetched {len(documents)} documents")
        
        if not documents:
            print("❌ No documents to process")
            return 1
        
        # Show document summary
        print("\n📄 Document Summary:")
        for i, doc in enumerate(documents[:3]):  # Show first 3
            title = doc.get("title", "Untitled")
            content_length = len(doc.get("content", ""))
            metadata = doc.get("metadata", {})
            message_count = metadata.get("message_count", 0)
            period_key = metadata.get("period_key", "unknown")
            print(f"   {i+1}. {title}")
            print(f"      Period: {period_key}, Messages: {message_count}, Content: {content_length} chars")
        
        if len(documents) > 3:
            print(f"   ... and {len(documents) - 3} more documents")
        
        # 6. Initialize ingestion service
        print("\n🔄 Initializing ingestion service...")
        ingestion_service = UnifiedIngestionService(tenant_slug)
        print("✅ UnifiedIngestionService initialized")
        
        # 7. Process documents
        print(f"\n🚀 Processing {len(documents)} documents...")
        processed = 0
        errors = 0
        
        for i, doc in enumerate(documents):
            try:
                print(f"   Processing {i+1}/{len(documents)}: {doc.get('title', 'Untitled')}")
                
                with transaction.atomic():
                    result = ingestion_service.process_document(doc, source)
                    
                    if result:
                        processed += 1
                        print(f"   ✅ Processed successfully")
                    else:
                        print(f"   ⚠️  Skipped (no result)")
                        
            except Exception as e:
                errors += 1
                print(f"   ❌ Error: {str(e)}")
                continue
        
        # 8. Print final statistics
        print("\n" + "=" * 50)
        print("📊 INGESTION COMPLETED")
        print("=" * 50)
        print(f"📄 Documents fetched: {len(documents)}")
        print(f"✅ Documents processed: {processed}")
        print(f"❌ Errors: {errors}")
        print(f"📈 Success rate: {(processed/len(documents)*100):.1f}%")
        
        if processed > 0:
            print(f"\n🎉 Successfully ingested {processed} Slack documents!")
            print("💡 You can now search this data using the RAG system")
        else:
            print("\n❌ No documents were successfully processed")
            return 1
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Fatal error: {str(e)}")
        logger.exception("Fatal error during ingestion")
        return 1


if __name__ == "__main__":
    exit(main())
