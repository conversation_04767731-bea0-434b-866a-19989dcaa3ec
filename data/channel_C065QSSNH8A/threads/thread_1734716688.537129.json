{"thread_ts": "1734716688.537129", "channel_id": "C065QSSNH8A", "reply_count": 13, "replies": [{"ts": "1734717154.251029", "text": "Please see the email from senders. They have explained the issue.  you would need to reach out to these types of contacts manually", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "1pJdQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please see the email from senders. They have explained the issue.  you would need to reach out to these types of contacts manually"}]}]}]}, {"ts": "1734717182.122949", "text": "Verified/likely to engage filter was not on in Apollo", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "Ij+MR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Verified/likely to engage filter was not on in Apollo"}]}]}]}, {"ts": "1734717346.993899", "text": "I read the email and we have the filter on, what <PERSON><PERSON> saying is that even the Verified emails are still getting marked as spam. Im not sure how we can reach out to them manually because we arent able to see this info until after we've already attempted to contact them and been marked as spam", "user": "U07NBMXTL1E", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "HrsH9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I read the email and we have the filter on, what <PERSON><PERSON> saying is that even the Verified emails are still getting marked as spam. Im not sure how we can reach out to them manually because we arent able to see this info until after we've already attempted to contact them and been marked as spam"}]}]}]}, {"ts": "1734717357.403619", "text": "Its not something we're able to see until after the first email has been sent", "user": "U07NBMXTL1E", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "aC4ct", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Its not something we're able to see until after the first email has been sent"}]}]}]}, {"ts": "1734717551.794459", "text": "As they explained in the email, some of these companies have a software that automatically flags any emails sent from Apollo and other similar tools as spam. There is nothing we can do about it.\n\nThe number one thing we can do for now is to make sure we do not send any emails to unverified contacts. \n\n", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "h5WFm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "As they explained in the email, some of these companies have a software that automatically flags any emails sent from Apollo and other similar tools as spam. There is nothing we can do about it.\n\nThe number one thing we can do for now is to make sure we do not send any emails to unverified contacts. \n\n"}]}]}]}, {"ts": "1734717612.658069", "text": "Is there a way they're able to ID those types of companies / software before sending?", "user": "U07NBMXTL1E", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "mgQjE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is there a way they're able to ID those types of companies / software before sending?"}]}]}]}, {"ts": "1734717616.160949", "text": "For the prospects where we are getting mugged as spam because of their internal software, you just have to reach out to them manually if you want to prospect to them", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "RVzSt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For the prospects where we are getting mugged as spam because of their internal software, you just have to reach out to them manually if you want to prospect to them"}]}]}]}, {"ts": "1734717637.195149", "text": "Because we're still just firing away at like a 10%+ spam rate which is crazy high", "user": "U07NBMXTL1E", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "qbX/p", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Because we're still just firing away at like a 10%+ spam rate which is crazy high"}]}]}]}, {"ts": "1734717648.404849", "text": "Can you send an email to senders with this question?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "yb1O/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can you send an email to senders with this question?"}]}]}]}, {"ts": "1734717657.826409", "text": "And I thought the entire point of Senders was to rotate inboxes so its not coming from Apollo", "user": "U07NBMXTL1E", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "0Zq5o", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And I thought the entire point of Senders was to rotate inboxes so its not coming from Apollo"}]}]}]}, {"ts": "1734717660.111609", "text": "The last I checked, the spam rate was 5%", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "2FSxT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The last I checked, the spam rate was 5%"}]}]}]}, {"ts": "1734717666.926579", "text": "<PERSON> Ill send this on the chain with <PERSON> and <PERSON>", "user": "U07NBMXTL1E", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "ugczx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> Ill send this on the chain with <PERSON> and <PERSON>"}]}]}]}, {"ts": "1734717850.771989", "text": "Currently at bounced rate is about 6%. If we stop sending to unverified emails. Then it should significantly reduced the bounce rate. that is at least half the battle one.\n\nFor spamming issue,<PERSON> suggested sending emails to mid-level managers first as C suite and executives generally have more restrictive filters for their emails", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734716688.537129", "blocks": [{"type": "rich_text", "block_id": "7Gcp6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Currently at bounced rate is about 6%. If we stop sending to unverified emails. Then it should significantly reduced the bounce rate. that is at least half the battle one.\n\nFor spamming issue,<PERSON> suggested sending emails to mid-level managers first as C suite and executives generally have more restrictive filters for their emails"}]}]}]}], "created_at": "2025-05-22T22:00:49.339337"}