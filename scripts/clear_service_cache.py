#!/usr/bin/env python3
"""
Clear the service cache to force reinitialization with correct tenant.
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.core.cache import cache

def clear_service_cache():
    """Clear the service cache."""
    print("🧹 Clearing Service Cache")
    print("=" * 40)
    
    # Clear all cache
    cache.clear()
    print("✅ All cache cleared")
    
    # Also clear specific service cache keys if they exist
    cache_keys_to_clear = [
        'service_cache:unified_rag:default:1',
        'service_cache:unified_rag:default:2',
        'service_cache:unified_rag:default:3',
        'service_cache:unified_rag:stride:1',
        'service_cache:unified_rag:stride:2',
        'service_cache:unified_rag:stride:3',
    ]
    
    for key in cache_keys_to_clear:
        cache.delete(key)
        print(f"✅ Cleared cache key: {key}")
    
    print("🎉 Service cache cleared successfully!")
    print("   Please restart the Django server to see the changes.")

if __name__ == "__main__":
    clear_service_cache()
