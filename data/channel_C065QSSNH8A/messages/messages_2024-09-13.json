{"date": "2024-09-13", "channel_id": "C065QSSNH8A", "message_count": 21, "messages": [{"ts": "1726250266.752679", "text": "I am thinking combining the case study and testimonial together might be best (for <PERSON> at least) So one call, recorded, going through case study questions. That will likely result in the material needed for the video testimonial. <https://docs.google.com/document/d/1h_1yUkG-H1zp03BH6p4k16yegGmAAbGp9SbpPznagCs/edit?usp=sharing>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726250266.752679", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "files": [{"id": "F07LYKDAAR5", "created": 1726250268, "timestamp": 1726250268, "name": "Case Study Instructions", "title": "Case Study Instructions", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 177885, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1h_1yUkG-H1zp03BH6p4k16yegGmAAbGp9SbpPznagCs", "external_url": "https://docs.google.com/document/d/1h_1yUkG-H1zp03BH6p4k16yegGmAAbGp9SbpPznagCs/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1h_1yUkG-H1zp03BH6p4k16yegGmAAbGp9SbpPznagCs/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07LYKDAAR5-da7933a1fd/case_study_instructions_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTSP1xRuHrSN1FJ+VADtw9aMj1puR6Cl3Z9PzoAdRRRQAjcc03cPU0r/dpo+maAHBx70oOexpmP9k0oAz0NAD6KKKAGv92mY/zmnv8Adpn4ZoAMe4/OnJx9frSd+gpyjuMUAOooooA//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07LYKDAAR5/case_study_instructions", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "cV9Ea", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am thinking combining the case study and testimonial together might be best (for <PERSON> at least) So one call, recorded, going through case study questions. That will likely result in the material needed for the video testimonial. "}, {"type": "link", "url": "https://docs.google.com/document/d/1h_1yUkG-H1zp03BH6p4k16yegGmAAbGp9SbpPznagCs/edit?usp=sharing"}]}]}]}, {"ts": "1726248531.449859", "text": "<@U04DKEFP1K8> Following changes have been deployed from priority list and recent tickets\n1. Additional Planning Levels\n2. Breakdown budget usage when budget is combined\n3. COLA Component \n4. Exclude by job title/comp type\n5. Add a new column to show region\n6. Merit Planning audit log - only on <http://qa.stridehr.io|qa.stridehr.io> will merge it to remaining ENVs on Monday\n7. Hourly Employee support - merged today to <http://test.stridehr.io|test.stridehr.io>\n8. Edit/view Performance rating from org view\n9. Control Recommendations by sub components\n10. Fix budget alerts in Cycle insights\n11. Other bugs fixed\n    a. <https://compiify.atlassian.net/browse/COM-3588>\n    b. div energy cycle creation issues and minor enhancements i the same ticket\n12. OTE was also deployed long back but needs to be tested. Please ignore if done already", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1726248531.449859", "reply_count": 3, "edited": {"user": "U0690EB5JE5", "ts": "1726491918.000000"}, "reactions": [{"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}, {"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13649::a55d833071f511efa99641c6f85bda48", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3588?atlOrigin=eyJpIjoiYTAzM2M1M2RlMmRlNDZjY2IzZDU5YTFmMDJlNTZkNzUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3588 Inconsistent Total Employee Count on eligibility rule step>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13649::a55d833271f511efa99641c6f85bda48", "elements": [{"type": "mrkdwn", "text": "Status: *In QA*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/712020:7d833b3a-e9d6-423f-93b8-d525378819e9/af9dff77-7442-4e64-9882-eb63aaa8f5a1/48?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13649::a55d833171f511efa99641c6f85bda48", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13649\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13649\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3588", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "px6n5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Following changes have been deployed from priority list and recent tickets\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Additional Planning Levels"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Breakdown budget usage when budget is combined"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "COLA Component "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Exclude by job title/comp type"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Add a new column to show region"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Merit Planning audit log - only on "}, {"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}, {"type": "text", "text": " will merge it to remaining ENVs on Monday"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Hourly Employee support - merged today to "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Edit/view Performance rating from org view"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Control Recommendations by sub components"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Fix budget alerts in Cycle insights"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Other bugs fixed"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3588"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "div energy cycle creation issues and minor enhancements i the same ticket"}]}], "style": "ordered", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OTE was also deployed long back but needs to be tested. Please ignore if done already"}]}], "style": "ordered", "indent": 0, "offset": 11, "border": 0}]}]}, {"ts": "1726244404.761649", "text": "<@U0690EB5JE5> Nauto feedback <https://app.fireflies.ai/view/Nauto-Feedback-Stride::T9mshlBSYB4VZWHi>", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"from_url": "https://app.fireflies.ai/view/Nauto-Feedback-Stride::T9mshlBSYB4VZWHi", "thumb_url": "https://files.fireflies.ai/app/img/preview.png", "thumb_width": 1280, "thumb_height": 720, "image_url": "https://files.fireflies.ai/app/img/preview.png", "image_width": 1280, "image_height": 720, "image_bytes": 188965, "service_icon": "https://app.fireflies.ai/favicon.ico", "id": 1, "original_url": "https://app.fireflies.ai/view/Nauto-Feedback-Stride::T9mshlBSYB4VZWHi", "fallback": "<PERSON><PERSON> / Stride - Meeting recording by Fireflies.ai", "text": "The Nauto Feedback/Stride meeting focused on gathering insights for product improvement and addressing user experiences during the merit cycle. Key discussions highl...", "title": "<PERSON><PERSON> / Stride - Meeting recording by Fireflies.ai", "title_link": "https://app.fireflies.ai/view/Nauto-Feedback-Stride::T9mshlBSYB4VZWHi", "service_name": "app.fireflies.ai"}], "blocks": [{"type": "rich_text", "block_id": "0f+HI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Nauto feedback "}, {"type": "link", "url": "https://app.fireflies.ai/view/Nauto-Feedback-Stride::T9mshlBSYB4VZWHi"}]}]}]}, {"ts": "1726244361.346369", "text": "we are still on wtih v", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5/GVn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we are still on wtih v"}]}]}]}, {"ts": "1726244259.416369", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> you can join my zoom above", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "M5mCQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " you can join my zoom above"}]}]}]}, {"ts": "1726244242.642829", "text": "<@U0690EB5JE5> lets meet here <https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9gC7i", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " lets meet here "}, {"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1726243472.896729", "text": "Let meet at 9:15 so you both get a little break", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+WQKz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let meet at 9:15 so you both get a little break"}]}]}]}, {"ts": "1726243433.160619", "text": "Me too running 5 mnts late.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pbY85", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Me too running 5 mnts late."}]}]}]}, {"ts": "1726243425.371669", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "1726243109.013599", "text": "<PERSON><PERSON><PERSON><PERSON> and I are going to run over, we are on with <PERSON><PERSON>", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "XwQYv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON> and I are going to run over, we are on with <PERSON><PERSON>"}]}]}]}, {"ts": "1726208923.721379", "text": "<!here> FYI... India Holiday calendar. We have a long weekend in October i.e. 30th Oct, 1st Nov. Its festival season in India.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "files": [{"id": "F06M66CEHK4", "created": 1709170503, "timestamp": 1709170503, "name": "Screenshot 2024-02-29 at 7.04.54 AM.png", "title": "Screenshot 2024-02-29 at 7.04.54 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 89730, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06M66CEHK4/screenshot_2024-02-29_at_7.04.54___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06M66CEHK4/download/screenshot_2024-02-29_at_7.04.54___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_360.png", "thumb_360_w": 360, "thumb_360_h": 297, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_480.png", "thumb_480_w": 480, "thumb_480_h": 396, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_720.png", "thumb_720_w": 720, "thumb_720_h": 594, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_800.png", "thumb_800_w": 800, "thumb_800_h": 660, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_960.png", "thumb_960_w": 960, "thumb_960_h": 792, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06M66CEHK4-90b3abde65/screenshot_2024-02-29_at_7.04.54___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 845, "original_w": 1088, "original_h": 898, "thumb_tiny": "AwAnADDS5JPP6Uo6c03qerUuP9o0ALRSYP8AeNLQAUxTk/ez/wABp9N/i/i/pQAEc9D+dKBz0P4mkI5PA/OgKO4H50AOooooAD04GaaoOejfiacelMUYPQD8aAFI56L+VKBjsPwpaKACiiigBDyOMfjSKpB6L+Ap1FAH/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06M66CEHK4/screenshot_2024-02-29_at_7.04.54___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06M66CEHK4-8fb373344a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "m/z4z", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " FYI... India Holiday calendar. We have a long weekend in October i.e. 30th Oct, 1st Nov. Its festival season in India."}]}]}]}, {"ts": "1726171453.623839", "text": "<@U07EJ2LP44S> we need a good response for <PERSON><PERSON> question:)", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726171453.623839", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "zSx88", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " we need a good response for <PERSON><PERSON> question:)"}]}]}]}, {"ts": "1726171425.742349", "text": "I think visier is probably one exception", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/z61t", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think visier is probably one exception"}]}]}]}, {"ts": "1726171410.311659", "text": "I don;t think other tools have this manager enablement piece yet. so not sure there is much we can find", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726171410.311659", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Z/rxV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I don;t think other tools have this manager enablement piece yet. so not sure there is much we can find"}]}]}]}, {"ts": "1726171010.702109", "text": "The Visier link reminded me... do you all have an internal list of all competitors, or any sort of competitive analysis <@U07M6QKHUC9>? I know of a few comp products, but I have a feeling there are many more out there that I am not aware of.\n\nNormally, I would have done a competitive analysis for a project like manager enablement, as it can be really critical for differentiation (and for understanding what the table stakes features are that all/most competitors have). I didn't go down that path yet because my time is so limited and I wanted to at least get you something to start with ASAP, but for the longer-term vision of Manager Enablement, this will be an important exercise.", "user": "U07HCJ07H7G", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GFJMi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The Visier link reminded me... do you all have an internal list of all competitors, or any sort of competitive analysis "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": "? I know of a few comp products, but I have a feeling there are many more out there that I am not aware of.\n\nNormally, I would have done a competitive analysis for a project like manager enablement, as it can be really critical for differentiation (and for understanding what the table stakes features are that all/most competitors have). I didn't go down that path yet because my time is so limited and I wanted to at least get you something to start with ASAP, but for the longer-term vision of Manager Enablement, this will be an important exercise."}]}]}]}, {"ts": "1726170519.955039", "text": "<PERSON><PERSON><PERSON> I thought you were in there already! I see two <PERSON><PERSON><PERSON> on the list?? (do you have an evil twin I didn't know about)", "user": "U07HCJ07H7G", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6E91n", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> I thought you were in there already! I see two <PERSON><PERSON><PERSON> on the list?? (do you have an evil twin I didn't know about)"}]}]}]}, {"ts": "1726169428.918699", "text": "Ok you're in there. I know what I did. I clicked on you but didn't click the tiny little 'add' link so it didn't actually do anything. Done now!", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0dLWM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok you're in there. I know what I did. I clicked on you but didn't click the tiny little 'add' link so it didn't actually do anything. Done now!"}]}]}]}, {"ts": "1726169336.863089", "text": "let me go look", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MUd9M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "let me go look"}]}]}]}, {"ts": "1726169333.182239", "text": "I definitely did! Maybe i readded the wrong one", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1Fsh7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I definitely did! Maybe i readded the wrong one"}]}]}]}, {"ts": "1726169305.633149", "text": "<@U07EJ2LP44S> gentle reminder on adding me to manager enablement channel :slightly_smiling_face:", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "g6wHz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " gentle reminder on adding me to manager enablement channel "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1726169274.058029", "text": "<@U07HCJ07H7G> here are some product images for <PERSON><PERSON><PERSON>'s comp tool. it has some neat ways to show data to managers for decision making\n<https://www.visier.com/products/smart-compensation/?showForm=0>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726169274.058029", "reply_count": 1, "attachments": [{"from_url": "https://www.visier.com/products/smart-compensation/?showForm=0", "thumb_url": "https://www.visier.com/static/visier-og-image-289b36a6392a307b7342ffcf69bdee4c.jpg", "thumb_width": 1200, "thumb_height": 630, "service_icon": "https://www.visier.com/icons/icon-48x48.png?v=e8ec1f4912f47be2b7d645b2d5d5b0d6", "id": 1, "original_url": "https://www.visier.com/products/smart-compensation/?showForm=0", "fallback": "Visier Smart Compensation - Compensation Planning Solution | Visier", "text": "Make fair and smarter compensation decisions with Visier Smart Compensation. The compensation planning solution helps your organization maximize retention impact from merit increase budget and ensure equitable compensation decisions with people analytics.", "title": "Visier Smart Compensation - Compensation Planning Solution | Visier", "title_link": "https://www.visier.com/products/smart-compensation/?showForm=0", "service_name": "visier.com"}], "blocks": [{"type": "rich_text", "block_id": "rilcw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07HCJ07H7G"}, {"type": "text", "text": " here are some product images for <PERSON><PERSON><PERSON>'s comp tool. it has some neat ways to show data to managers for decision making\n"}, {"type": "link", "url": "https://www.visier.com/products/smart-compensation/?showForm=0"}]}]}]}], "created_at": "2025-05-22T21:35:34.639399"}