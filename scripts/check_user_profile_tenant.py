#!/usr/bin/env python
"""
Check the user's profile and tenant assignment.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant

def check_user_profile_tenant():
    """Check the user's profile and tenant assignment."""
    print("🔍 Checking User Profile and Tenant")
    print("=" * 50)
    
    # Get the user
    user = User.objects.get(email="<EMAIL>")
    print(f"User: {user.email}")
    
    # Check if user has a profile
    if hasattr(user, 'profile'):
        profile = user.profile
        print(f"✅ User has profile: {profile}")
        
        if profile.tenant:
            print(f"✅ Profile has tenant: {profile.tenant.slug} ({profile.tenant.name})")
            print(f"   Tenant ID: {profile.tenant.id}")
        else:
            print(f"❌ Profile has no tenant")
    else:
        print(f"❌ User has no profile")
    
    # Check all available tenants
    print(f"\n📊 Available tenants:")
    tenants = Tenant.objects.all()
    for tenant in tenants:
        print(f"   - {tenant.slug}: {tenant.name} (ID: {tenant.id})")
    
    # Test the tenant determination logic from the view
    print(f"\n🔧 Testing tenant determination logic:")
    
    tenant_slug = (
        user.profile.tenant.slug
        if hasattr(user, "profile") and user.profile.tenant
        else "stride"
    )
    
    print(f"   Determined tenant slug: {tenant_slug}")
    
    # Check if this tenant exists
    try:
        tenant = Tenant.objects.get(slug=tenant_slug)
        print(f"   ✅ Tenant exists: {tenant.name} (ID: {tenant.id})")
    except Tenant.DoesNotExist:
        print(f"   ❌ Tenant does not exist!")
    
    # Check where the data actually is
    print(f"\n📊 Data location check:")
    from apps.documents.models import DocumentChunk
    
    for tenant in tenants:
        chunk_count = DocumentChunk.objects.filter(tenant=tenant).count()
        print(f"   {tenant.slug}: {chunk_count} chunks")

if __name__ == "__main__":
    check_user_profile_tenant()
