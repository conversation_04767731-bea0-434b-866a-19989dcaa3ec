{"date": "2024-03-23", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1711218325.487639", "text": "<@U04DKEFP1K8> For People Insights, we'll also need to include sections for bonus awards & equity. I've updated the <https://docs.google.com/document/d/111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc/edit|PRD> as well as the <https://www.figma.com/file/ESRnESN3izSXxWp7nNR6Gl/Design-file?type=design&node-id=3498-85611&mode=design&t=6DEn48w0oyFBTMC7-0|design file> to capture those additional sections; can you add whatever is needed in the <https://compiify.atlassian.net/browse/COM-2539|JIRA epic>?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1711218325.487639", "reply_count": 2, "edited": {"user": "U065H3M6WJV", "ts": "1711218343.000000"}, "files": [{"id": "F06K0N7TDAR", "created": 1708041932, "timestamp": 1708041932, "name": "Analytics & Insights PRD", "title": "People Insights PRD", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc", "external_url": "https://docs.google.com/document/d/111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc/edit", "url_private": "https://docs.google.com/document/d/111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06K0N7TDAR-31e08e5629/analytics___insights_prd_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSOc8MB+FJhv7w/KlYZ+tJhqAFAIJyc/hS03DUc/5NADqKKKAEYc9T+FIOP7x+tK3SmjGP/r0APz7UU3j0/Wjj0/WgB1FFFACN0puR/dpxIHWkCg85oAT/AIDSgn0pdtG2gBaKKKAP/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06K0N7TDAR/analytics___insights_prd", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12600::c2a76e10e94211eeaa5f7b2b9520719e", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2539?atlOrigin=eyJpIjoiNjJmODhjZTY1YjM5NDI2N2EzMDUyNDQyYjdmZmE3MjAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2539 People Insights>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12600::c2a76e12e94211eeaa5f7b2b9520719e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/acb0575739451e6f07f33e1cc4ca0b86?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12600::c2a76e11e94211eeaa5f7b2b9520719e", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12600\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12600\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2539", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "6tO+Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For People Insights, we'll also need to include sections for bonus awards & equity. I've updated the "}, {"type": "link", "url": "https://docs.google.com/document/d/111hKZ8-31Md2ACbwktCnqr5fxJzytGzsv29O0sxMnrc/edit", "text": "PRD"}, {"type": "text", "text": " as well as the "}, {"type": "link", "url": "https://www.figma.com/file/ESRnESN3izSXxWp7nNR6Gl/Design-file?type=design&node-id=3498-85611&mode=design&t=6DEn48w0oyFBTMC7-0", "text": "design file"}, {"type": "text", "text": " to capture those additional sections; can you add whatever is needed in the "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2539", "text": "JIRA epic"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1711153407.363549", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> What's the status of DA's adjustment letters? Last update on <https://compiify.atlassian.net/browse/COM-2291|this ticket> was 3/13, and in the QA environment I see a broken image for the signature and it's still using Emnet's name instead of <PERSON><PERSON><PERSON>'s. When will we be able to fix these and upload to the DA production environment for <PERSON>?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1711153407.363549", "reply_count": 7, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12352::926135c0e8ab11eeaa5f7b2b9520719e", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2291?atlOrigin=eyJpIjoiMzYwNjg0ZTUzZjlhNDU3M2FlMDE2OGE5YzYwN2Y0ZWUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2291 Set up adjustment letter templates for DA>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12352::926135c2e8ab11eeaa5f7b2b9520719e", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "mrkdwn", "text": "Type: *Sub-task*", "verbatim": false}, {"type": "image", "image_url": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/606486a6aee240006877c236/2b20eeb2-9337-436b-afa5-c418e47dbe4c/48?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12352::926135c1e8ab11eeaa5f7b2b9520719e", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12352\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12352\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2291", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "egfxs", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " What's the status of DA's adjustment letters? Last update on "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2291", "text": "this ticket"}, {"type": "text", "text": " was 3/13, and in the QA environment I see a broken image for the signature and it's still using Emnet's name instead of <PERSON><PERSON><PERSON>'s. When will we be able to fix these and upload to the DA production environment for <PERSON>?"}]}]}]}, {"ts": "1711147527.498539", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> I noticed during a customer demo that <PERSON>'s view on staging still has some broken css styling, and also does not have the latest build. I was trying to make some changes to the budget for the demo cycle, and found that I could not edit equity budget for \"<PERSON>'s directs\" team.\n• Would these be addressed when we push the fixes from the PR queue?\n• What's the timing for testing/validating all those merged PRs now?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1711147527.498539", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "NsOIH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I noticed during a customer demo that <PERSON>'s view on staging still has some broken css styling, and also does not have the latest build. I was trying to make some changes to the budget for the demo cycle, and found that I could not edit equity budget for \"<PERSON>'s directs\" team.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Would these be addressed when we push the fixes from the PR queue?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "What's the timing for testing/validating all those merged PRs now?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1711134610.669459", "text": "<@U04DKEFP1K8> Our beta customer Rightway will need Okta SSO. What will we need from their IT team in order to make that work?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1711134610.669459", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "CraEG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Our beta customer Rightway will need Okta SSO. What will we need from their IT team in order to make that work?"}]}]}]}], "created_at": "2025-05-22T21:35:34.606808"}