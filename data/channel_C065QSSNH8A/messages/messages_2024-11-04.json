{"date": "2024-11-04", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1730744103.262099", "text": "Yes I pinged <PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> for the correct answer", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nGhBy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes I pinged <PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> for the correct answer"}]}]}]}, {"ts": "1730743321.509829", "text": "<@U07EJ2LP44S> It looks ExponentHR has been waiting for a response from you", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vp2O0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " It looks ExponentHR has been waiting for a response from you"}]}]}]}, {"ts": "1730739744.816329", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> This should be fixed now. Please check and let me know", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730737277.416939", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1730739753.000000"}, "blocks": [{"type": "rich_text", "block_id": "qNQkr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " This should be fixed now. Please check and let me know"}]}]}]}, {"ts": "1730737278.915799", "text": "<https://compiify.atlassian.net/browse/COM-3953>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14036::0ea891b3307949fdab398fb36070d9aa", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3953?atlOrigin=eyJpIjoiODZlMmVmYmNkOTBhNGZmZDk0MTBlYjk2NDBhNmUyZWYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3953 Visibility Issues in SDF Test for <PERSON>>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14036::6b880a63fd054b3ba54d55d3e0acf095", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14036\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3953\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3953", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "f1SAp", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3953"}]}]}]}, {"ts": "1730737277.416939", "text": "Just submitted a bug for SDF; it's a blocker to sending them logins. When logging in as <PERSON>, I can see the vibrant team (which is not in his org). This was happened when we did the first round of testing as well.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1730737277.416939", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "sschB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Just submitted a bug for SDF; it's a blocker to sending them logins. When logging in as <PERSON>, I can see the vibrant team (which is not in his org). This was happened when we did the first round of testing as well."}]}]}]}, {"ts": "1730722665.754609", "text": "<@U07M6QKHUC9> SDF issues are addressed. Please take a look and let me know.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "uhIyy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " SDF issues are addressed. Please take a look and let me know."}]}]}]}, {"ts": "1730722632.844599", "text": "*Agenda for the call today:*\n• Bonus discussion - <@U07MH77PUBV> will be joining the call\n• DegenKolb report requirements\n• Any issues", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qjm3K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for the call today:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bonus discussion - "}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " will be joining the call"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "DegenKolb report requirements"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Any issues"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1730722582.131949", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> <@U04DKEFP1K8> Alayacare UPDATEs:\n*Fixes pushed today:*\n> • *<https://compiify.atlassian.net/browse/COM-3951?atlOrigin=eyJpIjoiM2VhM2UwOWRiMTkxNGVhMWFhZmIyNTRjODQ3YTlmMDUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3951 Missing Next Position in Band Allocation during Promotion Upload>* - re-uploading fixed the issue after handling trailing/leading spaces. Please re-upload the promotion file.\n> • *<https://compiify.atlassian.net/browse/COM-3940?atlOrigin=eyJpIjoiYTEzZjdiNDMwMGMxNDgzNmE4YWVhZjFiYTI5NGEzY2EiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3940 Inconsistent Flagging Issue for Proration Factor>*\n> • Cycle dates on cycle builder and merit task page were incorrectly displayed (less by a day)\n> • Cycle currency conversation rates were not set and I have updated the conversation rates\n*Known issues:*\n> • I noticed Org view page slowness or timing out issue for HRBP, especially hierarchy view. We are looking into this.\n> • Some of managers shown on the HRBP merit task view are not assigned manager role thus merit view table errors out. Please update the role for all recommenders\nAnd <http://qa.stridehr.io|qa.stridehr.io> is synced with alayacare prod.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730722582.131949", "reply_count": 3, "edited": {"user": "U0690EB5JE5", "ts": "1730723115.000000"}, "blocks": [{"type": "rich_text", "block_id": "knq0F", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Alayacare UPDATEs:\n"}, {"type": "text", "text": "Fixes pushed today:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3951?atlOrigin=eyJpIjoiM2VhM2UwOWRiMTkxNGVhMWFhZmIyNTRjODQ3YTlmMDUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ", "text": "COM-3951 Missing Next Position in Band Allocation during Promotion Upload", "style": {"bold": true}}, {"type": "text", "text": " - re-uploading fixed the issue after handling trailing/leading spaces. Please re-upload the promotion file."}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3940?atlOrigin=eyJpIjoiYTEzZjdiNDMwMGMxNDgzNmE4YWVhZjFiYTI5NGEzY2EiLCJwIjoiamlyYS1zbGFjay1pbnQifQ", "text": "COM-3940 Inconsistent Flagging Issue for Proration Factor", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle dates on cycle builder and merit task page were incorrectly displayed (less by a day)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Cycle currency conversation rates were not set and I have updated the conversation rates"}]}], "style": "bullet", "indent": 0, "border": 1}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Known issues:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I noticed Org view page slowness or timing out issue for HRBP, especially hierarchy view. We are looking into this."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Some of managers shown on the HRBP merit task view are not assigned manager role thus merit view table errors out. Please update the role for all recommenders"}]}], "style": "bullet", "indent": 0, "border": 1}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "And "}, {"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}, {"type": "text", "text": " is synced with alayacare prod."}]}]}]}, {"ts": "1730722122.080379", "text": "<@U07M6QKHUC9> This should be fixed now. I also observed this in Alayacare not just cycle dates all other dates in cycle builder and merit task page and fix is deployed.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1730562828.313779", "subtype": "thread_broadcast", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "NDDLZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " This should be fixed now. I also observed this in Alayacare not just cycle dates all other dates in cycle builder and merit task page and fix is deployed."}]}]}]}], "created_at": "2025-05-22T21:35:34.674647"}