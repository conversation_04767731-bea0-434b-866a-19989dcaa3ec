#!/usr/bin/env python3
"""
Validation script to check if enhanced prompts are working correctly.
This script validates the actual response quality and alignment with end goals.
"""

import os, sys, django
sys.path.append('.'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local"); django.setup()

from apps.search.services.rag_service import RAGService
from apps.accounts.models import Tenant
from django.contrib.auth.models import User
import re
from datetime import datetime

def validate_response_quality(query, response, expected_features):
    """
    Validate if the response meets the expected quality criteria.
    
    Args:
        query: The search query
        response: The generated response
        expected_features: List of features to check for
    
    Returns:
        dict: Validation results
    """
    validation_results = {
        "query": query,
        "response_length": len(response),
        "issues": [],
        "passed_checks": [],
        "overall_score": 0
    }
    
    # Check 1: Response should not contain template placeholders
    if "Please provide the context and question" in response:
        validation_results["issues"].append("❌ CRITICAL: Response contains template placeholder - prompts not receiving context")
        return validation_results
    
    if "{context}" in response or "{question}" in response:
        validation_results["issues"].append("❌ CRITICAL: Response contains unresolved template variables")
        return validation_results
    
    validation_results["passed_checks"].append("✅ No template placeholders found")
    
    # Check 2: Response should be substantial (not just a generic message)
    if len(response) < 100:
        validation_results["issues"].append("❌ Response too short - likely not using context properly")
    else:
        validation_results["passed_checks"].append("✅ Response has substantial content")
    
    # Check 3: Check for specific features based on query type
    for feature in expected_features:
        if feature == "dates":
            # Look for date patterns (various formats)
            date_patterns = [
                r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',  # MM/DD/YYYY or MM-DD-YYYY
                r'\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b',    # YYYY/MM/DD or YYYY-MM-DD
                r'\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{1,2},? \d{4}\b',  # Month DD, YYYY
                r'\b\d{1,2} (?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{4}\b',   # DD Month YYYY
                r'\b(?:yesterday|today|last week|last month|recently)\b',  # Relative dates
            ]
            
            has_dates = any(re.search(pattern, response, re.IGNORECASE) for pattern in date_patterns)
            if has_dates:
                validation_results["passed_checks"].append("✅ Contains date information")
            else:
                validation_results["issues"].append("❌ Missing date information for chronological query")
        
        elif feature == "citations":
            # Look for citation patterns
            citation_patterns = [
                r'\[Document \d+\]',
                r'\[\d+\]',
                r'Source:',
                r'According to'
            ]
            
            has_citations = any(re.search(pattern, response, re.IGNORECASE) for pattern in citation_patterns)
            if has_citations:
                validation_results["passed_checks"].append("✅ Contains citations")
            else:
                validation_results["issues"].append("❌ Missing citations")
        
        elif feature == "specific_details":
            # Check for specific details (names, numbers, technical terms)
            detail_indicators = [
                r'\b[A-Z][a-z]+ [A-Z][a-z]+\b',  # Names (First Last)
                r'\b\d+\.\d+\b',  # Version numbers
                r'\b\d+%\b',      # Percentages
                r'\bcurana\b',    # Specific topic mentioned
            ]
            
            detail_count = sum(len(re.findall(pattern, response, re.IGNORECASE)) for pattern in detail_indicators)
            if detail_count >= 3:
                validation_results["passed_checks"].append("✅ Contains specific details")
            else:
                validation_results["issues"].append("❌ Lacks specific details")
        
        elif feature == "structured_format":
            # Check for structured formatting
            structure_indicators = [
                r'^\s*[-*•]\s',  # Bullet points
                r'^\s*\d+\.\s',  # Numbered lists
                r'\*\*.*?\*\*',  # Bold text
                r'##?\s',        # Headers
            ]
            
            has_structure = any(re.search(pattern, response, re.MULTILINE) for pattern in structure_indicators)
            if has_structure:
                validation_results["passed_checks"].append("✅ Has structured formatting")
            else:
                validation_results["issues"].append("❌ Lacks structured formatting")
    
    # Calculate overall score
    total_checks = len(expected_features) + 2  # +2 for basic checks
    passed_checks = len(validation_results["passed_checks"])
    validation_results["overall_score"] = (passed_checks / total_checks) * 100
    
    return validation_results

def test_enhanced_prompts_validation():
    """Test enhanced prompts with proper validation."""
    
    print("🔍 Enhanced Prompts Validation Test")
    print("=" * 60)
    
    # Get tenant and user
    tenant = Tenant.objects.get(slug="stride")
    user = User.objects.get(email="<EMAIL>")
    
    # Create RAG service
    rag_service = RAGService(tenant_slug="stride", user=user)
    
    # Test cases with expected features
    test_cases = [
        {
            "query": "whats latest on curana?",
            "expected_features": ["dates", "citations", "specific_details", "structured_format"],
            "description": "Latest updates query - should have dates, citations, and structured details"
        },
        {
            "query": "what issues were reported about testing?",
            "expected_features": ["citations", "specific_details"],
            "description": "Issue query - should have citations and specific details"
        }
    ]
    
    overall_success = True
    
    for i, test_case in enumerate(test_cases, 1):
        query = test_case["query"]
        expected_features = test_case["expected_features"]
        description = test_case["description"]
        
        print(f"\n🧪 Test {i}: {description}")
        print(f"Query: '{query}'")
        print("-" * 40)
        
        try:
            # Perform search
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=10,
                use_hybrid_search=True,
                use_context_aware=True,
                output_format="text",
            )
            
            response = search_result.generated_answer
            
            # Validate response
            validation = validate_response_quality(query, response, expected_features)
            
            print(f"Response Length: {validation['response_length']} characters")
            print(f"Overall Score: {validation['overall_score']:.1f}%")
            
            # Show passed checks
            if validation["passed_checks"]:
                print("\nPassed Checks:")
                for check in validation["passed_checks"]:
                    print(f"  {check}")
            
            # Show issues
            if validation["issues"]:
                print("\nIssues Found:")
                for issue in validation["issues"]:
                    print(f"  {issue}")
                overall_success = False
            
            # Show response preview
            print(f"\nResponse Preview:")
            print(f"'{response[:200]}{'...' if len(response) > 200 else ''}'")
            
            # Show citations count
            citations_count = search_result.citations.count()
            print(f"\nCitations Retrieved: {citations_count}")
            
            if validation["overall_score"] < 70:
                overall_success = False
                print(f"❌ Test {i} FAILED - Score below 70%")
            else:
                print(f"✅ Test {i} PASSED")
                
        except Exception as e:
            print(f"❌ Test {i} ERROR: {str(e)}")
            overall_success = False
    
    print("\n" + "=" * 60)
    if overall_success:
        print("🎉 ALL TESTS PASSED - Enhanced prompts are working correctly!")
    else:
        print("❌ TESTS FAILED - Enhanced prompts need fixing!")
    
    return overall_success

if __name__ == "__main__":
    test_enhanced_prompts_validation()
