{"date": "2024-07-05", "channel_id": "C065QSSNH8A", "message_count": 10, "messages": [{"ts": "1720193732.013229", "text": "<@U0690EB5JE5> <PERSON><PERSON><PERSON><PERSON> was supposed to add <PERSON><PERSON><PERSON> <mailto:<EMAIL>|<EMAIL>> as admin. I don't see <PERSON><PERSON><PERSON> in the Vercara env. Can you please add her as admin", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "done", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TRTJK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON><PERSON> was supposed to add <PERSON><PERSON><PERSON> "}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": " as admin. I don't see <PERSON><PERSON><PERSON> in the Vercara env. Can you please add her as admin"}]}]}]}, {"ts": "1720191633.337239", "text": "I will go ahead and send them an email with the new link", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "3140o", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will go ahead and send them an email with the new link"}]}]}]}, {"ts": "1720191003.916279", "text": "ah I just saw their email", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Acxkr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ah I just saw their email"}]}]}]}, {"ts": "1720190848.574519", "text": "Currently they use the Compiify URL. They are not aware of the rebranding yet.  On Monday we are sending emails to all customers announcing stride.", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1720190996.000000"}, "blocks": [{"type": "rich_text", "block_id": "6Um1j", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Currently they use the Compiify URL. They are not aware of the rebranding yet.  On Monday we are sending emails to all customers announcing stride."}]}]}]}, {"ts": "1720182308.944619", "text": "Vercara users still using the compiify url. <@U04DS2MBWP4> Not sure if I am supposed to ask them to try `<https://vercara.stridehr.io/>` . Are they aware of rebranding?", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1720185209.000000"}, "blocks": [{"type": "rich_text", "block_id": "pxzSh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Vercara users still using the compiify url. "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Not sure if I am supposed to ask them to try "}, {"type": "link", "url": "https://vercara.stridehr.io/", "style": {"code": true}}, {"type": "text", "text": " . Are they aware of rebranding?"}]}]}]}, {"ts": "1720181554.528609", "text": "I am able to social login into Vercara though", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "W8s7H", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am able to social login into Vercara though"}]}]}]}, {"ts": "1720181449.099919", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FUpwS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "1720181420.107329", "text": "Vercara user use local login or social login?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Cj9kA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Vercara user use local login or social login?"}]}]}]}, {"ts": "1720118620.881919", "text": "I tried  to log into the CWA test environment but wasn’t able to log into it. <PERSON><PERSON><PERSON> said it might be due to some auth issue. ", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1720118620.881919", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "Ivi8F", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I tried  to log into the CWA test environment but "}, {"type": "text", "text": "wasn’t"}, {"type": "text", "text": " able to log into it. <PERSON><PERSON><PERSON> said it might be due to some auth issue. "}]}]}]}, {"ts": "1720118531.598649", "text": "Thanks. ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LRx4J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks. "}]}]}]}], "created_at": "2025-05-22T21:35:34.621357"}