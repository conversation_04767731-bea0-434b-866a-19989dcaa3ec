{"thread_ts": "1738694547.267379", "channel_id": "C065QSSNH8A", "reply_count": 3, "replies": [{"ts": "1738736449.374769", "text": "<@U07EJ2LP44S> This should be fixed. The issue was click is used to make an update call to system backend. We have simplified and now only enter key press will make an update call. Please note that user has to press enter key every time edits made for edits to take effect.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738694547.267379", "blocks": [{"type": "rich_text", "block_id": "tATli", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This should be fixed. The issue was click is used to make an update call to system backend. We have simplified and now only enter key press will make an update call. Please note that user has to press enter key every time edits made for edits to take effect."}]}]}]}, {"ts": "1738768284.137619", "text": "I think it’s going to be really confusing if everybody has to hit enter. I foresee a lot of issues if this is the case.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738694547.267379", "blocks": [{"type": "rich_text", "block_id": "mDodH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think it’s going to be really confusing if everybody has to hit enter. I foresee a lot of issues if this is the case."}]}]}]}, {"ts": "1738768344.197279", "text": "Yes, I also feel that. Let me check how to improve this experience and get back ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738694547.267379", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ZMyKs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, I also feel that. Let me check how to improve this experience and get back "}]}]}]}], "created_at": "2025-05-22T22:00:49.343994"}