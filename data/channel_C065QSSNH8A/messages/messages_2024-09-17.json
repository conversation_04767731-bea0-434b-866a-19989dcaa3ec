{"date": "2024-09-17", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "**********.646679", "text": "<@U07EJ2LP44S> Started curana health UAT here <https://compiify.atlassian.net/browse/COM-3613> and have added the issue with data sync , will discuss it later tonight with <@U0690EB5JE5>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13674::aa0d2650751b11efaa1cef8da9b56fe2", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3613?atlOrigin=eyJpIjoiNDFmMzgxMGU4NDVmNGEwNWIwMTMyNjc3MzdiZTIzMTIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3613 Curana Health UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13674::aa0d2652751b11efaa1cef8da9b56fe2", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13674::aa0d2651751b11efaa1cef8da9b56fe2", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13674\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13674\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3613", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "z2dBT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Started curana health UAT here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3613"}, {"type": "text", "text": " and have added the issue with data sync , will discuss it later tonight with "}, {"type": "user", "user_id": "U0690EB5JE5"}]}]}]}, {"ts": "**********.131859", "text": "Can we prioritize the bug that is preventing password reset from working? <@U0690EB5JE5> <@U04DKEFP1K8>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.131859", "reply_count": 13, "blocks": [{"type": "rich_text", "block_id": "fDz0d", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can we prioritize the bug that is preventing password reset from working? "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}]}]}]}, {"ts": "**********.189649", "text": "<@U0690EB5JE5> when does the data in the audit log start showing? is it only after it is submitted, reviewed and approved?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.189649", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "wveGb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " when does the data in the audit log start showing? is it only after it is submitted, reviewed and approved?"}]}]}]}, {"ts": "1726587746.462279", "text": "<!here> running 15 min late to standup ", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5/1o6", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " running 15 min late to standup "}]}]}]}, {"ts": "1726587314.951029", "text": "Product feedback from Monday board (I'm having trouble getting it transferred to a google doc so here's a pdf in the meantime)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726587314.951029", "reply_count": 1, "files": [{"id": "F07N5TEHHLZ", "created": 1726587310, "timestamp": 1726587310, "name": "Product Recommendations.pdf", "title": "Product Recommendations.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 8096656, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07N5TEHHLZ/product_recommendations.pdf", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07N5TEHHLZ/download/product_recommendations.pdf", "media_display_type": "unknown", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N5TEHHLZ-e62b0d55f3/product_recommendations_thumb_pdf.png", "thumb_pdf_w": 1833, "thumb_pdf_h": 2567, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07N5TEHHLZ/product_recommendations.pdf", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07N5TEHHLZ-cfd561e696", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "qXyQ8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Product feedback from Monday board (I'm having trouble getting it transferred to a google doc so here's a pdf in the meantime)"}]}]}]}, {"ts": "1726584581.276599", "text": "I have a customer demo today:)", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1726584581.276599", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "YJ7z7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have a customer demo today:)"}]}]}]}, {"ts": "1726584243.857379", "text": "<@U0690EB5JE5> and <@U04DKEFP1K8> i cannot get into the demo environment, can you check ?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726584243.857379", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "0YClF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " i cannot get into the demo environment, can you check ?"}]}]}]}, {"ts": "1726582719.118999", "text": "<@U0690EB5JE5> Can you add <PERSON> to the demo and test environments?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ku8eH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can you add <PERSON> to the demo and test environments?"}]}]}]}, {"ts": "1726520584.421339", "text": "i just scheduled sdf for the 26th over our daily standup. let me know if you want to push the standup an hour earlier (8am ET) or an hour back.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726520584.421339", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "IRvGZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i just scheduled sdf for the 26th over our daily standup. let me know if you want to push the standup an hour earlier (8am ET) or an hour back."}]}]}]}], "created_at": "2025-05-22T21:35:34.638579"}