{"date": "2024-04-26", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1714094026.368249", "text": "<!here> are the shortlisted summer internship candidates\n1. <PERSON><PERSON> <https://drive.google.com/file/d/1zs_gR4ET4rxB7iKY8tEUAg3P74v_sSfq/view?usp=drive_link>\n2. <PERSON><PERSON><PERSON> B<PERSON> <https://drive.google.com/open?id=1-Frv9YyJVcfD9epTZPZRB7MbYUuFU10N>\n3. <PERSON><PERSON><PERSON> <https://drive.google.com/open?id=1kG1d28VpDmZ4oleFWVUVk-7NaLjWdG4->\nAlso <PERSON> was offered the internship but he declined ( after initially accepting it )", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "lMh5Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " are the shortlisted summer internship candidates\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>-G<PERSON>ri "}, {"type": "link", "url": "https://drive.google.com/file/d/1zs_gR4ET4rxB7iKY8tEUAg3P74v_sSfq/view?usp=drive_link"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> "}, {"type": "link", "url": "https://drive.google.com/open?id=1-Frv9YyJVcfD9epTZPZRB7MbYUuFU10N"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> "}, {"type": "link", "url": "https://drive.google.com/open?id=1kG1d28VpDmZ4oleFWVUVk-7NaLjWdG4-"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAlso <PERSON> was offered the internship but he declined ( after initially accepting it )"}]}]}]}, {"ts": "1714091417.544729", "text": "Vercara UAT <https://compiify.atlassian.net/browse/COM-2833>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1714091417.544729", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12894::28cb9be0036411ef929db3fba3ff5629", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2833?atlOrigin=eyJpIjoiYTliYTM1MzllNDYwNDcyNGFlOTRiZDc1Y2NjMTFkMzIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2833 Vercara UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12894::28cb9be2036411ef929db3fba3ff5629", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12894::28cb9be1036411ef929db3fba3ff5629", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12894\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12894\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2833", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "rXP9U", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Vercara UAT "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2833"}]}]}]}, {"ts": "1714072442.460179", "text": "Practifi UAT jira: <https://compiify.atlassian.net/browse/COM-2821>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1714072442.460179", "reply_count": 6, "reactions": [{"name": "thankyouty", "users": ["U065H3M6WJV"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:12882::fb25d560033711ef94ec6f37b373e51a", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-2821?atlOrigin=eyJpIjoiZDJmMzE1NzhiYjZiNDExODhhZDliODU2ZjJmY2FiNzUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-2821 Practifi UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:12882::fb25d562033711ef94ec6f37b373e51a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/e00902229dab9e2e606e0890686c0aa6?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:12882::fb25d561033711ef94ec6f37b373e51a", "elements": [{"type": "button", "action_id": "{\"issueId\":\"12882\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"12882\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-2821", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "aTaxf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Practifi UAT jira: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2821"}]}]}]}], "created_at": "2025-05-22T21:35:34.602247"}