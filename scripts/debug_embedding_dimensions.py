#!/usr/bin/env python
"""
Debug embedding dimension mismatch issue.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from apps.core.utils.embedding_consistency import get_embedding_model_info, get_consistent_embedding_model
from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
from qdrant_client import QdrantClient
from django.conf import settings

def debug_embedding_dimensions():
    """Debug embedding dimension mismatch."""
    print("🔍 Debugging Embedding Dimension Mismatch")
    print("=" * 60)
    
    # Check environment variables
    print("📊 Environment Variables:")
    print(f"   EMBEDDING_MODEL_NAME: {os.environ.get('EMBEDDING_MODEL_NAME', 'Not set')}")
    print(f"   USE_GEMINI_EMBEDDING: {os.environ.get('USE_GEMINI_EMBEDDING', 'Not set')}")
    
    # Check Django settings
    print(f"\n📊 Django Settings:")
    print(f"   EMBEDDING_MODEL_NAME: {getattr(settings, 'EMBEDDING_MODEL_NAME', 'Not set')}")
    print(f"   USE_GEMINI_EMBEDDING: {getattr(settings, 'USE_GEMINI_EMBEDDING', 'Not set')}")
    
    # Check embedding model info
    print(f"\n📊 Embedding Model Info:")
    try:
        model_info = get_embedding_model_info()
        print(f"   Model name: {model_info.get('model_name', 'Unknown')}")
        print(f"   Dimensions: {model_info.get('dimensions', 'Unknown')}")
        print(f"   Provider: {model_info.get('provider', 'Unknown')}")
    except Exception as e:
        print(f"   ❌ Error getting model info: {e}")
    
    # Check consistent embedding model
    print(f"\n📊 Consistent Embedding Model:")
    try:
        embedding_model = get_consistent_embedding_model()
        print(f"   Model type: {type(embedding_model).__name__}")
        if hasattr(embedding_model, 'model_name'):
            print(f"   Model name: {embedding_model.model_name}")
        if hasattr(embedding_model, '_model'):
            print(f"   Underlying model: {embedding_model._model}")
        
        # Try to get dimensions
        try:
            # Test with a simple text
            test_embedding = embedding_model.get_text_embedding("test")
            print(f"   ✅ Actual dimensions: {len(test_embedding)}")
        except Exception as e:
            print(f"   ❌ Error getting test embedding: {e}")
            
    except Exception as e:
        print(f"   ❌ Error getting consistent model: {e}")
    
    # Check content-specific embedding model
    print(f"\n📊 Content-Specific Embedding Model:")
    try:
        content_model = get_embedding_model_for_content()
        print(f"   Model type: {type(content_model).__name__}")
        if hasattr(content_model, 'model_name'):
            print(f"   Model name: {content_model.model_name}")
        
        # Test dimensions
        try:
            test_embedding = content_model.get_text_embedding("test")
            print(f"   ✅ Actual dimensions: {len(test_embedding)}")
        except Exception as e:
            print(f"   ❌ Error getting test embedding: {e}")
            
    except Exception as e:
        print(f"   ❌ Error getting content model: {e}")
    
    # Check Qdrant collection info
    print(f"\n📊 Qdrant Collection Info:")
    try:
        client = QdrantClient(host=settings.QDRANT_HOST, port=settings.QDRANT_PORT)
        collection_name = "tenant_stride_default"
        
        collection_info = client.get_collection(collection_name)
        vector_config = collection_info.config.params.vectors
        
        if hasattr(vector_config, 'size'):
            print(f"   Collection: {collection_name}")
            print(f"   ✅ Expected dimensions: {vector_config.size}")
            print(f"   Distance: {vector_config.distance}")
        else:
            print(f"   ❌ Could not get vector config")
            
    except Exception as e:
        print(f"   ❌ Error getting Qdrant info: {e}")
    
    # Check for dimension mismatch
    print(f"\n🔍 Dimension Mismatch Analysis:")
    try:
        # Get current embedding dimensions
        embedding_model = get_consistent_embedding_model()
        test_embedding = embedding_model.get_text_embedding("test")
        current_dims = len(test_embedding)
        
        # Get Qdrant expected dimensions
        client = QdrantClient(host=settings.QDRANT_HOST, port=settings.QDRANT_PORT)
        collection_info = client.get_collection("tenant_stride_default")
        expected_dims = collection_info.config.params.vectors.size
        
        print(f"   Current embedding dimensions: {current_dims}")
        print(f"   Qdrant expected dimensions: {expected_dims}")
        
        if current_dims == expected_dims:
            print(f"   ✅ Dimensions match!")
        else:
            print(f"   ❌ DIMENSION MISMATCH!")
            print(f"   This is causing the 400 Bad Request error in the UI")
            
            # Suggest fix
            if expected_dims == 768:
                print(f"\n💡 Fix: Set environment variable to use 768d model:")
                print(f"   export EMBEDDING_MODEL_NAME='BAAI/bge-base-en-v1.5'")
            elif expected_dims == 384:
                print(f"\n💡 Fix: Set environment variable to use 384d model:")
                print(f"   export EMBEDDING_MODEL_NAME='sentence-transformers/all-MiniLM-L6-v2'")
                
    except Exception as e:
        print(f"   ❌ Error analyzing dimensions: {e}")

if __name__ == "__main__":
    debug_embedding_dimensions()
