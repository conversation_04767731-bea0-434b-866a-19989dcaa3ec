/* Modern UI Overrides for RAG Search */

/* Import Inter font for better typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Global overrides */
* {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Modern container */
.modern-search-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

/* Header section */
.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.search-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.breadcrumb-link {
  color: #64748b;
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-link:hover {
  color: #3b82f6;
}

.breadcrumb-separator {
  color: #cbd5e1;
  font-weight: 300;
}

.breadcrumb-current {
  color: #1e293b;
  font-weight: 600;
}

.search-actions {
  display: flex;
  gap: 1rem;
}

.modern-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.modern-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.modern-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
  color: white;
}

.modern-btn-secondary {
  background: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.modern-btn-secondary:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #475569;
  transform: translateY(-1px);
}

/* Override existing search cards with modern styling */
.search-card {
  background: white !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 20px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06) !important;
  overflow: hidden !important;
  margin-bottom: 2rem !important;
  transition: all 0.3s ease !important;
}

.search-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12) !important;
  border-color: #3b82f6 !important;
}

.search-card-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  padding: 1.5rem 2rem !important;
  border-bottom: 1px solid #e2e8f0 !important;
  font-weight: 600 !important;
  color: #1e293b !important;
  font-size: 1.1rem !important;
}

.search-card-body {
  padding: 2rem !important;
}

/* Modern response styling */
.professional-response-container {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  border: 2px solid #e2e8f0 !important;
  border-radius: 20px !important;
  padding: 2.5rem !important;
  margin: 2rem 0 !important;
  position: relative !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08) !important;
}

.professional-response-container::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 6px !important;
  background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%) !important;
  border-radius: 20px 20px 0 0 !important;
}

.response-content {
  line-height: 1.8 !important;
  color: #1e293b !important;
  font-size: 1.1rem !important;
  font-weight: 400 !important;
}

.response-content p {
  margin-bottom: 1.5rem !important;
  text-align: left !important;
}

.response-content ul, .response-content ol {
  margin: 1.5rem 0 !important;
  padding-left: 2rem !important;
}

.response-content li {
  margin-bottom: 0.75rem !important;
  line-height: 1.7 !important;
}

.response-content strong {
  color: #0f172a !important;
  font-weight: 700 !important;
}

/* Enhanced confidence indicators */
.confidence-indicator {
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
  padding: 1rem 1.5rem !important;
  border-radius: 16px !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  margin-bottom: 2rem !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

.confidence-high {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%) !important;
  color: #166534 !important;
  border: 2px solid #bbf7d0 !important;
}

.confidence-medium {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
  color: #92400e !important;
  border: 2px solid #fde68a !important;
}

.confidence-low {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%) !important;
  color: #991b1b !important;
  border: 2px solid #fecaca !important;
}

/* Modern source section */
.professional-sources-section {
  margin-top: 4rem !important;
  padding-top: 3rem !important;
  border-top: 3px solid #e2e8f0 !important;
}

.sources-header {
  text-align: center !important;
  margin-bottom: 3rem !important;
}

.sources-title {
  color: #0f172a !important;
  font-weight: 700 !important;
  font-size: 2rem !important;
  margin-bottom: 1rem !important;
}

.sources-subtitle {
  color: #64748b !important;
  font-size: 1.1rem !important;
  font-weight: 400 !important;
}

.professional-sources-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
  gap: 2rem !important;
}

/* Ultra-modern source cards */
.professional-source-card {
  background: white !important;
  border: 2px solid #e2e8f0 !important;
  border-radius: 20px !important;
  overflow: hidden !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06) !important;
}

.professional-source-card:hover {
  transform: translateY(-8px) scale(1.02) !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
  border-color: #3b82f6 !important;
}

.professional-source-card.source-active {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2) !important;
  transform: translateY(-4px) !important;
}

.source-card-header {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
  padding: 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.citation-number-large {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  color: white !important;
  width: 50px !important;
  height: 50px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: 800 !important;
  font-size: 1.3rem !important;
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4) !important;
}

.source-card-body {
  padding: 2rem !important;
}

.source-document-title {
  color: #0f172a !important;
  font-weight: 700 !important;
  font-size: 1.2rem !important;
  margin-bottom: 1rem !important;
  line-height: 1.4 !important;
}

.source-content-preview {
  color: #475569 !important;
  line-height: 1.7 !important;
  font-size: 1rem !important;
  margin-bottom: 1.5rem !important;
}

.source-card-footer {
  padding: 1.5rem 2rem !important;
  background: #f8fafc !important;
  border-top: 1px solid #e2e8f0 !important;
}

.professional-source-link {
  color: #3b82f6 !important;
  text-decoration: none !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  transition: all 0.2s ease !important;
}

.professional-source-link:hover {
  color: #1d4ed8 !important;
  transform: translateX(4px) !important;
}

/* Enhanced response content styling */
.response-content h1,
.response-content h2,
.response-content h3,
.response-content h4,
.response-content h5,
.response-content h6 {
  color: #0f172a !important;
  font-weight: 700 !important;
  margin: 2rem 0 1rem 0 !important;
  line-height: 1.3 !important;
}

.response-content h1 { font-size: 2rem !important; }
.response-content h2 { font-size: 1.75rem !important; }
.response-content h3 { font-size: 1.5rem !important; }
.response-content h4 { font-size: 1.25rem !important; }

.response-content .response-list {
  margin: 1.5rem 0 !important;
  padding-left: 2rem !important;
}

.response-content .response-list-item {
  margin-bottom: 0.75rem !important;
  line-height: 1.7 !important;
  color: #374151 !important;
}

.response-content .response-emphasis {
  color: #0f172a !important;
  font-weight: 700 !important;
}

.response-content .response-italic {
  color: #4b5563 !important;
  font-style: italic !important;
}

/* Enhanced citations in response */
.response-content .citation-number {
  display: inline-block !important;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  color: white !important;
  font-size: 0.8rem !important;
  padding: 0.4rem 0.8rem !important;
  border-radius: 20px !important;
  text-decoration: none !important;
  margin-left: 0.5rem !important;
  font-weight: 700 !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.response-content .citation-number:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%) !important;
  transform: scale(1.1) translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4) !important;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .modern-search-container {
    padding: 1rem;
  }

  .search-header {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .professional-sources-grid {
    grid-template-columns: 1fr !important;
    gap: 1.5rem !important;
  }

  .search-card-header {
    padding: 1rem !important;
  }

  .search-card-body {
    padding: 1.5rem !important;
  }

  .professional-response-container {
    padding: 1.5rem !important;
  }

  .citation-number-large {
    width: 40px !important;
    height: 40px !important;
    font-size: 1.1rem !important;
  }
}
