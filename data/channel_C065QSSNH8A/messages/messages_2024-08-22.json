{"date": "2024-08-22", "channel_id": "C065QSSNH8A", "message_count": 8, "messages": [{"ts": "1724346473.518559", "text": "<@U07EJ2LP44S> you mentioned that you found some issues during cycle creation in customer call. Did you make a note of those by any chance? ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724346473.518559", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "V5U4r", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " you mentioned that you found some issues during cycle creation in customer call. Did you make a note of those by any chance"}, {"type": "text", "text": "?"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1724335378.025319", "text": "I did a quick mockup of a prioritization spreadsheet yesterday that I'd love to show you all as well.", "user": "U07HCJ07H7G", "type": "message", "thread_ts": "1724318481.479989", "subtype": "thread_broadcast", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "uzZJL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I did a quick mockup of a prioritization spreadsheet yesterday that I'd love to show you all as well."}]}]}]}, {"ts": "1724318481.479989", "text": "Agenda for today:\n• Review and finalize the target list of work until end of September.\n• How to approach ambiguous/unclear requirements - me and <@U04DKEFP1K8> have discussed on this already.\n• Discuss a bit about support for part time employees in the product\n• COLA feature release", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724318481.479989", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "1724319006.000000"}, "blocks": [{"type": "rich_text", "block_id": "gBLkX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for today:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Review and finalize the target list of work until end of September."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "How to approach ambiguous/unclear requirements - me and "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " have discussed on this already."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Discuss a bit about support for part time employees in the product"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "COLA feature release"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1724317113.453869", "text": "<@U04DKEFP1K8> Could you please help me create a <https://docs.aws.amazon.com/transfer/latest/userguide/create-server-sftp.html|SFTP server on AWS>?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724188121.586739", "subtype": "thread_broadcast", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "zJsii", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Could you please help me create a "}, {"type": "link", "url": "https://docs.aws.amazon.com/transfer/latest/userguide/create-server-sftp.html", "text": "SFTP server on AWS"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1724291921.820139", "text": "<@U07EJ2LP44S> sorry I should have updated. We have disabled reset entirely for non stride admin users.\n<https://stride-hr.slack.com/archives/C0702497X55/p**********653359|https://stride-hr.slack.com/archives/C0702497X55/p**********653359>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724291921.820139", "reply_count": 2, "attachments": [{"from_url": "https://stride-hr.slack.com/archives/C0702497X55/p**********653359", "ts": "**********.653359", "author_id": "U07EJ2LP44S", "channel_id": "C0702497X55", "channel_team": "T04DM97F1UM", "is_msg_unfurl": true, "message_blocks": [{"team": "T04DM97F1UM", "channel": "C0702497X55", "ts": "**********.653359", "message": {"blocks": [{"type": "rich_text", "block_id": "jPU/f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Quick follow up from our call yesterday; we are working on getting the Rippling integration set up. It will require 'Rippling App Management', which allows access to the app necessary to make the connection. Will this be feasible on your end?\n\nIn the meantime you can do a full account reset using the CSV templates. Let me know if you have any questions on that or run into any issues!"}]}]}]}}], "private_channel_prompt": true, "id": 1, "original_url": "https://stride-hr.slack.com/archives/C0702497X55/p**********653359", "fallback": "[August 13th, 2024 3:33 PM] amanda: Quick follow up from our call yesterday; we are working on getting the Rippling integration set up. It will require 'Rippling App Management', which allows access to the app necessary to make the connection. Will this be feasible on your end?\n\nIn the meantime you can do a full account reset using the CSV templates. Let me know if you have any questions on that or run into any issues!", "text": "Quick follow up from our call yesterday; we are working on getting the Rippling integration set up. It will require 'Rippling App Management', which allows access to the app necessary to make the connection. Will this be feasible on your end?\n\nIn the meantime you can do a full account reset using the CSV templates. Let me know if you have any questions on that or run into any issues!", "author_name": "<PERSON>", "author_link": "https://stride-hr.slack.com/team/U07EJ2LP44S", "author_icon": "https://avatars.slack-edge.com/2024-07-30/7497943231509_d34fe1f848690e857cca_48.png", "author_subname": "<PERSON>", "mrkdwn_in": ["text"], "footer": "Slack Conversation"}], "blocks": [{"type": "rich_text", "block_id": "I8xuX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " sorry I should have updated. We have disabled reset entirely for non stride admin users.\n"}, {"type": "link", "url": "https://stride-hr.slack.com/archives/C0702497X55/p**********653359", "text": "https://stride-hr.slack.com/archives/C0702497X55/p**********653359"}]}]}]}, {"ts": "**********.452749", "text": "<@U07EJ2LP44S> <@U04DS2MBWP4> i am working on adjustment letters for nauto, do you guys need me to join implementation standup?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.452749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "oHeyG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " i am working on adjustment letters for na<PERSON>, do you guys need me to join implementation standup?"}]}]}]}, {"ts": "1724272253.098129", "text": "<@U0690EB5JE5> <https://success.15five.com/hc/en-us/articles/4404196285339-Manage-data-in-15Five-using-SFTP>", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1724272253.098129", "reply_count": 3, "attachments": [{"from_url": "https://success.15five.com/hc/en-us/articles/4404196285339-Manage-data-in-15Five-using-SFTP", "service_icon": "https://success.15five.com/hc/theming_assets/01HZPQTR0PZ1MH6EEAJES12EH9", "id": 1, "original_url": "https://success.15five.com/hc/en-us/articles/4404196285339-Manage-data-in-15Five-using-SFTP", "fallback": "15Five Help Center: Manage data in 15Five using SFTP", "text": "Similar to uploading employee information via CSV or API, you can automatically manage employee data via SFTP using 15Five and your HRIS. SFTP (Secure File Transfer Protocol or SSH File Transfer Pr...", "title": "Manage data in 15Five using SFTP", "title_link": "https://success.15five.com/hc/en-us/articles/4404196285339-Manage-data-in-15Five-using-SFTP", "service_name": "15Five Help Center"}], "blocks": [{"type": "rich_text", "block_id": "BoiK9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://success.15five.com/hc/en-us/articles/4404196285339-Manage-data-in-15Five-using-SFTP"}]}]}]}, {"ts": "1724268434.581099", "text": "*Recommendation* - if you can spare some time on a drive or walk, I highly recommend *<https://www.lennysnewsletter.com/p/a-framework-for-finding-product-market|this podcast episode>* about the four stages of Product/Market Fit and what success means at each stage. I believe Stride is mostly in Level 1 right now, about to enter the beginning of Level 2 (out of 4 levels). How a startup prioritizes is highly dependent on the stage, especially for those first two levels.\n\nIf you don't have a full hour+, you could start at 9 minutes, and stop around 49 minutes.", "user": "U07HCJ07H7G", "type": "message", "edited": {"user": "U07HCJ07H7G", "ts": "1724268466.000000"}, "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"image_url": "https://substackcdn.com/image/fetch/w_1200,h_600,c_fill,f_jpg,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F23538ef0-2702-41c4-8083-2d54c4c479f9_2048x2048.png", "image_width": 1200, "image_height": 600, "image_bytes": 60657, "from_url": "https://www.lennysnewsletter.com/p/a-framework-for-finding-product-market", "service_icon": "https://substackcdn.com/image/fetch/f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fbucketeer-e05bbc84-baa3-437e-9518-adb32be77984.s3.amazonaws.com%2Fpublic%2Fimages%2Fc7cde267-8f9e-47fa-9aef-5be03bad95ed%2Fapple-touch-icon-57x57.png", "id": 1, "original_url": "https://www.lennysnewsletter.com/p/a-framework-for-finding-product-market", "fallback": "A framework for finding product-market fit | <PERSON> (First Round Capital)", "text": "<PERSON> on why product-market fit matters, how to get unstuck, and First Round Capital’s four-part PMF framework.", "title": "A framework for finding product-market fit | <PERSON> (First Round Capital)", "title_link": "https://www.lennysnewsletter.com/p/a-framework-for-finding-product-market", "service_name": "lennysnewsletter.com"}], "blocks": [{"type": "rich_text", "block_id": "dwBG2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Recommendation", "style": {"bold": true}}, {"type": "text", "text": " - if you can spare some time on a drive or walk, I highly recommend "}, {"type": "link", "url": "https://www.lennysnewsletter.com/p/a-framework-for-finding-product-market", "text": "this podcast episode", "style": {"bold": true}}, {"type": "text", "text": " about the four stages of Product/Market Fit and what success means at each stage. I believe Stride is mostly in Level 1 right now, about to enter the beginning of Level 2 (out of 4 levels). How a startup prioritizes is highly dependent on the stage, especially for those first two levels.\n\nIf you don't have a full hour+, you could start at 9 minutes, and stop around 49 minutes."}]}]}]}], "created_at": "2025-05-22T21:35:34.626553"}