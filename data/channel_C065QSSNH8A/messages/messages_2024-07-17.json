{"date": "2024-07-17", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "**********.373739", "text": "<@U04DS2MBWP4> Updates\n1. Microsoft login issue were resolved on production environment (verified with my microsoft id) by mahesh and me. \n2. Here is the analysis for pay band employee count issue. In the attached image application currently displays list of employees filtered by (department, job category, job level and  region)\n       but for correctly mapping the band to employees another identifier was used ( job title).  If job title is taken into account then you will see 7 accounting coordinator.  This should not be treated as a blocker.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.373739", "reply_count": 9, "files": [{"id": "F07CWEBUHGT", "created": **********, "timestamp": **********, "name": "Screenshot 2024-07-17 at 11.20.40 AM.png", "title": "Screenshot 2024-07-17 at 11.20.40 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 219483, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07CWEBUHGT/screenshot_2024-07-17_at_11.20.40___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07CWEBUHGT/download/screenshot_2024-07-17_at_11.20.40___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_360.png", "thumb_360_w": 360, "thumb_360_h": 193, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_480.png", "thumb_480_w": 480, "thumb_480_h": 258, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_720.png", "thumb_720_w": 720, "thumb_720_h": 386, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_800.png", "thumb_800_w": 800, "thumb_800_h": 429, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_960.png", "thumb_960_w": 960, "thumb_960_h": 515, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CWEBUHGT-168d6cdc5c/screenshot_2024-07-17_at_11.20.40___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 550, "original_w": 1852, "original_h": 994, "thumb_tiny": "AwAZADDQ5z93P40uT/dP50velFADcn+6fzoyf7p/OnUd6AG5P90/nQCc8rj8adR3oATvQKx6KANmjvWNRQBs0VjUUAf/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07CWEBUHGT/screenshot_2024-07-17_at_11.20.40___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07CWEBUHGT-16fb4ad1c2", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "kPFu9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Updates\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Microsoft login issue were resolved on production environment (verified with my microsoft id) by mahesh and me. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the analysis for pay band employee count issue. In the attached image application currently displays list of employees filtered by (department, job category, job level and  region)"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "       but for correctly mapping the band to employees another identifier was used ( job title).  If job title is taken into account then you will see 7 accounting coordinator.  This should not be treated as a blocker."}]}]}]}, {"ts": "**********.568319", "text": "zoom is not logging me in", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.568319", "reply_count": 14, "blocks": [{"type": "rich_text", "block_id": "ocke<PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "zoom is not logging me in"}]}]}]}, {"ts": "**********.781119", "text": "<@U0690EB5JE5> we still have the HR admin page showing up for CWA", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "e1zID", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we still have the HR admin page showing up for CWA"}]}]}]}, {"ts": "**********.157899", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> Current structure for storing customer login credentials is very confusing. Going forward can we please use this <https://docs.google.com/spreadsheets/d/1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q/edit?gid=0#gid=0|spreadsheet>. Let's also migrate the existing and active credentials of customers to this spreadsheet please.", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F07CVDTM7PD", "created": 1721230689, "timestamp": 1721230689, "name": "Customer <PERSON>gin Credentials", "title": "Customer <PERSON>gin Credentials", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 11356, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q", "external_url": "https://docs.google.com/spreadsheets/d/1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q/edit?gid=0#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q/edit?gid=0#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07CVDTM7PD-9551370077/customer_login_credentials_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHTooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigD/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07CVDTM7PD/customer_login_credentials", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "qIVNE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Current structure for storing customer login credentials is very confusing. Going forward can we please use this "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1zsi77pePsY-wzGghUtyfGWOpMJv5455bjhk4-FO4n9Q/edit?gid=0#gid=0", "text": "spreadsheet"}, {"type": "text", "text": ". Let's also migrate the existing and active credentials of customers to this spreadsheet please."}]}]}]}, {"ts": "1721228535.140349", "text": "Might run 15 mnts late to eng sync up", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721228535.140349", "reply_count": 2, "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "DRfb7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Might run 15 mnts late to eng sync up"}]}]}]}, {"ts": "1721214828.916949", "text": "Org insights is Live", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1721214828.916949", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "J3pA3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Org insights is Live"}]}]}]}], "created_at": "2025-05-22T21:35:34.618955"}