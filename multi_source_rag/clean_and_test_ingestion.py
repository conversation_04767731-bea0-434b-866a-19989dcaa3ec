#!/usr/bin/env python
"""
Clean database and test ingestion with data quality and integrity checks.
"""

import os
import sys
import django
import logging
import json
from datetime import datetime
from pathlib import Path

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from django.db import transaction
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk, EmbeddingMetadata, DocumentContent
from apps.core.utils.vectorstore import get_qdrant_client
from apps.documents.interfaces.local_slack import LocalSlackSourceInterface
from apps.documents.services.ingestion_service import IngestionService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def clean_database():
    """Clean all data from database and vector store."""
    logger.info("🧹 Starting database cleanup...")

    try:
        # Clean PostgreSQL
        with transaction.atomic():
            DocumentChunk.objects.all().delete()
            RawDocument.objects.all().delete()
            DocumentSource.objects.all().delete()
            EmbeddingMetadata.objects.all().delete()
            DocumentContent.objects.all().delete()

        logger.info("✅ PostgreSQL cleaned")

        # Clean Qdrant
        try:
            client = get_qdrant_client()
            collections = client.get_collections()
            for collection in collections.collections:
                client.delete_collection(collection.name)
                logger.info(f"Deleted Qdrant collection: {collection.name}")
        except Exception as e:
            logger.warning(f"Could not clean Qdrant: {e}")

        logger.info("✅ Database cleanup completed")

    except Exception as e:
        logger.error(f"❌ Database cleanup failed: {e}")
        raise


def create_test_tenant():
    """Create a test tenant for ingestion."""
    logger.info("👤 Creating test tenant...")

    tenant, created = Tenant.objects.get_or_create(
        slug="test-ingestion",
        defaults={
            "name": "Test Ingestion Tenant",
            "config": {"purpose": "testing data ingestion quality and integrity"}
        }
    )

    if created:
        logger.info(f"✅ Created new tenant: {tenant.name}")
    else:
        logger.info(f"✅ Using existing tenant: {tenant.name}")

    return tenant


def create_document_source(tenant):
    """Create a document source for local Slack data."""
    logger.info("📄 Creating document source...")

    # Check available data
    data_dir = Path("../data/consolidated")
    if not data_dir.exists():
        data_dir = Path("data/consolidated")

    if not data_dir.exists():
        raise FileNotFoundError("Could not find consolidated data directory")

    json_files = list(data_dir.glob("*.json"))
    logger.info(f"Found {len(json_files)} consolidated data files")

    source, created = DocumentSource.objects.get_or_create(
        tenant=tenant,
        name="Slack Local Data - Quality Test",
        defaults={
            "source_type": "local_slack",
            "config": {
                "data_dir": str(data_dir.parent.absolute()),  # Point to parent of consolidated
                "channel_id": "C065QSSNH8A",
                "aggregation_strategy": "monthly",
                "include_threads": True,
                "filter_bots": True,
                "purpose": "quality and integrity testing"
            }
        }
    )

    if created:
        logger.info(f"✅ Created new document source: {source.name}")
    else:
        logger.info(f"✅ Using existing document source: {source.name}")

    return source


def test_data_ingestion(tenant, source):
    """Test data ingestion with quality and integrity checks."""
    logger.info("🔄 Starting data ingestion test...")

    try:
        # Create ingestion service
        ingestion_service = IngestionService(tenant=tenant)

        # Process the source
        logger.info("Processing source with IngestionService...")
        processed, failed = ingestion_service.process_source(source)

        logger.info(f"✅ Ingestion completed: {processed} documents processed, {failed} documents failed")

        # Data quality checks
        perform_data_quality_checks(tenant, source)

        return processed, failed

    except Exception as e:
        logger.error(f"❌ Ingestion failed: {str(e)}")
        raise


def perform_data_quality_checks(tenant, source):
    """Perform comprehensive data quality and integrity checks."""
    logger.info("🔍 Performing data quality checks...")

    # Check PostgreSQL data
    raw_docs = RawDocument.objects.filter(source=source)
    chunks = DocumentChunk.objects.filter(document__source=source)

    logger.info(f"📊 PostgreSQL Stats:")
    logger.info(f"  - Raw Documents: {raw_docs.count()}")
    logger.info(f"  - Document Chunks: {chunks.count()}")

    # Check for data integrity
    docs_without_chunks = raw_docs.filter(chunks__isnull=True)
    if docs_without_chunks.exists():
        logger.warning(f"⚠️  Found {docs_without_chunks.count()} documents without chunks")

    chunks_without_embeddings = chunks.filter(embedding__isnull=True)
    if chunks_without_embeddings.exists():
        logger.warning(f"⚠️  Found {chunks_without_embeddings.count()} chunks without embeddings")

    # Check Qdrant data
    try:
        client = get_qdrant_client()
        collections = client.get_collections()

        logger.info(f"🔍 Qdrant Stats:")
        for collection in collections.collections:
            info = client.get_collection(collection.name)
            logger.info(f"  - Collection {collection.name}: {info.points_count} points")

    except Exception as e:
        logger.error(f"❌ Could not check Qdrant: {e}")

    # Sample data quality check
    sample_chunks = chunks[:5]
    logger.info(f"📝 Sample chunk analysis:")
    for chunk in sample_chunks:
        logger.info(f"  - Chunk {chunk.id}: {len(chunk.text)} chars, metadata: {bool(chunk.metadata)}")

    logger.info("✅ Data quality checks completed")


def main():
    """Main function to run the complete test."""
    logger.info("🚀 Starting comprehensive ingestion test...")

    try:
        # Phase 1: Clean slate
        clean_database()

        # Phase 2: Setup
        tenant = create_test_tenant()
        source = create_document_source(tenant)

        # Phase 3: Ingestion and testing
        processed, failed = test_data_ingestion(tenant, source)

        logger.info("🎉 Ingestion test completed successfully!")
        logger.info(f"📊 Final Stats: {processed} processed, {failed} failed")

        return True

    except Exception as e:
        logger.error(f"💥 Test failed: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
