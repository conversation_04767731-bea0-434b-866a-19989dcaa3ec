{"date": "2024-08-01", "channel_id": "C065QSSNH8A", "message_count": 7, "messages": [{"ts": "**********.588909", "text": "<@U07EJ2LP44S> <@U04DS2MBWP4> Paycor integration requirement\n<https://help.merge.dev/en/articles/7183529-paycor-how-do-i-link-my-account>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "attachments": [{"image_url": "https://downloads.intercomcdn.com/i/o/379082/107841514fe57a3f0b2a855a/065e08fde79f8a1ee25eb6318655bdc9.jpg", "image_width": 1200, "image_height": 628, "image_bytes": 74161, "from_url": "https://help.merge.dev/en/articles/7183529-paycor-how-do-i-link-my-account", "id": 1, "original_url": "https://help.merge.dev/en/articles/7183529-paycor-how-do-i-link-my-account", "fallback": "Paycor - How do I link my account? | Merge Help Center", "text": "How to link your Paycor account", "title": "Paycor - How do I link my account? | Merge Help Center", "title_link": "https://help.merge.dev/en/articles/7183529-paycor-how-do-i-link-my-account", "service_name": "help.merge.dev"}], "blocks": [{"type": "rich_text", "block_id": "5OSRq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Paycor integration requirement\n"}, {"type": "link", "url": "https://help.merge.dev/en/articles/7183529-paycor-how-do-i-link-my-account"}]}]}]}, {"ts": "**********.647649", "text": "I am not ready to demo yet; I'm going to do a test cycle hopefully by Friday and then I will be in better shape.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "ack", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am not ready to demo yet; I'm going to do a test cycle hopefully by Friday and then I will be in better shape."}]}]}]}, {"ts": "**********.747909", "text": "<!here> here are my 2 cents for call with <PERSON>. Please suggest if you want to add anything.\n\n1. Intros (2 mins)\n2. Product demo (15 mins) <@U07EJ2LP44S> are you comfortable with giving the product demo? If you are not up to speed on it, then either <@U04DKEFP1K8> or I can do that\n3. Product Roadmap themes  (integrations + benchmarking, manager enablement, customer feedback/bugs, Self-Service capabilities)(10 mins) -Kapil\n4. PRDs/Docs overview (People Insights, Total Rewards, Manager Communication for Manager Enablement theme (10 mins)\n5. Jira process for development (10 mins) (<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>h)\n6. Current challenges (10 mins) -Everyone\n7. Next steps (5 min)", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RrGW0", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " here are my 2 cents for call with <PERSON>. Please suggest if you want to add anything.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Intros (2 mins)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Product demo (15 mins) "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " are you comfortable with giving the product demo? If you are not up to speed on it, then either "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " or I can do that"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Product Roadmap themes  (integrations + benchmarking, manager enablement, customer feedback/bugs, Self-Service capabilities)(10 mins) -Kapil"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "PRDs/Docs overview (People Insights, Total Rewards, Manager Communication for Manager Enablement theme (10 mins)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Jira process for development (10 mins) (<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Current challenges (10 mins) -Everyone"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Next steps (5 min)"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "1722457580.222469", "text": "<@U04DKEFP1K8>  Rightway call is set up for 9 am on Tue next week. Can we pls move the eng meeting to 10 am and make it optional for <@U0690EB5JE5> ?", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1722457591.000000"}, "reactions": [{"name": "done", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "IYf9m", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": "  Rightway call is set up for 9 am on Tue next week. Can we pls move the eng meeting to 10 am and make it optional for "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " ?"}]}]}]}, {"ts": "1722456398.913079", "text": "<PERSON><PERSON><PERSON> is prioritizing integrations now and we should be able to sync data with Paycom.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722456398.913079", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "0NBWu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> is prioritizing integrations now and we should be able to sync data with Paycom."}]}]}]}, {"ts": "1722456066.677389", "text": "Where did we end up on a Paycom integration (listening to <PERSON><PERSON><PERSON><PERSON>'s call)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722456066.677389", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "6Snta", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Where did we end up on a Paycom integration (listening to <PERSON><PERSON><PERSON><PERSON>'s call)"}]}]}]}, {"ts": "1722450733.952199", "text": "<@U04DKEFP1K8> great job in handling vercara questions!", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "CUDaA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " great job in handling vercara questions!"}]}]}]}], "created_at": "2025-05-22T21:35:34.634256"}