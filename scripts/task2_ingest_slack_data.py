#!/usr/bin/env python
"""
Task 2: Ingest Slack Data and Check Quality

This script:
1. Uses real IngestionService with LocalSlackSourceInterface
2. Ingests data from data/consolidated/ folder
3. Checks data consistency and quality
4. Verifies vector database and PostgreSQL consistency
5. Uses production-ready services end-to-end
"""

import os
import sys
import django
import logging
from datetime import datetime
from pathlib import Path

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv('/Users/<USER>/Desktop/RAGSearch/multi_source_rag/.env')

django.setup()

from django.contrib.auth.models import User
from django.db import transaction
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk, DocumentProcessingJob
from apps.documents.services.ingestion_service import IngestionService
from apps.core.utils.vectorstore import get_qdrant_client

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_data_availability():
    """Check what Slack data is available for ingestion."""
    print("📊 Checking available Slack data...")

    data_dir = Path("/Users/<USER>/Desktop/RAGSearch/data")
    consolidated_dir = data_dir / "consolidated"

    if not consolidated_dir.exists():
        print(f"❌ Consolidated data directory not found: {consolidated_dir}")
        return False

    # List available files
    json_files = list(consolidated_dir.glob("*.json"))
    print(f"  Found {len(json_files)} consolidated files:")

    total_size = 0
    for file in sorted(json_files):
        size = file.stat().st_size
        total_size += size
        print(f"    {file.name} ({size / 1024:.1f} KB)")

    print(f"  Total data size: {total_size / (1024 * 1024):.1f} MB")
    return len(json_files) > 0


def setup_tenant_and_user():
    """Setup or get tenant and user for ingestion."""
    print("👤 Setting up tenant and user...")

    # Get or create tenant
    tenant, created = Tenant.objects.get_or_create(
        slug='test-tenant',
        defaults={
            'name': 'Test Tenant',
            'description': 'Tenant for testing Slack data ingestion'
        }
    )

    if created:
        print(f"  ✅ Created new tenant: {tenant.name}")
    else:
        print(f"  ✅ Using existing tenant: {tenant.name}")

    # Get or create user
    user, created = User.objects.get_or_create(
        username='test-user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )

    if created:
        print(f"  ✅ Created new user: {user.username}")
    else:
        print(f"  ✅ Using existing user: {user.username}")

    return tenant, user


def create_document_source(tenant):
    """Create a document source for local Slack data."""
    print("📄 Creating document source...")

    # Configuration for LocalSlackSourceInterface
    # The interface expects data in channel_C065QSSNH8A/messages/ structure
    config = {
        "data_dir": "/Users/<USER>/Desktop/RAGSearch/data/",
        "channel": "C065QSSNH8A",  # Specify the channel ID
        "time_period": "monthly",
        "custom_days": 730,  # Load 2 years of data
        "enable_semantic_cross_refs": True,
        "quality_threshold": 0.1,  # Lower threshold to include more content
        "max_documents_per_channel": 5000,  # Increase limit
        "include_threads": True,
        "filter_bots": True,
        "summarize_documents": False,
    }

    # Create or get document source
    source, created = DocumentSource.objects.get_or_create(
        tenant=tenant,
        name="Slack Local Data - Consolidated",
        source_type="local_slack",
        defaults={
            'config': config,
            'is_active': True
        }
    )

    if created:
        print(f"  ✅ Created new document source: {source.name}")
    else:
        print(f"  ✅ Using existing document source: {source.name}")
        # Update config in case it changed
        source.config = config
        source.save()
        print(f"  ✅ Updated source configuration")

    return source


def check_existing_data(tenant):
    """Check what data already exists in the database."""
    print("🔍 Checking existing data...")

    # Check raw documents
    raw_docs = RawDocument.objects.filter(source__tenant=tenant)
    print(f"  Raw documents: {raw_docs.count()}")

    # Check document chunks
    chunks = DocumentChunk.objects.filter(document__source__tenant=tenant)
    print(f"  Document chunks: {chunks.count()}")

    # Check vector database
    try:
        client = get_qdrant_client()
        collections = client.get_collections()
        print(f"  Qdrant collections: {len(collections.collections)}")

        for collection in collections.collections:
            if 'test-tenant' in collection.name:
                info = client.get_collection(collection.name)
                print(f"    {collection.name}: {info.points_count} vectors")
    except Exception as e:
        print(f"  ❌ Error checking Qdrant: {e}")

    return raw_docs.count(), chunks.count()


def clean_existing_data(tenant, confirm=False):
    """Clean existing data if requested."""
    if not confirm:
        return

    print("🧹 Cleaning existing data...")

    with transaction.atomic():
        # Delete document chunks first (foreign key constraints)
        chunks_deleted = DocumentChunk.objects.filter(document__source__tenant=tenant).delete()[0]
        print(f"  ✅ Deleted {chunks_deleted} document chunks")

        # Delete raw documents
        docs_deleted = RawDocument.objects.filter(source__tenant=tenant).delete()[0]
        print(f"  ✅ Deleted {docs_deleted} raw documents")

        # Clean vector database
        try:
            client = get_qdrant_client()
            collections = client.get_collections()

            for collection in collections.collections:
                if 'test-tenant' in collection.name:
                    client.delete_collection(collection.name)
                    print(f"  ✅ Deleted Qdrant collection: {collection.name}")
        except Exception as e:
            print(f"  ⚠️  Error cleaning Qdrant: {e}")


def ingest_slack_data(tenant, user, source):
    """Ingest Slack data using the real IngestionService."""
    print("📥 Starting Slack data ingestion...")

    # Create ingestion service
    ingestion_service = IngestionService(tenant, user)

    # Create processing job
    job = ingestion_service.create_processing_job(source)
    print(f"  ✅ Created processing job: {job.id}")

    # Start ingestion
    start_time = datetime.now()

    try:
        processed, failed = ingestion_service.process_source(
            source=source,
            job=job,
            batch_size=50,  # Process in smaller batches for better monitoring
        )

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        print(f"  ✅ Ingestion completed in {duration:.1f} seconds")
        print(f"  📊 Results: {processed} processed, {failed} failed")

        return processed, failed, job

    except Exception as e:
        print(f"  ❌ Ingestion failed: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0, job


def verify_data_quality(tenant):
    """Verify the quality and consistency of ingested data."""
    print("🔍 Verifying data quality and consistency...")

    # Check PostgreSQL data
    raw_docs = RawDocument.objects.filter(source__tenant=tenant)
    chunks = DocumentChunk.objects.filter(document__source__tenant=tenant)

    print(f"  PostgreSQL:")
    print(f"    Raw documents: {raw_docs.count()}")
    print(f"    Document chunks: {chunks.count()}")

    # Check vector database
    try:
        client = get_qdrant_client()
        collections = client.get_collections()

        total_vectors = 0
        print(f"  Qdrant:")

        for collection in collections.collections:
            if 'test-tenant' in collection.name:
                info = client.get_collection(collection.name)
                total_vectors += info.points_count
                print(f"    {collection.name}: {info.points_count} vectors")

        print(f"    Total vectors: {total_vectors}")

        # Check consistency
        if chunks.count() == total_vectors:
            print(f"  ✅ Data consistency: PostgreSQL chunks ({chunks.count()}) = Qdrant vectors ({total_vectors})")
        else:
            print(f"  ⚠️  Data inconsistency: PostgreSQL chunks ({chunks.count()}) ≠ Qdrant vectors ({total_vectors})")

        return True

    except Exception as e:
        print(f"  ❌ Error verifying Qdrant: {e}")
        return False


def analyze_data_quality(tenant):
    """Analyze the quality of ingested data."""
    print("📊 Analyzing data quality...")

    # Analyze raw documents
    raw_docs = RawDocument.objects.filter(source__tenant=tenant)

    if raw_docs.count() == 0:
        print("  ❌ No raw documents found")
        return

    # Sample some documents for analysis
    sample_docs = raw_docs[:5]

    print(f"  Sample documents:")
    for doc in sample_docs:
        content_length = len(doc.raw_content) if hasattr(doc, 'raw_content') and doc.raw_content else 0
        print(f"    {doc.title[:50]}... ({content_length} chars)")

    # Analyze chunks
    chunks = DocumentChunk.objects.filter(document__source__tenant=tenant)

    if chunks.count() > 0:
        avg_chunk_size = sum(len(chunk.text) for chunk in chunks[:100]) / min(100, chunks.count())
        print(f"  Average chunk size: {avg_chunk_size:.0f} characters")

        # Check for empty chunks
        empty_chunks = chunks.filter(text="").count()
        if empty_chunks > 0:
            print(f"  ⚠️  Found {empty_chunks} empty chunks")
        else:
            print(f"  ✅ No empty chunks found")


def main():
    """Main function to run Task 2."""
    print("🚀 Task 2: Ingest Slack Data and Check Quality\n")

    # Step 1: Check data availability
    if not check_data_availability():
        print("❌ No data available for ingestion")
        return False

    # Step 2: Setup tenant and user
    tenant, user = setup_tenant_and_user()

    # Step 3: Create document source
    source = create_document_source(tenant)

    # Step 4: Check existing data
    existing_docs, existing_chunks = check_existing_data(tenant)

    # Step 5: Clean existing data if requested
    if existing_docs > 0 or existing_chunks > 0:
        response = input(f"\nFound existing data ({existing_docs} docs, {existing_chunks} chunks). Clean it? (y/N): ")
        if response.lower() == 'y':
            clean_existing_data(tenant, confirm=True)

    # Step 6: Ingest data
    processed, failed, job = ingest_slack_data(tenant, user, source)

    if processed == 0:
        print("❌ No data was processed")
        return False

    # Step 7: Verify data quality
    consistency_ok = verify_data_quality(tenant)

    # Step 8: Analyze data quality
    analyze_data_quality(tenant)

    # Summary
    print(f"\n📋 Task 2 Summary:")
    print(f"  ✅ Documents processed: {processed}")
    print(f"  ❌ Documents failed: {failed}")
    print(f"  {'✅' if consistency_ok else '❌'} Data consistency: {'OK' if consistency_ok else 'Issues found'}")
    print(f"  📊 Processing job ID: {job.id}")

    return processed > 0 and consistency_ok


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
