{"date": "2024-01-05", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1704395782.521809", "text": "Finally back in the states y'all", "user": "U0658EW4B8D", "type": "message", "thread_ts": "1704395782.521809", "reply_count": 3, "reactions": [{"name": "+1", "users": ["U065H3M6WJV", "U04DS2MBWP4", "U04DKEFP1K8"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "r4lv3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Finally back in the states y'all"}]}]}]}, {"ts": "1704393126.288799", "text": "Had a check-in with DA this morning:\n• They had layoffs in December. :scream: \n    ◦ Affected about 25 employees. <PERSON> will update the EmployeeDataTemplate to mark these employees as \"Inactive.\" <@U04DKEFP1K8> Can you confirm that doing this will correctly prevent those employees from being shown when we re-load their data into production?\n• Comp cycle schedule hasn't changed. :spiral_calendar_pad: \n    ◦ Budget is supposed to be finalized next week on Jan 12\n• Captured some of their requirements/wish list that might not be implemented yet:\n    ◦ In addition to flagging individual employees outside of salary guideline ranges, they want a global flag for any employee with a salary increase over 9% and one for any employe with a bonus over 9%, because Emnet always reviews these cases individually.\n    ◦ Reports: Budget use &amp; which departments were under budget; reports to check for \"disparate impact\" by gender (we won't support \"age\" reports this cycle); discrepancies between performance ratings and increase %s", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1704393126.288799", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "XfeHn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Had a check-in with DA this morning:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They had layoffs in December. "}, {"type": "emoji", "name": "scream", "unicode": "1f631"}, {"type": "text", "text": " "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Affected about 25 employees. <PERSON> will update the EmployeeDataTemplate to mark these employees as \"Inactive.\" "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Can you confirm that doing this will correctly prevent those employees from being shown when we re-load their data into production?"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Comp cycle schedule hasn't changed. "}, {"type": "emoji", "name": "spiral_calendar_pad", "unicode": "1f5d3-fe0f"}, {"type": "text", "text": " "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Budget is supposed to be finalized next week on Jan 12"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Captured some of their requirements/wish list that might not be implemented yet:"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In addition to flagging individual employees outside of salary guideline ranges, they want a global flag for any employee with a salary increase over 9% and one for any employe with a bonus over 9%, because Emnet always reviews these cases individually."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Reports: Budget use & which departments were under budget; reports to check for \"disparate impact\" by gender (we won't support \"age\" reports this cycle); discrepancies between performance ratings and increase %s"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.594765"}