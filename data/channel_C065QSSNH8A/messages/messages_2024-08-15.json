{"date": "2024-08-15", "channel_id": "C065QSSNH8A", "message_count": 2, "messages": [{"ts": "1723743055.227639", "text": "<!here> engg team ( na<PERSON> and muke<PERSON>) are looking into the issue now. I will keep this thread updated as i receive more updates from them ", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1723743055.227639", "reply_count": 3, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "yoERx", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " engg team ( na<PERSON> and muke<PERSON>) are looking into the issue now. I will keep this thread updated as i receive more updates from them "}]}]}]}, {"ts": "1723729593.158479", "text": "Good morning. I woke up under the weather today as well. I just packed up but will be a little too late for engineering call. If I am <PERSON><PERSON><PERSON><PERSON> let me know. otherwise I’ll be back shortly. After ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723729593.158479", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "vXJxG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Good morning. I woke up under the weather today as well. I just packed up but will be a little too late for engineering call. If I am <PERSON><PERSON><PERSON><PERSON> let me know. otherwise I’ll be back shortly. After "}]}]}]}], "created_at": "2025-05-22T21:35:34.629147"}