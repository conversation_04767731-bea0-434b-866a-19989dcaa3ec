{"date": "2024-12-23", "channel_id": "C065QSSNH8A", "message_count": 14, "messages": [{"ts": "1734976510.751069", "text": "<@U0690EB5JE5> VG has an extremely large number of approvers. Is there any way to upload a role list so I do not have to manually change the roles on these 65 employees? We will also need to add the HRBP roles but that should only be 3 people.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734976510.751069", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "LQH8K", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " VG has an extremely large number of approvers. Is there any way to upload a role list so I do not have to manually change the roles on these 65 employees? We will also need to add the HRBP roles but that should only be 3 people."}]}]}]}, {"ts": "1734975090.045959", "text": "<@U0690EB5JE5> There is  a cycle creation bug in VG env. I got a 'cannot fetch initial filters' error when I loaded the Eligibility Rules page, ~and it won't allow me to add any filters.;~ it eventually loaded. I was able to move past the step but it took maybe 45 seconds to a minute to respond on each filter.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734975090.045959", "reply_count": 12, "edited": {"user": "U07EJ2LP44S", "ts": "1734975241.000000"}, "blocks": [{"type": "rich_text", "block_id": "Q85xd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " There is  a cycle creation bug in VG env. I got a 'cannot fetch initial filters' error when I loaded the Eligibility Rules page, "}, {"type": "text", "text": "and it won't allow me to add any filters.; ", "style": {"strike": true}}, {"type": "text", "text": "it eventually loaded. I was able to move past the step but it took maybe 45 seconds to a minute to respond on each filter."}]}]}]}, {"ts": "1734969590.240969", "text": "and will be avaiable if we need to meet or discuss anything", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "OfW72", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "and will be avaiable if we need to meet or discuss anything"}]}]}]}, {"ts": "1734969564.742109", "text": "<@U0690EB5JE5> I am whatsapp call away", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "C+amN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am whatsapp call away"}]}]}]}, {"ts": "1734969516.465339", "text": "My goal today is to finalize Val Genesis’s environment for testing", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "reply_count": 26, "blocks": [{"type": "rich_text", "block_id": "wvVPt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "My goal today is to finalize Val Genesis’s environment for testing"}]}]}]}, {"ts": "1734969484.110269", "text": "<@U07M6QKHUC9>?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yplc+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1734969461.874809", "text": "I am out until New Year’s, but I can get on calls if need be", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "CthgY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am out until New Year’s, but I can get on calls if need be"}]}]}]}, {"ts": "1734969422.516889", "text": "I am fine to cancel", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "E3vNP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am fine to cancel"}]}]}]}, {"ts": "1734969407.110969", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EsPNH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1734969401.825709", "text": "What will be availability of you both for rest of the week?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oNFG7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "What will be availability of you both for rest of the week?"}]}]}]}, {"ts": "1734969368.421829", "text": "And I don't see stand up invite until Friday. So our next regular stand up will be next Friday?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "USp7n", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And I don't see stand up invite until Friday. So our next regular stand up will be next Friday?"}]}]}]}, {"ts": "1734969332.895039", "text": "I am good too to cancel.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PyXxS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am good too to cancel."}]}]}]}, {"ts": "1734969293.292569", "text": "<@U0690EB5JE5> since we are meeting later in the eve, I am okay to cancel leadeship standup.\n<@U07EJ2LP44S> do you have any agenda items to discuss, if not, lets get this time back", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "h8lxn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " since we are meeting later in the eve, I am okay to cancel lead<PERSON><PERSON> standup.\n"}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " do you have any agenda items to discuss, if not, lets get this time back"}]}]}]}, {"ts": "1734951360.750719", "text": "Travel day for me today - will be working and online if anything comes up, but guessing the airport wifi wont be great for calls", "user": "U07NBMXTL1E", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "UYJs3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Travel day for me today - will be working and online if anything comes up, but guessing the airport wifi wont be great for calls"}]}]}]}], "created_at": "2025-05-22T21:35:34.698378"}