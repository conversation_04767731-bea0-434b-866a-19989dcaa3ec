# RAG Enhancement Implementation Guide

## Overview

This document provides detailed instructions for implementing advanced RAG features from RAGByLlamaIndex into the RAGSearch system. The goal is to enhance conversation-aware processing while maintaining RAGSearch's existing enterprise features like multi-tenancy, Django integration, and time-based document organization.

## Table of Contents

1. [Understanding Current RAGSearch Architecture](#understanding-current-ragsearch-architecture)
2. [Key Features to Implement](#key-features-to-implement)
3. [Implementation Plan](#implementation-plan)
4. [Detailed Implementation Steps](#detailed-implementation-steps)
5. [Testing Strategy](#testing-strategy)
6. [Performance Considerations](#performance-considerations)

## Understanding Current RAGSearch Architecture

### Current Data Flow
```
Slack Data Files → local_slack.py → Time-based Documents → Django Models → Vector Store
```

### Key Components
- **LocalSlackSourceInterface**: <PERSON>les reading staged Slack data
- **IngestionService**: Processes documents and creates chunks
- **DocumentChunk**: Django model for storing text chunks
- **Time-based Aggregation**: Groups messages by configurable time periods

### Current Strengths (DO NOT REMOVE)
- ✅ Multi-tenant architecture
- ✅ Django ORM integration
- ✅ Time-based document organization
- ✅ Quality assessment
- ✅ Enterprise features (permissions, audit logs)
- ✅ Web interface integration

## Key Features to Implement

### 1. Conversation-Aware Chunking
**Goal**: Instead of chunking by time periods only, also consider conversation boundaries and semantic relationships.

**Benefits**:
- Better context preservation
- Reduced information fragmentation
- More relevant search results

### 2. Enhanced Message Processing
**Goal**: Add semantic clustering and thread-aware processing to group related messages.

**Benefits**:
- Related discussions stay together
- Better handling of async conversations
- Improved topic coherence

### 3. Advanced Query Engine
**Goal**: Implement query expansion, context-aware retrieval, and better response synthesis.

**Benefits**:
- More accurate search results
- Better handling of follow-up questions
- Context-aware responses

### 4. Cross-Reference Detection
**Goal**: Identify and link semantically related documents across time periods.

**Benefits**:
- Discover related conversations
- Better information connectivity
- Enhanced knowledge discovery

## Implementation Plan

### Phase 1: Conversation-Aware Chunking (Week 1-2)
1. Create `ConversationAwareChunker` class
2. Implement thread-aware message grouping
3. Add semantic similarity detection
4. Integrate with existing `LocalSlackSourceInterface`

### Phase 2: Enhanced Message Processing (Week 3-4)
1. Create `EnhancedMessageProcessor` class
2. Implement conversation clustering
3. Add anti-fragmentation logic
4. Update document creation pipeline

### Phase 3: Advanced Query Engine (Week 5-6)
1. Create `ImprovedQueryEngine` class
2. Implement query expansion
3. Add context-aware retrieval
4. Integrate with existing RAG service

### Phase 4: Cross-Reference System (Week 7-8)
1. Implement semantic similarity calculation
2. Add cross-reference detection
3. Create reference linking system
4. Update document metadata

## Detailed Implementation Steps

### Phase 1: Conversation-Aware Chunking

#### Step 1.1: Create ConversationAwareChunker Class

Create a new file: `multi_source_rag/apps/documents/processors/conversation_chunker.py`

```python
import re
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class ConversationChunk:
    """Represents a conversation-aware chunk with metadata."""
    content: str
    messages: List[Dict[str, Any]]
    start_time: datetime
    end_time: datetime
    participants: List[str]
    thread_count: int
    topics: List[str]
    quality_score: float
    chunk_type: str  # 'conversation', 'thread', 'temporal'

class ConversationAwareChunker:
    """
    Advanced chunker that creates chunks based on conversation boundaries
    rather than just time periods or character counts.
    """
    
    def __init__(self, 
                 max_chunk_size: int = 2000,
                 overlap_size: int = 200,
                 max_time_gap_minutes: int = 30,
                 min_chunk_size: int = 100,
                 preserve_threads: bool = True):
        """
        Initialize the conversation-aware chunker.
        
        Args:
            max_chunk_size: Maximum characters per chunk
            overlap_size: Characters to overlap between chunks
            max_time_gap_minutes: Maximum time gap to consider messages related
            min_chunk_size: Minimum chunk size to avoid tiny chunks
            preserve_threads: Whether to keep thread conversations together
        """
        self.max_chunk_size = max_chunk_size
        self.overlap_size = overlap_size
        self.max_time_gap_minutes = max_time_gap_minutes
        self.min_chunk_size = min_chunk_size
        self.preserve_threads = preserve_threads
        
    def chunk_messages(self, messages: List[Dict[str, Any]]) -> List[ConversationChunk]:
        """
        Create conversation-aware chunks from messages.
        
        Args:
            messages: List of message dictionaries
            
        Returns:
            List of ConversationChunk objects
        """
        if not messages:
            return []
            
        # Step 1: Group messages by threads
        threaded_groups = self._group_by_threads(messages)
        
        # Step 2: Create conversation clusters
        conversation_clusters = []
        for thread_messages in threaded_groups:
            clusters = self._create_conversation_clusters(thread_messages)
            conversation_clusters.extend(clusters)
        
        # Step 3: Convert clusters to chunks
        chunks = []
        for cluster in conversation_clusters:
            chunk_list = self._cluster_to_chunks(cluster)
            chunks.extend(chunk_list)
            
        return chunks
    
    def _group_by_threads(self, messages: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Group messages by thread timestamp."""
        if not self.preserve_threads:
            return [messages]
            
        thread_groups = defaultdict(list)
        
        for msg in messages:
            thread_ts = msg.get('thread_ts', msg.get('timestamp', msg.get('ts')))
            thread_groups[thread_ts].append(msg)
        
        # Sort messages within each thread by timestamp
        for thread_ts in thread_groups:
            thread_groups[thread_ts].sort(key=lambda x: float(x.get('timestamp', x.get('ts', '0'))))
            
        return list(thread_groups.values())
    
    def _create_conversation_clusters(self, messages: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Create conversation clusters based on time gaps and semantic similarity."""
        if not messages:
            return []
            
        clusters = []
        current_cluster = [messages[0]]
        
        for i in range(1, len(messages)):
            prev_msg = messages[i-1]
            curr_msg = messages[i]
            
            # Calculate time gap
            prev_time = self._parse_timestamp(prev_msg.get('timestamp', prev_msg.get('ts', '0')))
            curr_time = self._parse_timestamp(curr_msg.get('timestamp', curr_msg.get('ts', '0')))
            time_gap_minutes = (curr_time - prev_time).total_seconds() / 60
            
            # Check if messages should be in same cluster
            if (time_gap_minutes <= self.max_time_gap_minutes and 
                self._are_messages_related(prev_msg, curr_msg)):
                current_cluster.append(curr_msg)
            else:
                # Start new cluster
                if current_cluster:
                    clusters.append(current_cluster)
                current_cluster = [curr_msg]
        
        # Add the last cluster
        if current_cluster:
            clusters.append(current_cluster)
            
        return clusters
    
    def _are_messages_related(self, msg1: Dict[str, Any], msg2: Dict[str, Any]) -> bool:
        """Check if two messages are semantically related."""
        # Simple keyword-based similarity (can be enhanced with embeddings)
        text1 = msg1.get('text', '').lower()
        text2 = msg2.get('text', '').lower()
        
        if not text1 or not text2:
            return True  # Default to related if no text
            
        # Check for common keywords
        words1 = set(re.findall(r'\w+', text1))
        words2 = set(re.findall(r'\w+', text2))
        
        # Calculate Jaccard similarity
        if not words1 or not words2:
            return True
            
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        similarity = intersection / union if union > 0 else 0
        
        # Consider messages related if similarity > 0.1 or same user
        return (similarity > 0.1 or 
                msg1.get('user') == msg2.get('user'))
    
    def _cluster_to_chunks(self, cluster: List[Dict[str, Any]]) -> List[ConversationChunk]:
        """Convert a conversation cluster into appropriately sized chunks."""
        if not cluster:
            return []
            
        # Create formatted content
        formatted_content = self._format_cluster_content(cluster)
        
        # If content fits in one chunk, return single chunk
        if len(formatted_content) <= self.max_chunk_size:
            return [self._create_chunk_from_cluster(cluster, formatted_content)]
        
        # Split into multiple chunks with overlap
        chunks = []
        start_idx = 0
        
        while start_idx < len(cluster):
            # Find messages that fit in current chunk
            chunk_messages = []
            chunk_content = ""
            
            for i in range(start_idx, len(cluster)):
                msg_content = self._format_message(cluster[i])
                potential_content = chunk_content + msg_content + "\n\n"
                
                if len(potential_content) <= self.max_chunk_size:
                    chunk_messages.append(cluster[i])
                    chunk_content = potential_content
                else:
                    break
            
            # Ensure minimum chunk size
            if not chunk_messages and start_idx < len(cluster):
                # Force include at least one message even if it exceeds max size
                chunk_messages = [cluster[start_idx]]
                chunk_content = self._format_message(cluster[start_idx])
            
            if chunk_messages:
                chunk = self._create_chunk_from_cluster(chunk_messages, chunk_content.strip())
                chunks.append(chunk)
                
                # Calculate overlap for next chunk
                overlap_msgs = max(1, len(chunk_messages) // 4)  # 25% overlap
                start_idx += len(chunk_messages) - overlap_msgs
            else:
                break
                
        return chunks
    
    def _format_cluster_content(self, cluster: List[Dict[str, Any]]) -> str:
        """Format cluster messages into readable content."""
        formatted_messages = []
        
        for msg in cluster:
            formatted_msg = self._format_message(msg)
            if formatted_msg:
                formatted_messages.append(formatted_msg)
        
        return "\n\n".join(formatted_messages)
    
    def _format_message(self, msg: Dict[str, Any]) -> str:
        """Format a single message."""
        timestamp = msg.get('timestamp', msg.get('ts', ''))
        user = msg.get('user_name', msg.get('user', 'Unknown'))
        text = msg.get('text', '')
        
        if not text:
            return ""
            
        # Format timestamp
        try:
            dt = self._parse_timestamp(timestamp)
            time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            time_str = timestamp
            
        return f"[{time_str}] {user}: {text}"
    
    def _create_chunk_from_cluster(self, messages: List[Dict[str, Any]], content: str) -> ConversationChunk:
        """Create a ConversationChunk from messages and content."""
        if not messages:
            raise ValueError("Cannot create chunk from empty messages")
            
        # Extract metadata
        timestamps = [self._parse_timestamp(msg.get('timestamp', msg.get('ts', '0'))) for msg in messages]
        participants = list(set(msg.get('user_name', msg.get('user', '')) for msg in messages))
        thread_count = len(set(msg.get('thread_ts') for msg in messages if msg.get('thread_ts')))
        
        # Simple topic extraction (can be enhanced)
        topics = self._extract_topics(content)
        
        # Calculate quality score
        quality_score = self._calculate_quality_score(messages, content)
        
        # Determine chunk type
        chunk_type = self._determine_chunk_type(messages)
        
        return ConversationChunk(
            content=content,
            messages=messages,
            start_time=min(timestamps),
            end_time=max(timestamps),
            participants=participants,
            thread_count=thread_count,
            topics=topics,
            quality_score=quality_score,
            chunk_type=chunk_type
        )
    
    def _parse_timestamp(self, timestamp: str) -> datetime:
        """Parse Slack timestamp to datetime."""
        try:
            return datetime.fromtimestamp(float(timestamp))
        except (ValueError, TypeError):
            return datetime.now()
    
    def _extract_topics(self, content: str) -> List[str]:
        """Extract topics from content (simplified implementation)."""
        # This can be enhanced with NLP libraries
        technical_terms = [
            'api', 'database', 'server', 'client', 'bug', 'feature',
            'release', 'deploy', 'test', 'code', 'review', 'merge'
        ]
        
        content_lower = content.lower()
        found_topics = [term for term in technical_terms if term in content_lower]
        return found_topics[:5]  # Limit to top 5 topics
    
    def _calculate_quality_score(self, messages: List[Dict[str, Any]], content: str) -> float:
        """Calculate quality score for the chunk."""
        score = 0.5  # Base score
        
        # Length factor
        if len(content) > 500:
            score += 0.2
        elif len(content) < 100:
            score -= 0.1
            
        # Participant diversity
        unique_users = len(set(msg.get('user') for msg in messages))
        if unique_users > 3:
            score += 0.2
        elif unique_users > 1:
            score += 0.1
            
        # Thread activity
        thread_msgs = [msg for msg in messages if msg.get('thread_ts')]
        if thread_msgs:
            score += 0.1
            
        return max(0.0, min(1.0, score))
    
    def _determine_chunk_type(self, messages: List[Dict[str, Any]]) -> str:
        """Determine the type of chunk based on message characteristics."""
        thread_msgs = [msg for msg in messages if msg.get('thread_ts')]
        
        if len(thread_msgs) == len(messages) and len(thread_msgs) > 1:
            return 'thread'
        elif len(set(msg.get('user') for msg in messages)) > 2:
            return 'conversation'
        else:
            return 'temporal'
```

#### Step 1.2: Integrate with LocalSlackSourceInterface

Update `multi_source_rag/apps/documents/interfaces/local_slack.py`:

```python
# Add these imports at the top
from ..processors.conversation_chunker import ConversationAwareChunker, ConversationChunk

# Add this method to LocalSlackSourceInterface class
def _create_conversation_aware_documents(self, messages: List[SlackMessage]) -> List[Dict[str, Any]]:
    """
    Create documents using conversation-aware chunking.
    
    Args:
        messages: List of SlackMessage objects
        
    Returns:
        List of document dictionaries
    """
    if not messages:
        return []
    
    # Convert SlackMessage objects to dictionaries for chunker
    message_dicts = []
    for msg in messages:
        message_dicts.append({
            'text': msg.text,
            'user': msg.user,
            'user_name': msg.user_name,
            'timestamp': msg.timestamp,
            'thread_ts': msg.thread_ts,
            'ts': msg.timestamp,
            'channel': msg.channel,
            'reactions': msg.reactions,
            'reply_count': msg.reply_count,
            'files': msg.files,
            'edited': msg.edited,
            'subtype': msg.subtype
        })
    
    # Initialize conversation-aware chunker
    chunker = ConversationAwareChunker(
        max_chunk_size=self.config.get('conversation_chunk_size', 2000),
        overlap_size=self.config.get('conversation_overlap_size', 200),
        max_time_gap_minutes=self.config.get('max_conversation_gap_minutes', 30),
        preserve_threads=True
    )
    
    # Create conversation chunks
    conversation_chunks = chunker.chunk_messages(message_dicts)
    
    # Convert to documents
    documents = []
    for i, chunk in enumerate(conversation_chunks):
        # Create document ID
        doc_id = f"slack_{self.channel_id}_conv_{i}_{hash(chunk.content)}"
        
        # Create title based on chunk type and participants
        if chunk.chunk_type == 'thread':
            title = f"Thread Discussion - {', '.join(chunk.participants[:3])}"
        elif chunk.chunk_type == 'conversation':
            title = f"Conversation - {', '.join(chunk.participants[:3])}"
        else:
            title = f"Messages - {chunk.start_time.strftime('%Y-%m-%d %H:%M')}"
        
        # Create enhanced metadata
        metadata = {
            'channel_id': self.channel_id,
            'channel_name': self.get_channel_info(self.channel_id).get('name', ''),
            'chunk_type': chunk.chunk_type,
            'conversation_aware': True,
            'start_time': chunk.start_time.isoformat(),
            'end_time': chunk.end_time.isoformat(),
            'participants': chunk.participants,
            'participant_count': len(chunk.participants),
            'message_count': len(chunk.messages),
            'thread_count': chunk.thread_count,
            'topics': chunk.topics,
            'quality_score': chunk.quality_score,
            'chunking_strategy': 'conversation_aware',
            'duration_minutes': (chunk.end_time - chunk.start_time).total_seconds() / 60
        }
        
        # Create document
        document = {
            'id': doc_id,
            'title': title,
            'content': chunk.content,
            'metadata': metadata,
            'url': f"https://slack.com/archives/{self.channel_id}/p{chunk.messages[0]['timestamp'].replace('.', '')}",
            'content_type': 'text/slack'
        }
        
        documents.append(document)
    
    return documents

# Update the fetch_documents method to support conversation-aware chunking
def fetch_documents(self, **kwargs) -> List[Dict[str, Any]]:
    """
    Fetch documents from local Slack JSON files.
    Now supports both time-based and conversation-aware chunking.
    """
    # ... existing code ...
    
    # Check if conversation-aware chunking is enabled
    use_conversation_chunking = kwargs.get('use_conversation_chunking', 
                                         self.config.get('use_conversation_chunking', False))
    
    if use_conversation_chunking:
        # Use conversation-aware chunking
        return self._create_conversation_aware_documents(messages)
    else:
        # Use existing time-based chunking
        return self._create_time_based_documents(messages, time_period, custom_days)
```

### Phase 2: Enhanced Message Processing

#### Step 2.1: Create EnhancedMessageProcessor

Create `multi_source_rag/apps/documents/processors/enhanced_message_processor.py`:

```python
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple, Set
from collections import defaultdict
from dataclasses import dataclass
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import DBSCAN
from sklearn.metrics.pairwise import cosine_similarity

logger = logging.getLogger(__name__)

@dataclass
class MessageCluster:
    """Represents a cluster of related messages."""
    messages: List[Dict[str, Any]]
    cluster_id: int
    topic: str
    start_time: datetime
    end_time: datetime
    participants: Set[str]
    quality_score: float
    cluster_type: str  # 'topic_based', 'thread_based', 'temporal'

class EnhancedMessageProcessor:
    """
    Enhanced message processor that groups messages using semantic similarity
    and conversation analysis, preventing fragmentation of related discussions.
    """
    
    def __init__(self,
                 max_cluster_size: int = 35,
                 max_cluster_duration_hours: float = 8.0,
                 temporal_window_minutes: int = 45,
                 semantic_threshold: float = 0.15,
                 min_cluster_size: int = 2):
        """
        Initialize enhanced message processor.
        
        Args:
            max_cluster_size: Maximum messages per cluster
            max_cluster_duration_hours: Maximum time span for a cluster
            temporal_window_minutes: Time window for considering messages related
            semantic_threshold: Threshold for semantic similarity clustering
            min_cluster_size: Minimum messages required for a cluster
        """
        self.max_cluster_size = max_cluster_size
        self.max_cluster_duration_hours = max_cluster_duration_hours
        self.temporal_window_minutes = temporal_window_minutes
        self.semantic_threshold = semantic_threshold
        self.min_cluster_size = min_cluster_size
        
        # Initialize vectorizer for semantic analysis
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=1,
            max_df=0.95
        )
        
        # Processing statistics
        self.stats = {
            'messages_processed': 0,
            'clusters_created': 0,
            'fragmentation_prevented': 0,
            'processing_time_seconds': 0.0
        }
    
    def process_messages(self, messages: List[Dict[str, Any]]) -> List[MessageCluster]:
        """
        Process messages into semantically coherent clusters.
        
        Args:
            messages: List of message dictionaries
            
        Returns:
            List of MessageCluster objects
        """
        start_time = datetime.now()
        logger.info(f"Processing {len(messages)} messages with enhanced clustering...")
        
        if not messages:
            return []
        
        # Step 1: Pre-process and filter messages
        valid_messages = self._preprocess_messages(messages)
        
        # Step 2: Group by threads first (thread-based clustering)
        thread_clusters = self._cluster_by_threads(valid_messages)
        
        # Step 3: Apply semantic clustering to non-threaded messages
        semantic_clusters = self._apply_semantic_clustering(valid_messages)
        
        # Step 4: Apply temporal clustering for remaining messages
        temporal_clusters = self._apply_temporal_clustering(valid_messages)
        
        # Step 5: Merge and optimize clusters
        all_clusters = thread_clusters + semantic_clusters + temporal_clusters
        optimized_clusters = self._optimize_clusters(all_clusters)
        
        # Update statistics
        processing_time = (datetime.now() - start_time).total_seconds()
        self.stats.update({
            'messages_processed': len(messages),
            'clusters_created': len(optimized_clusters),
            'processing_time_seconds': processing_time
        })
        
        logger.info(f"Created {len(optimized_clusters)} clusters in {processing_time:.2f}s")
        return optimized_clusters
    
    def _preprocess_messages(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Preprocess and filter messages."""
        valid_messages = []
        
        for msg in messages:
            # Skip messages without text
            if not msg.get('text', '').strip():
                continue
                
            # Skip system messages
            if msg.get('subtype') in ['channel_join', 'channel_leave', 'channel_archive']:
                continue
                
            # Add processed timestamp
            try:
                msg['parsed_timestamp'] = datetime.fromtimestamp(float(msg.get('ts', '0')))
            except (ValueError, TypeError):
                msg['parsed_timestamp'] = datetime.now()
            
            # Clean and normalize text
            msg['clean_text'] = self._clean_text(msg['text'])
            
            valid_messages.append(msg)
        
        # Sort by timestamp
        valid_messages.sort(key=lambda x: x['parsed_timestamp'])
        return valid_messages
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize message text."""
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove mentions and channels
        text = re.sub(r'<@[A-Z0-9]+>', '', text)
        text = re.sub(r'<#[A-Z0-9]+\|[^>]+>', '', text)
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text.lower()
    
    def _cluster_by_threads(self, messages: List[Dict[str, Any]]) -> List[MessageCluster]:
        """Cluster messages by thread relationships."""
        thread_groups = defaultdict(list)
        
        # Group messages by thread
        for msg in messages:
            thread_ts = msg.get('thread_ts')
            if thread_ts:
                thread_groups[thread_ts].append(msg)
        
        # Create clusters from thread groups
        clusters = []
        for thread_ts, thread_messages in thread_groups.items():
            if len(thread_messages) >= self.min_cluster_size:
                cluster = self._create_cluster(
                    messages=thread_messages,
                    cluster_id=len(clusters),
                    cluster_type='thread_based'
                )
                clusters.append(cluster)
                
                # Remove these messages from further processing
                for msg in thread_messages:
                    if msg in messages:
                        messages.remove(msg)
        
        return clusters
    
    def _apply_semantic_clustering(self, messages: List[Dict[str, Any]]) -> List[MessageCluster]:
        """Apply semantic clustering to find topically related messages."""
        if len(messages) < self.min_cluster_size:
            return []
        
        # Extract text for vectorization
        texts = [msg['clean_text'] for msg in messages if msg['clean_text']]
        
        if not texts:
            return []
        
        try:
            # Create TF-IDF vectors
            tfidf_matrix = self.vectorizer.fit_transform(texts)
            
            # Apply DBSCAN clustering
            clustering = DBSCAN(
                eps=1 - self.semantic_threshold,  # Convert similarity to distance
                min_samples=self.min_cluster_size,
                metric='cosine'
            )
            
            cluster_labels = clustering.fit_predict(tfidf_matrix.toarray())
            
            # Group messages by cluster
            cluster_groups = defaultdict(list)
            for i, label in enumerate(cluster_labels):
                if label != -1:  # -1 is noise in DBSCAN
                    if i < len(messages):
                        cluster_groups[label].append(messages[i])
            
            # Create MessageCluster objects
            clusters = []
            for label, cluster_messages in cluster_groups.items():
                if len(cluster_messages) >= self.min_cluster_size:
                    cluster = self._create_cluster(
                        messages=cluster_messages,
                        cluster_id=len(clusters),
                        cluster_type='topic_based'
                    )
                    clusters.append(cluster)
                    
                    # Remove clustered messages from further processing
                    for msg in cluster_messages:
                        if msg in messages:
                            messages.remove(msg)
            
            return clusters
            
        except Exception as e:
            logger.error(f"Error in semantic clustering: {e}")
            return []
    
    def _apply_temporal_clustering(self, messages: List[Dict[str, Any]]) -> List[MessageCluster]:
        """Apply temporal clustering for remaining messages."""
        if not messages:
            return []
        
        clusters = []
        current_cluster = []
        
        for msg in messages:
            if not current_cluster:
                current_cluster.append(msg)
                continue
            
            # Check temporal proximity
            last_msg = current_cluster[-1]
            time_diff = msg['parsed_timestamp'] - last_msg['parsed_timestamp']
            
            if (time_diff.total_seconds() / 60 <= self.temporal_window_minutes and
                len(current_cluster) < self.max_cluster_size):
                current_cluster.append(msg)
            else:
                # Create cluster from current group
                if len(current_cluster) >= self.min_cluster_size:
                    cluster = self._create_cluster(
                        messages=current_cluster,
                        cluster_id=len(clusters),
                        cluster_type='temporal'
                    )
                    clusters.append(cluster)
                
                current_cluster = [msg]
        
        # Handle last cluster
        if len(current_cluster) >= self.min_cluster_size:
            cluster = self._create_cluster(
                messages=current_cluster,
                cluster_id=len(clusters),
                cluster_type='temporal'
            )
            clusters.append(cluster)
        
        return clusters
    
    def _create_cluster(self, messages: List[Dict[str, Any]], cluster_id: int, cluster_type: str) -> MessageCluster:
        """Create a MessageCluster from a group of messages."""
        if not messages:
            raise ValueError("Cannot create cluster from empty messages")
        
        # Sort messages by timestamp
        messages.sort(key=lambda x: x['parsed_timestamp'])
        
        # Extract metadata
        start_time = messages[0]['parsed_timestamp']
        end_time = messages[-1]['parsed_timestamp']
        participants = set(msg.get('user', '') for msg in messages)
        
        # Generate topic
        topic = self._generate_topic(messages)
        
        # Calculate quality score
        quality_score = self._calculate_cluster_quality(messages, cluster_type)
        
        return MessageCluster(
            messages=messages,
            cluster_id=cluster_id,
            topic=topic,
            start_time=start_time,
            end_time=end_time,
            participants=participants,
            quality_score=quality_score,
            cluster_type=cluster_type
        )
    
    def _generate_topic(self, messages: List[Dict[str, Any]]) -> str:
        """Generate a topic description for the cluster."""
        if not messages:
            return "Empty Cluster"
        
        # Extract key terms from messages
        all_text = ' '.join(msg.get('clean_text', '') for msg in messages)
        
        # Simple keyword extraction (can be enhanced with NLP)
        words = re.findall(r'\w+', all_text.lower())
        word_freq = defaultdict(int)
        
        # Count word frequencies, excluding common words
        stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 
                     'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
                     'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}
        
        for word in words:
            if len(word) > 3 and word not in stop_words:
                word_freq[word] += 1
        
        # Get top keywords
        top_keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:3]
        
        if top_keywords:
            return ' '.join([kw[0] for kw in top_keywords]).title()
        else:
            return f"Discussion - {messages[0]['parsed_timestamp'].strftime('%Y-%m-%d')}"
    
    def _calculate_cluster_quality(self, messages: List[Dict[str, Any]], cluster_type: str) -> float:
        """Calculate quality score for the cluster."""
        if not messages:
            return 0.0
        
        score = 0.5  # Base score
        
        # Size factor
        msg_count = len(messages)
        if 5 <= msg_count <= 20:
            score += 0.2
        elif msg_count > 20:
            score += 0.1
        elif msg_count < 3:
            score -= 0.1
        
        # Participant diversity
        participant_count = len(set(msg.get('user', '') for msg in messages))
        if participant_count > 3:
            score += 0.2
        elif participant_count > 1:
            score += 0.1
        
        # Time span factor
        time_span = messages[-1]['parsed_timestamp'] - messages[0]['parsed_timestamp']
        span_hours = time_span.total_seconds() / 3600
        
        if 0.5 <= span_hours <= 4:  # Good conversation length
            score += 0.1
        elif span_hours > 8:  # Too long, might be fragmented
            score -= 0.1
        
        # Cluster type bonus
        if cluster_type == 'thread_based':
            score += 0.1
        elif cluster_type == 'topic_based':
            score += 0.15
        
        # Content quality (presence of questions, reactions, etc.)
        has_questions = any('?' in msg.get('text', '') for msg in messages)
        has_reactions = any(msg.get('reactions') for msg in messages)
        
        if has_questions:
            score += 0.05
        if has_reactions:
            score += 0.05
        
        return max(0.0, min(1.0, score))
    
    def _optimize_clusters(self, clusters: List[MessageCluster]) -> List[MessageCluster]:
        """Optimize clusters by merging small ones and splitting large ones."""
        if not clusters:
            return []
        
        optimized = []
        
        for cluster in clusters:
            # Split oversized clusters
            if len(cluster.messages) > self.max_cluster_size:
                split_clusters = self._split_cluster(cluster)
                optimized.extend(split_clusters)
            else:
                optimized.append(cluster)
        
        # Try to merge small clusters with similar topics
        merged_clusters = self._merge_small_clusters(optimized)
        
        return merged_clusters
    
    def _split_cluster(self, cluster: MessageCluster) -> List[MessageCluster]:
        """Split an oversized cluster into smaller ones."""
        messages = cluster.messages
        chunk_size = self.max_cluster_size // 2
        
        split_clusters = []
        
        for i in range(0, len(messages), chunk_size):
            chunk_messages = messages[i:i + chunk_size]
            
            if len(chunk_messages) >= self.min_cluster_size:
                split_cluster = self._create_cluster(
                    messages=chunk_messages,
                    cluster_id=len(split_clusters),
                    cluster_type=cluster.cluster_type
                )
                split_clusters.append(split_cluster)
        
        return split_clusters
    
    def _merge_small_clusters(self, clusters: List[MessageCluster]) -> List[MessageCluster]:
        """Merge small clusters with similar topics."""
        if len(clusters) <= 1:
            return clusters
        
        # Find small clusters (less than half of min_cluster_size)
        small_clusters = [c for c in clusters if len(c.messages) < self.min_cluster_size * 1.5]
        large_clusters = [c for c in clusters if len(c.messages) >= self.min_cluster_size * 1.5]
        
        # Try to merge small clusters
        merged = []
        i = 0
        
        while i < len(small_clusters):
            current = small_clusters[i]
            
            # Look for a similar small cluster to merge with
            merged_with = None
            for j in range(i + 1, len(small_clusters)):
                candidate = small_clusters[j]
                
                if self._can_merge_clusters(current, candidate):
                    # Merge clusters
                    merged_messages = current.messages + candidate.messages
                    merged_messages.sort(key=lambda x: x['parsed_timestamp'])
                    
                    merged_cluster = self._create_cluster(
                        messages=merged_messages,
                        cluster_id=len(merged),
                        cluster_type='merged'
                    )
                    
                    merged.append(merged_cluster)
                    merged_with = j
                    break
            
            if merged_with is not None:
                # Remove the merged cluster
                small_clusters.pop(merged_with)
                i += 1
            else:
                # Keep as is if no merge candidate found
                merged.append(current)
                i += 1
        
        return large_clusters + merged
    
    def _can_merge_clusters(self, cluster1: MessageCluster, cluster2: MessageCluster) -> bool:
        """Check if two clusters can be merged."""
        # Check combined size
        combined_size = len(cluster1.messages) + len(cluster2.messages)
        if combined_size > self.max_cluster_size:
            return False
        
        # Check time proximity
        time_gap = abs((cluster2.start_time - cluster1.end_time).total_seconds() / 60)
        if time_gap > self.temporal_window_minutes * 2:
            return False
        
        # Check topic similarity (simple keyword matching)
        topic1_words = set(cluster1.topic.lower().split())
        topic2_words = set(cluster2.topic.lower().split())
        
        if topic1_words and topic2_words:
            similarity = len(topic1_words.intersection(topic2_words)) / len(topic1_words.union(topic2_words))
            return similarity > 0.3
        
        return True
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        return self.stats.copy()
```

#### Step 2.2: Update Document Creation Pipeline

Update `multi_source_rag/apps/documents/interfaces/local_slack.py` to use the enhanced processor:

```python
# Add import at the top
from ..processors.enhanced_message_processor import EnhancedMessageProcessor, MessageCluster

# Add this method to LocalSlackSourceInterface class
def _process_with_enhanced_clustering(self, messages: List[SlackMessage]) -> List[Dict[str, Any]]:
    """
    Process messages using enhanced semantic clustering.
    
    Args:
        messages: List of SlackMessage objects
        
    Returns:
        List of document dictionaries
    """
    if not messages:
        return []
    
    # Convert SlackMessage objects to dictionaries
    message_dicts = []
    for msg in messages:
        message_dicts.append({
            'text': msg.text,
            'user': msg.user,
            'user_name': msg.user_name,
            'ts': msg.timestamp,
            'timestamp': msg.timestamp,
            'thread_ts': msg.thread_ts,
            'channel': msg.channel,
            'reactions': msg.reactions,
            'reply_count': msg.reply_count,
            'files': msg.files,
            'edited': msg.edited,
            'subtype': msg.subtype
        })
    
    # Initialize enhanced message processor
    processor = EnhancedMessageProcessor(
        max_cluster_size=self.config.get('max_cluster_size', 35),
        max_cluster_duration_hours=self.config.get('max_cluster_duration_hours', 8.0),
        temporal_window_minutes=self.config.get('temporal_window_minutes', 45),
        semantic_threshold=self.config.get('semantic_threshold', 0.15),
        min_cluster_size=self.config.get('min_cluster_size', 2)
    )
    
    # Process messages into clusters
    clusters = processor.process_messages(message_dicts)
    
    # Convert clusters to documents
    documents = []
    for i, cluster in enumerate(clusters):
        doc = self._cluster_to_document(cluster, i)
        if doc:
            documents.append(doc)
    
    logger.info(f"Enhanced clustering created {len(documents)} documents from {len(clusters)} clusters")
    return documents

def _cluster_to_document(self, cluster: MessageCluster, doc_index: int) -> Optional[Dict[str, Any]]:
    """Convert a MessageCluster to a document."""
    if not cluster.messages:
        return None
    
    # Format content
    formatted_messages = []
    for msg in cluster.messages:
        timestamp = datetime.fromtimestamp(float(msg['timestamp'])).strftime('%Y-%m-%d %H:%M:%S')
        user_name = msg.get('user_name', msg.get('user', 'Unknown'))
        text = msg.get('text', '')
        
        formatted_msg = f"[{timestamp}] {user_name}: {text}"
        
        # Add reactions if present
        if msg.get('reactions'):
            reactions = ', '.join(f":{r}:" for r in msg['reactions'] if isinstance(r, str))
            if reactions:
                formatted_msg += f" [{reactions}]"
        
        formatted_messages.append(formatted_msg)
    
    content = "\n\n".join(formatted_messages)
    
    # Create document ID
    doc_id = f"slack_{self.channel_id}_cluster_{cluster.cluster_id}_{hash(content) % 10000}"
    
    # Create title
    title = f"{cluster.cluster_type.replace('_', ' ').title()}: {cluster.topic}"
    
    # Create metadata
    metadata = {
        'channel_id': self.channel_id,
        'channel_name': self.get_channel_info(self.channel_id).get('name', ''),
        'cluster_id': cluster.cluster_id,
        'cluster_type': cluster.cluster_type,
        'topic': cluster.topic,
        'start_time': cluster.start_time.isoformat(),
        'end_time': cluster.end_time.isoformat(),
        'participants': list(cluster.participants),
        'participant_count': len(cluster.participants),
        'message_count': len(cluster.messages),
        'quality_score': cluster.quality_score,
        'duration_hours': (cluster.end_time - cluster.start_time).total_seconds() / 3600,
        'chunking_strategy': 'enhanced_clustering',
        'enhanced_processing': True
    }
    
    # Create document
    document = {
        'id': doc_id,
        'title': title,
        'content': content,
        'metadata': metadata,
        'url': f"https://slack.com/archives/{self.channel_id}/p{cluster.messages[0]['timestamp'].replace('.', '')}",
        'content_type': 'text/slack'
    }
    
    return document

# Update fetch_documents method to support enhanced clustering
def fetch_documents(self, **kwargs) -> List[Dict[str, Any]]:
    """
    Fetch documents with multiple processing options.
    """
    # ... existing parameter extraction ...
    
    # Check processing mode
    use_enhanced_clustering = kwargs.get('use_enhanced_clustering', 
                                       self.config.get('use_enhanced_clustering', False))
    use_conversation_chunking = kwargs.get('use_conversation_chunking', 
                                         self.config.get('use_conversation_chunking', False))
    
    # Load messages
    messages = self.fetch_channel_messages(
        days_back=days_back,
        include_threads=include_threads,
        filter_bots=filter_bots,
        date_filter=date_filter
    )
    
    if not messages:
        logger.warning(f"No messages found for channel {channel_id}")
        return []
    
    # Choose processing method based on configuration
    if use_enhanced_clustering:
        documents = self._process_with_enhanced_clustering(messages)
    elif use_conversation_chunking:
        documents = self._create_conversation_aware_documents(messages)
    else:
        # Use existing time-based chunking
        time_period = kwargs.get("time_period", self.time_period)
        custom_days = kwargs.get("custom_days", self.custom_days)
        documents = self._create_time_based_documents(messages, time_period, custom_days)
    
    # Apply limit
    if limit and len(documents) > limit:
        documents = documents[:limit]
    
    return documents
```

### Phase 3: Advanced Query Engine

#### Step 3.1: Create ImprovedQueryEngine

Create `multi_source_rag/apps/search/engines/improved_query_engine.py`:

```python
import logging
import re
from typing import List, Dict, Any, Optional, Tuple, Set
from datetime import datetime, timedelta
import numpy as np
from collections import defaultdict

from llama_index.core.base.response.schema import Response
from llama_index.core.schema import NodeWithScore, QueryBundle
from llama_index.core.query_engine import BaseQueryEngine
from llama_index.core.indices.base import BaseIndex
from llama_index.core.postprocessor import SimilarityPostprocessor
from llama_index.core.response_synthesizers import ResponseMode

logger = logging.getLogger(__name__)

class ContextAwareQueryExpander:
    """Expands queries with context-aware terms and synonyms."""
    
    def __init__(self):
        self.technical_synonyms = {
            'bug': ['issue', 'problem', 'error', 'defect'],
            'feature': ['functionality', 'capability', 'enhancement'],
            'api': ['interface', 'endpoint', 'service'],
            'database': ['db', 'data store', 'storage'],
            'user': ['customer', 'client', 'person'],
            'deploy': ['deployment', 'release', 'launch'],
            'test': ['testing', 'qa', 'verification'],
            'meeting': ['discussion', 'call', 'sync'],
            'review': ['feedback', 'evaluation', 'assessment']
        }
        
        self.question_patterns = [
            r'\bwho\b', r'\bwhat\b', r'\bwhen\b', r'\bwhere\b', r'\bwhy\b', r'\bhow\b'
        ]
    
    def expand_query(self, query: str, conversation_history: List[str] = None) -> str:
        """
        Expand query with synonyms and context.
        
        Args:
            query: Original query string
            conversation_history: Previous queries for context
            
        Returns:
            Expanded query string
        """
        expanded_terms = []
        query_lower = query.lower()
        
        # Add original query
        expanded_terms.append(query)
        
        # Add synonyms
        for term, synonyms in self.technical_synonyms.items():
            if term in query_lower:
                expanded_terms.extend([f"({term} OR {syn})" for syn in synonyms[:2]])
        
        # Add context from conversation history
        if conversation_history:
            context_terms = self._extract_context_terms(conversation_history)
            for term in context_terms[:3]:  # Limit context terms
                if term not in query_lower:
                    expanded_terms.append(term)
        
        return " ".join(expanded_terms)
    
    def _extract_context_terms(self, history: List[str]) -> List[str]:
        """Extract relevant terms from conversation history."""
        all_text = " ".join(history[-3:])  # Last 3 queries
        
        # Extract technical terms
        terms = []
        for term in self.technical_synonyms.keys():
            if term in all_text.lower():
                terms.append(term)
        
        return terms

class ImprovedQueryEngine(BaseQueryEngine):
    """
    Improved query engine with context awareness and better retrieval.
    """
    
    def __init__(self, 
                 index: BaseIndex,
                 similarity_top_k: int = 10,
                 response_mode: str = "tree_summarize",
                 quality_threshold: float = 0.3):
        """
        Initialize improved query engine.
        
        Args:
            index: Vector index for retrieval
            similarity_top_k: Number of similar documents to retrieve
            response_mode: Response synthesis mode
            quality_threshold: Minimum quality score for documents
        """
        self.index = index
        self.similarity_top_k = similarity_top_k
        self.response_mode = ResponseMode(response_mode)
        self.quality_threshold = quality_threshold
        
        # Initialize components
        self.query_expander = ContextAwareQueryExpander()
        self.conversation_history = []
        
        # Set up retriever with postprocessing
        self.retriever = index.as_retriever(
            similarity_top_k=similarity_top_k * 2  # Retrieve more, then filter
        )
        
        # Set up postprocessor for quality filtering
        self.postprocessor = SimilarityPostprocessor(
            similarity_cutoff=0.1  # Very low cutoff, we'll do custom filtering
        )
        
        # Set up response synthesizer
        self.response_synthesizer = index.as_query_engine(
            similarity_top_k=similarity_top_k,
            response_mode=response_mode
        ).response_synthesizer
    
    def _query(self, query_bundle: QueryBundle) -> Response:
        """
        Execute improved query with context awareness.
        
        Args:
            query_bundle: Query bundle containing query string and metadata
            
        Returns:
            Response object with answer and sources
        """
        query_str = query_bundle.query_str
        logger.info(f"Processing improved query: {query_str}")
        
        # Step 1: Expand query with context
        expanded_query = self.query_expander.expand_query(
            query_str, 
            self.conversation_history
        )
        
        # Step 2: Retrieve documents
        expanded_bundle = QueryBundle(
            query_str=expanded_query,
            embedding=query_bundle.embedding
        )
        
        retrieved_nodes = self.retriever.retrieve(expanded_bundle)
        
        # Step 3: Apply intelligent filtering
        filtered_nodes = self._apply_intelligent_filtering(retrieved_nodes, query_str)
        
        # Step 4: Re-rank nodes
        reranked_nodes = self._rerank_nodes(filtered_nodes, query_str)
        
        # Step 5: Generate response
        response = self.response_synthesizer.synthesize(
            query=query_bundle,
            nodes=reranked_nodes[:self.similarity_top_k]
        )
        
        # Step 6: Enhance response with metadata
        enhanced_response = self._enhance_response(response, reranked_nodes, query_str)
        
        # Step 7: Update conversation history
        self.conversation_history.append(query_str)
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
        
        return enhanced_response
    
    def _apply_intelligent_filtering(self, nodes: List[NodeWithScore], query: str) -> List[NodeWithScore]:
        """Apply intelligent filtering based on quality and relevance."""
        filtered_nodes = []
        query_lower = query.lower()
        
        for node in nodes:
            # Get node metadata
            metadata = node.node.metadata or {}
            
            # Quality score filtering
            quality_score = metadata.get('quality_score', 0.5)
            if quality_score < self.quality_threshold:
                continue
            
            # Content relevance check
            content = node.node.text.lower()
            
            # Check for query terms in content
            query_terms = set(re.findall(r'\w+', query_lower))
            content_terms = set(re.findall(r'\w+', content))
            
            # Calculate term overlap
            overlap = len(query_terms.intersection(content_terms))
            overlap_ratio = overlap / len(query_terms) if query_terms else 0
            
            # Filter based on minimum overlap
            if overlap_ratio >= 0.1:  # At least 10% term overlap
                filtered_nodes.append(node)
        
        return filtered_nodes
    
    def _rerank_nodes(self, nodes: List[NodeWithScore], query: str) -> List[NodeWithScore]:
        """Re-rank nodes based on multiple factors."""
        if not nodes:
            return nodes
        
        query_lower = query.lower()
        scored_nodes = []
        
        for node in nodes:
            # Start with similarity score
            base_score = node.score or 0.0
            
            # Get metadata
            metadata = node.node.metadata or {}
            content = node.node.text.lower()
            
            # Scoring factors
            relevance_boost = 0.0
            
            # 1. Quality score boost
            quality_score = metadata.get('quality_score', 0.5)
            relevance_boost += quality_score * 0.2
            
            # 2. Cluster type boost
            cluster_type = metadata.get('cluster_type', '')
            if cluster_type == 'thread_based':
                relevance_boost += 0.1
            elif cluster_type == 'topic_based':
                relevance_boost += 0.15
            
            # 3. Participant diversity boost
            participant_count = metadata.get('participant_count', 1)
            if participant_count > 3:
                relevance_boost += 0.1
            elif participant_count > 1:
                relevance_boost += 0.05
            
            # 4. Recency boost (for time-sensitive queries)
            if any(term in query_lower for term in ['recent', 'latest', 'new', 'current']):
                try:
                    end_time = datetime.fromisoformat(metadata.get('end_time', ''))
                    days_old = (datetime.now() - end_time).days
                    if days_old < 7:
                        relevance_boost += 0.2
                    elif days_old < 30:
                        relevance_boost += 0.1
                except:
                    pass
            
            # 5. Content type boost for question-answer patterns
            if '?' in content and any(term in query_lower for term in ['how', 'what', 'why', 'when']):
                relevance_boost += 0.1
            
            # 6. Topic matching boost
            topics = metadata.get('topics', [])
            if topics and any(topic.lower() in query_lower for topic in topics):
                relevance_boost += 0.15
            
            # Calculate final score
            final_score = base_score + relevance_boost
            
            scored_nodes.append((node, final_score))
        
        # Sort by final score
        scored_nodes.sort(key=lambda x: x[1], reverse=True)
        
        # Return re-ranked nodes
        reranked = []
        for node, score in scored_nodes:
            # Update node score
            node.score = score
            reranked.append(node)
        
        return reranked
    
    def _enhance_response(self, response: Response, nodes: List[NodeWithScore], query: str) -> Response:
        """Enhance response with additional metadata and context."""
        # Add source information
        source_info = []
        
        for i, node in enumerate(nodes[:5]):  # Top 5 sources
            metadata = node.node.metadata or {}
            
            source_info.append({
                'rank': i + 1,
                'score': f"{node.score:.3f}" if node.score else "N/A",
                'cluster_type': metadata.get('cluster_type', 'unknown'),
                'participants': metadata.get('participant_count', 1),
                'quality': f"{metadata.get('quality_score', 0.5):.2f}",
                'topic': metadata.get('topic', 'General Discussion'),
                'channel': metadata.get('channel_name', 'Unknown Channel'),
                'timeframe': self._format_timeframe(metadata)
            })
        
        # Enhanced response text
        enhanced_text = str(response)
        
        # Add source summary
        if source_info:
            enhanced_text += "\n\n**Sources:**\n"
            for source in source_info:
                enhanced_text += f"• {source['topic']} (Quality: {source['quality']}, Participants: {source['participants']})\n"
        
        # Create enhanced response
        enhanced_response = Response(
            response=enhanced_text,
            source_nodes=response.source_nodes,
            metadata={
                'query': query,
                'sources_used': len(source_info),
                'processing_method': 'improved_query_engine',
                'source_details': source_info,
                **response.metadata
            }
        )
        
        return enhanced_response
    
    def _format_timeframe(self, metadata: Dict[str, Any]) -> str:
        """Format timeframe information from metadata."""
        try:
            start_time = metadata.get('start_time', '')
            end_time = metadata.get('end_time', '')
            
            if start_time and end_time:
                start_dt = datetime.fromisoformat(start_time)
                end_dt = datetime.fromisoformat(end_time)
                
                if start_dt.date() == end_dt.date():
                    return f"{start_dt.strftime('%Y-%m-%d %H:%M')} - {end_dt.strftime('%H:%M')}"
                else:
                    return f"{start_dt.strftime('%Y-%m-%d')} - {end_dt.strftime('%Y-%m-%d')}"
        except:
            pass
        
        return "Unknown timeframe"
    
    def clear_history(self):
        """Clear conversation history."""
        self.conversation_history = []
    
    def get_stats(self) -> Dict[str, Any]:
        """Get query engine statistics."""
        return {
            'conversation_history_length': len(self.conversation_history),
            'similarity_top_k': self.similarity_top_k,
            'quality_threshold': self.quality_threshold,
            'response_mode': str(self.response_mode)
        }
```

#### Step 3.2: Update RAG Service Integration

Update `multi_source_rag/apps/search/services/rag_service.py`:

```python
# Add import at the top
from ..engines.improved_query_engine import ImprovedQueryEngine

# Add method to RAGService class
def create_improved_query_engine(self, **kwargs) -> ImprovedQueryEngine:
    """
    Create an improved query engine with enhanced capabilities.
    
    Args:
        **kwargs: Configuration parameters
        
    Returns:
        ImprovedQueryEngine instance
    """
    if not self.index:
        raise ValueError("Index must be built before creating query engine")
    
    # Get configuration
    similarity_top_k = kwargs.get('similarity_top_k', 10)
    response_mode = kwargs.get('response_mode', 'tree_summarize')
    quality_threshold = kwargs.get('quality_threshold', 0.3)
    
    # Create improved query engine
    improved_engine = ImprovedQueryEngine(
        index=self.index,
        similarity_top_k=similarity_top_k,
        response_mode=response_mode,
        quality_threshold=quality_threshold
    )
    
    return improved_engine

# Update the query method to support improved engine
def query(self, query_text: str, use_improved_engine: bool = True, **kwargs):
    """
    Execute query with option to use improved engine.
    
    Args:
        query_text: Query string
        use_improved_engine: Whether to use the improved query engine
        **kwargs: Additional parameters
        
    Returns:
        Query response
    """
    if use_improved_engine:
        try:
            # Create improved engine
            improved_engine = self.create_improved_query_engine(**kwargs)
            
            # Execute query
            response = improved_engine.query(query_text)
            
            return {
                'answer': str(response),
                'sources': response.source_nodes,
                'metadata': response.metadata,
                'engine_type': 'improved'
            }
        except Exception as e:
            logger.error(f"Error with improved engine, falling back to standard: {e}")
            # Fall back to standard engine
    
    # Use standard query engine
    return self._standard_query(query_text, **kwargs)

def _standard_query(self, query_text: str, **kwargs):
    """Standard query execution (existing implementation)."""
    # ... existing query logic ...
    pass
```

### Phase 4: Cross-Reference System

#### Step 4.1: Create Cross-Reference Detector

Create `multi_source_rag/apps/documents/processors/cross_reference_detector.py`:

```python
import logging
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple, Set
import numpy as np
from collections import defaultdict
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

logger = logging.getLogger(__name__)

class CrossReferenceDetector:
    """
    Detects semantic relationships between documents to create cross-references.
    """
    
    def __init__(self,
                 similarity_threshold: float = 0.3,
                 max_references_per_doc: int = 5,
                 time_window_days: int = 30,
                 enable_caching: bool = True):
        """
        Initialize cross-reference detector.
        
        Args:
            similarity_threshold: Minimum similarity for cross-reference
            max_references_per_doc: Maximum references per document
            time_window_days: Time window for finding related documents
            enable_caching: Whether to cache similarity calculations
        """
        self.similarity_threshold = similarity_threshold
        self.max_references_per_doc = max_references_per_doc
        self.time_window_days = time_window_days
        self.enable_caching = enable_caching
        
        # Initialize vectorizer
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=1,
            max_df=0.95
        )
        
        # Caching
        self.similarity_cache = {} if enable_caching else None
        
        # Statistics
        self.stats = {
            'documents_processed': 0,
            'cross_references_created': 0,
            'cache_hits': 0,
            'processing_time': 0.0
        }
    
    def find_cross_references(self, documents: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Find cross-references between documents.
        
        Args:
            documents: List of document dictionaries
            
        Returns:
            Dictionary mapping document IDs to lists of cross-references
        """
        start_time = datetime.now()
        logger.info(f"Finding cross-references for {len(documents)} documents...")
        
        if len(documents) < 2:
            return {}
        
        # Group documents by time periods for efficient processing
        time_groups = self._group_documents_by_time(documents)
        
        # Find cross-references within and across time groups
        all_cross_refs = {}
        
        for group_name, group_docs in time_groups.items():
            # Find references within the group
            group_refs = self._find_references_in_group(group_docs)
            all_cross_refs.update(group_refs)
            
            # Find references to adjacent time groups
            adjacent_refs = self._find_adjacent_group_references(group_docs, time_groups, group_name)
            
            # Merge references
            for doc_id, refs in adjacent_refs.items():
                if doc_id in all_cross_refs:
                    all_cross_refs[doc_id].extend(refs)
                else:
                    all_cross_refs[doc_id] = refs
        
        # Post-process and rank references
        processed_refs = self._post_process_references(all_cross_refs)
        
        # Update statistics
        processing_time = (datetime.now() - start_time).total_seconds()
        self.stats.update({
            'documents_processed': len(documents),
            'cross_references_created': sum(len(refs) for refs in processed_refs.values()),
            'processing_time': processing_time
        })
        
        logger.info(f"Found {self.stats['cross_references_created']} cross-references in {processing_time:.2f}s")
        return processed_refs
    
    def _group_documents_by_time(self, documents: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Group documents by time periods for efficient processing."""
        time_groups = defaultdict(list)
        
        for doc in documents:
            try:
                # Get document time
                start_time = doc['metadata'].get('start_time', '')
                if start_time:
                    doc_time = datetime.fromisoformat(start_time)
                    # Group by week
                    week_key = doc_time.strftime('%Y-W%U')
                    time_groups[week_key].append(doc)
                else:
                    # Default group for documents without time
                    time_groups['no_time'].append(doc)
            except Exception as e:
                logger.warning(f"Error grouping document by time: {e}")
                time_groups['error'].append(doc)
        
        return dict(time_groups)
    
    def _find_references_in_group(self, documents: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Find cross-references within a group of documents."""
        if len(documents) < 2:
            return {}
        
        # Extract texts for vectorization
        texts = [doc.get('content', '') for doc in documents]
        doc_ids = [doc.get('id', '') for doc in documents]
        
        try:
            # Create TF-IDF matrix
            tfidf_matrix = self.vectorizer.fit_transform(texts)
            
            # Calculate similarity matrix
            similarity_matrix = cosine_similarity(tfidf_matrix)
            
            # Find cross-references
            cross_refs = {}
            
            for i, doc_id in enumerate(doc_ids):
                references = []
                
                # Get similarities for this document
                similarities = similarity_matrix[i]
                
                # Find similar documents
                for j, similarity in enumerate(similarities):
                    if i != j and similarity >= self.similarity_threshold:
                        ref_doc = documents[j]
                        
                        reference = {
                            'document_id': ref_doc.get('id', ''),
                            'title': ref_doc.get('title', ''),
                            'similarity': float(similarity),
                            'cluster_type': ref_doc.get('metadata', {}).get('cluster_type', ''),
                            'topic': ref_doc.get('metadata', {}).get('topic', ''),
                            'participants': ref_doc.get('metadata', {}).get('participants', []),
                            'timeframe': self._get_document_timeframe(ref_doc),
                            'reference_type': 'semantic'
                        }
                        references.append(reference)
                
                if references:
                    # Sort by similarity and limit
                    references.sort(key=lambda x: x['similarity'], reverse=True)
                    cross_refs[doc_id] = references[:self.max_references_per_doc]
            
            return cross_refs
            
        except Exception as e:
            logger.error(f"Error finding references in group: {e}")
            return {}
    
    def _find_adjacent_group_references(self, group_docs: List[Dict[str, Any]], 
                                      all_groups: Dict[str, List[Dict[str, Any]]], 
                                      current_group: str) -> Dict[str, List[Dict[str, Any]]]:
        """Find cross-references to adjacent time groups."""
        cross_refs = {}
        
        # Get adjacent groups (simplified - could be enhanced with proper time logic)
        adjacent_groups = []
        for group_name, group_documents in all_groups.items():
            if group_name != current_group and group_name not in ['no_time', 'error']:
                adjacent_groups.append(group_documents)
        
        if not adjacent_groups:
            return cross_refs
        
        # Combine adjacent documents
        adjacent_docs = []
        for group in adjacent_groups[:2]:  # Limit to 2 adjacent groups for performance
            adjacent_docs.extend(group)
        
        # Find references between current group and adjacent documents
        all_docs = group_docs + adjacent_docs
        group_refs = self._find_references_in_group(all_docs)
        
        # Filter to only include references from current group to adjacent groups
        current_doc_ids = {doc['id'] for doc in group_docs}
        
        for doc_id, refs in group_refs.items():
            if doc_id in current_doc_ids:
                # Filter references to only include adjacent documents
                adjacent_refs = [ref for ref in refs if ref['document_id'] not in current_doc_ids]
                if adjacent_refs:
                    cross_refs[doc_id] = adjacent_refs
        
        return cross_refs
    
    def _post_process_references(self, cross_refs: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[Dict[str, Any]]]:
        """Post-process and enhance cross-references."""
        processed_refs = {}
        
        for doc_id, references in cross_refs.items():
            if not references:
                continue
            
            # Remove duplicates
            seen_refs = set()
            unique_refs = []
            
            for ref in references:
                ref_key = ref['document_id']
                if ref_key not in seen_refs:
                    seen_refs.add(ref_key)
                    unique_refs.append(ref)
            
            # Sort by similarity and relevance
            unique_refs.sort(key=lambda x: (x['similarity'], len(x.get('participants', []))), reverse=True)
            
            # Limit to max references
            processed_refs[doc_id] = unique_refs[:self.max_references_per_doc]
        
        return processed_refs
    
    def _get_document_timeframe(self, document: Dict[str, Any]) -> str:
        """Get formatted timeframe for document."""
        metadata = document.get('metadata', {})
        
        try:
            start_time = metadata.get('start_time', '')
            end_time = metadata.get('end_time', '')
            
            if start_time and end_time:
                start_dt = datetime.fromisoformat(start_time)
                end_dt = datetime.fromisoformat(end_time)
                
                if start_dt.date() == end_dt.date():
                    return start_dt.strftime('%Y-%m-%d')
                else:
                    return f"{start_dt.strftime('%Y-%m-%d')} to {end_dt.strftime('%Y-%m-%d')}"
        except Exception:
            pass
        
        return "Unknown"
    
    def get_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts."""
        cache_key = None
        
        if self.enable_caching:
            # Create cache key
            key1 = hashlib.md5(text1.encode()).hexdigest()
            key2 = hashlib.md5(text2.encode()).hexdigest()
            cache_key = f"{min(key1, key2)}_{max(key1, key2)}"
            
            if cache_key in self.similarity_cache:
                self.stats['cache_hits'] += 1
                return self.similarity_cache[cache_key]
        
        try:
            # Calculate similarity
            texts = [text1, text2]
            tfidf_matrix = self.vectorizer.fit_transform(texts)
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            
            # Cache result
            if self.enable_caching and cache_key:
                self.similarity_cache[cache_key] = similarity
            
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            return 0.0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        stats = self.stats.copy()
        if self.similarity_cache:
            stats['cache_size'] = len(self.similarity_cache)
        return stats
    
    def clear_cache(self):
        """Clear similarity cache."""
        if self.similarity_cache:
            self.similarity_cache.clear()
```

#### Step 4.2: Integrate Cross-References with Document Processing

Update `multi_source_rag/apps/documents/interfaces/local_slack.py` to include cross-references:

```python
# Add import at the top
from ..processors.cross_reference_detector import CrossReferenceDetector

# Add method to LocalSlackSourceInterface class
def _add_cross_references(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Add cross-references to documents.
    
    Args:
        documents: List of document dictionaries
        
    Returns:
        Documents with cross-references added to metadata
    """
    if len(documents) < 2:
        return documents
    
    # Initialize cross-reference detector
    detector = CrossReferenceDetector(
        similarity_threshold=self.config.get('cross_ref_similarity_threshold', 0.3),
        max_references_per_doc=self.config.get('max_references_per_doc', 5),
        time_window_days=self.config.get('cross_ref_time_window_days', 30),
        enable_caching=True
    )
    
    # Find cross-references
    cross_references = detector.find_cross_references(documents)
    
    # Add cross-references to document metadata
    enhanced_documents = []
    for doc in documents:
        doc_id = doc.get('id', '')
        doc_copy = doc.copy()
        
        # Add cross-references to metadata
        if doc_id in cross_references:
            doc_copy['metadata']['cross_references'] = cross_references[doc_id]
            doc_copy['metadata']['has_cross_references'] = True
            doc_copy['metadata']['cross_reference_count'] = len(cross_references[doc_id])
        else:
            doc_copy['metadata']['cross_references'] = []
            doc_copy['metadata']['has_cross_references'] = False
            doc_copy['metadata']['cross_reference_count'] = 0
        
        enhanced_documents.append(doc_copy)
    
    logger.info(f"Added cross-references to {len([d for d in enhanced_documents if d['metadata']['has_cross_references']])} documents")
    return enhanced_documents

# Update fetch_documents method to include cross-references
def fetch_documents(self, **kwargs) -> List[Dict[str, Any]]:
    """
    Fetch documents with all enhancements.
    """
    # ... existing code for getting documents ...
    
    # Add cross-references if enabled
    add_cross_references = kwargs.get('add_cross_references', 
                                    self.config.get('add_cross_references', False))
    
    if add_cross_references and documents:
        documents = self._add_cross_references(documents)
    
    return documents
```

## Testing Strategy

### Phase 1: Unit Testing

Create comprehensive unit tests for each component:

#### Test File: `tests/test_conversation_chunker.py`

```python
import unittest
from datetime import datetime, timedelta
from multi_source_rag.apps.documents.processors.conversation_chunker import (
    ConversationAwareChunker, ConversationChunk
)

class TestConversationAwareChunker(unittest.TestCase):
    
    def setUp(self):
        self.chunker = ConversationAwareChunker(
            max_chunk_size=1000,
            overlap_size=100,
            max_time_gap_minutes=30
        )
        
        # Sample messages
        self.sample_messages = [
            {
                'text': 'Hello everyone, how is the project going?',
                'user': 'user1',
                'user_name': 'Alice',
                'timestamp': '1609459200.123456',  # 2021-01-01 00:00:00
                'ts': '1609459200.123456'
            },
            {
                'text': 'The API integration is almost done.',
                'user': 'user2',
                'user_name': 'Bob',
                'timestamp': '1609459260.123456',  # 1 minute later
                'ts': '1609459260.123456'
            },
            {
                'text': 'Great! Any issues with the database connection?',
                'user': 'user1',
                'user_name': 'Alice',
                'timestamp': '1609459320.123456',  # 2 minutes after first
                'ts': '1609459320.123456'
            }
        ]
    
    def test_chunk_messages_basic(self):
        """Test basic message chunking."""
        chunks = self.chunker.chunk_messages(self.sample_messages)
        
        self.assertIsInstance(chunks, list)
        self.assertGreater(len(chunks), 0)
        
        for chunk in chunks:
            self.assertIsInstance(chunk, ConversationChunk)
            self.assertIsInstance(chunk.content, str)
            self.assertIsInstance(chunk.messages, list)
    
    def test_thread_grouping(self):
        """Test thread-based message grouping."""
        # Add thread messages
        threaded_messages = self.sample_messages + [
            {
                'text': 'This is a thread reply',
                'user': 'user3',
                'user_name': 'Charlie',
                'timestamp': '1609459380.123456',
                'ts': '1609459380.123456',
                'thread_ts': '1609459200.123456'  # Reply to first message
            }
        ]
        
        chunks = self.chunker.chunk_messages(threaded_messages)
        
        # Should handle threaded conversations appropriately
        self.assertGreater(len(chunks), 0)
    
    def test_empty_messages(self):
        """Test handling of empty message list."""
        chunks = self.chunker.chunk_messages([])
        self.assertEqual(chunks, [])
    
    def test_time_gap_clustering(self):
        """Test clustering based on time gaps."""
        # Create messages with large time gap
        messages_with_gap = self.sample_messages + [
            {
                'text': 'This message is much later',
                'user': 'user4',
                'user_name': 'David',
                'timestamp': str(float(self.sample_messages[-1]['timestamp']) + 3600),  # 1 hour later
                'ts': str(float(self.sample_messages[-1]['ts']) + 3600)
            }
        ]
        
        chunks = self.chunker.chunk_messages(messages_with_gap)
        
        # Should create separate chunks due to time gap
        self.assertGreaterEqual(len(chunks), 1)

if __name__ == '__main__':
    unittest.main()
```

#### Test File: `tests/test_enhanced_message_processor.py`

```python
import unittest
from multi_source_rag.apps.documents.processors.enhanced_message_processor import (
    EnhancedMessageProcessor, MessageCluster
)

class TestEnhancedMessageProcessor(unittest.TestCase):
    
    def setUp(self):
        self.processor = EnhancedMessageProcessor(
            max_cluster_size=20,
            semantic_threshold=0.2
        )
        
        # Sample messages for testing
        self.test_messages = [
            {
                'text': 'We need to fix the API bug in production',
                'user': 'dev1',
                'ts': '1609459200.123456',
                'type': 'message'
            },
            {
                'text': 'I can look into the API issue tomorrow',
                'user': 'dev2', 
                'ts': '1609459300.123456',
                'type': 'message'
            },
            {
                'text': 'What time is the team meeting today?',
                'user': 'pm1',
                'ts': '1609461000.123456',  # Much later
                'type': 'message'
            }
        ]
    
    def test_process_messages(self):
        """Test message processing into clusters."""
        clusters = self.processor.process_messages(self.test_messages)
        
        self.assertIsInstance(clusters, list)
        
        for cluster in clusters:
            self.assertIsInstance(cluster, MessageCluster)
            self.assertGreater(len(cluster.messages), 0)
            self.assertIsInstance(cluster.topic, str)
            self.assertIsInstance(cluster.quality_score, float)
            self.assertIn(cluster.cluster_type, ['thread_based', 'topic_based', 'temporal'])
    
    def test_semantic_clustering(self):
        """Test semantic clustering of related messages."""
        # Messages about the same topic should be clustered together
        api_messages = [
            {'text': 'API endpoint is returning 500 errors', 'user': 'dev1', 'ts': '1609459200.123456', 'type': 'message'},
            {'text': 'The API authentication might be the issue', 'user': 'dev2', 'ts': '1609459300.123456', 'type': 'message'},
            {'text': 'Meeting scheduled for 3 PM', 'user': 'pm1', 'ts': '1609459400.123456', 'type': 'message'},
            {'text': 'API documentation needs updating', 'user': 'tech_writer', 'ts': '1609459500.123456', 'type': 'message'}
        ]
        
        clusters = self.processor.process_messages(api_messages)
        
        # Should group API-related messages together
        api_cluster = None
        for cluster in clusters:
            if 'api' in cluster.topic.lower():
                api_cluster = cluster
                break
        
        if api_cluster:
            api_message_count = sum(1 for msg in api_cluster.messages if 'api' in msg['text'].lower())
            self.assertGreater(api_message_count, 1)
    
    def test_stats_tracking(self):
        """Test statistics tracking."""
        initial_stats = self.processor.get_processing_stats()
        
        self.processor.process_messages(self.test_messages)
        
        final_stats = self.processor.get_processing_stats()
        
        self.assertGreater(final_stats['messages_processed'], initial_stats['messages_processed'])
        self.assertGreater(final_stats['clusters_created'], initial_stats['clusters_created'])

if __name__ == '__main__':
    unittest.main()
```

### Phase 2: Integration Testing

Create integration tests to verify components work together:

#### Test File: `tests/test_integration.py`

```python
import unittest
import tempfile
import json
import os
from datetime import datetime
from multi_source_rag.apps.documents.interfaces.local_slack import LocalSlackSourceInterface

class TestIntegration(unittest.TestCase):
    
    def setUp(self):
        # Create temporary test data
        self.test_dir = tempfile.mkdtemp()
        self.channel_dir = os.path.join(self.test_dir, 'channel_C123456')
        self.messages_dir = os.path.join(self.channel_dir, 'messages')
        self.users_dir = os.path.join(self.channel_dir, 'users')
        
        os.makedirs(self.messages_dir)
        os.makedirs(self.users_dir)
        
        # Create test message data
        test_messages = {
            'date': '2021-01-01',
            'channel_id': 'C123456',
            'message_count': 3,
            'messages': [
                {
                    'ts': '1609459200.123456',
                    'text': 'We should implement the new feature soon',
                    'user': 'U1',
                    'type': 'message'
                },
                {
                    'ts': '1609459260.123456', 
                    'text': 'I agree, the feature would help our users',
                    'user': 'U2',
                    'type': 'message'
                },
                {
                    'ts': '1609462800.123456',
                    'text': 'Different topic - when is the next release?',
                    'user': 'U3',
                    'type': 'message'
                }
            ]
        }
        
        # Save test data
        with open(os.path.join(self.messages_dir, 'messages_2021-01-01.json'), 'w') as f:
            json.dump(test_messages, f)
        
        # Create test user data
        test_users = {
            'user_count': 3,
            'users': {
                'U1': {'name': 'Alice', 'real_name': 'Alice Smith'},
                'U2': {'name': 'Bob', 'real_name': 'Bob Jones'},
                'U3': {'name': 'Charlie', 'real_name': 'Charlie Brown'}
            }
        }
        
        with open(os.path.join(self.users_dir, 'users.json'), 'w') as f:
            json.dump(test_users, f)
        
        # Initialize interface
        self.config = {
            'data_dir': self.test_dir,
            'channel': 'C123456',
            'use_conversation_chunking': True,
            'use_enhanced_clustering': True,
            'add_cross_references': True
        }
        
        self.interface = LocalSlackSourceInterface(self.config)
    
    def test_full_pipeline(self):
        """Test the complete processing pipeline."""
        # Fetch documents with all enhancements
        documents = self.interface.fetch_documents(
            use_conversation_chunking=True,
            use_enhanced_clustering=True,
            add_cross_references=True
        )
        
        self.assertIsInstance(documents, list)
        self.assertGreater(len(documents), 0)
        
        # Check document structure
        for doc in documents:
            self.assertIn('id', doc)
            self.assertIn('title', doc)
            self.assertIn('content', doc)
            self.assertIn('metadata', doc)
            
            # Check enhanced metadata
            metadata = doc['metadata']
            self.assertIn('chunking_strategy', metadata)
            
            # Check for enhancement flags
            if metadata.get('conversation_aware'):
                self.assertIn('chunk_type', metadata)
                self.assertIn('quality_score', metadata)
            
            if metadata.get('enhanced_processing'):
                self.assertIn('cluster_type', metadata)
                self.assertIn('topic', metadata)
            
            if metadata.get('has_cross_references', False):
                self.assertIn('cross_references', metadata)
                self.assertIsInstance(metadata['cross_references'], list)
    
    def test_performance(self):
        """Test performance with timing."""
        start_time = datetime.now()
        
        documents = self.interface.fetch_documents(
            use_enhanced_clustering=True,
            add_cross_references=True
        )
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # Should process in reasonable time (adjust threshold as needed)
        self.assertLess(processing_time, 10.0)  # 10 seconds max
        
        print(f"Processing time: {processing_time:.2f} seconds for {len(documents)} documents")
    
    def tearDown(self):
        # Clean up test files
        import shutil
        shutil.rmtree(self.test_dir)

if __name__ == '__main__':
    unittest.main()
```

### Phase 3: Configuration and Deployment

#### Configuration Updates

Update `multi_source_rag/config/settings.py`:

```python
# Add new RAG enhancement settings
RAG_ENHANCEMENTS = {
    # Conversation-aware chunking
    'use_conversation_chunking': env.bool('USE_CONVERSATION_CHUNKING', default=False),
    'conversation_chunk_size': env.int('CONVERSATION_CHUNK_SIZE', default=2000),
    'conversation_overlap_size': env.int('CONVERSATION_OVERLAP_SIZE', default=200),
    'max_conversation_gap_minutes': env.int('MAX_CONVERSATION_GAP_MINUTES', default=30),
    
    # Enhanced message processing
    'use_enhanced_clustering': env.bool('USE_ENHANCED_CLUSTERING', default=False),
    'max_cluster_size': env.int('MAX_CLUSTER_SIZE', default=35),
    'max_cluster_duration_hours': env.float('MAX_CLUSTER_DURATION_HOURS', default=8.0),
    'temporal_window_minutes': env.int('TEMPORAL_WINDOW_MINUTES', default=45),
    'semantic_threshold': env.float('SEMANTIC_THRESHOLD', default=0.15),
    
    # Cross-reference detection
    'add_cross_references': env.bool('ADD_CROSS_REFERENCES', default=False),
    'cross_ref_similarity_threshold': env.float('CROSS_REF_SIMILARITY_THRESHOLD', default=0.3),
    'max_references_per_doc': env.int('MAX_REFERENCES_PER_DOC', default=5),
    'cross_ref_time_window_days': env.int('CROSS_REF_TIME_WINDOW_DAYS', default=30),
    
    # Query engine improvements
    'use_improved_query_engine': env.bool('USE_IMPROVED_QUERY_ENGINE', default=True),
    'quality_threshold': env.float('QUALITY_THRESHOLD', default=0.3)
}
```

#### Migration Guide

Create `migrations/add_rag_enhancements.py`:

```python
"""
Migration script to add RAG enhancement features.
"""

def upgrade():
    """Add new configuration fields and update existing documents."""
    
    # Add new configuration options to existing document sources
    print("Adding RAG enhancement configuration options...")
    
    # Update existing LocalSlackSourceInterface configurations
    # This would be done through Django admin or configuration management
    
    print("RAG enhancements migration completed")

def downgrade():
    """Remove RAG enhancement features."""
    print("Removing RAG enhancement features...")
    
    # Remove enhanced configurations
    # Reset to basic time-based chunking
    
    print("RAG enhancements rollback completed")
```

## Performance Considerations

### Memory Management

1. **Batch Processing**: Process documents in batches to avoid memory issues
2. **Caching Strategy**: Implement intelligent caching for embeddings and similarities
3. **Lazy Loading**: Load components only when needed

### Optimization Tips

1. **Vectorization**: Use efficient libraries (scikit-learn, numpy) for calculations
2. **Parallel Processing**: Implement parallel processing for independent operations
3. **Database Indexing**: Ensure proper database indexing for metadata queries

### Monitoring and Logging

Add comprehensive logging and monitoring:

```python
# Add to your logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'rag_enhancements': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/rag_enhancements.log',
            'formatter': 'verbose'
        }
    },
    'loggers': {
        'multi_source_rag.apps.documents.processors': {
            'handlers': ['rag_enhancements'],
            'level': 'INFO',
            'propagate': True
        },
        'multi_source_rag.apps.search.engines': {
            'handlers': ['rag_enhancements'],
            'level': 'INFO',
            'propagate': True
        }
    }
}
```

## Rollout Strategy

### Phase 1: Development and Testing (Weeks 1-8)

1. **Week 1-2**: Implement conversation-aware chunking
   - Create `ConversationAwareChunker` class
   - Write unit tests
   - Integrate with `LocalSlackSourceInterface`

2. **Week 3-4**: Implement enhanced message processing
   - Create `EnhancedMessageProcessor` class
   - Add semantic clustering capabilities
   - Write integration tests

3. **Week 5-6**: Implement improved query engine
   - Create `ImprovedQueryEngine` class
   - Add query expansion and context awareness
   - Update RAG service integration

4. **Week 7-8**: Implement cross-reference system
   - Create `CrossReferenceDetector` class
   - Add to document processing pipeline
   - Performance testing and optimization

### Phase 2: Beta Testing (Weeks 9-10)

1. **Internal Testing**:
   - Deploy to staging environment
   - Test with real Slack data
   - Gather performance metrics

2. **Feature Flags**:
   ```python
   # Add feature flags for gradual rollout
   FEATURE_FLAGS = {
       'conversation_chunking_enabled': False,
       'enhanced_clustering_enabled': False, 
       'improved_query_engine_enabled': False,
       'cross_references_enabled': False
   }
   ```

3. **A/B Testing Setup**:
   - Compare old vs new processing methods
   - Measure query response quality
   - Monitor performance impact

### Phase 3: Production Rollout (Weeks 11-12)

1. **Gradual Enablement**:
   - Enable one feature at a time
   - Monitor system performance
   - Gather user feedback

2. **Monitoring Dashboard**:
   - Track processing times
   - Monitor memory usage
   - Alert on errors or performance degradation

3. **Rollback Plan**:
   - Quick disable switches for each feature
   - Database backup before major changes
   - Clear rollback procedures

## Backwards Compatibility

### Maintaining Current Features

The implementation is designed to be **fully backwards compatible**:

1. **Default Behavior**: All new features are **disabled by default**
2. **Configuration-Driven**: Features enabled through configuration, not code changes
3. **Fallback Mechanisms**: If new features fail, system falls back to existing behavior

### Configuration Examples

```python
# Conservative configuration (current behavior)
SLACK_CONFIG = {
    'use_conversation_chunking': False,
    'use_enhanced_clustering': False,
    'add_cross_references': False,
    'use_improved_query_engine': False
}

# Progressive configuration (new features enabled)
SLACK_CONFIG_ENHANCED = {
    'use_conversation_chunking': True,
    'conversation_chunk_size': 2000,
    'use_enhanced_clustering': True,
    'max_cluster_size': 35,
    'semantic_threshold': 0.15,
    'add_cross_references': True,
    'cross_ref_similarity_threshold': 0.3,
    'use_improved_query_engine': True,
    'quality_threshold': 0.3
}
```

## Error Handling and Resilience

### Comprehensive Error Handling

```python
# Example error handling pattern for all new components
class EnhancedMessageProcessor:
    def process_messages(self, messages):
        try:
            # New enhanced processing
            return self._enhanced_process(messages)
        except Exception as e:
            logger.error(f"Enhanced processing failed: {e}")
            logger.info("Falling back to basic processing")
            # Fallback to basic processing
            return self._basic_process(messages)
    
    def _enhanced_process(self, messages):
        # New functionality with proper error handling
        pass
    
    def _basic_process(self, messages):
        # Original simple processing as fallback
        pass
```

### Health Checks

```python
# Add health check endpoints
def health_check_rag_enhancements():
    """Check health of RAG enhancement features."""
    health_status = {
        'conversation_chunker': 'healthy',
        'enhanced_processor': 'healthy', 
        'improved_query_engine': 'healthy',
        'cross_reference_detector': 'healthy'
    }
    
    # Test each component
    try:
        # Test conversation chunker
        chunker = ConversationAwareChunker()
        chunker.chunk_messages([])  # Test with empty input
    except Exception as e:
        health_status['conversation_chunker'] = f'unhealthy: {e}'
    
    # Similar tests for other components...
    
    return health_status
```

## Documentation and Training

### API Documentation Updates

Update the API documentation to include new features:

```python
# Example endpoint documentation
"""
POST /api/v1/documents/ingest/

Enhanced Slack document ingestion with multiple processing options.

Parameters:
- use_conversation_chunking (bool): Enable conversation-aware chunking
- use_enhanced_clustering (bool): Enable semantic clustering 
- add_cross_references (bool): Add cross-document references
- quality_threshold (float): Minimum quality score for documents

Response includes enhanced metadata:
- chunk_type: Type of chunk (conversation, thread, temporal)
- cluster_type: Type of cluster (topic_based, thread_based, temporal)  
- quality_score: Document quality score (0.0-1.0)
- cross_references: List of related documents
- topics: Extracted topics from content
"""
```

### Training Materials

Create comprehensive training materials:

1. **Configuration Guide**: How to enable and configure new features
2. **Performance Guide**: Expected performance characteristics and tuning
3. **Troubleshooting Guide**: Common issues and solutions
4. **Migration Guide**: How to migrate from old to new system

## Performance Benchmarks

### Expected Improvements

Based on the RAGByLlamaIndex implementation, expect these improvements:

1. **Search Relevance**: 15-25% improvement in result relevance
2. **Context Preservation**: 40-60% better conversation context retention
3. **Cross-Document Discovery**: New capability for finding related discussions
4. **Query Understanding**: 20-30% better understanding of follow-up questions

### Performance Targets

Set clear performance targets:

```python
PERFORMANCE_TARGETS = {
    'document_processing': {
        'max_time_per_1000_messages': 30.0,  # seconds
        'memory_usage_max': 512,  # MB
        'max_documents_per_minute': 100
    },
    'query_processing': {
        'max_response_time': 3.0,  # seconds
        'cross_reference_lookup_time': 0.5,  # seconds
        'cache_hit_rate_min': 0.8  # 80%
    }
}
```

## Conclusion

This implementation guide provides a comprehensive roadmap for enhancing RAGSearch with advanced features from RAGByLlamaIndex. The key principles followed are:

### ✅ **Preservation of Existing Features**
- All current functionality remains intact
- Multi-tenant architecture preserved
- Django integration maintained
- Time-based document organization kept as default

### ✅ **Incremental Enhancement**
- Features can be enabled individually
- Gradual rollout with feature flags
- Comprehensive fallback mechanisms
- A/B testing capabilities

### ✅ **Performance Focused**
- Efficient algorithms and data structures
- Intelligent caching strategies
- Batch processing for scalability
- Memory usage optimization

### ✅ **Production Ready**
- Comprehensive error handling
- Health checks and monitoring
- Performance benchmarks
- Clear rollback procedures

### 🎯 **Key Benefits**
1. **Better Conversation Context**: Messages grouped by semantic meaning rather than just time
2. **Improved Search Relevance**: Context-aware query processing with expansion
3. **Knowledge Discovery**: Cross-references reveal related discussions across time
4. **Quality Assessment**: Automatic scoring helps surface the most valuable content

### 📋 **Next Steps for Implementation**

1. **Start with Phase 1**: Begin with conversation-aware chunking as it provides immediate benefits
2. **Test Thoroughly**: Use the provided test cases and create additional ones for your specific data
3. **Monitor Performance**: Implement monitoring from day one to track improvements
4. **Gather Feedback**: Work with users to validate that the enhancements provide real value
5. **Iterate**: Use feedback and performance data to tune parameters and improve results

The modular design ensures you can implement these enhancements at your own pace while maintaining system stability and backwards compatibility. Each phase builds upon the previous one, allowing for a gradual but comprehensive improvement to your RAG system's capabilities.