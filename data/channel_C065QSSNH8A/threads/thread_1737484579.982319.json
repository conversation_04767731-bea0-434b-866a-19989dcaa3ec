{"thread_ts": "1737484579.982319", "channel_id": "C065QSSNH8A", "reply_count": 3, "replies": [{"ts": "1737485474.029939", "text": "Call: <https://app.fireflies.ai/view/Curana-Stride-Weekly::f513NWmSRVHnnN1E>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737484579.982319", "attachments": [{"from_url": "https://app.fireflies.ai/view/Curana-Stride-Weekly::f513NWmSRVHnnN1E", "image_url": "https://share.fireflies.ai/og-preview?title=Curana%252FStride%2520Weekly&avatar=https%253A%252F%252Flh3.googleusercontent.com%252Fa%252FACg8ocLxa585LtVb3an-R0E4-Mu1JcHKCE7D1Z-6URxYinD3xEAbiQ%253Ds96-c&date=Tue%252C%252021%2520Jan&name=Amanda%2520Ingraham", "image_width": 1280, "image_height": 700, "image_bytes": 602673, "service_icon": "https://app.fireflies.ai/favicon.ico", "id": 1, "original_url": "https://app.fireflies.ai/view/Curana-Stride-Weekly::f513NWmSRVHnnN1E", "fallback": "Curana/Stride Weekly - Meeting recording by Fireflies.ai", "text": "The meeting focused on personal updates and crucial data and system updates for Curana/Stride, addressing troubleshooting needs.", "title": "Curana/Stride Weekly - Meeting recording by Fireflies.ai", "title_link": "https://app.fireflies.ai/view/Curana-Stride-Weekly::f513NWmSRVHnnN1E", "service_name": "app.fireflies.ai"}], "blocks": [{"type": "rich_text", "block_id": "Xb0gk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Call: "}, {"type": "link", "url": "https://app.fireflies.ai/view/Curana-Stride-Weekly::f513NWmSRVHnnN1E"}]}]}]}, {"ts": "1737510652.977899", "text": "merge is showing some sync failures on their end. I will take a look today.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737484579.982319", "blocks": [{"type": "rich_text", "block_id": "j/pR9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "merge is showing some sync failures on their end. I will take a look today."}]}]}]}, {"ts": "1737532109.776949", "text": "<@U07EJ2LP44S> Merge has some issue with sync. I have reached out to the support. Will keep this thread updated.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737484579.982319", "subtype": "thread_broadcast", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "kzUIX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " <PERSON><PERSON> has some issue with sync. I have reached out to the support. Will keep this thread updated."}]}]}]}], "created_at": "2025-05-22T22:00:49.351956"}