{"date": "2024-06-05", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1717607203.650539", "text": "I need to discuss with you on some scenarios if I have to be aware in case ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1717607203.650539", "reply_count": 2, "edited": {"user": "U0690EB5JE5", "ts": "1717611097.000000"}, "blocks": [{"type": "rich_text", "block_id": "0dXYX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I need to discuss with you on some scenarios if I have to be aware in case "}]}]}]}, {"ts": "1717607153.179969", "text": "<@U065H3M6WJV> not really, the goal is to avoid creating new cycle because editing corrupts the data. Will discuss in next eng discussion.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vxnVc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " not really, the goal is to avoid creating new cycle because editing corrupts the data. Will discuss in next eng discussion"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1717605615.629659", "text": "<@U0690EB5JE5> So after this change, we may need to start a new cycle for more realistic testing?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/B02N", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " So after this change, we may need to start a new cycle for more realistic testing?"}]}]}]}, {"ts": "1717590689.963149", "text": "So Please note that, updating cycle may mess up the merit changes  w.r.t submission/approval statuses, flags/comments :slightly_smiling_face:", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1717590785.000000"}, "blocks": [{"type": "rich_text", "block_id": "PhvH5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So Please note that, updating cycle may mess up the merit changes  w.r.t submission/approval statuses, flags/comments "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1717590661.845819", "text": "I am thinking to rewrite this API to make it update only not delete and recreate and optimize along the way.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9CqQ8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am thinking to rewrite this API to make it update only not delete and recreate and optimize along the way."}]}]}]}, {"ts": "1717590287.015469", "text": "<@U04DKEFP1K8> <@U065H3M6WJV> I am working on optimizing cycle builder create/update API. There are some major issues due to the way currently the API updates/creates the merit rows.\nEvery time any changes made to cycle config. It deletes all the rows and creates new merit rows copying adjustments made and recalculating budgets/guidelines.  Issues because of this\n• There is bug due to which flags/comments are not carried over, even if carried over we are losing the timestamp info.\n• This is also would cause issues with submission statuses, since the old rows are deleted and created again, we need to handle the new merit view 2 statuses as well i.e. copy over\n• This also would mess up the audit log if implemented later\n•  And another issue is,  The delete/create is done at last step (screenshot below) of guidelines and the following publish buttons have no meaning. Basically after clicking the continue button in this step triggers create/update API which deletes and recreates merit rows and post that changes are irreversible. Also if there is any failure in between, all the past merit changes are also lost.\n• Another related issue <https://compiify.atlassian.net/browse/COM-3089>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1717590287.015469", "reply_count": 4, "edited": {"user": "U0690EB5JE5", "ts": "1717591682.000000"}, "reactions": [{"name": "eyes", "users": ["U065H3M6WJV"], "count": 1}], "files": [{"id": "F076RS4FP60", "mode": "tombstone"}], "blocks": [{"type": "rich_text", "block_id": "ZPGtw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I am working on optimizing cycle builder create/update API. There are some major issues due to the way currently the API updates/creates the merit rows.\nEvery time any changes made to cycle config. It deletes all the rows and creates new merit rows copying adjustments made and recalculating budgets/guidelines.  Issues because of this\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There is bug due to which flags/comments are not carried over, even if carried over we are losing the timestamp info."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "This is also would cause issues with submission statuses, since the old rows are deleted and created again, we need to handle the new merit view 2 statuses as well i.e. copy over"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "This also would mess up the audit log if implemented later"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": " And another issue is,  The delete/create is done at last step (screenshot below) of guidelines and the following publish buttons have no meaning. Basically after clicking the continue button in this step triggers create/update API which deletes and recreates merit rows and post that changes are irreversible. Also if there is any failure in between, all the past merit changes are also lost."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Another related issue "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3089"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1717547427.657149", "text": "Will keep the email toggle in settings page?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1717547427.657149", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2hMwU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will keep the email toggle in settings page?"}]}]}]}, {"ts": "1717547393.325189", "text": "<@U065H3M6WJV> that should be quick one. Will get that done today. One more thing came to my mind for flags. Should we give ability to toggle flagging at employee level?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1717547393.325189", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "2lzSk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " that should be quick one. Will get that done today. One more thing came to my mind for flags. Should we give ability to toggle flagging at employee level"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1717534457.963979", "text": "Thinking about the challenge of finishing / releasing Merit 2.0 to customers around June 15 -- one thing I realize is that our new Merit 2.0 has email notifications built in, and we don't (yet) have a toggle for those. It could be extremely disruptive to a customer if their preliminary testing sends real emails to their managers and executives.\n\nWe should at the very least have a way to toggle the emails on/off. I don't think we have to get too clever about having emails rerouted to the HR admin or anything like that -- if they do want to preview them, they can test within the HR team. :wink:\n\nDo you think we can incorporate that within, or soon after, the June 15 timeline <@U0690EB5JE5>?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Cw4pi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thinking about the challenge of finishing / releasing Merit 2.0 to customers around June 15 -- one thing I realize is that our new Merit 2.0 has email notifications built in, and we don't (yet) have a toggle for those. It could be extremely disruptive to a customer if their preliminary testing sends real emails to their managers and executives.\n\nWe should at the very least have a way to toggle the emails on/off. I don't think we have to get too clever about having emails rerouted to the HR admin or anything like that -- if they do want to preview them, they can test within the HR team. "}, {"type": "emoji", "name": "wink", "unicode": "1f609"}, {"type": "text", "text": "\n\nDo you think we can incorporate that within, or soon after, the June 15 timeline "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "?"}]}]}]}], "created_at": "2025-05-22T21:35:34.597958"}