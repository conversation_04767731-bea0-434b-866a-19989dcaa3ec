{"date": "2025-02-12", "channel_id": "C065QSSNH8A", "message_count": 38, "messages": [{"ts": "1739376020.375069", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> Curana’s bonus prefill issue is not actually an issue. Basically when cycle was first created with prefill set to true the adjustments were pre-filled, but after setting prefill flag to false later the already filled values will not be overwritten as expected. Currently there is no way for system to know if the values were manually input or pre-filled by system. I have reset the pre-filled values and cycle should be good now.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9", "U07EJ2LP44S"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "fwy/x", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "text", "text": "<PERSON><PERSON><PERSON>’s "}, {"type": "text", "text": "bonus "}, {"type": "text", "text": "prefill issue is not actually an issue. Basically when cycle was first created with prefill set to true the adjustments were pre-filled, but after setting prefill flag to false later the already filled values will not be overwritten as expected. Currently there is no way for system to know if the values were manually input or pre-filled by system."}, {"type": "text", "text": " I have reset the pre-filled values and cycle should be good now."}]}]}]}, {"ts": "1739370057.908689", "text": "<@U0690EB5JE5> Valgenesis is looking to have the letters by 3/1. They may have a change to the Portugal one - if so when do you need that change (obviously asap but what is the date you have to have it)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739370057.908689", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "rgON0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Valgenesis is looking to have the letters by 3/1. They may have a change to the Portugal one - if so when do you need that change (obviously asap but what is the date you have to have it)"}]}]}]}, {"ts": "1739323748.268069", "text": "<@U07EJ2LP44S>\nWe have two fields \"Employment Type\" and \"Compensation Type\".  We can hide comp type on all the views and  can use \"Employment Type\" or \"Worker Type\" to display as they like as these fields don't have any impact on the functionality. However, Its also confusing to me from the message above when they say they would do adjustments on annualized numbers. Do they mean they want all the employees to be treated like Salaried employees? If its just about \"Compensation Type\" being displayed as `Part Time` we can address that quickly by changing it to `Hourly` in the UI. Let me know.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739296532.147379", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1739323827.000000"}, "blocks": [{"type": "rich_text", "block_id": "il/Gx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "\nWe have two fields \"Employment Type\" and \"Compensation Type\".  We can hide comp type on all the views and  can use \"Employment Type\" or \"Worker Type\" to display as they like as these fields don't have any impact on the functionality. However, Its also confusing to me from the message above when they say they would do adjustments on annualized numbers. Do they mean they want all the employees to be treated like Salaried employees? If its just about \"Compensation Type\" being displayed as "}, {"type": "text", "text": "Part Time", "style": {"code": true}}, {"type": "text", "text": " we can address that quickly by changing it to "}, {"type": "text", "text": "Hourly", "style": {"code": true}}, {"type": "text", "text": " in the UI. Let me know."}]}]}]}, {"ts": "1739323370.853989", "text": "We can discuss in detail in our next meeting.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "AF4IA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can discuss in detail in our next meeting."}]}]}]}, {"ts": "1739323324.586419", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> running two cycles in parallel is not possible at this point and needs work and a lot of testing. We can go ahead with the solution proposed above but lets wait for them to come back with their CEO's approval. Regarding new column, need details what exactly this should contain, is it same as `Bonus Award` column that we have today but just read only?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739323324.586419", "reply_count": 8, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "zvOaM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " running two cycles in parallel is not possible at this point and needs work and a lot of testing. We can go ahead with the solution proposed above but lets wait for them to come back with their CEO's approval. Regarding new column, need details what exactly this should contain, is it same as "}, {"type": "text", "text": "Bonus Award", "style": {"code": true}}, {"type": "text", "text": " column that we have today but just read only?"}]}]}]}, {"ts": "1739299468.892899", "text": "we'd basically kick of both cycles more or less at the same time - two weeks from now", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TR7qI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we'd basically kick of both cycles more or less at the same time - two weeks from now"}]}]}]}, {"ts": "**********.896629", "text": "It's gonna need some manual effort to set up local logins but I think we can deal with that", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "qVshM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It's gonna need some manual effort to set up local logins but I think we can deal with that"}]}]}]}, {"ts": "**********.225959", "text": "they'll use SSO in their main account", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XO2XC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "they'll use SSO in their main account"}]}]}]}, {"ts": "**********.993139", "text": "i dont' know what work is involved in local logins though, if we can just run a script for that or what", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pYhtC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i dont' know what work is involved in local logins though, if we can just run a script for that or what"}]}]}]}, {"ts": "**********.709379", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "**********.880109", "text": "overall it shouldn't be too much work to manage since the second account is only ~150 people and only bonus", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "6TE5M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "overall it shouldn't be too much work to manage since the second account is only ~150 people and only bonus"}]}]}]}, {"ts": "**********.891439", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "**********.045269", "text": "he needs to get approval since we can't do it in one cycle/account", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gV7X0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "he needs to get approval since we can't do it in one cycle/account"}]}]}]}, {"ts": "**********.628259", "text": "he's checking with his CEO on this solution as well", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "UCk5K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "he's checking with his CEO on this solution as well"}]}]}]}, {"ts": "**********.463509", "text": "we will have an answer by tomorrow", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "v176H", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we will have an answer by tomorrow"}]}]}]}, {"ts": "**********.332869", "text": "ok lets see how much of an eng effort it is to add a new column", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9PZcY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok lets see how much of an eng effort it is to add a new column"}]}]}]}, {"ts": "1739299154.079709", "text": "correct", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "j+ZXP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "correct"}]}]}]}, {"ts": "**********.286129", "text": "which i'm thinking is not posible", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6Y4NJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "which i'm thinking is not posible"}]}]}]}, {"ts": "**********.674049", "text": "unless mahesh has a way to run a bonus cycle concurrently with a merit cycle in the same account", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+CbBu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "unless mahesh has a way to run a bonus cycle concurrently with a merit cycle in the same account"}]}]}]}, {"ts": "**********.889149", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "**********.837169", "text": "since two cycles are not possible in one account this is the only workaround", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VQ1v9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "since two cycles are not possible in one account this is the only workaround"}]}]}]}, {"ts": "**********.794059", "text": "2 week cycle plus a buffer to get data out", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FT5g4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "2 week cycle plus a buffer to get data out"}]}]}]}, {"ts": "**********.694299", "text": "spin it up and turn it off", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Tv0tN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "spin it up and turn it off"}]}]}]}, {"ts": "**********.871169", "text": "ok", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "luWBi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok"}]}]}]}, {"ts": "**********.954519", "text": "i think one month", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OGvTg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i think one month"}]}]}]}, {"ts": "1739299075.292179", "text": "would they need the new env  to say active only for a say 2 months. or they want to keep it active as long as they have a contract with us", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "tO9D8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "would they need the new env  to say active only for a say 2 months. or they want to keep it active as long as they have a contract with us"}]}]}]}, {"ts": "**********.517369", "text": "yes", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IB8ze", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes"}]}]}]}, {"ts": "**********.749879", "text": "it was more the $400 a month we were talking about, just the hosting fee of an account", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4TDVK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "it was more the $400 a month we were talking about, just the hosting fee of an account"}]}]}]}, {"ts": "**********.316949", "text": "it's just adding a column so i'm thinking he's not going to love that", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "no0Mb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "it's just adding a column so i'm thinking he's not going to love that"}]}]}]}, {"ts": "**********.778199", "text": "i would think that would be harder to justify but i don't know", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rWGb+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i would think that would be harder to justify but i don't know"}]}]}]}, {"ts": "**********.330019", "text": "ok let me work with <PERSON><PERSON><PERSON> in the evening and get back to you the estimated cost of engineering work", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/CWnZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok let me work with <PERSON><PERSON><PERSON> in the evening and get back to you the estimated cost of engineering work"}]}]}]}, {"ts": "1739298892.303119", "text": "i don't know about that", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eOMXN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i don't know about that"}]}]}]}, {"ts": "1739298883.350189", "text": "Given the size of their company, <PERSON> should not have any problem in paying this amount", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "A9pnk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Given the size of their company, <PERSON> should not have any problem in paying this amount"}]}]}]}, {"ts": "1739298844.903549", "text": "It should not be more than $3K", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Os7CP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It should not be more than $3K"}]}]}]}, {"ts": "1739298825.826859", "text": "will they be open to paying the cost of additional engineering work for the 2nd item? I can check with <PERSON><PERSON><PERSON> what would be", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yB9Zz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will they be open to paying the cost of additional engineering work for the 2nd item? I can check with <PERSON><PERSON><PERSON> what would be"}]}]}]}, {"ts": "1739298756.122619", "text": "also he said he'd be more than happy to pay an invoice for the cost of the second enivorment to get it done", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0wjKG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "also he said he'd be more than happy to pay an invoice for the cost of the second enivorment to get it done"}]}]}]}, {"ts": "**********.323949", "text": "I'll summarize here - they are changing their bonus payout date, so they want to run the bonus cycle separately so they can do that immediately. the merit would still happen on their normal timeline, paid out a month later. To do this we'd need to do a couple things:\n\n1. create a second account and run a bonus-only cycle in that account. we'd only put the bonus-eligible employees in there, no paybands etc, and we'd run a fast 2 week cycle to get bonuses completed. assuming we'd need to do +1@email addresses, and local logins for 150 people \n2. remove bonus from the primary account, but ideally create a new column where we could display the bonus amount that was received from the bonus cycle. read only, just a data point. ", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1vypP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'll summarize here - they are changing their bonus payout date, so they want to run the bonus cycle separately so they can do that immediately. the merit would still happen on their normal timeline, paid out a month later. To do this we'd need to do a couple things:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "create a second account and run a bonus-only cycle in that account. we'd only put the bonus-eligible employees in there, no paybands etc, and we'd run a fast 2 week cycle to get bonuses completed. assuming we'd need to do +1@email addresses, and local logins for 150 people "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "remove bonus from the primary account, but ideally create a new column where we could display the bonus amount that was received from the bonus cycle. read only, just a data point. "}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"ts": "**********.299989", "text": "<PERSON>- we can meet and I can run it by <PERSON><PERSON><PERSON> in the evening", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Z8See", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>- we can meet and I can run it by <PERSON><PERSON><PERSON> in the evening"}]}]}]}], "created_at": "2025-05-22T21:35:34.699636"}