{"date": "2024-08-10", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1723253903.340039", "text": "<@U04DS2MBWP4> <@U04DKEFP1K8> <@U07EJ2LP44S> I am see that customers are facing issues with upload feature and I had already anticipated how painful it is now for any one uses as error messaging is not clear and started working on making it more flexible, stable and helpful error messages, we will mostly be done with this early next week with UX work as well. Keep you posted on the status.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "heart", "users": ["U04DS2MBWP4", "U07EJ2LP44S"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "ctLKb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am see that customers are facing issues with upload feature and I had already anticipated how painful it is now for any one uses as error messaging is not clear and started working on making it more flexible, stable and helpful error messages, we will mostly be done with this early next week with UX work as well. Keep you posted on the status."}]}]}]}, {"ts": "1723245239.495359", "text": "<@U04DKEFP1K8> When you get a chance, can you please send the  CWA requirements for different planning levels? so that we can incorporate into the new comp builder design", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1723245239.495359", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "B2zpu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " When you get a chance, can you please send the  CWA requirements for different planning levels? so that we can incorporate into the new comp builder design"}]}]}]}, {"ts": "1723235314.942249", "text": "<@U07EJ2LP44S> were we able to address the data upload issues playq and practifi were having?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1723235314.942249", "reply_count": 10, "blocks": [{"type": "rich_text", "block_id": "6mawE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " were we able to address the data upload issues playq and practifi were having?"}]}]}]}, {"ts": "1723234453.532679", "text": "<@U04DS2MBWP4> can you share the <PERSON> Watters call from fireflies? Also Valgenesis? Would it be easier if I used it myself?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723234453.532679", "reply_count": 6, "edited": {"user": "U07EJ2LP44S", "ts": "1723234523.000000"}, "blocks": [{"type": "rich_text", "block_id": "wofQO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " can you share the <PERSON> Watt<PERSON> call from fireflies? Also Valgenesis? Would it be easier if I used it myself?"}]}]}]}], "created_at": "2025-05-22T21:35:34.630577"}