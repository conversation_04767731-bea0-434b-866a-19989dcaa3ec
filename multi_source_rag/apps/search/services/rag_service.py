"""
RAG Service - Production Ready

This is the main RAG service that consolidates all RAG functionality from the previous
three-service architecture into a single, comprehensive service with feature flags:

- Standard search with citation tracking
- Query expansion with domain-specific enhancement
- Multi-step reasoning with sub-question decomposition
- Enhanced prompt templates with query classification
- Hybrid search capabilities
- Advanced routing and response synthesis

Features are controlled through boolean flags, eliminating code duplication while
maintaining all advanced capabilities.
"""

import logging
import time
from typing import Any, Dict, List, Optional, Tuple

from django.contrib.auth.models import User

# LlamaIndex imports
from llama_index.core import VectorStoreIndex
from llama_index.core.query_engine import (
    RouterQueryEngine,
    RetrieverQueryEngine,
    CitationQueryEngine,
    SubQuestionQueryEngine,
    MultiStepQueryEngine
)
from llama_index.core.retrievers import (
    VectorIndexRetriever,
    QueryFusionRetriever
)
from llama_index.core.tools import QueryEngineTool
from llama_index.core.selectors import LLMSingleSelector
from llama_index.core.response_synthesizers import get_response_synthesizer
from llama_index.core.postprocessor import SimilarityPostprocessor, LLMRerank
from llama_index.core.prompts import PromptTemplate

# App imports
from apps.accounts.models import Tenant
from apps.core.utils.collection_manager import get_collection_name
from apps.core.utils.llama_index_llm import get_llm
from apps.core.utils.llama_index_vectorstore import get_vector_store
from apps.core.utils.response_formatter import format_response
from apps.core.utils.prompt_templates import get_prompt_template, format_prompt
from apps.core.utils.query_classifier import classify_query
from apps.documents.models import DocumentChunk
from apps.search.models import ResultCitation, SearchQuery, SearchResult

logger = logging.getLogger(__name__)


class RAGService:
    """
    Consolidated RAG Service combining all functionality from previous services.
    
    This service provides all RAG capabilities through feature flags:
    - Standard search with intelligent routing
    - Query expansion for better semantic matching
    - Multi-step reasoning for complex queries
    - Enhanced prompt templates with query classification
    - Citation tracking and source attribution
    - Comprehensive error handling and monitoring
    """

    def __init__(self, user: User, tenant_slug: Optional[str] = None):
        """
        Initialize the consolidated RAG service.

        Args:
            user: User performing the search
            tenant_slug: Tenant slug to search in
        """
        self.user = user
        self.tenant_slug = tenant_slug

        # Get tenant from user profile if not provided
        if not self.tenant_slug and hasattr(user, "profile") and user.profile.tenant:
            self.tenant_slug = user.profile.tenant.slug
            self.tenant = user.profile.tenant
        else:
            # Get tenant from slug
            try:
                self.tenant = Tenant.objects.get(slug=self.tenant_slug)
            except Tenant.DoesNotExist:
                logger.error(f"Tenant with slug {self.tenant_slug} not found")
                raise ValueError(f"Tenant with slug '{self.tenant_slug}' not found")

        # Initialize components
        self._initialize_components()

        # Processing statistics
        self.stats = {
            "queries_processed": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0,
            "query_expansions_used": 0,
            "multi_step_queries": 0,
            "hybrid_searches": 0
        }

    def _initialize_components(self):
        """Initialize all LlamaIndex components and engines."""
        logger.info("🔧 Initializing consolidated RAG system components...")

        # Initialize LlamaIndex components if not already done
        self._ensure_llamaindex_initialized()

        # Build specialized query engines
        self.query_engines = self._build_query_engines()

        # Build router query engine
        self.router_engine = self._build_router_engine()

        # Build citation engine
        self.citation_engine = self._build_citation_engine()

        # Build advanced engines (created on-demand for performance)
        self.sub_question_engine = None
        self.multi_step_engine = None

        logger.info("✅ Consolidated RAG system components initialized")

    def _ensure_llamaindex_initialized(self):
        """Ensure LlamaIndex components are initialized."""
        try:
            # Initialize embedding models
            from apps.core.utils.llama_index_embeddings import initialize_embedding_models
            initialize_embedding_models()

            # Initialize LLMs
            from apps.core.utils.llama_index_llm import initialize_llms
            initialize_llms()

            # Initialize vector stores
            from apps.core.utils.llama_index_vectorstore import initialize_vector_stores
            initialize_vector_stores()

            # Set global LlamaIndex settings
            from llama_index.core import Settings
            from apps.core.utils.llama_index_setup import llama_index_registry

            # CRITICAL FIX: Use consistent embedding model across the entire system
            from apps.core.utils.embedding_consistency import set_global_embedding_model, validate_embedding_consistency

            # Set the globally consistent embedding model
            set_global_embedding_model()

            # Validate consistency
            if not validate_embedding_consistency():
                logger.error("Embedding model consistency validation failed!")
                raise RuntimeError("Embedding model inconsistency detected")

            logger.info("Set globally consistent embedding model for RAG service")

            # Set global LLM
            llm = llama_index_registry.get_llm()
            if llm:
                Settings.llm = llm
                logger.info("Set global LLM")

            logger.info("LlamaIndex components initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LlamaIndex components: {str(e)}")
            # Continue with degraded functionality
            pass

    def _build_query_engines(self) -> Dict[str, QueryEngineTool]:
        """
        Build specialized query engines for different content types.

        Returns:
            Dictionary of query engine tools
        """
        engines = {}

        # Conversation engine (Slack, chat)
        engines["conversation"] = QueryEngineTool.from_defaults(
            query_engine=self._build_conversation_engine(),
            name="conversation_search",
            description="Search Slack conversations, chat messages, and discussion threads"
        )

        # Code engine (GitHub, code files)
        engines["code"] = QueryEngineTool.from_defaults(
            query_engine=self._build_code_engine(),
            name="code_search",
            description="Search GitHub repositories, code files, PRs, and issues"
        )

        # Document engine (general documents)
        engines["document"] = QueryEngineTool.from_defaults(
            query_engine=self._build_document_engine(),
            name="document_search",
            description="Search general documents, markdown files, and text content"
        )

        return engines

    def _build_conversation_engine(self, use_enhanced_features: bool = False) -> RetrieverQueryEngine:
        """Build specialized engine for conversation content."""
        collection_name = get_collection_name(self.tenant_slug, intent="conversation")
        vector_store = get_vector_store(collection_name=collection_name)

        # Get embedding model for content type
        from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
        embed_model = get_embedding_model_for_content(content_type="conversation")

        index = VectorStoreIndex.from_vector_store(vector_store, embed_model=embed_model)

        # Create retriever with configurable features
        if use_enhanced_features:
            # Enhanced mode with more sophisticated retrieval
            vector_retriever = VectorIndexRetriever(
                index=index,
                similarity_top_k=10
            )

            # Use QueryFusionRetriever for enhanced search
            retriever = QueryFusionRetriever(
                retrievers=[vector_retriever],
                llm=get_llm(),
                num_queries=3,
                use_async=False
            )

            response_mode = "tree_summarize"
            postprocessors = [
                SimilarityPostprocessor(similarity_cutoff=0.7),
                LLMRerank(top_n=5, llm=get_llm())
            ]
        else:
            # Standard mode optimized for performance
            retriever = VectorIndexRetriever(
                index=index,
                similarity_top_k=10
            )

            response_mode = "compact"
            postprocessors = [
                SimilarityPostprocessor(similarity_cutoff=0.7)
            ]

        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            llm=get_llm(),
            response_synthesizer=get_response_synthesizer(
                response_mode=response_mode,
                llm=get_llm()
            ),
            node_postprocessors=postprocessors
        )

    def _build_code_engine(self) -> RetrieverQueryEngine:
        """Build specialized engine for code content."""
        collection_name = get_collection_name(self.tenant_slug, intent="code")
        vector_store = get_vector_store(collection_name=collection_name)

        # Get embedding model for content type
        from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
        embed_model = get_embedding_model_for_content(content_type="code")

        index = VectorStoreIndex.from_vector_store(vector_store, embed_model=embed_model)

        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=15
        )

        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            llm=get_llm(),
            response_synthesizer=get_response_synthesizer(
                response_mode="compact",
                llm=get_llm()
            ),
            node_postprocessors=[
                SimilarityPostprocessor(similarity_cutoff=0.6)
            ]
        )

    def _build_document_engine(self) -> RetrieverQueryEngine:
        """Build specialized engine for general documents."""
        collection_name = get_collection_name(self.tenant_slug, intent="document")
        vector_store = get_vector_store(collection_name=collection_name)

        # Get embedding model for content type
        from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
        embed_model = get_embedding_model_for_content(content_type="text")

        index = VectorStoreIndex.from_vector_store(vector_store, embed_model=embed_model)

        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=12
        )

        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            llm=get_llm(),
            response_synthesizer=get_response_synthesizer(
                response_mode="tree_summarize",
                llm=get_llm()
            ),
            node_postprocessors=[
                SimilarityPostprocessor(similarity_cutoff=0.65)
            ]
        )

    def _build_router_engine(self) -> RouterQueryEngine:
        """Build router query engine for intelligent query routing."""
        return RouterQueryEngine(
            selector=LLMSingleSelector.from_defaults(llm=get_llm()),
            query_engine_tools=list(self.query_engines.values()),
            verbose=True
        )

    def _build_citation_engine(self) -> RetrieverQueryEngine:
        """Build optimized citation engine with enhanced prompts."""
        # Use the conversation engine's retriever for citations
        collection_name = get_collection_name(self.tenant_slug, intent="conversation")
        vector_store = get_vector_store(collection_name=collection_name)
        index = VectorStoreIndex.from_vector_store(vector_store)

        # Use retriever with configurable results for comprehensive context
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=15  # OPTIMIZED: Reduced from 20 to 15 to prevent MAX_TOKENS issues
        )

        # Create query engine with enhanced prompt templates
        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            llm=get_llm(),
            response_synthesizer=get_response_synthesizer(
                response_mode="compact",  # Single LLM call mode
                llm=get_llm(),
                use_async=False
            )
        )

    def _build_sub_question_engine(self) -> SubQuestionQueryEngine:
        """Build sub-question query engine for complex query decomposition."""
        if self.sub_question_engine is None:
            self.sub_question_engine = SubQuestionQueryEngine.from_defaults(
                query_engine_tools=list(self.query_engines.values()),
                llm=get_llm(),
                use_async=False,
                verbose=True
            )
        return self.sub_question_engine

    def _build_multi_step_engine(self) -> MultiStepQueryEngine:
        """Build multi-step query engine for iterative reasoning."""
        if self.multi_step_engine is None:
            self.multi_step_engine = MultiStepQueryEngine.from_defaults(
                query_engine=self.router_engine,
                num_steps=3,
                early_stopping=True
            )
        return self.multi_step_engine

    def _expand_query_basic(self, query_text: str) -> str:
        """
        Basic query expansion without HyDE.
        Adds context and synonyms to improve search results.
        """
        # Add contextual terms based on query type
        expanded_terms = []

        query_lower = query_text.lower()

        # Add domain-specific terms
        if any(word in query_lower for word in ['issue', 'problem', 'bug', 'error']):
            expanded_terms.extend(['reported', 'mentioned', 'discussed', 'identified'])

        if any(word in query_lower for word in ['authentication', 'auth', 'login']):
            expanded_terms.extend(['security', 'access', 'credentials', 'permission'])

        if any(word in query_lower for word in ['latest', 'recent', 'new']):
            expanded_terms.extend(['current', 'updated', 'fresh', 'today'])

        # Combine original query with expanded terms
        if expanded_terms:
            expanded_query = f"{query_text} {' '.join(expanded_terms[:3])}"
            logger.debug(f"Expanded query: '{query_text}' -> '{expanded_query}'")
            return expanded_query

        return query_text

    def _query_with_enhanced_prompts(self, query_text: str):
        """
        Execute query with enhanced prompt templates based on query classification.

        Args:
            query_text: The query text

        Returns:
            LlamaIndex response with enhanced prompts
        """
        # Classify the query to determine the appropriate prompt template
        classification = classify_query(query_text)
        query_type = classification["type"]
        confidence = classification.get("confidence", 0.0)

        logger.info(f"Classified query as '{query_type}' (confidence: {confidence:.2f})")

        # Get the enhanced prompt template for this query type
        prompt_template = get_prompt_template(
            query=query_text,
            query_type=query_type
        )

        # Create a custom LlamaIndex prompt template with correct variable names
        # LlamaIndex expects {context_str} and {query_str} by default
        llama_template = prompt_template.replace("{context}", "{context_str}").replace("{question}", "{query_str}")

        # Create the LlamaIndex prompt template
        llama_prompt = PromptTemplate(llama_template)

        # Log the template being used for debugging
        logger.info(f"Using enhanced prompt template for '{query_type}' query")
        logger.debug(f"Template preview: {llama_template[:200]}...")

        # Update ALL relevant prompt templates in the citation engine
        prompt_updates = {
            "response_synthesizer:text_qa_template": llama_prompt,
            "response_synthesizer:refine_template": llama_prompt,
            "response_synthesizer:summary_template": llama_prompt,
        }

        # Apply the prompt updates
        self.citation_engine.update_prompts(prompt_updates)

        # Execute the query with enhanced prompts
        response = self.citation_engine.query(query_text)

        logger.info(f"Query executed with enhanced '{query_type}' prompt template")

        return response

    def search(
        self,
        query_text: str,
        top_k: int = 20,
        metadata_filter: Optional[Dict[str, Any]] = None,
        output_format: str = "text",
        min_relevance_score: float = 0.15,
        use_hybrid_search: bool = True,
        use_context_aware: bool = True,
        use_query_expansion: bool = False,
        use_multi_step_reasoning: bool = False,
        reasoning_mode: str = "sub_question"  # "sub_question" or "multi_step"
    ) -> Tuple[SearchResult, List[Tuple[Any, float]]]:
        """
        Consolidated search method with all RAG capabilities.

        Args:
            query_text: Query text
            top_k: Number of results to return
            metadata_filter: Filter to apply to search results
            output_format: Output format ('text', 'json', 'markdown', 'table')
            min_relevance_score: Minimum relevance score for documents
            use_hybrid_search: Whether to use hybrid search
            use_context_aware: Whether to use context-aware search (enhanced prompts)
            use_query_expansion: Whether to use query expansion
            use_multi_step_reasoning: Whether to use multi-step reasoning
            reasoning_mode: Type of reasoning ("sub_question" or "multi_step")

        Returns:
            Tuple of (SearchResult, retrieved_documents)
        """
        start_time = time.time()

        try:
            # Create search query record
            search_query = self._create_search_query(query_text, metadata_filter)

            # Step 1: Query Expansion (if enabled)
            if use_query_expansion:
                logger.info("Using query expansion")
                expanded_query = self._expand_query_basic(query_text)
                self.stats["query_expansions_used"] += 1
            else:
                expanded_query = query_text

            # Step 2: Choose search strategy based on features
            if use_multi_step_reasoning:
                if reasoning_mode == "sub_question":
                    logger.info("Using sub-question reasoning")
                    engine = self._build_sub_question_engine()
                    response = engine.query(expanded_query)
                    self.stats["multi_step_queries"] += 1
                elif reasoning_mode == "multi_step":
                    logger.info("Using multi-step reasoning")
                    engine = self._build_multi_step_engine()
                    response = engine.query(expanded_query)
                    self.stats["multi_step_queries"] += 1
                else:
                    logger.warning(f"Unknown reasoning mode: {reasoning_mode}, using enhanced prompts")
                    response = self._query_with_enhanced_prompts(expanded_query)
            elif use_context_aware:
                # Use enhanced prompt templates
                response = self._query_with_enhanced_prompts(expanded_query)
            else:
                # Use standard citation engine
                response = self.citation_engine.query(expanded_query)

            if use_hybrid_search:
                self.stats["hybrid_searches"] += 1

            # Extract source nodes for citations
            source_nodes = response.source_nodes if hasattr(response, 'source_nodes') else []

            # Filter by relevance score
            filtered_nodes = [
                node for node in source_nodes
                if hasattr(node, 'score') and node.score is not None and node.score >= min_relevance_score
            ][:top_k]

            # Calculate processing time
            processing_time = time.time() - start_time

            # Create search result
            search_result = self._create_search_result(
                search_query, response, filtered_nodes, output_format, processing_time
            )

            # Create citations
            self._create_citations(search_result, filtered_nodes)

            # Update statistics
            self._update_stats(processing_time)

            # Convert nodes to documents for return
            retrieved_docs = self._convert_nodes_to_documents(filtered_nodes)

            logger.info(f"Consolidated search completed in {processing_time:.2f}s with {len(retrieved_docs)} results")

            return search_result, retrieved_docs

        except Exception as e:
            logger.error(f"Error in consolidated search: {str(e)}", exc_info=True)
            # Create fallback search result
            search_query = self._create_search_query(query_text, metadata_filter)
            processing_time = time.time() - start_time

            search_result = SearchResult.objects.create(
                search_query=search_query,
                user=self.user,
                generated_answer=f"Error processing query: {str(e)}",
                retriever_score_avg=0.0,
                llm_confidence_score=0.0,
            )

            return search_result, []

    def _create_search_query(
        self,
        query_text: str,
        metadata_filter: Optional[Dict[str, Any]] = None
    ) -> SearchQuery:
        """
        Create a search query record.

        Args:
            query_text: Query text
            metadata_filter: Metadata filter

        Returns:
            SearchQuery instance
        """
        return SearchQuery.objects.create(
            tenant=self.tenant,
            user=self.user,
            query_text=query_text,
            search_params={
                "metadata_filter": metadata_filter or {},
                "service_type": "consolidated_rag"
            }
        )

    def _create_search_result(
        self,
        search_query: SearchQuery,
        response: Any,
        source_nodes: List[Any],
        output_format: str,
        processing_time: float
    ) -> SearchResult:
        """
        Create a search result record.

        Args:
            search_query: Search query
            response: LlamaIndex response
            source_nodes: Source nodes
            output_format: Output format
            processing_time: Processing time in seconds

        Returns:
            SearchResult instance
        """
        # Extract response text from LlamaIndex response object
        response_text = ""
        if hasattr(response, 'response'):
            response_text = response.response
        elif hasattr(response, 'answer'):
            response_text = response.answer
        else:
            response_text = str(response)

        # Format response based on output format
        formatted_answer = format_response(
            response_text,
            output_format,
            source_nodes,
            search_query.query_text
        )

        # Calculate average retriever score
        avg_score = 0.0
        if source_nodes:
            scores = [getattr(node, 'score', 0.0) for node in source_nodes]
            avg_score = sum(scores) / len(scores)

        return SearchResult.objects.create(
            search_query=search_query,
            user=self.user,
            generated_answer=formatted_answer,
            retriever_score_avg=avg_score,
            llm_confidence_score=min(avg_score * 1.2, 1.0)  # Boost confidence slightly
        )

    def _create_citations(
        self,
        search_result: SearchResult,
        source_nodes: List[Any]
    ) -> List[ResultCitation]:
        """
        Create citation records from source nodes with deduplication.

        Args:
            search_result: Search result
            source_nodes: Source nodes

        Returns:
            List of ResultCitation instances
        """
        citations = []
        seen_chunks = set()  # Track chunks we've already created citations for

        for node in source_nodes:
            try:
                # Try to find the corresponding document chunk using EmbeddingMetadata
                node_id = node.node_id

                # Use EmbeddingMetadata to map vector_id to chunk
                from apps.documents.models import EmbeddingMetadata
                chunk = EmbeddingMetadata.get_chunk_by_vector_id(node_id)

                # Ensure chunk belongs to the correct tenant
                if chunk and chunk.tenant != self.tenant:
                    chunk = None

                if chunk:
                    # Check if we've already created a citation for this chunk
                    if chunk.id in seen_chunks:
                        logger.debug(f"Skipping duplicate citation for chunk {chunk.id} (node {node_id})")
                        continue

                    # Check if citation already exists in database (extra safety)
                    existing_citation = ResultCitation.objects.filter(
                        result=search_result,
                        document_chunk=chunk
                    ).first()

                    if existing_citation:
                        logger.debug(f"Citation already exists for result {search_result.id} and chunk {chunk.id}")
                        citations.append(existing_citation)
                        seen_chunks.add(chunk.id)
                        continue

                    # Create new citation
                    citation = ResultCitation.objects.create(
                        result=search_result,
                        document_chunk=chunk,
                        relevance_score=getattr(node, 'score', 0.0),
                        rank=len(citations) + 1  # Use actual citation count for rank
                    )
                    citations.append(citation)
                    seen_chunks.add(chunk.id)

                else:
                    # Skip creating citation if no chunk is found
                    logger.warning(f"Could not find chunk for node {node_id}, skipping citation")
                    continue

            except Exception as e:
                logger.error(f"Error creating citation for node {node.node_id}: {str(e)}")

        logger.info(f"Created {len(citations)} unique citations for search result {search_result.id}")
        return citations

    def _convert_nodes_to_documents(self, nodes: List[Any]) -> List[Tuple[Any, float]]:
        """Convert LlamaIndex nodes to documents for backward compatibility."""
        documents = []
        for node in nodes:
            if hasattr(node, 'node') and hasattr(node, 'score'):
                documents.append((node.node, node.score))
            elif hasattr(node, 'score'):
                documents.append((node, node.score))
        return documents

    def _update_stats(self, processing_time: float):
        """
        Update processing statistics.

        Args:
            processing_time: Processing time in seconds
        """
        self.stats["queries_processed"] += 1
        self.stats["total_processing_time"] += processing_time
        self.stats["average_processing_time"] = (
            self.stats["total_processing_time"] / self.stats["queries_processed"]
        )

    def get_stats(self) -> Dict[str, Any]:
        """
        Get processing statistics.

        Returns:
            Dictionary of processing statistics
        """
        return {
            **self.stats,
            "service_type": "consolidated_rag",
            "features_enabled": {
                "query_expansion": True,
                "multi_step_reasoning": True,
                "hybrid_search": True,
                "citation_tracking": True,
                "enhanced_prompts": True
            }
        }

    def get_system_stats(self) -> Dict[str, Any]:
        """
        Get system statistics (alias for get_stats for backward compatibility).

        Returns:
            Dictionary of processing statistics
        """
        return self.get_stats()
