{"date": "2024-12-07", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1733522598.408439", "text": "<@U0690EB5JE5> let's go over the UX phase 1 and tab grouping scope on Sunday night my time. your monday morning, if that works for you", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733522598.408439", "reply_count": 6, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}, {"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "/KyBf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " let's go over the UX phase 1 and tab grouping scope on Sunday night my time. your monday morning, if that works for you"}]}]}]}, {"ts": "1733512697.669579", "text": "<@U0690EB5JE5> we had an option to set guidelines by both performance ratings and compa ratio. I am not sure why we removed that option from the cycle builder. It was a functional feature. Can we pls put it back and <PERSON><PERSON><PERSON> prefers to use both ratings and compa ration", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733512697.669579", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "s3clX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we had an option to set guidelines by both performance ratings and compa ratio. I am not sure why we removed that option from the cycle builder. It was a functional feature. Can we pls put it back and <PERSON><PERSON><PERSON> prefers to use both ratings and compa ration"}]}]}]}, {"ts": "1733512695.911009", "text": "its taking a second to populate the flag type", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "02UcN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "its taking a second to populate the flag type"}]}]}]}, {"ts": "1733512684.513389", "text": "Its' working now so I think it was a page load timing thing", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IhJ8A", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Its' working now so I think it was a page load timing thing"}]}]}]}, {"ts": "1733512606.933679", "text": "Let me test it again and see if I can replicate", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rlEsi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me test it again and see if I can replicate"}]}]}]}, {"ts": "1733512586.653549", "text": "<@U07EJ2LP44S> there was also a flag issue with flags not updating after out of guidelines recommendation. It worked for one employee and did not work for another? Are you able to replicate that issue for Tithely or are we good there?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sGckO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " there was also a flag issue with flags not updating after out of guidelines recommendation. It worked for one employee and did not work for another? Are you able to replicate that issue for Tith<PERSON> or are we good there?"}]}]}]}, {"ts": "1733512469.305469", "text": "<@U0690EB5JE5> I am not sure how we ended up with two epics for Tithely. Is there a way to merge those two epics to avoid any confusion?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733512469.305469", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "IoWgv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am not sure how we ended up with two epics for Tith<PERSON>. Is there a way to merge those two epics to avoid any confusion?"}]}]}]}, {"ts": "1733511957.374279", "text": "That's super weird, I don't see it either. I swear I did it yesterday. Maybe my internet stalled out when I was creating or something. It's here now: <https://compiify.atlassian.net/browse/COM-4020>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733511957.374279", "reply_count": 1, "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14103::ba7b896af64e4098a2c10f6da0abfc31", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4020?atlOrigin=eyJpIjoiOTAxNjg2N2Y4ZDM5NDVlN2FkNjk0N2QzZDQ4YzE4ZWUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4020 Bug in Audit History on Tithelys Environment>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14103::feacee7d1d0944e2a53f935a51a27da0", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14103\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4020\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4020", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "qhtTj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That's super weird, I don't see it either. I swear I did it yesterday. Maybe my internet stalled out when I was creating or something. It's here now: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4020"}]}]}]}, {"ts": "1733510340.808619", "text": "moving it here", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733425974.694719", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "TueZ+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "moving it here"}]}]}]}], "created_at": "2025-05-22T21:35:34.680816"}