{"date": "2025-01-30", "channel_id": "C065QSSNH8A", "message_count": 17, "messages": [{"ts": "1738258491.429299", "text": "<@U0690EB5JE5> I added the promotion component to Valgenesis and published the cycle. When I went back in and tried to upload the promotion sheet, it says there is no promotion component.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738258491.429299", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "sLZPh", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I added the promotion component to Valgenesis and published the cycle. When I went back in and tried to upload the promotion sheet, it says there is no promotion component."}]}]}]}, {"ts": "1738257717.427609", "text": "<@U07EJ2LP44S> Cycle is deleted", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738182725.579089", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "byKDI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Cycle is deleted"}]}]}]}, {"ts": "1738254775.354849", "text": "<@U0690EB5JE5> Bonus is not workign in Curana at all. He is supposed to demo to his CEO in 1 hour. Can you fix", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "a52B8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Bonus is not workign in Curana at all. He is supposed to demo to his CEO in 1 hour. Can you fix"}]}]}]}, {"ts": "1738253606.796499", "text": "If you need any assistance with data uploads. Please do share details here and I will get that done by next day.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "e1/ld", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If you need any assistance with data uploads. Please do share details here and I will get that done by next day."}]}]}]}, {"ts": "1738253469.995269", "text": "<@U07EJ2LP44S> I will be moving my schedule back to my usual one and will be available until 9 to 9.30 AM *PST*. Also any issues reported unless they are complete blocker would be addressed in 24 hours. Please do set expectations to customer accordingly.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1738253484.000000"}, "blocks": [{"type": "rich_text", "block_id": "3bxEC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I will be moving my schedule back to my usual one and will be available until 9 to 9.30 AM "}, {"type": "text", "text": "PST", "style": {"bold": true}}, {"type": "text", "text": ". Also any issues reported unless they are complete blocker would be addressed in 24 hours. Please do set expectations to customer accordingly."}]}]}]}, {"ts": "1738252931.255979", "text": "will try to fix this from next deployment", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ELCQh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will try to fix this from next deployment"}]}]}]}, {"ts": "1738252904.627339", "text": "<@U07EJ2LP44S> can you clear cookies and try. I did a deployment today.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ozQca", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can you clear cookies and try. I did a deployment today."}]}]}]}, {"ts": "1738252883.900529", "text": "Mayday. 'Something went wrong'", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "72q9P", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Mayday. 'Something went wrong'"}]}]}]}, {"ts": "1738252874.906599", "text": "<PERSON><PERSON><PERSON> is doing the same thing", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "R3Wgr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> is doing the same thing"}]}]}]}, {"ts": "1738252549.011159", "text": "<@U0690EB5JE5> test.stridehr seems to be down", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OzCy6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " test.<PERSON><PERSON> seems to be down"}]}]}]}, {"ts": "1738249134.834559", "text": "I have a call with <PERSON><PERSON><PERSON> today that may cross over the leadership call, FYI.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "/sWNo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have a call with <PERSON><PERSON><PERSON> today that may cross over the leadership call, FYI."}]}]}]}, {"ts": "1738235251.859219", "text": "<@U07M6QKHUC9> Finally I was able to fix the infra issue. default templates are set up and you can generate the letters from UI. Will discuss more in the meeting.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738166593.755499", "subtype": "thread_broadcast", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "d0z8a", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Finally I was able to fix the infra issue. default templates are set up and you can generate the letters from UI. Will discuss more in the meeting."}]}]}]}, {"ts": "1738223006.363629", "text": "OK sounds good", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xiPWu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OK sounds good"}]}]}]}, {"ts": "1738210711.112329", "text": "Team,\nI need to travel my native to attend an event on Sat. I will be off second half of Friday IST. <@U07M6QKHUC9> I am available for demo call later my night.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6VV7R", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Team,\nI need to travel my native to attend an event on Sat. I will be off second half of Friday IST. "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I am available for demo call later my night."}]}]}]}, {"ts": "1738186764.095919", "text": "<@U0690EB5JE5> Unable to edit performance ratings in Curana's environment <https://www.loom.com/share/9f9b5c2fe6cd496aaccd4fd7f24703f3?sid=41f37fc7-9b62-4f6e-80a4-84d6dd82862f>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738186764.095919", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "Z1mQ4", "video_url": "https://www.loom.com/embed/9f9b5c2fe6cd496aaccd4fd7f24703f3?sid=41f37fc7-9b62-4f6e-80a4-84d6dd82862f&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/9f9b5c2fe6cd496aaccd4fd7f24703f3-93aaf6cc39c79955-4x3.jpg", "alt_text": "Performance Ratings Setup for Demo 🚀", "title": {"type": "plain_text", "text": "Performance Ratings Setup for Demo 🚀", "emoji": true}, "title_url": "https://www.loom.com/share/9f9b5c2fe6cd496aaccd4fd7f24703f3", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 1 min  ", "emoji": true}}, {"type": "section", "block_id": "GtFlQ", "text": {"type": "mrkdwn", "text": ":information_source: In this video, I’m walking through the setup of performance ratings in <PERSON><PERSON><PERSON>'s account. I noticed that there are currently no ratings in the system...", "verbatim": false}}, {"type": "actions", "block_id": "eZYCj", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/9f9b5c2fe6cd496aaccd4fd7f24703f3?sid=41f37fc7-9b62-4f6e-80a4-84d6dd82862f"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"9f9b5c2fe6cd496aaccd4fd7f24703f3\",\"videoName\":\"Performance Ratings Setup for Demo 🚀\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://www.loom.com/share/9f9b5c2fe6cd496aaccd4fd7f24703f3?sid=41f37fc7-9b62-4f6e-80a4-84d6dd82862f", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "za0wh", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Unable to edit performance ratings in Curana's environment "}, {"type": "link", "url": "https://www.loom.com/share/9f9b5c2fe6cd496aaccd4fd7f24703f3?sid=41f37fc7-9b62-4f6e-80a4-84d6dd82862f"}]}]}]}, {"ts": "1738182725.579089", "text": "<@U0690EB5JE5> We're ready to delete Diversified's cycle and recreate it. Whenever you can do it after you get in!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738182725.579089", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "SrRfA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We're ready to delete Diversified's cycle and recreate it. Whenever you can do it after you get in!"}]}]}]}, {"ts": "1738175984.700879", "text": "<@U07EJ2LP44S> I am logging off. Will take up any issues during my day.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "UDzmM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am logging off. Will take up any issues during my day."}]}]}]}], "created_at": "2025-05-22T21:35:34.703581"}