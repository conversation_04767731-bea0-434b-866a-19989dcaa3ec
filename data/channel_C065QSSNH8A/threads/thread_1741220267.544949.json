{"thread_ts": "1741220267.544949", "channel_id": "C065QSSNH8A", "reply_count": 9, "replies": [{"ts": "1741225754.550429", "text": "<@U06HN8XDC5A> Please take a look.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741220267.544949", "reactions": [{"name": "white_check_mark", "users": ["U06HN8XDC5A"], "count": 1}], "files": [{"id": "F08H2H14L7J", "created": 1741225748, "timestamp": 1741225748, "name": "Screenshot 2025-03-06 at 7.18.55 AM.png", "title": "Screenshot 2025-03-06 at 7.18.55 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 255289, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08H2H14L7J/screenshot_2025-03-06_at_7.18.55___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08H2H14L7J/download/screenshot_2025-03-06_at_7.18.55___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H2H14L7J-363db0925c/screenshot_2025-03-06_at_7.18.55___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H2H14L7J-363db0925c/screenshot_2025-03-06_at_7.18.55___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H2H14L7J-363db0925c/screenshot_2025-03-06_at_7.18.55___am_360.png", "thumb_360_w": 360, "thumb_360_h": 134, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H2H14L7J-363db0925c/screenshot_2025-03-06_at_7.18.55___am_480.png", "thumb_480_w": 480, "thumb_480_h": 178, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H2H14L7J-363db0925c/screenshot_2025-03-06_at_7.18.55___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H2H14L7J-363db0925c/screenshot_2025-03-06_at_7.18.55___am_720.png", "thumb_720_w": 720, "thumb_720_h": 267, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H2H14L7J-363db0925c/screenshot_2025-03-06_at_7.18.55___am_800.png", "thumb_800_w": 800, "thumb_800_h": 297, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H2H14L7J-363db0925c/screenshot_2025-03-06_at_7.18.55___am_960.png", "thumb_960_w": 960, "thumb_960_h": 356, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08H2H14L7J-363db0925c/screenshot_2025-03-06_at_7.18.55___am_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 380, "original_w": 2666, "original_h": 990, "thumb_tiny": "AwARADDSwfWjB/vfpRS0AJ+NH40vaigAx70fjRRQAUUUUAFFFFABRRRQB//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08H2H14L7J/screenshot_2025-03-06_at_7.18.55___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08H2H14L7J-6d03483a7f", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Y6T9s", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": " Please take a look."}]}]}]}, {"ts": "1741250936.631639", "text": "proration modifier is 0 , for this user . so increment is going to 0.", "user": "U06HN8XDC5A", "type": "message", "thread_ts": "1741220267.544949", "files": [{"id": "F08GC57AFGV", "created": 1741250933, "timestamp": 1741250933, "name": "Screenshot 2025-03-06 at 2.18.49 PM.png", "title": "Screenshot 2025-03-06 at 2.18.49 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U06HN8XDC5A", "user_team": "T04DM97F1UM", "editable": false, "size": 88759, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08GC57AFGV/screenshot_2025-03-06_at_2.18.49___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08GC57AFGV/download/screenshot_2025-03-06_at_2.18.49___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GC57AFGV-eee2b56b95/screenshot_2025-03-06_at_2.18.49___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GC57AFGV-eee2b56b95/screenshot_2025-03-06_at_2.18.49___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GC57AFGV-eee2b56b95/screenshot_2025-03-06_at_2.18.49___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 311, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GC57AFGV-eee2b56b95/screenshot_2025-03-06_at_2.18.49___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 415, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08GC57AFGV-eee2b56b95/screenshot_2025-03-06_at_2.18.49___pm_160.png", "original_w": 590, "original_h": 510, "thumb_tiny": "AwApADCqBwDzkd+aQrz90n86BjA6fpS8e36UAMbaB0ppqQso7foKaWBHTnPpQAyilJyc8fhSUATjOB19utIcjkk4pwjGB86dP89qXyh/fT/P4UrgRMCOSxx9KaAuPvfpUxHlkAEEH/PpUL7TyAR9aAG0UUUwJvT/AD/Wjqf8/wCNMWlNAClgB1OOn+eaTcp69KUdfwpv8X4UAMop1NoA/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08GC57AFGV/screenshot_2025-03-06_at_2.18.49___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08GC57AFGV-3e33daa3cb", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "3cTnc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "proration modifier is 0 , for this user . so increment is going to 0."}]}]}]}, {"ts": "1741250989.440959", "text": "Why is it 0?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741220267.544949", "blocks": [{"type": "rich_text", "block_id": "Pa+hQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Why is it 0?"}]}]}]}, {"ts": "1741252225.675169", "text": "MEGAN ABBOTT,\n<PERSON><PERSON><PERSON><PERSON><PERSON> ST THOMAS,\nKEVIN FINKBINER,\nTIMOTHY PURI,\n<PERSON>GE<PERSON> THOMPSON,\nPATRICIA RUSHMAN,\n<PERSON><PERSON><PERSON><PERSON><PERSON> MCLEMORE,\n<PERSON><PERSON><PERSON><PERSON> COSLET,\n<PERSON><PERSON><PERSON> MEDITZ,\nTANDRIA EARLEY,\nMICH<PERSON><PERSON> GLEESON,\nSARAH INGISON,\nASHLEY WOOLFOLK,\n\nfor all these user names , cute off date &lt; hire date\n|", "user": "U06HN8XDC5A", "type": "message", "thread_ts": "1741220267.544949", "blocks": [{"type": "rich_text", "block_id": "M7USk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "MEGAN ABBOTT,\n<PERSON><PERSON><PERSON><PERSON><PERSON> ST THOMAS,\nKEVIN FINKBINER,\nTIMOTHY PURI,\n<PERSON><PERSON><PERSON> THOMPSON,\nPATRICIA RUSHMAN,\n<PERSON><PERSON><PERSON><PERSON><PERSON> MCLEMORE,\n<PERSON><PERSON><PERSON><PERSON> COSLET,\n<PERSON><PERSON><PERSON> MEDITZ,\nTANDRIA EARLEY,\nMICH<PERSON><PERSON> GLEESON,\nSARAH INGISON,\nASHLEY WOOLFOLK,\n\nfor all these user names , cute off date < hire date\n|"}]}]}]}, {"ts": "1741274110.605149", "text": "<@U07M6QKHUC9> This is a case we have hit for the first time. We have the fix but need to test a bit more before deployment for regression. I will get some time my morning and will get it fixed that time. Please buy another day for this fix. The issue due to HireDate being greater then cut-off date in eligibility rules which is an exception system never supported this case.\nWe have to handle this exception in case of proration. Proration is set to 0 due to this scenario and adjustments have been calculated to 0.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741220267.544949", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1741274519.000000"}, "blocks": [{"type": "rich_text", "block_id": "9gF+X", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " This is a case we have hit for the first time. We have the fix but need to test a bit more before deployment for regression. I will get some time my morning and will get it fixed that time. Please buy another day for this fix. The issue due to HireDate being greater then cut-off date in eligibility rules which is an exception system never supported this case.\nWe have to handle this exception in case of proration. Proration is set to 0 due to this scenario and adjustments have been calculated to 0."}]}]}]}, {"ts": "1741274820.388149", "text": "Ok", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741220267.544949", "blocks": [{"type": "rich_text", "block_id": "PfDzz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok"}]}]}]}, {"ts": "1741320934.975219", "text": "I haven’t got a chance, but will definitely close this before US morning starts.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741220267.544949", "blocks": [{"type": "rich_text", "block_id": "DvCmj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I "}, {"type": "text", "text": "haven’t"}, {"type": "text", "text": " got a chance, but will definitely close this before US morning starts."}]}]}]}, {"ts": "1741321139.022919", "text": "Yes, please they are in the middle of a cycle and I don’t think we can hold up on it for too long", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1741220267.544949", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "fIcMc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, please they are in the middle of a cycle and I don’t think we can hold up on it for too long"}]}]}]}, {"ts": "1741344153.351829", "text": "<@U07M6QKHUC9> fix is deployed. Please note for this employee proration is 0.16 and prorated increase will be way lesser than the actual proposed value.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741220267.544949", "subtype": "thread_broadcast", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "VTs+8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " fix is deployed. Please note for this employee proration is 0.16 and prorated increase will be way lesser than the actual proposed value."}]}]}]}], "created_at": "2025-05-22T22:00:49.367378"}