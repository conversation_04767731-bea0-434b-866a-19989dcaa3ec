#!/usr/bin/env python3
"""
Test the curana search fix by making a direct API call.
"""

import requests
import json
import time

def test_curana_search():
    """Test the curana search through the API."""
    print("🧪 Testing Curana Search Fix")
    print("=" * 50)
    
    # Login first
    login_url = "http://localhost:8000/accounts/login/"
    search_url = "http://localhost:8000/search/query/"
    
    session = requests.Session()
    
    # Get CSRF token
    print("🔐 Getting CSRF token...")
    response = session.get(login_url)
    if response.status_code != 200:
        print(f"❌ Failed to get login page: {response.status_code}")
        return False
    
    # Extract CSRF token
    csrf_token = None
    for line in response.text.split('\n'):
        if 'csrfmiddlewaretoken' in line and 'value=' in line:
            start = line.find('value="') + 7
            end = line.find('"', start)
            csrf_token = line[start:end]
            break
    
    if not csrf_token:
        print("❌ Could not find CSRF token")
        return False
    
    print(f"✅ Got CSRF token: {csrf_token[:10]}...")
    
    # Login
    print("🔐 Logging in...")
    login_data = {
        'username': 'testuser',
        'password': 'testpass123',
        'csrfmiddlewaretoken': csrf_token
    }
    
    response = session.post(login_url, data=login_data)
    if response.status_code != 200 and response.status_code != 302:
        print(f"❌ Login failed: {response.status_code}")
        return False
    
    print("✅ Login successful")
    
    # Get search page to get new CSRF token
    search_page_url = "http://localhost:8000/search/"
    response = session.get(search_page_url)
    if response.status_code != 200:
        print(f"❌ Failed to get search page: {response.status_code}")
        return False
    
    # Extract new CSRF token
    csrf_token = None
    for line in response.text.split('\n'):
        if 'csrfmiddlewaretoken' in line and 'value=' in line:
            start = line.find('value="') + 7
            end = line.find('"', start)
            csrf_token = line[start:end]
            break
    
    if not csrf_token:
        print("❌ Could not find CSRF token on search page")
        return False
    
    print(f"✅ Got search CSRF token: {csrf_token[:10]}...")
    
    # Test the curana search
    print("🔍 Testing 'whats latest on curana?' search...")
    search_data = {
        'query': 'whats latest on curana?',
        'csrfmiddlewaretoken': csrf_token
    }
    
    start_time = time.time()
    response = session.post(search_url, data=search_data)
    end_time = time.time()
    
    print(f"⏱️  Search took {end_time - start_time:.2f} seconds")
    print(f"📊 Response status: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ Search failed: {response.status_code}")
        print(f"Response: {response.text[:500]}...")
        return False
    
    # Check response content
    response_text = response.text.lower()
    
    # Check for success indicators
    if 'curana' in response_text:
        print("✅ Response contains 'curana' content!")
        
        # Check for citations
        if 'citation' in response_text or 'source' in response_text:
            print("✅ Response contains citations!")
        else:
            print("⚠️  No citations found in response")
        
        # Check for "none of the provided sources" error
        if 'none of the provided sources' in response_text:
            print("❌ Still getting 'none of the provided sources' error")
            return False
        else:
            print("✅ No 'none of the provided sources' error!")
        
        print("🎉 Curana search fix is working!")
        return True
    else:
        print("❌ Response does not contain curana content")
        
        # Check for error messages
        if 'none of the provided sources' in response_text:
            print("❌ Getting 'none of the provided sources' error")
        
        print(f"Response preview: {response.text[:500]}...")
        return False

if __name__ == "__main__":
    success = test_curana_search()
    if success:
        print("\n🎉 SUCCESS: Curana search fix is working correctly!")
    else:
        print("\n❌ FAILURE: Curana search fix needs more work")
