#!/usr/bin/env python3
"""
Data-Driven Query Test Suite - Based on Real Slack Data
Tests all 10 query types using actual conversations from the data folder.
"""

import os
import sys
import django
from datetime import datetime

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def test_query_with_real_data(query, query_type, description, expected_elements):
    """Test a query and validate the response quality."""
    
    print(f"\n🔍 {query_type.upper()}: {description}")
    print(f"📝 Query: '{query}'")
    print(f"🎯 Expected: {expected_elements}")
    print("-" * 80)
    
    try:
        from apps.search.services.rag_service import RAGService
        from django.contrib.auth.models import User
        
        user = User.objects.first()
        rag_service = RAGService(user=user, tenant_slug='stride')
        
        # Execute search with production settings
        search_result, retrieved_docs = rag_service.search(
            query_text=query,
            top_k=15,
            min_relevance_score=0.2,  # Reasonable threshold
            use_hybrid_search=True,
            use_context_aware=True,
            use_query_expansion=False,
            use_multi_step_reasoning=False
        )
        
        citations_count = search_result.citations.count()
        response_length = len(search_result.generated_answer)
        confidence = search_result.llm_confidence_score
        
        # Quality analysis
        response = search_result.generated_answer
        
        # Check for document references (should be clean)
        has_doc_refs = '[Document' in response
        
        # Check for human dates
        import re
        date_pattern = r'\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}\b'
        human_dates = re.findall(date_pattern, response)
        
        # Check for expected elements
        elements_found = []
        for element in expected_elements:
            if element.lower() in response.lower():
                elements_found.append(element)
        
        # Results
        print(f"✅ Search completed successfully")
        print(f"📊 Citations: {citations_count}")
        print(f"📝 Response: {response_length} chars")
        print(f"🎯 Confidence: {confidence:.2f}")
        print(f"📄 Document refs: {'❌ Found' if has_doc_refs else '✅ Clean'}")
        print(f"📅 Human dates: {'✅ ' + str(len(human_dates)) if human_dates else '⚠️ None'}")
        print(f"🔍 Expected elements found: {len(elements_found)}/{len(expected_elements)}")
        
        if elements_found:
            print(f"   Found: {', '.join(elements_found)}")
        
        # Show response preview
        print(f"\n📝 Response Preview:")
        preview = response[:400] + "..." if len(response) > 400 else response
        print(preview)
        
        # Show top citations
        if citations_count > 0:
            print(f"\n📚 Top Citations:")
            for i, citation in enumerate(search_result.citations.all()[:3]):
                print(f"  {i+1}. Score: {citation.relevance_score:.3f}")
                print(f"     Text: {citation.document_chunk.text[:100]}...")
                if citation.document_chunk.document.permalink:
                    print(f"     Link: {citation.document_chunk.document.permalink}")
                print()
        
        # Quality score
        quality_score = (
            (citations_count > 0) * 25 +  # Has citations
            (not has_doc_refs) * 25 +     # Clean response
            (len(human_dates) > 0) * 20 +  # Human dates
            (len(elements_found) / len(expected_elements)) * 30  # Expected elements
        )
        
        print(f"🎯 Quality Score: {quality_score:.0f}%")
        
        return {
            'success': True,
            'citations': citations_count,
            'response_length': response_length,
            'confidence': confidence,
            'has_doc_refs': has_doc_refs,
            'human_dates': len(human_dates),
            'elements_found': len(elements_found),
            'expected_elements': len(expected_elements),
            'quality_score': quality_score,
            'response': response
        }
        
    except Exception as e:
        print(f"❌ Search failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

def main():
    """Test all 10 query types with real data-driven queries."""
    
    print("🚀 Data-Driven Query Test Suite - Real Slack Data")
    print("=" * 80)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Based on actual data analysis, here are realistic queries
    test_queries = [
        # 1. Fact Lookup Questions
        {
            "query": "What did Amanda say about the proration date picker bug?",
            "type": "fact_lookup",
            "description": "Specific person + specific issue",
            "expected": ["Amanda", "proration", "date picker", "bug", "COM-4002"]
        },
        {
            "query": "Who is assigned to the UI/UX changes ticket COM-4036?",
            "type": "fact_lookup", 
            "description": "Ticket assignment lookup",
            "expected": ["Mahesh", "UI/UX", "COM-4036", "assigned"]
        },
        {
            "query": "What is Kapil's role in the company?",
            "type": "fact_lookup",
            "description": "Person role identification",
            "expected": ["Kapil", "CEO", "Founder"]
        },
        
        # 2. Thread Recall / Conversation Lookup
        {
            "query": "Show me the discussion about DegenKolb report changes",
            "type": "thread_recall",
            "description": "Customer-specific feature discussion",
            "expected": ["DegenKolb", "report", "summary row", "cycle changes"]
        },
        {
            "query": "Find the conversation about Alayacare cycle and bonus changes",
            "type": "thread_recall",
            "description": "Customer issue thread",
            "expected": ["Alayacare", "cycle", "bonus", "hourly employees"]
        },
        {
            "query": "Was there a discussion about Precycle budget planning?",
            "type": "thread_recall",
            "description": "Feature planning discussion",
            "expected": ["Precycle", "budget planning", "analytics", "feedback"]
        },
        
        # 3. Opinion or Decision Tracking
        {
            "query": "What was decided about collecting customer feedback on Precycle budget planning?",
            "type": "opinion_tracking",
            "description": "Decision on customer research",
            "expected": ["feedback", "20 customers", "Amanda", "lead", "January"]
        },
        {
            "query": "What did the team agree on for the standup agenda?",
            "type": "opinion_tracking",
            "description": "Meeting decisions",
            "expected": ["standup", "Total Rewards PRD", "action items", "tracker"]
        },
        
        # 4. Time-Based Queries
        {
            "query": "What issues were reported in November 2024?",
            "type": "time_based",
            "description": "Monthly issue tracking",
            "expected": ["November", "2024", "proration", "bug", "Alayacare"]
        },
        {
            "query": "Show me discussions from December 20, 2024",
            "type": "time_based",
            "description": "Specific date filtering",
            "expected": ["December", "2024", "spam", "domain reputation", "Vestwell"]
        },
        
        # 5. Follow-up / Multi-turn Questions  
        {
            "query": "Who was supposed to gather feedback from Curana and AlayaCare?",
            "type": "follow_up",
            "description": "Action item assignment",
            "expected": ["Amanda", "feedback", "Curana", "AlayaCare", "January 15th"]
        },
        
        # 6. Procedural or How-to Retrieval
        {
            "query": "How should we handle unverified contacts in email sequences?",
            "type": "procedural",
            "description": "Process guidance",
            "expected": ["unenroll", "unverified contacts", "sequences", "domain reputation"]
        },
        {
            "query": "What's the process for reviewing cycle changes reports?",
            "type": "procedural", 
            "description": "Review workflow",
            "expected": ["review", "report", "comment", "ticket", "changes"]
        },
        
        # 7. Incident & Alert Related
        {
            "query": "Any discussion about email spam issues?",
            "type": "incident",
            "description": "Email delivery problems",
            "expected": ["spam", "emails", "verified", "Apollo", "domain reputation"]
        },
        {
            "query": "What was the issue with Position In Band for Alayacare?",
            "type": "incident",
            "description": "Customer-specific bug",
            "expected": ["Position In Band", "Alayacare", "hourly employees", "fixed"]
        },
        
        # 8. Person or Team Scoped
        {
            "query": "What has Mahesh been working on lately?",
            "type": "person_scoped",
            "description": "Individual work tracking",
            "expected": ["Mahesh", "UI/UX", "assigned", "ticket", "COM-4036"]
        },
        {
            "query": "What tasks is Amanda responsible for?",
            "type": "person_scoped",
            "description": "Person responsibility tracking",
            "expected": ["Amanda", "feedback", "Precycle", "lead", "project"]
        },
        
        # 9. Multi-hop or Compositional Queries
        {
            "query": "Who reported the proration bug and who is fixing it?",
            "type": "multi_hop",
            "description": "Reporter + assignee connection",
            "expected": ["Amanda", "reported", "Mahesh", "assigned", "proration"]
        },
        {
            "query": "What customer issues led to the highest priority tickets?",
            "type": "multi_hop",
            "description": "Issue priority correlation",
            "expected": ["highest priority", "customer", "DegenKolb", "UI/UX", "COM"]
        },
        
        # 10. Wildcards / Fuzzy Queries
        {
            "query": "Something about compensation builder crashing",
            "type": "fuzzy",
            "description": "Vague issue recall",
            "expected": ["comp builder", "crashed", "weird behavior", "Amanda"]
        },
        {
            "query": "I remember someone mentioning domain reputation problems",
            "type": "fuzzy",
            "description": "Fuzzy problem recall",
            "expected": ["domain reputation", "spam", "unverified", "Kapil"]
        }
    ]
    
    results = {}
    total_quality = 0
    successful_queries = 0
    
    for test_case in test_queries:
        result = test_query_with_real_data(
            test_case["query"],
            test_case["type"], 
            test_case["description"],
            test_case["expected"]
        )
        
        results[test_case["query"]] = result
        
        if result.get('success'):
            successful_queries += 1
            total_quality += result.get('quality_score', 0)
    
    # Generate comprehensive report
    print("\n📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 80)
    
    success_rate = (successful_queries / len(test_queries)) * 100
    avg_quality = total_quality / successful_queries if successful_queries > 0 else 0
    
    print(f"🎯 Success Rate: {successful_queries}/{len(test_queries)} ({success_rate:.0f}%)")
    print(f"🏆 Average Quality Score: {avg_quality:.0f}%")
    
    # Category analysis
    categories = {}
    for test_case in test_queries:
        cat = test_case["type"]
        if cat not in categories:
            categories[cat] = {"total": 0, "success": 0, "quality": 0}
        
        categories[cat]["total"] += 1
        result = results[test_case["query"]]
        if result.get('success'):
            categories[cat]["success"] += 1
            categories[cat]["quality"] += result.get('quality_score', 0)
    
    print(f"\n📋 Category Performance:")
    for cat, stats in categories.items():
        success_pct = (stats["success"] / stats["total"]) * 100
        avg_qual = stats["quality"] / stats["success"] if stats["success"] > 0 else 0
        print(f"  {cat.replace('_', ' ').title()}: {stats['success']}/{stats['total']} ({success_pct:.0f}%) - Avg Quality: {avg_qual:.0f}%")
    
    # Citation analysis
    citations_found = sum(1 for r in results.values() if r.get('citations', 0) > 0)
    print(f"\n📚 Citation Analysis:")
    print(f"  Queries with citations: {citations_found}/{len(test_queries)}")
    print(f"  Citation success rate: {(citations_found/len(test_queries))*100:.0f}%")
    
    # Overall assessment
    print(f"\n🎯 OVERALL ASSESSMENT:")
    if avg_quality >= 80 and success_rate >= 90:
        print("🎉 EXCELLENT: RAG system is production-ready!")
        print("✅ High-quality responses across all query types")
        print("✅ Consistent citation retrieval")
        print("✅ Clean, professional formatting")
    elif avg_quality >= 60 and success_rate >= 75:
        print("✅ GOOD: RAG system performs well with minor improvements needed")
        print("💡 Focus on improving lower-scoring categories")
    else:
        print("⚠️  NEEDS IMPROVEMENT: RAG system requires optimization")
        print("💡 Review search thresholds and prompt templates")
    
    return results

if __name__ == "__main__":
    main()
