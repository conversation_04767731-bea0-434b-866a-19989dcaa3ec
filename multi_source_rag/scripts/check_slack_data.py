#!/usr/bin/env python
"""
Slack Data Validation and Analysis Script

This script analyzes the Slack data in the data/ folder to understand
its structure and validate it before ingestion.

Usage:
    python scripts/check_slack_data.py
"""

import os
import sys
import django
import json
import logging
from pathlib import Path
from datetime import datetime
from collections import defaultdict, Counter

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from apps.documents.interfaces.local_slack import LocalSlackSourceInterface

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def analyze_data_structure(data_dir: str = "data"):
    """Analyze the structure of the data directory."""
    
    print("🔍 ANALYZING SLACK DATA STRUCTURE")
    print("=" * 60)
    
    data_path = Path(data_dir)
    
    if not data_path.exists():
        print(f"❌ Data directory not found: {data_dir}")
        return False
    
    print(f"📁 Data directory: {data_path.absolute()}")
    
    # Find channel directories
    channel_dirs = [d for d in data_path.iterdir() if d.is_dir() and d.name.startswith("channel_")]
    
    print(f"📊 Found {len(channel_dirs)} channel directories:")
    
    for channel_dir in channel_dirs:
        channel_id = channel_dir.name.replace("channel_", "")
        print(f"\n📢 Channel: {channel_id}")
        print(f"   Path: {channel_dir}")
        
        # Check subdirectories
        subdirs = ["messages", "threads", "users", "metadata"]
        for subdir in subdirs:
            subdir_path = channel_dir / subdir
            if subdir_path.exists():
                if subdir == "messages":
                    message_files = list(subdir_path.glob("messages_*.json"))
                    print(f"   📝 Messages: {len(message_files)} files")
                    if message_files:
                        # Show date range
                        dates = []
                        for f in message_files:
                            date_str = f.stem.replace("messages_", "")
                            dates.append(date_str)
                        dates.sort()
                        print(f"      Date range: {dates[0]} to {dates[-1]}")
                
                elif subdir == "threads":
                    thread_files = list(subdir_path.glob("thread_*.json"))
                    print(f"   🧵 Threads: {len(thread_files)} files")
                
                elif subdir == "users":
                    user_files = list(subdir_path.glob("*.json"))
                    print(f"   👥 Users: {len(user_files)} files")
                
                elif subdir == "metadata":
                    metadata_files = list(subdir_path.glob("*.json"))
                    print(f"   📋 Metadata: {len(metadata_files)} files")
            else:
                print(f"   ❌ Missing: {subdir}")
    
    # Check consolidated directory
    consolidated_dir = data_path / "consolidated"
    if consolidated_dir.exists():
        consolidated_files = list(consolidated_dir.glob("*.json"))
        print(f"\n📦 Consolidated files: {len(consolidated_files)}")
        
        # Group by channel and show monthly breakdown
        channel_months = defaultdict(list)
        for f in consolidated_files:
            parts = f.stem.split("_")
            if len(parts) >= 2:
                channel = parts[0]
                month = parts[1]
                channel_months[channel].append(month)
        
        for channel, months in channel_months.items():
            months.sort()
            print(f"   {channel}: {len(months)} months ({months[0]} to {months[-1]})")
    
    return True


def analyze_message_content(data_dir: str = "data", channel_id: str = "C065QSSNH8A", sample_size: int = 5):
    """Analyze message content structure."""
    
    print(f"\n🔍 ANALYZING MESSAGE CONTENT FOR {channel_id}")
    print("=" * 60)
    
    data_path = Path(data_dir)
    channel_dir = data_path / f"channel_{channel_id}"
    messages_dir = channel_dir / "messages"
    
    if not messages_dir.exists():
        print(f"❌ Messages directory not found: {messages_dir}")
        return False
    
    # Get sample message files
    message_files = list(messages_dir.glob("messages_*.json"))
    if not message_files:
        print("❌ No message files found")
        return False
    
    print(f"📊 Found {len(message_files)} message files")
    
    # Analyze sample files
    sample_files = message_files[:sample_size]
    total_messages = 0
    message_types = Counter()
    user_messages = Counter()
    
    for message_file in sample_files:
        print(f"\n📄 Analyzing: {message_file.name}")
        
        try:
            with open(message_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            messages = data.get('messages', [])
            file_message_count = len(messages)
            total_messages += file_message_count
            
            print(f"   Messages: {file_message_count}")
            
            # Analyze message structure
            for msg in messages[:3]:  # Sample first 3 messages
                msg_type = msg.get('type', 'unknown')
                message_types[msg_type] += 1
                
                user = msg.get('user', 'unknown')
                user_messages[user] += 1
                
                # Show message structure
                if msg == messages[0]:  # Show structure for first message
                    print(f"   Sample message structure:")
                    for key, value in msg.items():
                        if isinstance(value, str) and len(value) > 50:
                            value = value[:50] + "..."
                        print(f"     {key}: {type(value).__name__} = {value}")
        
        except Exception as e:
            print(f"   ❌ Error reading file: {e}")
    
    print(f"\n📊 CONTENT ANALYSIS SUMMARY")
    print(f"   Total messages analyzed: {total_messages}")
    print(f"   Message types: {dict(message_types.most_common())}")
    print(f"   Top users: {dict(user_messages.most_common(5))}")
    
    return True


def test_local_slack_interface(data_dir: str = "data", channel_id: str = "C065QSSNH8A"):
    """Test the LocalSlackInterface with the data."""
    
    print(f"\n🧪 TESTING LOCAL SLACK INTERFACE")
    print("=" * 60)
    
    try:
        # Initialize interface
        config = {
            "channel_id": channel_id,
            "data_dir": data_dir,
            "time_period": "monthly",
            "enable_summary": False,
            "quality_threshold": 0.3
        }
        
        interface = LocalSlackSourceInterface(config)
        print(f"✅ LocalSlackInterface initialized")
        
        # Get data info
        print("\n📊 Getting staged data info...")
        data_info = interface.get_staged_data_info()
        
        print(f"   Channel ID: {data_info.channel_id}")
        print(f"   Data directory: {data_info.data_dir}")
        print(f"   Available dates: {len(data_info.available_dates)}")
        print(f"   Total messages: {data_info.total_messages}")
        print(f"   Thread count: {data_info.thread_count}")
        print(f"   User count: {data_info.user_count}")
        print(f"   Last staged: {data_info.last_staged}")
        
        if data_info.available_dates:
            print(f"   Date range: {data_info.available_dates[0]} to {data_info.available_dates[-1]}")
        
        # Test document fetching
        print("\n📥 Testing document fetching...")
        documents = interface.fetch_documents(
            channel_id=channel_id,
            days_back=30,  # Last 30 days
            include_threads=True,
            filter_bots=True,
            time_period="monthly"
        )
        
        print(f"✅ Fetched {len(documents)} documents")
        
        # Analyze documents
        if documents:
            print("\n📄 Document Analysis:")
            for i, doc in enumerate(documents[:3]):  # Show first 3
                title = doc.get("title", "Untitled")
                content_length = len(doc.get("content", ""))
                metadata = doc.get("metadata", {})
                
                print(f"\n   Document {i+1}: {title}")
                print(f"     Content length: {content_length} characters")
                print(f"     Metadata fields: {len(metadata)}")
                
                # Show key metadata
                key_fields = ["period_key", "message_count", "thread_count", "participants", "quality_score"]
                for field in key_fields:
                    if field in metadata:
                        value = metadata[field]
                        if isinstance(value, list) and len(value) > 3:
                            value = f"[{len(value)} items]"
                        print(f"       {field}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing LocalSlackInterface: {e}")
        logger.exception("Error testing interface")
        return False


def main():
    """Main analysis function."""
    
    print("🔍 SLACK DATA ANALYSIS AND VALIDATION")
    print("=" * 80)
    
    data_dir = "data"
    channel_id = "C065QSSNH8A"
    
    try:
        # 1. Analyze data structure
        if not analyze_data_structure(data_dir):
            return 1
        
        # 2. Analyze message content
        if not analyze_message_content(data_dir, channel_id):
            return 1
        
        # 3. Test LocalSlackInterface
        if not test_local_slack_interface(data_dir, channel_id):
            return 1
        
        print("\n" + "=" * 80)
        print("✅ DATA VALIDATION COMPLETED SUCCESSFULLY")
        print("=" * 80)
        print("💡 Your Slack data is ready for ingestion!")
        print("🚀 Run: python scripts/quick_slack_ingest.py")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Fatal error: {str(e)}")
        logger.exception("Fatal error during analysis")
        return 1


if __name__ == "__main__":
    exit(main())
