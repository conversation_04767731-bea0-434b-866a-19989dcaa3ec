{"thread_ts": "1738689869.849069", "channel_id": "C065QSSNH8A", "reply_count": 5, "replies": [{"ts": "1738689941.408779", "text": "<PERSON> too - cannot get table data.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738689869.849069", "blocks": [{"type": "rich_text", "block_id": "LTSD9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> too - cannot get table data."}]}]}]}, {"ts": "1738690044.908859", "text": "(these people all report to richard palya)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738689869.849069", "blocks": [{"type": "rich_text", "block_id": "hBJ6p", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "(these people all report to richard palya)"}]}]}]}, {"ts": "1738690142.703609", "text": "Confirming all three direct reports for <PERSON> are not loading in the merit view or as impersonation. All three have employees in the cycle, all three have a manager role. <PERSON> himself is working fine, but none of his direct reports are.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738689869.849069", "blocks": [{"type": "rich_text", "block_id": "0/qjh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Confirming all three direct reports for <PERSON> are not loading in the merit view or as impersonation. All three have employees in the cycle, all three have a manager role. <PERSON> himself is working fine, but none of his direct reports are."}]}]}]}, {"ts": "1738691924.655969", "text": "<@U07EJ2LP44S> Looking into this. Looks like bulk user creation had some issue.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738689869.849069", "blocks": [{"type": "rich_text", "block_id": "7cH/i", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Looking into this. Looks like bulk user creation had some issue."}]}]}]}, {"ts": "1738692584.353349", "text": "<@U07EJ2LP44S> This should be fixed now. I just re-assigned the role to fix it. It seems like our bulk creation script had some issue.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738689869.849069", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "tIxb/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This should be fixed now. I just re-assigned the role to fix it. It seems like our bulk creation script had some issue."}]}]}]}], "created_at": "2025-05-22T22:00:49.344127"}