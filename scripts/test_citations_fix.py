#!/usr/bin/env python3
"""
Test script to verify the citations fix is working properly.

This script tests the RAG search with UI default parameters to ensure
citations are created and displayed correctly.
"""

import os
import sys
import django

# Setup Django
sys.path.insert(0, '.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

from django.contrib.auth.models import User
from apps.search.services.rag_service import RAGService
import re


def test_citations_fix():
    """Test that citations work with UI default parameters."""
    print("🔍 TESTING CITATIONS FIX")
    print("=" * 50)
    
    # Get test user
    try:
        user = User.objects.get(username='mahesh')
        print(f"✅ Found user: {user.username}")
        print(f"   User tenant: {user.profile.tenant.slug}")
    except User.DoesNotExist:
        print("❌ Test user 'mahesh' not found")
        return False
    
    # Initialize RAG service
    try:
        service = RAGService(user=user, tenant_slug=user.profile.tenant.slug)
        print("✅ RAG service initialized")
    except Exception as e:
        print(f"❌ Failed to initialize RAG service: {e}")
        return False
    
    # Test with UI default parameters
    print("\n🧪 Testing with UI default parameters:")
    print("   - use_hybrid_search=True")
    print("   - use_context_aware=True (enhanced prompts)")
    print("   - use_query_expansion=False")
    print("   - use_multi_step_reasoning=False")
    
    try:
        result, docs = service.search(
            query_text='list issues reported on curana',
            top_k=20,  # Will be reduced to 5 for enhanced prompts
            metadata_filter=None,
            min_relevance_score=0.10,
            use_hybrid_search=True,
            use_context_aware=True,
            use_query_expansion=False,
            use_multi_step_reasoning=False
        )
        
        print(f"\n✅ Search completed successfully!")
        print(f"   Result ID: {result.id}")
        print(f"   Answer length: {len(result.generated_answer)} characters")
        
        # Check citations
        citations_count = result.citations.count()
        print(f"   Citations created: {citations_count}")
        
        if citations_count > 0:
            print("✅ SUCCESS: Citations are working!")
            
            # Check citation numbers in response
            citation_numbers = re.findall(r'\[(\d+)\]', result.generated_answer)
            print(f"   Citation numbers in text: {citation_numbers}")
            
            if citation_numbers:
                print("✅ SUCCESS: Citation numbers are embedded in response!")
            else:
                print("⚠️  WARNING: No citation numbers found in response text")
            
            # Show sample citations
            print("\n📚 Sample citations:")
            for i, citation in enumerate(result.citations.all()[:3]):
                print(f"   [{i+1}] Score: {citation.relevance_score:.3f}")
                print(f"       Document: {citation.document_chunk.document.title}")
                print(f"       Rank: {citation.rank}")
            
            return True
            
        else:
            print("❌ FAILURE: No citations created")
            print(f"   Answer preview: {result.generated_answer[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Search failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("🚀 RAG Citations Fix Test")
    print("Testing the fix for missing citations in search results\n")
    
    success = test_citations_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ALL TESTS PASSED! Citations fix is working correctly.")
        print("\nThe UI should now display citations properly when users")
        print("submit searches with the default settings.")
    else:
        print("❌ TESTS FAILED! Citations fix needs more work.")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
