{"date": "2024-05-11", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1715394441.208169", "text": "<@U065H3M6WJV> You will be talking to engineers directly sometimes with this approach :slightly_smiling_face:", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1715394618.000000"}, "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TMuFm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " You will be talking to engineers directly sometimes with this approach "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1715394404.570659", "text": "<@U065H3M6WJV> <@U04DS2MBWP4> <@U04DKEFP1K8> I would like to suggest a small(may be big) change in the way we used work with Lasoft engineers vs FTEs. The issues/requirements were always flowing via <PERSON><PERSON><PERSON><PERSON> and sometimes me. But with full time engineers, as I expect them to own and act like subject matter experts, thinking to create a new slack channel to report customer issues, bugs and any back and forth questions on the ongoing work. We can still use this channel for any new requirements/product strategy etc.. which are yet be finalized before engineers can start going through the same. Please let me know your thoughts on this :slightly_smiling_face:", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1715394404.570659", "reply_count": 5, "edited": {"user": "U0690EB5JE5", "ts": "1715394492.000000"}, "blocks": [{"type": "rich_text", "block_id": "q86P5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I would like to suggest a small(may be big) change in the way we used work with Lasoft engineers vs FTEs. The issues/requirements were always flowing via Saurab<PERSON> and sometimes me. But with full time engineers, as I expect them to own and act like subject matter experts, thinking to create a new slack channel to report customer issues, bugs and any back and forth questions on the ongoing work. We can still use this channel for any new requirements/product strategy etc.. which are yet be finalized before engineers can start going through the same. Please let me know your thoughts on this "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1715379532.446499", "text": "Hey <@U0690EB5JE5> <@U04DKEFP1K8> - <PERSON><PERSON><PERSON> at Vercara asked how they should input their salary bands and sent me <https://docs.google.com/spreadsheets/d/13vyDxX6tHcJAOpQ-xdNwN5EkfwRb9GOm/edit#gid=295722079|this Excel sheet> to show their existing format. (I uploaded &amp; converted to Google Sheets so we can work on it without the customer's visibility.)\n\nSome questions in the thread....", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1715379532.446499", "reply_count": 7, "edited": {"user": "U065H3M6WJV", "ts": "1715379538.000000"}, "blocks": [{"type": "rich_text", "block_id": "8wy2w", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " - <PERSON><PERSON><PERSON> at Vercara asked how they should input their salary bands and sent me "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/13vyDxX6tHcJAOpQ-xdNwN5EkfwRb9GOm/edit#gid=295722079", "text": "this Excel sheet"}, {"type": "text", "text": " to show their existing format. (I uploaded & converted to Google Sheets so we can work on it without the customer's visibility.)\n\nSome questions in the thread...."}]}]}]}], "created_at": "2025-05-22T21:35:34.600391"}