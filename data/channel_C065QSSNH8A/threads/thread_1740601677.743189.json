{"thread_ts": "1740601677.743189", "channel_id": "C065QSSNH8A", "reply_count": 7, "replies": [{"ts": "1740603043.574839", "text": "These are the 5 people and their total bonus amounts:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740601677.743189", "files": [{"id": "F08ET9F2HLP", "created": 1740603040, "timestamp": 1740603040, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 26267, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08ET9F2HLP/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08ET9F2HLP/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ET9F2HLP-89d6f0c542/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ET9F2HLP-89d6f0c542/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ET9F2HLP-89d6f0c542/image_360.png", "thumb_360_w": 360, "thumb_360_h": 54, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ET9F2HLP-89d6f0c542/image_480.png", "thumb_480_w": 480, "thumb_480_h": 72, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ET9F2HLP-89d6f0c542/image_160.png", "original_w": 570, "original_h": 86, "thumb_tiny": "AwAHADDP7UdqD0o7UAHejuaO9HrQAqffX6itg4rHT76/UVrmuetuho//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08ET9F2HLP/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08ET9F2HLP-ce4bb734e8", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "m8M5h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "These are the 5 people and their total bonus amounts:"}]}]}]}, {"ts": "1740617117.952219", "text": "its simple,\n`Total Bonus Amount(from sheet)` - `Earned Company Performance Amount` (from comp planner) = Input Value for `Earned Individual Performance`\nI will take care of this during my day.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740601677.743189", "blocks": [{"type": "rich_text", "block_id": "PkNzi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "its simple,\n"}, {"type": "text", "text": "Total Bonus Amount(from sheet)", "style": {"code": true}}, {"type": "text", "text": " - "}, {"type": "text", "text": "Earned Company Performance Amount", "style": {"code": true}}, {"type": "text", "text": " (from comp planner) = Input Value for "}, {"type": "text", "text": "Earned Individual Performance", "style": {"code": true}}, {"type": "text", "text": "\nI will take care of this during my day."}]}]}]}, {"ts": "1740623766.991599", "text": "Yes, we know how to do it, but the system is not accepting decimals", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740601677.743189", "blocks": [{"type": "rich_text", "block_id": "mreSm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, we know how to do it, but the system is not accepting decimals"}]}]}]}, {"ts": "1740623779.496369", "text": "And they want it to be whole numbers for the final bonus award", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740601677.743189", "blocks": [{"type": "rich_text", "block_id": "6cwRc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And they want it to be whole numbers for the final bonus award"}]}]}]}, {"ts": "1740626453.991189", "text": "Got it. I see the problem. Copy paste works in the input. I will get done that done and also fix the input.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740601677.743189", "blocks": [{"type": "rich_text", "block_id": "goxnX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Got it. I see the problem. Copy paste works in the input. I will get done that done and also fix the input."}]}]}]}, {"ts": "1740656021.810449", "text": "<@U07EJ2LP44S> The UI issue with input box is fixed. Also updated adjustments for all the above employees to rounded values per amounts shared above. Crazy bonuses :smile: .", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740601677.743189", "blocks": [{"type": "rich_text", "block_id": "/VApO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " The UI issue with input box is fixed. Also updated adjustments for all the above employees to rounded values per amounts shared above. Crazy bonuses "}, {"type": "emoji", "name": "smile", "unicode": "1f604"}, {"type": "text", "text": " ."}]}]}]}, {"ts": "1740666571.044709", "text": "I know. We should be in the oil and gas industry. :scream_cat: ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740601677.743189", "reactions": [{"name": "smile", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "lW+Gm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I know. We should be in the oil and gas industry. "}, {"type": "emoji", "name": "scream_cat", "unicode": "1f640"}, {"type": "text", "text": " "}]}]}]}], "created_at": "2025-05-22T22:00:49.357527"}