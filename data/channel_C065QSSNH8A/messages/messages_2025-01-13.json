{"date": "2025-01-13", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1736790360.770369", "text": "<@U0690EB5JE5> for Diversified, we will need to upload the individual performance attainment for their bonus cycle. Do you want this in an EEID/$ attainment/% attainment fomat?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WEWBv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " for Diversified, we will need to upload the individual performance attainment for their bonus cycle. Do you want this in an EEID/$ attainment/% attainment fomat?"}]}]}]}, {"ts": "1736790296.107339", "text": "Ticket for the navigation sections in QA: <https://compiify.atlassian.net/browse/COM-4054>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14137::4e87e74ca36141a8b6def8de1b0308c4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4054?atlOrigin=eyJpIjoiOWJiYmM3YTNlNDdlNDJmNDlkZDMzM2E0OTQxMGYzMzciLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4054 Issue: Incorrect Landing Position in Bonus Columns>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14137::f463a0150e2b4d37b867e55f2c625603", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14137\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4054\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4054", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "TLTF+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ticket for the navigation sections in QA: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4054"}]}]}]}, {"ts": "1736774194.892259", "text": "Also reg. Diversified SSO. Can we take this update later this week? I didn't get a chance due to ongoing QA and getting these ENVs ready.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736774194.892259", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "woKpL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also reg. Diversified SSO. Can we take this update later this week? I didn't get a chance due to ongoing QA and getting these ENVs ready."}]}]}]}, {"ts": "1736774149.596269", "text": "<@U07EJ2LP44S> UPDATE: We have found some bugs and still working on the fixes. Will let you know once I update the ENVs.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736774149.596269", "reply_count": 1, "reactions": [{"name": "eyes", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Ggvip", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " UPDATE: We have found some bugs and still working on the fixes. Will let you know once I update the ENVs."}]}]}]}], "created_at": "2025-05-22T21:35:34.694807"}