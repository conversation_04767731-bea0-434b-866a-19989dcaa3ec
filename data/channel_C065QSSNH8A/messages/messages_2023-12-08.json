{"date": "2023-12-08", "channel_id": "C065QSSNH8A", "message_count": 10, "messages": [{"ts": "1702057600.763319", "text": "Sorry y'all. Office is packed. just found a room so I'm ready for the call with DA", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "aYoQv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sorry y'all. Office is packed. just found a room so I'm ready for the call with DA"}]}]}]}, {"ts": "1702057258.639349", "text": "<@U0658EW4B8D> We're going to drop the current session - see you on the call with DA at 10!", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xew3q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " We're going to drop the current session - see you on the call with DA at 10!"}]}]}]}, {"ts": "1702056810.489539", "text": "Main links / files for today's call with *Digital Asset*:\n• <https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0|Project plan> (shared with customer)\n• <https://docs.google.com/document/d/1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs/edit|Cycle config questionnaire> (not shared with customer)", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F067S2J5BF0", "created": 1701322474, "timestamp": 1701322474, "name": "Digital Assets Implementation Project Plan", "title": "Digital Asset Implementation Project Plan", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE", "external_url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSbdnjH403L+q/lTj1popXAMv6r+VKN2eSMfSkHWnUXAWiiimAUUUUAGOaKKKACiiigAooooAKKKKACiiigD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F067S2J5BF0/digital_assets_implementation_project_plan", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ky1jI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Main links / files for today's call with "}, {"type": "text", "text": "Digital Asset", "style": {"bold": true}}, {"type": "text", "text": ":\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "text": "Project plan"}, {"type": "text", "text": " (shared with customer)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/document/d/1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs/edit", "text": "Cycle config questionnaire"}, {"type": "text", "text": " (not shared with customer)"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1701993640.133009", "text": "sweet", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZADOs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "sweet"}]}]}]}, {"ts": "1701993587.800259", "text": "• Respond \"1\" if you're fully satisfied with your tools for planning merit cycles\n• Respond \"2\" if merit planning is stressful or confusing for your managers and employees\n• Respond \"3\" if you're still doing your merit planning via spreadsheets", "user": "U065H3M6WJV", "type": "message", "edited": {"user": "U065H3M6WJV", "ts": "1701993601.000000"}, "blocks": [{"type": "rich_text", "block_id": "5cjkm", "elements": [{"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Respond \"1\" if you're fully satisfied with your tools for planning merit cycles"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Respond \"2\" if merit planning is stressful or confusing for your managers and employees"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Respond \"3\" if you're still doing your merit planning via spreadsheets"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1701993424.140749", "text": "good suggestion, I like how short and concise it is. Could we explore adding some specifics and/or more color on what it means to be satisfied or what's ideal? I also want to explicitly mention employee to make it look like it's a company-wide issue.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FNXRx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "good suggestion, I like how short and concise it is. Could we explore adding some specifics and/or more color on what it means to be satisfied or what's ideal? I also want to explicitly mention employee to make it look like it's a company-wide issue."}]}]}]}, {"ts": "1701992903.845849", "text": "I would maybe say:\n• Respond \"1\" if you're fully satisfied with your tools for planning merit cycles\n• Respond \"2\" if you have a system for planning merit cycles, but it's not ideal\n• Respond \"3\" if you're still using spreadsheets for merit planning", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "idsQV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I would maybe say:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Respond \"1\" if you're fully satisfied with your tools for planning merit cycles"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Respond \"2\" if you have a system for planning merit cycles, but it's not ideal"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Respond \"3\" if you're still using spreadsheets for merit planning"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1701992769.544389", "text": "<@U065H3M6WJV> <@U0658EW4B8D> We are going to use a quick survey in our sales outreach emails targeted towards HR leaders. Goal is to get some data on how they manage their comp cycles and if they do it either on spreadsheet or use a software, is it streamlined and effective.\n\nHere is a first take at the survey.\n\n*Which describes your situation?*\n• Respond \"1\" if your company plans merit cycles on spreadsheets.\n• Respond \"2\" if your company plans merit cycles on spreadsheets and you feel that your process is efficient, transparent and data-driven.\n• Respond \"3\" if your company plans merit cycle on spreadsheets and the processes is stressful or confusing for HR team and/or employees.\n\nAny thoughts on how to make it better or what questions we should actually be asking to ensure we are solving the right problem.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "QHMXL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " We are going to use a quick survey in our sales outreach emails targeted towards HR leaders. Goal is to get some data on how they manage their comp cycles and if they do it either on spreadsheet or use a software, is it streamlined and effective.\n\nHere is a first take at the survey.\n\n"}, {"type": "text", "text": "Which describes your situation?", "style": {"bold": true}}, {"type": "text", "text": "\n• Respond \"1\" if your company plans merit cycles on spreadsheets.\n• Respond \"2\" if your company plans merit cycles on spreadsheets and you feel that your process is efficient, transparent and data-driven.\n• Respond \"3\" if your company plans merit cycle on spreadsheets and the processes is stressful or confusing for HR team and/or employees.\n\nAny thoughts on how to make it better or what questions we should actually be asking to ensure we are solving the right problem."}]}]}]}, {"ts": "1701982827.181099", "text": "Agenda for tomorrow's call with *Digital Asset:*\n• Review the <https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0|Implementation Project Plan> and confirm key dates\n• Provide high-level info on planned UAT (don't need to review in detail) and get their commitment to do some testing once data is loaded\n• Walk through <https://docs.google.com/document/d/1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs/edit|cycle config questions> (verbally - not on screen) to capture any additional requirements for this cycle. I've prefilled this with what we know from their first call. \n    ◦ Specifically ask about what is lacking in HiBob and what they'll need in reports/analytics (noted at end of doc)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701982827.181099", "reply_count": 6, "reactions": [{"name": "moneybag", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F067S2J5BF0", "created": 1701322474, "timestamp": 1701322474, "name": "Digital Assets Implementation Project Plan", "title": "Digital Asset Implementation Project Plan", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE", "external_url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSbdnjH403L+q/lTj1popXAMv6r+VKN2eSMfSkHWnUXAWiiimAUUUUAGOaKKKACiiigAooooAKKKKACiiigD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F067S2J5BF0/digital_assets_implementation_project_plan", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}, {"id": "F069V9QT1DE", "created": 1701982829, "timestamp": 1701982829, "name": "DA - Cycle Config Questions", "title": "DA - Cycle Config Questions", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 126118, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs", "external_url": "https://docs.google.com/document/d/1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs/edit", "url_private": "https://docs.google.com/document/d/1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F069V9QT1DE-1f8a03782c/da_-_cycle_config_questions_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTSP1pB9TSscDpSAj0/WgBcH+8aUD3zRRQAUUUUAIwyKQKe+KHAPOKQLnsaAH4xRTQoHal2j0FAC0UUUAI31IpAD6n86G+poXk9TQAbT6n86cOlJj3NLQAUUUUAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F069V9QT1DE/da_-_cycle_config_questions", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "RZ6A0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for tomorrow's call with "}, {"type": "text", "text": "Digital Asset:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Review the "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "text": "Implementation Project Plan"}, {"type": "text", "text": " and confirm key dates"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Provide high-level info on planned UAT (don't need to review in detail) and get their commitment to do some testing once data is loaded"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Walk through "}, {"type": "link", "url": "https://docs.google.com/document/d/1vPSO_J-7IMJkiRcSVi_dSdcTKN88ptpk3N2ItYdnFqs/edit", "text": "cycle config questions"}, {"type": "text", "text": " (verbally - not on screen) to capture any additional requirements for this cycle. I've prefilled this with what we know from their first call. "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Specifically ask about what is lacking in HiBob and what they'll need in reports/analytics (noted at end of doc)"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}, {"ts": "1701976147.373879", "text": "<@U04DKEFP1K8> what's the status of fixing equity chart?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1701976147.373879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "QEZqJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what's the status of fixing equity chart?"}]}]}]}], "created_at": "2025-05-22T21:35:34.571298"}