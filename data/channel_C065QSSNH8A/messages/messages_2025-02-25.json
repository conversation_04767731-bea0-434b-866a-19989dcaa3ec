{"date": "2025-02-25", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1740507478.519829", "text": "<@U0690EB5JE5> Tithely Terminations (still getting formatting error on everything I upload). Can you upload?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740507478.519829", "reply_count": 6, "files": [{"id": "F08F0U47X8S", "created": 1740507467, "timestamp": 1740507467, "name": "TithelyTerms.csv", "title": "TithelyTerms.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 2504, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F0U47X8S/tithelyterms.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F0U47X8S/download/tithelyterms.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F0U47X8S/tithelyterms.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F0U47X8S-aabf979586", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F0U47X8S/tithelyterms.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,First Name *,Last Name *,Employee Name (Read Only),Email Address *,Gender *,Hire Date *,Location(Country) *,Reporting Manager Employee Id *,HRBP Employee Id,Employment Status,Original Hire Date,Probation Period End Date,Race / Ethnicity,Location(Region),Job Title,Department,Tier,FTE %,Job Category,Job Level,\"Time Type (FT,PT)\",Weekly Hours,\"Worker Type (Employee, contractor)\",\"Employee Type (regular, intern, trainee, other)\",Job Code,Compensation Grade,Cost Center,Profit Center,Job Family Group,Job Family,HR Business Partner,Compensation Type,Team,Payzone,Performance Review Start Date,Performance Review End Date,Performance Rating,Performance Comment,Annual Salary Currency,Annual Salary Amount,Annual Salary-Other Amount,Variable Pay Currency,Variable Pay (%),Variable Pay Amount,Target Bonus Currency,Target Bonus (%),Target Bonus Amount,Target Company Performance Amount,Target Company Performance (%),Target Individual Performance Amount,Target Individual Performance (%),E...", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">First Name *</div><div class=\"cm-col\">Last Name *</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Email Address *</div><div class=\"cm-col\">Gender *</div><div class=\"cm-col\">Hire Date *</div><div class=\"cm-col\">Location(Country) *</div><div class=\"cm-col\">Reporting Manager Employee Id *</div><div class=\"cm-col\">HRBP Employee Id</div><div class=\"cm-col\">Employment Status</div><div class=\"cm-col\">Original Hire Date</div><div class=\"cm-col\">Probation Period End Date</div><div class=\"cm-col\">Race / Ethnicity</div><div class=\"cm-col\">Location(Region)</div><div class=\"cm-col\">Job Title</div><div class=\"cm-col\">Department</div><div class=\"cm-col\">Tier</div><div class=\"cm-col\">FTE %</div><div class=\"cm-col\">Job Category</div><div class=\"cm-col\">Job Level</div><div class=\"cm-col\">Time Type (FT,PT)</div><div class=\"cm-col\">Weekly Hours</div><div class=\"cm-col\">Worker Type (Employee, contractor)</div><div class=\"cm-col\">Employee Type (regular, intern, trainee, other)</div><div class=\"cm-col\">Job Code</div><div class=\"cm-col\">Compensation Grade</div><div class=\"cm-col\">Cost Center</div><div class=\"cm-col\">Profit Center</div><div class=\"cm-col\">Job Family Group</div><div class=\"cm-col\">Job Family</div><div class=\"cm-col\">HR Business Partner</div><div class=\"cm-col\">Compensation Type</div><div class=\"cm-col\">Team</div><div class=\"cm-col\">Payzone</div><div class=\"cm-col\">Performance Review Start Date</div><div class=\"cm-col\">Performance Review End Date</div><div class=\"cm-col\">Performance Rating</div><div class=\"cm-col\">Performance Comment</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary Amount</div><div class=\"cm-col\">Annual Salary-Other Amount</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay Amount</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus Amount</div><div class=\"cm-col\">Target Company Performance Amount</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance Amount</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance Amount</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance Amount</div><div class=\"cm-col\">Earned Individual Performance (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE Amount</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Pay Mix</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">57</div><div class=\"cm-col\">Cory</div><div class=\"cm-col\">Wadstrom</div><div class=\"cm-col\">Cory Wadstrom</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">2/4/19</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">18</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">2/4/19</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">AR</div><div class=\"cm-col\">Sales Operations Manager</div><div class=\"cm-col\">Sales</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">135000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">4/1/22</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">321</div><div class=\"cm-col\">John</div><div class=\"cm-col\">Powers</div><div class=\"cm-col\">John Powers</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">4/24/23</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">31</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">4/24/23</div><div class=\"cm-col\"></div><div class=\"cm-col\">WHITE</div><div class=\"cm-col\">CA</div><div class=\"cm-col\">CFO</div><div class=\"cm-col\">Executive</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">M</div><div class=\"cm-col cm-num\">6</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">322400</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/25/24</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">129</div><div class=\"cm-col\">Paulina</div><div class=\"cm-col\">Arias</div><div class=\"cm-col\">Paulina Arias</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">F</div><div class=\"cm-col\">8/3/20</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">26</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">8/3/20</div><div class=\"cm-col\"></div><div class=\"cm-col\">BLACK_OR_AFRICAN_AMERICAN</div><div class=\"cm-col\">NY</div><div class=\"cm-col\">Lead UI / UX Designer</div><div class=\"cm-col\">Product Management</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">5</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">100000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">7/1/24</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">306</div><div class=\"cm-col\">Rudolph</div><div class=\"cm-col\">Abrot</div><div class=\"cm-col\">Rudolph Abrot</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">M</div><div class=\"cm-col\">12/5/22</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">137</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">12/5/22</div><div class=\"cm-col\"></div><div class=\"cm-col\">Asian</div><div class=\"cm-col\">CA</div><div class=\"cm-col\">Sr. Web Design &amp; Developer</div><div class=\"cm-col\">Marketing</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">4</div><div class=\"cm-col\">Yes</div><div class=\"cm-col\"></div><div class=\"cm-col\">FULL_TIME</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Regular</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">4/1/23</div><div class=\"cm-col\">3/30/24</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">153000</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">3/25/24</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">D</div><div class=\"cm-col cm-num\">358</div><div class=\"cm-col\">Maame</div><div class=\"cm-col\">Richardson</div><div class=\"cm-col\">Maame Richardson</div><div class=\"cm-col\"><EMAIL></div><div class=\"cm-col\">U</div><div class=\"cm-col\">1/13/25</div><div class=\"cm-col\">US</div><div class=\"cm-col cm-num\">200</div><div class=\"cm-col\"></div><div class=\"cm-col\">Active</div><div class=\"cm-col\">1/13/25</div><div class=\"cm-col\"></div><div class=\"cm-col\">N/A</div><div class=\"cm-col\">CA</div><div class=\"cm-col\">Operations Specialist</div><div class=\"cm-col\">Customer Experience</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">IC</div><div class=\"cm-col cm-num\">2</div><div class=\"cm-col\">No</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">Part Time</div><div class=\"cm-col\">N/A</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">56160</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col\">1/2/25</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">27</div><div class=\"cm-col\"></div></div></div>\n</div>\n", "lines": 6, "lines_more": 5, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "DVF43", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Tithely Terminations (still getting formatting error on everything I upload). Can you upload?"}]}]}]}, {"ts": "**********.356239", "text": "<@U0690EB5JE5> <PERSON><PERSON><PERSON> is going to make a few more data changes - in the MIP account they are changing salaries (they have a whole proration thing so they're hacking the system a bit), and they have a few more ratings to add. If there aren't too many ratings I can update them in the org view (though I'm not sure that will work for bonus cycle?). But expect there may be a few files coming I will likely need help with an ideally uploaded today. I told them there's some urgency to get them in in the next hour or so.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.356239", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "SDdMj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> is going to make a few more data changes - in the MIP account they are changing salaries (they have a whole proration thing so they're hacking the system a bit), and they have a few more ratings to add. If there aren't too many ratings I can update them in the org view (though I'm not sure that will work for bonus cycle?). But expect there may be a few files coming I will likely need help with an ideally uploaded today. I told them there's some urgency to get them in in the next hour or so."}]}]}]}, {"ts": "**********.181109", "text": "<@U07EJ2LP44S> what's the expected completion date for DE and Curana?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.181109", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "U4/KX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " what's the expected completion date for DE and Curana?"}]}]}]}, {"ts": "**********.726509", "text": "Tithely updated all their benefits data - they have decided to reflect the next years numbers vs the previous years, so the data has changed. Attached.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.726509", "reply_count": 18, "files": [{"id": "F08EDEXD4SK", "created": 1740431927, "timestamp": 1740431927, "name": "Stride Total Rewards 2.25.xlsx", "title": "Stride Total Rewards 2.25.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 39601, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08EDEXD4SK/stride_total_rewards_2.25.xlsx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08EDEXD4SK/download/stride_total_rewards_2.25.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDEXD4SK-dea481c717/stride_total_rewards_2.25_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EDEXD4SK-dea481c717/stride_total_rewards_2.25_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08EDEXD4SK/stride_total_rewards_2.25.xlsx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08EDEXD4SK-28568d94a3", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "BVJ6g", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Tithely updated all their benefits data - they have decided to reflect the next years numbers vs the previous years, so the data has changed. Attached."}]}]}]}, {"ts": "1740424013.471169", "text": "<@U0690EB5JE5> brian at curana getting this when trying to login", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740424013.471169", "reply_count": 5, "files": [{"id": "F08ETTV2EKC", "created": 1740424011, "timestamp": 1740424011, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 16196, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08ETTV2EKC/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08ETTV2EKC/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ETTV2EKC-fe1015a23c/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ETTV2EKC-fe1015a23c/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ETTV2EKC-fe1015a23c/image_360.png", "thumb_360_w": 360, "thumb_360_h": 117, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ETTV2EKC-fe1015a23c/image_480.png", "thumb_480_w": 480, "thumb_480_h": 156, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ETTV2EKC-fe1015a23c/image_160.png", "original_w": 684, "original_h": 222, "thumb_tiny": "AwAPADClTlUtnkDHqcUvlP8A3f1pQki9F/lTATyzj7y/99Cjyz/eT/vqnbZfT+VG2X0/lQAxlKjOVP0OabUhSQjBX+VJ5T/3f1oA/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08ETTV2EKC/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08ETTV2EKC-46ebfe6453", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ri47V", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " brian at curana getting this when trying to login"}]}]}]}], "created_at": "2025-05-22T21:35:34.709990"}