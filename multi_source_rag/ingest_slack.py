#!/usr/bin/env python
"""
Script to ingest Slack data using the EnterpriseRAGService.
"""

import os
import sys
import json
import django
import logging
from datetime import datetime

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk, EmbeddingMetadata
from apps.documents.processors.enhanced_message_processor import EnhancedMessageProcessor
from apps.core.utils.vectorstore import get_qdrant_client
from apps.search.services.rag_service import RAGService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_database():
    """Clean the database by removing all documents, chunks, and related data."""
    logger.info("Starting database cleaning process...")

    # Delete all embedding metadata
    embedding_count = EmbeddingMetadata.objects.count()
    EmbeddingMetadata.objects.all().delete()
    logger.info(f"Deleted {embedding_count} embedding metadata records")

    # Delete all document chunks
    chunk_count = DocumentChunk.objects.count()
    DocumentChunk.objects.all().delete()
    logger.info(f"Deleted {chunk_count} document chunks")

    # Delete all raw documents
    doc_count = RawDocument.objects.count()
    RawDocument.objects.all().delete()
    logger.info(f"Deleted {doc_count} raw documents")

    # Delete all document sources
    source_count = DocumentSource.objects.count()
    DocumentSource.objects.all().delete()
    logger.info(f"Deleted {source_count} document sources")

    # Clean Qdrant vector database
    try:
        # Get Qdrant client
        client = get_qdrant_client()

        # Get all tenants
        tenants = Tenant.objects.all()

        # Delete collections for each tenant
        for tenant in tenants:
            collection_name = f"{tenant.slug}_chunks"
            try:
                if client.collection_exists(collection_name):
                    client.delete_collection(collection_name)
                    logger.info(f"Deleted vector collection: {collection_name}")
            except Exception as e:
                logger.error(f"Error deleting vector collection {collection_name}: {str(e)}")
    except Exception as e:
        logger.error(f"Error cleaning vector database: {str(e)}")

    logger.info("Database cleaning completed successfully")

def create_slack_documents(tenant):
    """Create Slack documents directly from the message files."""
    logger.info("Creating Slack documents directly from message files...")

    # Path to the messages directory
    messages_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                               "data", "channel_C065QSSNH8A", "messages")

    if not os.path.exists(messages_dir):
        logger.error(f"Messages directory not found: {messages_dir}")
        return []

    # Get list of message files
    message_files = [f for f in os.listdir(messages_dir) if f.endswith(".json")]
    logger.info(f"Found {len(message_files)} message files")

    # Create documents
    documents = []
    for i, filename in enumerate(message_files[:10]):  # Process first 10 files for testing
        try:
            file_path = os.path.join(messages_dir, filename)
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Create content from messages
            if isinstance(data, list):
                # Format messages
                content = "\n\n".join([
                    f"User: {msg.get('user', 'unknown')}\nTime: {msg.get('ts', '')}\nMessage: {msg.get('text', '')}"
                    for msg in data if msg.get('type') == 'message' and msg.get('text')
                ])

                # Create document
                if content.strip():
                    doc = RawDocument.objects.create(
                        tenant=tenant,
                        title=f"Slack Messages - {filename.replace('.json', '')}",
                        content=content,
                        content_type="text/slack",
                        source_type="slack",
                        source_id=f"slack-{filename}",
                        metadata={
                            "channel": "C065QSSNH8A",
                            "date": filename.replace('messages_', '').replace('.json', ''),
                            "message_count": len(data)
                        }
                    )
                    documents.append(doc)
                    logger.info(f"Created document {i+1}/{len(message_files[:10])}: {doc.title}")
        except Exception as e:
            logger.error(f"Error processing file {filename}: {str(e)}")

    logger.info(f"Created {len(documents)} documents")
    return documents

def process_documents(tenant, documents):
    """Process documents using the RAGService."""
    logger.info("Processing documents with RAGService...")

    try:
        # Get a user for the RAG service
        from django.contrib.auth.models import User
        user = User.objects.first()
        if not user:
            logger.error("No user found in the database. Creating a default user.")
            user = User.objects.create_user(
                username="admin",
                email="<EMAIL>",
                password="admin"
            )

        # Create RAG service
        rag_service = RAGService(user=user, tenant_slug=tenant.slug)

        # Process each document
        for i, doc in enumerate(documents):
            try:
                logger.info(f"Processing document {i+1}/{len(documents)}: {doc.title}")
                # Create chunks from the document
                chunks = create_chunks_from_document(doc)
                # Process chunks - just log for now as the new RAGService doesn't have the same method
                for chunk in chunks:
                    logger.info(f"Processing chunk {chunk.chunk_index} of document {doc.title}")
                    # The new RAGService doesn't have process_messages_with_enhanced_processor
                    # We'll just log the chunks for now
                    logger.info(f"Chunk content: {chunk.content[:100]}...")

                logger.info(f"Successfully processed document: {doc.title}")
            except Exception as e:
                logger.error(f"Error processing document {doc.title}: {str(e)}")

        logger.info(f"Processed {len(documents)} documents")
    except Exception as e:
        logger.error(f"Error initializing RAG service: {str(e)}")

def create_chunks_from_document(document):
    """Create chunks from a document."""
    # Simple chunking strategy - split by paragraphs
    content = document.content
    chunks = []

    # Split content by paragraphs
    paragraphs = content.split("\n\n")

    # Create chunks
    for i, paragraph in enumerate(paragraphs):
        if paragraph.strip():
            chunk = DocumentChunk(
                tenant=document.tenant,
                document=document,
                content=paragraph,
                chunk_index=i,
                metadata={
                    "source": document.source_type,
                    "title": document.title,
                    "chunk_index": i,
                    "total_chunks": len(paragraphs)
                }
            )
            chunk.save()
            chunks.append(chunk)

    return chunks

def print_database_stats():
    """Print database statistics."""
    logger.info("Database Statistics:")
    logger.info(f"Raw Documents: {RawDocument.objects.count()}")
    logger.info(f"Document Chunks: {DocumentChunk.objects.count()}")
    logger.info(f"Document Sources: {DocumentSource.objects.count()}")
    logger.info(f"Embedding Metadata: {EmbeddingMetadata.objects.count()}")

def main():
    """Main function to ingest Slack data."""
    logger.info("Starting Slack data ingestion...")

    # Clean the database first
    clean_database()

    # Get or create tenant
    tenant, created = Tenant.objects.get_or_create(
        slug="default",
        defaults={"name": "Default Tenant"}
    )

    # Print initial database stats
    logger.info("Initial database stats:")
    print_database_stats()

    # Create Slack documents
    documents = create_slack_documents(tenant)
    if not documents:
        logger.error("No documents created. Exiting.")
        return

    # Process documents
    process_documents(tenant, documents)

    # Print final database stats
    logger.info("Final database stats after ingestion:")
    print_database_stats()

    logger.info("Slack data ingestion completed.")

if __name__ == "__main__":
    main()
