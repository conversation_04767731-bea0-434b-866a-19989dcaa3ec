{"thread_ts": "1736795356.625279", "channel_id": "C065QSSNH8A", "reply_count": 2, "replies": [{"ts": "1736816269.387569", "text": "This will be fixed in new build. We are getting rid of the system which was maintaining its own internal hierarchy which is causing the issue here. We will double confirm this anyway.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736795356.625279", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "bRN0J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This will be fixed in new build. We are getting rid of the system which was maintaining its own internal hierarchy which is causing the issue here. We will double confirm this anyway."}]}]}]}, {"ts": "1736865659.696049", "text": "<@U07M6QKHUC9> This also depends on how recommenders level set. Merit hierarchy is based on cycle configuration. Its Level 7. Manager name is based on the actual Hierarchy. I will test this further tomorrow and get back.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736795356.625279", "reactions": [{"name": "eyes", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "MDB+Q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " This also depends on how recommenders level set. Merit hierarchy is based on cycle configuration. Its Level 7. Manager name is based on the actual Hierarchy. I will test this further tomorrow and get back."}]}]}]}], "created_at": "2025-05-22T22:00:49.334958"}