# Generated by Django 4.2.10 on 2025-06-02 12:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("accounts", "0001_initial"),
        ("core", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="RawDocument",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("title", models.CharField(max_length=255)),
                (
                    "external_id",
                    models.Char<PERSON>ield(blank=True, max_length=255, null=True),
                ),
                (
                    "permalink",
                    models.URLField(
                        blank=True,
                        help_text="Permanent link to the original document",
                        null=True,
                    ),
                ),
                ("fetched_at", models.DateTimeField(auto_now_add=True)),
                ("metadata", models.J<PERSON><PERSON>ield(blank=True, default=dict)),
                (
                    "content_hash",
                    models.CharField(blank=True, max_length=64, null=True),
                ),
                (
                    "content_type",
                    models.CharField(
                        choices=[
                            ("text", "Plain Text"),
                            ("markdown", "Markdown"),
                            ("html", "HTML"),
                            ("pdf", "PDF"),
                            ("code", "Code"),
                            ("github_pr", "GitHub PR"),
                            ("github_issue", "GitHub Issue"),
                            ("slack_message", "Slack Message"),
                            ("other", "Other"),
                        ],
                        default="text",
                        max_length=20,
                    ),
                ),
                (
                    "quality_score",
                    models.FloatField(
                        blank=True,
                        help_text="Quality score of the document content",
                        null=True,
                    ),
                ),
                (
                    "is_high_quality",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the document is considered high quality",
                    ),
                ),
                (
                    "technical_entities",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Technical entities extracted from the document",
                    ),
                ),
                (
                    "source",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="core.documentsource",
                    ),
                ),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounts.tenant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Document",
                "verbose_name_plural": "Documents",
            },
        ),
        migrations.CreateModel(
            name="DocumentContent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "content",
                    models.TextField(help_text="The full content of the document"),
                ),
                (
                    "content_format",
                    models.CharField(
                        choices=[
                            ("text", "Plain Text"),
                            ("markdown", "Markdown"),
                            ("html", "HTML"),
                            ("slack", "Slack Format"),
                        ],
                        default="text",
                        max_length=20,
                    ),
                ),
                (
                    "content_size",
                    models.IntegerField(
                        default=0,
                        help_text="Size of content in characters for optimization",
                    ),
                ),
                (
                    "content_summary",
                    models.TextField(
                        blank=True,
                        help_text="Brief summary of content for quick access",
                        null=True,
                    ),
                ),
                (
                    "document",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="document_content",
                        to="documents.rawdocument",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DocumentChunk",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("text", models.TextField()),
                ("chunk_type", models.CharField(default="text", max_length=50)),
                ("thread_id", models.CharField(blank=True, max_length=100, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("token_count", models.IntegerField(default=0)),
                ("importance_score", models.FloatField(default=0.0)),
                (
                    "quality_score",
                    models.FloatField(
                        blank=True,
                        help_text="Quality score of the chunk content",
                        null=True,
                    ),
                ),
                (
                    "is_high_quality",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the chunk is considered high quality",
                    ),
                ),
                ("chunk_index", models.IntegerField()),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "technical_entities",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Technical entities extracted from the chunk",
                    ),
                ),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chunks",
                        to="documents.rawdocument",
                    ),
                ),
                (
                    "profile",
                    models.ForeignKey(
                        blank=True,
                        help_text="Author's platform profile",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="authored_chunks",
                        to="accounts.userplatformprofile",
                    ),
                ),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounts.tenant",
                    ),
                ),
            ],
            options={
                "ordering": ["document", "chunk_index"],
                "unique_together": {("document", "chunk_index")},
            },
        ),
        migrations.CreateModel(
            name="RelatedChunk",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("relation_type", models.CharField(max_length=50)),
                ("relation_strength", models.FloatField(default=0.0)),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional metadata about the relationship",
                    ),
                ),
                (
                    "chunk",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="related_chunks",
                        to="documents.documentchunk",
                    ),
                ),
                (
                    "related_chunk",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="related_to",
                        to="documents.documentchunk",
                    ),
                ),
            ],
            options={
                "unique_together": {("chunk", "related_chunk")},
            },
        ),
        migrations.CreateModel(
            name="EmbeddingMetadata",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "vector_id",
                    models.CharField(
                        db_index=True,
                        help_text="UUID used in vector database",
                        max_length=255,
                    ),
                ),
                ("embedded_at", models.DateTimeField(auto_now_add=True)),
                ("model_name", models.CharField(max_length=100)),
                ("vector_dimensions", models.IntegerField()),
                (
                    "is_synced",
                    models.BooleanField(
                        default=True,
                        help_text="Whether the embedding is synced with the vector database",
                    ),
                ),
                (
                    "chunk",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="embedding",
                        to="documents.documentchunk",
                    ),
                ),
            ],
            options={
                "indexes": [models.Index(fields=["vector_id"], name="vector_id_idx")],
            },
        ),
        migrations.CreateModel(
            name="ChunkRelationship",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "relationship_type",
                    models.CharField(
                        help_text="Type of relationship (parent, child, previous, next, etc.)",
                        max_length=50,
                    ),
                ),
                (
                    "strength",
                    models.FloatField(
                        default=1.0, help_text="Strength of the relationship (0-1)"
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional metadata about the relationship",
                    ),
                ),
                (
                    "source_chunk",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="outgoing_relationships",
                        to="documents.documentchunk",
                    ),
                ),
                (
                    "target_chunk",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="incoming_relationships",
                        to="documents.documentchunk",
                    ),
                ),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounts.tenant",
                    ),
                ),
            ],
            options={
                "unique_together": {
                    ("source_chunk", "target_chunk", "relationship_type")
                },
            },
        ),
    ]
