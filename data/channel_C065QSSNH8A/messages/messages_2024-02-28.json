{"date": "2024-02-28", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1709077152.419619", "text": "Another look at SDF progress... all managers (except the CEO) have input changes for most or all of their employees. :eyes:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709077152.419619", "reply_count": 7, "files": [{"id": "F06MM689LM6", "created": 1709076730, "timestamp": 1709076730, "name": "Screenshot 2024-02-27 at 3.31.48 PM.png", "title": "Screenshot 2024-02-27 at 3.31.48 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 87161, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06MM689LM6/screenshot_2024-02-27_at_3.31.48___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06MM689LM6/download/screenshot_2024-02-27_at_3.31.48___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 235, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 313, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 470, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MM689LM6-790bd2acb0/screenshot_2024-02-27_at_3.31.48___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 522, "original_w": 892, "original_h": 582, "thumb_tiny": "AwAfADDROQehNLk+hpjAbj938TQoH+z+BoAcc+hpc+xppxx0/E0DHt+dAxcnPQ/mKUE+hFIce35UooENbOep/KgZ9f8Ax2hgc9D+dAB9G/OgBTnjr+VA/wA8UEHjg/nQAfQ/nQMXn3paaV+v50oGPX86BH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06MM689LM6/screenshot_2024-02-27_at_3.31.48___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06MM689LM6-3004586c65", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "GoM81", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Another look at SDF progress... all managers (except the CEO) have input changes for most or all of their employees. "}, {"type": "emoji", "name": "eyes", "unicode": "1f440"}]}]}]}, {"ts": "1709074451.823809", "text": "<@U04DKEFP1K8> can you pls confirm here on this thread after you have completed GDPR deliverables for <PERSON> today? We don't want to block the progress on GDPR while we are paying them a monthly fee.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1709074451.823809", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "JF1/S", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you pls confirm here on this thread after you have completed GDPR deliverables for Tyler today? We don't want to block the progress on GDPR while we are paying them a monthly fee."}]}]}]}, {"ts": "1709063252.692809", "text": "<PERSON>, <PERSON> is saying <PERSON> is stuck to submit all, can we check if he has submit all reviews by impersonating", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1709063252.692809", "reply_count": 3, "edited": {"user": "U04DKEFP1K8", "ts": "1709063265.000000"}, "blocks": [{"type": "rich_text", "block_id": "BTzce", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>, <PERSON> is saying <PERSON> is stuck to submit all, can we check if he has submit all reviews by impersonating"}]}]}]}], "created_at": "2025-05-22T21:35:34.614592"}