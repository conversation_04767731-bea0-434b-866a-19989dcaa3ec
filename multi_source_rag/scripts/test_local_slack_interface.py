#!/usr/bin/env python
"""
Simple Django Shell Script to Test LocalSlackInterface with Ingestion Service

This script tests the complete integration between LocalSlackInterface and 
UnifiedLlamaIndexIngestionService using the proper factory pattern and 
process_source method.

Usage:
    python manage.py shell -c "exec(open('scripts/test_local_slack_interface.py').read())"
"""

from django.db import transaction
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk
from apps.documents.services.llama_ingestion_service_unified import UnifiedLlamaIndexIngestionService

print("🚀 TESTING LOCAL SLACK INTERFACE WITH INGESTION SERVICE")
print("=" * 70)

# Configuration
tenant_slug = "default"
channel_id = "C065QSSNH8A"  # 1-productengineering channel
data_dir = "data"

try:
    # 1. Setup tenant
    print("📋 Setting up tenant...")
    tenant, created = Tenant.objects.get_or_create(
        slug=tenant_slug,
        defaults={"name": f"Tenant {tenant_slug}"}
    )
    if created:
        print(f"✅ Created new tenant: {tenant_slug}")
    else:
        print(f"📋 Using existing tenant: {tenant_slug}")

    # 2. Create DocumentSource with LocalSlackInterface configuration
    print("\n📁 Setting up DocumentSource for LocalSlackInterface...")
    source, created = DocumentSource.objects.get_or_create(
        tenant=tenant,
        name=f"Local Slack Interface Test - {channel_id}",
        source_type="local_slack",  # This will trigger LocalSlackInterface via factory
        defaults={
            "config": {
                "channel_id": channel_id,
                "data_dir": data_dir,
                "time_period": "monthly",
                "enable_summary": False,
                "quality_threshold": 0.3,
                "max_gap_minutes": 30,
                "max_documents_per_channel": 1000
            }
        }
    )
    
    if created:
        print(f"✅ Created DocumentSource: {source.name}")
    else:
        print(f"📋 Using existing DocumentSource: {source.name}")
    
    print(f"   Source Type: {source.source_type}")
    print(f"   Config: {source.config}")

    # 3. Initialize UnifiedLlamaIndexIngestionService
    print("\n🔄 Initializing UnifiedLlamaIndexIngestionService...")
    ingestion_service = UnifiedLlamaIndexIngestionService(tenant_slug)
    print("✅ Ingestion service initialized")

    # 4. Check existing data before processing
    print("\n📊 Checking existing data...")
    existing_docs = RawDocument.objects.filter(tenant=tenant, source=source).count()
    existing_chunks = DocumentChunk.objects.filter(tenant=tenant, document__source=source).count()
    print(f"   Existing documents: {existing_docs}")
    print(f"   Existing chunks: {existing_chunks}")

    # 5. Process source using LocalSlackInterface
    print(f"\n🚀 Processing source using LocalSlackInterface...")
    print("   This will:")
    print("   • Use LocalDocumentSourceFactory to create LocalSlackInterface")
    print("   • Call interface.fetch_documents() to get Slack data")
    print("   • Process documents through UnifiedLlamaIndexIngestionService")
    print("   • Apply enhanced metadata and skip-chunking")
    print("   • Store in both PostgreSQL and Qdrant")
    
    # Process with additional parameters for LocalSlackInterface
    with transaction.atomic():
        processed, failed = ingestion_service.process_source(
            source=source,
            # Parameters passed to LocalSlackInterface.fetch_documents()
            channel_id=channel_id,
            days_back=90,  # Last 3 months
            include_threads=True,
            filter_bots=True,
            time_period="monthly"
        )

    # 6. Check results
    print(f"\n📊 PROCESSING RESULTS:")
    print(f"   Documents processed: {processed}")
    print(f"   Documents failed: {failed}")
    
    if processed > 0:
        success_rate = (processed / (processed + failed)) * 100 if (processed + failed) > 0 else 0
        print(f"   Success rate: {success_rate:.1f}%")
    
    # 7. Check database after processing
    print(f"\n📊 DATABASE STATUS AFTER PROCESSING:")
    total_docs = RawDocument.objects.filter(tenant=tenant, source=source).count()
    total_chunks = DocumentChunk.objects.filter(tenant=tenant, document__source=source).count()
    
    print(f"   Total documents: {total_docs}")
    print(f"   Total chunks: {total_chunks}")
    print(f"   New documents: {total_docs - existing_docs}")
    print(f"   New chunks: {total_chunks - existing_chunks}")

    # 8. Show sample document details
    if total_docs > 0:
        print(f"\n📄 SAMPLE DOCUMENT DETAILS:")
        sample_doc = RawDocument.objects.filter(tenant=tenant, source=source).first()
        print(f"   Document ID: {sample_doc.id}")
        print(f"   External ID: {sample_doc.external_id}")
        print(f"   Title: {sample_doc.title}")
        print(f"   Content length: {len(sample_doc.content)} characters")
        print(f"   Content type: {sample_doc.content_type}")
        print(f"   Created: {sample_doc.created_at}")
        
        # Show metadata
        if hasattr(sample_doc, 'metadata') and sample_doc.metadata:
            print(f"   Metadata fields: {len(sample_doc.metadata)}")
            key_fields = ["message_count", "participants", "quality_score", "start_date", "conversation_size"]
            for field in key_fields:
                if field in sample_doc.metadata:
                    value = sample_doc.metadata[field]
                    if isinstance(value, list) and len(value) > 3:
                        value = f"[{len(value)} items]"
                    print(f"     {field}: {value}")

    # 9. Show sample chunk details
    if total_chunks > 0:
        print(f"\n🧩 SAMPLE CHUNK DETAILS:")
        sample_chunk = DocumentChunk.objects.filter(tenant=tenant, document__source=source).first()
        print(f"   Chunk ID: {sample_chunk.id}")
        print(f"   Node ID: {sample_chunk.node_id}")
        print(f"   Content length: {len(sample_chunk.content)} characters")
        print(f"   Chunk index: {sample_chunk.chunk_index}")
        
        # Show enhanced metadata
        if sample_chunk.metadata:
            print(f"   Metadata fields: {len(sample_chunk.metadata)}")
            enhanced_fields = ["chunking_strategy", "is_complete_document", "quality_tier", "has_code", "is_technical"]
            for field in enhanced_fields:
                if field in sample_chunk.metadata:
                    print(f"     {field}: {sample_chunk.metadata[field]}")

    # 10. Final summary
    print(f"\n" + "=" * 70)
    if processed > 0:
        print("✅ LOCAL SLACK INTERFACE TEST COMPLETED SUCCESSFULLY!")
        print(f"🎉 Successfully processed {processed} documents using LocalSlackInterface")
        print("💡 The integration between LocalSlackInterface and UnifiedLlamaIndexIngestionService is working!")
        print("\n🔍 You can now test search functionality with this data:")
        print("   • Time-based filtering (dates, hours, weekdays)")
        print("   • User-based filtering (participants, collaboration)")
        print("   • Quality-based filtering (engagement, reactions)")
        print("   • Technical content filtering (code, terms, URLs)")
    else:
        print("⚠️  NO DOCUMENTS WERE PROCESSED")
        print("💡 This could mean:")
        print("   • No data files found in the data directory")
        print("   • Data files don't match the expected format")
        print("   • Configuration issues with LocalSlackInterface")
        print("   • Check the data/slack/ directory for JSON files")
    print("=" * 70)

except Exception as e:
    print(f"\n❌ ERROR: {str(e)}")
    import traceback
    traceback.print_exc()
    print("\n💡 Common issues:")
    print("   • Make sure data/slack/ directory exists with JSON files")
    print("   • Check that Qdrant is running on localhost:6333")
    print("   • Verify Django settings are properly configured")
    print("   • Ensure all required dependencies are installed")
