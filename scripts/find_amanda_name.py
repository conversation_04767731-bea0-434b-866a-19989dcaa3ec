#!/usr/bin/env python3
"""
Find <PERSON>'s actual name in the Slack messages.
"""

import os
import sys
import django
import re

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.documents.models import DocumentChunk
from apps.accounts.models import UserPlatformProfile

def find_amanda_name():
    """Find <PERSON>'s actual name in Slack messages."""
    print("🔍 Finding Amanda's Actual Name")
    print("=" * 50)
    
    # Get chunks that mention Amanda
    amanda_chunks = DocumentChunk.objects.filter(text__icontains='amanda')
    print(f"📊 Chunks mentioning Amanda: {amanda_chunks.count()}")
    
    # Extract all names from these chunks
    name_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] ([^:]+):'
    names_in_amanda_chunks = set()
    
    for chunk in amanda_chunks[:20]:  # Check first 20 chunks
        print(f"\n📄 Chunk {chunk.id}:")
        print(f"   Text: {chunk.text[:500]}...")
        
        matches = re.findall(name_pattern, chunk.text)
        for timestamp, name in matches:
            if name.strip() and name not in ['Previous context', '/Previous context']:
                names_in_amanda_chunks.add(name.strip())
                print(f"   Found name: {name.strip()}")
    
    print(f"\n📊 All names found in Amanda chunks:")
    for name in sorted(names_in_amanda_chunks):
        print(f"   - {name}")
    
    # Look for 'amanda' specifically in the text
    print(f"\n🔍 Looking for 'amanda' in message content:")
    for chunk in amanda_chunks[:10]:
        lines = chunk.text.split('\n')
        for line in lines:
            if 'amanda' in line.lower():
                print(f"   Line: {line.strip()}")

def create_amanda_profile():
    """Create Amanda's profile."""
    print(f"\n🔧 Creating Amanda Profile")
    print("=" * 50)
    
    # Check if amanda profile already exists
    amanda_profile = UserPlatformProfile.objects.filter(
        display_name__icontains='amanda'
    ).first()
    
    if amanda_profile:
        print(f"✅ Amanda profile already exists: {amanda_profile}")
    else:
        # Create amanda profile
        amanda_profile = UserPlatformProfile.objects.create(
            platform='slack',
            platform_user_id='slack_amanda',
            display_name='amanda'
        )
        print(f"✅ Created Amanda profile: {amanda_profile}")
    
    return amanda_profile

def fix_amanda_chunks():
    """Fix chunks that contain Amanda's messages."""
    print(f"\n🔧 Fixing Amanda Chunks")
    print("=" * 50)
    
    # Get or create Amanda profile
    amanda_profile = create_amanda_profile()
    
    # Find chunks where Amanda is speaking
    amanda_chunks = DocumentChunk.objects.filter(text__icontains='amanda:')
    print(f"📊 Chunks with 'amanda:': {amanda_chunks.count()}")
    
    updated_count = 0
    for chunk in amanda_chunks:
        if not chunk.profile:  # Only update if no profile is set
            chunk.profile = amanda_profile
            chunk.save(update_fields=['profile'])
            updated_count += 1
            print(f"✅ Updated chunk {chunk.id} -> amanda")
    
    print(f"\n🎉 Updated {updated_count} chunks for Amanda")
    
    # Also check for chunks where Amanda is mentioned but might be speaking
    print(f"\n🔍 Checking for other Amanda patterns...")
    
    # Look for lines that start with amanda
    all_amanda_chunks = DocumentChunk.objects.filter(text__icontains='amanda')
    additional_updates = 0
    
    for chunk in all_amanda_chunks:
        if chunk.profile:  # Skip if already has profile
            continue
            
        lines = chunk.text.split('\n')
        for line in lines:
            # Look for pattern like "] amanda:" 
            if re.search(r'\] amanda:', line, re.IGNORECASE):
                chunk.profile = amanda_profile
                chunk.save(update_fields=['profile'])
                additional_updates += 1
                print(f"✅ Updated chunk {chunk.id} -> amanda (pattern match)")
                break
    
    print(f"🎉 Updated {additional_updates} additional chunks for Amanda")

if __name__ == "__main__":
    find_amanda_name()
    fix_amanda_chunks()
