{"date": "2024-10-17", "channel_id": "C065QSSNH8A", "message_count": 17, "messages": [{"ts": "1729186715.829929", "text": "<@U0690EB5JE5>  how time consuming it would be to remove one time bonus from SDF env by <PERSON><PERSON> of next week? It will just create confusion with the SDF so trying to prevent that", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729186715.829929", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "+clLC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "  how time consuming it would be to remove one time bonus from SDF env by <PERSON><PERSON> of next week? It will just create confusion with the SDF so trying to prevent that"}]}]}]}, {"ts": "1729183166.426639", "text": "<@U07EJ2LP44S> Let's use the same format <PERSON><PERSON><PERSON> is using to document our QA testing. here is the doc\n<https://docs.google.com/spreadsheets/d/1j05ohorYpwG0Li6_nJNeuOm1UxMqSvX7bczHa-4ORsY/edit?gid=0#gid=0>", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07EJ2LP44S"], "count": 1}], "files": [{"id": "F07SR8P8AMP", "created": 1729183169, "timestamp": 1729183169, "name": "SDF QA Testing", "title": "SDF QA Testing", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1j05ohorYpwG0Li6_nJNeuOm1UxMqSvX7bczHa-4ORsY", "external_url": "https://docs.google.com/spreadsheets/d/1j05ohorYpwG0Li6_nJNeuOm1UxMqSvX7bczHa-4ORsY/edit?gid=0#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1j05ohorYpwG0Li6_nJNeuOm1UxMqSvX7bczHa-4ORsY/edit?gid=0#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SR8P8AMP-2b6919f66b/sdf_qa_testing_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSJ5xSEnPX9KXHOaNvOaAAZ9aWiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07SR8P8AMP/sdf_qa_testing", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Cb/gI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Let's use the same format <PERSON><PERSON><PERSON> is using to document our QA testing. here is the doc\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1j05ohorYpwG0Li6_nJNeuOm1UxMqSvX7bczHa-4ORsY/edit?gid=0#gid=0"}]}]}]}, {"ts": "1729183054.641239", "text": "Ok will do it tomorrow. I missed during my day today due to bug fixes.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zWf+I", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok will do it tomorrow. I missed during my day today due to bug fixes."}]}]}]}, {"ts": "1729183016.052219", "text": "in 30 mins", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1729183022.000000"}, "blocks": [{"type": "rich_text", "block_id": "uutwI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "in 30 mins"}]}]}]}, {"ts": "1729183000.688899", "text": "we have 2 demos today", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1zUOF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we have 2 demos today"}]}]}]}, {"ts": "1729182988.035019", "text": "<@U07M6QKHUC9> Shall update demo ENV?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729182988.035019", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "U6dI0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Shall update demo ENV?"}]}]}]}, {"ts": "1729182673.828029", "text": "<@U04DKEFP1K8> <PERSON><PERSON><PERSON> has documented what he tested today and issues found.\n<https://docs.google.com/spreadsheets/d/1D3RmLNzJytZJFNeoF8Q_k9GLVDy9uQ-55M4jskp1FVA/edit?gid=0#gid=0>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729182673.828029", "reply_count": 5, "edited": {"user": "U0690EB5JE5", "ts": "1729182701.000000"}, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "TFRsB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> has documented what he tested today and issues found.\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1D3RmLNzJytZJFNeoF8Q_k9GLVDy9uQ-55M4jskp1FVA/edit?gid=0#gid=0"}]}]}]}, {"ts": "1729180657.239779", "text": "Running 5 mnts late ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "dLzLk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Running 5 mnts late "}]}]}]}, {"ts": "1729180488.527289", "text": "I'd personally like to stop anything new that isn't critical and put the entire team on QA and bugs.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZAbdQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'd personally like to stop anything new that isn't critical and put the entire team on QA and bugs."}]}]}]}, {"ts": "1729180292.777699", "text": "Yes. I saw the issues <PERSON> is experiencing. ", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+oZDR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes. I saw the issues <PERSON> is experiencing. "}]}]}]}, {"ts": "1729179571.643399", "text": "I know we just talked about this, but I am growing exceedingly anxious about the stability of the system right now. We have 11 working days before we have 3 cycles running, and I am not confident they are going to be run smoothly. Can we chat about that in our call?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729179571.643399", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "/F6xZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I know we just talked about this, but I am growing exceedingly anxious about the stability of the system right now. We have 11 working days before we have 3 cycles running, and I am not confident they are going to be run smoothly. Can we chat about that in our call?"}]}]}]}, {"ts": "1729168554.671089", "text": "<@U07EJ2LP44S>  <https://playq-test.stridehr.io/> is active now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729094285.210969", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "kV3NM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "  "}, {"type": "link", "url": "https://playq-test.stridehr.io/"}, {"type": "text", "text": " is active now."}]}]}]}, {"ts": "1729164434.500949", "text": "<@U07EJ2LP44S> I figured out the issue. We recently disabled other options in the dropdown (screenshot below). The cycle was created with the option `Performance and Market Adjustment`  which is no more available. We didn't take care of cleaning up the data which caused this issue. I have fixed the cycle data from backend and I am able to edit the cycle now in stridedemo", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729101345.968239", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1729164912.000000"}, "files": [{"id": "F07RW3FS2B1", "created": 1729164336, "timestamp": 1729164336, "name": "Screenshot 2024-10-17 at 4.54.44 PM.png", "title": "Screenshot 2024-10-17 at 4.54.44 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 408506, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07RW3FS2B1/screenshot_2024-10-17_at_4.54.44___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07RW3FS2B1/download/screenshot_2024-10-17_at_4.54.44___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07RW3FS2B1-a04aa82263/screenshot_2024-10-17_at_4.54.44___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07RW3FS2B1-a04aa82263/screenshot_2024-10-17_at_4.54.44___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07RW3FS2B1-a04aa82263/screenshot_2024-10-17_at_4.54.44___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 200, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07RW3FS2B1-a04aa82263/screenshot_2024-10-17_at_4.54.44___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 267, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07RW3FS2B1-a04aa82263/screenshot_2024-10-17_at_4.54.44___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07RW3FS2B1-a04aa82263/screenshot_2024-10-17_at_4.54.44___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 401, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07RW3FS2B1-a04aa82263/screenshot_2024-10-17_at_4.54.44___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 445, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07RW3FS2B1-a04aa82263/screenshot_2024-10-17_at_4.54.44___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 534, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07RW3FS2B1-a04aa82263/screenshot_2024-10-17_at_4.54.44___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 570, "original_w": 3224, "original_h": 1794, "thumb_tiny": "AwAaADDSwM0ewpcc0yQgEZLfhQA7NGaarA9AfxGKfQAmaPwpaKACkNLSN1oAKWkpRQAUUUUAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07RW3FS2B1/screenshot_2024-10-17_at_4.54.44___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07RW3FS2B1-03051a5fcb", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "zO1UP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I figured out the issue. We recently disabled other options in the dropdown (screenshot below). The cycle was created with the option "}, {"type": "text", "text": "Performance and Market Adjustment", "style": {"code": true}}, {"type": "text", "text": "  which is no more available. We didn't take care of cleaning up the data which caused this issue. I have fixed the cycle data from backend and I am able to edit the cycle now in stridedemo"}]}]}]}, {"ts": "1729130248.411629", "text": "Manager role is available by default ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nIPHx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Manager role is available by default "}]}]}]}, {"ts": "1729130226.933159", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> we already support this by default ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zxGny", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " we already support this by default "}]}]}]}, {"ts": "1729130205.813159", "text": "<https://stride-hr.slack.com/archives/C07KUHPHS2C/p1729097317979339|https://stride-hr.slack.com/archives/C07KUHPHS2C/p1729097317979339>", "user": "U0690EB5JE5", "type": "message", "attachments": [{"from_url": "https://stride-hr.slack.com/archives/C07KUHPHS2C/p1729097317979339", "ts": "1729097317.979339", "author_id": "U04P2DQLLSW", "channel_id": "C07KUHPHS2C", "channel_team": "T04DM97F1UM", "is_msg_unfurl": true, "message_blocks": [{"team": "T04DM97F1UM", "channel": "C07KUHPHS2C", "ts": "1729097317.979339", "message": {"blocks": [{"type": "rich_text", "block_id": "dtPqb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "something to put on the roadmap, allow dual roles to be assigned, mainly for those in HR, since they need a role for span of support and may also be a manager."}]}]}]}}], "private_channel_prompt": true, "id": 1, "original_url": "https://stride-hr.slack.com/archives/C07KUHPHS2C/p1729097317979339", "fallback": "[October 16th, 2024 9:48 AM] cheryl.tsang: something to put on the roadmap, allow dual roles to be assigned, mainly for those in HR, since they need a role for span of support and may also be a manager.", "text": "something to put on the roadmap, allow dual roles to be assigned, mainly for those in HR, since they need a role for span of support and may also be a manager.", "author_name": "<PERSON>", "author_link": "https://stride-hr.slack.com/team/U04P2DQLLSW", "author_icon": "https://avatars.slack-edge.com/2024-04-04/6907662323862_53e5a489a4ecb3246e66_48.png", "author_subname": "<PERSON>", "mrkdwn_in": ["text"], "footer": "Slack Conversation"}], "blocks": [{"type": "rich_text", "block_id": "524PA", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://stride-hr.slack.com/archives/C07KUHPHS2C/p1729097317979339", "text": "https://stride-hr.slack.com/archives/C07KUHPHS2C/p1729097317979339"}]}]}]}, {"ts": "1729110859.567869", "text": "<@U0690EB5JE5> Topics for Merge's product webinar on 29th. It might be good for you to attend.\n\n• <PERSON><PERSON><PERSON>, Technical Implementation Consultant at Thrive Learning, will show how Merge's SFTP has enabled them to support manual data uploads\n• <PERSON>, Engineering Manager at 15Five, will show how they're leveraging Field Mapping to map custom data fields\n", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "EtjLH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Topics for <PERSON><PERSON>'s product webinar on 29th. It might be good for you to attend.\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON>, Technical Implementation Consultant at Thrive Learning, will show how Merge's SFTP has enabled them to support manual data uploads"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>, Engineering Manager at 15Five, will show how they're leveraging Field Mapping to map custom data fields"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}], "created_at": "2025-05-22T21:35:34.647083"}