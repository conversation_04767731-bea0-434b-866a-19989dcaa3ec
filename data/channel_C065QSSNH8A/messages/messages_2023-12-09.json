{"date": "2023-12-09", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1702142129.704259", "text": "<@U04DKEFP1K8> how do I get access to AWS to do this. See screenshot", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702142129.704259", "reply_count": 4, "files": [{"id": "F0697RDRQ2J", "created": 1702142126, "timestamp": 1702142126, "name": "Screenshot 2023-12-09 at 9.13.50 AM.png", "title": "Screenshot 2023-12-09 at 9.13.50 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 379003, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F0697RDRQ2J/screenshot_2023-12-09_at_9.13.50___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F0697RDRQ2J/download/screenshot_2023-12-09_at_9.13.50___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_360.png", "thumb_360_w": 343, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_480.png", "thumb_480_w": 458, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_720.png", "thumb_720_w": 686, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_800.png", "thumb_800_w": 800, "thumb_800_h": 839, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_960.png", "thumb_960_w": 915, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0697RDRQ2J-dfbe0dd31e/screenshot_2023-12-09_at_9.13.50___am_1024.png", "thumb_1024_w": 976, "thumb_1024_h": 1024, "original_w": 1792, "original_h": 1880, "thumb_tiny": "AwAwAC3RKg84yR05pvOfu/rT+1MDDuRQAoLDov60bn/u0deh/Q0uT6/+O0ALk+lFJk+v/jtKaAFFFAooARsjoQKBnvihulA5FAC0hpaQ0AA6Um8eho5o5oACwPY0owO9HPrRg+tAC5zSGjB9aTB9aAP/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0697RDRQ2J/screenshot_2023-12-09_at_9.13.50___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F0697RDRQ2J-4f78e10f40", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "h6ogx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " how do I get access to AWS to do this. See screenshot"}]}]}]}, {"ts": "1702139512.001619", "text": "<@U04DKEFP1K8> what's the status of demo sandbox?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702139512.001619", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "XYQWY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what's the status of demo sandbox?"}]}]}]}, {"ts": "1702112280.952049", "text": "<!here> i am back from vacation and i see multiple threads i need to respond to. I will start responding to them now. ", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "gratitude-thank-you", "users": ["U065H3M6WJV", "U0658EW4B8D"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "fEObI", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " i am back from vacation and i see multiple threads i need to respond to. I will start responding to them now. "}]}]}]}, {"ts": "1702085923.231049", "text": "<@U04DKEFP1K8> SDF is asking for Compiify SLAs. I am assuming we need to establish them. Can you do this by <PERSON><PERSON>?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1702085923.231049", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "G526y", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " SDF is asking for Compiify SLAs. I am assuming we need to establish them. Can you do this by <PERSON><PERSON>?"}]}]}]}, {"ts": "1702063339.271179", "text": "Main action items from today's call &amp; next steps for us:\n• DA - provide templates for adjustment letters\n• DA - provide their existing salary range info for US employees\n• <@U0658EW4B8D> - Update project plan dates to reflect their cycle timeline\n• <@U04DKEFP1K8> - Load data to Compiify so we can start UAT", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1702063339.271179", "reply_count": 4, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4", "U0658EW4B8D"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "BbjG5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Main action items from today's call & next steps for us:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA - provide templates for adjustment letters"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "DA - provide their existing salary range info for US employees"}]}, {"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " - Update project plan dates to reflect their cycle timeline"}]}, {"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " - Load data to Compiify so we can start UAT"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1702062328.208599", "text": "This meeting was actually great. I think we're going to be able to slam this one out of the park. Sounds like we'll get some ranges and be able to really show really strong value to DA", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "excited", "users": ["U065H3M6WJV"], "count": 1}, {"name": "tada", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "iaZLh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This meeting was actually great. I think we're going to be able to slam this one out of the park. Sounds like we'll get some ranges and be able to really show really strong value to DA"}]}]}]}], "created_at": "2025-05-22T21:35:34.570123"}