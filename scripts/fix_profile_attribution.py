#!/usr/bin/env python3
"""
Fix profile attribution for Slack messages.
"""

import os
import sys
import django
import re

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.documents.models import DocumentChunk
from apps.accounts.models import UserPlatformProfile

def analyze_profile_attribution():
    """Analyze current profile attribution."""
    print("🔍 Analyzing Profile Attribution")
    print("=" * 50)
    
    # Check existing platform profiles
    profiles = UserPlatformProfile.objects.all()
    print(f"📊 Total platform profiles: {profiles.count()}")
    
    for profile in profiles:
        print(f"   - {profile.display_name} ({profile.platform}) - ID: {profile.platform_user_id}")
    
    # Check chunks with profile attribution
    chunks_with_profiles = DocumentChunk.objects.exclude(profile__isnull=True)
    print(f"📊 Chunks with profiles: {chunks_with_profiles.count()}")
    
    # Check chunks without profile attribution
    chunks_without_profiles = DocumentChunk.objects.filter(profile__isnull=True)
    print(f"📊 Chunks without profiles: {chunks_without_profiles.count()}")
    
    # Sample chunks to see the text format
    print(f"\n📄 Sample chunk texts:")
    sample_chunks = DocumentChunk.objects.filter(text__icontains='amanda')[:3]
    for i, chunk in enumerate(sample_chunks, 1):
        print(f"\nChunk {i}:")
        print(f"   Text preview: {chunk.text[:300]}...")
        print(f"   Profile: {chunk.profile}")

def create_missing_profiles():
    """Create missing platform profiles based on chunk content."""
    print(f"\n🔧 Creating Missing Platform Profiles")
    print("=" * 50)
    
    # Find unique names in Slack messages
    slack_chunks = DocumentChunk.objects.filter(
        document__source__source_type='local_slack'
    )
    
    # Extract names from Slack message format
    names_found = set()
    name_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] ([^:]+):'
    
    for chunk in slack_chunks[:100]:  # Sample first 100 chunks
        matches = re.findall(name_pattern, chunk.text)
        for timestamp, name in matches:
            if name.strip() and name not in ['Previous context', '/Previous context']:
                names_found.add(name.strip())
    
    print(f"📊 Unique names found in Slack messages: {len(names_found)}")
    for name in sorted(names_found):
        print(f"   - {name}")
    
    # Create platform profiles for missing names
    created_count = 0
    for name in names_found:
        # Check if profile already exists
        existing = UserPlatformProfile.objects.filter(
            platform='slack',
            display_name=name
        ).first()
        
        if not existing:
            # Create new profile
            profile = UserPlatformProfile.objects.create(
                platform='slack',
                platform_user_id=f"slack_{name.lower().replace(' ', '_')}",
                display_name=name
            )
            print(f"✅ Created profile for: {name}")
            created_count += 1
        else:
            print(f"   Profile already exists for: {name}")
    
    print(f"\n🎉 Created {created_count} new platform profiles")

def fix_chunk_profile_attribution():
    """Fix profile attribution for chunks."""
    print(f"\n🔧 Fixing Chunk Profile Attribution")
    print("=" * 50)
    
    # Get all Slack chunks without profile attribution
    slack_chunks = DocumentChunk.objects.filter(
        document__source__source_type='local_slack',
        profile__isnull=True
    )
    
    print(f"📊 Slack chunks without profiles: {slack_chunks.count()}")
    
    # Get all platform profiles
    profiles = {profile.display_name: profile for profile in UserPlatformProfile.objects.filter(platform='slack')}
    
    updated_count = 0
    name_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] ([^:]+):'
    
    for chunk in slack_chunks:
        # Find the first speaker in the chunk
        matches = re.findall(name_pattern, chunk.text)
        if matches:
            # Get the first speaker (usually the main author of the chunk)
            first_speaker = matches[0][1].strip()
            
            if first_speaker in profiles and first_speaker not in ['Previous context', '/Previous context']:
                chunk.profile = profiles[first_speaker]
                chunk.save(update_fields=['profile'])
                updated_count += 1
                
                if updated_count <= 5:  # Show first 5 updates
                    print(f"✅ Updated chunk {chunk.id} -> {first_speaker}")
    
    print(f"\n🎉 Updated {updated_count} chunks with profile attribution")

def verify_amanda_attribution():
    """Verify Amanda's profile attribution."""
    print(f"\n🔍 Verifying Amanda's Profile Attribution")
    print("=" * 50)
    
    # Check Amanda's profile
    amanda_profile = UserPlatformProfile.objects.filter(
        display_name='amanda',
        platform='slack'
    ).first()
    
    if amanda_profile:
        print(f"✅ Amanda profile found: {amanda_profile}")
        
        # Check chunks attributed to Amanda
        amanda_chunks = DocumentChunk.objects.filter(profile=amanda_profile)
        print(f"📊 Chunks attributed to Amanda: {amanda_chunks.count()}")
        
        # Check chunks mentioning Amanda
        chunks_mentioning_amanda = DocumentChunk.objects.filter(text__icontains='amanda')
        print(f"📊 Chunks mentioning Amanda: {chunks_mentioning_amanda.count()}")
        
        # Show sample Amanda chunks
        for i, chunk in enumerate(amanda_chunks[:3], 1):
            print(f"\nAmanda Chunk {i}:")
            print(f"   Document: {chunk.document.title}")
            print(f"   Text preview: {chunk.text[:200]}...")
    else:
        print("❌ Amanda profile not found")

if __name__ == "__main__":
    analyze_profile_attribution()
    create_missing_profiles()
    fix_chunk_profile_attribution()
    verify_amanda_attribution()
