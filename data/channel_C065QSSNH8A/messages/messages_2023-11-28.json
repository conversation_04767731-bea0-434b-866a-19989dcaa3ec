{"date": "2023-11-28", "channel_id": "C065QSSNH8A", "message_count": 10, "messages": [{"ts": "1701150311.478749", "text": "<@U065H3M6WJV> I have added you to recurring advisor call with Paul &amp; Angela ( Made you the owner as well) Please accept ownership change. Once you are are the owner you can modify video conferencing detail sand add your zoom link. You are already part of recurring session with <PERSON> ( I have made you the owner for these sessions as well).", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1701150311.478749", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "q/o5E", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " I have added you to recurring advisor call with <PERSON> Angela ( Made you the owner as well) Please accept ownership change. Once you are are the owner you can modify video conferencing detail sand add your zoom link. You are already part of recurring session with <PERSON> ( I have made you the owner for these sessions as well)."}]}]}]}, {"ts": "1701137348.913659", "text": "I think you covered most of it. Given they haven't figured out the pay bands, job leveling and job architecture, we will have to discuss the workflows in more details. Lets chat more during the call with <PERSON> tomorrow.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nCaeS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think you covered most of it. Given they haven't figured out the pay bands, job leveling and job architecture, we will have to discuss the workflows in more details. Lets chat more during the call with <PERSON> tomorrow."}]}]}]}, {"ts": "1701131681.885769", "text": "Based on today's call with Digital Asset, it seems like these would be the main features we'd need to support for their first compensation cycle:\n\n• Google SSO\n    ◦ _Anticipate 3 users this cycle: <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (CFO)_\n• CSV upload of employee &amp; salary data\n• Ability to input Salary bands in Compiify (since they don't have pre-defined bands to upload)\n    ◦ C_ould Compiify help *suggest* salary bands based on the imported employee data?_\n• Ability to upload or directly input performance ratings in Compiify\n• Assign separate budgets for merit/promo and for spot bonuses\n• Ability to input perf &amp; comp on behalf of managers \n    ◦ _Super Admin access may be sufficient for this requirement_\n• Toggle between USD and local currency\n• Ability to send comp adjustment letters from Compiify\n\nAnything else?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "gDF1n", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Based on today's call with Digital Asset, it seems like these would be the main features we'd need to support for their first compensation cycle:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Google SSO"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Anticipate 3 users this cycle: <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (CFO)", "style": {"italic": true}}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "CSV upload of employee & salary data"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Ability to input Salary bands in Compiify (since they don't have pre-defined bands to upload)"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "C"}, {"type": "text", "text": "ould Compiify help ", "style": {"italic": true}}, {"type": "text", "text": "suggest ", "style": {"bold": true, "italic": true}}, {"type": "text", "text": "salary bands based on the imported employee data?", "style": {"italic": true}}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ability to upload or directly input performance ratings in Compiify"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Assign separate budgets for merit/promo and for spot bonuses"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Ability to input perf & comp on behalf of managers "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Super Admin access may be sufficient for this requirement", "style": {"italic": true}}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Toggle between USD and local currency"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Ability to send comp adjustment letters from Compiify"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n\nAnything else?"}]}]}]}, {"ts": "1701119792.078279", "text": "<@U04DKEFP1K8> can you add <PERSON> to all advisor calls including <PERSON> and <PERSON>", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "GcAO8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you add <PERSON> to all advisor calls including <PERSON> and <PERSON>"}]}]}]}, {"ts": "1701119645.090839", "text": "Dan's notes from call with DA\n\nJan 12 - Budgets decided / *Cycle Kickoff*\nFeb 14 - Recommendations submitted / *Cycle End*\nFeb 16 - CFO review\nMarch 14-18 - *Comp Commitee Reviews*\nMarch 25-29 - *Comp Conversations*\nApril 1 - *Effective date*\nApril 10 - Get increases to Payroll\nApril 15 - *First payroll US*\nMonthly - All other\n\n\n5-point rating scale\n1 - New / Training\n2 - Meets some expectations\n3 - Meets Most expectations\n4 - Exceeds Expectations\n5 - Above and Beyond\n\nNo Merit matrix nor clearly defined ranges\n\nBonus is most typically a spot bonus but will sometimes be lump sum in lieu of\n\nDo have variable/sales employees\n\nEquity is D-Units but won’t be input during cycle, no need for vested/unvested\n\n219 EEs\n57 Mgrs\n\nTechnology / PM have job leveling, everyone else no\n\n5-6 regions\nUK, Budapest, Sydney, Hong Kong, Zurich, US", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "lkX0Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>'s notes from call with DA\n\nJan 12 - Budgets decided / "}, {"type": "text", "text": "Cycle Kickoff", "style": {"bold": true}}, {"type": "text", "text": "\nFeb 14 - Recommendations submitted / "}, {"type": "text", "text": "Cycle End", "style": {"bold": true}}, {"type": "text", "text": "\nFeb 16 - CFO review\nMarch 14-18 - "}, {"type": "text", "text": "Comp Commitee Reviews", "style": {"bold": true}}, {"type": "text", "text": "\nMarch 25-29 - "}, {"type": "text", "text": "Comp Conversations", "style": {"bold": true}}, {"type": "text", "text": "\nApril 1 - "}, {"type": "text", "text": "Effective date", "style": {"bold": true}}, {"type": "text", "text": "\nApril 10 - Get increases to Payroll\nApril 15 - "}, {"type": "text", "text": "First payroll US", "style": {"bold": true}}, {"type": "text", "text": "\nMonthly - All other\n\n\n5-point rating scale\n1 - New / Training\n2 - Meets some expectations\n3 - Meets Most expectations\n4 - Exceeds Expectations\n5 - Above and Beyond\n\nNo Merit matrix nor clearly defined ranges\n\nBonus is most typically a spot bonus but will sometimes be lump sum in lieu of\n\nDo have variable/sales employees\n\nEquity is D-Units but won’t be input during cycle, no need for vested/unvested\n\n219 EEs\n57 Mgrs\n\nTechnology / PM have job leveling, everyone else no\n\n5-6 regions\nUK, Budapest, Sydney, Hong Kong, Zurich, US"}]}]}]}, {"ts": "1701115571.329359", "text": "BT<PERSON>, does <PERSON><PERSON><PERSON><PERSON><PERSON> give you a full transcript of each call? I was taking some notes on the side, but I'm sure I didn't capture everything.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701115571.329359", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "ntad1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "BT<PERSON>, does <PERSON><PERSON><PERSON><PERSON><PERSON> give you a full transcript of each call? I was taking some notes on the side, but I'm sure I didn't capture everything."}]}]}]}, {"ts": "1701115321.817339", "text": "Yes, this was a fascinating call! Feels like we'll learn a lot, and <PERSON><PERSON><PERSON> is especially forthcoming with her thoughts. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zUBC4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, this was a fascinating call! Feels like we'll learn a lot, and <PERSON><PERSON><PERSON> is especially forthcoming with her thoughts. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1701115277.167279", "text": "DA will be a good one for us to build customizations for companies with no structured processes in place", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FgTld", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA will be a good one for us to build customizations for companies with no structured processes in place"}]}]}]}, {"ts": "1701115252.105739", "text": "<@U04DKEFP1K8> Do we have a secure way for customers to share real employee data? Emailing CSV fils is probably not the best option...", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701115252.105739", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "RDh4O", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Do we have a secure way for customers to share real employee data? Emailing CSV fils is probably not the best option..."}]}]}]}, {"ts": "1701110867.700499", "text": "I see <PERSON><PERSON> put a meeting on our calendars for tomorrow to keep talking about <PERSON><PERSON> - are we ready for another sync? Do we have any tentative plan for implementing a proof of concept with their sandbox, for example?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1701110867.700499", "reply_count": 13, "blocks": [{"type": "rich_text", "block_id": "2tby3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I see <PERSON><PERSON> put a meeting on our calendars for tomorrow to keep talking about <PERSON><PERSON> - are we ready for another sync? Do we have any tentative plan for implementing a proof of concept with their sandbox, for example?"}]}]}]}], "created_at": "2025-05-22T21:35:34.575794"}