{"date": "2024-09-12", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1726157098.437619", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> just met with <PERSON><PERSON>. He's a GO from me", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "KSS7b", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " just met with <PERSON><PERSON>. He's a GO from me"}]}]}]}, {"ts": "1726095531.373489", "text": "Yes, we just need to schedule a call out a little bit so I have time", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "3pMhA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, we just need to schedule a call out a little bit so I have time"}]}]}]}, {"ts": "1726092204.273189", "text": "<@U07EJ2LP44S> is it possible to go through the SDF issues and ensure they are resolved before showing the demo to <PERSON> and her CTO?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "58j1z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " is it possible to go through the SDF issues and ensure they are resolved before showing the demo to <PERSON> and her CTO?"}]}]}]}, {"ts": "1726090795.343059", "text": "<!here> With cainwatters providing us with a a 2nd version of their salary bands earlier in the day, we will need versioning support for Salary Bands. In the production environment we cannot directly update existing bands as it can affect calculations for previous cycle. Will discuss with <@U0690EB5JE5> scope for this change.\ncc: <@U07EJ2LP44S>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1726090795.343059", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "cRTWg", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " With cainwatters providing us with a a 2nd version of their salary bands earlier in the day, we will need versioning support for Salary Bands. In the production environment we cannot directly update existing bands as it can affect calculations for previous cycle. Will discuss with "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " scope for this change.\ncc: "}, {"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}], "created_at": "2025-05-22T21:35:34.640267"}