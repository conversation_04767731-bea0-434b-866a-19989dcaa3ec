{"date": "2024-12-12", "channel_id": "C065QSSNH8A", "message_count": 10, "messages": [{"ts": "1734025306.800759", "text": "<@U0690EB5JE5> <@U07EJ2LP44S> based on the advisor calls, valgenesis call today, and market research, so far the conclusion is that giving customers the ability to create and manage paybands in Stride is not be the best problem to solve for at this point. It's not going to help us find the product market fit. I can share more during the standup.\n\nWe can still make improvements to the existing paybands module to make it more useful to the customers. I'll create the PRD for that but it's not a top priority.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734025306.800759", "reply_count": 3, "reactions": [{"name": "+1", "users": ["U07EJ2LP44S", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "UY/sK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " based on the advisor calls, valgenesis call today, and market research, so far the conclusion is that giving customers the ability to create and manage paybands in Stride is not be the best problem to solve for at this point. It's not going to help us find the product market fit. I can share more during the standup.\n\nWe can still make improvements to the existing paybands module to make it more useful to the customers. I'll create the PRD for that but it's not a top priority."}]}]}]}, {"ts": "1734022360.396179", "text": "Valgenesis promotions request: <https://compiify.atlassian.net/browse/COM-4030>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14113::2ba2409e8f0e48f59950056e8988b9af", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4030?atlOrigin=eyJpIjoiM2M1ZDNlZWU2MGMxNDI2NjkwZTM1OTU0Mjc0MDM1ODciLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4030 Promotions upload - preload salary>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14113::bdd7318f5f2c4d368e4efca64934ad09", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14113\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4030\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4030", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "6/kuC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis promotions request: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4030"}]}]}]}, {"ts": "1734022242.245159", "text": "Valgenesis HiBob data refresh request: <https://compiify.atlassian.net/browse/COM-4029>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734022242.245159", "reply_count": 11, "edited": {"user": "U07EJ2LP44S", "ts": "1734022257.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14112::e8166c654f0c4f07b628f3c762bd94ab", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4029?atlOrigin=eyJpIjoiZWI3ZTE2NTYxNzg5NDYzN2I0ZTQ5ZTBjMTdkY2VkNjIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4029 HiBob Refresh>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14112::67ee2b6e0ba1469391c46b56fe84db40", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14112\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4029\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4029", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "QTqIK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis HiBob data refresh request: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4029"}]}]}]}, {"ts": "1734019102.607529", "text": "It will be recorded", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "b18at", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It will be recorded"}]}]}]}, {"ts": "1734019098.808569", "text": "Today we are talking with valgenesis about their paybands needs", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "i2cOR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Today we are talking with valgenesis about their paybands needs"}]}]}]}, {"ts": "1734019079.223689", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> I won’t be able to attend meeting today and tomorrow. ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4xkw7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I "}, {"type": "text", "text": "won’t"}, {"type": "text", "text": " be able to attend meeting today and tomorrow. "}]}]}]}, {"ts": "1734015167.151239", "text": "<@U0690EB5JE5>  <@U07M6QKHUC9> Tith<PERSON> is asking if its possible to have salary penetration instead of compa ratio. This seems reasonable but I know it will be some work since compa ratio is in several places. I'm not sure we need to go fully into the insights, but maybe in the merit and org tables? Salary/range penetration is salary divided by the max shown as a percentage.\n\nThey don't use compa ratio so it'll be confusing for the recommending managers.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734015167.151239", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "NYe8L", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "  "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Tithely is asking if its possible to have salary penetration instead of compa ratio. This seems reasonable but I know it will be some work since compa ratio is in several places. I'm not sure we need to go fully into the insights, but maybe in the merit and org tables? Salary/range penetration is salary divided by the max shown as a percentage.\n\nThey don't use compa ratio so it'll be confusing for the recommending managers."}]}]}]}, {"ts": "1734001969.428889", "text": "<@U07M6QKHUC9> Regarding Tithley employment history, Could you please get an example employee with their employment history from their HRIS system, I did check the data from API, Currently we get some history but it is not making much sense to me. Need an example to confirm what we get from API matches the expectation.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734001969.428889", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "uvWMQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Regarding Tithley employment history, Could you please get an example employee with their employment history from their HRIS system, I did check the data from API, Currently we get some history but it is not making much sense to me. Need an example to confirm what we get from API matches the expectation."}]}]}]}, {"ts": "1733967545.251179", "text": "Will be off until Monday.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733713768.892529", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "itMUf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will be off until Monday."}]}]}]}, {"ts": "1733948742.699469", "text": "This is a really convoluted one, I hope I explained it correctly: <https://compiify.atlassian.net/browse/COM-4028>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "reply_count": 25, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14111::ca2907ce43364a5e83080dfd90c73b89", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4028?atlOrigin=eyJpIjoiMzlmZmRjODdkZTU1NDIwNjlmZjMyNWViMzRmZDMwYmEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4028 Issue: Bug in Diversified Recommender Selection>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14111::b25d0a039b66447aa9f5e90511b7ea1e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14111\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4028\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4028", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Y890q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is a really convoluted one, I hope I explained it correctly: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4028"}]}]}]}], "created_at": "2025-05-22T21:35:34.678596"}