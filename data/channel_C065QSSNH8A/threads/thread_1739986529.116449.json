{"thread_ts": "1739986529.116449", "channel_id": "C065QSSNH8A", "reply_count": 3, "replies": [{"ts": "1739986575.539029", "text": "Essentially as soon as they hit the threshold to not be eligible for a market increase, it turns off editability of the field completely.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739986529.116449", "blocks": [{"type": "rich_text", "block_id": "HSw0U", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Essentially as soon as they hit the threshold to not be eligible for a market increase, it turns off editability of the field completely."}]}]}]}, {"ts": "1740017520.145329", "text": "Understood. Looks like wrong value is used for checking.  Will get this fixed today.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739986529.116449", "blocks": [{"type": "rich_text", "block_id": "PaUIP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Understood. Looks like wrong value is used for checking.  Will get this fixed today."}]}]}]}, {"ts": "1740050860.181319", "text": "<@U07EJ2LP44S> The issue is fixed.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739986529.116449", "blocks": [{"type": "rich_text", "block_id": "FzbU3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " The issue is fixed."}]}]}]}], "created_at": "2025-05-22T22:00:49.360234"}