{"date": "2024-03-18", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1710783063.029459", "text": "<@U04DKEFP1K8> I walked through adjustment letters workflow with <PERSON> this morning, and have some updates:\n• They want to use <PERSON><PERSON><PERSON>'s name and signature instead of Emnet's. <PERSON> has sent me a .png for <PERSON><PERSON><PERSON>'s signature, and we'll need to update the template text as well. \n• Their pay effective date is April 1, not March 15, so I believe we need to change their current cycle settings for that. \n• They will not be giving any one-time bonuses so we only need to have 1 template, for pay increases.\n• <PERSON> will be generating &amp; downloading the adjustment letters -- let's make sure that the process for doing this in bulk works as expected, and correctly assigns a specific employee name in each file name.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1710783063.029459", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "bLs9a", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I walked through adjustment letters workflow with <PERSON> this morning, and have some updates:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They want to use <PERSON><PERSON><PERSON>'s name and signature instead of Emnet's. <PERSON> has sent me a .png for <PERSON><PERSON><PERSON>'s signature, and we'll need to update the template text as well. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Their pay effective date is April 1, not March 15, so I believe we need to change their current cycle settings for that. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "They will not be giving any one-time bonuses so we only need to have 1 template, for pay increases."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> will be generating & downloading the adjustment letters -- let's make sure that the process for doing this in bulk works as expected, and correctly assigns a specific employee name in each file name."}]}], "style": "bullet", "indent": 0, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.608699"}