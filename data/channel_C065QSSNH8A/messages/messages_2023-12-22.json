{"date": "2023-12-22", "channel_id": "C065QSSNH8A", "message_count": 16, "messages": [{"ts": "1703262892.979399", "text": "<@U065H3M6WJV> we can use this document for auth discussion later in the day <https://docs.google.com/document/d/13k3u2jMsN7nUZ_-4kKir_wcu6Ac-0_NKv5mEWUZDYJU/edit>", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "gratitude-thank-you", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "b7A17", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " we can use this document for auth discussion later in the day "}, {"type": "link", "url": "https://docs.google.com/document/d/13k3u2jMsN7nUZ_-4kKir_wcu6Ac-0_NKv5mEWUZDYJU/edit"}]}]}]}, {"ts": "1703206366.131979", "text": "<PERSON><PERSON>", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Mu001", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON>"}]}]}]}, {"ts": "1703206346.448719", "text": "new analytics dashboard coming together", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "aaaaaa", "users": ["U065H3M6WJV"], "count": 1}, {"name": "rolling_on_the_floor_laughing", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F06BC1YL2BW", "created": 1703206335, "timestamp": 1703206335, "name": "Screenshot 2023-12-21 at 4.52.12 PM.png", "title": "Screenshot 2023-12-21 at 4.52.12 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 77901, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F06BC1YL2BW/screenshot_2023-12-21_at_4.52.12_pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F06BC1YL2BW/download/screenshot_2023-12-21_at_4.52.12_pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_360.png", "thumb_360_w": 360, "thumb_360_h": 224, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_480.png", "thumb_480_w": 480, "thumb_480_h": 298, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_720.png", "thumb_720_w": 720, "thumb_720_h": 448, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_800.png", "thumb_800_w": 800, "thumb_800_h": 497, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_960.png", "thumb_960_w": 960, "thumb_960_h": 597, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06BC1YL2BW-9653a13ce9/screenshot_2023-12-21_at_4.52.12_pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 637, "original_w": 1264, "original_h": 786, "thumb_tiny": "AwAdADDRY4ANKCD0oYZU1HgADrmmBIpzn6012wcCo1cqTj9aUkE+/enYCUcjNLTYz8uPSnVICYzSBQPWlooAhwKMe5qXaPSjaPSncCMZHQmnKSW5NO2j0owOwouB/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06BC1YL2BW/screenshot_2023-12-21_at_4.52.12_pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F06BC1YL2BW-1d23a2e252", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "7fsvc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "new analytics dashboard coming together"}]}]}]}, {"ts": "1703202980.256099", "text": "Ah, I have heard the track but did not realize that it had to fit the tune :joy: ", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1703203020.000000"}, "blocks": [{"type": "rich_text", "block_id": "jG0v+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ah, I have heard the track but did not realize that it had to fit the tune "}, {"type": "emoji", "name": "joy", "unicode": "1f602"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1703201989.123149", "text": "(That's 2 for <PERSON> today! :joy: )", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2BCEw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "(That's 2 for <PERSON> today! "}, {"type": "emoji", "name": "joy", "unicode": "1f602"}, {"type": "text", "text": " )"}]}]}]}, {"ts": "1703201980.900429", "text": "(It rhymes, but doesn't _quite_ fit the tune -- <PERSON> wins the matchup!)", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "joy", "users": ["U04DS2MBWP4", "U0658EW4B8D"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "QPJCb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "(It rhymes, but doesn't "}, {"type": "text", "text": "quite", "style": {"italic": true}}, {"type": "text", "text": " fit the tune -- <PERSON> wins the matchup!)"}]}]}]}, {"ts": "1703201957.358189", "text": "Trying to figure out whether <PERSON><PERSON><PERSON> has heard the Frozen soundtrack as much as the rest of us... :zany_face: :joy:", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0EOkQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Trying to figure out whether <PERSON><PERSON><PERSON> has heard the Frozen soundtrack as much as the rest of us... "}, {"type": "emoji", "name": "zany_face", "unicode": "1f92a"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "joy", "unicode": "1f602"}]}]}]}, {"ts": "1703201356.216549", "text": "Do you need to align pay and role?\nWith Compiify, you take full control.\nFrom salary to bonus to equity, it's a knack,\nCompiify's will get you on track, you're never looking back!", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "thinking_face", "users": ["U065H3M6WJV"], "count": 1}, {"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "9dAlT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you need to align pay and role?\nWith Compiify, you take full control.\nFrom salary to bonus to equity, it's a knack,\nCompiify's will get you on track, you're never looking back!"}]}]}]}, {"ts": "1703200870.212329", "text": "_Do you want to build a cycle?_\n_There is nothing that we lack_\n_We can plan for Salary_\n_And even Equity_\n_Compiify's got your baaaaaack!_", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "+1", "users": ["U065H3M6WJV"], "count": 1}, {"name": "grinning", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "HhhKT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you want to build a cycle?", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "There is nothing that we lack", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "We can plan for Sal<PERSON>", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "And even Equity", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Compiify's got your baaaaaack!", "style": {"italic": true}}]}]}]}, {"ts": "1703198679.212559", "text": "And now, because this morning's Frozen parody idea is still stuck in my head, I finished the verse :laughing:\n\n:musical_note: :snowflake: :snowman: :snowflake: :musical_note:\n_Do you want to build a budget?_\n_How much money will you need?_\n_You can upload your employee file,_\n_Then sit back a while,_ \n_Compiify will take the leeeeeead...._", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "grinning", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Zj32J", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And now, because this morning's Frozen parody idea is still stuck in my head, I finished the verse "}, {"type": "emoji", "name": "laughing", "unicode": "1f606"}, {"type": "text", "text": "\n\n"}, {"type": "emoji", "name": "musical_note", "unicode": "1f3b5"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "snowflake", "unicode": "2744-fe0f"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "snowman", "unicode": "2603-fe0f"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "snowflake", "unicode": "2744-fe0f"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "musical_note", "unicode": "1f3b5"}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Do you want to build a budget?", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "How much money will you need?", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "You can upload your employee file,", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Then sit back a while, ", "style": {"italic": true}}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Compiify will take the leeeeeead....", "style": {"italic": true}}]}]}]}, {"ts": "1703195922.283379", "text": "Got it, thanks <@U0658EW4B8D>! I've added this to our standard templates and made a copy for our newest potential beta customer (Neuroflow) :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "DFVLy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Got it, thanks "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": "! I've added this to our standard templates and made a copy for our newest potential beta customer (Neuroflow) "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1703195393.496849", "text": "<PERSON><PERSON> know if you have difficulty downloading", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "RSBlf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON> know if you have difficulty downloading"}]}]}]}, {"ts": "1703192701.427219", "text": "<@U04DKEFP1K8> has  the Promotion template CSV/XLSX file", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HNvIH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " has  the Promotion template CSV/XLSX file"}]}]}]}, {"ts": "1703192666.463899", "text": "yes", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IB8ze", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes"}]}]}]}, {"ts": "1703192620.326569", "text": "<@U04DS2MBWP4> Is <https://drive.google.com/file/d/1LO-5QRTntGFl7AxbL9CFhv1bpI0tETdo/view?usp=drive_link|this the correct file> to send customers as our SOC 2 report?", "user": "U065H3M6WJV", "type": "message", "files": [{"id": "F06B8M9KZ2P", "created": 1703192624, "timestamp": 1703192624, "name": "COMPIIFY SOC 2 Type 2 Report.pdf", "title": "COMPIIFY SOC 2 Type 2 Report.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 162683, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1LO-5QRTntGFl7AxbL9CFhv1bpI0tETdo", "external_url": "https://drive.google.com/file/d/1LO-5QRTntGFl7AxbL9CFhv1bpI0tETdo/view?usp=drive_link", "url_private": "https://drive.google.com/file/d/1LO-5QRTntGFl7AxbL9CFhv1bpI0tETdo/view?usp=drive_link", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_720.png", "thumb_720_w": 556, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06B8M9KZ2P-f1612194ef/compiify_soc_2_type_2_report_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1325, "thumb_tiny": "AwAwACXQMiAkFhkdaPNj/vr+dKQM9BRgeg/KjQADBhkHNLR+VFABRRRQA0rk9R+VG0eo/KkZcsfl/HNGz2H5mlYdxdo9R+VKAB6U0Jj+Ffxpw4HQA+1FguLRRRTEISc0ZpaKAEBpaKKACiijNAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06B8M9KZ2P/compiify_soc_2_type_2_report.pdf", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "esRyl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Is "}, {"type": "link", "url": "https://drive.google.com/file/d/1LO-5QRTntGFl7AxbL9CFhv1bpI0tETdo/view?usp=drive_link", "text": "this the correct file"}, {"type": "text", "text": " to send customers as our SOC 2 report?"}]}]}]}, {"ts": "1703192428.507109", "text": "Who's got the Promotion template CSV/XLSX file?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EBCLX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Who's got the Promotion template CSV/XLSX file?"}]}]}]}], "created_at": "2025-05-22T21:35:34.596551"}