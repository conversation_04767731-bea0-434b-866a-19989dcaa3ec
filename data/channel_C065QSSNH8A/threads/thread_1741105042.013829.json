{"thread_ts": "**********.013829", "channel_id": "C065QSSNH8A", "reply_count": 3, "replies": [{"ts": "1741106153.902869", "text": "<@U07EJ2LP44S> Something wrong with this specific user email. I may have to reach out Auth0 support, I am unable to recreate the user. Will keep you posted.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.013829", "blocks": [{"type": "rich_text", "block_id": "ysQOW", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Something wrong with this specific user email. I may have to reach out Auth0 support, I am unable to recreate the user. Will keep you posted."}]}]}]}, {"ts": "**********.806489", "text": "<@U07EJ2LP44S> I deleted and recreated the user account. Could you please the user to retry?  Please ask to try first in the main account `<https://curanahealth.stridehr.io/>`.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.013829", "blocks": [{"type": "rich_text", "block_id": "xKdLY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I deleted and recreated the user account. Could you please the user to retry?  Please ask to try first in the main account "}, {"type": "link", "url": "https://curanahealth.stridehr.io/", "style": {"code": true}}, {"type": "text", "text": "."}]}]}]}, {"ts": "**********.949749", "text": "worst case the password login works.\n`<mailto:<EMAIL>|<EMAIL>>`\npassword: `#$23%qW9`", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.013829", "blocks": [{"type": "rich_text", "block_id": "mDa7w", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "worst case the password login works.\n"}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>", "style": {"code": true}}, {"type": "text", "text": "\npassword: "}, {"type": "text", "text": "#$23%qW9", "style": {"code": true}}]}]}]}], "created_at": "2025-05-22T22:00:49.369709"}