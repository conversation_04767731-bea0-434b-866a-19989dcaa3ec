{"thread_ts": "1741197243.744769", "channel_id": "C065QSSNH8A", "reply_count": 4, "replies": [{"ts": "1741198771.990899", "text": "I'm looking into this\nNot able to figure out yet, will update when I do", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1741197243.744769", "blocks": [{"type": "rich_text", "block_id": "hc88c", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'm looking into this\nNot able to figure out yet, will update when I do"}]}]}]}, {"ts": "1741201736.653079", "text": "The job title was changed maybe before the new band was created and the band assignment did not happen.\nWe need to debug more to find out the root cause, will have the fix by tomorrow.", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1741197243.744769", "blocks": [{"type": "rich_text", "block_id": "fTybr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The job title was changed maybe before the new band was created and the band assignment did not happen.\nWe need to debug more to find out the root cause, will have the fix by tomorrow."}]}]}]}, {"ts": "1741203088.149089", "text": "That might be the case, but I have gotten new bands to attach to existing employees just by refreshing the user. Which is what I was trying to do when I saved the employee again inside the org view, but it did not work. :weary: ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1741197243.744769", "blocks": [{"type": "rich_text", "block_id": "tOUA/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That might be the case, but I have gotten new bands to attach to existing employees just by refreshing the user. Which is what I was trying to do when I saved the employee again inside the org view, but it did not work. "}, {"type": "emoji", "name": "weary", "unicode": "1f629"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1741274017.744759", "text": "<@U07EJ2LP44S> This automatically got fixed when I just refreshed payband identifiers to trigger band calculation. Could you please confirm if things look good now. It looks fine to me now.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1741197243.744769", "subtype": "thread_broadcast", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ERz5z", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This automatically got fixed when I just refreshed payband identifiers to trigger band calculation. Could you please confirm if things look good now. It looks fine to me now."}]}]}]}], "created_at": "2025-05-22T22:00:49.368099"}