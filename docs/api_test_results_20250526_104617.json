{"total_tests": 9, "successful_tests": 0, "failed_tests": 9, "test_details": [{"test_name": "Basic Search", "status": "failed", "error": "HTTP 500: <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <title>ValueError\n          at /api/sear", "execution_time": 4.488270044326782}, {"test_name": "Query Expansion (HyDE)", "status": "failed", "error": "HTTP 500: <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <title>ValueError\n          at /api/sear", "execution_time": 0.06606698036193848}, {"test_name": "Multi-Step Reasoning", "status": "failed", "error": "HTTP 500: <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <title>ValueError\n          at /api/sear", "execution_time": 0.06356620788574219}, {"test_name": "Full RAG Features", "status": "failed", "error": "HTTP 500: <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <title>ValueError\n          at /api/sear", "execution_time": 0.062074899673461914}, {"test_name": "Low Relevance Threshold", "status": "failed", "error": "HTTP 500: <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <title>ValueError\n          at /api/sear", "execution_time": 0.06203103065490723}, {"test_name": "Output Format: text", "status": "failed", "error": "HTTP 500: <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <title>ValueError\n          at /api/sear", "execution_time": 0.06220602989196777}, {"test_name": "Output Format: json", "status": "failed", "error": "HTTP 500: <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <title>ValueError\n          at /api/sear", "execution_time": 0.06080484390258789}, {"test_name": "Output Format: markdown", "status": "failed", "error": "HTTP 500: <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <title>ValueError\n          at /api/sear", "execution_time": 0.06050610542297363}, {"test_name": "Output Format: table", "status": "failed", "error": "HTTP 500: <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <title>ValueError\n          at /api/sear", "execution_time": 0.06040596961975098}], "rag_techniques_verified": {"hybrid_search": false, "query_expansion": false, "multi_step_reasoning": false, "context_aware": false, "citation_engine": false, "router_engine": false}, "performance_metrics": {"total_time": 4.985932111740112, "average_time": 0.5539924568600125, "min_time": 0.06040596961975098, "max_time": 4.488270044326782}}