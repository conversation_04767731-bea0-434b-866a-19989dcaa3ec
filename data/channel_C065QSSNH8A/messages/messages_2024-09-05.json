{"date": "2024-09-05", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1725559572.579019", "text": "<@U04DKEFP1K8> we are terminating our contract with marketing but before we do that we need to publish the ROI calculator. I know you said last week the intern in on it. can we complete this task by Friday?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1725559572.579019", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "4L/fO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " we are terminating our contract with marketing but before we do that we need to publish the ROI calculator. I know you said last week the intern in on it. can we complete this task by Friday?"}]}]}]}, {"ts": "1725551270.539649", "text": "will be about 5 min late to the leadership call", "user": "U04DS2MBWP4", "type": "message", "edited": {"user": "U04DS2MBWP4", "ts": "1725551283.000000"}, "blocks": [{"type": "rich_text", "block_id": "ETF+O", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "will be about 5 min late to the leadership call"}]}]}]}, {"ts": "1725546469.625849", "text": "Good news! <https://www.linkedin.com/in/michael-a-b0378a1a3/|<PERSON>> has signed the offer letter. His start date is Sept 17th.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "party_blob", "users": ["U07EJ2LP44S", "U0690EB5JE5"], "count": 2}, {"name": "raised_hands", "users": ["U0690EB5JE5", "U07HCJ07H7G", "U04DKEFP1K8"], "count": 3}], "blocks": [{"type": "rich_text", "block_id": "s5qCy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Good news! "}, {"type": "link", "url": "https://www.linkedin.com/in/michael-a-b0378a1a3/", "text": "<PERSON>"}, {"type": "text", "text": " has signed the offer letter. His start date is Sept 17th."}]}]}]}, {"ts": "1725510643.477429", "text": "<@U07EJ2LP44S> <@U0690EB5JE5> First draft of HRBP role is in jira <https://compiify.atlassian.net/browse/COM-3506>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1725510643.477429", "reply_count": 5, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13567::9e8284006b3f11efb536b9cee8dab712", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3506?atlOrigin=eyJpIjoiYzUzMTkzMTE0YzU5NDNkNDhiNDJkZjYwNTUxZTVmMDMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3506 Enable role HRBP>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13567::9e8284026b3f11efb536b9cee8dab712", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13567::9e8284016b3f11efb536b9cee8dab712", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13567\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13567\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3506", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "zqVCp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " First draft of HRBP role is in jira "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3506"}]}]}]}, {"ts": "1725487648.450519", "text": "<@U04DKEFP1K8> <@U0690EB5JE5> Exponent got back about the SFTP and would like to schedule a call. Let me know if you want to join, <@U0690EB5JE5> - we could do 8am PST Friday", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1725487648.450519", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "ZuocT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Exponent got back about the SFTP and would like to schedule a call. Let me know if you want to join, "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " - we could do 8am PST Friday"}]}]}]}], "created_at": "2025-05-22T21:35:34.642085"}