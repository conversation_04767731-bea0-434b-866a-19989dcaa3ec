{"date": "2024-10-19", "channel_id": "C065QSSNH8A", "message_count": 37, "messages": [{"ts": "1729279506.539539", "text": "Before we hand over the environment to SDF to test, we should delete and created their cycle so they can start fresh. Will need help with this on Monday", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "j7vNI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Before we hand over the environment to SDF to test, we should delete and created their cycle so they can start fresh. Will need help with this on Monday"}]}]}]}, {"ts": "**********.700549", "text": "<@U07M6QKHUC9> Please do not mark the ticket as highest priority unless its really an issue. I see every ticket of yours is highest priority, It will be little confusing to priorotize.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.700549", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "Nhsx1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Please do not mark the ticket as highest priority unless its really an issue. I see every ticket of yours is highest priority, It will be little confusing to priorotize."}]}]}]}, {"ts": "**********.504469", "text": "Alayacare would see 607 employee count", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1XDRS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Alayacare would see 607 employee count"}]}]}]}, {"ts": "**********.392989", "text": "*PLEASE NOTE*.  In today's fixes, Root employee is excluded from everywhere to keep the count consistent across org view/org insights/merit view/reports (Except budget report). So customers will see one less than the total employees across the product consistently.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.392989", "reply_count": 15, "edited": {"user": "U0690EB5JE5", "ts": "1729356762.000000"}, "blocks": [{"type": "rich_text", "block_id": "cy5iV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "PLEASE NOTE", "style": {"bold": true}}, {"type": "text", "text": ".  In today's fixes, Root employee is excluded from everywhere to keep the count consistent across org view/org insights/merit view/reports (Except budget report). So customers will see one less than the total employees across the product consistently."}]}]}]}, {"ts": "1729356434.567569", "text": "Bonus budget fixes are ready but needs little more testing. I will push tomorrow.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "GnjoX", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bonus budget fixes are ready but needs little more testing. I will push tomorrow."}]}]}]}, {"ts": "1729356401.006319", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> following fixes or changes are synced until development branch. SDF-test is upto date with changes and fixes.\n<https://compiify.atlassian.net/browse/COM-3849>,\n<https://compiify.atlassian.net/browse/COM-3847>,\n<https://compiify.atlassian.net/browse/COM-3856>\n<https://compiify.atlassian.net/browse/COM-3857>\n<https://stride-hr.slack.com/archives/C065QSSNH8A/p1729327602595369>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729356401.006319", "reply_count": 1, "attachments": [{"from_url": "https://stride-hr.slack.com/archives/C065QSSNH8A/p1729327602595369", "ts": "1729327602.595369", "author_id": "U0690EB5JE5", "channel_id": "C065QSSNH8A", "channel_team": "T04DM97F1UM", "is_msg_unfurl": true, "is_thread_root_unfurl": true, "message_blocks": [{"team": "T04DM97F1UM", "channel": "C065QSSNH8A", "ts": "1729327602.595369", "message": {"blocks": [{"type": "rich_text", "block_id": "eV1J7", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " while I was working on disabling the one time bonus column. I have made a small change. Please let me know if this is okay, I will push.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Removed tick and replaced with flag to be consistent"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Removed Avatar with initials"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "I am also adding employee Id column for CWA  which hidden by default"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}}], "files": [{"id": "F07SMAJHXSP", "created": 1729327597, "timestamp": 1729327597, "name": "Screenshot 2024-10-18 at 5.58.49 PM.png", "title": "Screenshot 2024-10-18 at 5.58.49 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 432433, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07SMAJHXSP/screenshot_2024-10-18_at_5.58.49___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07SMAJHXSP/download/screenshot_2024-10-18_at_5.58.49___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 190, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 254, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 380, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 423, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 507, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 541, "original_w": 3396, "original_h": 1794, "thumb_tiny": "AwAZADDSHT1pab2p1ACUtJS0AJnnpSbec5b8zTu9FADTTqSloASlpO9LQAd6KKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07SMAJHXSP/screenshot_2024-10-18_at_5.58.49___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07SMAJHXSP-e915a7d52c", "comments_count": 0, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "id": 1, "original_url": "https://stride-hr.slack.com/archives/C065QSSNH8A/p1729327602595369", "fallback": "[October 19th, 2024 1:46 AM] mahesh: <!here> while I was working on disabling the one time bonus column. I have made a small change. Please let me know if this is okay, I will push.\n• Removed tick and replaced with flag to be consistent\n• Removed Avatar with initials\n• I am also adding employee Id column for CWA  which hidden by default", "text": "<!here> while I was working on disabling the one time bonus column. I have made a small change. Please let me know if this is okay, I will push.\n• Removed tick and replaced with flag to be consistent\n• Removed Avatar with initials\n• I am also adding employee Id column for CWA  which hidden by default", "author_name": "<PERSON><PERSON><PERSON>", "author_link": "https://stride-hr.slack.com/team/U0690EB5JE5", "author_icon": "https://avatars.slack-edge.com/2024-03-24/6845994044022_f0f5d7d82b9d10062154_48.jpg", "author_subname": "<PERSON><PERSON><PERSON>", "mrkdwn_in": ["text"], "footer": "Thread in Slack Conversation"}], "blocks": [{"type": "rich_text", "block_id": "o1Auc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " following fixes or changes are synced until development branch. SDF-test is upto date with changes and fixes.\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3849"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3847"}, {"type": "text", "text": ",\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3856"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3857"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://stride-hr.slack.com/archives/C065QSSNH8A/p1729327602595369"}]}]}]}, {"ts": "1729335513.367769", "text": "```Though it is intended behaviour its inconsistent. We hide it in merit table but do show in merit task page(tool tip) and cycle insights. May be we can show everywhere for now until any customer has concerns.```", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729335513.367769", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "7thR7", "elements": [{"type": "rich_text_preformatted", "elements": [{"type": "text", "text": "Though it is intended behaviour its inconsistent. We hide it in merit table but do show in merit task page(tool tip) and cycle insights. May be we can show everywhere for now until any customer has concerns."}], "border": 0}]}]}, {"ts": "1729335498.560579", "text": "<!here> Please check this ticket and let me know your thoughts.\n<https://compiify.atlassian.net/browse/COM-3850>", "user": "U0690EB5JE5", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13911::a9fea3565a1f42fa8e8af6b48e05418c", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3850?atlOrigin=eyJpIjoiOTBjMzIyM2ZmNjBkNDFiMjgwNjczN2JjYzAxMTYxZWQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3850 Unable to View Department Statistics in Impersonating View>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13911::89478108db954d84bc5bbc07937bb363", "elements": [{"type": "mrkdwn", "text": "Status: *In Development*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/35dd492af25f9dfa74f8be283d9a0bba?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13911\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3850\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3850", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Qvm03", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Please check this ticket and let me know your thoughts.\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3850"}]}]}]}, {"ts": "1729327602.595369", "text": "<!here> while I was working on disabling the one time bonus column. I have made a small change. Please let me know if this is okay, I will push.\n• Removed tick and replaced with flag to be consistent\n• Removed Avatar with initials\n• I am also adding employee Id column for CWA  which hidden by default", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729327602.595369", "reply_count": 7, "edited": {"user": "U0690EB5JE5", "ts": "1729330071.000000"}, "files": [{"id": "F07SMAJHXSP", "created": 1729327597, "timestamp": 1729327597, "name": "Screenshot 2024-10-18 at 5.58.49 PM.png", "title": "Screenshot 2024-10-18 at 5.58.49 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 432433, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07SMAJHXSP/screenshot_2024-10-18_at_5.58.49___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07SMAJHXSP/download/screenshot_2024-10-18_at_5.58.49___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 190, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 254, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 380, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 423, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 507, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SMAJHXSP-58a8bc292e/screenshot_2024-10-18_at_5.58.49___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 541, "original_w": 3396, "original_h": 1794, "thumb_tiny": "AwAZADDSHT1pab2p1ACUtJS0AJnnpSbec5b8zTu9FADTTqSloASlpO9LQAd6KKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07SMAJHXSP/screenshot_2024-10-18_at_5.58.49___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07SMAJHXSP-e915a7d52c", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "eV1J7", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " while I was working on disabling the one time bonus column. I have made a small change. Please let me know if this is okay, I will push.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Removed tick and replaced with flag to be consistent"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Removed Avatar with initials"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "I am also adding employee Id column for CWA  which hidden by default"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1729326157.219549", "text": "example we hide budget stats for the manager by design once submitted. Jus that its consistent.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+aR3n", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "example we hide budget stats for the manager by design once submitted. Jus that its consistent."}]}]}]}, {"ts": "1729326124.551709", "text": "<@U07M6QKHUC9> Some of your tickets are by design. I think we need to discuss and come up with what is expected if you feel that confusing.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729326124.551709", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "9MtS1", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Some of your tickets are by design. I think we need to discuss and come up with what is expected if you feel that confusing."}]}]}]}, {"ts": "1729303040.036059", "text": "How many bugs for sdf you raised in last two days?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "UHYjk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "How many bugs for sdf you raised in last two days?"}]}]}]}, {"ts": "1729302726.643159", "text": "Some in Alayacare and some in SDF", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YbaaY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Some in Alayacare and some in SDF"}]}]}]}, {"ts": "1729302604.077539", "text": "<@U07EJ2LP44S> ma<PERSON>h is unable to find the jira tickets you raised. Are they in the SDF UT epic?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1wFQC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " ma<PERSON>h is unable to find the jira tickets you raised. Are they in the SDF UT epic?"}]}]}]}, {"ts": "1729302447.370429", "text": "Thanks, <PERSON>. I’m happy to take over the customer communication until this is done. feel free to forward or direct the customer communication to me if you need to", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ZIIhw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks, <PERSON>. I’m happy to take over the customer communication until this is done. feel free to forward or direct the customer communication to me if you need to"}]}]}]}, {"ts": "1729302066.390899", "text": "<@U07M6QKHUC9> is it possible to do the testing over the weekend? If this is expected to be fixed for SDF. Also the sooner the better to avoid regression and it will be more efficient for engineering to a avoid touching the same code again and again", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1729295356.945689", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "rgc3g", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " is it possible to do the testing over the weekend"}, {"type": "text", "text": "?"}, {"type": "text", "text": " If this is expected to be fixed for SDF. Also the sooner the better to avoid regression and it will be more efficient for engineering to a avoid touching the same code again and again"}]}]}]}, {"ts": "1729300794.922079", "text": "Will sync up with <@U04DKEFP1K8> ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VLjJK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will sync up with "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1729300755.605359", "text": "There is so much going on :smile: ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OtxHh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There is so much going on "}, {"type": "emoji", "name": "smile", "unicode": "1f604"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1729300302.978529", "text": "This is not going to be finished today. I can work on it this weekend. I uncovered a lot of bugs and have been documenting them all in Jira as I’ve been going so that they can get corrected as soon as possible. I will add detail to the QA sheet this weekend/Monday am before your day starts. Please remember I am also communicating with customers and doing my best to keep to our SLA there as well.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "G7k0Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is not going to be finished today. I can work on it this weekend. I uncovered a lot of bugs and have been documenting them all in Jira as I’ve been going so that they can get corrected as soon as possible. I will add detail to the QA sheet this weekend/Monday am before your day starts. Please remember I am also communicating with customers and doing my best to keep to our SLA there as well."}]}]}]}, {"ts": "1729295392.345239", "text": "<@U04DKEFP1K8> Please let me know after you have signed off on Alaya Care, so that I can move forward with my own testing for Alaya Care", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "BB3zI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Please let me know after you have signed off on Alaya Care, so that I can move forward with my own testing for Alaya Care"}]}]}]}, {"ts": "1729295356.945689", "text": "<@U07EJ2LP44S> Once you have signed off on your testing for Reporting, Cycle Insights and Paybands, I will block time to do my own  testing for  Reporting, Cycle Insights and Paybands on Monday. Please let me know as soon as you are done.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729295356.945689", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "m37yK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Once you have signed off on your testing for Reporting, Cycle Insights and Paybands, I will block time to do my own  testing for  Reporting, Cycle Insights and Paybands on Monday. Please let me know as soon as you are done."}]}]}]}, {"ts": "1729295232.575739", "text": "<@U0690EB5JE5> Few high priority items that needs to be fixed before the SDF demo on Monday. It does not include any potential issues with Reporting, Cycle Insights and Paybands, which I am counting on <PERSON> to do the QA on.\n1. Discrepency between eligible employees shown in cycle builder vs merit view\n2. Org view and Org insights data not populating when impersonating as <PERSON>\n3. Budget status bar not showing  in merit planner when impersonating as manager \n4. Budget status bar not showing  in merit planner when admin views submitted manager's team\n5. Fixing Performance Ratings Data\n6. Fixing Tenure Date\n<@U04DKEFP1K8> as discussed in standup today, can you pls confirm you are taking care of items 5 and 6.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729295232.575739", "reply_count": 1, "edited": {"user": "U07M6QKHUC9", "ts": "1729295441.000000"}, "blocks": [{"type": "rich_text", "block_id": "haqaZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Few high priority items that needs to be fixed before the SDF demo on Monday. It does not include any potential issues with Reporting, Cycle Insights and Paybands, which I am counting on <PERSON> to do the QA on.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Discrepency between eligible employees shown in cycle builder vs merit view"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>g view and <PERSON>g insights data not populating when impersonating as <PERSON>"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Budget status bar not showing  in merit planner when impersonating as manager "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Budget status bar not showing  in merit planner when admin views submitted manager's team"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Fixing Performance Ratings Data"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Fixing Tenure Date"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " as discussed in standup today, can you pls confirm you are taking care of items 5 and 6."}]}]}]}, {"ts": "1729294637.645849", "text": "<@U0690EB5JE5> pls add me to sunday night standup so we can go over the SDF issues and at least fix the most obvious data inconsistencies by Monday", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9N7W7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " pls add me to sunday night standup so we can go over the SDF issues and at least fix the most obvious data inconsistencies by Monday"}]}]}]}, {"ts": "1729293529.168119", "text": "This exercise has the potential to minimize the quality and credibility issues we have been having for the last 10 months, if we do it right and pay attention to details.", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1729293558.000000"}, "blocks": [{"type": "rich_text", "block_id": "aIs0w", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This exercise has the potential to minimize the quality and credibility issues we have been having for the last 10 months, if we do it right and pay attention to details."}]}]}]}, {"ts": "1729293443.492599", "text": "<!here> Goal for Monday's standup:\n<PERSON> to go over her documentation for QA check list for org view, pay bands, people insights and reports\n<PERSON><PERSON><PERSON> to go over his documentation for QA checklist for Merit Planner\n\nAgain, the purpose of this QA checklist is to document specific and detailed  testing scenarios for everything that is visible in the product without leaving any scope for ambiguity and lack of clarity.\n\nLet's stay committed to what was agreed upon in today's standup and focus on high quality deliverables please.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nsxW5", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Goal for Monday's standup:\n<PERSON> to go over her documentation for QA check list for org view, pay bands, people insights and reports\n<PERSON><PERSON><PERSON> to go over his documentation for QA checklist for Merit Planner\n\nAgain, the purpose of this QA checklist is to document specific and detailed  testing scenarios for everything that is visible in the product without leaving any scope for ambiguity and lack of clarity.\n\nLet's stay committed to what was agreed upon in today's standup and focus on high quality deliverables please."}]}]}]}, {"ts": "1729293128.982459", "text": "<@U07EJ2LP44S>  I am still seeing the old ratings:unamused:", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729280953.690689", "subtype": "thread_broadcast", "files": [{"id": "F07SLGDD44T", "created": 1729293119, "timestamp": 1729293119, "name": "Screenshot 2024-10-18 at 4.11.56 PM.png", "title": "Screenshot 2024-10-18 at 4.11.56 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 260728, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07SLGDD44T/screenshot_2024-10-18_at_4.11.56___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07SLGDD44T/download/screenshot_2024-10-18_at_4.11.56___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SLGDD44T-d3d0f3cbaa/screenshot_2024-10-18_at_4.11.56___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SLGDD44T-d3d0f3cbaa/screenshot_2024-10-18_at_4.11.56___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SLGDD44T-d3d0f3cbaa/screenshot_2024-10-18_at_4.11.56___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 212, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SLGDD44T-d3d0f3cbaa/screenshot_2024-10-18_at_4.11.56___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 283, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SLGDD44T-d3d0f3cbaa/screenshot_2024-10-18_at_4.11.56___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SLGDD44T-d3d0f3cbaa/screenshot_2024-10-18_at_4.11.56___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 425, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SLGDD44T-d3d0f3cbaa/screenshot_2024-10-18_at_4.11.56___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 472, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SLGDD44T-d3d0f3cbaa/screenshot_2024-10-18_at_4.11.56___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 566, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07SLGDD44T-d3d0f3cbaa/screenshot_2024-10-18_at_4.11.56___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 604, "original_w": 2448, "original_h": 1444, "thumb_tiny": "AwAcADDSak/KlNFAAPrS0n4ilyKACiiigBDRS0UAJRRRQAUopKWgD//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07SLGDD44T/screenshot_2024-10-18_at_4.11.56___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07SLGDD44T-f2035a6efa", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "uUvTn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "  I am still seeing the old ratings"}, {"type": "emoji", "name": "unamused", "unicode": "1f612"}]}]}]}, {"ts": "1729286072.710979", "text": "I have to log off; daughter got the lead role in her school play, we're going for a celebratory dinner!", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "tada", "users": ["U04DKEFP1K8", "U07NBMXTL1E"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "aN5zs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have to log off; daughter got the lead role in her school play, we're going for a celebratory dinner!"}]}]}]}, {"ts": "1729285941.919599", "text": "<@U0690EB5JE5> AlayaCare just requested this. So pls prioritize it.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729186715.829929", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "lB491", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " AlayaCare just requested this. So pls prioritize it."}]}]}]}, {"ts": "1729285740.286109", "text": "<@U04DKEFP1K8> Cheryl noticed the HRBP thing - she can't assign them anymore. Is it this? <https://compiify.atlassian.net/issues/COM-3833?jql=text%20~%20%22hrbp%2A%22>", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MtKte", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " <PERSON> noticed the HRBP thing - she can't assign them anymore. Is it this? "}, {"type": "link", "url": "https://compiify.atlassian.net/issues/COM-3833?jql=text%20~%20%22hrbp%2A%22"}]}]}]}, {"ts": "1729284322.552489", "text": "<@U0690EB5JE5> FYI, all of the issues I experienced while impersonating <PERSON> occur even when I logged in as <PERSON> using her local login credentials. This has to be fixed as l<PERSON> requested login for her managers as well and they will be doing the testing.\n\nAs of now, we can not deliver the env to them :unamused:", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729284322.552489", "reply_count": 20, "blocks": [{"type": "rich_text", "block_id": "BU67U", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " FYI, all of the issues I experienced while impersonating <PERSON> occur even when I logged in as <PERSON> using her local login credentials. This has to be fixed as lisa requested login for her managers as well and they will be doing the testing.\n\nAs of now, we can not deliver the env to them "}, {"type": "emoji", "name": "unamused", "unicode": "1f612"}]}]}]}, {"ts": "1729283555.225189", "text": "<@U04DKEFP1K8> can you give me the local login credentials for <PERSON> so I can test her experience", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1729283555.225189", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "r/xpX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " can you give me the local login credentials for <PERSON> so I can test her experience"}]}]}]}, {"ts": "1729283131.908889", "text": "Hang on just recorded a demo", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FcA1A", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hang on just recorded a demo"}]}]}]}, {"ts": "1729282869.527809", "text": "<@U04DKEFP1K8> <@U07EJ2LP44S> I am not seeing the option to push loom to Jira. How do I do it?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vsLDU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am not seeing the option to push loom to Jira. How do I do it?"}]}]}]}, {"ts": "1729282833.320079", "text": "Pls capture those bugs in the QA Document as we discussed in the morning just in case they are not added there already", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WPmgL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Pls capture those bugs in the QA Document as we discussed in the morning just in case they are not added there already"}]}]}]}, {"ts": "1729280953.690689", "text": "I'm not sure what other data fixes there are, but I did remove the n/a employees and replace them with a different rating", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1729280953.690689", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "8ScKv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'm not sure what other data fixes there are, but I did remove the n/a employees and replace them with a different rating"}]}]}]}, {"ts": "1729280930.301479", "text": "I have submitted several bugs on the data side", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3U8dx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have submitted several bugs on the data side"}]}]}]}, {"ts": "1729280893.756749", "text": "Also, please, let’s make sure that the data is fixed before we hand over the environment", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "bvIOc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also, please, let’s make sure that the data is fixed before we hand over the environment"}]}]}]}], "created_at": "2025-05-22T21:35:34.645166"}