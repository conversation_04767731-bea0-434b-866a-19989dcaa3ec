{"channel_id": "C065QSSNH8A", "channel_name": "1-productengineering", "month": "2024-12", "message_count": 132, "messages": [{"ts": "1733153665.156469", "text": "<!here> can we please move our meeting to 8:30 AM I need to drop off my daughter to school", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733153665.156469", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "vztGB", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " can we please move our meeting to 8:30 AM I need to drop off my daughter to school"}]}]}]}, {"ts": "1733159203.672949", "text": "<https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "M6D04", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3"}]}]}]}, {"ts": "1733159674.355589", "text": "<@U07EJ2LP44S> please play around with Cura<PERSON> cycle and let me know potential blockers for your meeting.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MwUMh", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " please play around with <PERSON><PERSON><PERSON> cycle and let me know potential blockers for your meeting."}]}]}]}, {"ts": "1733161292.385309", "text": "Bug (maybe) in Curana's comp builder: <https://compiify.atlassian.net/browse/COM-4010>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733161292.385309", "reply_count": 7, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14093::fffb7cada4064b9b9a8f9a2b3c292806", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4010?atlOrigin=eyJpIjoiOTNkYjg3M2RhMWE3NDcxZWE1MTJhOWJkMzBhNWMzOWQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4010 Missing Performance Option in Recommendation Guidelines for Curana>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14093::35c09d0d7a514483adc5bc1ffaee5a01", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14093\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4010\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4010", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Qbito", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bug (maybe) in <PERSON><PERSON><PERSON>'s comp builder: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4010"}]}]}]}, {"ts": "1733239303.035249", "text": "<@U0690EB5JE5> here is first draft of the Column Configurator PRD for your review.\n<https://docs.google.com/document/d/1WxJlkkQtKbaRTpH0V70L8dgc2BpCB-68ctUh4N96swI/edit?usp=sharing>", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}, {"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "files": [{"id": "F083FGL28CB", "created": 1733239307, "timestamp": 1733239307, "name": "Field Configurator", "title": "Column/Field Configurator", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 160313, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1WxJlkkQtKbaRTpH0V70L8dgc2BpCB-68ctUh4N96swI", "external_url": "https://docs.google.com/document/d/1WxJlkkQtKbaRTpH0V70L8dgc2BpCB-68ctUh4N96swI/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1WxJlkkQtKbaRTpH0V70L8dgc2BpCB-68ctUh4N96swI/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F083FGL28CB-415a86623b/field_configurator_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F083FGL28CB-415a86623b/field_configurator_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F083FGL28CB-415a86623b/field_configurator_360.png", "thumb_360_w": 278, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F083FGL28CB-415a86623b/field_configurator_480.png", "thumb_480_w": 371, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F083FGL28CB-415a86623b/field_configurator_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F083FGL28CB-415a86623b/field_configurator_720.png", "thumb_720_w": 557, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F083FGL28CB-415a86623b/field_configurator_800.png", "thumb_800_w": 800, "thumb_800_h": 1035, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F083FGL28CB-415a86623b/field_configurator_960.png", "thumb_960_w": 742, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F083FGL28CB-415a86623b/field_configurator_1024.png", "thumb_1024_w": 791, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1035, "thumb_tiny": "AwAwACXSJxQG9aG6e9IM560ALuFLmkwfWjBoAWiiigBG6U3I9KeelRjOeKAFyPQfnSjnsKTn1oGaAH0UUUAI3Sm8+q08jNIFFACD8DTsewowPSigAooooA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F083FGL28CB/field_configurator", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Ycq5B", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " here is first draft of the Column Configurator PRD for your review.\n"}, {"type": "link", "url": "https://docs.google.com/document/d/1WxJlkkQtKbaRTpH0V70L8dgc2BpCB-68ctUh4N96swI/edit?usp=sharing"}]}]}]}, {"ts": "1733248808.000819", "text": "<!here> Wednesday agenda:\ngo over <PERSON>'s and <PERSON>'s doc for UI improvmeents", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PCt5S", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Wednesday agenda:\ngo over <PERSON>'s and <PERSON>'s doc for UI improvmeents"}]}]}]}, {"ts": "1733249004.413709", "text": "<@U0690EB5JE5> AlayaCare raised this issue again:\n\na prior issue has re-appeared for ees that are eligible for promo. it's gone back to showing their new salary and compa ratio based on their current band, and not that of their new role, examples are <PERSON><PERSON><PERSON> and <PERSON><PERSON>.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733249004.413709", "reply_count": 13, "blocks": [{"type": "rich_text", "block_id": "7aQwv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " AlayaCare raised this issue again:\n\na prior issue has re-appeared for ees that are eligible for promo. it's gone back to showing their new salary and compa ratio based on their current band, and not that of their new role, examples are <PERSON><PERSON><PERSON> and <PERSON><PERSON>."}]}]}]}, {"ts": "1733256530.021609", "text": "<@U0690EB5JE5> here is the rewards template for Tithely. let's discuss what we can or can not do today, and what do you need to show the total rewards", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733256530.021609", "reply_count": 1, "files": [{"id": "F083KSUCW6Q", "created": 1733256498, "timestamp": 1733256498, "name": "<PERSON><PERSON><PERSON> - 3.2024.pdf", "title": "<PERSON><PERSON><PERSON> - 3.2024.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 105838, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F083KSUCW6Q/kristopher_parker_-_3.2024.pdf", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F083KSUCW6Q/download/kristopher_parker_-_3.2024.pdf", "media_display_type": "unknown", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F083KSUCW6Q-31849a2b2d/kristopher_parker_-_3.2024_thumb_pdf.png", "thumb_pdf_w": 935, "thumb_pdf_h": 1210, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F083KSUCW6Q/kristopher_parker_-_3.2024.pdf", "permalink_public": "https://slack-files.com/T04DM97F1UM-F083KSUCW6Q-d4668c4fce", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "i/AaM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " here is the rewards template for <PERSON>ith<PERSON>. let's discuss what we can or can not do today, and what do you need to show the total rewards"}]}]}]}, {"ts": "**********.517799", "text": "<@U0690EB5JE5> Degenkolb bug 2: table load error. Same error showing in <PERSON><PERSON><PERSON>'s account, and likely the reason behind the issue of viewing the wrong team data in the account,. <https://compiify.atlassian.net/browse/COM-4013>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.517799", "reply_count": 11, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14096::898f12f66f0d47cc87e72f89a4667b07", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4013?atlOrigin=eyJpIjoiZTA3NTc1ZTJkZDAzNGNhMzgyNjY3MTQ2ZjZiYWNhNWIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4013 Bug Report: Table Load Error: Incorrect Team Data Displayed in Meri…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14096::2036e82433594f629c61685e8cd4af78", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14096\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4013\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4013", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "BK6p4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Degenkolb bug 2: table load error. Same error showing in <PERSON><PERSON><PERSON>'s account, and likely the reason behind the issue of viewing the wrong team data in the account,. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4013"}]}]}]}, {"ts": "**********.813939", "text": "Degenkolb bug 3: Merit Cycle Changes report not working (I'm guessing b/c we tried to push the new report?) They get an error when they attempt to download it. <https://compiify.atlassian.net/browse/COM-4014>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.813939", "reply_count": 3, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14097::4189d4401eb84882afa6a846586a35d0", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4014?atlOrigin=eyJpIjoiMGViOTViM2U2Y2M0NDgxMWE4YmYyMWE0Y2ZmYjkwZjkiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4014 Issue: Merit Cycle Changes Report Not Working for Degenkolb's Accou…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14097::6f5011f27ad548a380205a71adf75c70", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14097\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4014\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4014", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "C1Riq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Degenkolb bug 3: Merit Cycle Changes report not working (I'm guessing b/c we tried to push the new report?) They get an error when they attempt to download it. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4014"}]}]}]}, {"ts": "1733260829.506769", "text": "First draft of the doc I've been workign on: <https://docs.google.com/document/d/1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk/edit?usp=sharing>", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F083HN97AG2", "created": 1733260834, "timestamp": 1733260834, "name": "UX Product Recommendations ", "title": "UI/UX Product Recommendations", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 136275, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk", "external_url": "https://docs.google.com/document/d/1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTRbr1IFH/AzQ34/hRt9zQAo4/iz9aXI9aTHuaXFABRRRQA043c/wAqUY60jde9H50AOzRmkH0P5UfhQAtFFFADWxml20jdaAPp+dAC7RQAB0o49qXj2oAKKKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F083HN97AG2/ux_product_recommendations_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "4DeJ6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "First draft of the doc I've been workign on: "}, {"type": "link", "url": "https://docs.google.com/document/d/1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk/edit?usp=sharing"}]}]}]}, {"ts": "1733261751.843659", "text": "We can discuss if needed in tomorrows call, but here's the info for Curana's bonus:\n\n• Only certain people eligible for bonus plan (Management Incentive Plan)\n• Budget is accrued for full payout (1.2mm as an example)\n• Target earned is across the company (eg, company achieves 95%, everyone is eligible for 95% of their dollars.\n• Allocation of bonus is at manager discretion; there is no official performance component. However a manger may take away from one low performing employee to give to a higher performer. The leadership team (CSuite) have discretion if there is additional budget accrued but not spent: eg: if company achieves 95% but budget is accrued for 100%, CSuite may decide to give the additional 5% to high performers, OR to employees who are not on the bonus plan, as a spot bonus. \n• Can overperform (could reach 110%), not capped at 100%. But there is a $ budget amount for the company. ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733261751.843659", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "fF2qg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can discuss if needed in tomorrows call, but here's the info for <PERSON><PERSON><PERSON>'s bonus:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Only certain people eligible for bonus plan (Management Incentive Plan)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Budget is accrued for full payout (1.2mm as an example)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Target earned is across the company (eg, company achieves 95%, everyone is eligible for 95% of their dollars."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Allocation of bonus is at manager discretion; there is no official performance component. However a manger may take away from one low performing employee to give to a higher performer. The leadership team (CSuite) have discretion if there is additional budget accrued but not spent: eg: if company achieves 95% but budget is accrued for 100%, CSuite may decide to give the additional 5% to high performers, OR to employees who are not on the bonus plan, as a spot bonus. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Can overperform (could reach 110%), not capped at 100%. But there is a $ budget amount for the company. "}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1733263534.226799", "text": "<@U0690EB5JE5> I cannot get an upload of Curana's bonus amounts to take. It says Internal Error, reason being Column 1, field name. I've tried it three different ways (including how it downloaded) but it keeps giving an error. Can you try?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733263534.226799", "reply_count": 7, "files": [{"id": "F0846NM9LQY", "created": 1733263531, "timestamp": 1733263531, "name": "CuranaPercents.csv", "title": "CuranaPercents.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 10573, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F0846NM9LQY/curanapercents.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F0846NM9LQY/download/curanapercents.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0846NM9LQY/curanapercents.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F0846NM9LQY-3727b1b430", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F0846NM9LQY/curanapercents.csv/edit", "preview": "Employee Id,Employee Name (Read Only),Annual Salary Currency,Annual Salary (Currency),Annual Salary-Other (Currency),Variable Pay Currency,Variable Pay (%),Variable Pay (Currency),Target Bonus Currency,Target Bonus (%),Target Bonus (%),Last Raise Date,Previous Year Salary,Annual Salary OTE (Currency),Pay Mix,Hourly Rate,\"Update Type (NC,A,D,U)\"\r\n8093,TARIQ SYED,USD,141000.08,0,USD,0,0,USD,10,,45374,0,0,0,0,U\r\n8038,VICTOR SONE,USD,190289.93,0,USD,0,0,USD,20,,45374,0,0,0,0,U\r\n8032,SHARRA BLACKWELL,USD,100891.18,0,USD,0,0,USD,10,,45374,0,0,0,0,U\r\n8024,SHANICE HAIRSTON,USD,45302.4,0,USD,0,0,USD,8,,45374,0,0,0,21.78,U\r\n4412,STEPHANIE ST THOMAS,USD,250000,0,USD,0,0,USD,20,,,0,0,0,0,U\r\n4167,DEVON HOERNSCHEMEYER,USD,95000,0,USD,0,0,USD,10,,,0,0,0,0,U\r\n4142,LINDA BROCK,USD,185000,0,USD,0,0,USD,15,,,0,0,0,0,U\r\n3917,DANIEL BALK,USD,175000,0,USD,0,0,USD,25,,,0,0,0,0,U\r\n3673,MARJORIE WEINER,USD,185000,0,USD,0,0,USD,20,,,0,0,0,0,U\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Employee Id</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary (Currency)</div><div class=\"cm-col\">Annual Salary-Other (Currency)</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay (Currency)</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE (Currency)</div><div class=\"cm-col\">Pay Mix</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Update Type (NC,A,D,U)</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">8093</div><div class=\"cm-col\">TARIQ SYED</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">141000.08</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">45374</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">8038</div><div class=\"cm-col\">VICTOR SONE</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">190289.93</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">20</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">45374</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">8032</div><div class=\"cm-col\">SHARRA BLACKWELL</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">100891.18</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">45374</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">8024</div><div class=\"cm-col\">SHANICE HAIRSTON</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">45302.4</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">45374</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">21.78</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">4412</div><div class=\"cm-col\">STEPHANIE ST THOMAS</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">250000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">20</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">4167</div><div class=\"cm-col\">DEVON HOERNSCHEMEYER</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">95000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">4142</div><div class=\"cm-col\">LINDA BROCK</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">185000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">3917</div><div class=\"cm-col\">DANIEL BALK</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">175000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">25</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">3673</div><div class=\"cm-col\">MARJORIE WEINER</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">185000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">20</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div></div>\n</div>\n", "lines": 158, "lines_more": 148, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "7G/CM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I cannot get an upload of <PERSON><PERSON><PERSON>'s bonus amounts to take. It says Internal Error, reason being Column 1, field name. I've tried it three different ways (including how it downloaded) but it keeps giving an error. Can you try?"}]}]}]}, {"ts": "1733277319.912079", "text": "Will into all the issues today ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1zMoA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will into all the issues today "}]}]}]}, {"ts": "1733309484.829279", "text": "<@U07EJ2LP44S> Unfortunately the upload improvements are not yet available on Curana. you still have to upload full file. only div energy has those improvements as the bonus changes were done on top of those improvements code. Will be pushing the upload improvements to all ENVs by end of this week.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733263534.226799", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "M4V12", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Unfortunately the upload improvements are not yet available on Curana. you still have to upload full file. only div energy has those improvements as the bonus changes were done on top of those improvements code. Will be pushing the upload improvements to all ENVs by end of this week."}]}]}]}, {"ts": "1733309781.110229", "text": "<@U07EJ2LP44S> This issue happens when we publish cycle and this action overwrites new compensation band. We need to upload promotions again and should fix the data. I have fixed cycle publish code to not overwrite the new comp band.\n\nFor DGOC. I did manaully re-assigned the new title to fix the issue for case where the bands were overwritten. But I found couple of titles as missing in bands now. I need to clarify with you.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733249004.413709", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1733310349.000000"}, "blocks": [{"type": "rich_text", "block_id": "fUUln", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This issue happens when we publish cycle and this action overwrites new compensation band. We need to upload promotions again and should fix the data. I have fixed cycle publish code to not overwrite the new comp band.\n\nFor DGOC. I did manaully re-assigned the new title to fix the issue for case where the bands were overwritten. But I found couple of titles as missing in bands now. I need to clarify with you."}]}]}]}, {"ts": "1733337807.903289", "text": "General question <@U0690EB5JE5>: can we still do promotion uploads in the cycle builder if the customer does not have pay bands? If so, can we leave the Band ID blank?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733337807.903289", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "General question "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": ": can we still do promotion uploads in the cycle builder if the customer does not have pay bands? If so, can we leave the Band ID blank?"}]}]}]}, {"ts": "1733342217.835689", "text": "Bug in Alayacare - for compa ratio in the merit view, the system is taking the canadian salary amount (for US employees) and bumping it against the US band, so the compa ratio is incorrect. <https://compiify.atlassian.net/browse/COM-4016>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733342217.835689", "reply_count": 5, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14099::c70fc98cdc8a4b85a2d1b1c8826b61f8", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4016?atlOrigin=eyJpIjoiMDdjNTYwYTZjOTQxNDg1YWFjYzllZjQ1NmQxM2FiZmYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4016 Issue: Incorrect Currency Conversion Calculation for U.S. Employees…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14099::ab19c9b8c2804248847ebd92cfef4be5", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14099\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4016\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4016", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "n45Mc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bug in Alayacare - for compa ratio in the merit view, the system is taking the canadian salary amount (for US employees) and bumping it against the US band, so the compa ratio is incorrect. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4016"}]}]}]}, {"ts": "1733343938.091349", "text": "<@U0690EB5JE5> looking for some feedback on how to ingest the data we have for curana re: paybands for hourly employees. I need to confirm with <PERSON><PERSON>na how they want to handle but wanted your technical viewpoint first. <https://www.loom.com/share/e650daaf9daf4d29ba5de6ce2682b84e?sid=b2b92059-062f-4a99-9be0-fc4ec9b56560>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733343938.091349", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "KPVA0", "video_url": "https://www.loom.com/embed/e650daaf9daf4d29ba5de6ce2682b84e?sid=b2b92059-062f-4a99-9be0-fc4ec9b56560&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/e650daaf9daf4d29ba5de6ce2682b84e-f3d3fe3a771a74bd-4x3.jpg", "alt_text": "Understanding Quirana's Pay Bands", "title": {"type": "plain_text", "text": "Understanding Quirana's Pay Bands", "emoji": true}, "title_url": "https://www.loom.com/share/e650daaf9daf4d29ba5de6ce2682b84e", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 2 min  ", "emoji": true}}, {"type": "section", "block_id": "Jyddg", "text": {"type": "mrkdwn", "text": ":information_source: In this video, I discuss Quirana's pay bands, specifically focusing on the hourly and annual compensation data provided. I highlight how hourly...", "verbatim": false}}, {"type": "actions", "block_id": "6lM+q", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/e650daaf9daf4d29ba5de6ce2682b84e?sid=b2b92059-062f-4a99-9be0-fc4ec9b56560"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"e650daaf9daf4d29ba5de6ce2682b84e\",\"videoName\":\"Understanding Quirana's Pay Bands\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/e650daaf9daf4d29ba5de6ce2682b84e?sid=b2b92059-062f-4a99-9be0-fc4ec9b56560", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "92Dnp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " looking for some feedback on how to ingest the data we have for curana re: paybands for hourly employees. I need to confirm with <PERSON><PERSON><PERSON> how they want to handle but wanted your technical viewpoint first. "}, {"type": "link", "url": "https://www.loom.com/share/e650daaf9daf4d29ba5de6ce2682b84e?sid=b2b92059-062f-4a99-9be0-fc4ec9b56560"}]}]}]}, {"ts": "1733411180.688089", "text": "<@U0690EB5JE5> Will you look at this? I will put a bug in, but is there a step I'm missing along the way? I think everything should be working. <https://www.loom.com/share/fb147f06d1d546b78de8472535651494?sid=038ea0fb-9e17-4e20-b751-2457810f8ca7>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733411180.688089", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "4Mhhk", "video_url": "https://www.loom.com/embed/fb147f06d1d546b78de8472535651494?sid=038ea0fb-9e17-4e20-b751-2457810f8ca7&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/fb147f06d1d546b78de8472535651494-928dd9e9ad15f99b-4x3.jpg", "alt_text": "Troubleshooting Data Discrepancies 👩💻", "title": {"type": "plain_text", "text": "Troubleshooting Data Discrepancies 👩‍💻", "emoji": true}, "title_url": "https://www.loom.com/share/fb147f06d1d546b78de8472535651494", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 2 min  ", "emoji": true}}, {"type": "section", "block_id": "NRtpz", "text": {"type": "mrkdwn", "text": ":information_source: Hey there! I encountered an issue while impersonating <PERSON><PERSON><PERSON> in Diversified Energy. The data for employee count was missing initially, but...", "verbatim": false}}, {"type": "actions", "block_id": "nRBPZ", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/fb147f06d1d546b78de8472535651494?sid=038ea0fb-9e17-4e20-b751-2457810f8ca7"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"fb147f06d1d546b78de8472535651494\",\"videoName\":\"Troubleshooting Data Discrepancies 👩💻\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/fb147f06d1d546b78de8472535651494?sid=038ea0fb-9e17-4e20-b751-2457810f8ca7", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "OW1Tt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Will you look at this? I will put a bug in, but is there a step I'm missing along the way? I think everything should be working. "}, {"type": "link", "url": "https://www.loom.com/share/fb147f06d1d546b78de8472535651494?sid=038ea0fb-9e17-4e20-b751-2457810f8ca7"}]}]}]}, {"ts": "1733411336.131399", "text": "Will take a look. Could be data issue", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "4kvhd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will take a look. Could be data issue"}]}]}]}, {"ts": "1733425974.694719", "text": "<@U07EJ2LP44S> you did an awesome job with Tithely. Glad I asked you to do it :slightly_smiling_face:\n\n <@U0690EB5JE5> It looks like audit log bug appeared again. changes are not being updated correctly in the audit log in Tithely env.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733425974.694719", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "GhT2M", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " you did an awesome job with <PERSON><PERSON><PERSON>. Glad I asked you to do it "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": "\n\n "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " It looks like audit log bug appeared again. changes are not being updated correctly in the audit log in Tithely env."}]}]}]}, {"ts": "1733426040.907889", "text": "Thanks :slightly_smiling_face: Also I got good intel from Valgensis about both letters and pay bands", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "tada", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "W6X0D", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " Also I got good intel from Valgensis about both letters and pay bands"}]}]}]}, {"ts": "1733426116.327719", "text": "<@U07EJ2LP44S> Tithely don't have any employees in \"CANADA\" so it looks like a bug as well. Please create tickets for all of the bugs we saw today", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "aFn1x", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Tithely don't have any employees in \"CANADA\" so it looks like a bug as well. Please create tickets for all of the bugs we saw today"}]}]}]}, {"ts": "1733430038.014629", "text": "<@U0690EB5JE5> We have a second customer that needs a demo training enviornmnet. We have <http://stridedemo.stridehr.io|stridedemo.stridehr.io> but we'll need a second instance for Tithely, as Valgenesis is using that one and we'll need different cycle settings for Tithley. Can we duplicate the environment? Maybe call it <http://tithelydemo.stridehr.io|tithelydemo.stridehr.io>?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733430038.014629", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "KF1os", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " We have a second customer that needs a demo training enviornmnet. We have "}, {"type": "link", "url": "http://stridedemo.stridehr.io", "text": "stridedemo.stridehr.io"}, {"type": "text", "text": " but we'll need a second instance for <PERSON>ith<PERSON>, as Valgenesis is using that one and we'll need different cycle settings for Tithley. Can we duplicate the environment? Maybe call it "}, {"type": "link", "url": "http://tithelydemo.stridehr.io", "text": "tithelydemo.stridehr.io"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1733430169.433909", "text": "<@U0690EB5JE5> can you delete the active cycle in stridedemo for valgenesis? <https://compiify.atlassian.net/browse/COM-4018>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14101::a8977a77b7da46e59d40bd715341342a", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4018?atlOrigin=eyJpIjoiYTUzYmEwOTgwMzBmNDkyMjgwOGY5NjdmNjkzNTg0NDQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4018 Deleting Annual Cycle 2024 in Stride Demo>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14101::16a432e86e4346c28086fdd65fbb3a8a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14101\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4018\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4018", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "hzcu4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you delete the active cycle in stridedemo for valgenesis? "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4018"}]}]}]}, {"ts": "**********.877479", "text": "Tithely data issue with Canada listed twice: <https://compiify.atlassian.net/browse/COM-4019>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.877479", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14102::7e29b5bd27664cde9978a3e5e3ac0b66", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4019?atlOrigin=eyJpIjoiMjIwYTRmYzZmNjFmNDMyNWIxZTY1Y2EyNWM0MWRhZjYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4019 Issue: Remove Duplicate Country Entries in Tithely's Account>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14102::a0357cdabb0a4417bab88138e021c7e3", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14102\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4019\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4019", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "XeplU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Tithely data issue with Canada listed twice: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4019"}]}]}]}, {"ts": "1733432415.148929", "text": "We just had a great call with <PERSON>uster 300 emp. There is 75% chance they will convert. Let's keep the fingers crossed :partying_face:", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "p<PERSON>tsy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We just had a great call with <PERSON>uster 300 emp. There is 75% chance they will convert. Let's keep the fingers crossed "}, {"type": "emoji", "name": "partying_face", "unicode": "1f973"}]}]}]}, {"ts": "1733437751.803919", "text": "<@U07M6QKHUC9> I need to send SAML instructions to Diversified. Is there a different document now? I checked the drive folder but it is currently empty. I can dig up the old doc if there isn't a newer version.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733437751.803919", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "2I8gv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " I need to send SAML instructions to Diversified. Is there a different document now? I checked the drive folder but it is currently empty. I can dig up the old doc if there isn't a newer version."}]}]}]}, {"ts": "1733437920.630109", "text": "Diversified had a TON of feedback on the overall usability of the bonus page, and I do agree with all her feedback. May be easiest to go through it live on the call together tomorrow.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "B9Vtk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified had a TON of feedback on the overall usability of the bonus page, and I do agree with all her feedback. May be easiest to go through it live on the call together tomorrow."}]}]}]}, {"ts": "1733469502.370419", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> Fixed the data. This is issue with new upload improvements changes which we used to upload the data in div energy. We will fix it.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733411180.688089", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "4pxFJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Fixed the data. This is issue with new upload improvements changes which we used to upload the data in div energy. We will fix it."}]}]}]}, {"ts": "1733469541.014609", "text": "Removed duplicate entry. Long term fix is being worked on, That duplicate entry was through bands upload.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.877479", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "+RDPq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Removed duplicate entry. Long term fix is being worked on, That duplicate entry was through bands upload."}]}]}]}, {"ts": "1733477928.704629", "text": "<!here> I caught a stomach flu tonight and might not be able to join the standup in the morning.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733477928.704629", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "By+8w", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I caught a stomach flu tonight and might not be able to join the standup in the morning."}]}]}]}, {"ts": "1733493495.996899", "text": "<@U07EJ2LP44S> I don’t have anything specific to discuss. Let me know if you are fine to skip stand up. ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733493495.996899", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "+NZQd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I "}, {"type": "text", "text": "don’t"}, {"type": "text", "text": " have anything specific to discuss. Let me know if you are fine to skip stand up. "}]}]}]}, {"ts": "1733498510.956169", "text": "Diversified Feedback: <https://www.loom.com/share/cd8ca41eefea4a099aa62ed3ca73dd42?sid=f03cd08e-b82f-452d-8c34-79c035359f1f>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733498510.956169", "reply_count": 4, "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "tiNWE", "video_url": "https://www.loom.com/embed/cd8ca41eefea4a099aa62ed3ca73dd42?sid=f03cd08e-b82f-452d-8c34-79c035359f1f&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/cd8ca41eefea4a099aa62ed3ca73dd42-6e1e801b70bd38d6-4x3.jpg", "alt_text": "Improving Data Visualization 📊", "title": {"type": "plain_text", "text": "Improving Data Visualization 📊", "emoji": true}, "title_url": "https://www.loom.com/share/cd8ca41eefea4a099aa62ed3ca73dd42", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 5 min  ", "emoji": true}}, {"type": "section", "block_id": "Fg/4D", "text": {"type": "mrkdwn", "text": ":information_source: I had a call with <PERSON> from Diversified who's struggling with understanding complex numbers. The solution involves hiding columns and changing...", "verbatim": false}}, {"type": "actions", "block_id": "p+SBG", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/cd8ca41eefea4a099aa62ed3ca73dd42?sid=f03cd08e-b82f-452d-8c34-79c035359f1f"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"cd8ca41eefea4a099aa62ed3ca73dd42\",\"videoName\":\"Improving Data Visualization 📊\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/cd8ca41eefea4a099aa62ed3ca73dd42?sid=f03cd08e-b82f-452d-8c34-79c035359f1f", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "cEeKv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diversified Feedback: "}, {"type": "link", "url": "https://www.loom.com/share/cd8ca41eefea4a099aa62ed3ca73dd42?sid=f03cd08e-b82f-452d-8c34-79c035359f1f"}]}]}]}, {"ts": "1733510340.808619", "text": "moving it here", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733425974.694719", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "TueZ+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "moving it here"}]}]}]}, {"ts": "1733511957.374279", "text": "That's super weird, I don't see it either. I swear I did it yesterday. Maybe my internet stalled out when I was creating or something. It's here now: <https://compiify.atlassian.net/browse/COM-4020>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733511957.374279", "reply_count": 1, "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14103::ba7b896af64e4098a2c10f6da0abfc31", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4020?atlOrigin=eyJpIjoiOTAxNjg2N2Y4ZDM5NDVlN2FkNjk0N2QzZDQ4YzE4ZWUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4020 Bug in Audit History on Tithelys Environment>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14103::feacee7d1d0944e2a53f935a51a27da0", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14103\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4020\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4020", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "qhtTj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That's super weird, I don't see it either. I swear I did it yesterday. Maybe my internet stalled out when I was creating or something. It's here now: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4020"}]}]}]}, {"ts": "1733512469.305469", "text": "<@U0690EB5JE5> I am not sure how we ended up with two epics for Tithely. Is there a way to merge those two epics to avoid any confusion?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733512469.305469", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "IoWgv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am not sure how we ended up with two epics for Tith<PERSON>. Is there a way to merge those two epics to avoid any confusion?"}]}]}]}, {"ts": "1733512586.653549", "text": "<@U07EJ2LP44S> there was also a flag issue with flags not updating after out of guidelines recommendation. It worked for one employee and did not work for another? Are you able to replicate that issue for Tithely or are we good there?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "sGckO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " there was also a flag issue with flags not updating after out of guidelines recommendation. It worked for one employee and did not work for another? Are you able to replicate that issue for Tith<PERSON> or are we good there?"}]}]}]}, {"ts": "1733512606.933679", "text": "Let me test it again and see if I can replicate", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "rlEsi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me test it again and see if I can replicate"}]}]}]}, {"ts": "1733512684.513389", "text": "Its' working now so I think it was a page load timing thing", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "IhJ8A", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Its' working now so I think it was a page load timing thing"}]}]}]}, {"ts": "1733512695.911009", "text": "its taking a second to populate the flag type", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "02UcN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "its taking a second to populate the flag type"}]}]}]}, {"ts": "1733512697.669579", "text": "<@U0690EB5JE5> we had an option to set guidelines by both performance ratings and compa ratio. I am not sure why we removed that option from the cycle builder. It was a functional feature. Can we pls put it back and <PERSON><PERSON><PERSON> prefers to use both ratings and compa ration", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733512697.669579", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "s3clX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we had an option to set guidelines by both performance ratings and compa ratio. I am not sure why we removed that option from the cycle builder. It was a functional feature. Can we pls put it back and <PERSON><PERSON><PERSON> prefers to use both ratings and compa ration"}]}]}]}, {"ts": "1733522598.408439", "text": "<@U0690EB5JE5> let's go over the UX phase 1 and tab grouping scope on Sunday night my time. your monday morning, if that works for you", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733522598.408439", "reply_count": 6, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}, {"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "/KyBf", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " let's go over the UX phase 1 and tab grouping scope on Sunday night my time. your monday morning, if that works for you"}]}]}]}, {"ts": "1733713768.892529", "text": "<!here> Will be OOO Friday Dec 13th and Monday Dec 16th for my school alumni event organized by my batch. Will try to attend meetings if available.\nWorking half day on 12th Dec.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733713768.892529", "reply_count": 1, "edited": {"user": "U0690EB5JE5", "ts": "1733713826.000000"}, "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "y1Uet", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Will be OOO Friday Dec 13th and Monday Dec 16th for my school alumni event organized by my batch. Will try to attend meetings if available.\nWorking half day on 12th Dec."}]}]}]}, {"ts": "1733724330.375289", "text": "<@U06HN8XDC5A>\n<https://docs.google.com/document/d/1uTj_be2c27bBv6nd8kjcAqb2uBPGdQDlsxyFCrqVVUg/edit?tab=t.0>\n<https://docs.google.com/document/d/1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk/edit?tab=t.0>", "user": "U07M6QKHUC9", "type": "message", "files": [{"id": "F083HN97AG2", "created": 1733260834, "timestamp": 1733260834, "name": "UX Product Recommendations ", "title": "UI/UX Product Recommendations", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 136275, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk", "external_url": "https://docs.google.com/document/d/1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTRbr1IFH/AzQ34/hRt9zQAo4/iz9aXI9aTHuaXFABRRRQA043c/wAqUY60jde9H50AOzRmkH0P5UfhQAtFFFADWxml20jdaAPp+dAC7RQAB0o49qXj2oAKKKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F083HN97AG2/ux_product_recommendations_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}, {"id": "F084W6HSJSU", "created": 1733724333, "timestamp": 1733724333, "name": "Copy of Recommender Cycle Experience:  Product Brief & PRD ", "title": "Recommender Cycle Experience:  Product Brief &amp; PRD", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 188879, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1uTj_be2c27bBv6nd8kjcAqb2uBPGdQDlsxyFCrqVVUg", "external_url": "https://docs.google.com/document/d/1uTj_be2c27bBv6nd8kjcAqb2uBPGdQDlsxyFCrqVVUg/edit?tab=t.0", "url_private": "https://docs.google.com/document/d/1uTj_be2c27bBv6nd8kjcAqb2uBPGdQDlsxyFCrqVVUg/edit?tab=t.0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F084W6HSJSU-3cf9efa5f0/copy_of_recommender_cycle_experience___product_brief___prd__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F084W6HSJSU-3cf9efa5f0/copy_of_recommender_cycle_experience___product_brief___prd__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F084W6HSJSU-3cf9efa5f0/copy_of_recommender_cycle_experience___product_brief___prd__360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F084W6HSJSU-3cf9efa5f0/copy_of_recommender_cycle_experience___product_brief___prd__480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F084W6HSJSU-3cf9efa5f0/copy_of_recommender_cycle_experience___product_brief___prd__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F084W6HSJSU-3cf9efa5f0/copy_of_recommender_cycle_experience___product_brief___prd__720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F084W6HSJSU-3cf9efa5f0/copy_of_recommender_cycle_experience___product_brief___prd__800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F084W6HSJSU-3cf9efa5f0/copy_of_recommender_cycle_experience___product_brief___prd__960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F084W6HSJSU-3cf9efa5f0/copy_of_recommender_cycle_experience___product_brief___prd__1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTRYnPBwKFyerGg5zxTuaAEwf7xowf7xpaKACiiigBjZ3cUbm/yKVhk96QDHQn8jQAu4+lGT6UnPqfypcH1oAdRRRQA16aKc3XrSAE96AAZ7UvPvRhvWlAPrQAtFFFAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F084W6HSJSU/copy_of_recommender_cycle_experience___product_brief___prd_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Q7gwm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U06HN8XDC5A"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://docs.google.com/document/d/1uTj_be2c27bBv6nd8kjcAqb2uBPGdQDlsxyFCrqVVUg/edit?tab=t.0"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://docs.google.com/document/d/1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk/edit?tab=t.0"}]}]}]}, {"ts": "1733724451.259499", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> Agenda for today to begin with:\n• Integrations on going work  - <@U071FN2589Y> will be joining\n", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733724451.259499", "reply_count": 9, "blocks": [{"type": "rich_text", "block_id": "cqZ6W", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Agenda for today to begin with:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Integrations on going work  - "}, {"type": "user", "user_id": "U071FN2589Y"}, {"type": "text", "text": " will be joining"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1733757704.559669", "text": "<@U0690EB5JE5> This came through for CWA: how do we proceed?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733757704.559669", "reply_count": 4, "files": [{"id": "F084NJKUTCZ", "created": 1733757697, "timestamp": 1733757697, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 134657, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F084NJKUTCZ/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F084NJKUTCZ/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F084NJKUTCZ-8727cda8c7/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F084NJKUTCZ-8727cda8c7/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F084NJKUTCZ-8727cda8c7/image_360.png", "thumb_360_w": 360, "thumb_360_h": 199, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F084NJKUTCZ-8727cda8c7/image_480.png", "thumb_480_w": 480, "thumb_480_h": 266, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F084NJKUTCZ-8727cda8c7/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F084NJKUTCZ-8727cda8c7/image_720.png", "thumb_720_w": 720, "thumb_720_h": 399, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F084NJKUTCZ-8727cda8c7/image_800.png", "thumb_800_w": 800, "thumb_800_h": 443, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F084NJKUTCZ-8727cda8c7/image_960.png", "thumb_960_w": 960, "thumb_960_h": 532, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F084NJKUTCZ-8727cda8c7/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 567, "original_w": 1534, "original_h": 850, "thumb_tiny": "AwAaADC+IwgwMnJJ55pw4HelYkdKTdQAu4f5FG4f5FAOaWgBNw/yKODS0UANcgYzSKc8in0UAAooooAKKKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F084NJKUTCZ/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F084NJKUTCZ-e7c7a97d69", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "35POC", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " This came through for CWA: how do we proceed?"}]}]}]}, {"ts": "1733759682.497209", "text": "FYI <@U07M6QKHUC9>, <PERSON><PERSON><PERSON> says re: Paybands - \"We source them through a combination of PayScale's and PayFactor's software (same company, different tool) using the job title and linking them to relevant descriptions\"", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733759682.497209", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "A5E98", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "FYI "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": ", <PERSON><PERSON><PERSON> says re: Paybands - \"We source them through a combination of PayScale's and PayFactor's software (same company, different tool) using the job title and linking them to relevant descriptions\""}]}]}]}, {"ts": "1733768741.273199", "text": "<@U0690EB5JE5> what dataware house we use? can you provide some details that I can share with <PERSON><PERSON> in order to explore our integration with them", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733768741.273199", "reply_count": 12, "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " what dataware house we use? can you provide some details that I can share with <PERSON><PERSON> in order to explore our integration with them"}]}]}]}, {"ts": "1733774437.057339", "text": "Add ability to filter by bonus eligible: <https://compiify.atlassian.net/browse/COM-4021>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14104::e96ead8d4f9e4d00ad8847843cb30562", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4021?atlOrigin=eyJpIjoiMTY2MzJlODQxY2E3NDEyNDgxZDMxNGM3ZWE4ZDVmZDMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4021 Add Filter for Bonus Eligible Employees>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14104::0eec75519e08430aa9df54769c1bc429", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14104\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4021\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4021", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "mIzSJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Add ability to filter by bonus eligible: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4021"}]}]}]}, {"ts": "1733775569.361389", "text": "<@U0690EB5JE5> assuming the current number of active environments we have, do you have an idea of how much our AWS costs will go down by after we have implemented tenancy in Jan. Need this info to determine how much in AWS credits we need for 2025? they are either use it or loose it.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733775569.361389", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "bD1iS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " assuming the current number of active environments we have, do you have an idea of how much our AWS costs will go down by after we have implemented tenancy in Jan. Need this info to determine how much in AWS credits we need for 2025? they are either use it or loose it."}]}]}]}, {"ts": "1733780598.741879", "text": "<@U0690EB5JE5> Unless you have other agenda items for Tomorrow's standup, we can go over the PRD for total rewards module. First draft should be ready by tomorrow", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733780598.741879", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "lQkdX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Unless you have other agenda items for Tomorrow's standup, we can go over the PRD for total rewards module. First draft should be ready by tomorrow"}]}]}]}, {"ts": "1733781419.875869", "text": "Feature request for curana: <https://compiify.atlassian.net/browse/COM-4022> I think it would be good to go over both Di<PERSON>ified and <PERSON><PERSON><PERSON>'s feedback in the call tomorrow.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14105::44f918d2389d40b590078772f270a4d3", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4022?atlOrigin=eyJpIjoiNzhjNTAwY2Q2NGY5NGU2OWE0NjFiNDJlNTE4M2JhNWYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4022 Feature Request: Market Adjustment Field Default Setting>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14105::269f3dcfdcc848e1a26a68deca258aa6", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14105\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4022\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4022", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "liSJC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Feature request for curana: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4022"}, {"type": "text", "text": " I think it would be good to go over both Di<PERSON><PERSON> and <PERSON><PERSON><PERSON>'s feedback in the call tomorrow."}]}]}]}, {"ts": "1733782415.524489", "text": "<@U07EJ2LP44S> can we pls set up a discovery call with <PERSON><PERSON><PERSON> and Curana to understand how their process of creating pay bands manually? We need to understand their process to guide our PRD. I will also have <PERSON> join those calls as she would be helping in on this projects on as-needed basis", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Y5ls2", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can we pls set up a discovery call with <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> to understand how their process of creating pay bands manually? We need to understand their process to guide our PRD. I will also have <PERSON> join those calls as she would be helping in on this projects on as-needed basis"}]}]}]}, {"ts": "1733782850.273249", "text": "I will ask curana, they are incredibly busy right now. <PERSON> might have more time, I’m not sure.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "xObIn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will ask curana, they are incredibly busy right now. <PERSON> might have more time, I’m not sure."}]}]}]}, {"ts": "1733791313.113279", "text": "<@U07EJ2LP44S> can you list which of the items in Diversified's are not covered by <PERSON>'s Phase 1- Epic 1 &amp; 2 PRDs so <PERSON><PERSON><PERSON> has clarity on what additional work needs to be done. We should create tickets for those items.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733498510.956169", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "ck+oT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can you list which of the items in Diversified's are not covered by <PERSON>'s Phase 1- Epic 1 & 2 PRDs so <PERSON><PERSON><PERSON> has clarity on what additional work needs to be done. We should create tickets for those items."}]}]}]}, {"ts": "1733792807.347489", "text": "Are we already agreed on completing every item on the prd lists? I’m not clear on what we are going to definitely achieve and the timeline. I can cross check these requirements with <PERSON>‘s list, but I’d like to get clarity on what we’re actually doing.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "iZwZ/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Are we already agreed on completing every item on the prd lists? I’m not clear on what we are going to definitely achieve and the timeline. I can cross check these requirements with <PERSON>‘s list, but I’d like to get clarity on what we’re actually doing."}]}]}]}, {"ts": "1733846836.871939", "text": "<@U0690EB5JE5> <@U07EJ2LP44S> <https://docs.google.com/document/d/103EIDBRmuDFa83boLIshQnPc2E87ze1s-lBbBb36qX0/edit?tab=t.0|Total Rewards PRD for review>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733846836.871939", "reply_count": 2, "edited": {"user": "U07M6QKHUC9", "ts": "1733848392.000000"}, "files": [{"id": "F0855GY4PSL", "created": 1733848394, "timestamp": 1733848394, "name": "Total Rewards PRD_Dec 9 2024", "title": "Total Rewards PRD_Dec 9 2024", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 182762, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "103EIDBRmuDFa83boLIshQnPc2E87ze1s-lBbBb36qX0", "external_url": "https://docs.google.com/document/d/103EIDBRmuDFa83boLIshQnPc2E87ze1s-lBbBb36qX0/edit?tab=t.0", "url_private": "https://docs.google.com/document/d/103EIDBRmuDFa83boLIshQnPc2E87ze1s-lBbBb36qX0/edit?tab=t.0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F0855GY4PSL-1a17f53126/total_rewards_prd_dec_9_2024_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTSPXqRSYP98/pQ2D1/lQF44JoAUcd80uRSbfc0bfegBaKKKAGt196Bk9xQ2PWkC980AO5/vUc+tJt9x+VKAPWgBaKKKAGt1oGPWlbpTB1oAfketHFG4UZoAWiiigD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0855GY4PSL/total_rewards_prd_dec_9_2024", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Is/m+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://docs.google.com/document/d/103EIDBRmuDFa83boLIshQnPc2E87ze1s-lBbBb36qX0/edit?tab=t.0", "text": "Total Rewards PRD for review"}]}]}]}, {"ts": "1733850017.664969", "text": "Tithely UATs combined and new tickets created: <https://compiify.atlassian.net/browse/COM-3946>", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}, {"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14029::75cc7554f3e54ff78b2a7e03d1835e35", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3946?atlOrigin=eyJpIjoiOGI2MzZkOTU5MjViNDhjZDhlZGQ2NWQ4NDlmMWE2YTMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3946 Tithely UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14029::7e4f561343cf4ff18b34301a540df6ab", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14029\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3946\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3946", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "R9OWT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Tithely UATs combined and new tickets created: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3946"}]}]}]}, {"ts": "1733850416.561889", "text": "Diviersified bonus award field to be made uneditable request: <https://compiify.atlassian.net/browse/COM-4025>", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9", "U0690EB5JE5"], "count": 2}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14108::1eb73cc5381b42eb85dcb434e1efbecb", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4025?atlOrigin=eyJpIjoiM2ExNjYyNjI0NWNlNDYxZjgyZDA2N2E1ODMwNTc5ZTciLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4025 Request to Make Bonus Award Section Non-Editable>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14108::e1de495789f84b65be4151b6886cd56e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14108\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4025\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4025", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "VaglR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Diviersified bonus award field to be made uneditable request: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4025"}]}]}]}, {"ts": "1733855891.499739", "text": "Curana market adjustment/compa ratio request here: <https://compiify.atlassian.net/browse/COM-4027>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14110::afe1de6d3d1344bd853555e4329cf402", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4027?atlOrigin=eyJpIjoiNWRjNjA2MWRiMzgxNDZlZGEwZjA2ZGJjZWIyODQxNmIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4027 Feature Request: Curana's Market Adjustment workflow>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14110::053c9432e34e4f7d9d8904cec04273a5", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14110\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4027\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4027", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "YrN5W", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Curana market adjustment/compa ratio request here: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4027"}]}]}]}, {"ts": "1733860443.290419", "text": "<@U0690EB5JE5> When we are in test mode, are we triggering an email without realizing it? As in, if <PERSON> tests the approval chain, could these people who have not yet been introduced to Stride be getting an email? What are the trigger points for emails right now in the system?\n\nShe's concerned that if she goes in an submit's on behalf of a manager, it'll send an email to THEIR manager telling them they have something to approve. I haven't had this question before and it didn't even occur to me to ask.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733860443.290419", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "cy+gU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " When we are in test mode, are we triggering an email without realizing it? As in, if <PERSON> tests the approval chain, could these people who have not yet been introduced to Stride be getting an email? What are the trigger points for emails right now in the system?\n\nShe's concerned that if she goes in an submit's on behalf of a manager, it'll send an email to THEIR manager telling them they have something to approve. I haven't had this question before and it didn't even occur to me to ask."}]}]}]}, {"ts": "1733861819.861239", "text": "hey <@U07EJ2LP44S> When can we set up a paybands discovery session with <PERSON><PERSON><PERSON>? Would they be open to meeting this week?\n\nAlso can we do the same for C<PERSON><PERSON>? or make it an agenda items for the next weekly call with them?", "user": "U07M6QKHUC9", "type": "message", "edited": {"user": "U07M6QKHUC9", "ts": "1733861951.000000"}, "blocks": [{"type": "rich_text", "block_id": "xbCQ/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "hey "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " When can we set up a paybands discovery session with <PERSON><PERSON><PERSON>? Would they be open to meeting this week?\n\nAlso can we do the same for <PERSON><PERSON><PERSON>? or make it an agenda items for the next weekly call with them?"}]}]}]}, {"ts": "1733861973.350069", "text": "also can you pls invite <PERSON> to the next call with <PERSON><PERSON>?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lynq4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "also can you pls invite <PERSON> to the next call with <PERSON><PERSON>?"}]}]}]}, {"ts": "1733863332.234359", "text": "I'll ask <PERSON><PERSON><PERSON>; I want to be respectful of their capacity.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XzPlN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'll ask <PERSON><PERSON><PERSON>; I want to be respectful of their capacity."}]}]}]}, {"ts": "1733948742.699469", "text": "This is a really convoluted one, I hope I explained it correctly: <https://compiify.atlassian.net/browse/COM-4028>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733948742.699469", "reply_count": 25, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14111::ca2907ce43364a5e83080dfd90c73b89", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4028?atlOrigin=eyJpIjoiMzlmZmRjODdkZTU1NDIwNjlmZjMyNWViMzRmZDMwYmEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4028 Issue: Bug in Diversified Recommender Selection>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14111::b25d0a039b66447aa9f5e90511b7ea1e", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14111\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4028\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4028", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Y890q", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is a really convoluted one, I hope I explained it correctly: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4028"}]}]}]}, {"ts": "1733967545.251179", "text": "Will be off until Monday.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733713768.892529", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "itMUf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will be off until Monday."}]}]}]}, {"ts": "1734001969.428889", "text": "<@U07M6QKHUC9> Regarding Tithley employment history, Could you please get an example employee with their employment history from their HRIS system, I did check the data from API, Currently we get some history but it is not making much sense to me. Need an example to confirm what we get from API matches the expectation.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734001969.428889", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "uvWMQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Regarding Tithley employment history, Could you please get an example employee with their employment history from their HRIS system, I did check the data from API, Currently we get some history but it is not making much sense to me. Need an example to confirm what we get from API matches the expectation."}]}]}]}, {"ts": "1734015167.151239", "text": "<@U0690EB5JE5>  <@U07M6QKHUC9> Tith<PERSON> is asking if its possible to have salary penetration instead of compa ratio. This seems reasonable but I know it will be some work since compa ratio is in several places. I'm not sure we need to go fully into the insights, but maybe in the merit and org tables? Salary/range penetration is salary divided by the max shown as a percentage.\n\nThey don't use compa ratio so it'll be confusing for the recommending managers.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734015167.151239", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "NYe8L", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": "  "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Tithely is asking if its possible to have salary penetration instead of compa ratio. This seems reasonable but I know it will be some work since compa ratio is in several places. I'm not sure we need to go fully into the insights, but maybe in the merit and org tables? Salary/range penetration is salary divided by the max shown as a percentage.\n\nThey don't use compa ratio so it'll be confusing for the recommending managers."}]}]}]}, {"ts": "1734019079.223689", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> I won’t be able to attend meeting today and tomorrow. ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "4xkw7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I "}, {"type": "text", "text": "won’t"}, {"type": "text", "text": " be able to attend meeting today and tomorrow. "}]}]}]}, {"ts": "1734019098.808569", "text": "Today we are talking with valgenesis about their paybands needs", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "i2cOR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Today we are talking with valgenesis about their paybands needs"}]}]}]}, {"ts": "1734019102.607529", "text": "It will be recorded", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "b18at", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It will be recorded"}]}]}]}, {"ts": "1734022242.245159", "text": "Valgenesis HiBob data refresh request: <https://compiify.atlassian.net/browse/COM-4029>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734022242.245159", "reply_count": 11, "edited": {"user": "U07EJ2LP44S", "ts": "1734022257.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14112::e8166c654f0c4f07b628f3c762bd94ab", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4029?atlOrigin=eyJpIjoiZWI3ZTE2NTYxNzg5NDYzN2I0ZTQ5ZTBjMTdkY2VkNjIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4029 HiBob Refresh>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14112::67ee2b6e0ba1469391c46b56fe84db40", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14112\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4029\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4029", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "QTqIK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis HiBob data refresh request: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4029"}]}]}]}, {"ts": "1734022360.396179", "text": "Valgenesis promotions request: <https://compiify.atlassian.net/browse/COM-4030>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14113::2ba2409e8f0e48f59950056e8988b9af", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4030?atlOrigin=eyJpIjoiM2M1ZDNlZWU2MGMxNDI2NjkwZTM1OTU0Mjc0MDM1ODciLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4030 Promotions upload - preload salary>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14113::bdd7318f5f2c4d368e4efca64934ad09", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/high.png", "alt_text": "High"}, {"type": "mrkdwn", "text": "Priority: *High*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14113\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4030\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4030", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "6/kuC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis promotions request: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4030"}]}]}]}, {"ts": "1734025306.800759", "text": "<@U0690EB5JE5> <@U07EJ2LP44S> based on the advisor calls, valgenesis call today, and market research, so far the conclusion is that giving customers the ability to create and manage paybands in Stride is not be the best problem to solve for at this point. It's not going to help us find the product market fit. I can share more during the standup.\n\nWe can still make improvements to the existing paybands module to make it more useful to the customers. I'll create the PRD for that but it's not a top priority.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734025306.800759", "reply_count": 3, "reactions": [{"name": "+1", "users": ["U07EJ2LP44S", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "UY/sK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " based on the advisor calls, valgenesis call today, and market research, so far the conclusion is that giving customers the ability to create and manage paybands in Stride is not be the best problem to solve for at this point. It's not going to help us find the product market fit. I can share more during the standup.\n\nWe can still make improvements to the existing paybands module to make it more useful to the customers. I'll create the PRD for that but it's not a top priority."}]}]}]}, {"ts": "**********.186369", "text": "<@U0690EB5JE5> Degenkolb is ready to start generating their letters. Do you need anything else to kick off this process? Ideally, the letters can be exported by team so they are easier to distribute (like we talked about before, just putting them into folders vs downloads in the account). We'll want to QA a few of them once we've generated.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.186369", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "7V4/N", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Degenkolb is ready to start generating their letters. Do you need anything else to kick off this process? Ideally, the letters can be exported by team so they are easier to distribute (like we talked about before, just putting them into folders vs downloads in the account). We'll want to QA a few of them once we've generated."}]}]}]}, {"ts": "**********.597209", "text": "Should we cancel the leadership meeting tomorrow? <PERSON><PERSON><PERSON> is out of the office.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.597209", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "jbvv2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we cancel the leadership meeting tomorrow? <PERSON><PERSON><PERSON> is out of the office."}]}]}]}, {"ts": "**********.549309", "text": "<@U07EJ2LP44S> do we have any updates from Alaya Care on the closing of their cycle? I'd also love to attend the feedback call with them. Any idea when <PERSON> is going to be able to go over that with us?", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pOVDQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " do we have any updates from <PERSON><PERSON> Care on the closing of their cycle? I'd also love to attend the feedback call with them. Any idea when <PERSON> is going to be able to go over that with us?"}]}]}]}, {"ts": "1734108461.511459", "text": "It is ready to close. They gave some feedback on people insights that I will write up, but otherwise she said she gave her feedback throughout the cycle, so nothing new. They also said the report exports are 'really ugly' LOL so I think we could do better there. We can do a total wrap up call and discuss a testimonial/case study, but we'll need to do that after the new year. They still have a ton going on with the backend work of the cycle, and they are starting open enrollment now.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734108461.511459", "reply_count": 5, "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "QTEIY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It is ready to close. They gave some feedback on people insights that I will write up, but otherwise she said she gave her feedback throughout the cycle, so nothing new. They also said the report exports are 'really ugly' LOL so I think we could do better there. We can do a total wrap up call and discuss a testimonial/case study, but we'll need to do that after the new year. They still have a ton going on with the backend work of the cycle, and they are starting open enrollment now."}]}]}]}, {"ts": "1734108639.933309", "text": "<@U0690EB5JE5> SSO for Diversified - certificate/URL in your inbox; can you review when you return?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734108639.933309", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "AqXAU", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " SSO for Diversified - certificate/URL in your inbox; can you review when you return?"}]}]}]}, {"ts": "1734112157.732929", "text": "<@U07EJ2LP44S> at 15Five, were you involved in headcount planning at all?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734112157.732929", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "C6KdA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " at 15Five, were you involved in headcount planning at all?"}]}]}]}, {"ts": "1734114937.959729", "text": "<@U07EJ2LP44S> are you able to meet in 25 min from now to discuss pay bands functionality?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734114937.959729", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "z7xaF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " are you able to meet in 25 min from now to discuss pay bands functionality?"}]}]}]}, {"ts": "1734363102.374499", "text": "<@U07EJ2LP44S> since <PERSON><PERSON><PERSON> is out today. Also, let’s cancel the meeting today. <@U07NBMXTL1E> can provide sales updates in tomorrow’s call.", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07NBMXTL1E"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "b0amt", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " since <PERSON><PERSON><PERSON> is out today. Also, let’s cancel the meeting today. "}, {"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " can provide sales updates in tomorrow’s call."}]}]}]}, {"ts": "1734363123.266319", "text": "Will do", "user": "U07NBMXTL1E", "type": "message", "blocks": [{"type": "rich_text", "block_id": "HK0na", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will do"}]}]}]}, {"ts": "1734364678.412989", "text": "Ok that works", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BMeVM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok that works"}]}]}]}, {"ts": "1734368908.191279", "text": "Tooltips are complete and ready for review: <https://docs.google.com/document/d/1czwb2ElbZTwNZ6a-YNXVui25sN8PX9-KWvRGT7dsMxE/edit?usp=sharing>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734368908.191279", "reply_count": 5, "files": [{"id": "F084WMZSZST", "created": 1734368911, "timestamp": 1734368911, "name": "Manager Enablement Phase 1 - Tooltips Text", "title": "Manager Enablement Phase 1 - Tooltips Text", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 182543, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1czwb2ElbZTwNZ6a-YNXVui25sN8PX9-KWvRGT7dsMxE", "external_url": "https://docs.google.com/document/d/1czwb2ElbZTwNZ6a-YNXVui25sN8PX9-KWvRGT7dsMxE/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1czwb2ElbZTwNZ6a-YNXVui25sN8PX9-KWvRGT7dsMxE/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F084WMZSZST-da37ad8079/manager_enablement_phase_1_-_tooltips_text_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F084WMZSZST-da37ad8079/manager_enablement_phase_1_-_tooltips_text_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F084WMZSZST-da37ad8079/manager_enablement_phase_1_-_tooltips_text_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F084WMZSZST-da37ad8079/manager_enablement_phase_1_-_tooltips_text_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F084WMZSZST-da37ad8079/manager_enablement_phase_1_-_tooltips_text_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F084WMZSZST-da37ad8079/manager_enablement_phase_1_-_tooltips_text_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F084WMZSZST-da37ad8079/manager_enablement_phase_1_-_tooltips_text_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F084WMZSZST-da37ad8079/manager_enablement_phase_1_-_tooltips_text_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F084WMZSZST-da37ad8079/manager_enablement_phase_1_-_tooltips_text_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTSOexxTckHrT6j70AO59T+VKAfU0A0tABRRRQAGo+9SU0A55NAB/nrTh9KMUUAFFFFADWpMmlbikDe9AC59T+lLn3/AEpN3vS5Hr+lAC0UUUAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F084WMZSZST/manager_enablement_phase_1_-_tooltips_text", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "A30ql", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Tooltips are complete and ready for review: "}, {"type": "link", "url": "https://docs.google.com/document/d/1czwb2ElbZTwNZ6a-YNXVui25sN8PX9-KWvRGT7dsMxE/edit?usp=sharing"}]}]}]}, {"ts": "1734370134.049029", "text": "<@U07EJ2LP44S> DGOC letters. I am away from laptop, just forwarding the from my slack and haven’t checked them myself. Please review.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734370134.049029", "reply_count": 6, "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "files": [{"id": "F085DFU8B44", "file_access": "access_denied", "created": 0, "timestamp": 0, "user": "U08QENR4TNY", "filetype": "zip"}], "blocks": [{"type": "rich_text", "block_id": "7Xee+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " DGOC letters. I am away from laptop, just forwarding the from my slack and "}, {"type": "text", "text": "haven’t"}, {"type": "text", "text": " checked them myself. Please review."}]}]}]}, {"ts": "1734375439.294449", "text": "<@U0690EB5JE5> Pls add your comments too if you have any", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734368908.191279", "subtype": "thread_broadcast", "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Laon5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Pls add your comments too if you have any"}]}]}]}, {"ts": "1734383629.136579", "text": "<@U07M6QKHUC9> we have a call to discuss paybands with tithely this week. do you want to cancel, since building out the full feature is no longer on the table?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734383629.136579", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Ly6rL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " we have a call to discuss paybands with tithely this week. do you want to cancel, since building out the full feature is no longer on the table?"}]}]}]}, {"ts": "1734387638.779779", "text": "<@U0690EB5JE5> <PERSON><PERSON><PERSON><PERSON> is asking for SFTP info. She says 'I received the username and SSH key, but not the host site or password.'", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734387638.779779", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "hVS2R", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " <PERSON><PERSON><PERSON><PERSON> is asking for SFTP info. She says 'I received the username and SSH key, but not the host site or password.'"}]}]}]}, {"ts": "1734387813.542859", "text": "<@U0690EB5JE5> Also, can you please check your email for RE: Stride Letters, from Degenkolb. Id like to discuss if we can do what they are asking (different letters for different role types - exempt, non exempt, etc)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734387813.542859", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "x/OFk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Also, can you please check your email for RE: Stride Letters, from Degenkolb. Id like to discuss if we can do what they are asking (different letters for different role types - exempt, non exempt, etc)"}]}]}]}, {"ts": "1734444207.364829", "text": "<@U07EJ2LP44S> Letters updated per email from customer. Also folder structure per recommenders hierarchy. Please spot check once before sharing with customer.\n<@U07M6QKHUC9> Again this took almost 2 days of an engineer's time. This is not sustainable in the long term. We should stop accepting customer specific template requirements and generalize with support for little customizations (self serve)", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734370134.049029", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1734444386.000000"}, "files": [{"id": "F085DFAV2PP", "created": 1734444063, "timestamp": 1734444063, "name": "employee_letters_nested_fixed.zip", "title": "employee_letters_nested_fixed.zip", "mimetype": "application/zip", "filetype": "zip", "pretty_type": "Zip", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 27137152, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F085DFAV2PP/employee_letters_nested_fixed.zip", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F085DFAV2PP/download/employee_letters_nested_fixed.zip", "media_display_type": "unknown", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F085DFAV2PP/employee_letters_nested_fixed.zip", "permalink_public": "https://slack-files.com/T04DM97F1UM-F085DFAV2PP-b446eaab85", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "S7qsH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Letters updated per email from customer. Also folder structure per recommenders hierarchy. Please spot check once before sharing with customer.\n"}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Again this took almost 2 days of an engineer's time. This is not sustainable in the long term. We should stop accepting customer specific template requirements and generalize with support for little customizations (self serve)"}]}]}]}, {"ts": "1734452257.625669", "text": "<https://docs.google.com/document/d/1IEdPBEMQWs3VEA275DKcBt5QOWWHujR04eOVkAoYtv0/edit?tab=t.0>", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F085VSUPC1F", "created": 1734452260, "timestamp": 1734452260, "name": "Paybands Feedback", "title": "Paybands Feedback", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 60944, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1IEdPBEMQWs3VEA275DKcBt5QOWWHujR04eOVkAoYtv0", "external_url": "https://docs.google.com/document/d/1IEdPBEMQWs3VEA275DKcBt5QOWWHujR04eOVkAoYtv0/edit?tab=t.0", "url_private": "https://docs.google.com/document/d/1IEdPBEMQWs3VEA275DKcBt5QOWWHujR04eOVkAoYtv0/edit?tab=t.0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F085VSUPC1F-1d9d7dba47/paybands_feedback_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTRJwetOBzTWI6GlHSgBaKKKACiiigBDjPWgEDvQRmgKBQAuR60UUUAFFFFABRRRQAUUUUAFFFFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F085VSUPC1F/paybands_feedback", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "u7LdQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/document/d/1IEdPBEMQWs3VEA275DKcBt5QOWWHujR04eOVkAoYtv0/edit?tab=t.0"}]}]}]}, {"ts": "1734470992.623349", "text": "<@U0690EB5JE5> Looks like the Degenkolb letters are good except one title. The 'Design Engineer' title is a Professional Exempt position but they were put onto the non exempt template. Can we fix those only?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734470992.623349", "reply_count": 2, "files": [{"id": "F085XKECNGZ", "created": 1734470990, "timestamp": 1734470990, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 58470, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F085XKECNGZ/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F085XKECNGZ/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F085XKECNGZ-4755ef078f/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F085XKECNGZ-4755ef078f/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F085XKECNGZ-4755ef078f/image_360.png", "thumb_360_w": 360, "thumb_360_h": 210, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F085XKECNGZ-4755ef078f/image_480.png", "thumb_480_w": 480, "thumb_480_h": 280, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F085XKECNGZ-4755ef078f/image_160.png", "original_w": 702, "original_h": 410, "thumb_tiny": "AwAcADC66MMkMfpgUqxnGS2ePSnSfdOP5U5fuj6VNwIWRgcBifwHFPEZxy2fwpH5P3T/AN8g1IDkdMUXAhKMDjcfyFO8s7fvc/Shxl+hOPYGpD0ouAj/AHemfwzSr90fSggEcjNKAAMCiwEUgy33c/8AAc1JSFFY5Kg07FFgIXXLdAf+A5qXHGDSeWn90UoUBcAcelFgP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F085XKECNGZ/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F085XKECNGZ-4eb720f411", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "eKqYE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Looks like the Degenkolb letters are good except one title. The 'Design Engineer' title is a Professional Exempt position but they were put onto the non exempt template. Can we fix those only?"}]}]}]}, {"ts": "1734517942.652269", "text": "Hi <@U07EJ2LP44S> for \"Diversified SSO\" could you please share the instructions doc you sent to them.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734517942.652269", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "0ayXp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hi "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " for \"Diversified SSO\" could you please share the instructions doc you sent to them."}]}]}]}, {"ts": "1734518147.887649", "text": "<@U07M6QKHUC9> We need to add another task to the list. CWA SFTP integration.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9DEw/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " We need to add another task to the list. CWA SFTP integration."}]}]}]}, {"ts": "1734538629.366739", "text": "Vestwell Call: <https://app.fireflies.ai/view/Stride-Vestwell-Product-Demo::uaFDJCGWqFuXgqja?ref=recap&amp;track=uaFDJCGWqFuXgqja&amp;sg=nb&amp;utm_content=view_recap_cta>", "user": "U07NBMXTL1E", "type": "message", "attachments": [{"from_url": "https://app.fireflies.ai/view/Stride-Vestwell-Product-Demo::uaFDJCGWqFuXgqja?ref=recap&track=uaFDJCGWqFuXgqja&sg=nb&utm_content=view_recap_cta", "thumb_url": "https://files.fireflies.ai/app/img/preview.png", "thumb_width": 1280, "thumb_height": 720, "image_url": "https://files.fireflies.ai/app/img/preview.png", "image_width": 1280, "image_height": 720, "image_bytes": 188965, "service_icon": "https://app.fireflies.ai/favicon.ico", "id": 1, "original_url": "https://app.fireflies.ai/view/Stride-Vestwell-Product-Demo::uaFDJCGWqFuXgqja?ref=recap&amp;track=uaFDJCGWqFuXgqja&amp;sg=nb&amp;utm_content=view_recap_cta", "fallback": "Stride  Vestwell Product Demo - Meeting recording by Fireflies.ai", "title": "Stride  Vestwell Product Demo - Meeting recording by Fireflies.ai", "title_link": "https://app.fireflies.ai/view/Stride-Vestwell-Product-Demo::uaFDJCGWqFuXgqja?ref=recap&track=uaFDJCGWqFuXgqja&sg=nb&utm_content=view_recap_cta", "service_name": "app.fireflies.ai"}], "blocks": [{"type": "rich_text", "block_id": "uqO5f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Vestwell Call: "}, {"type": "link", "url": "https://app.fireflies.ai/view/Stride-Vestwell-Product-Demo::uaFDJCGWqFuXgqja?ref=recap&track=uaFDJCGWqFuXgqja&sg=nb&utm_content=view_recap_cta"}]}]}]}, {"ts": "1734539252.220109", "text": "<https://drive.google.com/file/d/1ru5cawpNNMml5MaWOI3L7RkOWRjqzC67/view?usp=drive_link>", "user": "U07M6QKHUC9", "type": "message", "files": [{"id": "F084J4J99UJ", "created": 1733862721, "timestamp": 1733862721, "name": "BetterComp Demo.MOV", "title": "BetterComp Demo.MOV", "mimetype": "video/quicktime", "filetype": "mov", "pretty_type": "QuickTime Movie", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1ru5cawpNNMml5MaWOI3L7RkOWRjqzC67", "external_url": "https://drive.google.com/file/d/1ru5cawpNNMml5MaWOI3L7RkOWRjqzC67/view?usp=drive_link", "url_private": "https://drive.google.com/file/d/1ru5cawpNNMml5MaWOI3L7RkOWRjqzC67/view?usp=drive_link", "media_display_type": "video", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F084J4J99UJ/bettercomp_demo.mov", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "qDLVg", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://drive.google.com/file/d/1ru5cawpNNMml5MaWOI3L7RkOWRjqzC67/view?usp=drive_link"}]}]}]}, {"ts": "1734606627.678479", "text": "<@U07EJ2LP44S> Need some help on finalizing the verbiage for <https://compiify.atlassian.net/browse/COM-4027|COM-4027>:\nAlso, for <https://compiify.atlassian.net/browse/COM-4021|COM-4021>, I was thinking that sorting this column can serve our purpose. Or do we only want filtering?\n\nCC: <@U0690EB5JE5>", "user": "U07MH77PUBV", "type": "message", "files": [{"id": "F085U21Q599", "created": 1734606481, "timestamp": 1734606481, "name": "Screenshot 2024-12-19 at 1.15.23 PM.png", "title": "Screenshot 2024-12-19 at 1.15.23 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07MH77PUBV", "user_team": "T04DM97F1UM", "editable": false, "size": 134591, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F085U21Q599/screenshot_2024-12-19_at_1.15.23___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F085U21Q599/download/screenshot_2024-12-19_at_1.15.23___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 297, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 396, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 595, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 661, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 793, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F085U21Q599-b82da93249/screenshot_2024-12-19_at_1.15.23___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 846, "original_w": 1166, "original_h": 963, "thumb_tiny": "AwAnADDRxkfMB1+tAVfSgDCnnPNKDQAg2g8D9KdTTwf/AK9Nd1Xhsj86AJKKRcEDGaNvufzoAB0pcUCigBD1pMH2pT1oGPQUAAB9aWjiigAFFAooAacZo+X0FB60lA7C/L6CnCmU5elAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F085U21Q599/screenshot_2024-12-19_at_1.15.23___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F085U21Q599-7607a47d81", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "KvSSc", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Need some help on finalizing the verbiage for "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4027", "text": "COM-4027"}, {"type": "text", "text": ":\nAlso, for "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4021", "text": "COM-4021"}, {"type": "text", "text": ", I was thinking that sorting this column can serve our purpose. Or do we only want filtering?\n\nCC: "}, {"type": "user", "user_id": "U0690EB5JE5"}]}]}]}, {"ts": "1734617693.076909", "text": "I actually think that verbiage makes total sense. Ideally, we would have this be sortable and filterable.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734617693.076909", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "XnWLf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I actually think that verbiage makes total sense. Ideally, we would have this be sortable and filterable."}]}]}]}, {"ts": "1734631941.726549", "text": "<@U07EJ2LP44S> Dan is actually available today to meet. I added you to the invite at 2 pm pst today", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "M9Efp", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Dan is actually available today to meet. I added you to the invite at 2 pm pst today"}]}]}]}, {"ts": "1734635710.536529", "text": "<@U07NBMXTL1E> we have three customer call today. Can we try to squeeze in 10 min during these calls to learn about customer's painpoints on Pre-Cycle budget planning and analytics to validate the pain points <PERSON><PERSON><PERSON> brought up?\nI think it should be fairly easy to do with <PERSON><PERSON> for sure since we are meeting them for the 3rd time today.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ni23i", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " we have three customer call today. Can we try to squeeze in 10 min during these calls to learn about customer's painpoints on Pre-Cycle budget planning and analytics to validate the pain points <PERSON>est<PERSON> brought up?\nI think it should be fairly easy to do with <PERSON><PERSON> for sure since we are meeting them for the 3rd time today."}]}]}]}, {"ts": "1734635937.828179", "text": "For the first and third calls, sure. For Alfatech in between, no. They're getting close to closure so adding new variables will probably do nothing but add confusion or give them an excuse to push this out further", "user": "U07NBMXTL1E", "type": "message", "thread_ts": "1734635937.828179", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "kMAbJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "For the first and third calls, sure. For Alfatech in between, no. They're getting close to closure so adding new variables will probably do nothing but add confusion or give them an excuse to push this out further"}]}]}]}, {"ts": "1734639441.024379", "text": "<!here> We do have one more 800 emp company that have validated the needs for pre-cycle budget planning and analytics.\n<https://app.fireflies.ai/view/Stride-Introduction-w-Tosh-RewardStyle::Q57CrAEBjMJ6hbue> check out the last 15 mins of the call", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734639441.024379", "reply_count": 1, "attachments": [{"image_url": "https://files.fireflies.ai/app/img/preview.png", "image_width": 1280, "image_height": 720, "image_bytes": 188965, "from_url": "https://app.fireflies.ai/view/Stride-Introduction-w-Tosh-RewardStyle::Q57CrAEBjMJ6hbue", "service_icon": "https://app.fireflies.ai/favicon.ico", "thumb_url": "https://files.fireflies.ai/app/img/preview.png", "thumb_width": 1280, "thumb_height": 720, "id": 1, "original_url": "https://app.fireflies.ai/view/Stride-Introduction-w-Tosh-RewardStyle::Q57CrAEBjMJ6hbue", "fallback": "Stride Introduction w/ Tosh @ RewardStyle - Meeting recording by Fireflies.ai", "text": "In the Product Introduction and Requirements Gathering meeting titled \"Stride Introduction w/ Tosh @ RewardStyle,\" participants discussed Stride, a compensation plan...", "title": "Stride Introduction w/ Tosh @ RewardStyle - Meeting recording by Fireflies.ai", "title_link": "https://app.fireflies.ai/view/Stride-Introduction-w-Tosh-RewardStyle::Q57CrAEBjMJ6hbue", "service_name": "app.fireflies.ai"}], "blocks": [{"type": "rich_text", "block_id": "tOpMt", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " We do have one more 800 emp company that have validated the needs for pre-cycle budget planning and analytics.\n"}, {"type": "link", "url": "https://app.fireflies.ai/view/Stride-Introduction-w-Tosh-RewardStyle::Q57CrAEBjMJ6hbue"}, {"type": "text", "text": " check out the last 15 mins of the call"}]}]}]}, {"ts": "1734650531.802349", "text": "<@U07EJ2LP44S> <PERSON><PERSON> agreed to submit the G2 reviews,\nBefore they renew, they want to see that we have incorporated their feedback in the product and they will not need the manual support for things they needed in their August Cycle. I know you had a feedback call with them. Can you also go through their slack channel to create a list of issues where they needed manual support from us.\nWe will need to show them that we have addressed all of those issues.\n\nLet's go through each of those items in the standup on Monday if they need engineering work", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1734650531.802349", "reply_count": 3, "edited": {"user": "U07M6QKHUC9", "ts": "1734651665.000000"}, "blocks": [{"type": "rich_text", "block_id": "28F70", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " <PERSON><PERSON> agreed to submit the G2 reviews,\nBefore they renew, they want to see that we have incorporated their feedback in the product and they will not need the manual support for things they needed in their August Cycle. I know you had a feedback call with them. Can you also go through their slack channel to create a list of issues where they needed manual support from us.\nWe will need to show them that we have addressed all of those issues.\n\nLet's go through each of those items in the standup on Monday if they need engineering work"}]}]}]}, {"ts": "1734651344.076449", "text": "Agenda for Tomorrow standup:\n• Review of Total Rewards PRD\n• Align on action items tracker so that we can track actions items going forward.\n• Finalize the execution plan along with activity timeline for Precycle budget planning and analytics. <PERSON> to lead this project. (only if time permits. If not <PERSON><PERSON><PERSON> and <PERSON> can do this offline)\nActions from Dec 19 standup\n• <PERSON> to gather detailed feedback from Curana, AlayaCare, &amp; Diversified on Precycle budget planning and analytics. Due Date Jan 15th\n• Ka<PERSON>l to document key features/functionalities for Comp Planner\n• Dedicate one day per week to go over the progress and status of strategic product initiatives, learnings, customer and prospect feedback etc.\n• Amanda to get customer approval for G2 reviews (discussed outside of standup)", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Agenda for Tomorrow standup:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Review of Total Rewards PRD"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Align on action items tracker so that we can track actions items going forward."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Finalize the execution plan along with activity timeline for Precycle budget planning and analytics. <PERSON> to lead this project. (only if time permits. If not <PERSON><PERSON><PERSON> and <PERSON> can do this offline)"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nActions from Dec 19 standup\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Amanda to gather detailed feedback from Curana, AlayaCare, & Diversified on Precycle budget planning and analytics. Due Date Jan 15th"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> to document key features/functionalities for Comp Planner"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Dedicate one day per week to go over the progress and status of strategic product initiatives, learnings, customer and prospect feedback etc."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Amanda to get customer approval for G2 reviews (discussed outside of standup)"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1734651605.887059", "text": "<@U07NBMXTL1E> Could you please get the feedback on Precycle budget planning and analytics in the two customer calls you have tomorrow? <@U07EJ2LP44S> Amanda can you pls join the 7 am pst call <PERSON><PERSON> has with one of the prospects to validate Precycle budget planning and analytics idea?\n\n<@U07NBMXTL1E> Let's collect feedback from 20 customers on this idea. Either <PERSON> or I will join the call depending on timing so please coordinate with us for all intro and demo calls you are having with 400+ emp customers over the next 3  weeks.", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07NBMXTL1E", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "oRtTO", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " Could you please get the feedback on Precycle budget planning and analytics in the two customer calls you have tomorrow? "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Amanda can you pls join the 7 am pst call <PERSON><PERSON> has with one of the prospects to validate Precycle budget planning and analytics idea?\n\n"}, {"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " Let's collect feedback from 20 customers on this idea. Either <PERSON> or I will join the call depending on timing so please coordinate with us for all intro and demo calls you are having with 400+ emp customers over the next 3  weeks."}]}]}]}, {"ts": "1734652225.579739", "text": "<@U07NBMXTL1E> can you pls set up a 1 hr call with vestwell during 1st or 2nd week of January to better understand their requirements and pain points on Precycle budget planning and analytics?", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07NBMXTL1E"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "dKg6a", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " can you pls set up a 1 hr call with vestwell during 1st or 2nd week of January to better understand their requirements and pain points on Precycle budget planning and analytics?"}]}]}]}, {"ts": "1734661754.611649", "text": "<!here> I've a conflict with another call for standup tomorrow. will join about 15 mins late", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "gsdB+", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I've a conflict with another call for standup tomorrow. will join about 15 mins late"}]}]}]}, {"ts": "1734711609.236899", "text": "<@U07NBMXTL1E> pls unenroll all unverified contacts from all of the active sequences immediately. It's hurting our domain reputation", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U07NBMXTL1E"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "RRDya", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " pls unenroll all unverified contacts from all of the active sequences immediately. It's hurting our domain reputation"}]}]}]}, {"ts": "1734716688.537129", "text": "So we've used those filters for Verified / Likely to Engage and what we're seeing now is that a lot of the emails get the green check, but still blocked as spam...not sure if this is a Senders thing, or an Apollo data quality issue, but its not like we're sending a crazy amount of emails to random or unverified people purposefully", "user": "U07NBMXTL1E", "type": "message", "thread_ts": "1734716688.537129", "reply_count": 13, "files": [{"id": "F086ENQNLF3", "created": 1734716619, "timestamp": 1734716619, "name": "Screenshot 2024-12-20 at 12.43.35 PM.png", "title": "Screenshot 2024-12-20 at 12.43.35 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07NBMXTL1E", "user_team": "T04DM97F1UM", "editable": false, "size": 391606, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F086ENQNLF3/screenshot_2024-12-20_at_12.43.35_pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F086ENQNLF3/download/screenshot_2024-12-20_at_12.43.35_pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_360.png", "thumb_360_w": 360, "thumb_360_h": 146, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_480.png", "thumb_480_w": 480, "thumb_480_h": 194, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_720.png", "thumb_720_w": 720, "thumb_720_h": 291, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_800.png", "thumb_800_w": 800, "thumb_800_h": 324, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_960.png", "thumb_960_w": 960, "thumb_960_h": 388, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F086ENQNLF3-95389aa000/screenshot_2024-12-20_at_12.43.35_pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 414, "original_w": 2794, "original_h": 1130, "thumb_tiny": "AwATADDSHWmtnf3/AFpw6/jTW+/0/SgB1FFAoADQKD0oFADQTk/Wmuf3oHHbtTh94/WmP/rh+FDBD88UoNJ2pRQArdKQHg0rdKaOhoA//9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F086ENQNLF3/screenshot_2024-12-20_at_12.43.35_pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F086ENQNLF3-496c91d275", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "OeWSK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So we've used those filters for Verified / Likely to Engage and what we're seeing now is that a lot of the emails get the green check, but still blocked as spam...not sure if this is a Senders thing, or an Apollo data quality issue, but its not like we're sending a crazy amount of emails to random or unverified people purposefully"}]}]}]}, {"ts": "1734718047.294219", "text": "UI/UX ticket: <https://compiify.atlassian.net/browse/COM-4036>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14119::ab84e554582745019ed471da6ae127d9", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4036?atlOrigin=eyJpIjoiYmRkNGU0MGJkYjBlNGI0ZjgzZmFkZTI2NWYyYzEyZmQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4036 UI/UX changes needed>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14119::313a12d485e44fe9b5fee5152bf2db03", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14119\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4036\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4036", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "uEuMf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "UI/UX ticket: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4036"}]}]}]}, {"ts": "1734722266.743259", "text": "From CainWatters. Also, their renewal is for 5k and I'm fairly certain they're going to refuse to renew at that rate.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734722266.743259", "reply_count": 5, "files": [{"id": "F086RAG78AC", "created": 1734722241, "timestamp": 1734722241, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 191120, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F086RAG78AC/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F086RAG78AC/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F086RAG78AC-5ecc001ec7/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F086RAG78AC-5ecc001ec7/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F086RAG78AC-5ecc001ec7/image_360.png", "thumb_360_w": 360, "thumb_360_h": 267, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F086RAG78AC-5ecc001ec7/image_480.png", "thumb_480_w": 480, "thumb_480_h": 356, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F086RAG78AC-5ecc001ec7/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F086RAG78AC-5ecc001ec7/image_720.png", "thumb_720_w": 720, "thumb_720_h": 533, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F086RAG78AC-5ecc001ec7/image_800.png", "thumb_800_w": 800, "thumb_800_h": 593, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F086RAG78AC-5ecc001ec7/image_960.png", "thumb_960_w": 960, "thumb_960_h": 711, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F086RAG78AC-5ecc001ec7/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 758, "original_w": 1342, "original_h": 994, "thumb_tiny": "AwAjADDQYgN0o3n2oYndSbj6mgB2W9KMt6U3J9TRlvU0AOy3pSgnuKZuPqaVSSeTQAH73QflQMn+EflSkqDyTSgAjqaAG4P90flS5b0FLt9z+dLQA3LegpQTnkUtFADSBmlA+v50GigBaKKKACiiigD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F086RAG78AC/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F086RAG78AC-c4eb668e30", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "3vdsM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "From CainWatters. Also, their renewal is for 5k and I'm fairly certain they're going to refuse to renew at that rate."}]}]}]}, {"ts": "1734951360.750719", "text": "Travel day for me today - will be working and online if anything comes up, but guessing the airport wifi wont be great for calls", "user": "U07NBMXTL1E", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "UYJs3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Travel day for me today - will be working and online if anything comes up, but guessing the airport wifi wont be great for calls"}]}]}]}, {"ts": "1734969293.292569", "text": "<@U0690EB5JE5> since we are meeting later in the eve, I am okay to cancel leadeship standup.\n<@U07EJ2LP44S> do you have any agenda items to discuss, if not, lets get this time back", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "h8lxn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " since we are meeting later in the eve, I am okay to cancel lead<PERSON><PERSON> standup.\n"}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " do you have any agenda items to discuss, if not, lets get this time back"}]}]}]}, {"ts": "1734969332.895039", "text": "I am good too to cancel.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "PyXxS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am good too to cancel."}]}]}]}, {"ts": "1734969368.421829", "text": "And I don't see stand up invite until Friday. So our next regular stand up will be next Friday?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "USp7n", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And I don't see stand up invite until Friday. So our next regular stand up will be next Friday?"}]}]}]}, {"ts": "1734969401.825709", "text": "What will be availability of you both for rest of the week?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oNFG7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "What will be availability of you both for rest of the week?"}]}]}]}, {"ts": "1734969407.110969", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "EsPNH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1734969422.516889", "text": "I am fine to cancel", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "E3vNP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am fine to cancel"}]}]}]}, {"ts": "1734969461.874809", "text": "I am out until New Year’s, but I can get on calls if need be", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "CthgY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am out until New Year’s, but I can get on calls if need be"}]}]}]}, {"ts": "1734969484.110269", "text": "<@U07M6QKHUC9>?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yplc+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": "?"}]}]}]}, {"ts": "1734969516.465339", "text": "My goal today is to finalize Val Genesis’s environment for testing", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734969516.465339", "reply_count": 26, "blocks": [{"type": "rich_text", "block_id": "wvVPt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "My goal today is to finalize Val Genesis’s environment for testing"}]}]}]}, {"ts": "1734969564.742109", "text": "<@U0690EB5JE5> I am whatsapp call away", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "C+amN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I am whatsapp call away"}]}]}]}, {"ts": "1734969590.240969", "text": "and will be avaiable if we need to meet or discuss anything", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "white_check_mark", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "OfW72", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "and will be avaiable if we need to meet or discuss anything"}]}]}]}, {"ts": "1734975090.045959", "text": "<@U0690EB5JE5> There is  a cycle creation bug in VG env. I got a 'cannot fetch initial filters' error when I loaded the Eligibility Rules page, ~and it won't allow me to add any filters.;~ it eventually loaded. I was able to move past the step but it took maybe 45 seconds to a minute to respond on each filter.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734975090.045959", "reply_count": 12, "edited": {"user": "U07EJ2LP44S", "ts": "1734975241.000000"}, "blocks": [{"type": "rich_text", "block_id": "Q85xd", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " There is  a cycle creation bug in VG env. I got a 'cannot fetch initial filters' error when I loaded the Eligibility Rules page, "}, {"type": "text", "text": "and it won't allow me to add any filters.; ", "style": {"strike": true}}, {"type": "text", "text": "it eventually loaded. I was able to move past the step but it took maybe 45 seconds to a minute to respond on each filter."}]}]}]}, {"ts": "1734976510.751069", "text": "<@U0690EB5JE5> VG has an extremely large number of approvers. Is there any way to upload a role list so I do not have to manually change the roles on these 65 employees? We will also need to add the HRBP roles but that should only be 3 people.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734976510.751069", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "LQH8K", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " VG has an extremely large number of approvers. Is there any way to upload a role list so I do not have to manually change the roles on these 65 employees? We will also need to add the HRBP roles but that should only be 3 people."}]}]}]}, {"ts": "1735017912.670519", "text": "<@U0690EB5JE5> <@U07MH77PUBV> <https://help.lattice.com/hc/en-us/sections/15087351953303-Total-Compensation>", "user": "U07M6QKHUC9", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "nuz9L", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://help.lattice.com/hc/en-us/sections/15087351953303-Total-Compensation"}]}]}]}, {"ts": "1735064563.086229", "text": "Valgenesis is all set up now, thank you! We still need the roles assigned but the cycle is launched.\n\nIn the future, can we upgrade the environment as soon as we're out of data mode and into needing to create cycles and test?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1735064563.086229", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "KvUUD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Valgenesis is all set up now, thank you! We still need the roles assigned but the cycle is launched.\n\nIn the future, can we upgrade the environment as soon as we're out of data mode and into needing to create cycles and test?"}]}]}]}, {"ts": "1735281281.284009", "text": "<@U0690EB5JE5> here are the demo scenarios requested by analyst <PERSON><PERSON> Insights. We can go over this tomorrow or or Monday.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1735281281.284009", "reply_count": 8, "files": [{"id": "F086PAXMXE0", "created": 1735281262, "timestamp": 1735281262, "name": "Cycle Management Demo Scenarios.docx", "title": "Cycle Management Demo Scenarios.docx", "mimetype": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filetype": "docx", "pretty_type": "Word Document", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 109134, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F086PAXMXE0/cycle_management_demo_scenarios.docx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F086PAXMXE0/download/cycle_management_demo_scenarios.docx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F086PAXMXE0-def0f50776/cycle_management_demo_scenarios_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F086PAXMXE0-def0f50776/cycle_management_demo_scenarios_thumb_pdf.png", "thumb_pdf_w": 935, "thumb_pdf_h": 1210, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F086PAXMXE0/cycle_management_demo_scenarios.docx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F086PAXMXE0-44e7b32d3d", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "79ggX", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " here are the demo scenarios requested by analyst <PERSON><PERSON>. We can go over this tomorrow or or Monday."}]}]}]}, {"ts": "1735573638.354659", "text": "I know <PERSON> is out so thinking standup today is cancelled, so just going to send over an async update for everyone:\n\n• One final week here where we'll probably be kinda spotty with outreach - I've cut a lot of our numbers in half just to have some stuff going out, but it's understandably very quiet and mostly OOO replies\n• Vestwell can do a call on 1/14 at 1p ET to discuss pre-cycle analytics\n• To that point, here's an email I've been sending / will continue to send to all of the conversations we've already had in the 400-500+ employee range to try and get as many of those people set up for feedback calls in these next few weeks of January as possible. Please let me know if you'd like me to change any of the copy here:\nHey &lt;first name&gt;,\n\n&lt;Quick blurb on Xmas / New Years / holidays / etc&gt;\n\nI know when we last spoke, we decided that it might not be the best fit for you to join Stride’s Customer Advisory Board, or to roll out our compensation planning beta product for &lt;company name&gt; at this point in time.\n\nAs we’ve had more and more of these conversations, a common theme has started to pop up and we’d love to get your take on it. A number of our advisors, customers, and prospects have expressed interest in a software that can handle pre-cycle analysis, in hopes of improving budgeting and budget planning, compensation and data analytics, and general scenario analysis.\n\nAs we mentioned to you on our call, we’re constantly looking to take in the feedback of HR and People Ops leaders in order to guide our own product roadmap. Given some of the interest we’ve already gotten, we were hoping to expand our sample size just a bit - would this type of tool to handle pre-cycle analysis be something you’d potentially be interested in?\n\nWould love any or all feedback you may have on the matter. Thanks so much!\n\nMichael and the Stride team", "user": "U07NBMXTL1E", "type": "message", "blocks": [{"type": "rich_text", "block_id": "CRHnE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I know <PERSON> is out so thinking standup today is cancelled, so just going to send over an async update for everyone:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "One final week here where we'll probably be kinda spotty with outreach - I've cut a lot of our numbers in half just to have some stuff going out, but it's understandably very quiet and mostly OOO replies"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Vestwell can do a call on 1/14 at 1p ET to discuss pre-cycle analytics"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "To that point, here's an email I've been sending / will continue to send to all of the conversations we've already had in the 400-500+ employee range to try and get as many of those people set up for feedback calls in these next few weeks of January as possible. Please let me know if you'd like me to change any of the copy here:"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nHey <first name>,\n\n<Quick blurb on Xmas / New Years / holidays / etc>\n\nI know when we last spoke, we decided that it might not be the best fit for you to join Stride’s Customer Advisory Board, or to roll out our compensation planning beta product for <company name> at this point in time.\n\nAs we’ve had more and more of these conversations, a common theme has started to pop up and we’d love to get your take on it. A number of our advisors, customers, and prospects have expressed interest in a software that can handle pre-cycle analysis, in hopes of improving budgeting and budget planning, compensation and data analytics, and general scenario analysis.\n\nAs we mentioned to you on our call, we’re constantly looking to take in the feedback of HR and People Ops leaders in order to guide our own product roadmap. Given some of the interest we’ve already gotten, we were hoping to expand our sample size just a bit - would this type of tool to handle pre-cycle analysis be something you’d potentially be interested in?\n\nWould love any or all feedback you may have on the matter. Thanks so much!\n\n<PERSON> and the Stride team"}]}]}]}]}