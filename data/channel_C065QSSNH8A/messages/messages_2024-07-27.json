{"date": "2024-07-27", "channel_id": "C065QSSNH8A", "message_count": 11, "messages": [{"ts": "1722048617.483739", "text": ":+1: ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zNKS9", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "+1", "unicode": "1f44d"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1722045019.009799", "text": "PT", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "t1s78", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "PT"}]}]}]}, {"ts": "1722045008.554749", "text": "Yes i have bandwidth on Sunday afternoon ", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OGxte", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes i have bandwidth on Sunday afternoon "}]}]}]}, {"ts": "1722044738.985969", "text": "<@U04DKEFP1K8> Are you planning to retest the issues over the weekend? I will try work on the merit creation issues based on that.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ta9LS", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Are you planning to retest the issues over the weekend? I will try work on the merit creation issues based on that."}]}]}]}, {"ts": "1722042922.502349", "text": "Sure <@U04DKEFP1K8>. Will take a look", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7FWBK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": ". Will take a look"}]}]}]}, {"ts": "1722040983.234669", "text": "<@U0690EB5JE5> Reported couple of error related to merit creation here <https://docs.google.com/document/d/1SLa4vJWY7NF8OqvVsT1bUjGek1QAlADusolsW8gHObM/edit>.\n(<PERSON><PERSON> wants us to delete couple of employees, I was able to delete but merit creation fails afterwards)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722040983.234669", "reply_count": 1, "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "YKPm0", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Reported couple of error related to merit creation here "}, {"type": "link", "url": "https://docs.google.com/document/d/1SLa4vJWY7NF8OqvVsT1bUjGek1QAlADusolsW8gHObM/edit"}, {"type": "text", "text": ".\n(<PERSON><PERSON> wants us to delete couple of employees, I was able to delete but merit creation fails afterwards)"}]}]}]}, {"ts": "1722034964.739559", "text": "<@U0690EB5JE5> Verified previously reported bonus issues. Reported new issues for bonus and OTE here <https://docs.google.com/document/d/1SXi8D1g1GQJva0C8q5ZBHWrqJ-j9AoSwz10ikzOw_Hs/edit>. I will be available in your evening on Saturday for any clarification on zoom.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722034964.739559", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "zhYX/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Verified previously reported bonus issues. Reported new issues for bonus and OTE here "}, {"type": "link", "url": "https://docs.google.com/document/d/1SXi8D1g1GQJva0C8q5ZBHWrqJ-j9AoSwz10ikzOw_Hs/edit"}, {"type": "text", "text": ". I will be available in your evening on Saturday for any clarification on zoom."}]}]}]}, {"ts": "**********.382069", "text": "<@U0690EB5JE5> can we create login credential for one of our advisors <PERSON> (<mailto:<EMAIL>|<EMAIL>>) for either the test account or demo account.  He will run a test cycle and give us his feedback from a Comp Leader perspective.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.382069", "reply_count": 7, "blocks": [{"type": "rich_text", "block_id": "ebrvG", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we create login credential for one of our advisors <PERSON> ("}, {"type": "link", "url": "mailto:<EMAIL>", "text": "<EMAIL>"}, {"type": "text", "text": ") for either the test account or demo account.  He will run a test cycle and give us his feedback from a Comp Leader perspective."}]}]}]}, {"ts": "**********.324599", "text": "<@U0690EB5JE5> I will time time on you cal to go over the comp builder testing together. I still keep getting stuck every time I try different configurations.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "FfAHb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I will time time on you cal to go over the comp builder testing together. I still keep getting stuck every time I try different configurations."}]}]}]}, {"ts": "**********.382399", "text": "I will take this over, and will complete it.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YrbNi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will take this over, and will complete it."}]}]}]}, {"ts": "1722019396.269549", "text": "<@U04DKEFP1K8> what is the ETA of finalizing roadmap implementation sheet?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722019396.269549", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "Cdits", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what is the ETA of finalizing roadmap implementation sheet?"}]}]}]}], "created_at": "2025-05-22T21:35:34.615784"}