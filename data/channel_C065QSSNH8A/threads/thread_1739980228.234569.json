{"thread_ts": "**********.234569", "channel_id": "C065QSSNH8A", "reply_count": 17, "replies": [{"ts": "1739980274.418289", "text": "You can probably use <http://demo.stridehr.io|demo.stridehr.io> once <PERSON><PERSON><PERSON> finishes his demo.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "dyvAu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "You can probably use "}, {"type": "link", "url": "http://demo.stridehr.io", "text": "demo.stridehr.io"}, {"type": "text", "text": " once <PERSON><PERSON><PERSON> finishes his demo."}]}]}]}, {"ts": "1739980304.493049", "text": "I have to recreate the whole cycle I don't think I'll have time for a Friday morning demo", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "aTMzM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have to recreate the whole cycle I don't think I'll have time for a Friday morning demo"}]}]}]}, {"ts": "1739980363.129769", "text": "I can help with that. I can still bring up another ENV  let me know.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "8tIKi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can help with that. I can still bring up another ENV  let me know."}]}]}]}, {"ts": "**********.941689", "text": "His demo is late tomorrow afternoon my time; there's no way I can turn that around. If you can get the account set up just like the curanahealthmip account that's fine, but it would be need to be done by my morning Friday, and ready for a demo to the executives", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "H3dx1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "His demo is late tomorrow afternoon my time; there's no way I can turn that around. If you can get the account set up just like the curanahealthmip account that's fine, but it would be need to be done by my morning Friday, and ready for a demo to the executives"}]}]}]}, {"ts": "**********.169789", "text": "Let me bring up <http://qa.stridehr.io|qa.stridehr.io>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.234569", "reactions": [{"name": "white_check_mark", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "XzA0V", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me bring up "}, {"type": "link", "url": "http://qa.stridehr.io", "text": "qa.stridehr.io"}]}]}]}, {"ts": "**********.848369", "text": "This doesn't look up yet, can you let me know when it is? I need time to replicate the curanahealthmip cycle, or if you can do any of that overnight that's great too", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "EWadg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This doesn't look up yet, can you let me know when it is? I need time to replicate the curanahealthmip cycle, or if you can do any of that overnight that's great too"}]}]}]}, {"ts": "**********.836809", "text": "It’s up but there seems to be some issue with the ENV. Will take a look ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "qn7tQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It’s"}, {"type": "text", "text": " up but there seems to be some issue with the ENV. Will take a look "}]}]}]}, {"ts": "**********.433199", "text": "<@U07EJ2LP44S> <http://qa.stridhr.io|qa.stridhr.io> is online.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "VhdzF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://qa.stridhr.io", "text": "qa.stridhr.io"}, {"type": "text", "text": " is online."}]}]}]}, {"ts": "1740021818.913059", "text": "I need to focus on Adjustment letters today and planning to close tomorrow. I will see if I can get time to replicate the cycle today.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "chAxu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I need to focus on Adjustment letters today and planning to close tomorrow. I will see if I can get time to replicate the cycle today."}]}]}]}, {"ts": "1740059967.910209", "text": "If it’s back up, I can create the cycle", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "HCW/t", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If it’s back up, I can create the cycle"}]}]}]}, {"ts": "1740060246.556379", "text": ":raised_hands: ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "3Wg9w", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "raised_hands", "unicode": "1f64c"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1740060407.632429", "text": "By the way, <PERSON> at Valgenesis did ask about letters today and if there’s a cut off date for the data. How long do we need after they stop making changes to the data to generate the letters?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "n7b/8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "By the way, <PERSON> at Valgenesis did ask about letters today and if there’s a cut off date for the data. How long do we need after they stop making changes to the data to generate the letters?"}]}]}]}, {"ts": "1740060475.402239", "text": "I can share the next day. Will try to share letters with current state by tomorrow or Monday worst case.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.234569", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "BTvFN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can share the next day. Will try to share letters with current state by tomorrow or Monday worst case."}]}]}]}, {"ts": "1740152174.247849", "text": "<@U07EJ2LP44S> Please find adjustment letters for Val<PERSON> with current cycle state. Please let me know if any issues.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.234569", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "xMptv", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please find adjustment letters for <PERSON><PERSON> with current cycle state. Please let me know if any issues."}]}]}]}, {"ts": "**********.774519", "text": "In the account? I tried downloadin one and it's not working, not sure if I should be looking elsewhere", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "dtIeP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In the account? I tried downloadin one and it's not working, not sure if I should be looking elsewhere"}]}]}]}, {"ts": "**********.938289", "text": "OH I see. LOL", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "zdGx3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OH I see. LOL"}]}]}]}, {"ts": "**********.721729", "text": "Yes, Attached in slack.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.234569", "blocks": [{"type": "rich_text", "block_id": "VBYXM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, Attached in slack."}]}]}]}], "created_at": "2025-05-22T22:00:49.360871"}