{"date": "2024-08-07", "channel_id": "C065QSSNH8A", "message_count": 12, "messages": [{"ts": "1723046798.079189", "text": "<@U04DKEFP1K8> Were templates shared with Diversified?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723046798.079189", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "tA/Ld", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Were templates shared with Diversified?"}]}]}]}, {"ts": "1723046421.520119", "text": "Sure\nHere are top things on my mind at this point\n1. We now have 3 customer implementation for Nov'24 ( Practifi, Alayacare and Degenkolb). We need to finalize customer specific feature development asap, it feels like we need a bit of push here to get things in motion.\n    a. Alayacare needs a major feature ( support budgeting in CAD)\n2. Let's keep sharing daily update on Diversified Energy", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "eyes", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "crZO2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sure\nHere are top things on my mind at this point\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We now have 3 customer implementation for Nov'24 ( Practifi, Alayacare and Degenkolb). We need to finalize customer specific feature development asap, it feels like we need a bit of push here to get things in motion."}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Alayacare needs a major feature ( support budgeting in CAD)"}]}], "style": "ordered", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let's keep sharing daily update on Diversified Energy"}]}], "style": "ordered", "indent": 0, "offset": 1, "border": 0}]}]}, {"ts": "1723045889.609799", "text": "Nothing on my end!", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yVwDH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Nothing on my end!"}]}]}]}, {"ts": "1723045484.161579", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> Do you have any specific agenda items to discuss during the eng call? If not we can cancel it today and I can focus on reviewing resumes of 120 AE candidates who have applied so far.", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "SWpzJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Do you have any specific agenda items to discuss during the eng call? If not we can cancel it today and I can focus on reviewing resumes of 120 AE candidates who have applied so far."}]}]}]}, {"ts": "1723041452.028829", "text": "Will share eng updates. Sorry about the short notice.", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1723044336.000000"}, "blocks": [{"type": "rich_text", "block_id": "lSsCZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will share eng updates. Sorry about the short notice"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1723041437.710559", "text": "I had come to meet a friend from UK and getting late. I might be late to eng discussion or miss it.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "eu5fI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I had come to meet a friend from UK and getting late. I might be late to eng discussion or miss it."}]}]}]}, {"ts": "1723037871.800629", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> For Degenkolb's multilple line items for one employee, do we need to edit the employee id to be xxxxxx-1 and xxxxxx-2 or similar so they will upload as two unique lines?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1723037871.800629", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Vw2jl", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For Degenkolb's multilple line items for one employee, do we need to edit the employee id to be xxxxxx-1 and xxxxxx-2 or similar so they will upload as two unique lines?"}]}]}]}, {"ts": "1723019940.385069", "text": "<@U04DS2MBWP4> whats the plan on cycle builder v2 and merit planning new design? There are some small enhancements we need to pick up on cycle builder (from SDF list). Based on when we would have the new designs ready we can prioritize them whether to address them now or along with new designs to avoid rework.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1723019940.385069", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "4vA/R", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " whats the plan on cycle builder v2 and merit planning new design? There are some small enhancements we need to pick up on cycle builder (from SDF list). Based on when we would have the new designs ready we can prioritize them whether to address them now or along with new designs to avoid rework."}]}]}]}, {"ts": "1722981864.687969", "text": "<!here> Confirming <PERSON> and I were on the call with vercara earlier in the day and we have resolved their issue. Their were couple minor issue that were observed , i will discuss the same with <PERSON><PERSON><PERSON> later tonight.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1722981864.687969", "reply_count": 4, "edited": {"user": "U04DKEFP1K8", "ts": "1722981880.000000"}, "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "8qXyP", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Confirming <PERSON> and I were on the call with vercara earlier in the day and we have resolved their issue. Their were couple minor issue that were observed , i will discuss the same with <PERSON><PERSON><PERSON> later tonight."}]}]}]}, {"ts": "1722971228.934379", "text": "feel better soon", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "y6Bmy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "feel better soon"}]}]}]}, {"ts": "1722971224.258189", "text": "yep, we can cancel it", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "vE90M", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yep, we can cancel it"}]}]}]}, {"ts": "1722971044.705809", "text": "Do we need to have our implementation meeting this afternoon? I'm running down a bit on energy", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722971044.705809", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "fDu8u", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we need to have our implementation meeting this afternoon? I'm running down a bit on energy"}]}]}]}], "created_at": "2025-05-22T21:35:34.631965"}