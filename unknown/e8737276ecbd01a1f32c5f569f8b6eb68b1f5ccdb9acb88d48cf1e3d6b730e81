#!/usr/bin/env python
"""
<PERSON>ript to test the LocalSlackSourceInterface with the IngestionService.
"""

import os
import sys
import django
import json
import logging
from datetime import datetime

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from django.db import transaction
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk, EmbeddingMetadata, DocumentContent
from apps.core.utils.vectorstore import get_qdrant_client
from apps.documents.interfaces.local_slack import LocalSlackSourceInterface
from apps.documents.services.ingestion_service import IngestionService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_database():
    """Clean the database by removing all documents, chunks, and related data."""
    logger.info("Starting database cleaning process...")

    # Delete all embedding metadata
    embedding_count = EmbeddingMetadata.objects.count()
    EmbeddingMetadata.objects.all().delete()
    logger.info(f"Deleted {embedding_count} embedding metadata records")

    # Delete all document chunks
    chunk_count = DocumentChunk.objects.count()
    DocumentChunk.objects.all().delete()
    logger.info(f"Deleted {chunk_count} document chunks")

    # Delete all document content
    content_count = DocumentContent.objects.count()
    DocumentContent.objects.all().delete()
    logger.info(f"Deleted {content_count} document content records")

    # Delete all raw documents
    doc_count = RawDocument.objects.count()
    RawDocument.objects.all().delete()
    logger.info(f"Deleted {doc_count} raw documents")

    # Delete all document sources
    source_count = DocumentSource.objects.count()
    DocumentSource.objects.all().delete()
    logger.info(f"Deleted {source_count} document sources")

    # Clean Qdrant vector database
    try:
        # Get Qdrant client
        client = get_qdrant_client()

        # Get all tenants
        tenants = Tenant.objects.all()

        # Delete collections for each tenant
        for tenant in tenants:
            collection_name = f"{tenant.slug}_chunks"
            try:
                if client.collection_exists(collection_name):
                    client.delete_collection(collection_name)
                    logger.info(f"Deleted vector collection: {collection_name}")
            except Exception as e:
                logger.error(f"Error deleting vector collection {collection_name}: {str(e)}")
    except Exception as e:
        logger.error(f"Error cleaning vector database: {str(e)}")

    logger.info("Database cleaning completed successfully")

def create_document_source(tenant):
    """Create a document source for the LocalSlackSourceInterface."""
    try:
        # Define the configuration for the LocalSlackSourceInterface
        config = {
            "data_dir": "../data/",  # Path to the parent directory containing channel_C065QSSNH8A
            "time_period": "custom",
            "custom_days": 730,  # Two years
            "enable_summary": False,
            "quality_threshold": 0.3,
        }

        # Create the document source
        source = DocumentSource.objects.create(
            tenant=tenant,
            name="Local Slack Data",
            source_type="local_slack",
            config=config,
            is_active=True
        )

        logger.info(f"Created document source: {source.name} ({source.source_type})")
        return source

    except Exception as e:
        logger.error(f"Error creating document source: {str(e)}")
        return None

def ingest_slack_data(tenant, source):
    """Ingest Slack data using the IngestionService."""
    try:
        # Create ingestion service
        ingestion_service = IngestionService(tenant=tenant)

        # Process the source
        processed, failed = ingestion_service.process_source(source)
        
        logger.info(f"Ingestion completed: {processed} documents processed, {failed} documents failed")
        return processed, failed

    except Exception as e:
        logger.error(f"Error ingesting data: {str(e)}")
        return 0, 0

def print_database_stats():
    """Print database statistics."""
    logger.info("Database Statistics:")
    logger.info(f"Raw Documents: {RawDocument.objects.count()}")
    logger.info(f"Document Chunks: {DocumentChunk.objects.count()}")
    logger.info(f"Document Sources: {DocumentSource.objects.count()}")
    logger.info(f"Embedding Metadata: {EmbeddingMetadata.objects.count()}")

def verify_data_integrity():
    """Verify data integrity across databases and tables."""
    logger.info("Verifying data integrity...")
    
    # Check if all raw documents have chunks
    raw_docs_without_chunks = RawDocument.objects.filter(documentchunk__isnull=True).count()
    logger.info(f"Raw documents without chunks: {raw_docs_without_chunks}")
    
    # Check if all chunks have embeddings
    chunks_without_embeddings = DocumentChunk.objects.filter(embeddingmetadata__isnull=True).count()
    logger.info(f"Chunks without embeddings: {chunks_without_embeddings}")
    
    # Check if all embeddings have corresponding chunks
    try:
        # Get Qdrant client
        client = get_qdrant_client()
        
        # Get all tenants
        tenants = Tenant.objects.all()
        
        for tenant in tenants:
            collection_name = f"{tenant.slug}_chunks"
            try:
                if client.collection_exists(collection_name):
                    # Get collection info
                    collection_info = client.get_collection(collection_name)
                    vector_count = collection_info.vectors_count
                    
                    # Compare with database counts
                    db_embedding_count = EmbeddingMetadata.objects.filter(chunk__document__tenant=tenant).count()
                    
                    logger.info(f"Tenant {tenant.name}:")
                    logger.info(f"  Vector count in Qdrant: {vector_count}")
                    logger.info(f"  Embedding count in DB: {db_embedding_count}")
                    
                    if vector_count != db_embedding_count:
                        logger.warning(f"  Mismatch between vector count and embedding count!")
            except Exception as e:
                logger.error(f"Error checking collection {collection_name}: {str(e)}")
    except Exception as e:
        logger.error(f"Error verifying vector database: {str(e)}")
    
    logger.info("Data integrity verification completed")

def main():
    """Main function to test the LocalSlackSourceInterface with the IngestionService."""
    logger.info("Starting test of LocalSlackSourceInterface with IngestionService...")

    # Clean the database first
    clean_database()

    # Get or create tenant
    tenant, created = Tenant.objects.get_or_create(
        slug="default",
        defaults={"name": "Default Tenant"}
    )

    # Print initial database stats
    logger.info("Initial database stats:")
    print_database_stats()

    # Create document source
    source = create_document_source(tenant)
    if not source:
        logger.error("Failed to create document source. Exiting.")
        return

    # Ingest Slack data
    logger.info("Ingesting Slack data...")
    processed, failed = ingest_slack_data(tenant, source)

    # Print final database stats
    logger.info("Final database stats after ingestion:")
    print_database_stats()
    
    # Verify data integrity
    verify_data_integrity()

    logger.info("Test of LocalSlackSourceInterface with IngestionService completed.")

if __name__ == "__main__":
    main()
