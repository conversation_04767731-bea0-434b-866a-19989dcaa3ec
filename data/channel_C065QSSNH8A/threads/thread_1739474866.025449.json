{"thread_ts": "1739474866.025449", "channel_id": "C065QSSNH8A", "reply_count": 7, "replies": [{"ts": "1739474946.277669", "text": "OH and the performance rating # is showing instead of the name of the performance rating.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739474866.025449", "blocks": [{"type": "rich_text", "block_id": "klZj+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OH and the performance rating # is showing instead of the name of the performance rating."}]}]}]}, {"ts": "1739475003.730139", "text": "They also were confused and thought they could run their whole performance cycle in here; that they could ask their managers to both rate the employees and also do the merit increases. They're clear now, but FYI. That's also why they were asking about the regrettable turnover question earlier, they thought they could do it in this whole process.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739474866.025449", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "UDztN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "They also were confused and thought they could run their whole performance cycle in here; that they could ask their managers to both rate the employees and also do the merit increases. They're clear now, but FYI. That's also why they were asking about the regrettable turnover question earlier, they thought they could do it in this whole process."}]}]}]}, {"ts": "1739478023.203329", "text": "<@U07EJ2LP44S> when can they send over the data they need us for 401(k) and other benefits", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1739474866.025449", "blocks": [{"type": "rich_text", "block_id": "yFr/Y", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " when can they send over the data they need us for 401(k) and other benefits"}]}]}]}, {"ts": "1739528922.710759", "text": "<@U07EJ2LP44S> There are few validations like below which would have blocked to proceed. Unfortunately UX is bad here", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739474866.025449", "files": [{"id": "F08DQ8SR8TB", "created": 1739528919, "timestamp": 1739528919, "name": "Screenshot 2025-02-14 at 3.58.33 PM.png", "title": "Screenshot 2025-02-14 at 3.58.33 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 38326, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08DQ8SR8TB/screenshot_2025-02-14_at_3.58.33___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08DQ8SR8TB/download/screenshot_2025-02-14_at_3.58.33___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DQ8SR8TB-37736f104a/screenshot_2025-02-14_at_3.58.33___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DQ8SR8TB-37736f104a/screenshot_2025-02-14_at_3.58.33___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DQ8SR8TB-37736f104a/screenshot_2025-02-14_at_3.58.33___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 86, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DQ8SR8TB-37736f104a/screenshot_2025-02-14_at_3.58.33___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 115, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DQ8SR8TB-37736f104a/screenshot_2025-02-14_at_3.58.33___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08DQ8SR8TB-37736f104a/screenshot_2025-02-14_at_3.58.33___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 173, "original_w": 793, "original_h": 190, "thumb_tiny": "AwALADCj3pePb8qSjJ9aAA/hR+VJk0ZoAX8qSjNFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08DQ8SR8TB/screenshot_2025-02-14_at_3.58.33___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08DQ8SR8TB-b2e30eb8e9", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "QPOm+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " There are few validations like below which would have blocked to proceed. Unfortunately UX is bad here"}]}]}]}, {"ts": "1739529007.845589", "text": "I have updated the matrix ranges for merit increase. Let me know if its the same for promotions as well, I will take care of it from backend. Its definitely painful to update the matrix from UI.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739474866.025449", "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have updated the matrix ranges for merit increase. Let me know if its the same for promotions as well, I will take care of it from backend. Its definitely painful to update the matrix from UI."}]}]}]}, {"ts": "1739540276.200399", "text": "Thank you! I’ll get the final numbers for promotions, which is different.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1739474866.025449", "blocks": [{"type": "rich_text", "block_id": "cDphN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you! I’ll get the final numbers for promotions, which is different."}]}]}]}, {"ts": "1739766363.866509", "text": "<@U07EJ2LP44S> We are targeting to wrap up the Total Rewards thing by end of this week. One question I have is, After incorporating their feedback, This pretty much covers their adjustment letters. Could you please check if they are still expecting adjustment letters separately? which I feel is redundant. Can we convince them to use Total rewards instead of adjustment letters.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1739474866.025449", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1739766493.000000"}, "blocks": [{"type": "rich_text", "block_id": "3oIqH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We are targeting to wrap up the Total Rewards thing by end of this week. One question I have is, After incorporating their feedback, This pretty much covers their adjustment letters. Could you please check if they are still expecting adjustment letters separately? which I feel is redundant. Can we convince them to use Total rewards instead of adjustment letters."}]}]}]}], "created_at": "2025-05-22T22:00:49.364410"}