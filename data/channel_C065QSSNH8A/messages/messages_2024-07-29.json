{"date": "2024-07-29", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1722267417.105129", "text": "Welcome to the Team <@U07EJ2LP44S>. Excited to learn from you :partyparrot: ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "WShuH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Welcome to the Team "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": ". Excited to learn from you "}, {"type": "emoji", "name": "partyparrot"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1722267371.475339", "text": "<@U0690EB5JE5> we should add integrations to the priority list. Ideally we should reach a point where we don't have to deal with manual uploads of emp data and comp data", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722267371.475339", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "2Z46M", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " we should add integrations to the priority list. Ideally we should reach a point where we don't have to deal with manual uploads of emp data and comp data"}]}]}]}, {"ts": "1722266470.194269", "text": "Hello! So excited!", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "tada", "users": ["U04DKEFP1K8", "U0690EB5JE5"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "d/Y46", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hello! So excited!"}]}]}]}, {"ts": "1722266379.987649", "text": "HI <@U07EJ2LP44S> welcome to the team! Looking forward to doing some great things together :partying_face::tada:", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "DcoSf", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "HI "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " welcome to the team! Looking forward to doing some great things together "}, {"type": "emoji", "name": "partying_face", "unicode": "1f973"}, {"type": "emoji", "name": "tada", "unicode": "1f389"}]}]}]}, {"ts": "1722262856.548989", "text": "Sounds good. ", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ndoCd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sounds good. "}]}]}]}, {"ts": "1722244823.629049", "text": "Couple of Agenda items for today if nothing higher priority.\n• Engineering priorities to pick up from next week. We will be mostly done with bugs and other current work this week, so we should start putting clear requirements for next priorities\n• Activity Log usecase. <@U04DS2MBWP4> I understand what Activity log is, I am still not clear how this helps additionally vs Audit Log and Employee History together.\n• Adjustment letters Template Generator. There is one more important component of Adjustment letters which we haven't discussed i.e. current process of template generation, its purely manual and we need to think about self serve for this.\n", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1722244985.000000"}, "blocks": [{"type": "rich_text", "block_id": "BrX8E", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Couple of Agenda items for today if nothing higher priority.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Engineering priorities to pick up from next week. We will be mostly done with bugs and other current work this week, so we should start putting clear requirements for next priorities"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Activity Log usecase. "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I understand what Activity log is, I am still not clear how this helps additionally vs Audit Log and Employee History together."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Adjustment letters Template Generator. There is one more important component of Adjustment letters which we haven't discussed i.e. current process of template generation, its purely manual and we need to think about self serve for this."}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, {"ts": "1722276859.218919", "text": "<!here> I am ready to send out communication to Vercara to update their environment to input Bonus and OTE amounts.\n<@U0690EB5JE5> Good work by the team!!\n<@U07EJ2LP44S> I will be looping you in the email thread with them and we can work out when these updates can go via you going forward.", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}, {"name": "100", "users": ["U04DS2MBWP4"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "SgKjW", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " I am ready to send out communication to Vercara to update their environment to input Bonus and OTE amounts.\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Good work by the team!!\n"}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I will be looping you in the email thread with them and we can work out when these updates can go via you going forward."}]}]}]}, {"ts": "1722273870.865609", "text": "<@U0690EB5JE5> can we pls create social login for <PERSON> for all demo, test and customer environments?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1722273870.865609", "reply_count": 10, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "PvQd7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can we pls create social login for <PERSON> for all demo, test and customer environments?"}]}]}]}, {"ts": "1722272504.689719", "text": "Roadmap sheet that I was sharing the in meeting today.\n<https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=286802099#gid=286802099>", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U04DS2MBWP4"], "count": 1}], "files": [{"id": "F06MMTVHJCA", "created": 1709446944, "timestamp": 1709446944, "name": "Project Plan - March to May", "title": "Roadmap - <PERSON><PERSON><PERSON>", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk", "external_url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06MMTVHJCA-357671be4e/project_plan_-_march_to_may_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSJOelJlvSnUUANy3pSjPelooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06MMTVHJCA/project_plan_-_march_to_may", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "FP93F", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Roadmap sheet that I was sharing the in meeting today.\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/149XI4sJ1gCSrf-keX1EpTmvUxhEO-NMOEu-rtN1tkUk/edit?gid=286802099#gid=286802099"}]}]}]}], "created_at": "2025-05-22T21:35:34.615204"}