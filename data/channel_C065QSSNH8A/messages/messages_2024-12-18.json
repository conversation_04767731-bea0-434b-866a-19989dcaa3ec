{"date": "2024-12-18", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1734539252.220109", "text": "<https://drive.google.com/file/d/1ru5cawpNNMml5MaWOI3L7RkOWRjqzC67/view?usp=drive_link>", "user": "U07M6QKHUC9", "type": "message", "files": [{"id": "F084J4J99UJ", "created": 1733862721, "timestamp": 1733862721, "name": "BetterComp Demo.MOV", "title": "BetterComp Demo.MOV", "mimetype": "video/quicktime", "filetype": "mov", "pretty_type": "QuickTime Movie", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1ru5cawpNNMml5MaWOI3L7RkOWRjqzC67", "external_url": "https://drive.google.com/file/d/1ru5cawpNNMml5MaWOI3L7RkOWRjqzC67/view?usp=drive_link", "url_private": "https://drive.google.com/file/d/1ru5cawpNNMml5MaWOI3L7RkOWRjqzC67/view?usp=drive_link", "media_display_type": "video", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F084J4J99UJ/bettercomp_demo.mov", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "qDLVg", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://drive.google.com/file/d/1ru5cawpNNMml5MaWOI3L7RkOWRjqzC67/view?usp=drive_link"}]}]}]}, {"ts": "1734538629.366739", "text": "Vestwell Call: <https://app.fireflies.ai/view/Stride-Vestwell-Product-Demo::uaFDJCGWqFuXgqja?ref=recap&amp;track=uaFDJCGWqFuXgqja&amp;sg=nb&amp;utm_content=view_recap_cta>", "user": "U07NBMXTL1E", "type": "message", "attachments": [{"from_url": "https://app.fireflies.ai/view/Stride-Vestwell-Product-Demo::uaFDJCGWqFuXgqja?ref=recap&track=uaFDJCGWqFuXgqja&sg=nb&utm_content=view_recap_cta", "thumb_url": "https://files.fireflies.ai/app/img/preview.png", "thumb_width": 1280, "thumb_height": 720, "image_url": "https://files.fireflies.ai/app/img/preview.png", "image_width": 1280, "image_height": 720, "image_bytes": 188965, "service_icon": "https://app.fireflies.ai/favicon.ico", "id": 1, "original_url": "https://app.fireflies.ai/view/Stride-Vestwell-Product-Demo::uaFDJCGWqFuXgqja?ref=recap&amp;track=uaFDJCGWqFuXgqja&amp;sg=nb&amp;utm_content=view_recap_cta", "fallback": "Stride  Vestwell Product Demo - Meeting recording by Fireflies.ai", "title": "Stride  Vestwell Product Demo - Meeting recording by Fireflies.ai", "title_link": "https://app.fireflies.ai/view/Stride-Vestwell-Product-Demo::uaFDJCGWqFuXgqja?ref=recap&track=uaFDJCGWqFuXgqja&sg=nb&utm_content=view_recap_cta", "service_name": "app.fireflies.ai"}], "blocks": [{"type": "rich_text", "block_id": "uqO5f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Vestwell Call: "}, {"type": "link", "url": "https://app.fireflies.ai/view/Stride-Vestwell-Product-Demo::uaFDJCGWqFuXgqja?ref=recap&track=uaFDJCGWqFuXgqja&sg=nb&utm_content=view_recap_cta"}]}]}]}, {"ts": "1734518147.887649", "text": "<@U07M6QKHUC9> We need to add another task to the list. CWA SFTP integration.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9DEw/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " We need to add another task to the list. CWA SFTP integration."}]}]}]}, {"ts": "1734517942.652269", "text": "Hi <@U07EJ2LP44S> for \"Diversified SSO\" could you please share the instructions doc you sent to them.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734517942.652269", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "0ayXp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hi "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " for \"Diversified SSO\" could you please share the instructions doc you sent to them."}]}]}]}, {"ts": "1734470992.623349", "text": "<@U0690EB5JE5> Looks like the Degenkolb letters are good except one title. The 'Design Engineer' title is a Professional Exempt position but they were put onto the non exempt template. Can we fix those only?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734470992.623349", "reply_count": 2, "files": [{"id": "F085XKECNGZ", "created": 1734470990, "timestamp": 1734470990, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 58470, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F085XKECNGZ/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F085XKECNGZ/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F085XKECNGZ-4755ef078f/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F085XKECNGZ-4755ef078f/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F085XKECNGZ-4755ef078f/image_360.png", "thumb_360_w": 360, "thumb_360_h": 210, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F085XKECNGZ-4755ef078f/image_480.png", "thumb_480_w": 480, "thumb_480_h": 280, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F085XKECNGZ-4755ef078f/image_160.png", "original_w": 702, "original_h": 410, "thumb_tiny": "AwAcADC66MMkMfpgUqxnGS2ePSnSfdOP5U5fuj6VNwIWRgcBifwHFPEZxy2fwpH5P3T/AN8g1IDkdMUXAhKMDjcfyFO8s7fvc/Shxl+hOPYGpD0ouAj/AHemfwzSr90fSggEcjNKAAMCiwEUgy33c/8AAc1JSFFY5Kg07FFgIXXLdAf+A5qXHGDSeWn90UoUBcAcelFgP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F085XKECNGZ/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F085XKECNGZ-4eb720f411", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "eKqYE", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Looks like the Degenkolb letters are good except one title. The 'Design Engineer' title is a Professional Exempt position but they were put onto the non exempt template. Can we fix those only?"}]}]}]}], "created_at": "2025-05-22T21:35:34.677302"}