#!/usr/bin/env python
"""
Task 3: Test RAG Search API End-to-End

This script:
1. Tests the RAG Search API with various query types
2. Verifies Gemini LLM integration
3. Tests search functionality with real ingested data
4. Validates response quality and citations
5. Tests different search scenarios and edge cases
"""

import os
import sys
import django
import json
import logging
from datetime import datetime
from typing import Dict, List, Any

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv('/Users/<USER>/Desktop/RAGSearch/multi_source_rag/.env')

django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk
from apps.search.services.unified_rag_service import UnifiedRAGService
from apps.core.utils.gemini_llm import get_llm_status, is_gemini_available

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_system_status():
    """Check the status of all system components."""
    print("🔍 Checking system status...")

    # Check LLM status
    llm_status = get_llm_status()
    print(f"  Gemini LLM: {'✅' if llm_status['gemini']['available'] else '❌'}")
    print(f"  Ollama LLM: {'✅' if llm_status['ollama']['available'] else '❌'}")

    # Check data availability
    tenant = Tenant.objects.get(slug='test-tenant')
    raw_docs = RawDocument.objects.filter(source__tenant=tenant).count()
    chunks = DocumentChunk.objects.filter(document__source__tenant=tenant).count()

    print(f"  Raw documents: {raw_docs}")
    print(f"  Document chunks: {chunks}")

    if raw_docs == 0 or chunks == 0:
        print("  ❌ No data available for testing")
        return False

    print(f"  ✅ System ready for testing")
    return True


def create_rag_service():
    """Create and configure the RAG service."""
    print("🚀 Creating RAG service...")

    tenant = Tenant.objects.get(slug='test-tenant')
    user = User.objects.get(username='test-user')

    # Create RAG service (UnifiedRAGService expects tenant slug as string)
    rag_service = UnifiedRAGService(tenant=tenant.slug, user=user)

    print(f"  ✅ RAG service created for tenant: {tenant.name}")
    return rag_service


def test_basic_search(rag_service):
    """Test basic search functionality."""
    print("\n📝 Testing basic search functionality...")

    test_queries = [
        "What are the main engineering challenges discussed?",
        "Tell me about product development issues",
        "What technical problems were mentioned?",
        "How is the team handling deployment?",
        "What are the recent updates on the project?"
    ]

    results = []

    for i, query in enumerate(test_queries, 1):
        print(f"\n  Query {i}: {query}")

        try:
            start_time = datetime.now()

            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=5,
                use_hybrid_search=True
            )

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            print(f"    ✅ Response time: {duration:.2f}s")
            print(f"    📄 Sources found: {len(retrieved_docs)}")

            # Get answer from search result
            answer = search_result.generated_answer if search_result else ""
            print(f"    💬 Response length: {len(answer)}")

            # Show first 100 chars of response
            preview = answer[:100] + "..." if len(answer) > 100 else answer
            print(f"    📝 Preview: {preview}")

            results.append({
                'query': query,
                'duration': duration,
                'sources_count': len(retrieved_docs),
                'answer_length': len(answer),
                'success': True
            })

        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
            results.append({
                'query': query,
                'error': str(e),
                'success': False
            })

    return results


def test_specific_queries(rag_service):
    """Test specific query types relevant to the ingested Slack data."""
    print("\n🎯 Testing specific query types...")

    specific_queries = [
        {
            'type': 'Summary',
            'query': 'Summarize the key engineering discussions from the past month'
        },
        {
            'type': 'Time-framed',
            'query': 'What were the main issues discussed in December 2024?'
        },
        {
            'type': 'Technical',
            'query': 'What technical challenges or bugs were reported?'
        },
        {
            'type': 'Process',
            'query': 'How does the team handle code reviews and deployments?'
        },
        {
            'type': 'People',
            'query': 'Who are the main contributors to engineering discussions?'
        }
    ]

    results = []

    for query_info in specific_queries:
        query_type = query_info['type']
        query = query_info['query']

        print(f"\n  {query_type} Query: {query}")

        try:
            start_time = datetime.now()

            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=8,
                use_hybrid_search=True
            )

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            sources = retrieved_docs
            answer = search_result.generated_answer if search_result else ""

            print(f"    ✅ Response time: {duration:.2f}s")
            print(f"    📄 Sources: {len(sources)}")
            print(f"    💬 Answer length: {len(answer)} chars")

            # Analyze sources (retrieved_docs is a list of (node, score) tuples)
            if sources:
                source_types = set()
                date_range = []

                for node, score in sources:
                    # Get metadata from node
                    metadata = getattr(node, 'metadata', {})
                    source_types.add(metadata.get('source_type', 'unknown'))

                    # Extract dates if available
                    created_at = metadata.get('created_at')
                    if created_at:
                        date_range.append(created_at)

                print(f"    📊 Source types: {', '.join(source_types)}")
                if date_range:
                    print(f"    📅 Date range: {min(date_range)} to {max(date_range)}")

            # Show answer preview
            preview = answer[:150] + "..." if len(answer) > 150 else answer
            print(f"    📝 Answer preview: {preview}")

            results.append({
                'type': query_type,
                'query': query,
                'duration': duration,
                'sources_count': len(sources),
                'answer_length': len(answer),
                'success': True
            })

        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
            results.append({
                'type': query_type,
                'query': query,
                'error': str(e),
                'success': False
            })

    return results


def test_search_parameters(rag_service):
    """Test different search parameters and configurations."""
    print("\n⚙️ Testing search parameters...")

    base_query = "What are the main engineering challenges?"

    parameter_tests = [
        {'name': 'Small limit', 'params': {'top_k': 3}},
        {'name': 'Large limit', 'params': {'top_k': 10}},
        {'name': 'Semantic search', 'params': {'use_hybrid_search': False}},
        {'name': 'Hybrid search', 'params': {'use_hybrid_search': True}},
        {'name': 'Low relevance', 'params': {'min_relevance_score': 0.1}},
        {'name': 'High relevance', 'params': {'min_relevance_score': 0.5}},
        {'name': 'Query expansion', 'params': {'use_query_expansion': True}},
        {'name': 'Multi-step reasoning', 'params': {'use_multi_step_reasoning': True}},
    ]

    results = []

    for test in parameter_tests:
        test_name = test['name']
        params = test['params']

        print(f"\n  Testing {test_name}: {params}")

        try:
            start_time = datetime.now()

            search_result, retrieved_docs = rag_service.search(
                query_text=base_query,
                **params
            )

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            sources_count = len(retrieved_docs)
            answer_length = len(search_result.generated_answer if search_result else "")

            print(f"    ✅ Duration: {duration:.2f}s")
            print(f"    📄 Sources: {sources_count}")
            print(f"    💬 Answer: {answer_length} chars")

            results.append({
                'test_name': test_name,
                'params': params,
                'duration': duration,
                'sources_count': sources_count,
                'answer_length': answer_length,
                'success': True
            })

        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
            results.append({
                'test_name': test_name,
                'params': params,
                'error': str(e),
                'success': False
            })

    return results


def test_edge_cases(rag_service):
    """Test edge cases and error handling."""
    print("\n🧪 Testing edge cases...")

    edge_cases = [
        {'name': 'Empty query', 'query': ''},
        {'name': 'Very short query', 'query': 'bug'},
        {'name': 'Very long query', 'query': 'What are all the engineering challenges, technical issues, deployment problems, code review processes, team collaboration methods, project management approaches, and development workflows that have been discussed in the engineering team over the past several months including any specific incidents, solutions, best practices, and recommendations that were shared?' * 2},
        {'name': 'Non-English query', 'query': 'Quels sont les défis techniques?'},
        {'name': 'Special characters', 'query': 'What about @mentions and #hashtags?'},
        {'name': 'Code-related query', 'query': 'Show me discussions about Python, JavaScript, or API issues'},
    ]

    results = []

    for case in edge_cases:
        case_name = case['name']
        query = case['query']

        print(f"\n  Testing {case_name}: '{query[:50]}{'...' if len(query) > 50 else ''}'")

        try:
            start_time = datetime.now()

            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=5,
                use_hybrid_search=True
            )

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            sources_count = len(retrieved_docs)
            answer_length = len(search_result.generated_answer if search_result else "")

            print(f"    ✅ Handled gracefully")
            print(f"    ⏱️ Duration: {duration:.2f}s")
            print(f"    📄 Sources: {sources_count}")
            print(f"    💬 Answer: {answer_length} chars")

            results.append({
                'case_name': case_name,
                'query': query,
                'duration': duration,
                'sources_count': sources_count,
                'answer_length': answer_length,
                'success': True
            })

        except Exception as e:
            print(f"    ⚠️ Error (expected for some cases): {str(e)}")
            results.append({
                'case_name': case_name,
                'query': query,
                'error': str(e),
                'success': False
            })

    return results


def analyze_results(all_results):
    """Analyze and summarize all test results."""
    print("\n📊 Analyzing test results...")

    total_tests = 0
    successful_tests = 0
    total_duration = 0

    for test_category, results in all_results.items():
        print(f"\n  {test_category}:")

        category_success = sum(1 for r in results if r.get('success', False))
        category_total = len(results)
        category_duration = sum(r.get('duration', 0) for r in results if r.get('success', False))

        print(f"    Success rate: {category_success}/{category_total} ({category_success/category_total*100:.1f}%)")

        if category_success > 0:
            avg_duration = category_duration / category_success
            print(f"    Average response time: {avg_duration:.2f}s")

        total_tests += category_total
        successful_tests += category_success
        total_duration += category_duration

    print(f"\n📋 Overall Summary:")
    print(f"  Total tests: {total_tests}")
    print(f"  Successful tests: {successful_tests}")
    print(f"  Success rate: {successful_tests/total_tests*100:.1f}%")

    if successful_tests > 0:
        avg_duration = total_duration / successful_tests
        print(f"  Average response time: {avg_duration:.2f}s")

    return {
        'total_tests': total_tests,
        'successful_tests': successful_tests,
        'success_rate': successful_tests/total_tests*100,
        'average_duration': total_duration / successful_tests if successful_tests > 0 else 0
    }


def main():
    """Main function to run Task 3."""
    print("🚀 Task 3: Test RAG Search API End-to-End\n")

    # Step 1: Check system status
    if not check_system_status():
        print("❌ System not ready for testing")
        return False

    # Step 2: Create RAG service
    try:
        rag_service = create_rag_service()
    except Exception as e:
        print(f"❌ Failed to create RAG service: {e}")
        return False

    # Step 3: Run all tests
    all_results = {}

    try:
        all_results['Basic Search'] = test_basic_search(rag_service)
        all_results['Specific Queries'] = test_specific_queries(rag_service)
        all_results['Search Parameters'] = test_search_parameters(rag_service)
        all_results['Edge Cases'] = test_edge_cases(rag_service)

    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

    # Step 4: Analyze results
    summary = analyze_results(all_results)

    # Step 5: Final assessment
    print(f"\n🎯 Task 3 Assessment:")

    if summary['success_rate'] >= 80:
        print(f"  ✅ RAG Search API is working well ({summary['success_rate']:.1f}% success rate)")
    elif summary['success_rate'] >= 60:
        print(f"  ⚠️ RAG Search API has some issues ({summary['success_rate']:.1f}% success rate)")
    else:
        print(f"  ❌ RAG Search API needs significant work ({summary['success_rate']:.1f}% success rate)")

    print(f"  ⏱️ Average response time: {summary['average_duration']:.2f}s")
    print(f"  🧪 Tests completed: {summary['successful_tests']}/{summary['total_tests']}")

    # Check if Gemini is being used
    if is_gemini_available():
        print(f"  🤖 Using Gemini LLM: ✅")
    else:
        print(f"  🤖 Using Gemini LLM: ❌ (fallback to Ollama)")

    return summary['success_rate'] >= 70


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
