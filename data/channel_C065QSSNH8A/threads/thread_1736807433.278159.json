{"thread_ts": "1736807433.278159", "channel_id": "C065QSSNH8A", "reply_count": 8, "replies": [{"ts": "1736807661.618739", "text": "I put in a bug for this earlier today as well", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736807433.278159", "blocks": [{"type": "rich_text", "block_id": "ONetQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I put in a bug for this earlier today as well"}]}]}]}, {"ts": "1736807742.924269", "text": "there was not epic for QA UAT. I just created it. Pls put all of your QA UAT bugs in the QA UAT epic so it's easier to track", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736807433.278159", "blocks": [{"type": "rich_text", "block_id": "B4N/8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "there was not epic for QA UAT. I just created it. Pls put all of your QA UAT bugs in the QA UAT epic so it's easier to track"}]}]}]}, {"ts": "1736807773.451829", "text": "There is a QA UAT already, that’s where I put it. You can see the bug above and where I put it.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736807433.278159", "blocks": [{"type": "rich_text", "block_id": "F8LPI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There is a QA UAT already, that’s where I put it. You can see the bug above and where I put it."}]}]}]}, {"ts": "1736807874.878509", "text": "I only see COM 4057 which I created 30 mins ago", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736807433.278159", "blocks": [{"type": "rich_text", "block_id": "Pqawz", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I only see COM 4057 which I created 30 mins ago"}]}]}]}, {"ts": "1736807882.598719", "text": "which QA UAT are you referring to?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736807433.278159", "blocks": [{"type": "rich_text", "block_id": "iV9Zp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "which QA UAT are you referring to?"}]}]}]}, {"ts": "1736807990.747329", "text": "", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736807433.278159", "subtype": "thread_broadcast", "files": [{"id": "F088238UZ47", "created": 1736807984, "timestamp": 1736807984, "name": "IMG_4652.png", "title": "IMG_4652", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 1160876, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F088238UZ47/img_4652.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F088238UZ47/download/img_4652.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F088238UZ47-5f0ddc93d5/img_4652_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F088238UZ47-5f0ddc93d5/img_4652_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F088238UZ47-5f0ddc93d5/img_4652_360.png", "thumb_360_w": 166, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F088238UZ47-5f0ddc93d5/img_4652_480.png", "thumb_480_w": 221, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F088238UZ47-5f0ddc93d5/img_4652_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F088238UZ47-5f0ddc93d5/img_4652_720.png", "thumb_720_w": 332, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F088238UZ47-5f0ddc93d5/img_4652_800.png", "thumb_800_w": 800, "thumb_800_h": 1734, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F088238UZ47-5f0ddc93d5/img_4652_960.png", "thumb_960_w": 443, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F088238UZ47-5f0ddc93d5/img_4652_1024.png", "thumb_1024_w": 472, "thumb_1024_h": 1024, "original_w": 1179, "original_h": 2556, "thumb_tiny": "AwAwABa4ztvwvWlVn7inMzcbBu/HFJvk/uD/AL6pgKpPoR9aXJ9KMFl+8Qfak8tv+ejUCHbP9pvzo2f7TfnTASB14/D/AAo3H1/Uf4Uhj9n+0350eX/tN+dN3H1/z+VG4+v+fyoAAN3Rgfz/AMaXYfX+f+NKQ3r+tICD/Hz9aYBsPr/P/GjYfUfr/jTtvuaMe5pAf//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F088238UZ47/img_4652.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F088238UZ47-866b29099a", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}]}, {"ts": "1736808171.604359", "text": "ah okk. Let's use COM 4057 going forward since I already created a new one for QA UAT", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1736807433.278159", "blocks": [{"type": "rich_text", "block_id": "UgXIY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ah okk. Let's use COM 4057 going forward since I already created a new one for QA UAT"}]}]}]}, {"ts": "1736858595.337509", "text": "I had noticed this and being fixed.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1736807433.278159", "blocks": [{"type": "rich_text", "block_id": "iAM3b", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I had noticed this and being fixed."}]}]}]}], "created_at": "2025-05-22T22:00:49.334453"}