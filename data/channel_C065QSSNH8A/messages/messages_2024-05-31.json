{"date": "2024-05-31", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1717094514.561549", "text": "<!here> is today's update for people insight\n1. Item 1,3, and 8 were pending as of yesterday from the description provided in <https://compiify.atlassian.net/browse/COM-3091>. Fixes for 1 &amp; 3 are in code review. No progress on item 8\n2. UI updates: Items reported under <https://compiify.atlassian.net/browse/COM-3090> were  reviewed and merged ( comment section has details about few pending  / blocking items remaining)\n3. <https://compiify.atlassian.net/browse/COM-3095> was opened to fix BUDGET SPEND BY DEPARTMENT. Fix for this jira is in code review \n4. New issue opened: <https://compiify.atlassian.net/browse/COM-3116>\n5. Fix for <https://compiify.atlassian.net/browse/COM-3072> is in code review \nOpen items to track for friday\n1. <https://compiify.atlassian.net/browse/COM-3116>\n2. <https://compiify.atlassian.net/browse/COM-3118>", "user": "U04DKEFP1K8", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13152::4a025c701eb411ef9f02e1d8ecdd1bb4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3091?atlOrigin=eyJpIjoiMjRmYzU0MDI5NzFhNGM1NTk2MDQyZTc2MjYyNmFmMWQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3091 People Insights: Match the current Implementation with the PRD>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13152::4a0283821eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "mrkdwn", "text": "Status: *In Development*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2a19465d953b8640b8f3558fd4f15da9?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13152::4a025c711eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13152\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13152\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3091", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:13151::4a025c721eb411ef9f02e1d8ecdd1bb4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3090?atlOrigin=eyJpIjoiMTUzOTIwYTNlYmRhNDNkNjk4MGE4ZTY2OGYwODdlNTgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3090 People Insights | Match the current UI with the Figma Design>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13151::4a0283831eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "mrkdwn", "text": "Status: *Blocked*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/606486a6aee240006877c236/2b20eeb2-9337-436b-afa5-c418e47dbe4c/48?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13151::4a025c731eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13151\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13151\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3090", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 3, "blocks": [{"type": "section", "block_id": "uf:ih:13156::4a025c741eb411ef9f02e1d8ecdd1bb4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3095?atlOrigin=eyJpIjoiMTE3MzBkZWIwYjM2NDA4NGJlN2YzNmE5ZGU2OTZmNTIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3095 Budget spend is adding up against a single department even when the…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13156::4a0283841eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/79ad6fec1eb23766c2c2c46cb17078ab?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13156::4a025c751eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13156\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13156\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3095", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 4, "blocks": [{"type": "section", "block_id": "uf:ih:13177::4a025c761eb411ef9f02e1d8ecdd1bb4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3116?atlOrigin=eyJpIjoiZmFhNDIyOTQ4MjdlNGQwNzlmMjRhNDMxYWJiMDAyMzQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3116 Position-in-band employee counts don't add up>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13177::4a0283851eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2a19465d953b8640b8f3558fd4f15da9?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13177::4a025c771eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13177\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13177\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3116", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 5, "blocks": [{"type": "section", "block_id": "uf:ih:13133::4a0283801eb411ef9f02e1d8ecdd1bb4", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3072?atlOrigin=eyJpIjoiNDEyNjIzNzA3YmY4NGQzMGJkMzE2ZDFiODQ4OGJiZmYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3072 Unable to load people insights after doing bulk update of any categ…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13133::4a0283861eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png", "alt_text": "Bug"}, {"type": "mrkdwn", "text": "Type: *Bug*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/79ad6fec1eb23766c2c2c46cb17078ab?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13133::4a0283811eb411ef9f02e1d8ecdd1bb4", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13133\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13133\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3072", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "8qS9K", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " is today's update for people insight\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Item 1,3, and 8 were pending as of yesterday from the description provided in "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3091"}, {"type": "text", "text": ". Fixes for 1 & 3 are in code review. No progress on item 8"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "UI updates: Items reported under "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3090"}, {"type": "text", "text": " were  reviewed and merged ( comment section has details about few pending  / blocking items remaining)"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3095"}, {"type": "text", "text": " was opened to fix BUDGET SPEND BY DEPARTMENT. Fix for this jira is in code review "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "New issue opened: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3116"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Fix for "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3072"}, {"type": "text", "text": " is in code review "}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nOpen items to track for friday\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3116"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3118"}]}], "style": "ordered", "indent": 0, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.598412"}