{"date": "2024-05-02", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1714598285.612259", "text": "<@U0690EB5JE5> <@U04DKEFP1K8> For Merit 2.0, I've updated <https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit#heading=h.cdw134u67616|this doc> with more detail on the different cases we need to support in the planner view. The \"task list\" design will likely have some more refinement between now and next week, so the planner (\"manager\") view is higher confidence design to work on.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1714598285.612259", "reply_count": 1, "reactions": [{"name": "raised_hands", "users": ["U0690EB5JE5"], "count": 1}], "files": [{"id": "F07033J43GE", "created": 1713895996, "timestamp": 1713895996, "name": "Merit 2.0 designs (in progress)", "title": "Merit 2.0 designs (in progress)", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10", "external_url": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit", "url_private": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07033J43GE-266a35fe4b/merit_2_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSJ+v4Ckz9fyobGRnNIMepoAeDmimYH96l+X1oAdRQOlFACEA0mB6CnGkFACYHpR0FLRQAA5GaWgUUAITikyP8ilLAGkDA+tABuH+RS5+tJlfWgbQetADhRQDmigD/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07033J43GE/merit_2", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "EXJrq", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " For Merit 2.0, I've updated "}, {"type": "link", "url": "https://docs.google.com/document/d/1K6UtqxL-aKLXXfXXNFuVA2_N7cJ9qbl7nbMmri3tt10/edit#heading=h.cdw134u67616", "text": "this doc"}, {"type": "text", "text": " with more detail on the different cases we need to support in the planner view. The \"task list\" design will likely have some more refinement between now and next week, so the planner (\"manager\") view is higher confidence design to work on."}]}]}]}], "created_at": "2025-05-22T21:35:34.601607"}