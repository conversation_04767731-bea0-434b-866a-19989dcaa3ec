"""
Structured output utilities for the Multi-Source RAG system.

This module provides functions for generating structured outputs from LLM responses.
"""

import json
import logging
from enum import Enum
from typing import Any, Dict, List, Literal, Optional, TypedDict, Union

from django.conf import settings
from langchain.output_parsers import PydanticOutputParser
from langchain_community.llms import Ollama as OllamaLLM
from langchain_core.documents import Document
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.pydantic_v1 import BaseModel as LCBaseModel
from langchain_core.pydantic_v1 import Field as LCField
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

# Ollama API endpoint
OLLAMA_API_HOST = getattr(settings, "OLLAMA_API_HOST", "http://localhost:11434")
OLLAMA_MODEL_NAME = getattr(settings, "OLLAMA_MODEL_NAME", "llama3")


class AnswerFormat(str, Enum):
    """Enum for answer formats."""

    TEXT = "text"
    JSON = "json"
    MARKDOWN = "markdown"
    TABLE = "table"


class SourceDocument(LCBaseModel):
    """Model for a source document reference."""

    document_id: str = LCField(description="The ID of the source document")
    title: str = LCField(description="The title of the source document")
    source_type: str = LCField(
        description="The type of the source (e.g., slack, github)"
    )
    relevance: float = LCField(description="The relevance score of the document (0-1)")


class StructuredAnswer(LCBaseModel):
    """Model for a structured answer."""

    answer: str = LCField(description="The answer to the question")
    sources: List[SourceDocument] = LCField(
        description="The sources used to generate the answer"
    )
    confidence: float = LCField(description="The confidence score of the answer (0-1)")
    format: str = LCField(
        description="The format of the answer (text, json, markdown, table)"
    )


def get_structured_llm(streaming: bool = False) -> OllamaLLM:
    """
    Initialize and return an Ollama LLM instance for structured output.

    Args:
        streaming: Whether to stream the output

    Returns:
        OllamaLLM: Initialized LLM
    """
    try:
        # Initialize the Ollama LLM with lower temperature for more structured output
        llm = OllamaLLM(
            model=OLLAMA_MODEL_NAME,
            base_url=OLLAMA_API_HOST,
            temperature=0.1,
            top_p=0.95,
            num_ctx=4096,  # Context window size
        )

        return llm
    except Exception as e:
        logger.error(f"Failed to initialize Ollama LLM: {str(e)}")
        raise ValueError(f"Failed to connect to Ollama service: {str(e)}")


def generate_structured_answer(
    question: str,
    context_docs: List[Document],
    output_format: AnswerFormat = AnswerFormat.TEXT,
) -> Union[str, Dict[str, Any]]:
    """
    Generate a structured answer to a question.

    Args:
        question: The question to answer
        context_docs: List of context documents
        output_format: Format of the output

    Returns:
        Union[str, Dict[str, Any]]: Structured answer
    """
    if not context_docs:
        return "I couldn't find any relevant information to answer your question."

    try:
        # Get LLM
        llm = get_structured_llm()

        # Create output parser
        parser = PydanticOutputParser(pydantic_object=StructuredAnswer)

        # Format documents into a string
        docs_str = "\n\n".join(
            [
                f"Document {i+1}:\nTitle: {doc.metadata.get('title', 'Unknown')}\n"
                f"Source: {doc.metadata.get('source_type', 'Unknown')}\n"
                f"Content: {doc.page_content}"
                for i, doc in enumerate(context_docs)
            ]
        )

        # Create prompt template based on output format
        format_instructions = {
            AnswerFormat.TEXT: "Provide a clear, concise text answer.",
            AnswerFormat.JSON: "Format the answer as a JSON object with key information.",
            AnswerFormat.MARKDOWN: "Format the answer in Markdown with headings, lists, and emphasis.",
            AnswerFormat.TABLE: "If the answer contains tabular data, format it as a Markdown table.",
        }

        template = f"""
        <|system|>
        You are an AI assistant for answering questions based on the provided context.
        Your task is to provide accurate, helpful answers based solely on the information in the context.

        Guidelines:
        1. If the answer cannot be determined from the context, say "I don't have enough information to answer this question."
        2. Do not make up information that is not in the context.
        3. Provide a well-structured, informative answer that directly addresses the question.
        4. {format_instructions[output_format]}
        5. When referencing information, include citations to the original source in your answer.
        6. For each piece of information, include a citation like [Document X] where X is the document number.

        {parser.get_format_instructions()}
        </|system|>

        <|user|>
        Context:
        {docs_str}

        Question: {question}
        </|user|>

        <|assistant|>
        """

        # Generate answer
        result = llm.invoke(template)

        # Parse result
        try:
            structured_answer = parser.parse(result)

            # Return based on requested format
            if output_format == AnswerFormat.JSON:
                return structured_answer.dict()
            else:
                return structured_answer.answer
        except Exception as parse_error:
            logger.warning(f"Failed to parse structured output: {str(parse_error)}")
            # Fall back to returning the raw result
            return result

    except Exception as e:
        logger.error(f"Error generating structured answer: {str(e)}")
        raise ValueError(f"Failed to generate structured answer: {str(e)}")


def generate_markdown_table(question: str, context_docs: List[Document]) -> str:
    """
    Generate a Markdown table from the context documents.

    Args:
        question: The question to answer
        context_docs: List of context documents

    Returns:
        str: Markdown table
    """
    return generate_structured_answer(question, context_docs, AnswerFormat.TABLE)


def generate_json_response(
    question: str, context_docs: List[Document]
) -> Dict[str, Any]:
    """
    Generate a JSON response from the context documents.

    Args:
        question: The question to answer
        context_docs: List of context documents

    Returns:
        Dict[str, Any]: JSON response
    """
    return generate_structured_answer(question, context_docs, AnswerFormat.JSON)
