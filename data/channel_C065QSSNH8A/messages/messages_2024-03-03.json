{"date": "2024-03-03", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1709431346.908089", "text": "<@U065H3M6WJV> social login are enabled on <http://nauto-test.compiify.com|nauto-test.compiify.com>", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1709431346.908089", "reply_count": 15, "blocks": [{"type": "rich_text", "block_id": "J55ja", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " social login are enabled on "}, {"type": "link", "url": "http://nauto-test.compiify.com", "text": "nauto-test.compiify.com"}]}]}]}, {"ts": "1709426186.441359", "text": "<@U065H3M6WJV> Nauto's equity data is uploaded in test environment. We will need to add enhancement to allow manual upload of equity vesting data for an employee ( currently we use automated calculation for the same)", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1709426186.441359", "reply_count": 1, "edited": {"user": "U04DKEFP1K8", "ts": "1709429439.000000"}, "blocks": [{"type": "rich_text", "block_id": "SLkuY", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Nauto's equity data is uploaded in test environment. We will need to add enhancement to allow manual upload of equity vesting data for an employee ( currently we use automated calculation for the same)"}]}]}]}, {"ts": "1709424222.753689", "text": "<@U065H3M6WJV> <PERSON><PERSON>'s data update request is complete", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "gratitude-thank-you", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "NpFAI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Katy<PERSON>'s data update request is complete"}]}]}]}, {"ts": "1709407468.036369", "text": "In addition to the bigger tech questions, we still have some live customers we need to stay \"in stride\" with. <@U04DKEFP1K8> who is on point for each of these?\n• *SDF* - Adjustment letters \n    ◦ Ensure the logo is correctly displayed / not a broken link when generating\n    ◦ Identify plan for incorporating Lumen grant amounts if SDF provides these (should be ~13 employees total with a new grant)\n• *DA* - Adjustment letters\n    ◦ Ensure the format &amp; logo match what they requested\n• *Nauto* - Google social login\n    ◦ Need to test this before releasing to customers\n    ◦ Should allow admins like Delaney &amp; Renato to enable other employees without our intervention", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1709407468.036369", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "RBL12", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "In addition to the bigger tech questions, we still have some live customers we need to stay \"in stride\" with. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " who is on point for each of these?\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF", "style": {"bold": true}}, {"type": "text", "text": " - Adjustment letters "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ensure the logo is correctly displayed / not a broken link when generating"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Identify plan for incorporating Lumen grant amounts if SDF provides these (should be ~13 employees total with a new grant)"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA ", "style": {"bold": true}}, {"type": "text", "text": "- Adjustment letters"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ensure the format & logo match what they requested"}]}], "style": "bullet", "indent": 1, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON> ", "style": {"bold": true}}, {"type": "text", "text": "- Google social login"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Need to test this before releasing to customers"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should allow admins like Delaney & Renato to enable other employees without our intervention"}]}], "style": "bullet", "indent": 1, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.613113"}