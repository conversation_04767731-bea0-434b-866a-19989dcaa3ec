{"date": "2025-01-15", "channel_id": "C065QSSNH8A", "message_count": 38, "messages": [{"ts": "1736963555.372289", "text": "Thank you!!", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "OOIUM", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you!!"}]}]}]}, {"ts": "1736962833.308109", "text": "Please take a look and let me know. Also I am bumping up <PERSON><PERSON><PERSON>'s infra and this will take like 20mnts. But ENV is accessible", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "3xB8s", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Please take a look and let me know. Also I am bumping up <PERSON><PERSON><PERSON>'s infra and this will take like 20mnts. But ENV is accessible"}]}]}]}, {"ts": "1736962798.724799", "text": "CURANA Data Refresh is DONE :tada:", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "0wNZd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "CURANA Data Refresh is DONE "}, {"type": "emoji", "name": "tada", "unicode": "1f389"}]}]}]}, {"ts": "1736962032.532699", "text": "<@U07EJ2LP44S> Curana will be down for sometime. Please inform customer.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yKciN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " <PERSON><PERSON><PERSON> will be down for sometime. Please inform customer."}]}]}]}, {"ts": "1736961499.526279", "text": "Also verify once the cycle that was already created.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "p/Zt2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also verify once the cycle that was already created."}]}]}]}, {"ts": "1736961475.416959", "text": "I expect some issues with data. Please let me know in case any issues.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "tkj5a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I expect some issues with data. Please let me know in case any issues."}]}]}]}, {"ts": "1736961448.854489", "text": "there are 2058 employees", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lsCJn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "there are 2058 employees"}]}]}]}, {"ts": "1736961433.964729", "text": "<@U07EJ2LP44S> Update: I was able sync data and was hit with some issues.  Updating production.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "My0tI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Update: I was able sync data and was hit with some issues.  Updating production."}]}]}]}, {"ts": "1736957748.637309", "text": "<@U07EJ2LP44S> Looks like its IP whitelisting issue. I connected to my mobile hotspot and able to connect now :smile:", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U07EJ2LP44S"], "count": 1}, {"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "MhPDu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Looks like its IP whitelisting issue. I connected to my mobile hotspot and able to connect now "}, {"type": "emoji", "name": "smile", "unicode": "1f604"}]}]}]}, {"ts": "1736957711.530709", "text": ":crossed_fingers:", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "G5M06", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "crossed_fingers", "unicode": "1f91e"}]}]}]}, {"ts": "1736957701.544449", "text": "yes, I understand there were just too many issues. I am syncing data now. Hopefully should be done.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "9St4H", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes, I understand there were just too many issues. I am syncing data now. Hopefully should be done."}]}]}]}, {"ts": "1736957628.695439", "text": "Updating data is quite time sensitive right now", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ak8/H", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Updating data is quite time sensitive right now"}]}]}]}, {"ts": "1736956964.777649", "text": "ok on it.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "+wwK/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok on it."}]}]}]}, {"ts": "1736956917.922199", "text": "thank you, there is definitely a sense of urgency", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zcfhT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "thank you, there is definitely a sense of urgency"}]}]}]}, {"ts": "1736956887.582719", "text": "yes let me reach out to their support", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "uLC7L", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yes let me reach out to their support"}]}]}]}, {"ts": "**********.227649", "text": "I am guessing my IP is blacklisted probably.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "o0V35", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am guessing my IP is blacklisted probably."}]}]}]}, {"ts": "**********.951709", "text": "These accounts all need to be updated so we can continue our work", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "e/0CC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "These accounts all need to be updated so we can continue our work"}]}]}]}, {"ts": "**********.157189", "text": "Do we need to talk tto their support?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "<PERSON><PERSON><PERSON><PERSON>", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do we need to talk tto their support?"}]}]}]}, {"ts": "**********.590209", "text": "<@U07EJ2LP44S> I am facing again connectivity issue with Merge system.  Not sure will be able to sync data. Will update here if am able to.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "TFezQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I am facing again connectivity issue with Merge system.  Not sure will be able to sync data. Will update here if am able to."}]}]}]}, {"ts": "**********.967869", "text": "Yes, they have some hierarchy issues that need to be addressed and he is trying to demote his CEO today", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "c/bg6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, they have some hierarchy issues that need to be addressed and he is trying to demote his CEO today"}]}]}]}, {"ts": "1736954400.604569", "text": "Do you still want me to sync data? if yes I can try now.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ydJaL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Do you still want me to sync data? if yes I can try now."}]}]}]}, {"ts": "1736954369.507659", "text": "<PERSON><PERSON><PERSON> since there were so many issues, I didn't sync data yet.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "pjNBp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON><PERSON><PERSON> since there were so many issues, I didn't sync data yet."}]}]}]}, {"ts": "1736954312.194699", "text": "VG can wait", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "hllKh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "VG can wait"}]}]}]}, {"ts": "1736954309.378599", "text": "And cannot wait", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "YAVJr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "And cannot wait"}]}]}]}, {"ts": "1736954301.504309", "text": "Curana is most important", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zfIht", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Curana is most important"}]}]}]}, {"ts": "1736954250.964449", "text": "Let me know if VG data sync can't wait till tomorrow.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VTS7o", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me know if VG data sync can't wait till tomorrow."}]}]}]}, {"ts": "1736954227.437139", "text": "Its been very long day and didn't get a chance to sync data due to all these issues. I will be syncing data tomorrow", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "LRisg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Its been very long day and didn't get a chance to sync data due to all these issues. I will be syncing data tomorrow"}]}]}]}, {"ts": "1736954220.967349", "text": "Have we made any progress on data refreshes for curana and VG?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "/+Nmo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Have we made any progress on data refreshes for curana and VG?"}]}]}]}, {"ts": "1736954185.468459", "text": "Thank you!", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Ye0AT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you!"}]}]}]}, {"ts": "1736952152.966299", "text": "<@U07EJ2LP44S> Following issues are fixed.\n<https://compiify.atlassian.net/browse/COM-4063>\n<https://compiify.atlassian.net/browse/COM-4064>\n<https://compiify.atlassian.net/browse/COM-4062>\n<https://compiify.atlassian.net/browse/COM-4058>", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1736953186.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14146::e930d3b361ae43a8a4a60bc2c35417af", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4063?atlOrigin=eyJpIjoiNTc3OTljNDdkMzIwNDU2MWJhZWQ3NWJiZDM1ZWViNTAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4063 Issue with Role Assigning in Curana>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14146::30cb4a55dcbc4c79862bada22d0b363c", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14146\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4063\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4063", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 2, "blocks": [{"type": "section", "block_id": "uf:ih:14147::9a8f9ce3e57f4f96acb4d3aa9d02426b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4064?atlOrigin=eyJpIjoiMDdhNDIxNjI0NjA5NGE1OGI0ZWIyMjE2Y2EwMWI1YTEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4064 Cycle Builder Issue - Cannot Complete Cycle>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14147::36cbef6f97744d6e88db44aab5d02503", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/712020:7d833b3a-e9d6-423f-93b8-d525378819e9/af9dff77-7442-4e64-9882-eb63aaa8f5a1/48?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14147\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4064\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4064", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 3, "blocks": [{"type": "section", "block_id": "uf:ih:14145::88a69757a52b42af87fdc368800d1866", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4062?atlOrigin=eyJpIjoiOWUzZTEyMjIxOGI2NDRkMTliNjNmZjNiZGQ1ZTMxYTAiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4062 Bonus Feature Bugs>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14145::e6b0b84ac47944ba93e47e89fe7d84f8", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14145\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4062\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4062", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}, {"id": 4, "blocks": [{"type": "section", "block_id": "uf:ih:14141::ec9c602bce2f462aa7f4b4619b9a0a7b", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4058?atlOrigin=eyJpIjoiODljMTk1MGZmYWM5NGY4ZTgzZTBkZDdiYTc4ZWZjZTciLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4058 Missing Fields for Variable Pay in Column Configurator>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14141::d7f6cdf5b85d45ff897041dcd50b43d3", "elements": [{"type": "mrkdwn", "text": "Status: *In Review*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/3ba050d9f25e3d5164f213ae816bb449?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14141\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4058\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4058", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Fw2rx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Following issues are fixed.\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4063"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4064"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4062"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4058"}]}]}]}, {"ts": "1736945645.784629", "text": "Will keep you posted", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "B/KfC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will keep you posted"}]}]}]}, {"ts": "1736945637.950259", "text": "<@U07EJ2LP44S> the fixes are in progress. We should be done one or two hours", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "89VmP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " the fixes are in progress. We should be done one or two hours"}]}]}]}, {"ts": "1736939150.202069", "text": "<@U0690EB5JE5> <@U07M6QKHUC9> <@U07EJ2LP44S> toggle to control the visibility of percentage, please let me know your feedback", "user": "U06HN8XDC5A", "type": "message", "thread_ts": "1736939150.202069", "reply_count": 4, "files": [{"id": "F089E4B0QNL", "created": 1736937367, "timestamp": 1736937367, "name": "Screenshot 2025-01-15 at 3.12.47 PM.png", "title": "Screenshot 2025-01-15 at 3.12.47 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U06HN8XDC5A", "user_team": "T04DM97F1UM", "editable": false, "size": 108330, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089E4B0QNL/screenshot_2025-01-15_at_3.12.47___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089E4B0QNL/download/screenshot_2025-01-15_at_3.12.47___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4B0QNL-0e9b094156/screenshot_2025-01-15_at_3.12.47___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4B0QNL-0e9b094156/screenshot_2025-01-15_at_3.12.47___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4B0QNL-0e9b094156/screenshot_2025-01-15_at_3.12.47___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 355, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4B0QNL-0e9b094156/screenshot_2025-01-15_at_3.12.47___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 473, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4B0QNL-0e9b094156/screenshot_2025-01-15_at_3.12.47___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4B0QNL-0e9b094156/screenshot_2025-01-15_at_3.12.47___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 709, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4B0QNL-0e9b094156/screenshot_2025-01-15_at_3.12.47___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 788, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4B0QNL-0e9b094156/screenshot_2025-01-15_at_3.12.47___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 946, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4B0QNL-0e9b094156/screenshot_2025-01-15_at_3.12.47___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 1009, "original_w": 1198, "original_h": 1180, "thumb_tiny": "AwAvADDR5owaXrRgAdKAE+ajmjCnsOPal4x7UAJj2oAPpS8elGaAClpKWgBMn0oycdKOfal7e9ACZozRzRzQAUtFFACY9zRjijA9KXAxjtQAmKMe9LgUYFAH/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089E4B0QNL/screenshot_2025-01-15_at_3.12.47___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089E4B0QNL-7fb01e929e", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}, {"id": "F089E4BA3H6", "created": 1736937372, "timestamp": 1736937372, "name": "Screenshot 2025-01-15 at 3.14.15 PM.png", "title": "Screenshot 2025-01-15 at 3.14.15 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U06HN8XDC5A", "user_team": "T04DM97F1UM", "editable": false, "size": 67080, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089E4BA3H6/screenshot_2025-01-15_at_3.14.15___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089E4BA3H6/download/screenshot_2025-01-15_at_3.14.15___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4BA3H6-8a7c00a1f2/screenshot_2025-01-15_at_3.14.15___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4BA3H6-8a7c00a1f2/screenshot_2025-01-15_at_3.14.15___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4BA3H6-8a7c00a1f2/screenshot_2025-01-15_at_3.14.15___pm_360.png", "thumb_360_w": 216, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4BA3H6-8a7c00a1f2/screenshot_2025-01-15_at_3.14.15___pm_480.png", "thumb_480_w": 288, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4BA3H6-8a7c00a1f2/screenshot_2025-01-15_at_3.14.15___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4BA3H6-8a7c00a1f2/screenshot_2025-01-15_at_3.14.15___pm_720.png", "thumb_720_w": 433, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4BA3H6-8a7c00a1f2/screenshot_2025-01-15_at_3.14.15___pm_800.png", "thumb_800_w": 481, "thumb_800_h": 800, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4BA3H6-8a7c00a1f2/screenshot_2025-01-15_at_3.14.15___pm_960.png", "thumb_960_w": 577, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F089E4BA3H6-8a7c00a1f2/screenshot_2025-01-15_at_3.14.15___pm_1024.png", "thumb_1024_w": 615, "thumb_1024_h": 1024, "original_w": 720, "original_h": 1198, "thumb_tiny": "AwAwABzRx6j9KTB9KdgelJxnpz9KADHt+lHIpRx0pOPSgB1JznpS0nOaAFpPwpaTmgBaTv1NLSYGc4oAWk/GlpMY7UAf/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089E4BA3H6/screenshot_2025-01-15_at_3.14.15___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089E4BA3H6-152105c642", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "S+4JL", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " toggle to control the visibility of percentage, please let me know your feedback"}]}]}]}, {"ts": "1736886734.837449", "text": "<@U07M6QKHUC9> We have a call with Alayacare tomorrow; she was holding it to give you the feedback you'd asked for on paybands before the holidays. Do you have another topic you'd like to discuss with her? I also asked if there was anything specific beyond French Canadian language that was needed for her next cycle, and she said that was the big one, and they couldn't roll it out more widely without it. And I'm guessing she'd likely not renew if she didn't have a tool that could do all employees for the next cycle.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736886734.837449", "reply_count": 9, "edited": {"user": "U07EJ2LP44S", "ts": "1736886816.000000"}, "blocks": [{"type": "rich_text", "block_id": "dYVEn", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " We have a call with Al<PERSON><PERSON> tomorrow; she was holding it to give you the feedback you'd asked for on paybands before the holidays. Do you have another topic you'd like to discuss with her? I also asked if there was anything specific beyond French Canadian language that was needed for her next cycle, and she said that was the big one, and they couldn't roll it out more widely without it. And I'm guessing she'd likely not renew if she didn't have a tool that could do all employees for the next cycle."}]}]}]}, {"ts": "**********.444179", "text": "<PERSON> let me know that their account was not working properly, we finally refreshed a lot of times and got data back showing. However, the column configurator has turned OFF a lot of columns by default. Only 2 columns show up in org view:", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.444179", "reply_count": 5, "files": [{"id": "F088MPF3CCT", "created": **********, "timestamp": **********, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 212724, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F088MPF3CCT/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F088MPF3CCT/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F088MPF3CCT-4f4a70df38/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F088MPF3CCT-4f4a70df38/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F088MPF3CCT-4f4a70df38/image_360.png", "thumb_360_w": 360, "thumb_360_h": 177, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F088MPF3CCT-4f4a70df38/image_480.png", "thumb_480_w": 480, "thumb_480_h": 236, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F088MPF3CCT-4f4a70df38/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F088MPF3CCT-4f4a70df38/image_720.png", "thumb_720_w": 720, "thumb_720_h": 355, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F088MPF3CCT-4f4a70df38/image_800.png", "thumb_800_w": 800, "thumb_800_h": 394, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F088MPF3CCT-4f4a70df38/image_960.png", "thumb_960_w": 960, "thumb_960_h": 473, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F088MPF3CCT-4f4a70df38/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 505, "original_w": 2464, "original_h": 1214, "thumb_tiny": "AwAXADC/s5+8/wCdL5f+2/8A31Th1NLTuBH5X+2//fVJs/2n/wC+jUtJii4DPK/23/76o8vH8T/99VJSYouADqaWkHU0tIAooooAKKKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F088MPF3CCT/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F088MPF3CCT-bf677ca439", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "SZexT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> let me know that their account was not working properly, we finally refreshed a lot of times and got data back showing. However, the column configurator has turned OFF a lot of columns by default. Only 2 columns show up in org view:"}]}]}]}, {"ts": "**********.129209", "text": "Issue completing cycle and publishing: <https://compiify.atlassian.net/browse/COM-4064>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.129209", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14147::b65f59c775904787b8a0e97844bf1e3d", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4064?atlOrigin=eyJpIjoiMDkwNWI0Mjc1MDI0NDQxMjgzNTFlMTU3YWZjMDg4MjUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4064 Cycle Builder Issue - Cannot Complete Cycle>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14147::01692070763e496eb2d5dfd61d291bcd", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14147\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4064\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4064", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "e7u9U", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Issue completing cycle and publishing: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4064"}]}]}]}, {"ts": "1736882181.104609", "text": "There were several issues - here is one with role assignment that is causing several issues for the main admin, <PERSON>: <https://compiify.atlassian.net/browse/COM-4063>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1736882181.104609", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14146::5326e1cd38e942a6a28a2ee4c6039b9d", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4063?atlOrigin=eyJpIjoiNzEwNThjOGE5M2E3NGMwM2E4ZDAzODk5Y2Q3ODJhZDYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4063 Issue with Role Assigning in Curana>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14146::61982008116f454fbaa9470ae1d9757f", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14146\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4063\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "assign", "text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"type": "button", "action_id": "transition", "text": {"type": "plain_text", "text": "Change status", "emoji": true}, "value": "transition"}, {"type": "button", "action_id": "comment", "text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "bot_team_id": "T04DM97F1UM", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4063", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "Frht9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There were several issues - here is one with role assignment that is causing several issues for the main admin, <PERSON>: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4063"}]}]}]}, {"ts": "1736880961.982769", "text": "<@U0690EB5JE5> comp builder for Curana is broken and we can't get past the bonus. I will let <@U07EJ2LP44S> share more details but can we pls get this fixed by tomorrow morning.", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "u50HJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " comp builder for <PERSON><PERSON><PERSON> is broken and we can't get past the bonus. I will let "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " share more details but can we pls get this fixed by tomorrow morning."}]}]}]}], "created_at": "2025-05-22T21:35:34.691854"}