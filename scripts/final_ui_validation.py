#!/usr/bin/env python3
"""
Final UI Validation Script - Production Readiness Test
Tests the complete user experience with real data and browser validation.
"""

import os
import sys
import django
import requests
from datetime import datetime
import json

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def test_server_status():
    """Test if the server is running and accessible."""
    
    print("🌐 Testing Server Status")
    print("=" * 50)
    
    try:
        response = requests.get("http://127.0.0.1:8000/search/", timeout=5)
        
        if response.status_code in [200, 302]:
            print("✅ Server is accessible")
            print(f"📊 Status Code: {response.status_code}")
            return True
        else:
            print(f"⚠️  Server returned status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server")
        print("💡 Make sure to run: poetry run python manage.py runserver")
        return False
    except Exception as e:
        print(f"❌ Server test failed: {e}")
        return False

def test_template_improvements():
    """Test all template improvements are in place."""
    
    print("\n🎨 Testing Template Improvements")
    print("=" * 50)
    
    improvements = {}
    
    # Test conversation detail template
    conv_template_path = "/Users/<USER>/Desktop/RAGSearch/multi_source_rag/apps/search/templates/search/conversation_detail.html"
    try:
        with open(conv_template_path, 'r') as f:
            conv_content = f.read()
        
        improvements['conv_uses_css'] = 'search_results.css' in conv_content
        improvements['conv_uses_markdown'] = 'markdown_to_html' in conv_content
        improvements['conv_uses_container'] = 'response-container' in conv_content
        improvements['conv_ai_assistant'] = 'AI Assistant' in conv_content
        
        print(f"✅ Conversation CSS: {'FIXED' if improvements['conv_uses_css'] else 'MISSING'}")
        print(f"✅ Conversation Markdown: {'FIXED' if improvements['conv_uses_markdown'] else 'MISSING'}")
        print(f"✅ Conversation Container: {'FIXED' if improvements['conv_uses_container'] else 'MISSING'}")
        print(f"✅ AI Assistant Label: {'FIXED' if improvements['conv_ai_assistant'] else 'MISSING'}")
        
    except FileNotFoundError:
        print("❌ Conversation template not found")
        return False
    
    # Test search results template
    search_template_path = "/Users/<USER>/Desktop/RAGSearch/multi_source_rag/apps/search/templates/search/search_results.html"
    try:
        with open(search_template_path, 'r') as f:
            search_content = f.read()
        
        improvements['search_header'] = 'AI Response & Analysis' in search_content
        improvements['search_uses_markdown'] = 'markdown_to_html' in search_content
        
        print(f"✅ Search Header: {'IMPROVED' if improvements['search_header'] else 'MISSING'}")
        print(f"✅ Search Markdown: {'FIXED' if improvements['search_uses_markdown'] else 'MISSING'}")
        
    except FileNotFoundError:
        print("❌ Search results template not found")
        return False
    
    # Calculate success rate
    success_count = sum(improvements.values())
    total_count = len(improvements)
    success_rate = (success_count / total_count) * 100
    
    print(f"\n📊 Template Improvements: {success_count}/{total_count} ({success_rate:.0f}%)")
    
    return success_rate >= 80

def test_css_improvements():
    """Test CSS improvements for professional appearance."""
    
    print("\n🎨 Testing CSS Improvements")
    print("=" * 50)
    
    css_file = "/Users/<USER>/Desktop/RAGSearch/multi_source_rag/static/css/search_results.css"
    
    try:
        with open(css_file, 'r') as f:
            css_content = f.read()
        
        css_checks = {
            'medium_weight_headers': 'font-weight: 500' in css_content,
            'subtle_borders': 'border-bottom: 1px solid' in css_content,
            'proper_line_height': 'line-height: 1.3' in css_content,
            'response_heading_class': '.response-heading' in css_content,
            'response_list_class': '.response-list' in css_content,
            'citation_styling': '.citation-number' in css_content,
        }
        
        for check, passed in css_checks.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{status} {check.replace('_', ' ').title()}")
        
        success_rate = (sum(css_checks.values()) / len(css_checks)) * 100
        print(f"\n📊 CSS Quality: {success_rate:.0f}%")
        
        return success_rate >= 80
        
    except FileNotFoundError:
        print(f"❌ CSS file not found: {css_file}")
        return False

def test_template_filter():
    """Test the enhanced template filter functionality."""
    
    print("\n🔧 Testing Template Filter")
    print("=" * 50)
    
    try:
        from apps.search.templatetags.search_extras import markdown_to_html, humanize_dates, clean_document_references
        
        # Test document reference cleaning
        test_text = "This is a test [Document 2921] with references [Document 2922]."
        cleaned = clean_document_references(test_text)
        doc_refs_removed = '[Document' not in cleaned
        
        # Test date humanization
        date_text = "On 2024-11-06 we had a meeting."
        humanized = humanize_dates(date_text)
        dates_humanized = 'November 6, 2024' in humanized
        
        # Test markdown conversion
        markdown_text = """## Test Header [Document 123]

### Subheader
- **2024-11-06**: Test item
- Another item

This is **bold** and *italic* text with date 2024-03-15."""
        
        html_result = markdown_to_html(markdown_text)
        
        filter_checks = {
            'document_refs_removed': '[Document' not in html_result,
            'dates_humanized': 'November 6, 2024' in html_result and 'March 15, 2024' in html_result,
            'headers_styled': 'response-heading' in html_result,
            'lists_styled': 'response-list' in html_result,
            'markdown_converted': '<h2' in html_result and '<ul>' in html_result,
            'bold_preserved': '<strong>' in html_result,
            'italic_preserved': '<em>' in html_result,
        }
        
        for check, passed in filter_checks.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{status} {check.replace('_', ' ').title()}")
        
        success_rate = (sum(filter_checks.values()) / len(filter_checks)) * 100
        print(f"\n📊 Filter Quality: {success_rate:.0f}%")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ Template filter test failed: {e}")
        return False

def generate_browser_test_instructions():
    """Generate instructions for manual browser testing."""
    
    print("\n🌐 Browser Testing Instructions")
    print("=" * 50)
    
    instructions = [
        "1. Open http://127.0.0.1:8000/search/ in your browser",
        "2. Login with your credentials (<EMAIL>)",
        "",
        "🔍 Test Query 1: 'list issues reported by Amanda'",
        "   Expected: Structured list with dates, no [Document X] references",
        "   Check: Professional headers, human-readable dates, citations",
        "",
        "🔍 Test Query 2: 'whats latest on curana?'", 
        "   Expected: Chronological updates with clean formatting",
        "   Check: Timeline format, proper styling, clickable citations",
        "",
        "🔍 Test Query 3: 'explain the database schema'",
        "   Expected: Detailed explanation with proper structure",
        "   Check: Headers not too bold, good readability",
        "",
        "📱 Test Conversation Detail Page:",
        "   - Click on any conversation from the list",
        "   - Check: Consistent styling with search results",
        "   - Check: 'AI Assistant' label instead of 'Assistant'",
        "   - Check: Proper markdown formatting in messages",
        "",
        "✅ Validation Checklist:",
        "   □ No [Document XXXX] references visible",
        "   □ Dates show as 'Month Day, Year' format",
        "   □ Headers are medium weight, not bold",
        "   □ Citations are clickable and functional",
        "   □ Professional appearance throughout",
        "   □ Consistent styling between pages",
        "   □ Mobile responsive design",
    ]
    
    for instruction in instructions:
        print(instruction)
    
    return instructions

def main():
    """Main validation function."""
    
    print("🚀 Final UI Validation - Production Readiness Test")
    print("=" * 70)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all validation tests
    test_results = {}
    
    test_results['server_status'] = test_server_status()
    test_results['template_improvements'] = test_template_improvements()
    test_results['css_improvements'] = test_css_improvements()
    test_results['template_filter'] = test_template_filter()
    
    # Generate final report
    print("\n📊 FINAL VALIDATION RESULTS")
    print("=" * 70)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name.replace('_', ' ').title()}")
    
    print(f"\n🎯 Overall Success Rate: {passed_tests}/{total_tests} ({success_rate:.0f}%)")
    
    if success_rate >= 75:
        print("\n🎉 UI IS PRODUCTION READY!")
        print("✅ All major improvements implemented")
        print("✅ Professional appearance achieved")
        print("✅ Template consistency established")
        print("✅ Enhanced user experience delivered")
        
        # Generate browser test instructions
        generate_browser_test_instructions()
        
    else:
        print("\n⚠️  UI NEEDS FINAL TOUCHES")
        print("💡 Review failed tests and apply remaining fixes")
    
    return test_results

if __name__ == "__main__":
    main()
