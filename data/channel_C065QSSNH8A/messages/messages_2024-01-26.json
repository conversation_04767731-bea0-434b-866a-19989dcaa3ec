{"date": "2024-01-26", "channel_id": "C065QSSNH8A", "message_count": 11, "messages": [{"ts": "1706290164.420289", "text": "<@U0658EW4B8D> <@U065H3M6WJV> Do you have any suggestions on how to help <PERSON>?\nHi <PERSON><PERSON><PERSON>,\nCurious what you all and other customers are using to compile market data to create compensation bands? With the acquisition of OPtion Impact by Pave the data in there seems really spotty, not to mention we can't see equity because we don't have a cap table and therefore we have no data to upload in exchange for seeing their data. And Radford is just so confusing, its hard to understand what the right data is to look at.\nLet me know if you have any advice.\nThanks!\nlisa\n\n<@U065H3M6WJV> can you pls also try to get <PERSON>'s feedback on this?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1706290164.420289", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "OjIcx", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " Do you have any suggestions on how to help <PERSON>?\nHi <PERSON>,\nCurious what you all and other customers are using to compile market data to create compensation bands? With the acquisition of OPtion Impact by Pave the data in there seems really spotty, not to mention we can't see equity because we don't have a cap table and therefore we have no data to upload in exchange for seeing their data. And <PERSON><PERSON> is just so confusing, its hard to understand what the right data is to look at.\nLet me know if you have any advice.\nThanks!\nlisa\n\n"}, {"type": "user", "user_id": "U065H3M6WJV"}, {"type": "text", "text": " can you pls also try to get <PERSON>'s feedback on this?"}]}]}]}, {"ts": "1706241193.582769", "text": "Priorities for Eng for the next day:\n• :repeat: SDF: Allow inputting new titles for promotions (<https://compiify.atlassian.net/browse/COM-2198|COM-2198>) - In development - *ETA 1/26*\n• Fix adjustment letter variables (<https://compiify.atlassian.net/browse/COM-2224|COM-2224>)\n• SDF / DA / Neuroflow: Date drift in cycle steps (<https://compiify.atlassian.net/browse/COM-2223|COM-2223>)\n• Currency conversion issues (<https://compiify.atlassian.net/browse/COM-1919|COM-1919> and <https://compiify.atlassian.net/browse/COM-2142|COM-2142>)\n• Enable comment (view/add) on \"Reviewed\" rows (<https://compiify.atlassian.net/browse/COM-2140|COM-2140>)\n• Promotion job-title display issue (<https://compiify.atlassian.net/browse/COM-2212|COM-2212>)\n• Export filename bug (<https://compiify.atlassian.net/browse/COM-2213|COM-2213>)\nAfter this continue with the rest of <https://compiify.atlassian.net/browse/COM-2087|Wave 3> from top to bottom.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706241193.582769", "reply_count": 1, "edited": {"user": "U065H3M6WJV", "ts": "1706242178.000000"}, "blocks": [{"type": "rich_text", "block_id": "Elej2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " SDF: Allow inputting new titles for promotions ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2198", "text": "COM-2198"}, {"type": "text", "text": ") - In development - "}, {"type": "text", "text": "ETA 1/26", "style": {"bold": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Fix adjustment letter variables ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2224", "text": "COM-2224"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF / DA / Neuroflow: Date drift in cycle steps ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2223", "text": "COM-2223"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Currency conversion issues ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-1919", "text": "COM-1919"}, {"type": "text", "text": " and "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2142", "text": "COM-2142"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Enable comment (view/add) on \"Reviewed\" rows ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2140", "text": "COM-2140"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Promotion job-title display issue ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2212", "text": "COM-2212"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Export filename bug ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2213", "text": "COM-2213"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAfter this continue with the rest of "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " from top to bottom."}]}]}]}, {"ts": "1706236333.620429", "text": "<@U0658EW4B8D> I have uploaded <PERSON>'s templates on qa environment.( I already see few fields are not getting updated with letters generated via new templates, i will check them post dinner and revert back) Feel free to raise bugs in the meanwhile.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1706236333.620429", "reply_count": 6, "edited": {"user": "U04DKEFP1K8", "ts": "1706237085.000000"}, "blocks": [{"type": "rich_text", "block_id": "rMITk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " I have uploaded <PERSON>'s templates on qa environment.( I already see few fields are not getting updated with letters generated via new templates, i will check them post dinner and revert back) Feel free to raise bugs in the meanwhile."}]}]}]}, {"ts": "1706232925.844119", "text": "That is exactly what I am looking for thank you", "user": "U0658EW4B8D", "type": "message", "reactions": [{"name": "meow_attention", "users": ["U065H3M6WJV"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "h488h", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That is exactly what I am looking for thank you"}]}]}]}, {"ts": "1706232826.188189", "text": "we don't filter in hierarchy view", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "z1Iia", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we don't filter in hierarchy view"}]}]}]}, {"ts": "1706232817.717169", "text": "Filters are there - but you have to click the \"List view\" icon", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Vi8U1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Filters are there - but you have to click the \"List view\" icon"}]}]}]}, {"ts": "1706232788.487399", "text": "Sub tab", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "nYP6p", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sub tab"}]}]}]}, {"ts": "1706232772.819669", "text": "You mean \"Organization\" view outside of Merit view? Or the sub-tab of Merit View?", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "jpbur", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "You mean \"Organization\" view outside of Merit view? Or the sub-tab of Merit View?"}]}]}]}, {"ts": "1706232745.816849", "text": "Would love to have Table filters for org view :eyes:", "user": "U0658EW4B8D", "type": "message", "blocks": [{"type": "rich_text", "block_id": "ogqPm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Would love to have Table filters for org view "}, {"type": "emoji", "name": "eyes", "unicode": "1f440"}]}]}]}, {"ts": "1706232382.723659", "text": "<@U0658EW4B8D> Edits in progress - need another ~20 min at least, but I think we'll have a more readable version of the letter for you today! :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "4t0YN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " Edits in progress - need another ~20 min at least, but I think we'll have a more readable version of the letter for you today! "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1706210817.601269", "text": "SDF To-do's\n• PROD: Remove existing performance ratings and ensure Merit view still loads (they'll give us a new upload Feb 15th)\n• PROD: Remove prefills and make sure the guidance tooltip still shows\n• PROD: Enable planners to enter a promotion title or select \"Other\" if the one they want isn't in our list\n• TEST: Load artificial data", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706210817.601269", "reply_count": 3, "reactions": [{"name": "white_check_mark", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "x+5XC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SDF To-do's\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "PROD: Remove existing performance ratings and ensure Merit view still loads (they'll give us a new upload Feb 15th)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "PROD: Remove prefills and make sure the guidance tooltip still shows"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "PROD: Enable planners to enter a promotion title or select \"Other\" if the one they want isn't in our list"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "TEST: Load artificial data"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.588076"}