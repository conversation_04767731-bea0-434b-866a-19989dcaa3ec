#!/usr/bin/env python3
"""
Debug the citation issue by checking node_id mapping.
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.unified_rag_service import UnifiedRAGService
from apps.accounts.models import Tenant, User
from apps.documents.models import DocumentChunk

def debug_citation_mapping():
    """Debug the citation node_id mapping issue."""
    print("🔍 Debugging Citation Node ID Mapping")
    print("=" * 50)

    # Get tenant and user
    tenant = Tenant.objects.get(slug='stride')
    user = User.objects.filter(email='<EMAIL>').first()

    if not user:
        print("❌ User not found")
        return

    # Initialize RAG service
    print("🔧 Initializing UnifiedRAGService...")
    rag_service = UnifiedRAGService('stride', user)

    # Perform search
    query = "whats latest on curana?"
    print(f"🔎 Searching for: '{query}'")

    # Execute query directly with citation engine to get source nodes
    response = rag_service.citation_engine.query(query)
    source_nodes = response.source_nodes

    print(f"\n📊 Found {len(source_nodes)} source nodes")

    # Check each node
    for i, node in enumerate(source_nodes[:10]):  # Check first 10
        node_id = node.node_id
        print(f"\n🔍 Node {i+1}:")
        print(f"   Node ID: {node_id}")
        print(f"   Score: {getattr(node, 'score', 'N/A')}")

        # Try to find corresponding chunk
        chunk = DocumentChunk.objects.filter(
            tenant=tenant,
            metadata__node_id=node_id
        ).first()

        if chunk:
            print(f"   ✅ Found chunk: {chunk.id}")
            print(f"   Chunk text preview: {chunk.text[:100]}...")
        else:
            print(f"   ❌ No chunk found for node_id: {node_id}")

            # Check if any chunks have similar node_ids
            similar_chunks = DocumentChunk.objects.filter(
                tenant=tenant,
                metadata__node_id__icontains=node_id[:8]  # First 8 chars
            )

            if similar_chunks.exists():
                print(f"   🔍 Found {similar_chunks.count()} chunks with similar node_id prefix")
                for chunk in similar_chunks[:3]:
                    print(f"      Similar: {chunk.metadata.get('node_id')}")
            else:
                print(f"   🔍 No chunks found with similar node_id prefix")

    print("\n" + "=" * 50)
    print("🔍 Checking database chunks with 'curana' content...")

    # Find chunks containing curana
    curana_chunks = DocumentChunk.objects.filter(
        tenant=tenant,
        text__icontains='curana'
    )[:5]

    print(f"📊 Found {curana_chunks.count()} chunks containing 'curana'")

    for chunk in curana_chunks:
        print(f"\n📄 Chunk {chunk.id}:")
        print(f"   Node ID: {chunk.metadata.get('node_id')}")
        print(f"   Text preview: {chunk.text[:100]}...")

if __name__ == "__main__":
    debug_citation_mapping()
