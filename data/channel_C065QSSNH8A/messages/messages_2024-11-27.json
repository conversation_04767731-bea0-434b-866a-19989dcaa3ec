{"date": "2024-11-27", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1732726845.964279", "text": "<https://docs.google.com/spreadsheets/d/1MY7uJ55XVaOJAXjPHJRkQvrayIPoP2kl/edit?gid=1855194697#gid=1855194697>", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1732726845.964279", "reply_count": 3, "files": [{"id": "F082NNP8CLV", "created": 1732726853, "timestamp": 1732726853, "name": "Pro-ration Concept.xlsx", "title": "Pro-ration Concept.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 63744, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1MY7uJ55XVaOJAXjPHJRkQvrayIPoP2kl", "external_url": "https://docs.google.com/spreadsheets/d/1MY7uJ55XVaOJAXjPHJRkQvrayIPoP2kl/edit?gid=1855194697#gid=1855194697", "url_private": "https://docs.google.com/spreadsheets/d/1MY7uJ55XVaOJAXjPHJRkQvrayIPoP2kl/edit?gid=1855194697#gid=1855194697", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F082NNP8CLV-a86a2dbef9/pro-ration_concept_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F082NNP8CLV-a86a2dbef9/pro-ration_concept_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F082NNP8CLV-a86a2dbef9/pro-ration_concept_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F082NNP8CLV-a86a2dbef9/pro-ration_concept_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F082NNP8CLV-a86a2dbef9/pro-ration_concept_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F082NNP8CLV-a86a2dbef9/pro-ration_concept_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F082NNP8CLV-a86a2dbef9/pro-ration_concept_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F082NNP8CLV-a86a2dbef9/pro-ration_concept_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F082NNP8CLV-a86a2dbef9/pro-ration_concept_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHSY49KAT7U1vvYpd31pXAdzRTA9KG5pgOooooAQrk5yaTZ7n86dRQA3Z7n86ULg9TS0UAFFFFABRRRQAUUUUAFFFFAH//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F082NNP8CLV/pro-ration_concept.xlsx", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "KGhsM", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://docs.google.com/spreadsheets/d/1MY7uJ55XVaOJAXjPHJRkQvrayIPoP2kl/edit?gid=1855194697#gid=1855194697"}]}]}]}, {"ts": "1732697558.616709", "text": "<@U07MH77PUBV> will be joining for Bonus discussion", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "tXgNr", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07MH77PUBV"}, {"type": "text", "text": " will be joining for Bonus discussion"}]}]}]}, {"ts": "1732697432.762019", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S>\n*Agenda for today:*\n• Bonus Budget planning clarification\n• Alaycare eligibiltiy issue\n• Curana cycle issues", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VTv/b", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": "\n"}, {"type": "text", "text": "Agenda for today:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Bonus Budget planning clarification"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Alaycare eligibiltiy issue"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Curana cycle issues"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1732686854.890369", "text": "<@U07EJ2LP44S> is there anything that we need to do to meet their requirements for HRBP.L? If so, have you already created the ticket for it?\nCc <@U0690EB5JE5> ", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1732213986.492409", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "p7P1v", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " is there anything that we need to do to meet their requirements for HRBP.L? If so, have you already created the ticket for it?\nCc "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " "}]}]}]}, {"ts": "1732686101.978239", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> We are not clear on how the budget planning should work for Bonus in cycle builder. I have some thoughts how it should work but would like align on the approach before we implement the changes.\nThis will be first Agenda for today's call.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1732686101.978239", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "xKQqA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We are not clear on how the budget planning should work for Bonus in cycle builder. I have some thoughts how it should work but would like align on the approach before we implement the changes.\nThis will be first Agenda for today's call."}]}]}]}, {"ts": "1732685336.542279", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> Sharing the details of Regression test Automation work\n<https://docs.google.com/spreadsheets/d/1p9jOmCZapPT6E8ntG42TBFBeogYH1HWCJTMwOK_6WLQ/edit?gid=0#gid=0>\nPlease go through and let us know if we are on the right direction", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1732685336.542279", "reply_count": 2, "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "a146m", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Sharing the details of Regression test Automation work\n"}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1p9jOmCZapPT6E8ntG42TBFBeogYH1HWCJTMwOK_6WLQ/edit?gid=0#gid=0"}, {"type": "text", "text": "\nPlease go through and let us know if we are on the right direction"}]}]}]}, {"ts": "1732653673.434269", "text": "Alayacare user eligibility issue (~and also maybe a cycle builder bug~): <https://compiify.atlassian.net/browse/COM-4005>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732653673.434269", "reply_count": 12, "edited": {"user": "U07EJ2LP44S", "ts": "1732653799.000000"}, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14088::b5ab8fce21ea48f0839475191e7fbf23", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4005?atlOrigin=eyJpIjoiNjAyZDUxNmYzNTFiNDdiMzg2ZWQ1OWRjYTFlY2I2YjgiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4005 Employee Ineligibility Issue in AlayaCare Environment>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14088::84a87a621fb34048a1cf36df230aa1e0", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14088\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4005\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4005", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "l7Hhh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Alayacare user eligibility issue ("}, {"type": "text", "text": "and also maybe a cycle builder bug", "style": {"strike": true}}, {"type": "text", "text": "): "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4005"}]}]}]}, {"ts": "1732651872.466409", "text": "I put this in the Implementation Efficiency epic - this is the 'final' step of the data analysis for Curana, this is a pretty manual and confusing process. <https://compiify.atlassian.net/browse/COM-4004>", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "eyes", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14087::3dc0079281554ae895b0056920fab7b6", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4004?atlOrigin=eyJpIjoiMWQ2MWE4NWE3YTFlNDhmNGE5ZGQ3OTY4N2FiMGFhYmYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4004 Automating Data Analysis for Curana>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14087::8fdddf01cf54462e8d048e3ef534b874", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14087\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4004\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4004", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "5LjtK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I put this in the Implementation Efficiency epic - this is the 'final' step of the data analysis for Curana, this is a pretty manual and confusing process. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4004"}]}]}]}, {"ts": "1732646376.519629", "text": "Apparently I should have knocked wood when I said the comp builder was working. When I was building it out with <PERSON><PERSON><PERSON> on a call today, it crashed multiple times and exhibited some super weird behavior. My long winded video is in the bug. <https://compiify.atlassian.net/browse/COM-4003>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1732646376.519629", "reply_count": 7, "reactions": [{"name": "eyes", "users": ["U07M6QKHUC9"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14086::b4fad851c69e4fc88c75194491bc154d", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4003?atlOrigin=eyJpIjoiNjJmZGU0MGU1Y2ZiNDYwNjk5NGUzMmQxZDMzYzYwN2MiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4003 Issue: Comp Builder behaving erratically and crashing during setup …>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14086::5b2057dede344d77838d8e2cb134c96c", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14086\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4003\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4003", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "P6QNS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Apparently I should have knocked wood when I said the comp builder was working. When I was building it out with <PERSON><PERSON><PERSON> on a call today, it crashed multiple times and exhibited some super weird behavior. My long winded video is in the bug. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4003"}]}]}]}], "created_at": "2025-05-22T21:35:34.683489"}