{"date": "2024-10-04", "channel_id": "C065QSSNH8A", "message_count": 24, "messages": [{"ts": "1728060318.941759", "text": "ChatGPT case study: <https://docs.google.com/document/d/1zNZV-8F6UWrfa74HF34kdEiODFthm2JzJxOx__DNefU/edit?usp=sharing>", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F07Q98KEW0N", "created": 1728060321, "timestamp": 1728060321, "name": "Nauto Case Study", "title": "Nauto Case Study", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1zNZV-8F6UWrfa74HF34kdEiODFthm2JzJxOx__DNefU", "external_url": "https://docs.google.com/document/d/1zNZV-8F6UWrfa74HF34kdEiODFthm2JzJxOx__DNefU/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1zNZV-8F6UWrfa74HF34kdEiODFthm2JzJxOx__DNefU/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07Q98KEW0N-373f7e1bea/nauto_case_study_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07Q98KEW0N-373f7e1bea/nauto_case_study_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07Q98KEW0N-373f7e1bea/nauto_case_study_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07Q98KEW0N-373f7e1bea/nauto_case_study_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07Q98KEW0N-373f7e1bea/nauto_case_study_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07Q98KEW0N-373f7e1bea/nauto_case_study_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07Q98KEW0N-373f7e1bea/nauto_case_study_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07Q98KEW0N-373f7e1bea/nauto_case_study_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07Q98KEW0N-373f7e1bea/nauto_case_study_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSLY7E/SjPsaa/UU2gCTJ9DSg+xFR4pQOeRQA+iiigBrevNNz9fzpzZzxSY9zQAA/X86cBjuabj3NKBz1oAdRRRQAjdMUwAeop7evpTc/7NABge1KAM9f1pM/7NKDz92gB1FFFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07Q98KEW0N/nauto_case_study", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "FytBe", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ChatGPT case study: "}, {"type": "link", "url": "https://docs.google.com/document/d/1zNZV-8F6UWrfa74HF34kdEiODFthm2JzJxOx__DNefU/edit?usp=sharing"}]}]}]}, {"ts": "1728059339.460349", "text": "<https://app.fireflies.ai/view/Stride-case-study::zTAE58GcsiokPDZj>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1728059339.460349", "reply_count": 2, "attachments": [{"from_url": "https://app.fireflies.ai/view/Stride-case-study::zTAE58GcsiokPDZj", "image_url": "https://files.fireflies.ai/app/img/preview.png", "image_width": 1280, "image_height": 720, "image_bytes": 188965, "service_icon": "https://app.fireflies.ai/favicon.ico", "id": 1, "original_url": "https://app.fireflies.ai/view/Stride-case-study::zTAE58GcsiokPDZj", "fallback": "Fireflies.ai - #1 <PERSON> Notetaker", "text": "Record, transcribe, search and collaborate across your meetings. Fireflies takes notes for your meetings and turn words into actions.", "title": "Fireflies.ai - #1 <PERSON> Notetaker", "title_link": "https://app.fireflies.ai/view/Stride-case-study::zTAE58GcsiokPDZj", "service_name": "app.fireflies.ai"}], "blocks": [{"type": "rich_text", "block_id": "7Z92p", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://app.fireflies.ai/view/Stride-case-study::zTAE58GcsiokPDZj"}]}]}]}, {"ts": "1728059337.091779", "text": "<@U07EJ2LP44S> FYA", "user": "U07M6QKHUC9", "type": "message", "files": [{"id": "F07QCU36RFX", "created": 1728059331, "timestamp": 1728059331, "name": "Comprehensive Case Study - Mercury.pdf", "title": "Comprehensive Case Study - Mercury.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 5164684, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07QCU36RFX/comprehensive_case_study_-_mercury.pdf", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07QCU36RFX/download/comprehensive_case_study_-_mercury.pdf", "media_display_type": "unknown", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QCU36RFX-861231c9e5/comprehensive_case_study_-_mercury_thumb_pdf.png", "thumb_pdf_w": 935, "thumb_pdf_h": 1210, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07QCU36RFX/comprehensive_case_study_-_mercury.pdf", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07QCU36RFX-b2ea586ea1", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "qqtDV", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " FYA"}]}]}]}, {"ts": "1728057353.953279", "text": "<@U07EJ2LP44S> Looks like your tickets more of a change than a bug. Please discuss with <@U04DKEFP1K8> and update ticket what would be expected, We will prioritize accordingly\n<https://compiify.atlassian.net/browse/COM-3675>\n<https://compiify.atlassian.net/browse/COM-3696>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1728057353.953279", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "LtrAZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Looks like your tickets more of a change than a bug. Please discuss with "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " and update ticket what would be expected, We will prioritize accordingly\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3675"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3696"}]}]}]}, {"ts": "1728047748.852469", "text": "PRs deployed today\n<https://github.com/Compiify/Yosemite/pull/1657>\n<https://github.com/Compiify/Yosemite/pull/1658>\n<https://github.com/Compiify/Yellowstone/pull/1615>\n<https://github.com/Compiify/Yellowstone/pull/1616> - This there no ticket. We did regress flags and fixed some bugs", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "11PHk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "PRs deployed today\n"}, {"type": "link", "url": "https://github.com/Compiify/Yosemite/pull/1657"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://github.com/Compiify/Yosemite/pull/1658"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://github.com/Compiify/Yellowstone/pull/1615"}, {"type": "text", "text": "\n"}, {"type": "link", "url": "https://github.com/Compiify/Yellowstone/pull/1616"}, {"type": "text", "text": " - This there no ticket. We did regress flags and fixed some bugs"}]}]}]}, {"ts": "1728047665.700419", "text": "<@U04DKEFP1K8> All the ENVs are upto date with all the fixes changes till now.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "XMSe6", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " All the ENVs are upto date with all the fixes changes till now."}]}]}]}, {"ts": "1728047172.469259", "text": "I will need to drop after sharing my updates today in leadership standup. I need to travel out of town early my morning around 4am for some work and need to get some sleep", "user": "U0690EB5JE5", "type": "message", "edited": {"user": "U0690EB5JE5", "ts": "1728048360.000000"}, "blocks": [{"type": "rich_text", "block_id": "K404/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I will need to drop after sharing my updates today in leadership standup. I need to travel out of town early my morning around 4am for some work"}, {"type": "text", "text": " and need to get some sleep"}]}]}]}, {"ts": "**********.793729", "text": "<@U07EJ2LP44S> I was able to find the data point to filter employees from their groups. They have a group called \"GROUP\" :slightly_smiling_face: . I filtered for these 3 groups and got around 356 employees. Could you please confirm the same with customer. Also I am still unable to pull comp data.\n• \"Healthplan Admin\",\n•  \"Shared Services\",\n•  \"Med Group Admin\"\nAlso you mentioned above `Exclude: Physicians, APP, Leraners` is it different data field in HRIS other than group?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "**********.717399", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "**********.000000"}, "blocks": [{"type": "rich_text", "block_id": "FMHEI", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I was able to find the data point to filter employees from their groups. They have a group called \"GROUP\" "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " . I filtered for these 3 groups and got around 356 employees. Could you please confirm the same"}, {"type": "text", "text": " with customer"}, {"type": "text", "text": ". Also I am still unable to pull comp data.\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "\"Healthplan Admin\","}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": " \"Shared Services\","}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": " \"Med Group Admin\""}]}], "style": "bullet", "indent": 0, "offset": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAlso you mentioned above "}, {"type": "text", "text": "Exclude: Physicians, APP, Leraners", "style": {"bold": false, "italic": false, "code": true}}, {"type": "text", "text": " is it different data field in HRIS other than group?"}]}]}]}, {"ts": "**********.685319", "text": "<@U07EJ2LP44S> <https://tithely.stridehr.io/> all set and Just works ready to connect as well", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "zcuCQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://tithely.stridehr.io/"}, {"type": "text", "text": " all set and Just works ready to connect as well"}]}]}]}, {"ts": "**********.534899", "text": "We will be shutting down the PlayQ and Practifi cloud environments and deleting Vercara. Please let me know if everyone is okay with this plan.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.534899", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "b+vVK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We will be shutting down the PlayQ and Practifi cloud environments and deleting Vercara. Please let me know if everyone is okay with this plan."}]}]}]}, {"ts": "**********.411909", "text": "<@U04DKEFP1K8> do you have a sec to join the zoom call. I am getting some authenticatior errors from aws for sendgrid account", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.411909", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "aVxax", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " do you have a sec to join the zoom call. I am getting some authenticatior errors from aws for sendgrid account"}]}]}]}, {"ts": "**********.381459", "text": "What is happening with practi<PERSON>?", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "**********.381459", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "4aP8+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "What is happening with practi<PERSON>?"}]}]}]}, {"ts": "**********.413139", "text": "What are we using stridenow for? Just curious", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.413139", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "7PYsx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "What are we using stridenow for? Just curious"}]}]}]}, {"ts": "**********.043349", "text": "my email from stridenow is still going to spam", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.043349", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "4rHtj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "my email from stridenow is still going to spam"}]}]}]}, {"ts": "1727990969.684339", "text": "here are the results for stridehr so you can see the difference", "user": "U07M6QKHUC9", "type": "message", "files": [{"id": "F07QPP22EM7", "created": 1727990965, "timestamp": 1727990965, "name": "Screenshot 2024-10-03 at 2.29.20 PM.png", "title": "Screenshot 2024-10-03 at 2.29.20 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 325571, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07QPP22EM7/screenshot_2024-10-03_at_2.29.20___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07QPP22EM7/download/screenshot_2024-10-03_at_2.29.20___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QPP22EM7-5505263446/screenshot_2024-10-03_at_2.29.20___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QPP22EM7-5505263446/screenshot_2024-10-03_at_2.29.20___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QPP22EM7-5505263446/screenshot_2024-10-03_at_2.29.20___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 167, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QPP22EM7-5505263446/screenshot_2024-10-03_at_2.29.20___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 223, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QPP22EM7-5505263446/screenshot_2024-10-03_at_2.29.20___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QPP22EM7-5505263446/screenshot_2024-10-03_at_2.29.20___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 334, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QPP22EM7-5505263446/screenshot_2024-10-03_at_2.29.20___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 372, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QPP22EM7-5505263446/screenshot_2024-10-03_at_2.29.20___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 446, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07QPP22EM7-5505263446/screenshot_2024-10-03_at_2.29.20___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 476, "original_w": 3372, "original_h": 1566, "thumb_tiny": "AwAWADDQANAXB4zTqB1oAMH1NGD6mlooATHuaMe9A6UtACUc0UtABz7UnPtS0UAJz7UvPtRRQB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07QPP22EM7/screenshot_2024-10-03_at_2.29.20___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07QPP22EM7-246e154eb3", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "82oK4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "here are the results for stridehr so you can see the difference"}]}]}]}, {"ts": "1727990931.986979", "text": "<@U04DKEFP1K8> DMARC is not compliant", "user": "U07M6QKHUC9", "type": "message", "files": [{"id": "F07PWG8CQ3Z", "created": 1727990926, "timestamp": 1727990926, "name": "Screenshot 2024-10-03 at 2.28.22 PM.png", "title": "Screenshot 2024-10-03 at 2.28.22 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 367410, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07PWG8CQ3Z/screenshot_2024-10-03_at_2.28.22___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07PWG8CQ3Z/download/screenshot_2024-10-03_at_2.28.22___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PWG8CQ3Z-dfdf5e739a/screenshot_2024-10-03_at_2.28.22___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PWG8CQ3Z-dfdf5e739a/screenshot_2024-10-03_at_2.28.22___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PWG8CQ3Z-dfdf5e739a/screenshot_2024-10-03_at_2.28.22___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 165, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PWG8CQ3Z-dfdf5e739a/screenshot_2024-10-03_at_2.28.22___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 220, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PWG8CQ3Z-dfdf5e739a/screenshot_2024-10-03_at_2.28.22___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PWG8CQ3Z-dfdf5e739a/screenshot_2024-10-03_at_2.28.22___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 331, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PWG8CQ3Z-dfdf5e739a/screenshot_2024-10-03_at_2.28.22___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 367, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PWG8CQ3Z-dfdf5e739a/screenshot_2024-10-03_at_2.28.22___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 441, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07PWG8CQ3Z-dfdf5e739a/screenshot_2024-10-03_at_2.28.22___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 470, "original_w": 3362, "original_h": 1544, "thumb_tiny": "AwAWADDQANAXHTP506gUAGPc0Y+tAzijn2oAMe5oK57mlooASgA+tFLQAmPejHvS0UAJj3pfxoooA//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07PWG8CQ3Z/screenshot_2024-10-03_at_2.28.22___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07PWG8CQ3Z-077dfdad06", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "LIn7q", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " DMARC is not compliant"}]}]}]}, {"ts": "1727990804.554889", "text": "<@U04DKEFP1K8> what's the cloud cost for last month?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1727990804.554889", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "nVhVa", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " what's the cloud cost for last month?"}]}]}]}, {"ts": "**********.451519", "text": "Wouldn't it be better to just create a new EPIC for SDF and move all of these bugs in it?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.451519", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "0zwPE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Wouldn't it be better to just create a new EPIC for SDF and move all of these bugs in it?"}]}]}]}, {"ts": "**********.127439", "text": "Also I will resume testing tomorrow, but all the bugs I found are here: <https://compiify.atlassian.net/browse/COM-3626>\n\nI used the Demo account issues Epic and added the label 'Prioritized' to all, hopefully everything came across properly.", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13687::168a0083efbb44fda6991ea389165ebc", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3626?atlOrigin=eyJpIjoiZTE4YjVkMjUwODcwNGFmMjhjODg5YjBhNWYyMjE2ODQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3626 Demo account issues [Sep 2024]>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13687::caa39100212a485ab9a1ebe4cb61d687", "elements": [{"type": "mrkdwn", "text": "Status: *In QA*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/39b4b364807f203c465b678307a3f359?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"13687\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-3626\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3626", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "2ouTb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also I will resume testing tomorrow, but all the bugs I found are here: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3626"}, {"type": "text", "text": "\n\nI used the Demo account issues Epic and added the label 'Prioritized' to all, hopefully everything came across properly."}]}]}]}, {"ts": "**********.752149", "text": "<@U04DKEFP1K8><@U0690EB5JE5> Can we push the clickable cycle builder tabs to the sdf test account? It would make the testing go a lot faster.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.752149", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "jpSxF", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Can we push the clickable cycle builder tabs to the sdf test account? It would make the testing go a lot faster."}]}]}]}, {"ts": "**********.305329", "text": "She also kind of got annoyed with me when I asked her to elaborate on 'it made it easier' LOL. I'm like, <PERSON> I need something for the case study.", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "grinning", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "npf+x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She also kind of got annoyed with me when I asked her to elaborate on 'it made it easier' LOL. I'm like, <PERSON> I need something for the case study."}]}]}]}, {"ts": "**********.196899", "text": "Sound good. That's still a win.\nWe can tag <PERSON><PERSON> as a customer without referring <PERSON><PERSON>, which you were planning to do that anyway", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Cj2qy", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Sound good. That's still a win.\nWe can tag <PERSON><PERSON> as a customer without referring <PERSON><PERSON>, which you were planning to do that anyway"}]}]}]}, {"ts": "1727981854.247529", "text": "<@U07M6QKHUC9> Just got off the case study call with <PERSON>. Two major things - she did not consent to being on video. She actually strongly refused. So it has to be text only. She did consent to using it in marketing, tagging in LI posts etc.\n\nThe second thing is she does NOT want <PERSON>'s name associated with <PERSON><PERSON>. So keeping <PERSON>'s testimonials as a 'beta user' or something is all we can do. I kind of thought it might go this way which is why we recorded it the way we did.", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "dxWat", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Just got off the case study call with <PERSON>. Two major things - she did not consent to being on video. She actually strongly refused. So it has to be text only. She did consent to using it in marketing, tagging in LI posts etc.\n\nThe second thing is she does NOT want <PERSON>'s name associated with <PERSON><PERSON>. So keeping <PERSON>'s testimonials as a 'beta user' or something is all we can do. I kind of thought it might go this way which is why we recorded it the way we did."}]}]}]}, {"ts": "1727980865.557049", "text": "<@U07EJ2LP44S> <@U07M6QKHUC9> As a workaround for this cycle\n\nSince <PERSON><PERSON> and <PERSON><PERSON><PERSON> will be entering all the adjustments themselves for this cycle, they can select the entire organization ( In Merit View tasks), filter by department, complete the updates, and before they move on to the next department, adjusting the rating scale as needed in cycle builder and complete adjustment for the next department. They can continue this process until all departments are updated. This approach won’t impact their budget, as we’re using the actual amounts for the adjustments.", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1727979891.610209", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "2izqP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " As a workaround for this cycle\n\nSince <PERSON><PERSON> and <PERSON><PERSON><PERSON> will be entering all the adjustments themselves for this cycle, they can select the entire organization ( In Merit View tasks), filter by department, complete the updates, and before they move on to the next department, adjusting the rating scale as needed in cycle builder and complete adjustment for the next department. They can continue this process until all departments are updated. This approach won’t impact their budget, as we’re using the actual amounts for the adjustments."}]}]}]}], "created_at": "2025-05-22T21:35:34.651425"}