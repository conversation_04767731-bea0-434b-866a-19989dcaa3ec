# RAGSearch Guides

This document provides comprehensive guides for working with the RAGSearch system.

## Table of Contents

1. [Setup and Installation](#setup-and-installation)
2. [Data Ingestion](#data-ingestion)
3. [Search and Retrieval](#search-and-retrieval)
4. [System Management](#system-management)
5. [Development](#development)

## Setup and Installation

### Environment Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd RAGSearch
   ```

2. Set up a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   poetry install
   ```

4. Create a `.env` file based on `.env.example`:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. Set up the database:
   ```bash
   python manage.py migrate
   ```

6. Create a superuser:
   ```bash
   python manage.py createsuperuser
   ```

7. Run the development server:
   ```bash
   python manage.py runserver
   ```

### Docker Setup

1. Build and start the containers:
   ```bash
   docker-compose up -d
   ```

2. Run migrations:
   ```bash
   docker-compose exec web python manage.py migrate
   ```

3. Create a superuser:
   ```bash
   docker-compose exec web python manage.py createsuperuser
   ```

## Data Ingestion

### Slack Data Ingestion

1. Prepare Slack data files in JSON format and place them in the `data/slack/` directory.

2. Configure a document source in the admin interface:
   - Go to Admin > Documents > Document Sources
   - Create a new document source with type "local_slack"
   - Configure the source with the following settings:
     ```json
     {
       "data_dir": "data/slack/",
       "time_period": "custom",
       "custom_days": 730,
       "enable_semantic_cross_refs": true,
       "use_embeddings": true,
       "quality_threshold": 0.3,
       "max_documents_per_channel": 1000
     }
     ```

3. Run the ingestion command:
   ```bash
   python manage.py ingest_documents --source-id <source-id>
   ```

4. Monitor the ingestion process in the admin interface under Document Processing Jobs.

### Time-Based Slack Chunking

The system supports different time-based chunking strategies for Slack data:

- **Monthly**: Groups messages by calendar month
- **Weekly**: Groups messages by week
- **Daily**: Groups messages by day
- **Custom**: Groups messages by a custom number of days

Configure the chunking strategy in the document source configuration:

```json
{
  "time_period": "monthly",  // Options: "monthly", "weekly", "daily", "custom"
  "custom_days": 30          // Only used when time_period is "custom"
}
```

### Conversation-Aware Chunking

For more advanced chunking that preserves conversation context:

1. Enable conversation-aware chunking in the document source configuration:
   ```json
   {
     "use_conversation_aware_chunking": true,
     "max_chunk_size": 1000,
     "overlap_size": 100,
     "preserve_boundaries": true,
     "respect_threads": true
   }
   ```

2. This will use the enhanced message processor that implements:
   - Thread-based clustering
   - Temporal clustering
   - Semantic clustering
   - Anti-fragmentation optimization

## Search and Retrieval

### Basic Search

1. Access the search interface at `/search/`
2. Enter a natural language query
3. View the generated response with citations

### Advanced Search Options

The search API supports several advanced options:

- **Hybrid Search**: Combines vector similarity and keyword matching
  ```
  ?use_hybrid_search=true
  ```

- **Context-Aware Retrieval**: Considers document relationships
  ```
  ?use_context_aware=true
  ```

- **Query Expansion**: Generates multiple query variations
  ```
  ?use_query_expansion=true
  ```

- **Result Limit**: Controls the number of results
  ```
  ?top_k=20
  ```

- **Minimum Relevance**: Filters results by relevance score
  ```
  ?min_relevance_score=0.4
  ```

### API Usage

The search API is available at `/api/search/`:

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Token <api-token>" \
  -d '{"query": "What is RAG?", "top_k": 20, "use_hybrid_search": true}' \
  http://localhost:8000/api/search/
```

## System Management

### Database Management

#### Cleaning the Database

To clean the database and start fresh:

```bash
python scripts/clean_databases.py
```

#### Data Consistency Check

To verify data consistency across PostgreSQL and Qdrant:

```bash
python manage.py shell < scripts/verify_data_consistency.py
```

### Collection Management

#### Creating a New Collection

```bash
python manage.py create_vector_collection --tenant-id <tenant-id> --name <collection-name> --dimension 768
```

#### Deleting a Collection

```bash
python manage.py delete_vector_collection --collection-name <collection-name>
```

## Development

### Adding a New Document Source

1. Create a new source interface in `apps/documents/interfaces/`:

```python
from .base import BaseSourceInterface

class NewSourceInterface(BaseSourceInterface):
    source_type = "new_source"
    
    def __init__(self, config):
        super().__init__(config)
        # Initialize source-specific configuration
        
    def fetch_documents(self):
        # Implement document fetching logic
        pass
        
    def process_documents(self, documents):
        # Implement document processing logic
        pass
```

2. Register the interface in `apps/documents/interfaces/__init__.py`:

```python
from .new_source import NewSourceInterface

SOURCE_INTERFACES = {
    "local_slack": LocalSlackSourceInterface,
    "github": GitHubSourceInterface,
    "new_source": NewSourceInterface,
}
```

### Testing

Run the test suite:

```bash
python manage.py test
```

Run specific tests:

```bash
python manage.py test apps.documents.tests.test_ingestion
```

### Debugging

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

Use the Django debug toolbar by adding your IP to `INTERNAL_IPS` in `settings/development.py`.
