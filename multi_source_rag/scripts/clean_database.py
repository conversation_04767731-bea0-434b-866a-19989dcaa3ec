#!/usr/bin/env python
"""
Enhanced Database Cleaning Script for RAGSearch

This script provides comprehensive database cleaning functionality for both
PostgreSQL and Qdrant vector databases. It supports selective cleaning and
provides detailed statistics.

Usage:
    python scripts/clean_database.py [--tenant TENANT_SLUG] [--confirm] [--vector-only] [--postgres-only]
"""

import argparse
import os
import sys
import django
import logging
from datetime import datetime
from typing import Optional, List

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk, EmbeddingMetadata
from apps.documents.models import ChunkRelationship, DocumentProcessingJob

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DatabaseCleaner:
    """Enhanced database cleaner with selective cleaning capabilities."""

    def __init__(self, tenant_slug: Optional[str] = None):
        """
        Initialize the database cleaner.

        Args:
            tenant_slug: Optional tenant slug to clean specific tenant data only
        """
        self.tenant_slug = tenant_slug
        self.tenant = None
        self.stats = {
            'embedding_metadata': 0,
            'chunk_relationships': 0,
            'document_chunks': 0,
            'raw_documents': 0,
            'processing_jobs': 0,
            'document_sources': 0,
            'vector_collections': 0,
            'start_time': None,
            'end_time': None
        }

        if tenant_slug:
            try:
                self.tenant = Tenant.objects.get(slug=tenant_slug)
                logger.info(f"Cleaning data for tenant: {self.tenant.name} ({tenant_slug})")
            except Tenant.DoesNotExist:
                logger.error(f"Tenant with slug '{tenant_slug}' not found")
                sys.exit(1)
        else:
            logger.info("Cleaning data for all tenants")

    def get_cleaning_stats(self) -> dict:
        """Get statistics about what will be cleaned."""
        stats = {}

        if self.tenant:
            # Tenant-specific counts
            stats['embedding_metadata'] = EmbeddingMetadata.objects.filter(
                chunk__document__source__tenant=self.tenant
            ).count()
            stats['chunk_relationships'] = ChunkRelationship.objects.filter(
                source_chunk__document__source__tenant=self.tenant
            ).count()
            stats['document_chunks'] = DocumentChunk.objects.filter(
                document__source__tenant=self.tenant
            ).count()
            stats['raw_documents'] = RawDocument.objects.filter(
                source__tenant=self.tenant
            ).count()
            stats['processing_jobs'] = DocumentProcessingJob.objects.filter(
                tenant=self.tenant
            ).count()
            stats['document_sources'] = DocumentSource.objects.filter(
                tenant=self.tenant
            ).count()
        else:
            # Global counts
            stats['embedding_metadata'] = EmbeddingMetadata.objects.count()
            stats['chunk_relationships'] = ChunkRelationship.objects.count()
            stats['document_chunks'] = DocumentChunk.objects.count()
            stats['raw_documents'] = RawDocument.objects.count()
            stats['processing_jobs'] = DocumentProcessingJob.objects.count()
            stats['document_sources'] = DocumentSource.objects.count()

        return stats

    def clean_postgres_data(self) -> None:
        """Clean PostgreSQL database data."""
        logger.info("Starting PostgreSQL database cleaning...")
        self.stats['start_time'] = datetime.now()

        if self.tenant:
            # Clean tenant-specific data
            self._clean_tenant_postgres_data()
        else:
            # Clean all data
            self._clean_all_postgres_data()

    def _clean_tenant_postgres_data(self) -> None:
        """Clean PostgreSQL data for a specific tenant."""
        # Delete embedding metadata for tenant
        embedding_count = EmbeddingMetadata.objects.filter(
            chunk__document__source__tenant=self.tenant
        ).count()
        EmbeddingMetadata.objects.filter(
            chunk__document__source__tenant=self.tenant
        ).delete()
        self.stats['embedding_metadata'] = embedding_count
        logger.info(f"Deleted {embedding_count} embedding metadata records for tenant {self.tenant.slug}")

        # Delete chunk relationships for tenant
        relationship_count = ChunkRelationship.objects.filter(
            source_chunk__document__source__tenant=self.tenant
        ).count()
        ChunkRelationship.objects.filter(
            source_chunk__document__source__tenant=self.tenant
        ).delete()
        self.stats['chunk_relationships'] = relationship_count
        logger.info(f"Deleted {relationship_count} chunk relationships for tenant {self.tenant.slug}")

        # Delete document chunks for tenant
        chunk_count = DocumentChunk.objects.filter(
            document__source__tenant=self.tenant
        ).count()
        DocumentChunk.objects.filter(
            document__source__tenant=self.tenant
        ).delete()
        self.stats['document_chunks'] = chunk_count
        logger.info(f"Deleted {chunk_count} document chunks for tenant {self.tenant.slug}")

        # Delete raw documents for tenant
        doc_count = RawDocument.objects.filter(source__tenant=self.tenant).count()
        RawDocument.objects.filter(source__tenant=self.tenant).delete()
        self.stats['raw_documents'] = doc_count
        logger.info(f"Deleted {doc_count} raw documents for tenant {self.tenant.slug}")

        # Delete processing jobs for tenant
        job_count = DocumentProcessingJob.objects.filter(tenant=self.tenant).count()
        DocumentProcessingJob.objects.filter(tenant=self.tenant).delete()
        self.stats['processing_jobs'] = job_count
        logger.info(f"Deleted {job_count} processing jobs for tenant {self.tenant.slug}")

        # Delete document sources for tenant
        source_count = DocumentSource.objects.filter(tenant=self.tenant).count()
        DocumentSource.objects.filter(tenant=self.tenant).delete()
        self.stats['document_sources'] = source_count
        logger.info(f"Deleted {source_count} document sources for tenant {self.tenant.slug}")

    def _clean_all_postgres_data(self) -> None:
        """Clean all PostgreSQL data."""
        # Delete all embedding metadata
        embedding_count = EmbeddingMetadata.objects.count()
        EmbeddingMetadata.objects.all().delete()
        self.stats['embedding_metadata'] = embedding_count
        logger.info(f"Deleted {embedding_count} embedding metadata records")

        # Delete all chunk relationships
        relationship_count = ChunkRelationship.objects.count()
        ChunkRelationship.objects.all().delete()
        self.stats['chunk_relationships'] = relationship_count
        logger.info(f"Deleted {relationship_count} chunk relationships")

        # Delete all document chunks
        chunk_count = DocumentChunk.objects.count()
        DocumentChunk.objects.all().delete()
        self.stats['document_chunks'] = chunk_count
        logger.info(f"Deleted {chunk_count} document chunks")

        # Delete all raw documents
        doc_count = RawDocument.objects.count()
        RawDocument.objects.all().delete()
        self.stats['raw_documents'] = doc_count
        logger.info(f"Deleted {doc_count} raw documents")

        # Delete all document processing jobs
        job_count = DocumentProcessingJob.objects.count()
        DocumentProcessingJob.objects.all().delete()
        self.stats['processing_jobs'] = job_count
        logger.info(f"Deleted {job_count} document processing jobs")

        # Delete all document sources
        source_count = DocumentSource.objects.count()
        DocumentSource.objects.all().delete()
        self.stats['document_sources'] = source_count
        logger.info(f"Deleted {source_count} document sources")

    def clean_vector_data(self) -> None:
        """Clean Qdrant vector database data."""
        logger.info("Starting vector database cleaning...")

        try:
            from apps.core.utils.llama_index_vectorstore import get_vector_store
            from apps.core.utils.collection_manager import get_collection_name

            collections_deleted = 0

            if self.tenant:
                # Clean tenant-specific collections
                collections_deleted = self._clean_tenant_vector_data()
            else:
                # Clean all tenant collections
                tenants = Tenant.objects.all()
                for tenant in tenants:
                    collections_deleted += self._clean_tenant_vector_data(tenant)

            self.stats['vector_collections'] = collections_deleted
            logger.info(f"Cleaned {collections_deleted} vector collections")

        except Exception as e:
            logger.error(f"Error cleaning vector database: {str(e)}")
            logger.warning("Vector database cleaning failed, but PostgreSQL cleaning was successful")

    def _clean_tenant_vector_data(self, tenant: Optional[Tenant] = None) -> int:
        """Clean vector data for a specific tenant."""
        if tenant is None:
            tenant = self.tenant

        collections_deleted = 0

        try:
            from apps.core.utils.llama_index_vectorstore import get_vector_store
            from apps.core.utils.collection_manager import get_collection_name

            # Get all possible collection names for this tenant
            collection_types = ["document", "conversation", "code", "general"]

            for collection_type in collection_types:
                try:
                    collection_name = get_collection_name(tenant.slug, intent=collection_type)
                    vector_store = get_vector_store(collection_name=collection_name)

                    # Check if collection exists and delete it
                    if hasattr(vector_store, '_client'):
                        client = vector_store._client
                        if client.collection_exists(collection_name):
                            client.delete_collection(collection_name)
                            collections_deleted += 1
                            logger.info(f"Deleted vector collection: {collection_name}")

                except Exception as e:
                    logger.warning(f"Could not delete collection {collection_name}: {str(e)}")

        except Exception as e:
            logger.error(f"Error cleaning vector data for tenant {tenant.slug}: {str(e)}")

        return collections_deleted

    def clean_all(self, postgres_only: bool = False, vector_only: bool = False) -> None:
        """Clean both PostgreSQL and vector databases."""
        if vector_only:
            self.clean_vector_data()
        elif postgres_only:
            self.clean_postgres_data()
        else:
            self.clean_postgres_data()
            self.clean_vector_data()

        self.stats['end_time'] = datetime.now()

    def print_summary(self) -> None:
        """Print cleaning summary."""
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            logger.info(f"Cleaning completed in {duration.total_seconds():.2f} seconds")

        logger.info("=== Cleaning Summary ===")
        logger.info(f"Embedding Metadata: {self.stats['embedding_metadata']}")
        logger.info(f"Chunk Relationships: {self.stats['chunk_relationships']}")
        logger.info(f"Document Chunks: {self.stats['document_chunks']}")
        logger.info(f"Raw Documents: {self.stats['raw_documents']}")
        logger.info(f"Processing Jobs: {self.stats['processing_jobs']}")
        logger.info(f"Document Sources: {self.stats['document_sources']}")
        logger.info(f"Vector Collections: {self.stats['vector_collections']}")
        logger.info("========================")


def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(description="Clean RAGSearch databases")
    parser.add_argument("--tenant", help="Tenant slug to clean (optional)")
    parser.add_argument("--confirm", action="store_true", help="Skip confirmation prompt")
    parser.add_argument("--vector-only", action="store_true", help="Clean only vector database")
    parser.add_argument("--postgres-only", action="store_true", help="Clean only PostgreSQL database")
    parser.add_argument("--stats-only", action="store_true", help="Show statistics without cleaning")

    args = parser.parse_args()

    # Create cleaner instance
    cleaner = DatabaseCleaner(tenant_slug=args.tenant)

    # Show statistics
    if args.stats_only:
        stats = cleaner.get_cleaning_stats()
        logger.info("=== Current Database Statistics ===")
        for key, value in stats.items():
            logger.info(f"{key.replace('_', ' ').title()}: {value}")
        logger.info("===================================")
        return

    # Get cleaning stats for confirmation
    stats = cleaner.get_cleaning_stats()
    total_items = sum(stats.values())

    if total_items == 0:
        logger.info("Database is already clean. Nothing to delete.")
        return

    # Confirmation prompt
    if not args.confirm:
        logger.info("=== Items to be deleted ===")
        for key, value in stats.items():
            if value > 0:
                logger.info(f"{key.replace('_', ' ').title()}: {value}")
        logger.info("===========================")

        response = input(f"Are you sure you want to delete {total_items} items? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            logger.info("Cleaning cancelled.")
            return

    # Perform cleaning
    try:
        cleaner.clean_all(
            postgres_only=args.postgres_only,
            vector_only=args.vector_only
        )
        cleaner.print_summary()
        logger.info("Database cleaning completed successfully!")

    except Exception as e:
        logger.error(f"Error during cleaning: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
