{"date": "2024-02-02", "channel_id": "C065QSSNH8A", "message_count": 4, "messages": [{"ts": "1706894408.918929", "text": "Hey <@U04DKEFP1K8>, one of today's Loom updates reminded me that we need to improve our hygiene on customer data.\n\nSome options:\n• Ensure external contractors never have actual customer data in their dev environments\n• Ensure screenshots &amp; videos always use stricter sharing permissions (vs \"anyone with link\") \nWhat do you think is the best way to tighten that up?", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706894408.918929", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "DNTod", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hey "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": ", one of today's Loom updates reminded me that we need to improve our hygiene on customer data.\n\nSome options:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ensure external contractors never have actual customer data in their dev environments"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Ensure screenshots & videos always use stricter sharing permissions (vs \"anyone with link\") "}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nWhat do you think is the best way to tighten that up?"}]}]}]}, {"ts": "1706836884.231359", "text": "Mainly just FYI: Here's the \"<https://docs.google.com/presentation/d/1ZR5fmOfrSw35ALW68KDMceXTG3_FjGKOkuEqSTUYoJs/edit?usp=sharing|Quick Guide>\" I put together for <PERSON><PERSON> to use for Stellar managers. I'll see if she feels this is enough or has feedback. :slightly_smiling_face:", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706836884.231359", "reply_count": 9, "reactions": [{"name": "raised_hands", "users": ["U04DS2MBWP4", "U04DKEFP1K8"], "count": 2}], "files": [{"id": "F06HHAJA3EU", "created": 1706836885, "timestamp": 1706836885, "name": "SDF: Quick Guide to Compiify Planning", "title": "SDF: Quick Guide to Compiify Planning", "mimetype": "application/vnd.google-apps.presentation", "filetype": "gpres", "pretty_type": "Google Slides", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 14280, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1ZR5fmOfrSw35ALW68KDMceXTG3_FjGKOkuEqSTUYoJs", "external_url": "https://docs.google.com/presentation/d/1ZR5fmOfrSw35ALW68KDMceXTG3_FjGKOkuEqSTUYoJs/edit?usp=sharing", "url_private": "https://docs.google.com/presentation/d/1ZR5fmOfrSw35ALW68KDMceXTG3_FjGKOkuEqSTUYoJs/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_360.png", "thumb_360_w": 360, "thumb_360_h": 203, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_480.png", "thumb_480_w": 480, "thumb_480_h": 270, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_720.png", "thumb_720_w": 720, "thumb_720_h": 405, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06HHAJA3EU-e23e8ae14e/sdf__quick_guide_to_compiify_planning_800.png", "thumb_800_w": 800, "thumb_800_h": 450, "original_w": 800, "original_h": 450, "thumb_tiny": "AwAbADDToophL54AI96AH0VHmTI+VcfWjMg7KfxoAkopgL7hlRj1Bp9ABRRRQA3Yv90UbV/uinUUAFFFFAH/2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06HHAJA3EU/sdf__quick_guide_to_compiify_planning", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "yYFre", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Mainly just FYI: Here's the \""}, {"type": "link", "url": "https://docs.google.com/presentation/d/1ZR5fmOfrSw35ALW68KDMceXTG3_FjGKOkuEqSTUYoJs/edit?usp=sharing", "text": "Quick Guide"}, {"type": "text", "text": "\" I put together for <PERSON><PERSON> to use for Stellar managers. I'll see if she feels this is enough or has feedback. "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1706836332.808979", "text": "Priorities for Eng for the next day:\n• :repeat: Password reset flow (<https://compiify.atlassian.net/browse/COM-2240|COM-2240>) \n• :repeat: Bulk Actions for adjustment letters (<https://compiify.atlassian.net/browse/COM-2252|COM-2252>) _(BE completed, need FE next)_\n• Incorrect salary increase in adjustment letter (<https://compiify.atlassian.net/browse/COM-2267|COM-2267>)\n• Finish fixes for downloadable reports in Wave 3 (<https://compiify.atlassian.net/browse/COM-2122|COM-2122>, <https://compiify.atlassian.net/browse/COM-2129|COM-2129>, <https://compiify.atlassian.net/browse/COM-2124|COM-2124>, <https://compiify.atlassian.net/browse/COM-2123|COM-2123>, <https://compiify.atlassian.net/browse/COM-2126|COM-2126>, <https://compiify.atlassian.net/browse/COM-2120|COM-2120>)\n• Update Total Rewards values (<https://compiify.atlassian.net/browse/COM-2268|COM-2268>)\n• :repeat: Errors while approving/overriding on QA env (<https://compiify.atlassian.net/browse/COM-2261|COM-2261>)\n• :repeat: Job level filter for Adjustment letters (<https://compiify.atlassian.net/browse/COM-2250|COM-2250>)\nThen, continue with <https://compiify.atlassian.net/browse/COM-2087|Wave 3> from the top to bottom.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706836332.808979", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Mlyc6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Priorities for Eng for the next day:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Password reset flow ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2240", "text": "COM-2240"}, {"type": "text", "text": ") "}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Bulk Actions for adjustment letters ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2252", "text": "COM-2252"}, {"type": "text", "text": ") "}, {"type": "text", "text": "(BE completed, need FE next)", "style": {"italic": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Incorrect salary increase in adjustment letter ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2267", "text": "COM-2267"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Finish fixes for downloadable reports in Wave 3 ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2122", "text": "COM-2122"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2129", "text": "COM-2129"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2124", "text": "COM-2124"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2123", "text": "COM-2123"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2126", "text": "COM-2126"}, {"type": "text", "text": ", "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2120", "text": "COM-2120"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Update Total Rewards values ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2268", "text": "COM-2268"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Errors while approving/overriding on QA env ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2261", "text": "COM-2261"}, {"type": "text", "text": ")"}]}, {"type": "rich_text_section", "elements": [{"type": "emoji", "name": "repeat", "unicode": "1f501"}, {"type": "text", "text": " Job level filter for Adjustment letters ("}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2250", "text": "COM-2250"}, {"type": "text", "text": ")"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nThen, continue with "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-2087", "text": "Wave 3"}, {"type": "text", "text": " from the top to bottom."}]}]}]}, {"ts": "1706820381.355689", "text": "<@U04DKEFP1K8> The QA environment should be ready for any testing <@U0658EW4B8D> was supposed to do on adjustment letters today, right? (Did we cover those additional 2 cases you previously said to exclude?)", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1706820381.355689", "reply_count": 16, "blocks": [{"type": "rich_text", "block_id": "LDME5", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " The QA environment should be ready for any testing "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " was supposed to do on adjustment letters today, right? (Did we cover those additional 2 cases you previously said to exclude?)"}]}]}]}], "created_at": "2025-05-22T21:35:34.585135"}