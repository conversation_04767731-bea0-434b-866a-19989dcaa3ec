{"date": "2023-11-18", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1700254267.408169", "text": "Competitive set that Merge is already supporting via HRIS/payroll integrations:\n• <https://www.assemble.inc/|Assemble>\n• <https://pequity.com/|Pequity>\n• <https://www.payscale.com/|Payscale>\n• <https://ravio.com/|Ravio>", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1700254267.408169", "reply_count": 4, "blocks": [{"type": "rich_text", "block_id": "QpZI0", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Competitive set that Merge is already supporting via HRIS/payroll integrations:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://www.assemble.inc/", "text": "Assemble"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://pequity.com/", "text": "Pequity"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://www.payscale.com/", "text": "Payscale"}]}, {"type": "rich_text_section", "elements": [{"type": "link", "url": "https://ravio.com/", "text": "<PERSON><PERSON>"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.577186"}