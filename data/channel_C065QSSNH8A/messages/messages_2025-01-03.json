{"date": "2025-01-03", "channel_id": "C065QSSNH8A", "message_count": 13, "messages": [{"ts": "1735926927.655129", "text": "<@U0690EB5JE5> Prioritized request for cycle builder; recommendations page. These are very important for larger organizations (and have already been challenging for both Diversified and Curana). <https://compiify.atlassian.net/browse/COM-4045>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1735926927.655129", "reply_count": 1, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14128::de943aebd0604c7592260b80ec8404d8", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4045?atlOrigin=eyJpIjoiNmQ2Mzc1ZmZmMDEzNDVjOTg2ZGY3ODJhZDM4ZDIzYjQiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4045 Enhancement Request for CycleBuilder: Recommender Summary and Hiera…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14128::c29c9d982221436a89db5a63310cdd36", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14128\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4045\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4045", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "yct7n", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Prioritized request for cycle builder; recommendations page. These are very important for larger organizations (and have already been challenging for both Diversified and Curana). "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4045"}]}]}]}, {"ts": "1735926554.182029", "text": "<@U0690EB5JE5> Question: Diversified will be relying on email notifications when they begin their cycle; how do we enable those? Do we have a list of when an email goes out? For example, they are asking for notifications when something has been submitted, so the manager knows they have an action to take. They've also requested the admin have some summary notifications, though I understand that may not be feasible this cycle. For example; a weekly summary of what's on the My Tasks page, emailed to them.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1735926554.182029", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "vc+1G", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Question: Diversified will be relying on email notifications when they begin their cycle; how do we enable those? Do we have a list of when an email goes out? For example, they are asking for notifications when something has been submitted, so the manager knows they have an action to take. They've also requested the admin have some summary notifications, though I understand that may not be feasible this cycle. For example; a weekly summary of what's on the My Tasks page, emailed to them."}]}]}]}, {"ts": "1735926428.747269", "text": "Data refresh: Diversified: <https://compiify.atlassian.net/browse/COM-4044>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14127::a6621af93480485d8a6c5c98eac11c1e", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4044?atlOrigin=eyJpIjoiNWI5YjRmM2NjMzNkNDgwMDg3OWVjMzg0NmE4ZDMyOGEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4044 Refresh HRIS data>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14127::7f892f32191e4a198baf1dde500decf4", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14127\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4044\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4044", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "M3H+A", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Data refresh: Diversified: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4044"}]}]}]}, {"ts": "1735926308.135059", "text": "<@U0690EB5JE5> bug with impersonation in Diversified: <https://compiify.atlassian.net/browse/COM-4043>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1735926308.135059", "reply_count": 2, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14126::51faad30d020406d89a8b30c7de84476", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4043?atlOrigin=eyJpIjoiN2U0NTZhNTIwZjBjNDE2MDg3ZWE5YjhkMDY1MGMzMTEiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4043 Bug: Impersonation Issues for Cycle Admins in Diversified's Environ…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14126::908535a2e40945728269e66a5d28a0bf", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14126\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4043\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4043", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "yCUcK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " bug with impersonation in Diversified: "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4043"}]}]}]}, {"ts": "1735926294.061939", "text": "Tithely - mid March\nCurana - early March\nDiversified - Feb 1\nValgenesis - Feb 3", "user": "U07EJ2LP44S", "type": "message", "reactions": [{"name": "thankyouty", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "lIi2Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Tithely - mid March\nCurana - early March\nDiversified - Feb 1\nValgenesis - Feb 3"}]}]}]}, {"ts": "1735920227.653749", "text": "we can meet here <https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "6qP+e", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we can meet here "}, {"type": "link", "url": "https://us06web.zoom.us/j/9254807019?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1735919882.590659", "text": "Let me know when you start the meeting <@U07EJ2LP44S>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "5DlJD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Let me know when you start the meeting "}, {"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1735919862.848069", "text": "I am available now actually.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "A4oCV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am available now actually."}]}]}]}, {"ts": "1735918387.143709", "text": "I’m running behind too, start at 10 after?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "VcVwZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I’m"}, {"type": "text", "text": " running behind too, start at 10 after?"}]}]}]}, {"ts": "1735917644.931479", "text": "Running late to meeting by 10mnts", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "8pL0L", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Running late to meeting by 10mnts"}]}]}]}, {"ts": "1735906712.682999", "text": "cc: <@U06HN8XDC5A>", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "oFPT4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "cc: "}, {"type": "user", "user_id": "U06HN8XDC5A"}]}]}]}, {"ts": "1735906701.589779", "text": "<@U07M6QKHUC9> <@U07EJ2LP44S> Heads up. We will be demoing UX improvements on Monday first 10 mnts of stand up.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BZDDk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Heads up. We will be demoing UX improvements on Monday first 10 mnts of stand up."}]}]}]}, {"ts": "1735890133.730899", "text": "<@U0690EB5JE5> I need access to <https://www.figma.com/design/WOJVdr3bbnBloZT9z6pY9K/Design-file-(Copy)?node-id=4-7&amp;node-type=canvas&amp;t=MFMI7mtChcIjWRX5-0|this figma> design for total rewards. Requested for it.", "user": "U07MH77PUBV", "type": "message", "thread_ts": "1735890133.730899", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "ybFKK", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I need access to "}, {"type": "link", "url": "https://www.figma.com/design/WOJVdr3bbnBloZT9z6pY9K/Design-file-(Copy)?node-id=4-7&node-type=canvas&t=MFMI7mtChcIjWRX5-0", "text": "this figma"}, {"type": "text", "text": " design for total rewards. Requested for it."}]}]}]}], "created_at": "2025-05-22T21:35:34.696750"}