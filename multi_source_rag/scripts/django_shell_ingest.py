"""
Enhanced Django Shell Script for RAGSearch Data Management

This script provides easy-to-use functions for cleaning databases and ingesting data
directly from the Django shell. It uses the new LlamaIndex-based services.

To run this script:
1. Open a terminal in your project directory
2. Run the Django shell:
   python manage.py shell
3. In the shell, run:
   exec(open('scripts/django_shell_ingest.py').read())

Available functions after running:
- clean_database(tenant_slug="default")
- ingest_slack_data(data_dir="../data", tenant_slug="default")
- ingest_file_data(data_dir="../data", tenant_slug="default")
- clean_and_ingest(data_dir="../data", tenant_slug="default")
- show_stats(tenant_slug="default")
"""

import json
import logging
from datetime import datetime
from django.contrib.auth.models import User

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import Django models and services
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk, EmbeddingMetadata
from apps.documents.models import ChunkRelationship, DocumentProcessingJob
from apps.documents.services.llama_ingestion_service_unified import UnifiedLlamaIndexIngestionService

print("RAGSearch Django Shell Helper Loaded!")
print("Available functions:")
print("- clean_database(tenant_slug='default')")
print("- ingest_slack_data(data_dir='../data', tenant_slug='default')")
print("- ingest_file_data(data_dir='../data', tenant_slug='default')")
print("- clean_and_ingest(data_dir='../data', tenant_slug='default')")
print("- show_stats(tenant_slug='default')")
print()


def get_or_create_tenant(tenant_slug="default"):
    """Get or create a tenant."""
    tenant, created = Tenant.objects.get_or_create(
        slug=tenant_slug,
        defaults={"name": f"Tenant {tenant_slug.title()}"}
    )
    if created:
        print(f"Created new tenant: {tenant.name}")
    else:
        print(f"Using existing tenant: {tenant.name}")
    return tenant


def get_or_create_user(email="<EMAIL>"):
    """Get or create a user."""
    try:
        user = User.objects.get(email=email)
        print(f"Using existing user: {user.email}")
    except User.DoesNotExist:
        user = User.objects.create_user(
            username=email.split('@')[0],
            email=email,
            password='defaultpassword123'
        )
        print(f"Created new user: {user.email}")
    return user


def clean_database(tenant_slug="default"):
    """
    Clean the database for a specific tenant or all tenants.

    Args:
        tenant_slug: Tenant slug to clean, or "all" for all tenants
    """
    print(f"Cleaning database for tenant: {tenant_slug}")

    if tenant_slug == "all":
        # Clean all data
        embedding_count = EmbeddingMetadata.objects.count()
        EmbeddingMetadata.objects.all().delete()
        print(f"Deleted {embedding_count} embedding metadata records")

        relationship_count = ChunkRelationship.objects.count()
        ChunkRelationship.objects.all().delete()
        print(f"Deleted {relationship_count} chunk relationships")

        chunk_count = DocumentChunk.objects.count()
        DocumentChunk.objects.all().delete()
        print(f"Deleted {chunk_count} document chunks")

        doc_count = RawDocument.objects.count()
        RawDocument.objects.all().delete()
        print(f"Deleted {doc_count} raw documents")

        job_count = DocumentProcessingJob.objects.count()
        DocumentProcessingJob.objects.all().delete()
        print(f"Deleted {job_count} processing jobs")

        source_count = DocumentSource.objects.count()
        DocumentSource.objects.all().delete()
        print(f"Deleted {source_count} document sources")
    else:
        # Clean tenant-specific data
        tenant = get_or_create_tenant(tenant_slug)

        embedding_count = EmbeddingMetadata.objects.filter(
            chunk__document__source__tenant=tenant
        ).count()
        EmbeddingMetadata.objects.filter(
            chunk__document__source__tenant=tenant
        ).delete()
        print(f"Deleted {embedding_count} embedding metadata records for {tenant_slug}")

        relationship_count = ChunkRelationship.objects.filter(
            source_chunk__document__source__tenant=tenant
        ).count()
        ChunkRelationship.objects.filter(
            source_chunk__document__source__tenant=tenant
        ).delete()
        print(f"Deleted {relationship_count} chunk relationships for {tenant_slug}")

        chunk_count = DocumentChunk.objects.filter(
            document__source__tenant=tenant
        ).count()
        DocumentChunk.objects.filter(
            document__source__tenant=tenant
        ).delete()
        print(f"Deleted {chunk_count} document chunks for {tenant_slug}")

        doc_count = RawDocument.objects.filter(source__tenant=tenant).count()
        RawDocument.objects.filter(source__tenant=tenant).delete()
        print(f"Deleted {doc_count} raw documents for {tenant_slug}")

        job_count = DocumentProcessingJob.objects.filter(tenant=tenant).count()
        DocumentProcessingJob.objects.filter(tenant=tenant).delete()
        print(f"Deleted {job_count} processing jobs for {tenant_slug}")

        source_count = DocumentSource.objects.filter(tenant=tenant).count()
        DocumentSource.objects.filter(tenant=tenant).delete()
        print(f"Deleted {source_count} document sources for {tenant_slug}")

    # Clean vector database
    try:
        from apps.core.utils.llama_index_vectorstore import get_vector_store
        from apps.core.utils.collection_manager import get_collection_name

        if tenant_slug == "all":
            tenants = Tenant.objects.all()
        else:
            tenants = [get_or_create_tenant(tenant_slug)]

        collections_deleted = 0
        for tenant in tenants:
            collection_types = ["document", "conversation", "code", "general"]
            for collection_type in collection_types:
                try:
                    collection_name = get_collection_name(tenant.slug, intent=collection_type)
                    vector_store = get_vector_store(collection_name=collection_name)

                    if hasattr(vector_store, '_client'):
                        client = vector_store._client
                        if client.collection_exists(collection_name):
                            client.delete_collection(collection_name)
                            collections_deleted += 1
                            print(f"Deleted vector collection: {collection_name}")
                except Exception as e:
                    print(f"Could not delete collection {collection_name}: {str(e)}")

        print(f"Deleted {collections_deleted} vector collections")

    except Exception as e:
        print(f"Error cleaning vector database: {str(e)}")

    print("Database cleaning completed!")


def ingest_slack_data(data_dir="../data", tenant_slug="default", batch_size=50, limit=None):
    """
    Ingest Slack data from a directory.

    Args:
        data_dir: Path to the data directory
        tenant_slug: Tenant slug for ingestion
        batch_size: Batch size for processing
        limit: Optional limit on documents to process
    """
    print(f"Ingesting Slack data from {data_dir} for tenant {tenant_slug}")

    # Get tenant and user
    tenant = get_or_create_tenant(tenant_slug)
    user = get_or_create_user()

    # Create document source
    config = {
        "data_dir": data_dir,
        "time_period": "custom",
        "custom_days": 730,  # Two years
        "enable_semantic_cross_refs": True,
        "quality_threshold": 0.3,
        "chunking_strategy": "monthly",
        "include_threads": True,
        "filter_bots": True,
        "min_message_length": 10,
        "max_chunk_size": 8000,
        "overlap_size": 200
    }

    source = DocumentSource.objects.create(
        tenant=tenant,
        name=f"Local Slack Data - {data_dir}",
        source_type="local_slack",
        config=config,
        is_active=True
    )
    print(f"Created document source: {source.name}")

    # Initialize ingestion service
    ingestion_service = UnifiedLlamaIndexIngestionService(tenant=tenant, user=user)

    # Process the source
    print("Starting ingestion process...")
    start_time = datetime.now()

    processed, failed = ingestion_service.process_source(
        source=source,
        batch_size=batch_size,
        limit=limit
    )

    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()

    print(f"Ingestion completed in {duration:.2f} seconds")
    print(f"Result: {processed} documents processed, {failed} documents failed")

    return processed, failed


def ingest_file_data(data_dir="../data", tenant_slug="default", batch_size=50, limit=None):
    """
    Ingest file data from a directory.

    Args:
        data_dir: Path to the data directory
        tenant_slug: Tenant slug for ingestion
        batch_size: Batch size for processing
        limit: Optional limit on documents to process
    """
    print(f"Ingesting file data from {data_dir} for tenant {tenant_slug}")

    # Get tenant and user
    tenant = get_or_create_tenant(tenant_slug)
    user = get_or_create_user()

    # Create document source
    config = {
        "data_dir": data_dir,
        "file_extensions": [".txt", ".md", ".pdf", ".docx"],
        "recursive": True,
        "max_file_size": 50 * 1024 * 1024,  # 50MB
        "encoding": "utf-8"
    }

    source = DocumentSource.objects.create(
        tenant=tenant,
        name=f"File Data - {data_dir}",
        source_type="file",
        config=config,
        is_active=True
    )
    print(f"Created document source: {source.name}")

    # Initialize ingestion service
    ingestion_service = UnifiedLlamaIndexIngestionService(tenant=tenant, user=user)

    # Process the source
    print("Starting ingestion process...")
    start_time = datetime.now()

    processed, failed = ingestion_service.process_source(
        source=source,
        batch_size=batch_size,
        limit=limit
    )

    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()

    print(f"Ingestion completed in {duration:.2f} seconds")
    print(f"Result: {processed} documents processed, {failed} documents failed")

    return processed, failed


def clean_and_ingest(data_dir="../data", tenant_slug="default", batch_size=50, limit=None, source_type="auto"):
    """
    Clean database and ingest fresh data in one operation.

    Args:
        data_dir: Path to the data directory
        tenant_slug: Tenant slug for operations
        batch_size: Batch size for processing
        limit: Optional limit on documents to process
        source_type: Type of source (auto, slack, file)
    """
    print(f"Starting clean and ingest operation for tenant {tenant_slug}")

    # Clean database first
    clean_database(tenant_slug)

    # Auto-detect source type if needed
    if source_type == "auto":
        import os
        from pathlib import Path
        data_path = Path(data_dir)
        if data_path.exists() and any(f.name.startswith("channel_") for f in data_path.iterdir() if f.is_dir()):
            source_type = "slack"
            print("Auto-detected source type: slack")
        else:
            source_type = "file"
            print("Auto-detected source type: file")

    # Ingest data
    if source_type == "slack":
        return ingest_slack_data(data_dir, tenant_slug, batch_size, limit)
    elif source_type == "file":
        return ingest_file_data(data_dir, tenant_slug, batch_size, limit)
    else:
        print(f"Unsupported source type: {source_type}")
        return 0, 0


def show_stats(tenant_slug="default"):
    """
    Show current database statistics for a tenant.

    Args:
        tenant_slug: Tenant slug to show stats for, or "all" for all tenants
    """
    print(f"Database Statistics for tenant: {tenant_slug}")
    print("=" * 50)

    if tenant_slug == "all":
        # Show global stats
        print(f"Total Tenants: {Tenant.objects.count()}")
        print(f"Total Document Sources: {DocumentSource.objects.count()}")
        print(f"Total Raw Documents: {RawDocument.objects.count()}")
        print(f"Total Document Chunks: {DocumentChunk.objects.count()}")
        print(f"Total Embedding Metadata: {EmbeddingMetadata.objects.count()}")
        print(f"Total Chunk Relationships: {ChunkRelationship.objects.count()}")
        print(f"Total Processing Jobs: {DocumentProcessingJob.objects.count()}")

        # Show per-tenant breakdown
        print("\nPer-Tenant Breakdown:")
        for tenant in Tenant.objects.all():
            sources = DocumentSource.objects.filter(tenant=tenant).count()
            docs = RawDocument.objects.filter(source__tenant=tenant).count()
            chunks = DocumentChunk.objects.filter(document__source__tenant=tenant).count()
            print(f"  {tenant.slug}: {sources} sources, {docs} documents, {chunks} chunks")
    else:
        # Show tenant-specific stats
        try:
            tenant = Tenant.objects.get(slug=tenant_slug)
        except Tenant.DoesNotExist:
            print(f"Tenant '{tenant_slug}' not found")
            return

        sources = DocumentSource.objects.filter(tenant=tenant)
        docs = RawDocument.objects.filter(source__tenant=tenant)
        chunks = DocumentChunk.objects.filter(document__source__tenant=tenant)
        embeddings = EmbeddingMetadata.objects.filter(chunk__document__source__tenant=tenant)
        relationships = ChunkRelationship.objects.filter(source_chunk__document__source__tenant=tenant)
        jobs = DocumentProcessingJob.objects.filter(tenant=tenant)

        print(f"Tenant: {tenant.name} ({tenant.slug})")
        print(f"Document Sources: {sources.count()}")
        print(f"Raw Documents: {docs.count()}")
        print(f"Document Chunks: {chunks.count()}")
        print(f"Embedding Metadata: {embeddings.count()}")
        print(f"Chunk Relationships: {relationships.count()}")
        print(f"Processing Jobs: {jobs.count()}")

        # Show source breakdown
        if sources.exists():
            print("\nSource Breakdown:")
            for source in sources:
                source_docs = docs.filter(source=source).count()
                source_chunks = chunks.filter(document__source=source).count()
                print(f"  {source.name} ({source.source_type}): {source_docs} docs, {source_chunks} chunks")

        # Show recent processing jobs
        recent_jobs = jobs.order_by('-created_at')[:5]
        if recent_jobs:
            print("\nRecent Processing Jobs:")
            for job in recent_jobs:
                print(f"  {job.created_at.strftime('%Y-%m-%d %H:%M')} - {job.source.name} - {job.status}")

    print("=" * 50)


# Example usage message
print("\nExample usage:")
print("# Clean database and ingest Slack data:")
print("clean_and_ingest('../data')")
print()
print("# Just clean database:")
print("clean_database()")
print()
print("# Just ingest Slack data:")
print("ingest_slack_data('../data')")
print()
print("# Show current statistics:")
print("show_stats()")
print()
print("# Clean and ingest with custom settings:")
print("clean_and_ingest('../data', tenant_slug='my-company', batch_size=25, limit=100)")
print()
