{"thread_ts": "1740160092.101529", "channel_id": "C065QSSNH8A", "reply_count": 15, "replies": [{"ts": "1740160128.042839", "text": "", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740160092.101529", "files": [{"id": "F08EUMRT2CR", "created": 1740160123, "timestamp": 1740160123, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 12573, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08EUMRT2CR/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08EUMRT2CR/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EUMRT2CR-18d7818e01/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EUMRT2CR-18d7818e01/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EUMRT2CR-18d7818e01/image_360.png", "thumb_360_w": 360, "thumb_360_h": 30, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EUMRT2CR-18d7818e01/image_480.png", "thumb_480_w": 480, "thumb_480_h": 40, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EUMRT2CR-18d7818e01/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EUMRT2CR-18d7818e01/image_720.png", "thumb_720_w": 720, "thumb_720_h": 60, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EUMRT2CR-18d7818e01/image_800.png", "thumb_800_w": 800, "thumb_800_h": 66, "original_w": 916, "original_h": 76, "thumb_tiny": "AwADADDTIzSbR7/nS0UAJge/50bR7/nS0UAJtHv+dKBiiigD/9k=", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08EUMRT2CR/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08EUMRT2CR-2d2e77f25f", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}]}, {"ts": "1740160181.608369", "text": "OK will take a look, The missing would be the one's who have not got any adjustments. i.e. no change in comp. Will check that example.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "e0GCN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "OK will take a look, The missing would be the one's who have not got any adjustments. i.e. no change in comp. Will check that example."}]}]}]}, {"ts": "1740161959.192029", "text": "<PERSON> Blackwell is not eligible for the cycle. Do they need letter for all employees which I can generate if they want.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "s54DS", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON> Blackwell is not eligible for the cycle. Do they need letter for all employees which I can generate if they want."}]}]}]}, {"ts": "1740161989.902079", "text": "<@U07EJ2LP44S>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "oI5mB", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}]}]}]}, {"ts": "1740416654.067459", "text": "He is not eligible but he DOES have a team in there, so he should have a folder", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "JKlE6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "He is not eligible but he DOES have a team in there, so he should have a folder"}]}]}]}, {"ts": "1740416671.301969", "text": "As a manager, not a letter for himself", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "sS0kJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "As a manager, not a letter for himself"}]}]}]}, {"ts": "1740492263.884269", "text": "<@U0690EB5JE5> did you see this response?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "ge9hu", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " did you see this response?"}]}]}]}, {"ts": "1740492317.926839", "text": "Yes <@U07EJ2LP44S> will take care all the feedback once cycle is done.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "olaq/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes "}, {"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " will take care all the feedback once cycle is done."}]}]}]}, {"ts": "1740492345.563639", "text": "Are they done with cycle?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "1n22p", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Are they done with cycle?"}]}]}]}, {"ts": "1740493827.617199", "text": "I don't think so, i'll ask", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "bjzs/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I don't think so, i'll ask"}]}]}]}, {"ts": "1740493873.167939", "text": "Ok, please do check for any other feedback on Adjustment letters. ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "mPaQT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok, please do check for any other feedback on Adjustment letters. "}]}]}]}, {"ts": "1740494051.476489", "text": "Did you get the other ones - the hyphens etc? The portugal feedback? There were several in that screenshot", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "bWsrt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Did you get the other ones - the hyphens etc? The portugal feedback? There were several in that screenshot"}]}]}]}, {"ts": "1740494116.350039", "text": "Yes, those will take care, do you want me to share  once more the adjustment letters with feedback addressed?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "x+VX+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, those will take care, do you want me to share  once more the adjustment letters with feedback addressed?"}]}]}]}, {"ts": "1740496540.254079", "text": "She has just sent a 'dont be mad at me, India wants % change added' message - see attached letter", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740160092.101529", "files": [{"id": "F08ERQ3C51C", "created": 1740496536, "timestamp": 1740496536, "name": "India - Salary Increment Confirmation Letter Template (FY2025).docx", "title": "India - Salary Increment Confirmation Letter Template (FY2025).docx", "mimetype": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filetype": "docx", "pretty_type": "Word Document", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 62155, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08ERQ3C51C/india_-_salary_increment_confirmation_letter_template__fy2025_.docx", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08ERQ3C51C/download/india_-_salary_increment_confirmation_letter_template__fy2025_.docx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ERQ3C51C-485543cc8a/india_-_salary_increment_confirmation_letter_template__fy2025__converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F08ERQ3C51C-485543cc8a/india_-_salary_increment_confirmation_letter_template__fy2025__thumb_pdf.png", "thumb_pdf_w": 935, "thumb_pdf_h": 1210, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08ERQ3C51C/india_-_salary_increment_confirmation_letter_template__fy2025_.docx", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08ERQ3C51C-7c408b52e3", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "ZFbQN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "She has just sent a 'dont be mad at me, India wants % change added' message - see attached letter"}]}]}]}, {"ts": "1740496604.421649", "text": "It was the initial template :smile: . Sure will take care should be easy.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740160092.101529", "blocks": [{"type": "rich_text", "block_id": "/4QvK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It was the initial template "}, {"type": "emoji", "name": "smile", "unicode": "1f604"}, {"type": "text", "text": " . Sure will take care should be easy."}]}]}]}], "created_at": "2025-05-22T22:00:49.359764"}