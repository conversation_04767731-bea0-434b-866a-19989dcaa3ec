{"date": "2024-08-30", "channel_id": "C065QSSNH8A", "message_count": 5, "messages": [{"ts": "1725032410.561479", "text": "I'll be following this format for the call today (under implementation call) and see how that flows. <https://stridehr.monday.com/docs/7332651062>", "user": "U07EJ2LP44S", "type": "message", "attachments": [{"from_url": "https://stridehr.monday.com/docs/7332651062", "service_icon": "https://cdn.monday.com/apple-touch-icon-120x120.png", "thumb_url": "https://s3.amazonaws.com/general-assets/monday-200x200.png", "thumb_width": 200, "thumb_height": 200, "id": 1, "original_url": "https://stridehr.monday.com/docs/7332651062", "fallback": "Welcome to monday.com | a new way of working", "text": "<http://monday.com|monday.com> Work OS is an open platform where anyone can create the tools they need to run every aspect of their work.", "title": "Welcome to monday.com | a new way of working", "title_link": "https://stridehr.monday.com/docs/7332651062", "service_name": "stridehr.monday.com"}], "blocks": [{"type": "rich_text", "block_id": "XGRQo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I'll be following this format for the call today (under implementation call) and see how that flows. "}, {"type": "link", "url": "https://stridehr.monday.com/docs/7332651062"}]}]}]}, {"ts": "1725030761.107409", "text": "<@U04DS2MBWP4> do you have any context on Sonendo before our call today?", "user": "U07EJ2LP44S", "type": "message", "blocks": [{"type": "rich_text", "block_id": "yDDQZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " do you have any context on Sonendo before our call today?"}]}]}]}, {"ts": "1725023810.726949", "text": "General question: in what circumstance are flags triggered? Only upon submission? Only when there's a recommendation in place?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1725023810.726949", "reply_count": 8, "blocks": [{"type": "rich_text", "block_id": "Lf6vH", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "General question: in what circumstance are flags triggered? Only upon submission? Only when there's a recommendation in place?"}]}]}]}, {"ts": "1725020565.332379", "text": "<@U07EJ2LP44S> We have the fix. Unfortunately there is an unexpected infra issue causing deployment failure and can't deploy the fix today.\n<@U04DKEFP1K8> <http://test.stridehr.io|test.stridehr.io> FE deployed successfully but no BE for some changes. Vadym is working on the infra issues. We are so far unable to figure out the issue as it seems related to docker. Probably you will have to use local setup for QA.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1724934314.734769", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1725020622.000000"}, "blocks": [{"type": "rich_text", "block_id": "LaH21", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We have the fix. Unfortunately there is an unexpected infra issue causing deployment failure and can't deploy the fix today.\n"}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "link", "url": "http://test.stridehr.io", "text": "test.stridehr.io"}, {"type": "text", "text": " FE deployed successfully but no BE for some changes. Vadym is working on the infra issues. We are so far unable to figure out the issue as it seems related to docker. Probably you will have to use local setup for QA."}]}]}]}, {"ts": "1724980746.399419", "text": "<@U07EJ2LP44S> I have added new jira tickets for Degenkolb here <https://compiify.atlassian.net/browse/COM-3553>.\n<@U0690EB5JE5> please update prioritization sheet with these requirements", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1724980746.399419", "reply_count": 1, "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:13614::daa32c90666d11ef892e27ae126fa8dd", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-3553?atlOrigin=eyJpIjoiOWU5MjZiZDAzNTA0NGNjMmE5MTI1MWNkOTU2NjE2ZTkiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-3553 Degenkolb UAT>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:13614::daa32c92666d11ef892e27ae126fa8dd", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/epic.png", "alt_text": "Epic"}, {"type": "mrkdwn", "text": "Type: *Epic*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/medium.png", "alt_text": "Medium"}, {"type": "mrkdwn", "text": "Priority: *Medium*", "verbatim": false}]}, {"type": "actions", "block_id": "uf:issueFooterActions:13614::daa32c91666d11ef892e27ae126fa8dd", "elements": [{"type": "button", "action_id": "{\"issueId\":\"13614\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"action\":\"watch\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"main\"}", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "{\"issueId\":\"13614\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"nType\":\"ISSUE_UNFURL\",\"nPlace\":\"menu\"}", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-3553", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "ib0ZM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have added new jira tickets for Degenkolb here "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-3553"}, {"type": "text", "text": ".\n"}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " please update prioritization sheet with these requirements"}]}]}]}], "created_at": "2025-05-22T21:35:34.643158"}