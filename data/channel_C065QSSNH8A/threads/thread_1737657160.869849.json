{"thread_ts": "1737657160.869849", "channel_id": "C065QSSNH8A", "reply_count": 6, "replies": [{"ts": "1737657255.091869", "text": "There's probably data reasons it's not working, a few of these employees had a previous number under bonus amount, so maybe that's it?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737657160.869849", "blocks": [{"type": "rich_text", "block_id": "iPY6m", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There's probably data reasons it's not working, a few of these employees had a previous number under bonus amount, so maybe that's it?"}]}]}]}, {"ts": "1737657341.889319", "text": "My plan is to create a bonus cycle using the three departments in this file as the training data", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737657160.869849", "blocks": [{"type": "rich_text", "block_id": "K8np4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "My plan is to create a bonus cycle using the three departments in this file as the training data"}]}]}]}, {"ts": "1737657634.008259", "text": "<@U07EJ2LP44S> uploaded.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737657160.869849", "reactions": [{"name": "fire", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "fLEFQ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " uploaded."}]}]}]}, {"ts": "1737657658.591739", "text": "for some reason currency column required to be added.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737657160.869849", "blocks": [{"type": "rich_text", "block_id": "mgKZR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "for some reason currency column required to be added."}]}]}]}, {"ts": "1737657665.332669", "text": "", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737657160.869849", "files": [{"id": "F089U7A3RRC", "created": 1737657663, "timestamp": 1737657663, "name": "test - PayrightBonuses (1).csv", "title": "test - PayrightBonuses (1).csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": true, "size": 1619, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F089U7A3RRC/test_-_payrightbonuses__1_.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F089U7A3RRC/download/test_-_payrightbonuses__1_.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F089U7A3RRC/test_-_payrightbonuses__1_.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F089U7A3RRC-d5019fe46b", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F089U7A3RRC/test_-_payrightbonuses__1_.csv/edit", "preview": "\"Update Type (NC,A,D,U)\",Employee Id *,Target Bonus Currency,Target Bonus (%),Target Company Performance (%),Target Individual Performance (%),Earned Company Performance (%),Earned Individual Performance (%)\r\nU,37,USD,10,40,60,100,71\r\nU,10,USD,10,40,60,100,72\r\nU,126,USD,10,40,60,100,73\r\nU,168,USD,10,40,60,100,74\r\nU,231,USD,10,40,60,100,75\r\nU,19,USD,10,40,60,100,76\r\nU,24,USD,10,40,60,100,77\r\nU,157,USD,10,40,60,100,78\r\nU,193,USD,10,40,60,100,79\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Update Type (NC,A,D,U)</div><div class=\"cm-col\">Employee Id *</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Company Performance (%)</div><div class=\"cm-col\">Target Individual Performance (%)</div><div class=\"cm-col\">Earned Company Performance (%)</div><div class=\"cm-col\">Earned Individual Performance (%)</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">37</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">71</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">72</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">126</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">73</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">168</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">74</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">231</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">75</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">19</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">76</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">24</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">77</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">157</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">78</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">U</div><div class=\"cm-col cm-num\">193</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col cm-num\">40</div><div class=\"cm-col cm-num\">60</div><div class=\"cm-col cm-num\">100</div><div class=\"cm-col cm-num\">79</div></div></div>\n</div>\n", "lines": 54, "lines_more": 44, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}]}, {"ts": "1737657724.270079", "text": "Thank you!", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1737657160.869849", "blocks": [{"type": "rich_text", "block_id": "Ye0AT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you!"}]}]}]}], "created_at": "2025-05-22T22:00:49.349202"}