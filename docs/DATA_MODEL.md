# RAGSearch Data Model

This document provides a comprehensive overview of the data models used in the RAGSearch system, a multi-tenant RAG application that supports various document sources.

## Entity Relationship Diagram

```mermaid
classDiagram
    class Tenant {
        string id
        string name
        string domain
        string slug
        datetime created_at
        datetime updated_at
        json config
        boolean is_active
    }

    class User {
        string id
        string username
        string email
        string password
        boolean is_active
    }

    class UserProfile {
        string id
        string user_id
        string tenant_id
        string bio
        json preferences
        datetime created_at
        datetime updated_at
    }

    class UserPlatformProfile {
        string id
        string user_id
        string platform
        string platform_user_id
        string display_name
        string avatar_url
        datetime created_at
        datetime updated_at
    }

    class DocumentSource {
        string id
        string tenant_id
        string name
        string source_type
        json config
        datetime last_synced
        boolean is_active
    }

    class RawDocument {
        string id
        string tenant_id
        string source_id
        string title
        string external_id
        string permalink
        datetime fetched_at
        json metadata
        string content_hash
        string content_type
    }

    class DocumentChunk {
        string id
        string tenant_id
        string document_id
        string profile_id
        string text
        string chunk_type
        string thread_id
        datetime created_at
        int token_count
        float importance_score
        int chunk_index
        json metadata
    }

    class EmbeddingMetadata {
        string id
        string chunk_id
        string vector_id
        datetime embedded_at
        string model_name
        int vector_dimensions
    }

    class ChunkRelationship {
        string id
        string source_chunk_id
        string target_chunk_id
        string relation_type
        float relation_strength
    }

    class VectorIndex {
        string id
        string tenant_id
        string name
        string description
        string vector_db_type
        string collection_name
        int dimension
        boolean is_active
    }

    class EmbeddingModel {
        string id
        string tenant_id
        string name
        string model_name
        int dimension
        boolean is_default
    }

    class SearchQuery {
        string id
        string tenant_id
        string user_id
        string session_id
        string query_text
        datetime timestamp
        json search_params
    }

    class UserSession {
        string id
        string tenant_id
        string user_id
        datetime started_at
        json context_metadata
    }

    class SearchResult {
        string id
        string query_id
        string user_id
        string generated_answer
        datetime timestamp
        float retriever_score_avg
        float llm_confidence_score
    }

    class ResultCitation {
        string id
        string result_id
        string document_chunk_id
        float relevance_score
        int rank
    }

    class Feedback {
        string id
        string result_id
        string user_id
        boolean is_helpful
        string comment
        datetime submitted_at
    }

    class Conversation {
        string id
        string tenant_id
        string user_id
        string title
    }

    class Message {
        string id
        string conversation_id
        string search_query_id
        string content
        boolean is_user
        datetime created_at
    }

    class DocumentProcessingJob {
        string id
        string tenant_id
        string source_id
        string user_id
        string status
        datetime started_at
        datetime completed_at
        string error_message
        int documents_processed
        int documents_failed
    }

    class APIKey {
        string id
        string tenant_id
        string user_id
        string name
        string key
        boolean is_active
        datetime last_used
    }

    class APIUsage {
        string id
        string api_key_id
        string endpoint
        string method
        int status_code
        float response_time
        datetime timestamp
    }

    Tenant --> UserProfile : has
    Tenant --> DocumentSource : owns
    Tenant --> RawDocument : owns
    Tenant --> SearchQuery : owns
    Tenant --> UserSession : owns
    Tenant --> Conversation : owns
    Tenant --> VectorIndex : owns
    Tenant --> EmbeddingModel : owns

    User --> UserProfile : has
    User --> UserPlatformProfile : uses
    User --> SearchQuery : submits
    User --> SearchResult : receives
    User --> Feedback : provides
    User --> UserSession : starts
    User --> Conversation : initiates
    User --> APIKey : owns

    DocumentSource --> RawDocument : provides
    DocumentSource --> DocumentProcessingJob : executes
    RawDocument --> DocumentChunk : contains
    UserPlatformProfile --> DocumentChunk : authors
    DocumentChunk --> EmbeddingMetadata : embeds
    DocumentChunk --> ChunkRelationship : links

    SearchQuery --> SearchResult : generates
    SearchQuery --> Message : sends
    SearchResult --> ResultCitation : cites
    SearchResult --> Feedback : collects
    ResultCitation --> DocumentChunk : references
    UserSession --> SearchQuery : includes
    Conversation --> Message : has
    APIKey --> APIUsage : logs
```

## Model Descriptions

### Core Models

#### Tenant
The root entity that represents an organization using the RAG system. Each tenant has isolated data, configuration preferences, and users.

#### User
Django's built-in user model for authentication. Includes username, email, and password fields.

#### UserProfile
Extends the User model with tenant association and preferences. Links a user to their tenant and stores user-specific settings.

#### UserPlatformProfile
Represents a user's identity on an external platform (Slack, GitHub, etc.). A single authenticated user may have multiple platform profiles.

### Document Models

#### DocumentSource
Represents a source of documents for a specific tenant. Supports various types (Slack, GitHub, etc.) with source-specific configuration.

#### RawDocument
Core document entity representing individual content pieces from various sources. Contains metadata and permalinks for citations.

#### DocumentChunk
Represents chunks of text created from documents during processing. Links to both the source document and authoring platform profile.

#### EmbeddingMetadata
Stores vector embeddings metadata for document chunks. Separates embedding metadata from content for vector store flexibility.

#### ChunkRelationship
Tracks relationships between chunks, such as thread replies or related sections. Preserves conversational and document context.

#### DocumentProcessingJob
Tracks the status and progress of document ingestion jobs. Includes metadata about processing success/failure.

### Vector Database Models

#### VectorIndex
Tracks vector database collections and their configuration. Enables managing multiple vector indexes per tenant.

#### EmbeddingModel
Stores information about embedding models used in the system. Allows tenants to configure embedding preferences.

### Search Models

#### SearchQuery
Records user queries with tenant isolation. Links to user session for conversation context preservation.

#### UserSession
Tracks user interaction sessions, allowing for contextual conversations and history-aware responses.

#### SearchResult
Stores generated answers and retrieval quality metrics. Links to the originating query and user.

#### ResultCitation
Junction model linking search results to the document chunks used to generate the answer. Includes relevance score and ranking.

#### Feedback
Captures user feedback on search results, enabling quality improvement and performance tracking.

### Conversation Models

#### Conversation
Represents a conversation session between a user and the RAG system. Serves as a container for messages.

#### Message
Individual messages within a conversation. Can be from the user or the system, and may link to search queries.

### API Models

#### APIKey
Manages API keys for programmatic access to the system. Links keys to users and tenants for proper isolation.

#### APIUsage
Tracks API usage metrics for monitoring and rate limiting purposes.

## Vector Database Schema (Qdrant)

In Qdrant, collections follow this structure:

```
Collection: {tenant_id}_{vector_index_name}
Vector Dimension: Based on embedding model (e.g., 768 or 1536)

Payload:
{
    "chunk_id": "DocumentChunk.id",
    "text": "Chunk text",
    "document_id": "RawDocument.id",
    "document_title": "RawDocument.title",
    "permalink": "RawDocument.permalink",
    "source_type": "DocumentSource.source_type",
    "source_name": "DocumentSource.name",
    "platform": "UserPlatformProfile.platform",
    "platform_user_id": "UserPlatformProfile.platform_user_id",
    "author_name": "UserPlatformProfile.display_name",
    "created_at": "DocumentChunk.created_at",
    "thread_id": "DocumentChunk.thread_id",
    "importance_score": "DocumentChunk.importance_score",
    "chunk_type": "DocumentChunk.chunk_type",
    "metadata": "DocumentChunk.metadata"
}
```

## Implementation Notes

1. **Tenant Isolation**: Complete tenant separation through foreign keys and collection naming

2. **Database Indexing**: All foreign keys are indexed for performance

3. **Abstract Base Model**: TenantAwareModel provides common fields and behavior

4. **Django Integration**: Leverages Django's auth system while extending it for multi-tenancy

5. **Citation Formatting**: Supports formatted citations based on document source type

6. **Processing Flow**: Document processing follows a consistent pipeline from source to chunks to embeddings

7. **Vector Store Flexibility**: Models support switching between vector database implementations
