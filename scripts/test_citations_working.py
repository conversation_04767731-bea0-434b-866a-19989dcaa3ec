#!/usr/bin/env python
"""
Test that citations are working correctly in the search system.
"""
import os, sys, django

sys.path.append('multi_source_rag'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development"); django.setup()

from apps.search.services.rag_service import RAGService
from django.contrib.auth.models import User

def test_citations_working():
    """Test that citations are working correctly."""
    print("🔍 Testing Citations Functionality")
    print("=" * 60)
    
    # Get the user
    user = User.objects.get(email="<EMAIL>")
    print(f"Using user: {user.email}")
    
    # Create RAG service
    rag_service = RAGService(user=user, tenant_slug="stride")
    print(f"✅ RAG service created with tenant: {rag_service.tenant_slug}")
    
    # Test queries that should have citations
    test_queries = [
        "List issues reported by <PERSON>",
        "What problems did <PERSON> mention?", 
        "Show me all bug reports",
        "What is discussed in the channel?",
        "Tell me about testing"
    ]
    
    print(f"\n🧪 Testing {len(test_queries)} queries for citations:")
    
    total_tests = 0
    successful_tests = 0
    total_citations = 0
    
    for i, query in enumerate(test_queries):
        print(f"\n🔍 Test {i+1}: '{query}'")
        
        try:
            # Perform search
            result, docs = rag_service.search(query, top_k=10)
            
            # Check citations
            citations = result.citations.all()
            citation_count = citations.count()
            
            print(f"   ✅ Search completed")
            print(f"   📊 Result ID: {result.id}")
            print(f"   📊 Tenant: {result.search_query.tenant.slug}")
            print(f"   📊 Citations: {citation_count}")
            
            if citation_count > 0:
                print(f"   ✅ Citations working!")
                successful_tests += 1
                total_citations += citation_count
                
                # Show first citation details
                first_citation = citations.first()
                print(f"   📖 First citation:")
                print(f"      Chunk ID: {first_citation.document_chunk.id}")
                print(f"      Score: {first_citation.relevance_score:.3f}")
                if first_citation.document_chunk.document:
                    doc = first_citation.document_chunk.document
                    print(f"      Document: {doc.title[:50]}...")
                    if doc.permalink:
                        print(f"      Permalink: ✅ Available")
                    else:
                        print(f"      Permalink: ❌ Missing")
            else:
                print(f"   ❌ No citations created")
            
            total_tests += 1
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            total_tests += 1
    
    # Summary
    print(f"\n📊 CITATION TEST SUMMARY")
    print(f"=" * 60)
    print(f"Total tests: {total_tests}")
    print(f"Successful tests (with citations): {successful_tests}")
    print(f"Success rate: {(successful_tests/total_tests)*100:.1f}%")
    print(f"Total citations created: {total_citations}")
    print(f"Average citations per successful test: {total_citations/successful_tests if successful_tests > 0 else 0:.1f}")
    
    if successful_tests == total_tests:
        print(f"\n🎉 ALL TESTS PASSED! Citations are working perfectly!")
        return True
    elif successful_tests > 0:
        print(f"\n⚠️  PARTIAL SUCCESS: {successful_tests}/{total_tests} tests passed")
        return False
    else:
        print(f"\n❌ ALL TESTS FAILED: Citations are not working")
        return False

if __name__ == "__main__":
    success = test_citations_working()
    if success:
        print("\n✅ Citations functionality is production-ready!")
    else:
        print("\n❌ Citations functionality needs attention!")
