{"date": "2024-09-18", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1726679754.516489", "text": "you can accept it. I will check why it shows tomorrow", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1726679754.516489", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "Soyqt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "you can accept it. I will check why it shows tomorrow"}]}]}]}, {"ts": "1726679624.823969", "text": "<@U0690EB5JE5> i am seeing this window pop up , fyi", "user": "U04DKEFP1K8", "type": "message", "files": [{"id": "F07N03F7RB5", "created": 1726679619, "timestamp": 1726679619, "name": "Screenshot 2024-09-18 at 10.13.10 AM.png", "title": "Screenshot 2024-09-18 at 10.13.10 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DKEFP1K8", "user_team": "T04DM97F1UM", "editable": false, "size": 181085, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07N03F7RB5/screenshot_2024-09-18_at_10.13.10___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07N03F7RB5/download/screenshot_2024-09-18_at_10.13.10___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_360.png", "thumb_360_w": 273, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_480.png", "thumb_480_w": 364, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_720.png", "thumb_720_w": 546, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_800.png", "thumb_800_w": 800, "thumb_800_h": 1055, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_960.png", "thumb_960_w": 728, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07N03F7RB5-45e7ae820e/screenshot_2024-09-18_at_10.13.10___am_1024.png", "thumb_1024_w": 776, "thumb_1024_h": 1024, "original_w": 978, "original_h": 1290, "thumb_tiny": "AwAwACTTooFRyRqxyQx+lAD9wPcUtQ+Um7O1uv4VNQAUUDpRQAVV8qYXe7rHnPWrPY4ppDH0oAcQT0OPwpR05pgDA9qcM96AFHSigdKKACmgL/d/SnUnNAFWe7WKUx+XnHfNOtroTSFAm3Az1qxj2FH4CqurbAKOlFFFSB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07N03F7RB5/screenshot_2024-09-18_at_10.13.10___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07N03F7RB5-51933f6087", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "n0y+H", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " i am seeing this window pop up , fyi"}]}]}]}, {"ts": "1726676848.775439", "text": "<@U07EJ2LP44S> <@U04DKEFP1K8> `<https://alayacare-test.stridehr.io/>` is ready. This has the exact copy of data from <http://demo.stridehr.io|demo.stridehr.io>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1726676848.775439", "reply_count": 4, "reactions": [{"name": "+1", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "67RtJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "link", "url": "https://alayacare-test.stridehr.io/", "style": {"code": true}}, {"type": "text", "text": " is ready. This has the exact copy of data from "}, {"type": "link", "url": "http://demo.stridehr.io", "text": "demo.stridehr.io"}]}]}]}, {"ts": "1726672841.156789", "text": "<@U0690EB5JE5> can you add <@U07NBMXTL1E> to demo and test environments? With SSO?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1726672841.156789", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "KaS+X", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " can you add "}, {"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " to demo and test environments? With SSO?"}]}]}]}, {"ts": "1726646853.846219", "text": "<@U07EJ2LP44S> sdf-test ENV is updated with their cycle data now it is copy of their current production state. <@U04DKEFP1K8> Please verify and let me know if anything else needs to be taken care w.r.t cycle per new merit flow.", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "ack", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ckgC7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " sdf-test ENV is updated with their cycle data now it is copy of their current production state. "}, {"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Please verify and let me know if anything else needs to be taken care w.r.t cycle per new merit flow."}]}]}]}, {"ts": "1726636871.399349", "text": "<@U07M6QKHUC9> Finally renamed \"Compiify Admin\" to \"Stride Admin\" :slightly_smiling_face:", "user": "U0690EB5JE5", "type": "message", "reactions": [{"name": "partyparrot", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Rd2o8", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Finally renamed \"Compiify Admin\" to \"Stride Admin\" "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}, {"ts": "1726617093.966589", "text": "<!here> For the leadership standup, let’s make sure we clearly distinguish between the November and January implementations. It’s highly critical that we consistently prioritize the November tasks over those scheduled for January.", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "100", "users": ["U07M6QKHUC9", "U07EJ2LP44S"], "count": 2}], "blocks": [{"type": "rich_text", "block_id": "Z+I9u", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " For the leadership standup, let’s make sure we clearly distinguish between the November and January implementations. It’s highly critical that we consistently prioritize the November tasks over those scheduled for January."}]}]}]}, {"ts": "1726610441.348659", "text": "I gave a demo to a customer today. They are deciding between <http://salary.com|salary.com> and us. It will be a good test to how do we fair against <http://salary.com|salary.com>", "user": "U07M6QKHUC9", "type": "message", "blocks": [{"type": "rich_text", "block_id": "7DzKV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I gave a demo to a customer today. They are deciding between "}, {"type": "link", "url": "http://salary.com", "text": "salary.com"}, {"type": "text", "text": " and us. It will be a good test to how do we fair against "}, {"type": "link", "url": "http://salary.com", "text": "salary.com"}]}]}]}, {"ts": "1726605387.416699", "text": "SaaS startup for dmarc, dkim stuff <https://easydmarc.com/> :slightly_smiling_face: Raised $20m, cmon :slightly_smiling_face:", "user": "U04DKEFP1K8", "type": "message", "thread_ts": "1726605387.416699", "reply_count": 1, "attachments": [{"from_url": "https://easydmarc.com/", "service_icon": "https://easydmarc.com/img/favicon/apple-touch-icon.png", "thumb_url": "https://easydmarc.com/img/easydmarc-og.jpg", "thumb_width": 1200, "thumb_height": 627, "id": 1, "original_url": "https://easydmarc.com/", "fallback": "EasyDMARC: EasyDMARC | DMARC Journey Made Simple", "text": "Your smart DMARC reporting and monitoring platform. Ensure domain-level security and email deliverability with EasyDMARC’s DMARC, SPF, DKIM, and BIMI services.", "title": "EasyDMARC | DMARC Journey Made Simple", "title_link": "https://easydmarc.com/", "service_name": "EasyDMARC"}], "blocks": [{"type": "rich_text", "block_id": "pLGK/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "SaaS startup for dmarc, dkim stuff "}, {"type": "link", "url": "https://easydmarc.com/"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " Raised $20m, cmon "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}]}]}]}], "created_at": "2025-05-22T21:35:34.638143"}