{"date": "2024-02-19", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1708309313.229159", "text": "I've also started a <https://docs.google.com/document/d/15jp8d6rO3_SsU4Jdgl7g96ag9zvIwul_uCcp8Ulw7MI/edit|PRD for Total Rewards settings>, to capture the level of customization we'll need to support in the design.\n\nSome of the main questions I have while completing this:\n• Which types of employee data are customers most likely to use to determine who sees Total Rewards and specific benefits?\n• What configuration will we need from admins (in addition to any uploaded data) in order to display accurate Equity information? ", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1708309313.229159", "reply_count": 2, "files": [{"id": "F06KH6CDN68", "created": 1708309315, "timestamp": 1708309315, "name": "Total Rewards Config PRD", "title": "Total Rewards Config PRD", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "15jp8d6rO3_SsU4Jdgl7g96ag9zvIwul_uCcp8Ulw7MI", "external_url": "https://docs.google.com/document/d/15jp8d6rO3_SsU4Jdgl7g96ag9zvIwul_uCcp8Ulw7MI/edit", "url_private": "https://docs.google.com/document/d/15jp8d6rO3_SsU4Jdgl7g96ag9zvIwul_uCcp8Ulw7MI/edit", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06KH6CDN68-639574a128/total_rewards_config_prd_1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACTSOcjBApMN/eH5UrDjOOab83vQA4Bs8kEfSlpg3UvP+TQA6iiigBrdR1/OgYHcmlJx/wDqpoHNAD80ZpuB3/nS4H+TQAtFFFACN0pgzT2z2OKaCfXP40AHPqKBml3ew/OlBPp+tACjpRRRQB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06KH6CDN68/total_rewards_config_prd", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "sv2vJ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I've also started a "}, {"type": "link", "url": "https://docs.google.com/document/d/15jp8d6rO3_SsU4Jdgl7g96ag9zvIwul_uCcp8Ulw7MI/edit", "text": "PRD for Total Rewards settings"}, {"type": "text", "text": ", to capture the level of customization we'll need to support in the design.\n\nSome of the main questions I have while completing this:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Which types of employee data are customers most likely to use to determine who sees Total Rewards and specific benefits?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "What configuration will we need from admins (in addition to any uploaded data) in order to display accurate Equity information? "}]}], "style": "bullet", "indent": 0, "border": 0}]}]}], "created_at": "2025-05-22T21:35:34.581826"}