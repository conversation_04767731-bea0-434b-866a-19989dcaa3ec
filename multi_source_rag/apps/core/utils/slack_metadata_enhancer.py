"""
Slack Metadata Enhancement for Vector Search Filtering

This module enhances Slack message metadata to enable powerful filtering
capabilities in vector search. It preserves rich Slack context including
users, timestamps, reactions, threads, and conversation patterns.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Set
import re

logger = logging.getLogger(__name__)


class SlackMetadataEnhancer:
    """
    Enhances Slack metadata for better vector search filtering and context preservation.
    """
    
    def __init__(self):
        """Initialize the metadata enhancer."""
        self.user_cache = {}
        self.channel_cache = {}
    
    def enhance_document_metadata(
        self,
        document: Dict[str, Any],
        source_type: str = "local_slack"
    ) -> Dict[str, Any]:
        """
        Enhance document metadata with rich Slack context for vector filtering.
        
        Args:
            document: Document with Slack content and metadata
            source_type: Type of Slack source
            
        Returns:
            Dict[str, Any]: Enhanced document with vector-ready metadata
        """
        if source_type not in ["slack", "local_slack"]:
            return document
        
        original_metadata = document.get("metadata", {})
        enhanced_metadata = self._create_enhanced_metadata(original_metadata, document)
        
        # Merge enhanced metadata with original
        document["metadata"] = {**original_metadata, **enhanced_metadata}
        
        logger.debug(f"Enhanced metadata for document {document.get('id', 'unknown')}")
        return document
    
    def _create_enhanced_metadata(
        self,
        original_metadata: Dict[str, Any],
        document: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create enhanced metadata for vector search filtering.
        
        Args:
            original_metadata: Original document metadata
            document: Full document data
            
        Returns:
            Dict[str, Any]: Enhanced metadata
        """
        enhanced = {}
        
        # 1. Time-based metadata for temporal filtering
        enhanced.update(self._extract_temporal_metadata(original_metadata))
        
        # 2. User and participation metadata
        enhanced.update(self._extract_user_metadata(original_metadata))
        
        # 3. Conversation and thread metadata
        enhanced.update(self._extract_conversation_metadata(original_metadata))
        
        # 4. Content quality and engagement metadata
        enhanced.update(self._extract_engagement_metadata(original_metadata))
        
        # 5. Technical content metadata
        enhanced.update(self._extract_technical_metadata(original_metadata, document))
        
        # 6. Channel and workspace metadata
        enhanced.update(self._extract_channel_metadata(original_metadata))
        
        return enhanced
    
    def _extract_temporal_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Extract time-based metadata for temporal filtering."""
        temporal = {}
        
        # Parse start and end times
        start_time = metadata.get("start_time")
        end_time = metadata.get("end_time")
        
        if start_time:
            try:
                start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                temporal.update({
                    "start_timestamp": int(start_dt.timestamp()),
                    "start_date": start_dt.strftime("%Y-%m-%d"),
                    "start_hour": start_dt.hour,
                    "start_weekday": start_dt.weekday(),  # 0=Monday, 6=Sunday
                    "start_month": start_dt.strftime("%Y-%m"),
                    "start_year": start_dt.year
                })
            except Exception as e:
                logger.warning(f"Failed to parse start_time {start_time}: {e}")
        
        if end_time:
            try:
                end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                temporal.update({
                    "end_timestamp": int(end_dt.timestamp()),
                    "end_date": end_dt.strftime("%Y-%m-%d"),
                    "duration_hours": (end_dt - start_dt).total_seconds() / 3600 if start_time else 0
                })
            except Exception as e:
                logger.warning(f"Failed to parse end_time {end_time}: {e}")
        
        # Add period information
        period_type = metadata.get("period_type")
        if period_type:
            temporal["period_type"] = period_type
        
        period_key = metadata.get("period_key")
        if period_key:
            temporal["period_key"] = period_key
        
        return temporal
    
    def _extract_user_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Extract user and participation metadata."""
        user_meta = {}
        
        # Participants
        participants = metadata.get("participants", [])
        if participants:
            user_meta.update({
                "participants": participants,
                "participant_count": len(participants),
                "has_multiple_participants": len(participants) > 1
            })
            
            # Extract unique user domains (if email-like usernames)
            domains = set()
            for participant in participants:
                if "@" in participant:
                    domain = participant.split("@")[-1]
                    domains.add(domain)
            
            if domains:
                user_meta["participant_domains"] = list(domains)
                user_meta["cross_domain_conversation"] = len(domains) > 1
        
        return user_meta
    
    def _extract_conversation_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Extract conversation and thread metadata."""
        conv_meta = {}
        
        # Message and thread counts
        message_count = metadata.get("message_count", 0)
        thread_count = metadata.get("thread_count", 0)
        reply_count = metadata.get("reply_count", 0)
        
        conv_meta.update({
            "message_count": message_count,
            "thread_count": thread_count,
            "reply_count": reply_count,
            "has_threads": thread_count > 0,
            "has_replies": reply_count > 0,
            "conversation_size": self._categorize_conversation_size(message_count)
        })
        
        # Thread information
        thread_ts = metadata.get("thread_ts")
        if thread_ts:
            conv_meta.update({
                "thread_ts": thread_ts,
                "is_thread": True
            })
        
        return conv_meta
    
    def _extract_engagement_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Extract engagement and quality metadata."""
        engagement = {}
        
        # Quality score
        quality_score = metadata.get("quality_score", 0.0)
        engagement.update({
            "quality_score": quality_score,
            "quality_tier": self._categorize_quality(quality_score)
        })
        
        # Reactions
        reactions = metadata.get("reactions", [])
        if reactions:
            total_reactions = sum(r.get("count", 0) for r in reactions if isinstance(r, dict))
            unique_reactions = len(reactions)
            
            engagement.update({
                "total_reactions": total_reactions,
                "unique_reactions": unique_reactions,
                "has_reactions": total_reactions > 0,
                "highly_reacted": total_reactions >= 5,
                "reaction_types": [r.get("name", "") for r in reactions if isinstance(r, dict)]
            })
        
        return engagement
    
    def _extract_technical_metadata(
        self,
        metadata: Dict[str, Any],
        document: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract technical content metadata."""
        technical = {}
        
        # Code presence
        has_code = metadata.get("has_code", False)
        technical["has_code"] = has_code
        
        # Technical terms
        technical_terms = metadata.get("technical_terms", [])
        if technical_terms:
            technical.update({
                "technical_terms": technical_terms,
                "technical_term_count": len(technical_terms),
                "is_technical": len(technical_terms) > 0
            })
        
        # Content analysis
        content = document.get("content", "")
        if content:
            technical.update(self._analyze_content_patterns(content))
        
        return technical
    
    def _extract_channel_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Extract channel and workspace metadata."""
        channel_meta = {}
        
        # Channel information
        channel = metadata.get("channel")
        if channel:
            channel_meta["channel"] = channel
            
            # Infer channel type from name patterns
            if channel.startswith("C"):
                channel_meta["channel_type"] = "public"
            elif channel.startswith("D"):
                channel_meta["channel_type"] = "direct_message"
            elif channel.startswith("G"):
                channel_meta["channel_type"] = "private_group"
            else:
                channel_meta["channel_type"] = "unknown"
        
        return channel_meta
    
    def _categorize_conversation_size(self, message_count: int) -> str:
        """Categorize conversation size for filtering."""
        if message_count <= 5:
            return "small"
        elif message_count <= 20:
            return "medium"
        elif message_count <= 50:
            return "large"
        else:
            return "very_large"
    
    def _categorize_quality(self, quality_score: float) -> str:
        """Categorize quality score for filtering."""
        if quality_score >= 0.8:
            return "high"
        elif quality_score >= 0.6:
            return "medium"
        elif quality_score >= 0.4:
            return "low"
        else:
            return "very_low"
    
    def _analyze_content_patterns(self, content: str) -> Dict[str, Any]:
        """Analyze content for additional metadata."""
        patterns = {}
        
        # URL patterns
        url_pattern = r'https?://[^\s]+'
        urls = re.findall(url_pattern, content)
        if urls:
            patterns.update({
                "has_urls": True,
                "url_count": len(urls),
                "urls": urls[:5]  # Limit to first 5 URLs
            })
        
        # Mention patterns
        mention_pattern = r'<@[UW][A-Z0-9]+>'
        mentions = re.findall(mention_pattern, content)
        if mentions:
            patterns.update({
                "has_mentions": True,
                "mention_count": len(mentions)
            })
        
        # Channel reference patterns
        channel_pattern = r'<#[C][A-Z0-9]+\|[^>]+>'
        channel_refs = re.findall(channel_pattern, content)
        if channel_refs:
            patterns.update({
                "has_channel_refs": True,
                "channel_ref_count": len(channel_refs)
            })
        
        # Question patterns
        question_patterns = [r'\?', r'\bwhat\b', r'\bhow\b', r'\bwhy\b', r'\bwhen\b', r'\bwhere\b']
        has_questions = any(re.search(pattern, content, re.IGNORECASE) for pattern in question_patterns)
        patterns["has_questions"] = has_questions
        
        return patterns
    
    def create_vector_metadata_filter(
        self,
        time_range: Optional[Dict[str, Any]] = None,
        participants: Optional[List[str]] = None,
        channels: Optional[List[str]] = None,
        quality_tier: Optional[str] = None,
        has_code: Optional[bool] = None,
        conversation_size: Optional[str] = None,
        has_reactions: Optional[bool] = None,
        is_technical: Optional[bool] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a metadata filter for vector search.
        
        Args:
            time_range: Time range filter (start_timestamp, end_timestamp)
            participants: List of participants to filter by
            channels: List of channels to filter by
            quality_tier: Quality tier (high, medium, low, very_low)
            has_code: Filter by presence of code
            conversation_size: Conversation size (small, medium, large, very_large)
            has_reactions: Filter by presence of reactions
            is_technical: Filter by technical content
            **kwargs: Additional filter parameters
            
        Returns:
            Dict[str, Any]: Qdrant-compatible metadata filter
        """
        filters = {}
        
        # Time range filtering
        if time_range:
            if "start_timestamp" in time_range:
                filters["start_timestamp"] = {"range": {"gte": time_range["start_timestamp"]}}
            if "end_timestamp" in time_range:
                filters["end_timestamp"] = {"range": {"lte": time_range["end_timestamp"]}}
        
        # Participant filtering
        if participants:
            filters["participants"] = participants
        
        # Channel filtering
        if channels:
            filters["channel"] = channels
        
        # Quality filtering
        if quality_tier:
            filters["quality_tier"] = quality_tier
        
        # Boolean filters
        if has_code is not None:
            filters["has_code"] = has_code
        
        if has_reactions is not None:
            filters["has_reactions"] = has_reactions
        
        if is_technical is not None:
            filters["is_technical"] = is_technical
        
        # Conversation size filtering
        if conversation_size:
            filters["conversation_size"] = conversation_size
        
        # Add any additional filters
        filters.update(kwargs)
        
        return filters


# Utility functions for easy integration
def enhance_slack_metadata(document: Dict[str, Any], source_type: str = "local_slack") -> Dict[str, Any]:
    """Convenience function to enhance Slack metadata."""
    enhancer = SlackMetadataEnhancer()
    return enhancer.enhance_document_metadata(document, source_type)


def create_slack_search_filter(**kwargs) -> Dict[str, Any]:
    """Convenience function to create Slack search filters."""
    enhancer = SlackMetadataEnhancer()
    return enhancer.create_vector_metadata_filter(**kwargs)
