{"thread_ts": "1734370134.049029", "channel_id": "C065QSSNH8A", "reply_count": 6, "replies": [{"ts": "1734370170.336049", "text": "I cannot access, can you add me to the file?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734370134.049029", "blocks": [{"type": "rich_text", "block_id": "sQ71Z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I cannot access, can you add me to the file?"}]}]}]}, {"ts": "1734370180.810429", "text": "(and thank you!)", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734370134.049029", "blocks": [{"type": "rich_text", "block_id": "8atS1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "(and thank you!)"}]}]}]}, {"ts": "1734444207.364829", "text": "<@U07EJ2LP44S> Letters updated per email from customer. Also folder structure per recommenders hierarchy. Please spot check once before sharing with customer.\n<@U07M6QKHUC9> Again this took almost 2 days of an engineer's time. This is not sustainable in the long term. We should stop accepting customer specific template requirements and generalize with support for little customizations (self serve)", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734370134.049029", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1734444386.000000"}, "files": [{"id": "F085DFAV2PP", "created": 1734444063, "timestamp": 1734444063, "name": "employee_letters_nested_fixed.zip", "title": "employee_letters_nested_fixed.zip", "mimetype": "application/zip", "filetype": "zip", "pretty_type": "Zip", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 27137152, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F085DFAV2PP/employee_letters_nested_fixed.zip", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F085DFAV2PP/download/employee_letters_nested_fixed.zip", "media_display_type": "unknown", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F085DFAV2PP/employee_letters_nested_fixed.zip", "permalink_public": "https://slack-files.com/T04DM97F1UM-F085DFAV2PP-b446eaab85", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "S7qsH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Letters updated per email from customer. Also folder structure per recommenders hierarchy. Please spot check once before sharing with customer.\n"}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " Again this took almost 2 days of an engineer's time. This is not sustainable in the long term. We should stop accepting customer specific template requirements and generalize with support for little customizations (self serve)"}]}]}]}, {"ts": "1734446898.216719", "text": "THANK YOU! It looked ok to me, so I sent it on.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734370134.049029", "blocks": [{"type": "rich_text", "block_id": "1bied", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "THANK YOU! It looked ok to me, so I sent it on."}]}]}]}, {"ts": "1734446925.681429", "text": "Also we have custom templates already for Valgenesis. I think we will probably have them for Curana as well.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734370134.049029", "blocks": [{"type": "rich_text", "block_id": "29iDi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also we have custom templates already for Valgenesis. I think we will probably have them for Curana as well."}]}]}]}, {"ts": "1734447668.964499", "text": "Ok. Let’s discuss before committing to customers going forward. Only problem is we support now but can we do the same for next cycle? ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734370134.049029", "blocks": [{"type": "rich_text", "block_id": "iCIF+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. "}, {"type": "text", "text": "Let’s"}, {"type": "text", "text": " discuss before committing to customers going forward. Only problem is we support"}, {"type": "text", "text": " "}, {"type": "text", "text": "now but can we do the same for next cycle? "}]}]}]}], "created_at": "2025-05-22T22:00:49.314302"}