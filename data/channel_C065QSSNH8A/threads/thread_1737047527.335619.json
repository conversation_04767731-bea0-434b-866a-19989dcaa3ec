{"thread_ts": "1737047527.335619", "channel_id": "C065QSSNH8A", "reply_count": 6, "replies": [{"ts": "1737048242.100429", "text": "<@U07M6QKHUC9> When we last discussed, we didn't discuss about adding history and benefits. Instead we show them Total Rewards. And I don't think its worth the effort for one customer.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737047527.335619", "blocks": [{"type": "rich_text", "block_id": "1kwOA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " When we last discussed, we didn't discuss about adding history and benefits. Instead we show them Total Rewards. And I don't think its worth the effort for one customer."}]}]}]}, {"ts": "1737048290.509579", "text": "benefits in total rewards not the letters", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737047527.335619", "blocks": [{"type": "rich_text", "block_id": "wArYp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "benefits in total rewards not the letters"}]}]}]}, {"ts": "1737048326.685359", "text": "Thats yes. I think its already in scope of requirements. correct me.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737047527.335619", "blocks": [{"type": "rich_text", "block_id": "/fYF5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thats yes. I think its already in scope of requirements. correct me."}]}]}]}, {"ts": "1737048334.609299", "text": "both history and benefits in the total rewards not the adjustment letters", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737047527.335619", "blocks": [{"type": "rich_text", "block_id": "nB+Ym", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "both history and benefits in the total rewards not the adjustment letters"}]}]}]}, {"ts": "1737048378.688179", "text": "I think they are but want to make sure we can upload individual benefits for each employee in total rewards", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1737047527.335619", "blocks": [{"type": "rich_text", "block_id": "+qGHi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think they are but want to make sure we can upload individual benefits for each employee in total rewards"}]}]}]}, {"ts": "1737048398.044389", "text": "oh sorry, I just letters and got confused. Yes screenshot of UI if its configured and data uploaded.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1737047527.335619", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "s8Czn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "oh sorry, I just letters and got confused. Yes screenshot of UI if its configured and data uploaded."}]}]}]}], "created_at": "2025-05-22T22:00:49.331531"}