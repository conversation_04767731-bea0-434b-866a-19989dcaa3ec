#!/usr/bin/env python3
"""
Comprehensive RAG Test Suite - Production Ready Testing
Tests the system holistically with real data from simple to complex queries.
"""

import os
import sys
import django
import time
import json
from typing import List, Dict, Any

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.unified_rag_service import UnifiedRAGService
from apps.accounts.models import Tenant, User
from apps.core.utils.query_classifier import classify_query

class RAGTestSuite:
    """Comprehensive test suite for RAG system."""
    
    def __init__(self):
        """Initialize the test suite."""
        self.tenant = Tenant.objects.get(slug='stride')
        self.user = User.objects.first()
        self.service = UnifiedRAGService(self.tenant.slug, self.user)
        self.results = []
        
        print(f"🚀 RAG Test Suite Initialized")
        print(f"   Tenant: {self.tenant.name}")
        print(f"   User: {self.user.email}")
        print("=" * 80)
    
    def run_query_test(self, query: str, expected_type: str = None, min_citations: int = 0, 
                      complexity: str = "medium") -> Dict[str, Any]:
        """Run a single query test and return results."""
        print(f"\n🔍 Testing Query ({complexity.upper()}): '{query}'")
        print("-" * 60)
        
        start_time = time.time()
        
        try:
            # Test query classification
            classification = classify_query(query)
            print(f"📊 Query Classification: {classification['type']} (confidence: {classification['confidence']:.2f})")
            
            if expected_type and classification['type'] != expected_type:
                print(f"⚠️  Expected type: {expected_type}, got: {classification['type']}")
            
            # Perform search
            search_result, retrieved_docs = self.service.search(
                query_text=query,
                top_k=15,
                min_relevance_score=0.1  # Low threshold for comprehensive testing
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Analyze results
            citations = search_result.citations.all()
            citation_count = citations.count()
            
            print(f"⏱️  Search Duration: {duration:.2f}s")
            print(f"📊 Retrieved Documents: {len(retrieved_docs)}")
            print(f"🔗 Citations: {citation_count}")
            print(f"⭐ Average Score: {search_result.retriever_score_avg:.3f}")
            
            # Check citations quality
            working_citations = 0
            profile_citations = 0
            
            for i, citation in enumerate(citations[:5], 1):
                print(f"   Citation {i}:")
                print(f"     Score: {citation.relevance_score:.3f}")
                print(f"     Document: {citation.document_chunk.document.title[:60]}...")
                
                if citation.document_chunk.document.permalink:
                    working_citations += 1
                    print(f"     ✅ Link: {citation.document_chunk.document.permalink}")
                else:
                    print(f"     ❌ No link available")
                
                if citation.document_chunk.profile:
                    profile_citations += 1
                    print(f"     👤 Profile: {citation.document_chunk.profile.display_name}")
                else:
                    print(f"     ❌ No profile attribution")
            
            # Analyze answer quality
            answer = search_result.generated_answer
            answer_length = len(answer)
            
            print(f"📝 Answer Length: {answer_length} characters")
            
            # Check for structured formatting
            has_structure = any(tag in answer for tag in ['<h3>', '<div', '<ul>', '<li>'])
            print(f"🎨 Structured Formatting: {'✅' if has_structure else '❌'}")
            
            # Show answer preview
            preview = answer[:300].replace('\n', ' ')
            print(f"📄 Answer Preview: {preview}...")
            
            # Test result
            test_passed = (
                citation_count >= min_citations and
                working_citations > 0 and
                answer_length > 50 and
                duration < 120  # 2 minutes max
            )
            
            result = {
                'query': query,
                'complexity': complexity,
                'expected_type': expected_type,
                'actual_type': classification['type'],
                'confidence': classification['confidence'],
                'duration': duration,
                'retrieved_docs': len(retrieved_docs),
                'citations': citation_count,
                'working_citations': working_citations,
                'profile_citations': profile_citations,
                'answer_length': answer_length,
                'has_structure': has_structure,
                'avg_score': search_result.retriever_score_avg,
                'passed': test_passed
            }
            
            print(f"🎯 Test Result: {'✅ PASSED' if test_passed else '❌ FAILED'}")
            
            return result
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return {
                'query': query,
                'complexity': complexity,
                'error': str(e),
                'passed': False
            }
    
    def run_simple_tests(self):
        """Run simple, straightforward queries."""
        print(f"\n🔹 SIMPLE TESTS - Basic Information Retrieval")
        print("=" * 80)
        
        simple_queries = [
            ("What did Amanda say?", "person", 1),
            ("Show me messages from Rachel", "person", 1),
            ("What did Kapil mention?", "person", 1),
            ("Tell me about testing", "general", 1),
            ("What is discussed in the channel?", "general", 1),
        ]
        
        for query, expected_type, min_citations in simple_queries:
            result = self.run_query_test(query, expected_type, min_citations, "simple")
            self.results.append(result)
    
    def run_medium_tests(self):
        """Run medium complexity queries."""
        print(f"\n🔹 MEDIUM TESTS - Structured Information Extraction")
        print("=" * 80)
        
        medium_queries = [
            ("List issues reported by Amanda", "list_issues", 2),
            ("What problems did Rachel mention?", "list_issues", 1),
            ("Show me all bug reports", "list_issues", 1),
            ("What testing challenges were discussed?", "general", 1),
            ("Summarize deployment issues", "summarize_issues", 1),
        ]
        
        for query, expected_type, min_citations in medium_queries:
            result = self.run_query_test(query, expected_type, min_citations, "medium")
            self.results.append(result)
    
    def run_complex_tests(self):
        """Run complex, multi-faceted queries."""
        print(f"\n🔹 COMPLEX TESTS - Advanced Analysis and Synthesis")
        print("=" * 80)
        
        complex_queries = [
            ("Summarize all issues reported by Amanda and Rachel about testing and deployment", "summarize_issues", 3),
            ("What are the main challenges discussed by the product engineering team?", "analytical", 2),
            ("Compare the different perspectives on testing mentioned by team members", "analytical", 2),
            ("What patterns can you identify in the bug reports and feature discussions?", "analytical", 2),
            ("Analyze the team's approach to product development based on their conversations", "analytical", 3),
        ]
        
        for query, expected_type, min_citations in complex_queries:
            result = self.run_query_test(query, expected_type, min_citations, "complex")
            self.results.append(result)
    
    def run_edge_case_tests(self):
        """Run edge case and stress tests."""
        print(f"\n🔹 EDGE CASE TESTS - Boundary Conditions")
        print("=" * 80)
        
        edge_queries = [
            ("Tell me about something that doesn't exist in the data", "general", 0),
            ("What did a non-existent person say?", "person", 0),
            ("List all issues from 1990", "list_issues", 0),
            ("", "general", 0),  # Empty query
            ("a", "general", 0),  # Single character
        ]
        
        for query, expected_type, min_citations in edge_queries:
            if query:  # Skip empty query for now
                result = self.run_query_test(query, expected_type, min_citations, "edge_case")
                self.results.append(result)
    
    def generate_report(self):
        """Generate comprehensive test report."""
        print(f"\n📊 COMPREHENSIVE TEST REPORT")
        print("=" * 80)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.get('passed', False))
        failed_tests = total_tests - passed_tests
        
        print(f"📈 Overall Results:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"   Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        
        # Group by complexity
        by_complexity = {}
        for result in self.results:
            complexity = result.get('complexity', 'unknown')
            if complexity not in by_complexity:
                by_complexity[complexity] = {'total': 0, 'passed': 0}
            by_complexity[complexity]['total'] += 1
            if result.get('passed', False):
                by_complexity[complexity]['passed'] += 1
        
        print(f"\n📊 Results by Complexity:")
        for complexity, stats in by_complexity.items():
            total = stats['total']
            passed = stats['passed']
            print(f"   {complexity.title()}: {passed}/{total} ({passed/total*100:.1f}%)")
        
        # Performance metrics
        durations = [r.get('duration', 0) for r in self.results if 'duration' in r]
        if durations:
            avg_duration = sum(durations) / len(durations)
            max_duration = max(durations)
            print(f"\n⏱️  Performance Metrics:")
            print(f"   Average Duration: {avg_duration:.2f}s")
            print(f"   Max Duration: {max_duration:.2f}s")
        
        # Citation metrics
        citations = [r.get('citations', 0) for r in self.results if 'citations' in r]
        if citations:
            avg_citations = sum(citations) / len(citations)
            print(f"   Average Citations: {avg_citations:.1f}")
        
        # Failed tests details
        failed_results = [r for r in self.results if not r.get('passed', False)]
        if failed_results:
            print(f"\n❌ Failed Tests:")
            for result in failed_results:
                query = result.get('query', 'Unknown')
                error = result.get('error', 'Test criteria not met')
                print(f"   '{query}': {error}")
        
        # Success criteria
        success_rate = passed_tests / total_tests
        print(f"\n🎯 Production Readiness Assessment:")
        if success_rate >= 0.9:
            print(f"   ✅ EXCELLENT - System is production ready ({success_rate*100:.1f}% success)")
        elif success_rate >= 0.8:
            print(f"   ✅ GOOD - System is mostly ready ({success_rate*100:.1f}% success)")
        elif success_rate >= 0.7:
            print(f"   ⚠️  FAIR - System needs improvement ({success_rate*100:.1f}% success)")
        else:
            print(f"   ❌ POOR - System needs significant work ({success_rate*100:.1f}% success)")
    
    def run_all_tests(self):
        """Run the complete test suite."""
        print(f"🚀 Starting Comprehensive RAG Test Suite")
        print(f"Testing with real production data - No mocks, fallbacks, or hacks")
        print("=" * 80)
        
        start_time = time.time()
        
        self.run_simple_tests()
        self.run_medium_tests()
        self.run_complex_tests()
        self.run_edge_case_tests()
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        print(f"\n⏱️  Total Test Suite Duration: {total_duration:.2f}s")
        
        self.generate_report()

if __name__ == "__main__":
    test_suite = RAGTestSuite()
    test_suite.run_all_tests()
