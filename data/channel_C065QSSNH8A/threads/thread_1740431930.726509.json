{"thread_ts": "1740431930.726509", "channel_id": "C065QSSNH8A", "reply_count": 18, "replies": [{"ts": "1740461928.738659", "text": "We are almost done with addressing feedback. Will push the changes and update the data by tomorrow for feedback.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "YI91z", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We are almost done with addressing feedback. Will push the changes and update the data by tomorrow for feedback."}]}]}]}, {"ts": "1740462187.366979", "text": "cc: <@U07MH77PUBV>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "j4uwO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "cc: "}, {"type": "user", "user_id": "U07MH77PUBV"}]}]}]}, {"ts": "1740547180.129609", "text": "<@U07EJ2LP44S> Couple of questions with the data\n• We have only employer values. Should we show only employer contributions?\n• And the amounts are \"monthly\"/ \"weekly\"/ Half yearly  (looks like annual though just need confirmation? Do we need to show annual numbers or as is from the file?\n• Also should we include internet stipend as \"Other Cateogry?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "/K6j+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Couple of questions with the data\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We have only employer values. Should we show only employer contributions?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "And the amounts are \"monthly\"/ \"weekly\"/ Half yearly  (looks like annual though just need confirmation? Do we need to show annual numbers or as is from the file?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Also should we include internet stipend as \"Other Cateogry?"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1740568321.488139", "text": "<@U07EJ2LP44S> We have addressed the feedback and updated the ENV with data shared above. There are some minor nitpicks we are addressing but should be good to share with customer for initial feedback. We may have to update the data gain based on the answers to the questions above.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "subtype": "thread_broadcast", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "KyIvM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " We have addressed the feedback and updated the ENV with data shared above. There are some minor nitpicks we are addressing but should be good to share with customer for initial feedback. We may have to update the data gain based on the answers to the questions above."}]}]}]}, {"ts": "1740583685.540069", "text": "<PERSON>'s reply:\n\nYa<PERSON>! Looking really great! We are only showing employer contributions for the total rewards. They should all be an annual amount. Can you tell me where you are seeing the “monthly” and “weekly”?\nThe internet stipend can stay in Other yes!\n\nMy only remaining question here is the salary history. Is there any chance of making that a percentage?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "FR/KG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "<PERSON>'s reply:\n\nYa<PERSON>! Looking really great! We are only showing employer contributions for the total rewards. They should all be an annual amount. Can you tell me where you are seeing the “monthly” and “weekly”?\nThe internet stipend can stay in Other yes!\n\nMy only remaining question here is the salary history. Is there any chance of making that a percentage?"}]}]}]}, {"ts": "1740583977.537489", "text": "That was a question. If they are all annual amounts then we are good.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "ow0Bk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That was a question. If they are all annual amounts then we are good."}]}]}]}, {"ts": "1740584007.935109", "text": "Salary history we do show %increase in the card. Are they expecting in the timeline list items as well?", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "k+6V5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Salary history we do show %increase in the card. Are they expecting in the timeline list items as well?"}]}]}]}, {"ts": "1740584876.675819", "text": "", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740431930.726509", "files": [{"id": "F08F41334N9", "created": 1740584872, "timestamp": 1740584872, "name": "image.png", "title": "image.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 231357, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08F41334N9/image.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08F41334N9/download/image.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F41334N9-77dde5f25c/image_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F41334N9-77dde5f25c/image_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F41334N9-77dde5f25c/image_360.png", "thumb_360_w": 360, "thumb_360_h": 296, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F41334N9-77dde5f25c/image_480.png", "thumb_480_w": 480, "thumb_480_h": 394, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F41334N9-77dde5f25c/image_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F41334N9-77dde5f25c/image_720.png", "thumb_720_w": 720, "thumb_720_h": 591, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F41334N9-77dde5f25c/image_800.png", "thumb_800_w": 800, "thumb_800_h": 657, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F41334N9-77dde5f25c/image_960.png", "thumb_960_w": 960, "thumb_960_h": 788, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08F41334N9-77dde5f25c/image_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 841, "original_w": 1206, "original_h": 990, "thumb_tiny": "AwAnADC9sK5yxbJ/Kl5FPIHejaKAG5P+RRkjp/KnbRS9BQAzJ/yKUEk89PpTqKAA1HgehqQ0z/gVADlAxxS0i9OuaWgAooooADTcH0FONFACD3paSigBaKKKAP/Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08F41334N9/image.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08F41334N9-c677ee2a62", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}]}, {"ts": "1740585548.233529", "text": "there is some bug % doesn't show up. Will get it fixed.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "UqOla", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "there is some bug % doesn't show up. Will get it fixed."}]}]}]}, {"ts": "1740585728.782559", "text": "So the expectation is to show overall % increase overtime? this we have already covered but seems not working.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "files": [{"id": "F08EZBWRHSS", "created": 1740585721, "timestamp": 1740585721, "name": "Screenshot 2025-02-26 at 9.29.24 PM.png", "title": "Screenshot 2025-02-26 at 9.29.24 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U0690EB5JE5", "user_team": "T04DM97F1UM", "editable": false, "size": 436773, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F08EZBWRHSS/screenshot_2025-02-26_at_9.29.24___pm.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F08EZBWRHSS/download/screenshot_2025-02-26_at_9.29.24___pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EZBWRHSS-9d959a5110/screenshot_2025-02-26_at_9.29.24___pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EZBWRHSS-9d959a5110/screenshot_2025-02-26_at_9.29.24___pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EZBWRHSS-9d959a5110/screenshot_2025-02-26_at_9.29.24___pm_360.png", "thumb_360_w": 360, "thumb_360_h": 195, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EZBWRHSS-9d959a5110/screenshot_2025-02-26_at_9.29.24___pm_480.png", "thumb_480_w": 480, "thumb_480_h": 260, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EZBWRHSS-9d959a5110/screenshot_2025-02-26_at_9.29.24___pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EZBWRHSS-9d959a5110/screenshot_2025-02-26_at_9.29.24___pm_720.png", "thumb_720_w": 720, "thumb_720_h": 391, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EZBWRHSS-9d959a5110/screenshot_2025-02-26_at_9.29.24___pm_800.png", "thumb_800_w": 800, "thumb_800_h": 434, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EZBWRHSS-9d959a5110/screenshot_2025-02-26_at_9.29.24___pm_960.png", "thumb_960_w": 960, "thumb_960_h": 521, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F08EZBWRHSS-9d959a5110/screenshot_2025-02-26_at_9.29.24___pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 556, "original_w": 3350, "original_h": 1818, "thumb_tiny": "AwAaADDSxkcmkYArg0oORQeBQAgHGAfypcH1oHSlFABRRSd6ACg9KKKAAUtFFABSd6WigD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F08EZBWRHSS/screenshot_2025-02-26_at_9.29.24___pm.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F08EZBWRHSS-e4de47e224", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "6aeO2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So the expectation is to show overall % increase overtime? this we have already covered but seems not working."}]}]}]}, {"ts": "1740586178.818909", "text": "Yes I believe so. I think it's also very small and hard to see, so she may not have even noticed it. I didn't notice it at first either", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "X6zDk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes I believe so. I think it's also very small and hard to see, so she may not have even noticed it. I didn't notice it at first either"}]}]}]}, {"ts": "1740586222.669949", "text": "Can we call more attention to it visually? Larger font or in it's own card or something?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "JCU+5", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can we call more attention to it visually? Larger font or in it's own card or something?"}]}]}]}, {"ts": "1740586314.443999", "text": "We can make it little bold and larger font and left align on the top of the history timeline.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "1y+JU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can make it little bold and larger font and left align on the top of the history timeline."}]}]}]}, {"ts": "1740586418.435429", "text": "Yes I think that would help", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "IZuMp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes I think that would help"}]}]}]}, {"ts": "1740586483.205929", "text": "Can it also be formatted like: Total increase over time: xx%", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "LbhXr", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Can it also be formatted like: Total increase over time: xx%"}]}]}]}, {"ts": "1740586492.086519", "text": "It will make it clearer", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "y+Qvb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It will make it clearer"}]}]}]}, {"ts": "1740587068.520559", "text": "Yes will do", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "blocks": [{"type": "rich_text", "block_id": "yexi+", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes will do"}]}]}]}, {"ts": "1740673162.054169", "text": "<@U07EJ2LP44S> All the feedback is taken care.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1740431930.726509", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "2u3dj", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " All the feedback is taken care."}]}]}]}], "created_at": "2025-05-22T22:00:49.358980"}