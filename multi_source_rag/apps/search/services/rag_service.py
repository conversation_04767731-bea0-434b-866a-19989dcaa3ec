"""
Unified LlamaIndex RAG Service - Production Ready

This service completely replaces custom RAG logic with LlamaIndex end-to-end implementation.
It imports and uses the UnifiedRAGService for all operations.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple

from django.contrib.auth.models import User

from apps.search.services.unified_rag_service import UnifiedRAGService
from apps.core.utils.service_cache import get_cached_unified_rag_service

logger = logging.getLogger(__name__)


class RAGService:
    """
    LlamaIndex-based RAG Service Wrapper.

    This class provides backward compatibility while using the unified
    LlamaIndex RAG service under the hood.
    """

    def __init__(self, user: User, tenant_slug: Optional[str] = None):
        """
        Initialize the RAG service.

        Args:
            user: User making the request
            tenant_slug: Tenant slug to search in
        """
        self.user = user
        self.tenant_slug = tenant_slug

        # Get tenant from user profile if not provided
        if not self.tenant_slug and hasattr(user, "profile") and user.profile.tenant:
            self.tenant_slug = user.profile.tenant.slug
            self.tenant = user.profile.tenant
        else:
            # Get tenant from slug
            try:
                from apps.accounts.models import Tenant
                self.tenant = Tenant.objects.get(slug=self.tenant_slug)
            except Tenant.DoesNotExist:
                logger.error(f"Tenant with slug {self.tenant_slug} not found")
                self.tenant = None

        # Initialize the unified RAG service using cache
        if self.tenant_slug:
            # Use cached service to avoid expensive reinitialization
            self.unified_service = get_cached_unified_rag_service(
                tenant_slug=self.tenant_slug,
                user_id=user.id if user else None
            )
            logger.info(f"RAGService initialized with cached UnifiedRAGService for tenant: {self.tenant_slug}")
        else:
            self.unified_service = None
            logger.warning("RAGService initialized without tenant_slug - no unified service available")

    def search(
        self,
        query_text: str,
        top_k: int = 20,
        metadata_filter: Optional[Dict[str, Any]] = None,
        output_format: str = "text",
        min_relevance_score: float = 0.15,  # FIXED: Lowered from 0.4 to 0.15 for better recall
        use_hybrid_search: bool = True,
        use_context_aware: bool = True,
        use_query_expansion: bool = False,
        use_multi_step_reasoning: bool = False,
    ) -> Tuple[Any, List[Tuple[Any, float]]]:
        """
        Perform a search using the unified LlamaIndex RAG service.

        Args:
            query_text: Query text
            top_k: Number of results to return
            metadata_filter: Filter to apply to search results
            output_format: Output format ('text', 'json', 'markdown', 'table')
            min_relevance_score: Minimum relevance score for documents
            use_hybrid_search: Whether to use hybrid search (maintained for compatibility)
            use_context_aware: Whether to use context-aware search (maintained for compatibility)
            use_query_expansion: Whether to use query expansion (maintained for compatibility)
            use_multi_step_reasoning: Whether to use multi-step reasoning (maintained for compatibility)

        Returns:
            Tuple of (SearchResult, retrieved_documents)
        """
        if not self.unified_service:
            raise ValueError("Unified service not initialized. Tenant is required.")

        # Log advanced features being requested
        if use_query_expansion:
            logger.info("Query expansion requested - delegating to enhanced RAG service")
        if use_multi_step_reasoning:
            logger.info("Multi-step reasoning requested - delegating to enhanced RAG service")

        # Delegate to the unified RAG service with all advanced parameters
        return self.unified_service.search(
            query_text=query_text,
            top_k=top_k,
            metadata_filter=metadata_filter,
            output_format=output_format,
            min_relevance_score=min_relevance_score,
            use_query_expansion=use_query_expansion,
            use_multi_step_reasoning=use_multi_step_reasoning,
            use_hybrid_search=use_hybrid_search,
            use_context_aware=use_context_aware
        )

    def get_stats(self) -> Dict[str, Any]:
        """
        Get processing statistics from the unified service.

        Returns:
            Dictionary of processing statistics
        """
        if not self.unified_service:
            return {}

        return self.unified_service.get_stats()

    def get_system_stats(self) -> Dict[str, Any]:
        """
        Get system statistics (alias for get_stats for backward compatibility).

        Returns:
            Dictionary of processing statistics
        """
        return self.get_stats()
