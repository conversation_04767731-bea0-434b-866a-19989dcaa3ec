{"date": "2023-11-30", "channel_id": "C065QSSNH8A", "message_count": 6, "messages": [{"ts": "1701322487.383439", "text": "Thank you <@U0658EW4B8D> :moneybag:", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "yFpCl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thank you "}, {"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " "}, {"type": "emoji", "name": "moneybag", "unicode": "1f4b0"}]}]}]}, {"ts": "1701322413.276239", "text": "Alright team\n\n• Here is the finalized <https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0|Digital Assets Implementation Project Plan>\n• Here is the finalized <https://docs.google.com/spreadsheets/d/1v9GuZ_hLL7gztLiaecNW2yg9WnsMOw792bdyGUT8IpU/edit#gid=0|[Master] Implementation Project Plan Template>", "user": "U0658EW4B8D", "type": "message", "edited": {"user": "U0658EW4B8D", "ts": "1701322581.000000"}, "reactions": [{"name": "rocket", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F067YMCKUV8", "created": 1701322473, "timestamp": 1701322473, "name": "[Master] Implementation Project Plan", "title": "[Template] Implementation Project Plan", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 104064, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1v9GuZ_hLL7gztLiaecNW2yg9WnsMOw792bdyGUT8IpU", "external_url": "https://docs.google.com/spreadsheets/d/1v9GuZ_hLL7gztLiaecNW2yg9WnsMOw792bdyGUT8IpU/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1v9GuZ_hLL7gztLiaecNW2yg9WnsMOw792bdyGUT8IpU/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F067YMCKUV8-591058e91b/_master__implementation_project_plan_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "original_w": 1024, "original_h": 1449, "thumb_tiny": "AwAwACHSbdngj8abl/Vfypx60gFK4CZf1X8qUbs8kY+lHelouAtFFFMAooooAMc0UUUAFFFFABRRRQAUUUUAFFFFAH//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F067YMCKUV8/_master__implementation_project_plan", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}, {"id": "F067S2J5BF0", "created": 1701322474, "timestamp": 1701322474, "name": "Digital Assets Implementation Project Plan", "title": "Digital Asset Implementation Project Plan", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U0658EW4B8D", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE", "external_url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "url_private": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F067S2J5BF0-e343e50eb7/digital_assets_implementation_project_plan_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSbdnjH403L+q/lTj1popXAMv6r+VKN2eSMfSkHWnUXAWiiimAUUUUAGOaKKKACiiigAooooAKKKKACiiigD//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F067S2J5BF0/digital_assets_implementation_project_plan", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "OCUQo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Alright team\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the finalized "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1-mMBQmculMF7pcUJCc2rEceORjpHfqAnsqJbAAWT5YE/edit#gid=0", "text": "Digital Assets Implementation Project Plan"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Here is the finalized "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1v9GuZ_hLL7gztLiaecNW2yg9WnsMOw792bdyGUT8IpU/edit#gid=0", "text": "[Master] Implementation Project Plan Template"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1701322291.025799", "text": "His key goal would be to generate $500K in 2024 and bring in 40 new logos.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "K05eC", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "His key goal would be to generate $500K in 2024 and bring in 40 new logos."}]}]}]}, {"ts": "1701322204.269929", "text": "Update: Wohooo!<PERSON> has signed the offer letter :tada:. His start date is Jan 3. Here is his Linkedln <https://www.linkedin.com/in/wesley-yarber/>\n\nPlease send him a connection request on your linkedln. Also it would be a great gesture to text him at ‭(<tel:919)210-0496|*************>‬ introducing yourself and send him a welcome message. We want to make him feel we are super excited about him joining the team.\n\nPlease confirm with a check mark after you have sent him the welcome message. :blush:", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "raised_hands", "users": ["U0658EW4B8D", "U04DKEFP1K8"], "count": 2}, {"name": "white_check_mark", "users": ["U065H3M6WJV"], "count": 1}], "attachments": [{"from_url": "https://www.linkedin.com/in/wesley-yarber/", "service_icon": "https://static.licdn.com/aero-v1/sc/h/al2o9zrvru7aqj8e1x2rzsrca", "id": 1, "original_url": "https://www.linkedin.com/in/wesley-yarber/", "fallback": "<PERSON> Con<PERSON>u | LinkedIn", "text": "View Wesley Y.’s profile on LinkedIn, the world’s largest professional community. <PERSON> has 8 jobs listed on their profile. See the complete profile on LinkedIn and discover <PERSON>’s connections and jobs at similar companies.", "title": "<PERSON> Con<PERSON>u | LinkedIn", "title_link": "https://www.linkedin.com/in/wesley-yarber/", "service_name": "linkedin.com"}], "blocks": [{"type": "rich_text", "block_id": "15fBN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Update: Wohooo!<PERSON> has signed the offer letter "}, {"type": "emoji", "name": "tada", "unicode": "1f389"}, {"type": "text", "text": ". His start date is Jan 3. Here is his Linkedln "}, {"type": "link", "url": "https://www.linkedin.com/in/wesley-yarber/"}, {"type": "text", "text": "\n\nPlease send him a connection request on your linkedln. Also it would be a great gesture to text him at ‭(*************‬ introducing yourself and send him a welcome message. We want to make him feel we are super excited about him joining the team.\n\nPlease confirm with a check mark after you have sent him the welcome message. "}, {"type": "emoji", "name": "blush", "unicode": "1f60a"}]}]}]}, {"ts": "1701296151.602419", "text": "<@U0658EW4B8D> I know we won't have <PERSON><PERSON><PERSON><PERSON> in today's working session. I think it'd be great to try and hit these topics this afternoon with your help:\n• DA project plan - share your initial thoughts &amp; any questions we need to chase down\n• Handling OTE in merit &amp; org view\n• Prioritizing items from the Front RFP spreadsheet (we'll walk through this live)", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "lTPDz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0658EW4B8D"}, {"type": "text", "text": " I know we won't have <PERSON><PERSON><PERSON><PERSON> in today's working session. I think it'd be great to try and hit these topics this afternoon with your help:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "DA project plan - share your initial thoughts & any questions we need to chase down"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Handling OTE in merit & org view"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Prioritizing items from the Front RFP spreadsheet (we'll walk through this live)"}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1701294534.997319", "text": "<!here> Exciting update. We just got a verbal acceptance from a solid candidate for the founding sales role with a start date of Jan 3.. He is based out of Raleigh.", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "allclap", "users": ["U065H3M6WJV", "U0658EW4B8D"], "count": 2}, {"name": "christmas_parrot", "users": ["U065H3M6WJV"], "count": 1}, {"name": "tada", "users": ["U04DKEFP1K8"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "ZYmZ/", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " Exciting update. We just got a verbal acceptance from a solid candidate for the founding sales role with a start date of Jan 3.. He is based out of Raleigh."}]}]}]}], "created_at": "2025-05-22T21:35:34.574553"}