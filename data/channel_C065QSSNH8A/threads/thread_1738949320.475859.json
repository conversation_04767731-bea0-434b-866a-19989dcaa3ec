{"thread_ts": "1738949320.475859", "channel_id": "C065QSSNH8A", "reply_count": 2, "replies": [{"ts": "1738950503.965269", "text": "Also <PERSON>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1738949320.475859", "blocks": [{"type": "rich_text", "block_id": "HJjJc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Also <PERSON>"}]}]}]}, {"ts": "1739196399.368199", "text": "<@U07EJ2LP44S> I have mitigated this. Fixing this needs a product discussion. Basically we had recently made a change in product to disable system calculated numbers to be updated to allocate page if there was any manual edits made  if you remember and this check stops new managers being added as that affects the budgets as well. For now I enabled auto update budgets and then added the new managers and updated the new budget numbers shared in another thread. You will have to let me know if any new recommenders needs to be added until we fix this.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1738949320.475859", "blocks": [{"type": "rich_text", "block_id": "wfeS/", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " I have mitigated this. Fixing this needs a product discussion. Basically we had recently made a change in product to disable system calculated numbers to be updated to allocate page if there was any manual edits made  if you remember and this check stops new managers being added as that affects the budgets as well. For now I enabled auto update budgets and then added the new managers and updated the new budget numbers shared in another thread. You will have to let me know if any new recommenders needs to be added until we fix this."}]}]}]}], "created_at": "2025-05-22T22:00:49.342475"}