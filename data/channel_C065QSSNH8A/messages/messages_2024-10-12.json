{"date": "2024-10-12", "channel_id": "C065QSSNH8A", "message_count": 1, "messages": [{"ts": "1728686593.693509", "text": "<@U07NBMXTL1E> <@U07M6QKHUC9> all the tech pave use on their website to track leads etc are listed here <https://builtwith.com/pave.com> ( sharing for infor only)", "user": "U04DKEFP1K8", "type": "message", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "attachments": [{"from_url": "https://builtwith.com/pave.com", "service_icon": "https://d28rh9vvmrd65v.cloudfront.net/img/apple-touch-icon.png", "thumb_url": "https://d28rh9vvmrd65v.cloudfront.net/img/logo/logoSquare.png", "thumb_width": 400, "thumb_height": 400, "id": 1, "original_url": "https://builtwith.com/pave.com", "fallback": "BuiltWith: PAVE.COM Technology Profile on BuiltWith", "text": "Web technologies <http://PAVE.COM|PAVE.COM> is using on their website.", "title": "PAVE.COM Technology Profile on BuiltWith", "title_link": "https://builtwith.com/pave.com", "service_name": "BuiltWith"}], "blocks": [{"type": "rich_text", "block_id": "d1IOe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07NBMXTL1E"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " all the tech pave use on their website to track leads etc are listed here "}, {"type": "link", "url": "https://builtwith.com/pave.com"}, {"type": "text", "text": " ( sharing for infor only)"}]}]}]}], "created_at": "2025-05-22T21:35:34.648749"}