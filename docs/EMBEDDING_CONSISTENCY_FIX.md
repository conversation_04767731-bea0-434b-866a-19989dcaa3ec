# Embedding Model Consistency Fix

## Problem Description

**CRITICAL BUG**: The RAG system was suffering from embedding model inconsistency, where different parts of the system used different embedding models with different vector dimensions. This completely breaks vector similarity search functionality.

### The Issue

```python
# Before Fix - BROKEN CODE:
# In ingestion service:
Settings.embed_model = HuggingFaceEmbedding()  # 384 dimensions

# In search service:  
Settings.embed_model = GeminiEmbedding()       # 768 dimensions

# Result: Vector search completely broken!
```

**Root Cause**: Multiple places in the codebase were setting `Settings.embed_model` to different embedding models:

1. **UnifiedRAGService** (line 127): Forced HuggingFace embedding (384d)
2. **LlamaIndex Init** (line 229): Set from registry (could be Gemini 768d)
3. **Gemini LLM**: Initialized Gemini embedding (768d) first
4. **Content Embedding Function**: Switched between models based on content type

## Solution Overview

Created a **centralized embedding consistency manager** that ensures the entire RAG system uses exactly the same embedding model with consistent dimensions.

### Key Components

1. **`apps/core/utils/embedding_consistency.py`** - Central consistency manager
2. **Updated services** to use consistent embedding model
3. **Validation system** to detect and prevent inconsistencies
4. **Configuration-based model selection** with proper fallbacks

## Implementation Details

### 1. Embedding Consistency Manager

**File**: `apps/core/utils/embedding_consistency.py`

**Key Functions**:
- `get_consistent_embedding_model()` - Single source of truth for embedding model
- `get_embedding_dimensions()` - Consistent dimension information
- `set_global_embedding_model()` - Sets LlamaIndex global settings
- `validate_embedding_consistency()` - Validates system consistency
- `get_embedding_model_info()` - Model configuration information

**Configuration Priority**:
1. Environment variables (`EMBEDDING_MODEL_NAME`, `USE_GEMINI_EMBEDDING`)
2. Django settings
3. Default fallback (HuggingFace all-MiniLM-L6-v2, 384d)

### 2. Fixed Services

#### UnifiedRAGService
**Before**:
```python
# Hardcoded HuggingFace embedding
hf_embedding = HuggingFaceEmbedding(model_name="sentence-transformers/all-MiniLM-L6-v2")
Settings.embed_model = hf_embedding
```

**After**:
```python
# Use consistent embedding model
from apps.core.utils.embedding_consistency import set_global_embedding_model, validate_embedding_consistency
set_global_embedding_model()
if not validate_embedding_consistency():
    raise RuntimeError("Embedding model inconsistency detected")
```

#### Ingestion Service
**Before**:
```python
# Hardcoded metadata
model_name="sentence-transformers/all-MiniLM-L6-v2"
vector_dimensions=384
```

**After**:
```python
# Dynamic consistent metadata
from apps.core.utils.embedding_consistency import get_embedding_model_info
model_info = get_embedding_model_info()
model_name=model_info.get("model_name", "sentence-transformers/all-MiniLM-L6-v2")
vector_dimensions=model_info.get("dimensions", 384)
```

#### Content Embedding Function
**Before**:
```python
# Different models for different content types
if is_gemini_available():
    return get_gemini_embedding()  # 768d
else:
    return HuggingFaceEmbedding()  # 384d
```

**After**:
```python
# Always consistent model
from apps.core.utils.embedding_consistency import get_consistent_embedding_model
return get_consistent_embedding_model()  # Always same dimensions
```

### 3. Configuration Options

#### Environment Variables
```bash
# Use Gemini embedding (requires API key)
export USE_GEMINI_EMBEDDING=true
export GEMINI_API_KEY=your_api_key

# Use specific HuggingFace model
export EMBEDDING_MODEL_NAME=sentence-transformers/all-mpnet-base-v2
```

#### Django Settings
```python
# In settings.py
EMBEDDING_MODEL_NAME = "sentence-transformers/all-MiniLM-L6-v2"
USE_GEMINI_EMBEDDING = False
GEMINI_API_KEY = "your_api_key"
GEMINI_EMBEDDING_MODEL = "embedding-001"
```

## Validation and Testing

### Validation Script
Run the validation script to ensure consistency:

```bash
cd /Users/<USER>/Desktop/RAGSearch
python scripts/validate_embedding_consistency.py
```

**Tests Performed**:
1. ✅ Consistency Manager functionality
2. ✅ Global LlamaIndex Settings consistency
3. ✅ RAG Service embedding model
4. ✅ Ingestion Service embedding model
5. ✅ Content embedding function consistency
6. ✅ Vector dimensions consistency

### Expected Output
```
🎉 EMBEDDING CONSISTENCY FIX SUCCESSFUL!
   All components are using consistent embedding model:
   Model: sentence-transformers/all-MiniLM-L6-v2
   Type: HuggingFaceEmbedding
   Dimensions: 384
```

## Benefits

### 1. **Fixed Vector Search**
- All vectors now have consistent dimensions
- Similarity search works correctly
- No more dimension mismatch errors

### 2. **Centralized Configuration**
- Single source of truth for embedding model
- Easy to change model across entire system
- Environment-based configuration

### 3. **Automatic Validation**
- Built-in consistency checking
- Early detection of configuration issues
- Prevents silent failures

### 4. **Flexible Model Selection**
- Support for both Gemini and HuggingFace models
- Automatic fallback mechanisms
- Easy to add new embedding models

## Migration Guide

### For Existing Data

If you have existing data with inconsistent embeddings:

1. **Clean vector database**:
```bash
python -c "
import qdrant_client
client = qdrant_client.QdrantClient(host='localhost', port=6333)
collections = client.get_collections().collections
for collection in collections:
    client.delete_collection(collection.name)
"
```

2. **Re-ingest all data** with consistent embeddings:
```bash
python manage.py shell -c "
from apps.documents.services.ingestion_service import IngestionService
# Re-ingest all your data sources
"
```

### For New Deployments

1. **Set environment variables** for your preferred embedding model
2. **Run validation script** to ensure consistency
3. **Ingest data** - will automatically use consistent embeddings

## Monitoring

### Health Checks
Add to your monitoring:

```python
from apps.core.utils.embedding_consistency import validate_embedding_consistency

def health_check():
    if not validate_embedding_consistency():
        raise Exception("Embedding model inconsistency detected!")
```

### Logging
The system now logs embedding model information:
```
INFO - Initialized consistent Gemini embedding: embedding-001 (768d)
INFO - Set global LlamaIndex embedding model: GeminiEmbedding (768d)
INFO - Embedding model consistency validated: GeminiEmbedding (768d)
```

## Troubleshooting

### Common Issues

1. **"Embedding model inconsistency detected"**
   - Run validation script to identify the issue
   - Check environment variables and Django settings
   - Ensure all services are restarted after configuration changes

2. **"Failed to initialize Gemini embedding"**
   - Check GEMINI_API_KEY is set correctly
   - Verify API key has proper permissions
   - System will automatically fall back to HuggingFace

3. **Vector search still not working**
   - Clean vector database and re-ingest data
   - Verify all existing vectors use consistent dimensions
   - Check Qdrant collection configuration

### Debug Commands

```bash
# Check current model configuration
python manage.py shell -c "
from apps.core.utils.embedding_consistency import get_embedding_model_info
print(get_embedding_model_info())
"

# Validate consistency
python scripts/validate_embedding_consistency.py

# Reset and reinitialize
python manage.py shell -c "
from apps.core.utils.embedding_consistency import reset_embedding_model, set_global_embedding_model
reset_embedding_model()
set_global_embedding_model()
"
```

## Conclusion

This fix ensures that the RAG system maintains embedding model consistency across all components, preventing the critical dimension mismatch bug that was breaking vector similarity search. The solution is robust, configurable, and includes comprehensive validation to prevent future issues.
