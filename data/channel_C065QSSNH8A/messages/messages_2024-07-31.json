{"date": "2024-07-31", "channel_id": "C065QSSNH8A", "message_count": 14, "messages": [{"ts": "1722447757.077989", "text": "That was definitely a bug; not seeing the whole column option box", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1722447757.077989", "reply_count": 6, "blocks": [{"type": "rich_text", "block_id": "uC3fT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That was definitely a bug; not seeing the whole column option box"}]}]}]}, {"ts": "1722447618.751739", "text": "<@U04DS2MBWP4> Are you fine staging ENV being little slow or down for like 30mnts? I can deploy now or will do my morning.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1722447618.751739", "reply_count": 3, "blocks": [{"type": "rich_text", "block_id": "QHIiw", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " Are you fine staging ENV being little slow or down for like 30mnts? I can deploy now or will do my morning."}]}]}]}, {"ts": "**********.170989", "text": "There are some fixes done today. Let me push those changes to staging.", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "BfMC8", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There are some fixes done today. Let me push those changes to staging."}]}]}]}, {"ts": "**********.090299", "text": "<@U0690EB5JE5> I tried up edit the employee data in the demo account and I am getting \"Failed to update error\"", "user": "U04DS2MBWP4", "type": "message", "files": [{"id": "F07EXSF0Y7M", "created": **********, "timestamp": **********, "name": "Screenshot 2024-07-31 at 10.30.25 AM.png", "title": "Screenshot 2024-07-31 at 10.30.25 AM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U04DS2MBWP4", "user_team": "T04DM97F1UM", "editable": false, "size": 276813, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F07EXSF0Y7M/screenshot_2024-07-31_at_10.30.25___am.png", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F07EXSF0Y7M/download/screenshot_2024-07-31_at_10.30.25___am.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_360.png", "thumb_360_w": 354, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_480.png", "thumb_480_w": 472, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_720.png", "thumb_720_w": 709, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_800.png", "thumb_800_w": 800, "thumb_800_h": 813, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_960.png", "thumb_960_w": 945, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F07EXSF0Y7M-bac004b544/screenshot_2024-07-31_at_10.30.25___am_1024.png", "thumb_1024_w": 1008, "thumb_1024_h": 1024, "original_w": 1860, "original_h": 1890, "thumb_tiny": "AwAwAC+3EiuDn+dPES91P50y2GEI96sAYoAjEKeh/OjyU9D+dSUmT3zQBG0SBSQOg9ar1bf7jfSqQUhCKALMHQ/Wpqgg+6frU9AAelNBPv8AlTjSD2GKAEf7jfSqlW3+430qoaAJoPun61PUEDAA5IH1qXev94fnQA49KQU15ML8u1j6E4qPzZP7if8AfdOwEr/cb6VUNTea5BDKoGDyGzUJoA//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F07EXSF0Y7M/screenshot_2024-07-31_at_10.30.25___am.png", "permalink_public": "https://slack-files.com/T04DM97F1UM-F07EXSF0Y7M-56b4ae2ae5", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "WU++H", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I tried up edit the employee data in the demo account and I am getting \"Failed to update error\""}]}]}]}, {"ts": "**********.340439", "text": "<!here> folks i have some unplanned stuff to take care at home and i will be unavailable between 9am - 10am PT", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "giNGD", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here"}, {"type": "text", "text": " folks i have some unplanned stuff to take care at home and i will be unavailable between 9am - 10am PT"}]}]}]}, {"ts": "**********.509049", "text": "<https://us06web.zoom.us/j/**********?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09>", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R07EGTA93HD", "block_id": "H88hO", "api_decoration_available": false, "call": {"v1": {"id": "R07EGTA93HD", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1722441902, "active_participants": [], "all_participants": [], "display_id": "925-480-7019", "join_url": "https://us06web.zoom.us/j/**********?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1722534684, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/**********?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "2HwXU", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://us06web.zoom.us/j/**********?pwd=bjVlQVUrZFErY010L3hHZ3F6ODRydz09"}]}]}]}, {"ts": "1722441891.100599", "text": "I am waiting for <PERSON><PERSON><PERSON><PERSON> to join. We can meet on my zoom", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "W6mrP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am waiting for <PERSON><PERSON><PERSON><PERSON> to join. We can meet on my zoom"}]}]}]}, {"ts": "1722441869.879179", "text": "is meeting started?", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "MQa6A", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "is meeting started?"}]}]}]}, {"ts": "1722439201.496399", "text": "*<!here>* Here is the agenda for today's eng meeting. Feel free to add other agenda items:\nPrep for call with <PERSON> (mostly updating <PERSON><PERSON><PERSON> on the yesterday's discussion with <PERSON> and <PERSON><PERSON><PERSON><PERSON>)\nComp Builder Requirements Doc\nIntegrations", "user": "U04DS2MBWP4", "type": "message", "reactions": [{"name": "+1", "users": ["U0690EB5JE5"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "Ruid+", "elements": [{"type": "rich_text_section", "elements": [{"type": "broadcast", "range": "here", "style": {"bold": true}}, {"type": "text", "text": " Here is the agenda for today's eng meeting. Feel free to add other agenda items:\nPrep for call with <PERSON> (mostly updating <PERSON><PERSON><PERSON> on the yesterday's discussion with <PERSON> and <PERSON><PERSON><PERSON><PERSON>)\nComp Builder Requirements Doc\nIntegrations"}]}]}]}, {"ts": "**********.936369", "text": "Morning team! I have a conflict for todays engineering call; a class I scheduled several weeks ago. Sorry to miss, or if you think it's a critical call let me know.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.936369", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "dikj3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Morning team! I have a conflict for todays engineering call; a class I scheduled several weeks ago. Sorry to miss, or if you think it's a critical call let me know."}]}]}]}, {"ts": "**********.872819", "text": "<@U04DKEFP1K8> I am not able to sign into Atlassian to approve <PERSON>'s request to join. Do I still have atlassian account?", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "**********.872819", "reply_count": 5, "blocks": [{"type": "rich_text", "block_id": "QjoxP", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " I am not able to sign into Atlassian to approve <PERSON>'s request to join. Do I still have atlassian account?"}]}]}]}, {"ts": "**********.013519", "text": "I am on call with <PERSON>. Feel free to join this one", "user": "U04DS2MBWP4", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Xr0/V", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am on call with <PERSON>. Feel free to join this one"}]}]}]}, {"ts": "**********.658449", "text": "<https://us06web.zoom.us/j/***********?pwd=et7QrrCbefXeCZpe1964nskETrAtKm.1>", "user": "U04DS2MBWP4", "type": "message", "attachments": [{"id": 1, "blocks": [{"type": "call", "call_id": "R07F5AA9BCZ", "block_id": "DmcLW", "api_decoration_available": false, "call": {"v1": {"id": "R07F5AA9BCZ", "app_id": "A5GE9BMQC", "app_icon_urls": {"image_32": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_32.png", "image_36": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_36.png", "image_48": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_48.png", "image_64": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_64.png", "image_72": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_72.png", "image_96": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_96.png", "image_128": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_128.png", "image_192": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_192.png", "image_512": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_512.png", "image_1024": "https://slack-files2.s3-us-west-2.amazonaws.com/avatars/2017-09-21/245295805989_06e77af1bfb8e3c81d4d_1024.png"}, "date_start": 1722373340, "active_participants": [], "all_participants": [], "display_id": "814-4468-9654", "join_url": "https://us06web.zoom.us/j/***********?pwd=et7QrrCbefXeCZpe1964nskETrAtKm.1", "name": "Zoom meeting", "created_by": "U05185RFCNT", "date_end": 1722491437, "channels": ["C065QSSNH8A"], "is_dm_call": false, "was_rejected": false, "was_missed": false, "was_accepted": false, "has_ended": true}, "media_backend_type": "platform_call"}}], "fallback": "[no preview available]", "bot_id": "B051KP08BRB", "app_unfurl_url": "https://us06web.zoom.us/j/***********?pwd=et7QrrCbefXeCZpe1964nskETrAtKm.1", "is_app_unfurl": true, "app_id": "A5GE9BMQC"}], "blocks": [{"type": "rich_text", "block_id": "f2zDB", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://us06web.zoom.us/j/***********?pwd=et7QrrCbefXeCZpe1964nskETrAtKm.1"}]}]}]}, {"ts": "1722373312.296889", "text": "<@U04DS2MBWP4> ping when you start the implementation meeting , i see you have another meeting in progress", "user": "U04DKEFP1K8", "type": "message", "blocks": [{"type": "rich_text", "block_id": "fUwPi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " ping when you start the implementation meeting , i see you have another meeting in progress"}]}]}]}], "created_at": "2025-05-22T21:35:34.634614"}