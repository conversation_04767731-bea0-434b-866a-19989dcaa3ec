{"date": "2024-12-04", "channel_id": "C065QSSNH8A", "message_count": 9, "messages": [{"ts": "1733309781.110229", "text": "<@U07EJ2LP44S> This issue happens when we publish cycle and this action overwrites new compensation band. We need to upload promotions again and should fix the data. I have fixed cycle publish code to not overwrite the new comp band.\n\nFor DGOC. I did manaully re-assigned the new title to fix the issue for case where the bands were overwritten. But I found couple of titles as missing in bands now. I need to clarify with you.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733249004.413709", "subtype": "thread_broadcast", "edited": {"user": "U0690EB5JE5", "ts": "1733310349.000000"}, "blocks": [{"type": "rich_text", "block_id": "fUUln", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " This issue happens when we publish cycle and this action overwrites new compensation band. We need to upload promotions again and should fix the data. I have fixed cycle publish code to not overwrite the new comp band.\n\nFor DGOC. I did manaully re-assigned the new title to fix the issue for case where the bands were overwritten. But I found couple of titles as missing in bands now. I need to clarify with you."}]}]}]}, {"ts": "1733309484.829279", "text": "<@U07EJ2LP44S> Unfortunately the upload improvements are not yet available on Curana. you still have to upload full file. only div energy has those improvements as the bonus changes were done on top of those improvements code. Will be pushing the upload improvements to all ENVs by end of this week.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733263534.226799", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "M4V12", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Unfortunately the upload improvements are not yet available on Curana. you still have to upload full file. only div energy has those improvements as the bonus changes were done on top of those improvements code. Will be pushing the upload improvements to all ENVs by end of this week."}]}]}]}, {"ts": "1733277319.912079", "text": "Will into all the issues today ", "user": "U0690EB5JE5", "type": "message", "blocks": [{"type": "rich_text", "block_id": "1zMoA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Will into all the issues today "}]}]}]}, {"ts": "1733263534.226799", "text": "<@U0690EB5JE5> I cannot get an upload of Curana's bonus amounts to take. It says Internal Error, reason being Column 1, field name. I've tried it three different ways (including how it downloaded) but it keeps giving an error. Can you try?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733263534.226799", "reply_count": 7, "files": [{"id": "F0846NM9LQY", "created": 1733263531, "timestamp": 1733263531, "name": "CuranaPercents.csv", "title": "CuranaPercents.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": true, "size": 10573, "mode": "snippet", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F0846NM9LQY/curanapercents.csv", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F0846NM9LQY/download/curanapercents.csv", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F0846NM9LQY/curanapercents.csv", "permalink_public": "https://slack-files.com/T04DM97F1UM-F0846NM9LQY-3727b1b430", "edit_link": "https://stride-hr.slack.com/files/U08QENR4TNY/F0846NM9LQY/curanapercents.csv/edit", "preview": "Employee Id,Employee Name (Read Only),Annual Salary Currency,Annual Salary (Currency),Annual Salary-Other (Currency),Variable Pay Currency,Variable Pay (%),Variable Pay (Currency),Target Bonus Currency,Target Bonus (%),Target Bonus (%),Last Raise Date,Previous Year Salary,Annual Salary OTE (Currency),Pay Mix,Hourly Rate,\"Update Type (NC,A,D,U)\"\r\n8093,TARIQ SYED,USD,141000.08,0,USD,0,0,USD,10,,45374,0,0,0,0,U\r\n8038,VICTOR SONE,USD,190289.93,0,USD,0,0,USD,20,,45374,0,0,0,0,U\r\n8032,SHARRA BLACKWELL,USD,100891.18,0,USD,0,0,USD,10,,45374,0,0,0,0,U\r\n8024,SHANICE HAIRSTON,USD,45302.4,0,USD,0,0,USD,8,,45374,0,0,0,21.78,U\r\n4412,STEPHANIE ST THOMAS,USD,250000,0,USD,0,0,USD,20,,,0,0,0,0,U\r\n4167,DEVON HOERNSCHEMEYER,USD,95000,0,USD,0,0,USD,10,,,0,0,0,0,U\r\n4142,LINDA BROCK,USD,185000,0,USD,0,0,USD,15,,,0,0,0,0,U\r\n3917,DANIEL BALK,USD,175000,0,USD,0,0,USD,25,,,0,0,0,0,U\r\n3673,MARJORIE WEINER,USD,185000,0,USD,0,0,USD,20,,,0,0,0,0,U\r", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer cm-csv-tsv-wrapper\">\n<div class=\"CodeMirror-code\">\n<div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col\">Employee Id</div><div class=\"cm-col\">Employee Name (Read Only)</div><div class=\"cm-col\">Annual Salary Currency</div><div class=\"cm-col\">Annual Salary (Currency)</div><div class=\"cm-col\">Annual Salary-Other (Currency)</div><div class=\"cm-col\">Variable Pay Currency</div><div class=\"cm-col\">Variable Pay (%)</div><div class=\"cm-col\">Variable Pay (Currency)</div><div class=\"cm-col\">Target Bonus Currency</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Target Bonus (%)</div><div class=\"cm-col\">Last Raise Date</div><div class=\"cm-col\">Previous Year Salary</div><div class=\"cm-col\">Annual Salary OTE (Currency)</div><div class=\"cm-col\">Pay Mix</div><div class=\"cm-col\">Hourly Rate</div><div class=\"cm-col\">Update Type (NC,A,D,U)</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">8093</div><div class=\"cm-col\">TARIQ SYED</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">141000.08</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">45374</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">8038</div><div class=\"cm-col\">VICTOR SONE</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">190289.93</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">20</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">45374</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">8032</div><div class=\"cm-col\">SHARRA BLACKWELL</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">100891.18</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">45374</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">8024</div><div class=\"cm-col\">SHANICE HAIRSTON</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">45302.4</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">8</div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">45374</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">21.78</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">4412</div><div class=\"cm-col\">STEPHANIE ST THOMAS</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">250000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">20</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">4167</div><div class=\"cm-col\">DEVON HOERNSCHEMEYER</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">95000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">10</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">4142</div><div class=\"cm-col\">LINDA BROCK</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">185000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">15</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">3917</div><div class=\"cm-col\">DANIEL BALK</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">175000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">25</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div><div class=\"cm-row cm-hide-line-num\"><div class=\"cm-col cm-num\">3673</div><div class=\"cm-col\">MARJORIE WEINER</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">185000</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">USD</div><div class=\"cm-col cm-num\">20</div><div class=\"cm-col\"></div><div class=\"cm-col\"></div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col cm-num\">0</div><div class=\"cm-col\">U</div></div></div>\n</div>\n", "lines": 158, "lines_more": 148, "preview_is_truncated": true, "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "7G/CM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " I cannot get an upload of <PERSON><PERSON><PERSON>'s bonus amounts to take. It says Internal Error, reason being Column 1, field name. I've tried it three different ways (including how it downloaded) but it keeps giving an error. Can you try?"}]}]}]}, {"ts": "1733261751.843659", "text": "We can discuss if needed in tomorrows call, but here's the info for Curana's bonus:\n\n• Only certain people eligible for bonus plan (Management Incentive Plan)\n• Budget is accrued for full payout (1.2mm as an example)\n• Target earned is across the company (eg, company achieves 95%, everyone is eligible for 95% of their dollars.\n• Allocation of bonus is at manager discretion; there is no official performance component. However a manger may take away from one low performing employee to give to a higher performer. The leadership team (CSuite) have discretion if there is additional budget accrued but not spent: eg: if company achieves 95% but budget is accrued for 100%, CSuite may decide to give the additional 5% to high performers, OR to employees who are not on the bonus plan, as a spot bonus. \n• Can overperform (could reach 110%), not capped at 100%. But there is a $ budget amount for the company. ", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733261751.843659", "reply_count": 2, "blocks": [{"type": "rich_text", "block_id": "fF2qg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can discuss if needed in tomorrows call, but here's the info for <PERSON><PERSON><PERSON>'s bonus:\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Only certain people eligible for bonus plan (Management Incentive Plan)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Budget is accrued for full payout (1.2mm as an example)"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Target earned is across the company (eg, company achieves 95%, everyone is eligible for 95% of their dollars."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Allocation of bonus is at manager discretion; there is no official performance component. However a manger may take away from one low performing employee to give to a higher performer. The leadership team (CSuite) have discretion if there is additional budget accrued but not spent: eg: if company achieves 95% but budget is accrued for 100%, CSuite may decide to give the additional 5% to high performers, OR to employees who are not on the bonus plan, as a spot bonus. "}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Can overperform (could reach 110%), not capped at 100%. But there is a $ budget amount for the company. "}]}], "style": "bullet", "indent": 0, "border": 0}]}]}, {"ts": "1733260829.506769", "text": "First draft of the doc I've been workign on: <https://docs.google.com/document/d/1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk/edit?usp=sharing>", "user": "U07EJ2LP44S", "type": "message", "files": [{"id": "F083HN97AG2", "created": 1733260834, "timestamp": 1733260834, "name": "UX Product Recommendations ", "title": "UI/UX Product Recommendations", "mimetype": "application/vnd.google-apps.document", "filetype": "gdoc", "pretty_type": "Google Docs", "user": "U07EJ2LP44S", "user_team": "T04DM97F1UM", "editable": false, "size": 136275, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk", "external_url": "https://docs.google.com/document/d/1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk/edit?usp=sharing", "url_private": "https://docs.google.com/document/d/1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__360.png", "thumb_360_w": 277, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__480.png", "thumb_480_w": 369, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__720.png", "thumb_720_w": 554, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__800.png", "thumb_800_w": 800, "thumb_800_h": 1040, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__960.png", "thumb_960_w": 738, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F083HN97AG2-7613bb8de9/ux_product_recommendations__1024.png", "thumb_1024_w": 788, "thumb_1024_h": 1024, "original_w": 800, "original_h": 1040, "thumb_tiny": "AwAwACTRbr1IFH/AzQ34/hRt9zQAo4/iz9aXI9aTHuaXFABRRRQA043c/wAqUY60jde9H50AOzRmkH0P5UfhQAtFFFADWxml20jdaAPp+dAC7RQAB0o49qXj2oAKKKKAP//Z", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F083HN97AG2/ux_product_recommendations_", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "4DeJ6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "First draft of the doc I've been workign on: "}, {"type": "link", "url": "https://docs.google.com/document/d/1Kyd7eN9JsSa62CvUkrrrxGwRPLo3FhJLzDGc2s09-zk/edit?usp=sharing"}]}]}]}, {"ts": "1733258269.813939", "text": "Degenkolb bug 3: Merit Cycle Changes report not working (I'm guessing b/c we tried to push the new report?) They get an error when they attempt to download it. <https://compiify.atlassian.net/browse/COM-4014>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733258269.813939", "reply_count": 3, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14097::4189d4401eb84882afa6a846586a35d0", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4014?atlOrigin=eyJpIjoiMGViOTViM2U2Y2M0NDgxMWE4YmYyMWE0Y2ZmYjkwZjkiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4014 Issue: Merit Cycle Changes Report Not Working for Degenkolb's Accou…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14097::6f5011f27ad548a380205a71adf75c70", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14097\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4014\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4014", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "C1Riq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Degenkolb bug 3: Merit Cycle Changes report not working (I'm guessing b/c we tried to push the new report?) They get an error when they attempt to download it. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4014"}]}]}]}, {"ts": "**********.517799", "text": "<@U0690EB5JE5> Degenkolb bug 2: table load error. Same error showing in <PERSON><PERSON><PERSON>'s account, and likely the reason behind the issue of viewing the wrong team data in the account,. <https://compiify.atlassian.net/browse/COM-4013>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "**********.517799", "reply_count": 11, "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14096::898f12f66f0d47cc87e72f89a4667b07", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4013?atlOrigin=eyJpIjoiZTA3NTc1ZTJkZDAzNGNhMzgyNjY3MTQ2ZjZiYWNhNWIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4013 Bug Report: Table Load Error: Incorrect Team Data Displayed in Meri…>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14096::2036e82433594f629c61685e8cd4af78", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14096\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4013\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4013", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "BK6p4", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " Degenkolb bug 2: table load error. Same error showing in <PERSON><PERSON><PERSON>'s account, and likely the reason behind the issue of viewing the wrong team data in the account,. "}, {"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4013"}]}]}]}, {"ts": "**********.021609", "text": "<@U0690EB5JE5> here is the rewards template for Tithely. let's discuss what we can or can not do today, and what do you need to show the total rewards", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "**********.021609", "reply_count": 1, "files": [{"id": "F083KSUCW6Q", "created": **********, "timestamp": **********, "name": "<PERSON><PERSON><PERSON> - 3.2024.pdf", "title": "<PERSON><PERSON><PERSON> - 3.2024.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U07M6QKHUC9", "user_team": "T04DM97F1UM", "editable": false, "size": 105838, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T04DM97F1UM-F083KSUCW6Q/kristopher_parker_-_3.2024.pdf", "url_private_download": "https://files.slack.com/files-pri/T04DM97F1UM-F083KSUCW6Q/download/kristopher_parker_-_3.2024.pdf", "media_display_type": "unknown", "thumb_pdf": "https://files.slack.com/files-tmb/T04DM97F1UM-F083KSUCW6Q-31849a2b2d/kristopher_parker_-_3.2024_thumb_pdf.png", "thumb_pdf_w": 935, "thumb_pdf_h": 1210, "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F083KSUCW6Q/kristopher_parker_-_3.2024.pdf", "permalink_public": "https://slack-files.com/T04DM97F1UM-F083KSUCW6Q-d4668c4fce", "is_starred": false, "skipped_shares": true, "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "i/AaM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " here is the rewards template for <PERSON>ith<PERSON>. let's discuss what we can or can not do today, and what do you need to show the total rewards"}]}]}]}], "created_at": "2025-05-22T21:35:34.682322"}