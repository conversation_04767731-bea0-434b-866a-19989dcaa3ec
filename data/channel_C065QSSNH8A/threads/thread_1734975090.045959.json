{"thread_ts": "1734975090.045959", "channel_id": "C065QSSNH8A", "reply_count": 12, "replies": [{"ts": "1734975563.361159", "text": "That error actually has no impact. We will fix that error. Are you still blocked to create cycle? If yes, I will take care during my day.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734975090.045959", "blocks": [{"type": "rich_text", "block_id": "/mai3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That error actually has no impact. We will fix that error. Are you still blocked to create cycle"}, {"type": "text", "text": "?"}, {"type": "text", "text": " If yes, I will take care during my day."}]}]}]}, {"ts": "1734975586.862519", "text": "Now I am getting a 'something went wrong, cannot load approvers data' error periodically on the recommenders page. It is happening when I click on a carrot to open the hierarchy on the page", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734975090.045959", "blocks": [{"type": "rich_text", "block_id": "qGtME", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Now I am getting a 'something went wrong, cannot load approvers data' error periodically on the recommenders page. It is happening when I click on a carrot to open the hierarchy on the page"}]}]}]}, {"ts": "1734975600.183339", "text": "I am not blocked but cycle creation is being buggy for sure", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734975090.045959", "blocks": [{"type": "rich_text", "block_id": "WA9q3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I am not blocked but cycle creation is being buggy for sure"}]}]}]}, {"ts": "1734975609.035889", "text": "i'm seeing if i can get through the whole process", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734975090.045959", "blocks": [{"type": "rich_text", "block_id": "WqDPA", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i'm seeing if i can get through the whole process"}]}]}]}, {"ts": "1734975680.178439", "text": "It is not letting me move past this page", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734975090.045959", "blocks": [{"type": "rich_text", "block_id": "wQiO9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It is not letting me move past this page"}]}]}]}, {"ts": "1734975827.008159", "text": "Blocked: <https://www.loom.com/share/900107ecfd5f4a24ba8f10420076dc9d?sid=9005d5a9-987e-4b8a-a621-62a2f29dcf7f>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734975090.045959", "attachments": [{"id": 1, "blocks": [{"type": "video", "block_id": "13xnI", "video_url": "https://www.loom.com/embed/900107ecfd5f4a24ba8f10420076dc9d?sid=9005d5a9-987e-4b8a-a621-62a2f29dcf7f&unfurl=blocks", "thumbnail_url": "https://cdn.loom.com/sessions/thumbnails/900107ecfd5f4a24ba8f10420076dc9d-8f5add0f8722227a-4x3.jpg", "alt_text": "Troubleshooting Data Entry Issues 😕", "title": {"type": "plain_text", "text": "Troubleshooting Data Entry Issues 😕", "emoji": true}, "title_url": "https://www.loom.com/share/900107ecfd5f4a24ba8f10420076dc9d", "author_name": "<PERSON>", "provider_name": "Loom", "provider_icon_url": "https://cdn.loom.com/assets/favicons-loom/favicon-32x32.png", "description": {"type": "plain_text", "text": "⏱ 1 min  ", "emoji": true}}, {"type": "section", "block_id": "JHxIm", "text": {"type": "mrkdwn", "text": ":information_source: Hey there! In this video, I encountered a problem while adding recommenders to the system. Despite some success, I faced errors preventing me from...", "verbatim": false}}, {"type": "actions", "block_id": "ZxfLI", "elements": [{"type": "button", "action_id": "view-on-loom-cta", "text": {"type": "plain_text", "text": "Watch on Loom", "emoji": true}, "url": "https://www.loom.com/share/900107ecfd5f4a24ba8f10420076dc9d?sid=9005d5a9-987e-4b8a-a621-62a2f29dcf7f"}, {"type": "button", "action_id": "watch-video-later-cta", "text": {"type": "plain_text", "text": "Watch Later", "emoji": true}, "value": "{\"videoId\":\"900107ecfd5f4a24ba8f10420076dc9d\",\"videoName\":\"Troubleshooting Data Entry Issues 😕\",\"sendWatchLaterReminderWeekdaysOnly\":false,\"loomBaseUrl\":\"https://www.loom.com\"}"}]}], "fallback": "[no preview available]", "bot_id": "B06A6BR8DL1", "app_unfurl_url": "https://www.loom.com/share/900107ecfd5f4a24ba8f10420076dc9d?sid=9005d5a9-987e-4b8a-a621-62a2f29dcf7f", "is_app_unfurl": true, "app_id": "A9G1TH4S2"}], "blocks": [{"type": "rich_text", "block_id": "8s4Qo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Blocked: "}, {"type": "link", "url": "https://www.loom.com/share/900107ecfd5f4a24ba8f10420076dc9d?sid=9005d5a9-987e-4b8a-a621-62a2f29dcf7f"}]}]}]}, {"ts": "1734975833.191649", "text": "Going to log out and in again just ot see", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734975090.045959", "blocks": [{"type": "rich_text", "block_id": "7dxHk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Going to log out and in again just ot see"}]}]}]}, {"ts": "1734975913.909099", "text": "That did not work. Still cannot move past this page.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734975090.045959", "blocks": [{"type": "rich_text", "block_id": "EKoSF", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That did not work. Still cannot move past this page."}]}]}]}, {"ts": "1734975994.887979", "text": "<https://compiify.atlassian.net/browse/COM-4039>", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734975090.045959", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "uf:ih:14122::919b870502a747e692bde54d0eb9baed", "text": {"type": "mrkdwn", "text": "*<https://compiify.atlassian.net/browse/COM-4039?atlOrigin=eyJpIjoiZjY0ZGU3OGNiNzI1NDVkMDhmOTQxNWMwNGQ4ZTU2OTYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|COM-4039 Issue: Unable to progress after adding recommenders>*", "verbatim": false}}, {"type": "context", "block_id": "uf:cx:14122::e70399b8e43849689bd9ffc2fdf6184a", "elements": [{"type": "mrkdwn", "text": "Status: *To Do*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-issuetype/task.png", "alt_text": "Task"}, {"type": "mrkdwn", "text": "Type: *Task*", "verbatim": false}, {"type": "image", "image_url": "https://secure.gravatar.com/avatar/2ea5110e02efd539f44800e3c074fb19?d=https%3A%2F%2Fproduct-integrations-cdn.atl-paas.net%2Ficons%2Funknown-user.png", "alt_text": "<PERSON><PERSON><PERSON>"}, {"type": "mrkdwn", "text": "Assignee: *<PERSON><PERSON><PERSON>*", "verbatim": false}, {"type": "image", "image_url": "https://product-integrations-cdn.atl-paas.net/jira-priority/highest.png", "alt_text": "Highest"}, {"type": "mrkdwn", "text": "Priority: *Highest*", "verbatim": false}]}, {"type": "actions", "block_id": "issueFooterActions:{\"issueId\":\"14122\",\"instanceId\":\"cd9dc0f6-dc71-30f7-a9a3-8fa77c174f39\",\"projectId\":\"10000\",\"issueKey\":\"COM-4039\",\"messageType\":\"uf\",\"nType\":\"ISSUE_UNFURL\"}", "elements": [{"type": "button", "action_id": "watch", "text": {"type": "plain_text", "text": "Watch", "emoji": true}, "value": "watch"}, {"type": "static_select", "action_id": "jiraOverflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "comment"}, {"text": {"type": "plain_text", "text": "Assign", "emoji": true}, "value": "assign"}, {"text": {"type": "plain_text", "text": "Transition", "emoji": true}, "value": "transition"}, {"text": {"type": "plain_text", "text": "Why am I seeing this?", "emoji": true}, "value": "why"}, {"text": {"type": "plain_text", "text": "Receive updates in channel", "emoji": true}, "value": "connect"}]}]}], "color": "#2684FF", "fallback": "[no preview available]", "bot_id": "B04R9CUPQ5P", "app_unfurl_url": "https://compiify.atlassian.net/browse/COM-4039", "is_app_unfurl": true, "app_id": "A2RPP3NFR"}], "blocks": [{"type": "rich_text", "block_id": "YiH+S", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://compiify.atlassian.net/browse/COM-4039"}]}]}]}, {"ts": "1735024118.802219", "text": "<@U07EJ2LP44S> There some issue with infra. I have upgraded it and was able to create cycle. Please verify and launch.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734975090.045959", "blocks": [{"type": "rich_text", "block_id": "wcw3+", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " There some issue with infra. I have upgraded it and was able to create cycle. Please verify and launch."}]}]}]}, {"ts": "1735055500.178279", "text": "Oh good! Did you attempt to duplicate their cycle from before? Or do I need to go in and make all of the selections?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1734975090.045959", "blocks": [{"type": "rich_text", "block_id": "GEDfW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Oh good! Did you attempt to duplicate their cycle from before? Or do I need to go in and make all of the selections?"}]}]}]}, {"ts": "1735055551.190039", "text": "I did copy the selections manually. May be you want to verify once ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1734975090.045959", "reactions": [{"name": "thankyouty", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "12z/v", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I did copy the selections manually. May be you want to verify once "}]}]}]}], "created_at": "2025-05-22T22:00:49.338195"}