{"date": "2024-02-13", "channel_id": "C065QSSNH8A", "message_count": 3, "messages": [{"ts": "1707801734.363119", "text": "Thanks, <PERSON>\nOn a separate note, I think we should definitely hide the bonus columns for SDF. I think we did that when we first created an environment for them. So I don’t know why it wasn’t done in this environment.", "user": "U04DS2MBWP4", "type": "message", "thread_ts": "1707801734.363119", "reply_count": 1, "blocks": [{"type": "rich_text", "block_id": "J73sb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks, <PERSON>\nOn a separate note, I think we should definitely hide the bonus columns for SDF. I think we did that when we first created an environment for them. So I don’t know why it wasn’t done in this environment."}]}]}]}, {"ts": "1707797015.000719", "text": "<@U04DKEFP1K8> Eng priorities for next day are <https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3|updated on the board>.", "user": "U065H3M6WJV", "type": "message", "blocks": [{"type": "rich_text", "block_id": "Guzzi", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " Eng priorities for next day are "}, {"type": "link", "url": "https://compiify.atlassian.net/jira/software/c/projects/COM/boards/3", "text": "updated on the board"}, {"type": "text", "text": "."}]}]}]}, {"ts": "1707796974.451969", "text": "<@U04DKEFP1K8> <@U04DS2MBWP4> I pulled together a list of the requested changes (including both customizations &amp; bug fixes) that SDF has asked for in <https://docs.google.com/spreadsheets/d/1R36110yTrU-jlpKTVJA4qr9Ndx-AjqS6wjj0p_KEtLM/edit?usp=sharing|this sheet>. This might be useful for us to walk through to understand where we could do better with automation or self-service for future customers, as well as understanding some common themes in product feature gaps.", "user": "U065H3M6WJV", "type": "message", "thread_ts": "1707796974.451969", "reply_count": 3, "reactions": [{"name": "+1", "users": ["U04DS2MBWP4"], "count": 1}, {"name": "raised_hands", "users": ["U04DKEFP1K8"], "count": 1}], "files": [{"id": "F06JDFTDRFX", "created": 1707796977, "timestamp": 1707796977, "name": "SDF Change Log", "title": "SDF Change Log", "mimetype": "application/vnd.google-apps.spreadsheet", "filetype": "gsheet", "pretty_type": "Google Sheets", "user": "U065H3M6WJV", "user_team": "T04DM97F1UM", "editable": false, "size": 0, "mode": "external", "is_external": true, "external_type": "gdrive", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "external_id": "1R36110yTrU-jlpKTVJA4qr9Ndx-AjqS6wjj0p_KEtLM", "external_url": "https://docs.google.com/spreadsheets/d/1R36110yTrU-jlpKTVJA4qr9Ndx-AjqS6wjj0p_KEtLM/edit?usp=sharing", "url_private": "https://docs.google.com/spreadsheets/d/1R36110yTrU-jlpKTVJA4qr9Ndx-AjqS6wjj0p_KEtLM/edit?usp=sharing", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_64.png", "thumb_80": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_80.png", "thumb_360": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_360.png", "thumb_360_w": 254, "thumb_360_h": 360, "thumb_480": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_480.png", "thumb_480_w": 339, "thumb_480_h": 480, "thumb_160": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_160.png", "thumb_720": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_720.png", "thumb_720_w": 509, "thumb_720_h": 720, "thumb_800": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_800.png", "thumb_800_w": 800, "thumb_800_h": 1132, "thumb_960": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_960.png", "thumb_960_w": 678, "thumb_960_h": 960, "thumb_1024": "https://files.slack.com/files-tmb/T04DM97F1UM-F06JDFTDRFX-10bbeb50f4/sdf_change_log_1024.png", "thumb_1024_w": 724, "thumb_1024_h": 1024, "thumb_tiny": "AwAwACHSZsUAk0j9qBQA7miiigAooooAa/agcUOcYoHPWgBR9c0tIAB0paACiiigBr44zQOlKy5oA96AAZ70tFFABRRRQB//2Q==", "permalink": "https://stride-hr.slack.com/files/U08QENR4TNY/F06JDFTDRFX/sdf_change_log", "is_starred": false, "skipped_shares": true, "has_rich_preview": true, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "Rko8X", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04DKEFP1K8"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U04DS2MBWP4"}, {"type": "text", "text": " I pulled together a list of the requested changes (including both customizations & bug fixes) that SDF has asked for in "}, {"type": "link", "url": "https://docs.google.com/spreadsheets/d/1R36110yTrU-jlpKTVJA4qr9Ndx-AjqS6wjj0p_KEtLM/edit?usp=sharing", "text": "this sheet"}, {"type": "text", "text": ". This might be useful for us to walk through to understand where we could do better with automation or self-service for future customers, as well as understanding some common themes in product feature gaps."}]}]}]}], "created_at": "2025-05-22T21:35:34.582924"}