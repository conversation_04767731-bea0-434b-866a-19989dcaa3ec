#!/usr/bin/env python
"""
Quick and simple Slack data ingestion script.

Usage:
    cd multi_source_rag
    python ../scripts/quick_ingest.py
"""

import os
import sys
import django

# Set up Django
sys.path.append('.')
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from apps.documents.services.ingestion_service import IngestionService
from apps.documents.interfaces.local_slack import LocalSlackSourceInterface
from apps.accounts.models import Tenant

def main():
    print("🚀 Quick Slack Data Ingestion")
    print("=" * 40)

    # 1. Initialize Slack interface
    slack_interface = LocalSlackSourceInterface({
        "data_dir": "../data/",
        "channel": "C065QSSNH8A"
    })

    # 2. Get data info
    info = slack_interface.get_staged_data_info()
    print(f"📊 Found {info.total_messages} messages")

    # 3. Fetch documents (limit to 100 for quick test)
    print("📄 Fetching documents...")
    documents = slack_interface.fetch_documents(limit=100, days_back=365)
    print(f"✅ Got {len(documents)} documents")

    # 4. Create or get tenant and source
    print("🔧 Setting up ingestion...")
    tenant, _ = Tenant.objects.get_or_create(
        slug="default",
        defaults={"name": "Default Tenant"}
    )

    # Create ingestion service
    ingestion_service = IngestionService(tenant=tenant)

    # Create or get document source
    try:
        source = ingestion_service.create_source(
            name="Local Slack C065QSSNH8A",
            source_type="slack",
            config={
                "data_dir": "../data/",
                "channel": "C065QSSNH8A"
            }
        )
    except Exception as e:
        print(f"⚠️ Source creation failed, trying to get existing: {e}")
        from apps.documents.models import DocumentSource
        source = DocumentSource.objects.filter(
            tenant=tenant,
            source_type="slack",
            name="Local Slack C065QSSNH8A"
        ).first()
        if not source:
            raise Exception("Could not create or find source")

    # 5. Process documents through the source
    print("🔄 Starting ingestion...")
    processed, failed = ingestion_service.process_source(
        source=source,
        batch_size=20
    )

    # 6. Show results
    print(f"🎉 Completed!")
    print(f"📄 Processed: {processed}")
    print(f"❌ Failed: {failed}")

    # Get additional stats
    stats = ingestion_service.get_processing_stats()
    if stats:
        print(f"🔗 Chunks: {stats.get('chunks_created', 0)}")
        print(f"⚡ Vectors: {stats.get('vectors_stored', 0)}")

if __name__ == "__main__":
    main()
